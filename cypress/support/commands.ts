/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
// declare global {
//   namespace Cypress {
//     interface Chainable {
//       login(email: string, password: string): Chainable<void>
//       drag(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       dismiss(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       visit(originalFn: CommandOriginalFn, url: string, options: Partial<VisitOptions>): Chainable<Element>
//     }
//   }
// }
Cypress.Commands.add('loginBackoffice', (username = '<EMAIL>', password = 'simple') => {
    cy.visit('/login/backoffice');
    cy.get('#userName').clear().type(username);
    cy.get('#pwd').clear().type(password, { log: false });
    cy.get('#submitbutton').click();
    // cy.location('pathname').should('eq', '/backoffice/dashboard/');
});

Cypress.Commands.add('openLoanByEmail', (email, tab = 'HMLI') => {
    cy.get("#kt_quick_search_toggle > div.topbar-item svg").click();
    cy.get("#mainSearch").clear().type(`${email}{enter}`);
    cy.get("#row-0 > td.tdClassNamePropAddress a")
        .invoke('attr', 'href')
        .then(href => {
            if (typeof href === 'string') {
                const separator = href.includes('?') ? '&' : '?';
                cy.visit(`${href}${separator}tabOpt=${tab}`);
            }
        });
});
Cypress.Commands.add('createNewLoanAndJumpToTab', (tabName = 'HMLI') => {
    const randomString = Math.random().toString(36).substring(2, 8);
    const borrowerFName = `${randomString}FN`;
    const borrowerLName = `${randomString}LN`;
    const borrowerEmail = `${randomString}@example.com`;

    // Start new loan
    cy.get("#menu_createloan").click();
    cy.get("#menu_quickapp").click();
    //cy.location("href").should("include", "eOpt=0&cliType=PC&tabOpt=QAPP");
    cy.wait(5000);
    // Fill out loan form
    cy.get("#branchId").select("Main Branch DE");
    //cy.get("#loanModForm > div:nth-of-type(1) div:nth-of-type(5) span").click();
    //cy.get("li.highlighted").click();
    cy.wait(2500);
    cy.get('#LMRClientType_chosen').click();
    cy.wait(1000);
    cy.get('#LMRClientType_chosen .chosen-results li')
        .contains('Commercial')
        .click();
    cy.get("#primaryStatus").select("63104");
    cy.get("#borrowerFName").click().type(borrowerFName);
    cy.get("#borrowerLName").click().type(borrowerLName);
    cy.get("#borrowerEmail").click().type(borrowerEmail);

    // Save loan and capture resulting URL
    cy.get("#saveBtn").click();
    cy.wait(8000);
    //cy.location("href").should("include", "LMRequest.php?eId=");

    // Store the final loan URL and extract the tab-neutral version
    cy.location("href").then((savedLoanUrl) => {
        const baseUrl = savedLoanUrl.replace(/&tabOpt=[^&]+/, '');
        const tabUrl = `${baseUrl}&tabOpt=${tabName}`;
        cy.visit(tabUrl);
    });
});
