describe('Rehab/Construction Cost A/B Test', () => {
    let loanUrl: string;

    function parseFormattedNumber(val) {
        return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
    }

    const phaseA = {
        costBasis: 5000000,
        maxAmtToPutDown: 200000,
        rehabCost: 120000,
        rehabPercent: 100,
        prepaidReserve: 120000,
        closingCost: 11000
    };

    const phaseB = {
        costBasis: 5100000,
        maxAmtToPutDown: 210000,
        rehabCost: 130000,
        rehabPercent: 95,
        prepaidReserve: 125000,
        closingCost: 11500
    };

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanAndJumpToTab('HMLI');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });

    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select('Purchase');
        cy.get('#costBasis').clear().type(data.costBasis.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#maxAmtToPutDown').clear().type(data.maxAmtToPutDown.toString());
        cy.get('#downPaymentPercentage').click();
        cy.get('#rehabCost').clear().type(data.rehabCost.toString());
        cy.get('#rehabCostPercentageFinanced')
            .clear()
            .type(data.rehabPercent.toString(), { force: true });
        cy.get('td.doesPropertyNeedRehabDispDiv label').click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(1) label:nth-of-type(1) > span").click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(2) label:nth-of-type(1) > span").click();
        cy.get('#initialAdvance').click();
        cy.get('#prepaidInterestReserve').clear().type(data.prepaidReserve.toString());
        cy.get('#closingCostFinanced').clear().type(data.closingCost.toString());
        cy.get('td.px-0 label').click();
    };

    const assertSavedInputs = (data) => {
        cy.get('#costBasis').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.costBasis);
        });
        cy.get('#maxAmtToPutDown').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.maxAmtToPutDown);
        });
        cy.get('#rehabCost').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.rehabCost);
        });
        cy.get('#rehabCostPercentageFinanced').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.rehabPercent);
        });
        cy.get('#prepaidInterestReserve').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.prepaidReserve);
        });
        cy.get('#closingCostFinanced').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.closingCost);
        });
    };

    const assertRehabCostFormula = (expectedPercent) => {
        cy.get('#rehabCostFinanced').invoke('val').then(financedVal => {
            const financed = parseFormattedNumber(financedVal);
            const expected = (financed * 100) / expectedPercent;

            cy.get('#rehabCost').invoke('val').then(actualVal => {
                const actual = parseFormattedNumber(actualVal);
                cy.log(`Expected rehabCost: ${expected}, Actual: ${actual}`);
                expect(actual).to.eq(expected);
            });
        });
    };

    it('JS Test - Phase A Entry and Validation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertRehabCostFormula(phaseA.rehabPercent);
        cy.get('#saveBtn').click();
        cy.wait(8000);
    });

    it('PHP Test - Phase A Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertRehabCostFormula(phaseA.rehabPercent);
    });

    it('JS Test - Phase B Entry and Re-Validation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertRehabCostFormula(phaseB.rehabPercent);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase B Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertRehabCostFormula(phaseB.rehabPercent);
    });
});
