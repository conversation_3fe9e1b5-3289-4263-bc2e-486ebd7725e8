describe('Total Project Cost - A/B Suite', () => {
    let loanUrl: string;
    function parseFormattedNumber(val) {
        return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
    }

    const phaseA = {
        costBasis: 5050000,
        maxAmtToPutDown: 210000,
        rehabCost: 123000,
        rehabFinancedPercent: 100,
        closingCost: 11500,
        prepaidReserve: 120000,
        costSpent: 88500
    };

    const phaseB = {
        costBasis: 4710000,
        maxAmtToPutDown: 250000,
        rehabCost: 96000,
        rehabFinancedPercent: 90,
        closingCost: 14800,
        prepaidReserve: 113000,
        costSpent: 76500
    };

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanAndJumpToTab('HMLI');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });

    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select('Purchase');
        cy.get('#costBasis').clear().type(data.costBasis.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#maxAmtToPutDown').clear().type(data.maxAmtToPutDown.toString());
        cy.get('#downPaymentPercentage').click();
        cy.get('#rehabCost').clear().type(data.rehabCost.toString());
        cy.get('#rehabCostPercentageFinanced').clear().type(`${data.rehabFinancedPercent}`, { force: true });
        cy.get('td.doesPropertyNeedRehabDispDiv label').click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(1) label:nth-of-type(1) > span").click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(2) label:nth-of-type(1) > span").click();
        cy.get('#initialAdvance').click();
        cy.get('#prepaidInterestReserve').clear().type(data.prepaidReserve.toString());
        cy.get('#closingCostFinanced').clear().type(data.closingCost.toString());
        cy.get('#costSpent').clear().type(data.costSpent.toString());
        cy.get('td.px-0 label').click();
    };

    const assertSavedInputs = (data) => {
        assertCurrencyInput('#costBasis', data.costBasis);
        assertCurrencyInput('#rehabCost', data.rehabCost);
        assertCurrencyInput('#costSpent', data.costSpent);
        assertCurrencyInput('#prepaidInterestReserve', data.prepaidReserve);
    };

    const assertCurrencyInput = (selector: string, expected: number) => {
        cy.get(selector).invoke('val').then(raw => {
            const actual = parseFormattedNumber(raw);
            cy.log(`${selector} expected: ${expected}, actual: ${actual}`);
            expect(actual).to.eq(expected);
        });
    };

    const assertFinancedPercentage = (data) => {
        const expected = data.costBasis + data.rehabCost + data.costSpent + data.prepaidReserve;
        cy.get('#totalProjectCost').invoke('text').then(text => {
            const actual = parseFormattedNumber(text);
            cy.log(`Expected: ${expected}`);
            cy.log(`Actual: ${actual}`);
            expect(actual).to.eq(expected);
        });
    };

    // Phase A
    it('JS Test - Phase A Input + Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertFinancedPercentage(phaseA);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase A Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertFinancedPercentage(phaseA);
    });

    // Phase B
    it('JS Test - Phase B Input + Re-Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertFinancedPercentage(phaseB);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase B Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertFinancedPercentage(phaseB);
    });
});
