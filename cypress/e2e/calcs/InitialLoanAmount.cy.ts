describe('Initial Loan Amount - A/B Test', () => {
    let loanUrl: string;
    function parseFormattedNumber(val) {
        return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
    }

    const phaseA = {
        costBasis: 5000000,
        maxAmtToPutDown: 200000,
        rehabCost: 120000,
        prepaid: 120000,
        closing: 11000
    };

    const phaseB = {
        costBasis: 4900000,
        maxAmtToPutDown: 250000,
        rehabCost: 110000,
        prepaid: 100000,
        closing: 15000
    };

    // Store session and loan once
    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanAndJumpToTab('HMLI');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });

    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select('Purchase');
        cy.get('#costBasis').clear().type(data.costBasis.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#maxAmtToPutDown').clear().type(data.maxAmtToPutDown.toString());
        cy.get('#downPaymentPercentage').click();
        cy.get('#rehabCost').clear().type(data.rehabCost.toString());
        cy.get('#rehabCostPercentageFinanced').clear().type('100').blur();
        cy.get('td.doesPropertyNeedRehabDispDiv label').click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(1) label:nth-of-type(1) > span").click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(2) label:nth-of-type(1) > span").click();
        cy.get('#initialAdvance').click();
        cy.get('#prepaidInterestReserve').clear().type(data.prepaid.toString());
        cy.get('#closingCostFinanced').clear().type(data.closing.toString());
    };

    const assertSavedInputs = (data) => {
        cy.get('#costBasis').invoke('val').then(val => expect(parseFormattedNumber(val)).to.eq(data.costBasis));
        cy.get('#maxAmtToPutDown').invoke('val').then(val => expect(parseFormattedNumber(val)).to.eq(data.maxAmtToPutDown));
        cy.get('#rehabCost').invoke('val').then(val => expect(parseFormattedNumber(val)).to.eq(data.rehabCost));
        cy.get('#prepaidInterestReserve').invoke('val').then(val => expect(parseFormattedNumber(val)).to.eq(data.prepaid));
        cy.get('#closingCostFinanced').invoke('val').then(val => expect(parseFormattedNumber(val)).to.eq(data.closing));
    };

    const assertAcquisitionPriceFinanced = (expectedILA) => {
        cy.get('#acquisitionPriceFinanced').should('be.visible').invoke('text').then(text => {
            const actual = parseFormattedNumber(text);
            cy.log(`Expected ILA: ${expectedILA}, Actual ILA: ${actual}`);
            expect(actual).to.eq(expectedILA);
        });
    };

    it('JS Test - Initial Calculation & Save (A)', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertAcquisitionPriceFinanced(phaseA.costBasis - phaseA.maxAmtToPutDown);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Validate Saved Inputs (A)', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertAcquisitionPriceFinanced(phaseA.costBasis - phaseA.maxAmtToPutDown);
    });

    it('JS Test - Modify and Recalculate (B)', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertAcquisitionPriceFinanced(phaseB.costBasis - phaseB.maxAmtToPutDown);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Validate Saved Inputs (B)', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertAcquisitionPriceFinanced(phaseB.costBasis - phaseB.maxAmtToPutDown);
    });
});
