describe('Rehab/Construction Cost % Financed - A/B Test', () => {
    let loanUrl: string;
    function parseFormattedNumber(val) {
        return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
    }

    const phaseA = {
        typeOfLoan: 'Purchase',
        costBasis: 5050000,
        maxAmtToPutDown: 205000,
        rehabCost: 122000,
        rehabPercent: 97,
        prepaidReserve: 118000,
        closingCost: 10800
    };

    const phaseB = {
        typeOfLoan: 'Purchase',
        costBasis: 5010001,
        maxAmtToPutDown: 200000,
        rehabCost: 133000,
        rehabPercent: 92,
        prepaidReserve: 122000,
        closingCost: 11200
    };

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanAndJumpToTab('HMLI');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });


    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select(data.typeOfLoan);
        cy.get('#costBasis').clear().type(data.costBasis.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#maxAmtToPutDown').clear().type(data.maxAmtToPutDown.toString());
        cy.get('#downPaymentPercentage').click();

        cy.get('#rehabCost').clear().type(data.rehabCost.toString());
        cy.get('#rehabCostPercentageFinanced')
            .clear()
            .type(data.rehabPercent.toString(), { force: true });

        cy.get('td.doesPropertyNeedRehabDispDiv label').click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(1) label:nth-of-type(1) > span").click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(2) label:nth-of-type(1) > span").click();
        cy.get('#initialAdvance').click();
        cy.get('#prepaidInterestReserve').clear().type(data.prepaidReserve.toString());
        cy.get('#closingCostFinanced').clear().type(data.closingCost.toString());
        cy.get('td.px-0 label').click();
    };

    const assertSavedInputs = (data) => {
        cy.get('#rehabCost').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.rehabCost);
        });
        cy.get('#rehabCostPercentageFinanced').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.rehabPercent);
        });
    };

    const assertFinancedPercentage = () => {
        cy.get('#rehabCostFinanced').invoke('val').then(financedVal => {
            cy.get('#rehabCost').invoke('val').then(rehabCostVal => {
                const financed = parseFormattedNumber(financedVal);
                const rehabCost = parseFormattedNumber(rehabCostVal);
                const expected = (financed * 100) / rehabCost;

                cy.get('#rehabCostPercentageFinanced')
                    .invoke('val')
                    .then(actualVal => {
                        const actual = parseFormattedNumber(actualVal);
                        cy.log(`Expected % Financed: ${expected}, Actual: ${actual}`);
                        expect(actual).to.eq(expected);
                    });
            });
        });
    };

    it('JS Test - Phase A Input + Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertFinancedPercentage();
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase A Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertFinancedPercentage();
    });

    it('JS Test - Phase B Input + Re-Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertFinancedPercentage();
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase B Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertFinancedPercentage();
    });
});
