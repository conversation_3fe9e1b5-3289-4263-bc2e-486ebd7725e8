describe('Simple ARV Calculation - A/B Test', () => {
    let loanUrl: string;
    function parseFormattedNumber(val) {
        return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
    }

    const phaseA = {
        origPoints: 1,
        brokerPoints: 5,
        prepaidReserve: 15000,
        assessedValue: 6000000
    };

    const phaseB = {
        origPoints: 2,
        brokerPoints: 4,
        prepaidReserve: 10000,
        assessedValue: 5000000
    };

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanAndJumpToTab('HMLI');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });

    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select('Purchase');
        cy.get('#originationPointsRate').clear().type(data.origPoints.toString());
        cy.get('#brokerPointsRate').clear().type(data.brokerPoints.toString());
        cy.get('#prePaidInterest').clear().type(data.prepaidReserve.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#assessedValue').clear().type(data.assessedValue.toString()).blur();
        cy.wait(2000);
    };

    const assertSavedInputs = (data) => {
        cy.get('#originationPointsRate').invoke('val').then(val =>
            expect(parseFormattedNumber(val)).to.eq(data.origPoints)
        );
        cy.get('#brokerPointsRate').invoke('val').then(val =>
            expect(parseFormattedNumber(val)).to.eq(data.brokerPoints)
        );
        cy.get('#prePaidInterest').invoke('val').then(val =>
            expect(parseFormattedNumber(val)).to.eq(data.prepaidReserve)
        );
        cy.get('#assessedValue').invoke('val').then(val =>
            expect(parseFormattedNumber(val)).to.eq(data.assessedValue)
        );
    };

    const assertSimpleARVFormula = () => {
        cy.get('#totalLoanAmount1').invoke('val').then(totalLoanVal => {
            cy.get('#originationPointsValue').invoke('val').then(origVal => {
                cy.get('#brokerPointsValue').invoke('val').then(brokerVal => {
                    cy.get('#prepaidInterestReserve').invoke('val').then(prepaidVal => {
                        cy.get('#assessedValue').invoke('val').then(arvVal => {
                            cy.get('#simpleARV').invoke('text').then(displayedText => {
                                const parse = parseFormattedNumber;

                                const totalLoan = parse(totalLoanVal);
                                const orig = parse(origVal);
                                const broker = parse(brokerVal);
                                const prepaid = parse(prepaidVal);
                                const arv = parse(arvVal);
                                const displayed = parse(displayedText);

                                const netLoan = totalLoan - orig - broker - prepaid;
                                const expected = arv === 0 ? 0 : (netLoan / arv) * 100;

                                cy.log(`Expected: ${expected} | Displayed: ${displayed}`);
                                expect(displayed).to.be.closeTo(expected, 0.1);
                            });
                        });
                    });
                });
            });
        });
    };

    it('JS Test - Phase A Entry + Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertSimpleARVFormula();
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase A Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertSimpleARVFormula();
    });

    it('JS Test - Phase B Update + Re-Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertSimpleARVFormula();
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase B Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertSimpleARVFormula();
    });
});
