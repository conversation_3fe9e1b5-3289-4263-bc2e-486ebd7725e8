describe('Total Loan Amount - A/B Validation Suite', () => {
    let loanUrl: string;
    const parseFormattedNumber = (val) =>
        parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;

    const phaseA = {
        costBasis: 5100000,
        maxAmtToPutDown: 210000,
        rehabCost: 125000,
        rehabFinancedPercent: 100,
        closingCost: 11500,
        prepaidReserve: 118000
    };

    const phaseB = {
        costBasis: 4600000,
        maxAmtToPutDown: 160000,
        rehabCost: 95000,
        rehabFinancedPercent: 90,
        closingCost: 15800,
        prepaidReserve: 95000
    };

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanAndJumpToTab('HMLI');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });


    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select('Purchase');
        cy.get('#costBasis').clear().type(data.costBasis.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#maxAmtToPutDown').clear().type(data.maxAmtToPutDown.toString());
        cy.get('#downPaymentPercentage').click();
        cy.get('#rehabCost').clear().type(data.rehabCost.toString());
        cy.get('#rehabCostPercentageFinanced').clear().type(`${data.rehabFinancedPercent}`, { force: true });
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(1) label:nth-of-type(1) > span").click(); //Do you want to Finance Pre-paid Interest Reserve? Y
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(2) label:nth-of-type(1) > span").click(); //Add to Total Project Cost? Y
        cy.get('#initialAdvance').click();
        cy.get('#prepaidInterestReserve').clear().type(data.prepaidReserve.toString());
        cy.get('#closingCostFinanced').clear().type(data.closingCost.toString()).blur();
    };

    const assertSavedInputs = (data) => {
        cy.get('#rehabCostFinanced').invoke('val').then(val => {
            const actual = parseFormattedNumber(val);
            const expected = (data.rehabCost * data.rehabFinancedPercent) / 100;
            expect(actual).to.eq(expected);
        });

        cy.get('#closingCostFinanced').invoke('val').then(val => {
            const actual = parseFormattedNumber(val);
            expect(actual).to.eq(data.closingCost);
        });

        cy.get('#prepaidInterestReserve').invoke('val').then(val => {
            const actual = parseFormattedNumber(val);
            expect(actual).to.eq(data.prepaidReserve);
        });
    };

    const assertTotalLoanFormula = (data) => {
        cy.get('#acquisitionPriceFinanced').should('be.visible').invoke('text').then(acquisitionText => {
            const ILA = parseFormattedNumber(acquisitionText);
            cy.get('#rehabCostFinanced').invoke('val').then(rehabVal => {
                cy.get('#closingCostFinanced').invoke('val').then(closingVal => {
                    cy.get('#prepaidInterestReserve').invoke('val').then(prepaidVal => {
                        const rehab = parseFormattedNumber(rehabVal);
                        const closing = parseFormattedNumber(closingVal);
                        const prepaid = parseFormattedNumber(prepaidVal);
                        const expectedTotal = ILA + rehab + closing + prepaid;

                        cy.get('#totalLoanAmount1')
                            .invoke('val')
                            .then(totalVal => {
                                const actualTotal = parseFormattedNumber(totalVal);
                                expect(actualTotal).to.eq(expectedTotal);
                            });
                    });
                });
            });
        });
    };

    it('Phase A - JS Input & Formula', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertTotalLoanFormula(phaseA);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('Phase A - PHP Reload Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertTotalLoanFormula(phaseA);
    });

    it('Phase B - JS Input & Formula', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertSavedInputs(phaseB);
        assertTotalLoanFormula(phaseB);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('Phase B - PHP Reload Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertTotalLoanFormula(phaseB);
    });
});
