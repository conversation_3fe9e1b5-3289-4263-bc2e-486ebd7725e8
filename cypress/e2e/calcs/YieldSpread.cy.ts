describe('Yield Spread Calculation - A/B Test', () => {
  let loanUrl: string;
  function parseFormattedNumber(val) {
    return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
  }

  const phaseA = { lien1Rate: 4, costOfCapital: 2 };
  const phaseB = { lien1Rate: 7, costOfCapital: 3 };

  before(() => {
    cy.session('backoffice-login', () => {
      cy.viewport(1920, 1080);
      cy.loginBackoffice();
    });

    cy.visit('/backoffice/dashboard');
    cy.createNewLoanAndJumpToTab('HMLI');

    cy.location('href').then((href) => {
      loanUrl = href.replace(/&tabOpt=[^&]+/, '');
    });
  });

  beforeEach(() => {
    cy.session('backoffice-login', () => {
      cy.viewport(1920, 1080);
      cy.loginBackoffice();
    });

    cy.viewport(1920, 1080);
    cy.wait(250);
  });

  const revisitLoanTab = (tab = 'HMLI') => {
    cy.visit(`${loanUrl}&tabOpt=${tab}`);
  };

  const fillForm = (data) => {
    cy.get('#lien1Rate').clear().type(data.lien1Rate.toString(), { force: true });
    cy.get('#costOfCapital').clear().type(data.costOfCapital.toString(), { force: true });
    cy.get('#yieldSpread').click();
  };

  const computeExpectedSpread = (data) =>
      parseFloat((data.lien1Rate - data.costOfCapital).toFixed(3));

  const assertSavedInputs = (data) => {
    const expected = computeExpectedSpread(data);

    cy.get('#lien1Rate').invoke('val').then(val => {
      expect(parseFormattedNumber(val)).to.eq(data.lien1Rate);
    });

    cy.get('#costOfCapital').invoke('val').then(val => {
      expect(parseFormattedNumber(val)).to.eq(data.costOfCapital);
    });

    cy.get('#yieldSpread').invoke('val').then(val => {
      expect(parseFormattedNumber(val)).to.eq(expected);
    });
  };

  it('Phase A - JS Entry & Yield Spread Check', () => {
    revisitLoanTab('HMLI');
    fillForm(phaseA);
    assertSavedInputs(phaseA);
    cy.get('#saveBtn').click();
    cy.wait(4000);
  });

  it('Phase A - PHP Saved Value Assertion', () => {
    revisitLoanTab('HMLI');
    assertSavedInputs(phaseA);
  });

  it('Phase B - JS Entry with New Inputs & Yield Spread Recheck', () => {
    revisitLoanTab('HMLI');
    fillForm(phaseB);
    assertSavedInputs(phaseB);
    cy.get('#saveBtn').click();
    cy.wait(4000);
  });

  it('Phase B - PHP Saved Value Assertion', () => {
    revisitLoanTab('HMLI');
    assertSavedInputs(phaseB);
  });
});
