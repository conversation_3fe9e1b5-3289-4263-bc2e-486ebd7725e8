describe('Rehab/Construction Cost Financed - A/B Validation', () => {
    let loanUrl: string;
    function parseFormattedNumber(val) {
        return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
    }

    const phaseA = {
        typeOfLoan: 'Purchase',
        costBasis: 5000000,
        maxAmtToPutDown: 210000,
        rehabCost: 130000,
        rehabPercent: 40,
        prepaidReserve: 90000,
        closingCost: 17000
    };

    const phaseB = {
        typeOfLoan: 'Purchase',
        costBasis: 5000000,
        maxAmtToPutDown: 100000,
        rehabCost: 90000,
        rehabPercent: 88,
        prepaidReserve: 115000,
        closingCost: 9000
    };

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanAndJumpToTab('HMLI');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });

    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select(data.typeOfLoan);
        cy.get('#costBasis').clear().type(data.costBasis.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#maxAmtToPutDown').clear().type(data.maxAmtToPutDown.toString());
        cy.get('#downPaymentPercentage').click();

        cy.get('#rehabCost').clear().type(data.rehabCost.toString());
        cy.get('#rehabCostPercentageFinanced')
            .clear()
            .type(data.rehabPercent.toString(), { force: true });

        cy.get('td.doesPropertyNeedRehabDispDiv label').click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(1) label:nth-of-type(1) > span").click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(2) label:nth-of-type(1) > span").click();
        cy.get('#initialAdvance').click();
        cy.get('#prepaidInterestReserve').clear().type(data.prepaidReserve.toString());
        cy.get('#closingCostFinanced').clear().type(data.closingCost.toString());
        cy.get('td.px-0 label').click();
    };

    const assertSavedInputs = (data) => {
        cy.get('#rehabCost').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.rehabCost);
        });
        cy.get('#rehabCostPercentageFinanced').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.rehabPercent);
        });
        cy.get('#prepaidInterestReserve').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.prepaidReserve);
        });
        cy.get('#closingCostFinanced').invoke('val').then(val => {
            expect(parseFormattedNumber(val)).to.eq(data.closingCost);
        });
    };

    const assertFinancedAmount = (rehabCost, rehabPercent) => {
        const expected = (rehabCost * rehabPercent) / 100;
        cy.get('#rehabCostFinanced').invoke('val').then(val => {
            const actual = parseFormattedNumber(val);
            cy.log(`Expected rehabCostFinanced: ${expected}, Actual: ${actual}`);
            expect(actual).to.eq(expected);
        });
    };

    it('JS Test - Phase A Input and Assertion', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertFinancedAmount(phaseA.rehabCost, phaseA.rehabPercent);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase A Verification', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertFinancedAmount(phaseA.rehabCost, phaseA.rehabPercent);
    });

    it('JS Test - Phase B Update and Assertion', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertFinancedAmount(phaseB.rehabCost, phaseB.rehabPercent);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase B Verification', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertFinancedAmount(phaseB.rehabCost, phaseB.rehabPercent);
    });
});
