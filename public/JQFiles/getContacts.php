<?php


use models\Controllers\backoffice\contactList;
use models\Database2;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;
use pages\backoffice\contacts\classes\Contacts;

session_start();

global $userRole, $userName, $allowCFPBAuditing, $userGroup,
       $allowToCFPBSubmitForPC, $allowEmpToCreateBranch, $externalBroker;

require '../includes/util.php';
require CONST_ROOT_PATH . 'backoffice/initPageVariables.php';
require CONST_ROOT_PATH . 'backoffice/getPageVariables.php';

$contactsArray = [];
$contacts = [];
if (Request::isset('query')) $searchTerm = Request::GetClean('query') ?? null;
if (Request::isset('PCID')) $PCID = Request::GetClean('PCID') ?? null;

$contactsArray = Contacts::getReport(
    $PCID,
    $searchTerm,
    null,
    0,
    0,
    0,
    null,
    null
);
foreach ($contactsArray as $contact){
    $contacts[] = [
        'id' => $contact->CID,
        'text' => Strings::processString(trim($contact->contactName . ' - ' . $contact->contactLName)),
    ];
}
HTTP::ExitJSON([
    'items' => $contacts,
]);
