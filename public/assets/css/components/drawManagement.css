.category-details {
    flex-grow: 1;
}
.category-details h5 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
    font-weight: 600;
}
.category-details p {
    margin-bottom: 0;
    font-size: 0.875rem;
}
.actions a {
    margin-left: 0.75rem;
}
.actions a:hover {
    color: #007bff;
}

.categories-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.category-item {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.category-item:hover, .editable-line-item:hover  {
    background-color: #f3f6f9;
    cursor: move;
}

.category-item .actions {
    display: flex;
    flex-shrink: 0;
}

.category-item .actions a {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    padding: 5px;
}

i.fas:hover {
    color: #181c32;
}
i.fa-trash-alt:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .categories-container {
        grid-template-columns: 1fr;
    }
}

/* Stepper Styling */
.stepper-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: relative;
}

.stepper-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    border-top: 1px solid #ccc;
    z-index: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    z-index: 1;
    background: white;
    padding: 0 10px;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #ccc;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-bottom: 5px;
}

.step-label {
    font-size: 0.9em;
    color: #555;
}

.step.active .step-icon {
    background-color: #3699ff;
}

.step.active .step-label {
    font-weight: bold;
    color: #000;
}
.sortable-ghost {
    opacity: 0.4;
    background-color: #e9ecef;
    border: 2px dashed #adb5bd;
}

.line-item-table input {
    border-radius: 4px;;
}

.add-line-item-row {
    background-color: #fffbe6;
    cursor: pointer;
}

.add-line-item-row:hover {
    background-color: #fffacd;
}

.add-line-item-row td {
    font-style: italic;
}

.line-item-input {
    width: 100%;
    border: 1px solid #ced4da;
    padding: 5px;
    box-sizing: border-box;
}

.line-item-category-header{
    cursor: pointer;
}

.line-item-category-header[aria-expanded="true"] .collapse-icon {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}
.line-item-category-header[aria-expanded="false"] .collapse-icon {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

.line-item-display {
    display: block;
}

.line-item-display::after {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f303";
    margin-left: 10px;
    transform: translateY(-50%);
    color: #b5b5c3;
    pointer-events: none;
}

.line-item-display:not(:empty)::after {
    display: none;
}

.line-item-display:not(:empty):hover::after {
    display: inline;
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: .875em;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

