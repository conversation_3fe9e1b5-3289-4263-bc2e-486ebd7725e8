$(document).ready(function() {
    let currentCategoriesData = [];
    let categoriesModified = false;
    let lineItemsModified = false;

    const $categoriesContainer = $('.categories-container');
    const $saveCatBtn = $('.save-cat');
    const $saveCategoryModalBtn = $('#saveCategoryModalBtn');
    const pcid = $('#pcid').val();
    const $openAddCategoryModalBtn = $('#openAddCategoryModalBtn');
    const MAX_CATEGORIES = parseInt($('#maxCategories').val());

    const $lineItemCategoriesContainer = $('#lineItemCategoriesContainer');
    const $saveLineItemsBtn = $('.save-line-items');

    // --- Helper: Form Validation (Category Modal) ---
    function validateCategoryForm() {
        let isValid = true;
        const $nameField = $('#modalCategoryName');
        $nameField.removeClass('is-invalid').siblings('.invalid-feedback').hide();

        if ($nameField.val().trim() === '') {
            $nameField.addClass('is-invalid').siblings('.invalid-feedback').show();
            isValid = false;
        }
        return isValid;
    }

    // --- Helper: Open and prepare the category modal ---
    function openCategoryModal(mode, categoryData = {}) {
        const $modal = $('#categoryFormModal');
        const $form = $('#categoryForm');
        $form[0].reset();
        $('#modalCategoryName').removeClass('is-invalid').siblings('.invalid-feedback').hide();

        if (mode === 'add') {
            $('#categoryModalLabel').text('Add New Category');
            $saveCategoryModalBtn.text('Add Category');
            $('#modalCategoryId').val('');
        } else if (mode === 'edit' && categoryData.id) {
            $('#categoryModalLabel').text('Edit Category');
            $saveCategoryModalBtn.text('Save Changes');
            $('#modalCategoryId').val(categoryData.id);
            $('#modalCategoryName').val(categoryData.name);
            $('#modalCategoryDescription').val(categoryData.description);
        } else {
            console.error("Invalid mode or missing data for category modal.");
            return;
        }
        $modal.modal('show');
    }

    // --- Event Listener: "Add Category" button ---
    $openAddCategoryModalBtn.on('click', function() {
        if (!checkAddMoreCategories(true)) {
            toastrNotification('Maximum number of categories reached.', 'warning');
            return;
        }
        openCategoryModal('add');
    });

    // --- Event Delegation: "Edit Category" icon ---
    $categoriesContainer.on('click', '.edit-category-btn', function(e) {
        e.preventDefault();
        const $categoryItem = $(this).closest('.category-item');
        const categoryData = {
            id: $categoryItem.data('category-id'),
            name: $categoryItem.data('name'),
            description: $categoryItem.data('description')
        };
        openCategoryModal('edit', categoryData);
    });

    // --- Event Listener: Save button in the Category Modal ---
    $saveCategoryModalBtn.on('click', function() {
        if (!validateCategoryForm()) {
            return;
        }

        const categoryId = $('#modalCategoryId').val();
        const name = $('#modalCategoryName').val().trim();
        const description = $('#modalCategoryDescription').val().trim();
        const template = document.getElementById('category-item-template');

        if (categoryId) {
            const $categoryItem = $(`.category-item[data-category-id="${categoryId}"]`);
            if ($categoryItem.length) {
                $categoryItem.find('.category-name').text(name);
                $categoryItem.find('.category-description').text(description);
                $categoryItem.data('name', name);
                $categoryItem.data('description', description);
                $categoryItem.attr('data-name', name);
                $categoryItem.attr('data-description', description);
            }
        } else {
            if (!checkAddMoreCategories(true)) return;

            const newId = 'new_cat_' + Date.now();
            const clone = template.content.cloneNode(true);
            const $newCategoryItem = $(clone).find('.category-item');

            $newCategoryItem.attr('data-category-id', newId);
            $newCategoryItem.attr('data-name', name);
            $newCategoryItem.attr('data-description', description);
            $newCategoryItem.find('.category-name').text(name);
            $newCategoryItem.find('.category-description').text(description);

            $categoriesContainer.find('.no-cat').remove();
            $categoriesContainer.append($newCategoryItem);
        }

        categoriesModified = true;
        $saveCatBtn.prop('disabled', false);
        $('#categoryFormModal').modal('hide');
        checkAddMoreCategories();
    });

    $categoriesContainer.on('click', '.delete-category-btn', async function(e) {
        e.preventDefault();
        const $categoryItem = $(this).closest('.category-item');
        const confirm = await showConfirmationModal('Are you sure you want to delete this category?');
        if (confirm) {
            $categoryItem.remove();
            categoriesModified = true;
            $saveCatBtn.prop('disabled', false);
            if ($categoriesContainer.find('.category-item').length === 0) {
                $categoriesContainer.append('<p class="no-cat">No categories added. Click "Add Category" to begin.</p>');
            }
            checkAddMoreCategories();
        }
    });

    // --- Function to fetch and render categories  ---
    function fetchAndRenderCategories() {
        return new Promise((resolve, reject) => {
            if (!pcid) {
                console.error('PCID is missing. Cannot load categories.');
                $categoriesContainer.html('<p class="text-danger">Error: Configuration problem (PCID missing). Cannot load categories.</p>');
                return;
            }

            $.ajax({
                url: `/backoffice/api_v2/DrawManagement/SowCategories?pcid=${pcid}`,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.data) {
                        currentCategoriesData = response.data;
                        renderCategoriesUI();
                        categoriesModified = false;
                        $saveCatBtn.prop('disabled', true);
                        resolve(response.data);
                    } else {
                        console.error('Failed to fetch categories:', response.message);
                        $categoriesContainer.html(`<p class="text-danger">Error loading categories: ${response.message || 'Unknown server error'}</p>`);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching categories:', status, error);
                    let errorMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
                    $categoriesContainer.html(`<p class="text-danger">Error loading categories: ${errorMsg}</p>`);
                },
                complete: function () {
                }
            });
        });
    }

    // --- Function to render categories in the UI ---
    function renderCategoriesUI() {
        const categories = currentCategoriesData;
        const template = document.getElementById('category-item-template');
        $categoriesContainer.find('.category-item, .no-cat').remove();

        if (!categories || categories.length === 0) {
            $categoriesContainer.append('<p class="no-cat">No categories found. Click "Add Category" to begin.</p>');
        } else {
            Object.entries(categories).forEach(([key, category]) => {
                const clone = template.content.cloneNode(true);
                const $categoryItem = $(clone).find('.category-item');

                $categoryItem.attr('data-category-id', category.id);
                $categoryItem.attr('data-name', category.name);
                $categoryItem.attr('data-description', category.description);
                $categoryItem.find('.category-name').text(category.name);
                $categoryItem.find('.category-description').text(category.description);

                $categoriesContainer.append($categoryItem);
            });
        }
        checkAddMoreCategories();
    }

    // --- Initialize Sortable for categories ---
    new Sortable($categoriesContainer[0], {
        animation: 150,
        ghostClass: 'sortable-ghost',
        handle: '.category-item',
        onEnd: function(evt) {
            categoriesModified = true;
            $saveCatBtn.prop('disabled', false);
        }
    });

    // --- Function to check and manage "Add Category" button state ---
    const checkAddMoreCategories = (showAlert = false) => {
        let numCategories = $categoriesContainer.children('.category-item').length;
        if (numCategories >= MAX_CATEGORIES) {
            $openAddCategoryModalBtn.prop('disabled', true).attr('title', 'Maximum number of categories reached.');
            if (showAlert) toastrNotification('Maximum number of categories (' + MAX_CATEGORIES + ') reached.', 'error');
            return false;
        }
        $openAddCategoryModalBtn.prop('disabled', false).attr('title', '');
        return true;
    };

    // --- Function to build categories JSON for saving ---
    function buildCategoriesJson() {
        var categories = [];
        $categoriesContainer.find('.category-item').each(function(index) {
            var $this = $(this);
            var id = $this.data('category-id');
            var name = $this.find('.category-name').text();
            var description = $this.find('.category-description').text();
            var order = index + 1;

            if (typeof id === 'string' && id.startsWith('new_cat_')) {
                 id = null;
            } else {
                 id = parseInt(id, 10);
            }
            categories.push({ id: id, name: name, description: description, order: order });
        });

        return categories;
    }

    // --- Handle save category button click (Categories Step) ---
    $saveCatBtn.on('click', function() {
        const categoriesToSave = buildCategoriesJson();
        const $saveBtn = $(this);
        const btnText = $saveBtn.text();

        if (!pcid) { toastrNotification('Error: PCID is missing.', 'error'); return; }

        $saveBtn.prop('disabled', true).text('Saving...');
        $.ajax({
            url: '/backoffice/api_v2/DrawManagement/SowCategories',
            type: 'POST',
            data: JSON.stringify({ pcid: pcid, categories: categoriesToSave }),
            dataType: 'json',
            ContentType: 'application/json',
            success: function(response) {
                if (response.success && response.data) {
                    currentCategoriesData = response.data;
                    renderCategoriesUI();
                    categoriesModified = false;
                    toastrNotification('Categories saved successfully!', 'success');
                    $saveCatBtn.prop('disabled', true);
                } else {
                    toastrNotification('Error saving categories: ' + (response.message || 'Unknown error', 'error'));
                    $saveCatBtn.prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                let errMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
                toastrNotification('Error saving categories: ' + errMsg, 'error');
                $saveCatBtn.prop('disabled', false);
            },
            complete: function() {
                $saveBtn.text(btnText);
                if(categoriesModified == false && $saveBtn.attr('id') === 'save-next') {
                    switchStep('line-items');
                }
            }
        });
    });

    // --- Function to Render Line Items for ALL Categories ---
    function renderLineItemsForCategoriesUI() {
        $lineItemCategoriesContainer.empty();
        const categories = currentCategoriesData;
        if (!categories || categories.length === 0) {
            $lineItemCategoriesContainer.html('<p class="text-muted text-center mt-5">No categories defined. Please go back to the "Categories" step to add them.</p>');
            return;
        }
        const categoryCardTemplate = document.getElementById('line-item-category-card-template');
        if (!categoryCardTemplate) { console.error("line-item-category-card-template not found!"); return; }
        Object.entries(categories).forEach(([key, category]) => {
            const clone = categoryCardTemplate.content.cloneNode(true);
            const $categoryCard = $(clone).find('.line-item-category-section');
            const collapseId = `collapseCategory_${category.id || ('temp' + index)}`;
            const lineItems = category['lineItems'];

            $categoryCard.find('.category-name').text(category.name.toUpperCase());
            $categoryCard.find('.category-description').text(category.description || '');
            $categoryCard.find('.line-item-category-header')
                .attr('data-target', `#${collapseId}`)
                .attr('aria-controls', collapseId);
            const $collapseTarget = $categoryCard.find('.category-collapse-placeholder');
            $collapseTarget.attr('id', collapseId);

            if (key < 5) {
                $collapseTarget.addClass('show');
                $categoryCard.find('.line-item-category-header').attr('aria-expanded', 'true');
            } else {
                $collapseTarget.removeClass('show');
                $categoryCard.find('.line-item-category-header').attr('aria-expanded', 'false');
            }

            $categoryCard.attr('data-category-id', category.id);
            $categoryCard.find('.line-items-tbody-placeholder').attr('data-category-id', category.id);
            $categoryCard.find('.add-line-item-row').attr('data-category-id', category.id);

            $lineItemCategoriesContainer.append($categoryCard);
            renderLineItemsForCategory(lineItems, category.id, $categoryCard.find('.line-items-tbody-placeholder'));
        });
        initSortableLineItems();
    }

    // --- Function to Fetch and Render Line Items for a SINGLE Category ---
    function renderLineItemsForCategory(lineItemsData, categoryId, $tbodyElement) {
        if (typeof categoryId === 'string' && categoryId.startsWith('new_cat_')) {
            renderLineItemRowsUI([], $tbodyElement);
            return Promise.resolve({ success: true, data: [] });
        } else {
            categoryId = parseInt(categoryId, 10);
            renderLineItemRowsUI(lineItemsData, $tbodyElement);
        }
    }

    function fetchLineItemsData() {
        return $.ajax({
            url: `/backoffice/api_v2/DrawManagement/SowLineItems?pcid=${pcid}`,
            type: 'GET',
            dataType: 'json'
        }).done(function(response) {
            if (response.success && response.data) {
                currentCategoriesData = response.data;
                $saveLineItemsBtn.prop('disabled', true);
                lineItemsModified = false;
                renderLineItemsForCategoriesUI();
            } else {
                console.error(`Failed to fetch line items for category ${categoryId}:`, response.message);
                $lineItemCategoriesContainer.html(`<tr><td colspan="3" class="text-danger text-center">Error loading line items: ${response.message || 'Unknown error'}</td></tr>`);
            }
        }).fail(function(xhr, status, error) {
            console.error(`AJAX error fetching line items for category ${categoryId}:`, error);
            let errorMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
            $lineItemCategoriesContainer.html(`<tr><td colspan="3" class="text-danger text-center">Error loading line items: ${errorMsg}</td></tr>`);
        });
    }

    // --- Function to Render Line Item Rows into a tbody ---
    function renderLineItemRowsUI(lineItems, $tbodyElement) {
        $tbodyElement.empty();
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) { console.error("line-item-row-template not found!"); return; }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');
                $row.attr('data-line-item-id', item.id);
                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);
                $row.find('.line-item-description-display').text(item.description || '');
                $row.find('.line-item-description-input').val(item.description || '');
                $tbodyElement.append($row);
            });
        }
        lineItemsModified = false;
        $saveLineItemsBtn.prop('disabled', true);
    }

    // --- Initialize Sortable for Line Item tables ---
    function initSortableLineItems() {
        const sortableLineItemLists = document.querySelectorAll('#lineItemCategoriesContainer .line-item-table tbody.sortable');
        sortableLineItemLists.forEach(function(listEl) {
            if (listEl.sortableInstance) { listEl.sortableInstance.destroy(); }
            listEl.sortableInstance = new Sortable(listEl, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                filter: '.sortable-disabled',
                onEnd: function(evt) {
                    lineItemsModified = true;
                    $saveLineItemsBtn.prop('disabled', false);
                }
            });
        });
    }

    // Inline Editing: Click on TD (not the last one with actions)
    $lineItemCategoriesContainer.on('click', '.editable-line-item td:not(:last-child)', function() {
        const $td = $(this);
        $td.closest('tr').find('.line-item-input:visible').not($td.find('.line-item-input')).each(function() {
            $(this).trigger($.Event('keypress', { which: 13 }));
        });
        $td.find('.line-item-display').addClass('d-none');
        $td.find('.line-item-input').removeClass('d-none').focus();
    });

    // Inline Editing: Focusout or Enter on input
    $lineItemCategoriesContainer.on('focusout keypress', '.line-item-input', function(e) {
        if (e.type === 'focusout' || (e.type === 'keypress' && e.which === 13)) {
            e.preventDefault();
            const $input = $(this);
            const $display = $input.siblings('.line-item-display');
            const newValue = $input.val().trim();
            const $row = $input.closest('tr');
            const lineItemId = $row.data('line-item-id');

            if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_') && $input.hasClass('line-item-name-input') && newValue === '') {
                $row.remove();
                return;
            }
            $display.text(newValue).removeClass('d-none');
            $input.addClass('d-none');

            // For new items, store value on data attr for buildJson if not yet in currentLineItemsData
             if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_')) {
                 if ($input.hasClass('line-item-name-input')) $row.data('new-name', newValue);
                 if ($input.hasClass('line-item-description-input')) $row.data('new-description', newValue);
             }
            lineItemsModified = true;
            $saveLineItemsBtn.prop('disabled', false);
        }
    });

    // Add New Line Item
    $lineItemCategoriesContainer.on('click', '.add-new-line-item-link', function(e) {
        e.preventDefault();
        const $addRowClicked = $(this).closest('.add-line-item-row');
        const categoryId = $addRowClicked.data('category-id');
        const $tbody = $addRowClicked.closest('table').find('tbody.sortable');
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) return;

        const $lastEditableItem = $tbody.find('.editable-line-item:last');
        if ($lastEditableItem.length > 0) {
            const $nameInputOfLast = $lastEditableItem.find('.line-item-name-input');
            if ($nameInputOfLast.val().trim() === '' && $lastEditableItem.find('.line-item-name-display').text().trim() === '') {
                 $lastEditableItem.find('.line-item-name-display').addClass('d-none');
                 $nameInputOfLast.removeClass('d-none').focus();
                 toastrNotification('Please complete the current new line item first.', 'info');
                 return;
            }
        }
        const clone = rowTemplate.content.cloneNode(true);
        const $newRow = $(clone).find('tr');
        const newLineItemId = 'new_li_' + Date.now();
        $newRow.attr('data-line-item-id', newLineItemId).attr('data-category-id', categoryId);
        $tbody.append($newRow);
        const $firstInput = $newRow.find('.line-item-name-input');
        $firstInput.siblings('.line-item-display').addClass('d-none');
        $firstInput.removeClass('d-none').focus();
        lineItemsModified = true;
        $saveLineItemsBtn.prop('disabled', false);
    });

    // Remove Line Item
    $lineItemCategoriesContainer.on('click', '.remove-line-item-btn', async function(e) {
        e.preventDefault();
        const $row = $(this).closest('tr');
        const confirm = await showConfirmationModal('Are you sure you want to delete this line item?');
        if (confirm) {
            $row.remove();
            lineItemsModified = true;
            $saveLineItemsBtn.prop('disabled', false);
        }
    });

    // --- Function to build Line Items JSON (grouped by category) for saving ---
    function buildLineItemsJson() {
        const groupedLineItems = {};

        $lineItemCategoriesContainer.find('.line-item-category-section').each(function() {
            const $categoryCard = $(this);
            const categoryId = $categoryCard.data('category-id');
            if (!categoryId) {
                return;
            }

            const itemsForThisCategory = [];
            $categoryCard.find('tbody.sortable tr.editable-line-item').each(function(index) {
                const $row = $(this);
                let lineItemId = $row.data('line-item-id');
                let name, description;

                // For new items, get data from inputs or data attributes if they were blurred
                if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_')) {
                    name = $row.find('.line-item-name-input').val().trim() || $row.data('new-name');
                    description = $row.find('.line-item-description-input').val().trim() || $row.data('new-description');

                    if (!name) {
                        return;
                    }
                    lineItemId = null;
                } else {
                    name = $row.find('.line-item-name-display').text().trim();
                    description = $row.find('.line-item-description-display').text().trim();
                    lineItemId = parseInt(lineItemId, 10);
                }

                itemsForThisCategory.push({
                    id: lineItemId,
                    categoryId: categoryId,
                    name: name,
                    description: description || '',
                    order: index + 1
                });
            });
            if (itemsForThisCategory.length === 0) {
                groupedLineItems[categoryId] = [];
            }
            groupedLineItems[categoryId] = itemsForThisCategory;
        });

        return groupedLineItems;
    }

    // --- Handle "Save Line Items" button click ---
    $saveLineItemsBtn.on('click', async function() {
        const lineItemsJson = buildLineItemsJson();

        $(this).prop('disabled', true).text('Saving...');
        $.ajax({
            url: '/backoffice/api_v2/DrawManagement/SowLineItems',
            type: 'POST',
            data: JSON.stringify({ pcid: pcid, lineItems: lineItemsJson }),
            dataType: 'json',
            ContentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    toastrNotification('Line items saved successfully!', 'success');
                    lineItemsModified = false;
                } else {
                    toastrNotification('Error saving line items: ' + (response.message || 'Unknown error', 'error'));
                    $saveLineItemsBtn.prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                let errMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
                toastrNotification('Error saving line items: ' + errMsg, 'error');
                $saveLineItemsBtn.prop('disabled', false);
            },
            complete: function() {
                $saveLineItemsBtn.text('Save Line Items');
            }
        });
    });


    // == STEPPER LOGIC
    const steps = document.querySelectorAll('.step');
    const stepContents = document.querySelectorAll('.step-content > div');

    async function switchStep(stepElementOrName) {
        let targetStepName;
        let stepElement;

        if (typeof stepElementOrName === 'string') {
            targetStepName = stepElementOrName;
            stepElement = document.querySelector(`.stepper-container .step[data-step="${targetStepName}"]`);
        } else if (stepElementOrName && stepElementOrName.jquery) {
            stepElement = stepElementOrName[0];
            targetStepName = $(stepElementOrName).data('step');
        } else {
            stepElement = stepElementOrName;
            targetStepName = stepElement.dataset.step;
        }

        if (!stepElement) {
            return;
        }
        if (stepElement.classList.contains('active')) {
            return;
        }

        if (categoriesModified || lineItemsModified) {
            const confirm = await showConfirmationModal(
                'You have unsaved changes. Are you sure you want to leave without saving?',
            );
            if (!confirm) {
                return;
            }
        }

        document.querySelectorAll('.stepper-container .step').forEach(s => s.classList.remove('active'));
        document.querySelectorAll('.step-content > div').forEach(content => content.style.display = 'none');

        stepElement.classList.add('active');
        const targetContent = document.getElementById(`content-step-${targetStepName}`);
        if (targetContent) {
            targetContent.style.display = 'block';
        } else {
            stepElement.classList.remove('active');
            return;
        }

        if (targetStepName === 'line-items') {
            fetchLineItemsData();
        } else {
            fetchAndRenderCategories()
            .then(() => {
                checkAddMoreCategories();
            });
        }
        categoriesModified = false;
        lineItemsModified = false;
    }

    steps.forEach(step => {
        step.addEventListener('click', function() {
            switchStep(this);
        });
    });

    $('.switch-step').on('click', function() {
        const targetStepName = $(this).data('target-step');
        switchStep(targetStepName);
    });

    // --- Initial Load ---
    if (document.getElementById('content-step-categories')) {
        document.getElementById('content-step-categories').style.display = 'block';
        fetchAndRenderCategories()
            .then(() => {
                checkAddMoreCategories();
            });
    } else {
        console.error("Element 'content-step-categories' not found.");
    }

    function showConfirmationModal(message, confirmButtonText = "Confirm", cancelButtonText = "Cancel") {
        return new Promise((resolve) => {
            const modalElement = $('#confirmationModal');
            const modalBodyElement = $('#confirmationModalBody');
            const confirmBtn = $('#confirmModalConfirmBtn');
            const cancelBtn = $('#confirmModalCancelBtn');
            confirm = false;

            modalBodyElement.html(message);
            confirmBtn.text(confirmButtonText);
            cancelBtn.text(cancelButtonText);

            confirmBtn.off('click');
            cancelBtn.off('click');
            modalElement.off('hidden.bs.modal');

            confirmBtn.on('click', () => {
                modalElement.modal('hide');
                resolve(true);
            });

            cancelBtn.on('click', () => {
                resolve(false);
            });

            modalElement.on('hidden.bs.modal', () => {
                resolve(false);
            });

            modalElement.modal('show');
        });
    }

});
