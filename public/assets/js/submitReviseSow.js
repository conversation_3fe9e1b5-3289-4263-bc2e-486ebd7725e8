$(document).ready(function() {
    let currentCategoriesData = [];
    let lineItemsModified = false;

    const pcid = $('#pcid').val(); // Assuming a pcid input exists on the page
    const $lineItemCategoriesContainer = $('#lineItemCategoriesContainer');
    const $saveBtn = $('#save-sow'); // Assuming a save button with this ID

    // --- Function to Render Line Items for ALL Categories ---
    function renderLineItemsForCategoriesUI() {
        $lineItemCategoriesContainer.empty();
        const categories = currentCategoriesData;
        if (!categories || categories.length === 0) {
            $lineItemCategoriesContainer.html('<p class="text-muted text-center mt-5">No categories found.</p>');
            return;
        }
        const categoryCardTemplate = document.getElementById('line-item-category-card-template');
        if (!categoryCardTemplate) { console.error("line-item-category-card-template not found!"); return; }

        Object.entries(categories).forEach(([key, category]) => {
            const clone = categoryCardTemplate.content.cloneNode(true);
            const $categoryCard = $(clone).find('.line-item-category-section');
            const collapseId = `collapseCategory_${category.id || ('temp' + key)}`;
            const lineItems = category['lineItems'];

            $categoryCard.find('.category-name').text(category.name.toUpperCase());
            $categoryCard.find('.category-description').text(category.description || '');
            $categoryCard.find('.line-item-category-header')
                .attr('data-target', `#${collapseId}`)
                .attr('aria-controls', collapseId);
            const $collapseTarget = $categoryCard.find('.category-collapse-placeholder');
            $collapseTarget.attr('id', collapseId);

            $collapseTarget.addClass('show');
            $categoryCard.find('.line-item-category-header').attr('aria-expanded', 'true');

            $categoryCard.attr('data-category-id', category.id);
            $categoryCard.find('.line-items-tbody-placeholder').attr('data-category-id', category.id);
            $categoryCard.find('.add-line-item-row').attr('data-category-id', category.id);

            $lineItemCategoriesContainer.append($categoryCard);
            renderLineItemRowsUI(lineItems, $categoryCard.find('.line-items-tbody-placeholder'));
        });
    }

    // --- Function to Fetch Line Items Data ---
    function fetchLineItemsData() {
        // TODO: Adjust API endpoint if necessary
        return $.ajax({
            url: `/backoffice/api_v2/DrawManagement/SowLineItems?pcid=${pcid}`,
            type: 'GET',
            dataType: 'json'
        }).done(function(response) {
            if (response.success && response.data) {
                currentCategoriesData = response.data;
                lineItemsModified = false;
                renderLineItemsForCategoriesUI();
            } else {
                console.error(`Failed to fetch line items:`, response.message);
                $lineItemCategoriesContainer.html(`<div class="text-danger text-center">Error loading line items: ${response.message || 'Unknown error'}</div>`);
            }
        }).fail(function(xhr, status, error) {
            console.error(`AJAX error fetching line items:`, error);
            let errorMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
            $lineItemCategoriesContainer.html(`<div class="text-danger text-center">Error loading line items: ${errorMsg}</div>`);
        });
    }

    // --- Function to Render Line Item Rows into a tbody ---
    function renderLineItemRowsUI(lineItems, $tbodyElement) {
        $tbodyElement.empty();
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) { console.error("line-item-row-template not found!"); return; }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');
                $row.attr('data-line-item-id', item.id);
                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);

                // Handle new fields
                $row.find('input[placeholder="0.00"]').val(item.cost || '');
                const completedInputs = $row.find('input[placeholder="0"]');
                if(completedInputs.length > 1) {
                    $(completedInputs[0]).val(item.percent_completed || '');
                    $(completedInputs[1]).val(item.dollar_completed || '');
                }
                $row.find('.note-btn').data('note', item.notes || '');

                $tbodyElement.append($row);
            });
        }
        lineItemsModified = false;
    }

    // --- Event Handlers for inline editing, adding, and removing line items ---
    // (This part can be simplified if adding/removing/editing names is not allowed in this view)

    // Add New Line Item (if allowed)
    $lineItemCategoriesContainer.on('click', '.add-new-line-item-link', function(e) {
        e.preventDefault();
        // Logic to add a new row, similar to drawManagement.js
        // For simplicity, this is omitted, assuming line items are fixed for this view.
        // If adding is needed, copy the logic from drawManagement.js
        toastrNotification('Adding new line items is not enabled in this view.', 'info');
    });

    // Remove Line Item (if allowed)
    $lineItemCategoriesContainer.on('click', '.remove-line-item-btn', async function(e) {
        e.preventDefault();
        // Logic to remove a row
        toastrNotification('Removing line items is not enabled in this view.', 'info');
    });


    // --- Function to build Line Items JSON for saving ---
    function buildLineItemsJson() {
        const allLineItems = [];
        $lineItemCategoriesContainer.find('.editable-line-item').each(function() {
            const $row = $(this);
            const costInput = $row.find('input[placeholder="0.00"]');
            const completedInputs = $row.find('input[placeholder="0"]');

            const item = {
                id: $row.data('line-item-id'),
                cost: costInput.val(),
                percent_completed: $(completedInputs[0]).val(),
                dollar_completed: $(completedInputs[1]).val(),
                notes: $row.find('.note-btn').data('note') || ''
            };
            allLineItems.push(item);
        });
        return allLineItems;
    }

    // --- Handle Save button click ---
    $saveBtn.on('click', function() {
        const lineItemsToSave = buildLineItemsJson();

        // TODO: Adjust API endpoint for saving
        const saveUrl = '/api/v1/submitReviseSow';

        $(this).prop('disabled', true).text('Saving...');
        $.ajax({
            url: saveUrl,
            type: 'POST',
            data: JSON.stringify({ pcid: pcid, lineItems: lineItemsToSave }),
            dataType: 'json',
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    toastrNotification('Scope of Work saved successfully!', 'success');
                    // Optionally redirect or update UI
                } else {
                    toastrNotification('Error saving: ' + (response.message || 'Unknown error'), 'error');
                    $saveBtn.prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                let errMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : error;
                toastrNotification('Error saving: ' + errMsg, 'error');
                $saveBtn.prop('disabled', false);
            },
            complete: function() {
                $saveBtn.text('Save');
            }
        });
    });

    // --- Initial Load ---
    if (pcid) {
        fetchLineItemsData();
    } else {
        console.error("PCID is missing. Cannot load data.");
        $lineItemCategoriesContainer.html('<div class="text-danger text-center">Configuration error: PCID is missing.</div>');
    }

    // --- Notes Modal Logic ---
    let currentNoteBtn;
    $('#noteModal').on('show.bs.modal', function (event) {
        currentNoteBtn = $(event.relatedTarget);
        const noteText = currentNoteBtn.data('note') || '';
        $('#noteTextarea').val(noteText);
    });

    $('#saveNoteBtn').on('click', function () {
        const updatedNote = $('#noteTextarea').val();
        currentNoteBtn.data('note', updatedNote);
        if (updatedNote.trim()) {
            currentNoteBtn.find('i').addClass('text-primary'); // Or some other visual indicator
        } else {
            currentNoteBtn.find('i').removeClass('text-primary');
        }
        $('#noteModal').modal('hide');
        lineItemsModified = true; // Mark as modified
    });
});
