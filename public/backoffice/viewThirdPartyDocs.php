<?php

use models\composite\oThirdPartyServices\viewThirdPartyDocs;
use models\standard\Arrays;

session_start();
require '../includes/util.php';
$response = viewThirdPartyDocs::getReport(Arrays::getArrayValue('tID', $_REQUEST));
$resXml = urldecode($response['response_xml']);
$xml = @simplexml_load_string($resXml);
$json = json_encode($xml);
$jsonArray = json_decode($json, true);

if ($response['cra'] === 'xactus') {
    $MIMEType = Arrays::getArrayValue('MIMETypeIdentifier', $jsonArray['DOCUMENT_SETS']['DOCUMENT_SET']['DOCUMENTS']['DOCUMENT']['VIEWS']['VIEW']['VIEW_FILES']['VIEW_FILE'][0]['FOREIGN_OBJECT']);
    $pdf_content = Arrays::getArrayValue('EmbeddedContentXML', $jsonArray['DOCUMENT_SETS']['DOCUMENT_SET']['DOCUMENTS']['DOCUMENT']['VIEWS']['VIEW']['VIEW_FILES']['VIEW_FILE'][0]['FOREIGN_OBJECT']);
    if (empty($pdf_content)) {
        $xml = new DOMDocument();
        $xml->loadXML($resXml);
        $xmlObject = $xml->getElementsByTagName('DOCUMENT');
        $MIMEType = 'application/pdf';
        $pdf_content = $xmlObject->item(0)->nodeValue;
    }
} else {
    $MIMEType = Arrays::getArrayValue('MIMETypeIdentifier', $jsonArray['DOCUMENT_SETS']['DOCUMENT_SET']['DOCUMENTS']['DOCUMENT']['VIEWS']['VIEW']['VIEW_FILES']['VIEW_FILE']['FOREIGN_OBJECT']);
    $pdf_content = Arrays::getArrayValue('EmbeddedContentXML', $jsonArray['DOCUMENT_SETS']['DOCUMENT_SET']['DOCUMENTS']['DOCUMENT']['VIEWS']['VIEW']['VIEW_FILES']['VIEW_FILE']['FOREIGN_OBJECT']);
}

if ($pdf_content != '' && $MIMEType == 'application/pdf') {
    $data = base64_decode($pdf_content);
    header('Content-Type: application/pdf');
    echo $data;
    exit;
} else if (strpos($resXml, '<html>') !== false && $MIMEType == 'text/html') {
    /**
     * Removing xml content.
     */
    $resXml = substr($resXml, strpos($resXml, '<html>'));
    $resXml = substr($resXml, 0, strpos($resXml, '</html>') + 7);
    $resXml = str_replace('/*&lt;![CDATA[*/', '', $resXml);
    $resXml = str_replace('/*]]&gt;*/', '', $resXml);
    echo $resXml;
    exit;
} else {
    echo '<html>
            <head>
                <title>File Id is missing</title>
                <meta name="robots" content="noindex" />
            </head>
            <body>
            <center>
                <div style="margin-top:100px;width:600px;">
                <h1 style="color:red">File Id is missing.<br>Please contact your Account executives and ask them to resend the link.</h1>
                </div>
            </center>
            </body>
        </html>';
    exit;
}
