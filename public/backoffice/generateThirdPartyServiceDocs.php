<?php

use models\composite\oHMLOInfo\saveHMLOClientInfo;
use models\composite\oHMLOInfo\saveHMLOFile2Info;
use models\composite\oThirdPartyServices\businessCreditReportRequestAndRetrieve;
use models\composite\oThirdPartyServices\consumerCreditReportAndRetrieve;
use models\composite\oThirdPartyServices\saveLoanInformation;
use models\constants\gl\glCRAServices;
use models\cypher;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';

UserAccess::checkReferrerPgs(['url' => 'LMRequest.php']);
UserAccess::CheckAdminUse();

$executiveId = $LMRId = $responseId = 0;
$op = $goToTab = $isSave = $activeTab = '';
if (isset($_POST['encryptedEId'])) $executiveId = cypher::myDecryption(trim($_POST['encryptedEId']));
if (isset($_POST['encryptedLId'])) $LMRId = cypher::myDecryption(trim($_POST['encryptedLId']));
if (isset($_POST['encryptedRId'])) $responseId = trim(cypher::myDecryption(trim($_POST['encryptedRId'])));
if (isset($_POST['goToTab'])) $goToTab = trim($_POST['goToTab']);
if (isset($_POST['op'])) $op = trim($_POST['op']);
if (isset($_POST['isSave'])) $isSave = trim($_POST['isSave']);
if (isset($_POST['activeTab'])) $activeTab = trim($_POST['activeTab']);
/** Save file information */
$inputArray = [
    'LMRId' => $_POST['LMRId'],
    'PCID' => $_POST['FPCID'],
    'UID' => $_POST['UID'],
    'URole' => $_POST['URole'],
    'CID' => $_POST['selClientId'],
];
$_REQUEST['ssnNumber'] = Strings::getNumberValue($_REQUEST['ssnNumber']);
$_REQUEST['phoneNumber'] = Strings::getNumberValue($_REQUEST['phoneNumber']);
$_REQUEST['cellNumber'] = Strings::getNumberValue($_REQUEST['cellNumber']);
$_REQUEST['employer1Phone'] = Strings::getNumberValue($_REQUEST['employer1Phone']);
$_REQUEST['businessPhone'] = Strings::getNumberValue($_REQUEST['businessPhone']);
saveHMLOClientInfo::getReport($inputArray);
saveLoanInformation::getReport($inputArray);
saveHMLOFile2Info::getReport($inputArray);
$_REQUEST['borrowerDOB'] = Dates::formatDateWithRE($_REQUEST['borrowerDOB'], 'MDY', 'Y-m-d');
$redirect = strtok($_SERVER['HTTP_REFERER'], '?') . '?eId=' . cypher::myEncryption($executiveId) . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($responseId) . '&op=' . trim($op) . '&tabOpt=';
if (isset($_REQUEST['btnSave']) || isset($_REQUEST['psSubmit'])) $redirect .= $activeTab;
else $redirect .= $goToTab;
if (isset($_REQUEST['psSubmit'])) {
    $_POST['LoanIdentifier'] = Strings::cleanString(Arrays::getArrayValue('thirdPartyServices', $_POST)) . '_' . $_POST['LMRId']; //Loan identifier.
    if (Arrays::getArrayValue('thirdPartyServices', $_POST) == glCRAServices::BUSINESS_CREDIT_REPORT) { //Business Credit Report
        $responseArr = businessCreditReportRequestAndRetrieve::getReport();
    } else if (Arrays::getArrayValue('thirdPartyServices', $_POST) == glCRAServices::BORROWER_CREDIT_REPORT) { //Borrower Credit Report.
        $responseArr = consumerCreditReportAndRetrieve::getReport();
    }
}
Strings::SetSess('msg', 'Updated Successfully.');
header('Location: ' . $redirect);
exit;
