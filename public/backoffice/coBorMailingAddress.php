<?php
global $secArr, $allowToEdit, $hideThisField, $tabIndexNo, $stateArray,
       $coBorrowerMailingState, $coBMailingPropType, $fileMC;

use models\composite\oFile\getFileInfo\propertyCountyInfo;
use models\constants\gl\glPropTypeArray;
use models\constants\states;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\standard\BaseHTML;
use models\standard\Strings;

$glPropTypeArray = glPropTypeArray::$glPropTypeArray;

$cBorMallingCountyInfo = propertyCountyInfo::getReport(LMRequest::$activeTab, $coBorrowerMailingState);

$maillingCountyArray = [];
foreach ($cBorMallingCountyInfo as $countyKey => $countyValue) {
    $maillingCountyArray[$countyValue['countyName']] = $countyValue['countyName'];
}
?>

<div class="form-group col-lg-12" id="comailingaddtitle">
    <div class="row ">
        <label class="bg-secondary py-2 col-lg-12">
            <b><?php echo BaseHTML::getSubSectionHeading('CBI', 'coborrowerMaillingAddressSubSection'); ?></b>
        </label>
    </div>
</div>

<div class="comailingadd  col-md-6 <?php echo loanForm::showField('isborrowerAdd'); ?>">
    <div class="row form-group">
        <?php echo loanForm::label('isborrowerAdd', 'col-md-5 '); ?>
        <div class="col-md-7">
            <?php echo loanForm::switch(
                'mailingAddressAsBorrower',
                $allowToEdit,
                $tabIndexNo++,
                Strings::showField('mailingAddressAsBorrower', 'LMRInfo'),
                null,
                'toggleSwitch(\'mailingAddressAsBorrower_switch\', \'mailingAddressAsBorrower\', \'1\', \'0\' );autoPopulateMailingAddressAsFile(\'loanModForm\', \'\'); populateStateTimeZone(\'loanModForm\', \'coBorrowerMailingState\', \'coBorrowerTimeZone\');',
                '',
                'isborrowerAdd'
            ); ?>
        </div>
    </div>
</div>

<?php if ($hideThisField) { ?>
    <div class="comailingadd  col-md-6 <?php echo loanForm::showField('coBorrowerMailingAddress'); ?> ">
        <div class="row form-group">
            <script>
                $(document).ready(function () {
                    $('#coBorrowerMailingAddress').on('input', function () {
                        address_lookup.InitLegacy($(this));
                    });
                });
            </script>
            <?php echo loanForm::label('coBorrowerMailingAddress', 'col-md-5 '); ?>
            <div class="col-md-7">
                <?php echo loanForm::text(
                    'coBorrowerMailingAddress',
                    $allowToEdit,
                    $tabIndexNo++,
                    Strings::showField('coBorrowerMailingAddress', 'LMRInfo'),
                    null,
                    null,
                    null,
                    null,
                    [
                        'address' => 'coBorrowerMailingAddress',
                        'city'    => 'coBorrowerMailingCity',
                        'state'   => 'coBorrowerMailingState',
                        'zip'     => 'coBorrowerMailingZip',
                        'unit'    => 'coBorrowerMailingUnit',
                        'county'  => 'coBorrowerMailingCounty',
                    ]
                ); ?>
            </div>
        </div>
    </div>
<?php } ?>

<div class="comailingadd  col-md-6 <?php echo loanForm::showField('coBorrowerMailingUnit'); ?>">
    <div class="row form-group">
        <?php echo loanForm::label('coBorrowerMailingUnit', 'col-md-5 '); ?>
        <div class="col-md-7">
            <?php echo loanForm::text(
                'coBorrowerMailingUnit',
                $allowToEdit,
                $tabIndexNo++,
                LMRequest::myFileInfo()->file2Info()->coBorrowerMailingUnit,
            ); ?>
        </div>
    </div>
</div>


<div class="comailingadd  col-md-6 <?php echo loanForm::showField('coBorrowerMailingCity'); ?>">
    <div class="row form-group">
        <?php echo loanForm::label('coBorrowerMailingCity', 'col-md-5 '); ?>
        <div class="col-md-7">
            <?php echo loanForm::text(
                'coBorrowerMailingCity',
                $allowToEdit,
                $tabIndexNo++,
                Strings::showField('coBorrowerMailingCity', 'LMRInfo'),
            ); ?>
        </div>
    </div>
</div>

<div
        class="comailingadd  col-md-6 <?php echo loanForm::showField('coBorrowerMailingState'); ?>">
    <div class="row form-group">
        <?php echo loanForm::label('coBorrowerMailingState', 'col-md-5 '); ?>
        <div class="col-md-7">
            <?php echo loanForm::select(
                'coBorrowerMailingState',
                $allowToEdit,
                $tabIndexNo++,
                $coBorrowerMailingState,
                states::getOptions(),
                'populateStateTimeZone(\'loanModForm\', \'coBorrowerMailingState\', \'coBorrowerTimeZone\');populateStateCounty(\'loanModForm\', \'coBorrowerMailingState\', \'coBorrowerMailingCounty\')',
                '',
                '',
                'Please Select'
            ); ?>
        </div>
    </div>
</div>

<div class="comailingadd  col-md-6 <?php echo loanForm::showField('coBorrowerMailingZip'); ?> ">
    <div class="row form-group">
        <?php echo loanForm::label('coBorrowerMailingZip', 'col-md-5 '); ?>
        <div class="col-md-7">
            <?php echo loanForm::text(
                'coBorrowerMailingZip',
                $allowToEdit,
                $tabIndexNo++,
                Strings::showField('coBorrowerMailingZip', 'LMRInfo'),
            ); ?>
        </div>
    </div>
</div>

<div class="comailingadd col-md-6 <?php echo loanForm::showField('coBorrowerMailingCounty'); ?>">
    <div class="row form-group">
        <?php echo loanForm::label('coBorrowerMailingCounty', 'col-md-5'); ?>
        <div class="col-md-7">
            <?php echo loanForm::select(
                'coBorrowerMailingCounty',
                $allowToEdit,
                $tabIndexNo++,
                LMRequest::myFileInfo()->file2Info()->coBorrowerMailingCounty,
                $maillingCountyArray,
                '',
                ' input-sm ',
                '- Select -',
            ); ?>
        </div>
    </div>
</div>

<div class="comailingadd col-md-6 <?php echo loanForm::showField('coBMailingPropType'); ?>">
    <div class="row form-group">
        <?php echo loanForm::label('coBMailingPropType', 'col-md-5 '); ?>
        <div class="col-md-7">
            <?php echo loanForm::select(
                'coBMailingPropType',
                $allowToEdit,
                $tabIndexNo++,
                $coBMailingPropType,
                $glPropTypeArray,
                '',
                '',
                '- Select -',
                'Please Select'
            ); ?>
        </div>
    </div>
</div>
<script>
    $(function () {
        if ($('.comailingadd.secShow').length > 0) { //show title
            $("#comailingaddtitle").show();
        } else { //hide title
            $("#comailingaddtitle").hide();
        }
    });
</script>
<!-- coBorMailingAddress.php -->
