<?php

use models\composite\oFile\getFileInfo\thirdPartyServiceInfo;
use models\composite\oPC\PCInfo;
use models\composite\oThirdPartyServices\getCreditCardInformation;
use models\constants\gl\glThirdPartyServicesCRA;
use models\constants\gl\glCRAServices;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;
use models\Request;

global $myFileInfo, $thirdPartyServiceCSR, $thirdPartyServicesProducts, $PCID, $allowToEdit;
global $fileMC, $stateArray, $LMRId, $gltypeOfHMLOLoanRequesting, $userTimeZone;
global $userGroup;

$service = Request::GetClean("ser") ?? '';

$glThirdPartyServices = PageVariables::$glThirdPartyServices;
$glThirdPartyServicesLegalDocs = PageVariables::$glThirdPartyServicesLegalDocs;
$thirdPartyServices = PageVariables::$thirdPartyServices;
$thirdPartyServicesLegalDocs = PageVariables::$thirdPartyServicesLegalDocs;
$showCRA = $thirdPartyServices > 0 && $glThirdPartyServices > 0;
$showLegalDocs = $thirdPartyServicesLegalDocs > 0 && $glThirdPartyServicesLegalDocs > 0;
$activeCRA = $showCRA && !$service;
$activeLegalDocs = !$showCRA || $service === 'ld';
$activeTab = $activeCRA ? 'cra' : 'legalDocs';

$glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();

$thirdPartyServiceCSRArray = [];
$selThirdPartyServiceCSR = [];
$thirdPartyServicesProductsArray = [];
$ccInfo = [];

if ( !($showCRA || $showLegalDocs) ) {
    echo "<h1 style=\"color:red;text-align:center;margin-top:30px;\">You are not authorized to view this tab</h1>";
    exit;
}
//Super User
if ($userGroup == 'Super') {
    $pcData = PCInfo::getReport(['PCID' => $PCID]);
    if (count($pcData) > 0) {
        $thirdPartyServiceCSR = $pcData['thirdPartyServiceCSR'];
        $thirdPartyServicesProducts = $pcData['thirdPartyServicesProducts'];
    }
}
/* @var thirdPartyServiceInfo[] $thirdPartyServiceInfo */
$thirdPartyServiceInfo = $myFileInfo['thirdPartyServiceInfo'];
$thirdPartyServiceCSRArray = explode(',', $thirdPartyServiceCSR);
$thirdPartyServicesProductsArray = explode(',', $thirdPartyServicesProducts);
$coBorDisp = Strings::showField('isCoBorrower', 'LMRInfo') == '1';

$ccInfo = getCreditCardInformation::getReport([
    'paymentThrough' => 'Company Credit Card',
    'FPCID' => $PCID,
]);

foreach ($glThirdPartyServicesCRA as $cra => $craValue) {
    $creditReportArray = [glCRAServices::BORROWER_CREDIT_REPORT, glCRAServices::CO_BORROWER_CREDIT_REPORT, glCRAServices::JOINT_CREDIT_REPORT];
    $softReportArray = [glCRAServices::BORROWER_SOFT_PULL, glCRAServices::CO_BORROWER_SOFT_PULL, glCRAServices::JOINT_SOFT_PULL];
    $fraudReportArray = [glCRAServices::BORROWER_FRAUD, glCRAServices::CO_BORROWER_FRAUD, glCRAServices::JOINT_FRAUD];
    $floodReportArray = [glCRAServices::FLOOD, glCRAServices::CO_BORROWER_FLOOD, glCRAServices::JOINT_FLOOD];
    $creditReportSeparator = false;
    $softReportSeparator = false;
    $fraudReportSeparator = false;
    $floodReportSeparator = false;
    $otherReportSeparator = false;
    $tempService = [];
    if (in_array($cra, $thirdPartyServiceCSRArray)) {
        foreach ($craValue->Services as $productKey => $productName) {
            if (in_array($cra . '_' . $productKey, $thirdPartyServicesProductsArray)) {
                if ($cra === 'xactus' && $coBorDisp) {
                    if (!$creditReportSeparator && in_array($productKey, $creditReportArray)) {
                        $tempService['separator_full'] = '------------ Full Credit Reports ------------';
                        $creditReportSeparator = true;
                    }
                    if (!$softReportSeparator && in_array($productKey, $softReportArray)) {
                        $tempService['separator_soft'] = '---------------- Soft Pulls ----------------';
                        $softReportSeparator = true;
                    }
                    if (!$fraudReportSeparator && in_array($productKey, $fraudReportArray)) {
                        $tempService['separator_fraud'] = '-------------- Fraud Reports --------------';
                        $fraudReportSeparator = true;
                    }
                    if (!$floodReportSeparator && in_array($productKey, $floodReportArray)) {
                        $tempService['separator_flood'] = '-------------- Flood Reports --------------';
                        $floodReportSeparator = true;
                    }
                    if (!$otherReportSeparator && !in_array($productKey, array_merge($creditReportArray, $softReportArray, $fraudReportArray, $floodReportArray))) {
                        $tempService['separator_other'] = '-------------- Other Services --------------';
                        $otherReportSeparator = true;
                    }
                }
                if (!(strpos($productKey, 'coborrower') !== false || strpos($productKey, 'joint') !== false) || $coBorDisp) {
                    $tempService[$productKey] = $productName['Name'];
                }
            }
        }
        $selThirdPartyServiceCSR[$cra] = $tempService;
    }
}
?>
<script src="/backoffice/api/js/thirdPartyService.js?<?= CONST_JS_VERSION ?>"></script>
<script>
    let thirdPartyServiceInfo_AX = <?php echo trim(Arrays::getArrayValue('creditCardType', $ccInfo)) == 'AX' ? 1 : 0; ?>;
    let thirdPartyServiceInfo_PCID = <?php echo $PCID; ?>;
    let thirdPartyServiceInfo_LMRId = <?php echo $LMRId; ?>;
    let thirdPartyServiceInfo_services = <?php echo json_encode($selThirdPartyServiceCSR); ?>;
    $(document).ready(function() {ThirdPartyServiceInfoForm.enableTabData(<?php echo "'".$activeTab."'"; ?>)});
</script>
<script src="/backoffice/thirdPartyServiceInfoForm/js/thirdPartyServiceInfoForm.js?<?= CONST_JS_VERSION ?>"></script>
<script src="/backoffice/thirdPartyServiceInfoForm/js/thirdPartyServiceInfoFormLegalDocs.js?<?= CONST_JS_VERSION ?>"></script>
<script src="/backoffice/LMRequest/propertyInfo/js/Property.js?<?= CONST_JS_VERSION ?>"></script>

<style>
    .TPSDiv h2 {
        font-size: 20px;
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .TPSTbl td {
        font-size: 13px;
    }

    .avm_fields,
    .business_credit_fields,
    .consumer_credit_fields,
    .coborrower_credit_fields,
    .criminal_record_fields,
    .flood_fields,
    .mers_fields,
    .ssn_fields {
        display: none;
    }

    .line-separator {
        height: 1px;
        background-color: #ddd;
        width: 100%;
        margin-bottom: 15px;
    }
</style>

<!-- Section Start -->
<div class="card card-custom thirdPartServicesCard">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">3rd Party Integrations</h3>
        </div>

        <div class="card-toolbar">
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="toggle"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="javascript:void(0);"
               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
               data-card-tool="toggle"
               data-section="thirdPartServicesCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body thirdPartServicesCard_body">
        <ul class="nav nav-tabs" id="thirdPartyServicesMenu" role="tablist">
            <?php if ($showCRA) { ?>
            <li class="nav-item">
                <a class="nav-link  <?php if($activeCRA) echo 'active'; ?>" id="CRATabMenu" data-toggle="tab" onclick="ThirdPartyServiceInfoForm.enableTabData('cra')" href="#CRATab" role="tab"
                   aria-controls="tab1" aria-selected="true"><h6>Credit Reporting Agencies</h6></a>
            </li>
            <?php } if ($showLegalDocs > 0) { ?>
            <li class="nav-item">
                <a class="nav-link <?php if($activeLegalDocs) echo 'active'; ?>" id="legalDocsTabMenu" data-toggle="tab" onclick="ThirdPartyServiceInfoForm.enableTabData('legalDocs')" href="#legalDocsTab" role="tab" aria-controls="tab2"
                   aria-selected="false"><h6>Legal Docs</h6></a>
            </li>
            <?php } ?>
        </ul>
        <div class="tab-content pt-4" id="tabContent">
            <?php if ($showCRA) { ?>
                <div class="tab-pane fade <?php if($activeCRA) echo 'active show'; ?>" id="CRATab" role="tabpanel" aria-labelledby="CRATabMenu">
                    <?php require_once __DIR__ . '/thirdPartyServiceInfoForm/form.php'; ?>
                    <?php
                    if (count($thirdPartyServiceInfo) > 0) {
                        require_once __DIR__ . '/thirdPartyServiceInfoForm/thirdPartyServiceDocs.php';
                        require_once __DIR__ . '/thirdPartyServiceInfoForm/submissions.php';
                    } ?>
                </div>
            <?php } if ($showLegalDocs > 0) { ?>
                <div class="tab-pane fade show <?php if($activeLegalDocs) echo 'active show'; ?>" id="legalDocsTab" role="tabpanel" aria-labelledby="legalDocsTabMenu">
                    <?php require_once __DIR__ . '/thirdPartyServiceInfoForm/legalDocs/legalDocsForm.php'; ?>
                </div>
            <?php } ?>
        </div>
    </div>
</div>

