<?php

global $LMRId, $fileInfo, $externalBroker, $HMLOPCBasicLoanInfo, $fileTab, $isHMLO, $userGroup, $isEF,
       $lockedSections, $allowToEdit, $fileMC,
       $payReservesMonth, $paymentReservesAmt, $doesPropertyNeedRehabDispDiv,
       $contigencyCls, $requiredConstructionAmt,
       $contingencyReserveAmt, $downPaymentFieldDispOpt, $reqForLoanProUnderwriting, $activeTab, $LMRResponseId, $brokerNumber, $PCID;
global $fieldsInfo, $userRole;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPaymentReserves;
use models\constants\gl\glPCID;
use models\constants\gl\glprePaymentPenalty;
use models\constants\gl\glRequiredConstruction;
use models\constants\packageId;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\cypher;
use models\HMLOLoanTermsCalculation;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;

$glprePaymentPenalty = glprePaymentPenalty::getPrePaymentPenalty($PCID);
$glRequiredConstruction = glRequiredConstruction::$glRequiredConstruction;
$glPaymentReserves = glPaymentReserves::$glPaymentReserves;
$tabIndex = 1;
$fileHMLONewLoanInfo = [];
$servicesRequested = [];
$BranchInfo = $BrokerInfo = $filepaydownInfo = $fileModuleInfo = [];
$fileHMLOPropertyInfo = [];
$shortSaleInfo = [];
$LMRInfo = $fileHMLOInfo = $fileHMLOExperienceInfo = [];
$LOExplanationInfo = [];
$PCInfo = [];
$rehabCompanyName = '';
$rehabContractorName = '';
$rehabGCLicense = '';
$rehabContractorEmail = '';
$rehabContractorPhone = '';
$rehabStrategyPlans = '';
$typeOfHMLOLoanRequesting = '';
$purchaseCloseDate = $desiredCloseDate = '';
$acceptedPurchase = '';
$requiredLoanAmount = '';
$exitStrategy = '';
$rehabCost = '';
$propertyNeedRehab = $calcInrBasedOnMonthlyPayment = '';
$isThisGroundUpConstruction = '';
$lotPurchasePrice = 0;
$currentLotMarket = 0;
$lotStatus = '';
$paymentFrequency = '';
$rehabRepairDetails = '';
$neededToCompleteRehab = '';
$approvedLoanAmt = '';
$lien1Rate = '';
$lien1Terms = '';
$maxAmtToPutDown = '';
$propEquity = '';
$totalLoanToValue = '';
$borComment = '';
$borBorrowedDownPaymentExpln = '';
$prePaymentPenalty = '';
$loanTerm = '';
$lienPosition = '1';
$typeOfSale = '';
$docTypeLoanterms = '';
$fileHMLOBackGroundInfo = [];
$loanTermExpireDate = '';
$assumability = '';
$isTaxesInsEscrowed = '';
$isBorBorrowedDownPayment = '';
$isBorIntendToOccupyPropAsPRI = '';
$isCoBorIntendToOccupyPropAsPRI = '';
$isBorPersonallyGuaranteeLoan = '';
$acquisitionPriceFinanced = 0;
$acquisitionLTV = 0;
$totalProjectCost = $perRehabCostFinanced = 0;
$rehabCostFinanced = 0;
$originationPointsRate = 0;
$originationPointsValue = 0;
$processingFee = 0;
$drawsFee = 0;
$interestReserves = '';
$percentageOfBudget = 0;
$brokerPointsRate = 0;
$brokerPointsValue = 0;
$appraisalFee = 0;
$applicationFee = 0;
$drawsSetUpFee = 0;
$InrBasedOnMonthlyPayment = 0;
$estdTitleClosingFee = '';
$miscellaneousFee = 0;
$closingCostFinanced = 0;
$extensionOption = '';
$LTC = '';
$totalLoanAmount = 0;
$totalFeesAndCost = 0;
$HMLOLender = '';
$perClosingCostFinanced = 0;
$ARV = 0;
$closingCostNotFinanced = 0;
$payOffLiensCreditors = 0;
$totalCashToClose = 0;
$HMLOEstateHeldIn = '';
$taxes1 = 0;
$insurance1 = 0;
$originalPurchaseDate = '';
$originalPurchasePrice = 0;
$costOfImprovementsMade = 0;
$noOfPropertiesAcquiring = 1;
$amortizationType = '';
$actualRentsInPlace = 0;
$spcf_hoafees = 0;
$lessActualExpenses = 0;
$netOperatingIncome = 0;
$grossAnnualRentLargestTenant = 0;
$cashOut = '';
$cashOutAmt = 0;
$additionalPropertyRestrictions = '';
$restrictionsExplain = '';
$resalePrice = 0;
$resaleClosingDate = '';
$refinanceCurrentRate = '';
$refinanceMonthlyPayment = 0;
$payOffMortgage1 = 0;
$payOffMortgage2 = 0;
$payOffOutstandingTaxes = 0;
$payOffOtherOutstandingAmounts = 0;
$CashOut = '';
$refinanceCurrentLender = '';
$prePaymentPenaltyPercentage = $extensionOptionPercentage = '';
$prePaymentSelectVal = '';
$extensionRatePercentage = '';
$datesigned = '';
$minRate = 0;
$maxRate = 0;
$initialAdvance = 0;
$secondaryFinancingAmount = 0;
$totalRequiredReserves = 0;
$CORTotalLoanAmt = 0;
$totalCashOutAmt = 0;

$valuationBPOFee = 0;
$valuationCMAFee = 0;
$valuationAVEFee = 0;
$valuationAVMFee = 0;
$creditReportFee = 0;
$creditCheckFee = 0;
$employmentVerificationFee = 0;
$backgroundCheckFee = 0;
$taxReturnOrderFee = 0;
$floodCertificateFee = 0;
$loanOriginationFee = 0;
$documentPreparationFee = 0;
$wireFee = 0;
$servicingSetUpFee = 0;
$taxServiceFee = 0;
$floodServiceFee = 0;
$constructionHoldbackFee = 0;
$thirdPartyFees = 0;
$otherFee = 0;
$taxImpoundsMonth = 0;
$taxImpoundsMonthAmt = 0;
$taxImpoundsFee = 0;
$insImpoundsMonth = 0;
$insImpoundsMonthAmt = 0;
$insImpoundsFee = 0;
$interestChargedFromDate = '';
$interestChargedEndDate = '';
$netLenderFundsToBorrower = 0;
$diemDays = '';
$totalDailyInterestCharge = 0;
$totalEstPerDiem = 0;
$paymentReserves = '';
$requiredConstruction = '';
$contingencyReserve = '';
$costOfImprovementsToBeMade = 0;
$isBlanketLoan = '';
$LOCTotalLoanAmt = 0;
$rehabCostPercentageFinanced = 100;
$downPaymentPercentage = '';
$CORefiLTVPercentage = '';
$includeCCF = 0;
$isOwnLand = '';
$landValue = '';
$PAExpirationDate = '';
$inspectionFees = 0;
$projectFeasibility = 0;
$dueDiligence = 0;
$UccLienSearch = 0;
$assetCollateralized = '';
$isFeeUpdated = 0;
$haveBorSquareFootage = $borNoOfSquareFeet = '';
$lenderNotes = $checkDisplayTermSheet = '';
$prepaidInterestReserve = $noOfMonthsPrepaid = $interestOnInterestReserve = $closingCostFinancingFee = $attorneyFee = 0;
$haveInterestreserve = '';
$interestOnInterestReserveFee = 0;
$involvedPurchaseDispOpt = 'display : none';
$involvedPurchase = '';
$wholesaleFee = 0;
$seekingCashRefinanceDispOpt = 'display : none;';
$seekingCashRefinance = '';
$seekingCash = '';
$seekingFund = '';
$desiredLoanAmount = 0;
$desiredInterestRateRangeFrom = '';
$desiredInterestRateRangeTo = '';
$ownPropertyFreeAndClear = '';
$servicer1 = '';
$lien1MaturityDate = '';
$lean1CurrentDefault = '';
$servicer2 = '';
$lien2MaturityDate = '';
$lean2CurrentDefault = '';

$ownPropertyFreeAndClearDispOpt = 'display : none;';
$lien2Rate = '';
$lien2Payment = '';
$rentalIncomePerMonth = 0;
$escrowFees = 0;
$recordingFee = 0;
$prepayentSectionDisplay = 'display : none;';
$bufferAndMessengerFee = 0;
$travelNotaryFee = 0;
$prePaidInterest = 0;
$realEstateTaxes = 0;
$insurancePremium = 0;
$downPayment = 0;
$wireTransferFeeToTitle = 0;
$wireTransferFeeToEscrow = 0;
$pastDuePropertyTaxes = 0;
$underwritingFees = 0;
$propertyTax = 0;
$earnestDeposit = 0;
$otherDownPayment = 0;
$isTherePrePaymentPenalty = '';

$lenderAddress = $lenderCity = $lenderZip = $lenderStateOfFormation = $isLoanPaymentAmt = '';
$allowEditToIRAgent = '';
$allowEditToIR = '';
$ownedFreeAndClear = $ownedSameEntity = '';
$allowEditToIRBranch = '';
$refinanceCurrentLoanBalance = 0;
$approvedAcquisition = 0;
$requiredConstructionReservesFinanced = '';
$cityCountyTaxStamps = 0;
$prePayExcessOf20percent = '';
$loanMadeWholly = '';
$limitedOrNot = '';
$securityInstrument = '';
$addRentableSqFtForCashFlow = $noUnitsOccupiedForCashFlow = 0;

/* Estimated Project Cost */
$estimatedProjectCostArray = [];
$landAcquisition = '';
$newBuildingConstruction = '';
$constructionContingency = '';
$businessAcquisition = '';
$landAndBusinessAcquisition = '';
$buildingOrLeasehold = '';
$acquisitionOfMachineryEquipment = '';
$acquisitionOfFurnitureFixtures = '';
$inventoryPurchase = '';
$workingCapital = '';
$refinancingExistingBusinessDebt = '';
$otherCostText = '';
$otherCost = '';
$otherCost2 = '';
$otherCostText2 = '';
$estProjectCost = '';
$lessOwnerEquityToBeInjected = '';
$lessSellerCarryBack = '';
$loanRequestedForProject = '';
$franchiseFee = '';
$closingCost = '';
/* Estimated Project Cost */

/* Additional Questions */
$purposeOfLoan = '';
/* Additional Questions */
$daysUntilClose = '';
$exitFeePoints = 0;
$exitFeeAmount = 0;

$addRentableSqFtForCashFlow = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyRentableSqFt;
$noUnitsOccupiedForCashFlow = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyNumberOfUnits;

$myFileInfo = [];
if (array_key_exists($LMRId, $fileInfo)) {
    $myFileInfo = $fileInfo[$LMRId];
}
if (count($myFileInfo) > 0) {
    $HMLOPCBasicLoanInfo = $myFileInfo['HMLOPCBasicLoanInfo'] ?? [];

    if (array_key_exists('fileHMLOPropertyInfo', $myFileInfo)) $fileHMLOPropertyInfo = $myFileInfo['fileHMLOPropertyInfo'];
    if (array_key_exists('listingRealtorInfo', $myFileInfo)) $shortSaleInfo = $myFileInfo['listingRealtorInfo'];
    if (array_key_exists('LMRInfo', $myFileInfo)) $LMRInfo = $myFileInfo['LMRInfo'];
    if (array_key_exists('LOExplanationInfo', $myFileInfo)) $LOExplanationInfo = $myFileInfo['LOExplanationInfo'];
    if (array_key_exists('fileHMLOBackGroundInfo', $myFileInfo)) $fileHMLOBackGroundInfo = $myFileInfo['fileHMLOBackGroundInfo'];
    if (array_key_exists('fileHMLONewLoanInfo', $myFileInfo)) $fileHMLONewLoanInfo = $myFileInfo['fileHMLONewLoanInfo'];
    if (array_key_exists('branchClientTypeInfo', $myFileInfo)) $servicesRequested = $myFileInfo['branchClientTypeInfo'];
    if (array_key_exists('BranchInfo', $myFileInfo)) $BranchInfo = $myFileInfo['BranchInfo'];
    if (array_key_exists('BrokerInfo', $myFileInfo)) $BrokerInfo = $myFileInfo['BrokerInfo'];
    if (array_key_exists('SecondaryBrokerInfo', $myFileInfo)) $SecondaryBrokerInfo = $myFileInfo['SecondaryBrokerInfo'];
    if (array_key_exists('PCInfo', $myFileInfo)) $PCInfo = $myFileInfo['PCInfo'];
    if (array_key_exists('budgetAndDrawsInfo', $myFileInfo)) $budgetAndDrawsInfo = $myFileInfo['budgetAndDrawsInfo'];
    if (array_key_exists('fileHMLOInfo', $myFileInfo)) $fileHMLOInfo = $myFileInfo['fileHMLOInfo'];
    if (array_key_exists('fileHMLOExperienceInfo', $myFileInfo)) $fileHMLOExperienceInfo = $myFileInfo['fileHMLOExperienceInfo'];
    if (array_key_exists('paydownInfo', $myFileInfo)) $filepaydownInfo = $myFileInfo['paydownInfo'];
    if (array_key_exists('estimatedProjectCost', $myFileInfo)) $estimatedProjectCostArray = $myFileInfo['estimatedProjectCost'];
    if (isset($myFileInfo['fileModuleInfo'][$LMRId])) $fileModuleInfo = $myFileInfo['fileModuleInfo'][$LMRId];

    /** Fetch all Branch services Types **/

}

$fileloanProgram = $LMRClientTypeInfo[0]['ClientType'] ?? '';


if (count($LMRInfo) > 0) {
    $lien1Rate = Strings::showField('lien1Rate', 'LMRInfo');
    $lien1Terms = Strings::showField('lien1Terms', 'LMRInfo');
}
$borComment = Strings::showField('borComment', 'LOExplanationInfo');

if (count($BrokerInfo) > 0 && $externalBroker == 0) {
    $allowEditToIRAgent = Strings::showField('allowEditToIR', 'BrokerInfo');
} else if (count($SecondaryBrokerInfo) > 0 && $externalBroker > 0) {  /*these permissions set in page are kinda 
	wonky, they rely on the fact that only 1 branch can see file so allowEditToIRBranch is fine for example 
	to set from the branch info in the file, allowEditToIRAgent on the other hand now has 2 agent types who 
	can see the file so i have to see if externalBroker is 1 which comes from getPageVariables.php from the 
	logged in agents profile.  Knowing this i can then get the value from SecondaryBrokerInfo array instead */
    $allowEditToIRAgent = Strings::showField('allowEditToIR', 'SecondaryBrokerInfo');
}

if (count($BranchInfo) > 0) {
    $allowEditToIRBranch = Strings::showField('allowEditToIR', 'BranchInfo');
}
$prePaymentPenaltyResArr = glprePaymentPenalty::getPCLevelPrePaymentPenalty($PCID, 'FC');

if (count($fileHMLONewLoanInfo) > 0) {

    $isTaxesInsEscrowed = Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo');
    $assumability = Strings::showField('assumability', 'fileHMLONewLoanInfo');
    $InrBasedOnMonthlyPayment = Strings::showField('InrBasedOnMonthlyPayment', 'fileHMLONewLoanInfo');
    $calcInrBasedOnMonthlyPayment = Strings::showField('calcInrBasedOnMonthlyPayment', 'fileHMLONewLoanInfo');
    $paymentFrequency = Strings::showField('paymentFrequency', 'fileHMLONewLoanInfo');
    $rehabCostFinanced = Strings::showField('rehabCostFinanced', 'fileHMLONewLoanInfo');
    $originationPointsRate = Strings::showField('originationPointsRate', 'fileHMLONewLoanInfo');
    $originationPointsValue = Strings::showField('originationPointsValue', 'fileHMLONewLoanInfo');
    $processingFee = Strings::showField('processingFee', 'fileHMLONewLoanInfo');
    $brokerPointsRate = Strings::showField('brokerPointsRate', 'fileHMLONewLoanInfo');
    $brokerPointsValue = Strings::showField('brokerPointsValue', 'fileHMLONewLoanInfo');
    $appraisalFee = Strings::showField('appraisalFee', 'fileHMLONewLoanInfo');
    $applicationFee = Strings::showField('applicationFee', 'fileHMLONewLoanInfo');
    $drawsSetUpFee = Strings::showField('drawsSetUpFee', 'fileHMLONewLoanInfo');
    $estdTitleClosingFee = Strings::showField('estdTitleClosingFee', 'fileHMLONewLoanInfo');
    $miscellaneousFee = Strings::showField('miscellaneousFee', 'fileHMLONewLoanInfo');
    $closingCostFinanced = Strings::showField('closingCostFinanced', 'fileHMLONewLoanInfo');
    $extensionOption = Strings::showField('extensionOption', 'fileHMLONewLoanInfo');
    $loanTermExpireDate = Strings::showField('loanTermExpireDate', 'fileHMLONewLoanInfo');
    $HMLOLender = Strings::showField('HMLOLender', 'fileHMLONewLoanInfo');
    $drawsFee = Strings::showField('drawsFee', 'fileHMLONewLoanInfo');
    $interestReserves = Strings::showField('interestReserves', 'fileHMLONewLoanInfo');
    $percentageOfBudget = Strings::showField('percentageOfBudget', 'fileHMLONewLoanInfo');
    $originalPurchaseDate = Strings::showField('originalPurchaseDate', 'fileHMLONewLoanInfo');
    $originalPurchasePrice = Strings::showField('originalPurchasePrice', 'fileHMLONewLoanInfo');
    $costOfImprovementsMade = Strings::showField('costOfImprovementsMade', 'fileHMLONewLoanInfo');
    $payOffMortgage1 = Strings::showField('payOffMortgage1', 'fileHMLONewLoanInfo');
    $payOffMortgage2 = Strings::showField('payOffMortgage2', 'fileHMLONewLoanInfo');
    $payOffOutstandingTaxes = Strings::showField('payOffOutstandingTaxes', 'fileHMLONewLoanInfo');
    $payOffOtherOutstandingAmounts = Strings::showField('payOffOtherOutstandingAmounts', 'fileHMLONewLoanInfo');
    $refinanceCurrentLender = Strings::showField('refinanceCurrentLender', 'fileHMLONewLoanInfo');
    $refinanceCurrentRate = Strings::showField('refinanceCurrentRate', 'fileHMLONewLoanInfo');
    $refinanceMonthlyPayment = Strings::showField('refinanceMonthlyPayment', 'fileHMLONewLoanInfo');
    $CashOut = Strings::showField('CashOut', 'fileHMLONewLoanInfo');
    $additionalPropertyRestrictions = Strings::showField('additionalPropertyRestrictions', 'fileHMLONewLoanInfo');
    $restrictionsExplain = Strings::showField('restrictionsExplain', 'fileHMLONewLoanInfo');
    $actualRentsInPlace = Strings::showField('actualRentsInPlace', 'fileHMLONewLoanInfo');
    $spcf_hoafees = Strings::showField('spcf_hoafees', 'fileHMLONewLoanInfo');
    $lessActualExpenses = Strings::showField('lessActualExpenses', 'fileHMLONewLoanInfo');
    $prePaymentPenaltyPercentage = Strings::showField('prePaymentPenaltyPercentage', 'fileHMLONewLoanInfo');
    $prePaymentSelectVal = Strings::showField('prePaymentSelectVal', 'fileHMLONewLoanInfo');
    $prePaymentSelectValArr = explode(',', $prePaymentSelectVal);
    $extensionOptionPercentage = Strings::showField('extensionOptionPercentage', 'fileHMLONewLoanInfo');
    $extensionRatePercentage = Strings::showField('extensionRatePercentage', 'fileHMLONewLoanInfo');
    $amortizationType = Strings::showField('amortizationType', 'fileHMLONewLoanInfo');
    $grossAnnualRentLargestTenant = Strings::showField('grossAnnualRentLargestTenant', 'fileHMLONewLoanInfo');
    $noOfPropertiesAcquiring = Strings::showField('noOfPropertiesAcquiring', 'fileHMLONewLoanInfo');
    $haveBorSquareFootage = Strings::showField('haveBorSquareFootage', 'fileHMLONewLoanInfo');
    $borNoOfSquareFeet = Strings::showField('borNoOfSquareFeet', 'fileHMLONewLoanInfo');
    $cashOutAmt = Strings::showField('cashOutAmt', 'fileHMLONewLoanInfo');
    $datesigned = Strings::showField('datesigned', 'fileHMLONewLoanInfo');
    $resalePrice = Strings::showField('resalePrice', 'fileHMLONewLoanInfo');
    $resaleClosingDate = Strings::showField('resaleClosingDate', 'fileHMLONewLoanInfo');
    $initialAdvance = Strings::showField('initialAdvance', 'fileHMLONewLoanInfo');
    $secondaryFinancingAmount = Strings::showField('secondaryFinancingAmount', 'fileHMLONewLoanInfo');

    LMRequest::$initialAdvance = Strings::Numeric($initialAdvance);

    $valuationBPOFee = Strings::showField('valuationBPOFee', 'fileHMLONewLoanInfo');
    $valuationCMAFee = Strings::showField('valuationCMAFee', 'fileHMLONewLoanInfo');
    $valuationAVEFee = Strings::showField('valuationAVEFee', 'fileHMLONewLoanInfo');
    $valuationAVMFee = Strings::showField('valuationAVMFee', 'fileHMLONewLoanInfo');
    $creditReportFee = Strings::showField('creditReportFee', 'fileHMLONewLoanInfo');
    $creditCheckFee = Strings::showField('creditCheckFee', 'fileHMLONewLoanInfo');
    $employmentVerificationFee = Strings::showField('employmentVerificationFee', 'fileHMLONewLoanInfo');
    $backgroundCheckFee = Strings::showField('backgroundCheckFee', 'fileHMLONewLoanInfo');
    $taxReturnOrderFee = Strings::showField('taxReturnOrderFee', 'fileHMLONewLoanInfo');
    $floodCertificateFee = Strings::showField('floodCertificateFee', 'fileHMLONewLoanInfo');
    $loanOriginationFee = Strings::showField('loanOriginationFee', 'fileHMLONewLoanInfo');
    $documentPreparationFee = Strings::showField('documentPreparationFee', 'fileHMLONewLoanInfo');
    $wireFee = Strings::showField('wireFee', 'fileHMLONewLoanInfo');
    $servicingSetUpFee = Strings::showField('servicingSetUpFee', 'fileHMLONewLoanInfo');
    $taxServiceFee = Strings::showField('taxServiceFee', 'fileHMLONewLoanInfo');
    $floodServiceFee = Strings::showField('floodServiceFee', 'fileHMLONewLoanInfo');
    $constructionHoldbackFee = Strings::showField('constructionHoldbackFee', 'fileHMLONewLoanInfo');
    $thirdPartyFees = Strings::showField('thirdPartyFees', 'fileHMLONewLoanInfo');
    $otherFee = Strings::showField('otherFee', 'fileHMLONewLoanInfo');
    $taxImpoundsMonth = Strings::showField('taxImpoundsMonth', 'fileHMLONewLoanInfo');
    $taxImpoundsMonthAmt = Strings::showField('taxImpoundsMonthAmt', 'fileHMLONewLoanInfo');
    $taxImpoundsFee = Strings::showField('taxImpoundsFee', 'fileHMLONewLoanInfo');
    $insImpoundsMonth = Strings::showField('insImpoundsMonth', 'fileHMLONewLoanInfo');
    $insImpoundsMonthAmt = Strings::showField('insImpoundsMonthAmt', 'fileHMLONewLoanInfo');
    $insImpoundsFee = Strings::showField('insImpoundsFee', 'fileHMLONewLoanInfo');
    $interestChargedFromDate = Strings::showField('interestChargedFromDate', 'fileHMLONewLoanInfo');
    $interestChargedEndDate = Strings::showField('interestChargedEndDate', 'fileHMLONewLoanInfo');
    $costOfImprovementsToBeMade = Strings::showField('costOfImprovementsToBeMade', 'fileHMLONewLoanInfo');
    $LOCTotalLoanAmt = Strings::showField('LOCTotalLoanAmt', 'fileHMLONewLoanInfo');
    $rehabCostPercentageFinanced = Strings::showField('rehabCostPercentageFinanced', 'fileHMLONewLoanInfo');
    $downPaymentPercentage = Strings::showField('downPaymentPercentage', 'fileHMLONewLoanInfo');
    $CORTotalLoanAmt = Strings::showField('CORTotalLoanAmt', 'fileHMLONewLoanInfo');
    $CORefiLTVPercentage = Strings::showField('CORefiLTVPercentage', 'fileHMLONewLoanInfo');
    $includeCCF = Strings::showField('includeCCF', 'fileHMLONewLoanInfo');
    $isOwnLand = Strings::showField('isOwnLand', 'fileHMLONewLoanInfo');
    $landValue = Strings::showField('landValue', 'fileHMLONewLoanInfo');
    $PAExpirationDate = Strings::showField('PAExpirationDate', 'fileHMLONewLoanInfo');

    $inspectionFees = Strings::showField('inspectionFees', 'fileHMLONewLoanInfo');
    $projectFeasibility = Strings::showField('projectFeasibility', 'fileHMLONewLoanInfo');
    $dueDiligence = Strings::showField('dueDiligence', 'fileHMLONewLoanInfo');
    $UccLienSearch = Strings::showField('UccLienSearch', 'fileHMLONewLoanInfo');
    $isFeeUpdated = Strings::showField('isFeeUpdated', 'fileHMLONewLoanInfo');
    $lenderNotes = Strings::showField('lenderNotes', 'fileHMLOPropertyInfo');
    $checkDisplayTermSheet = Strings::showField('checkDisplayTermSheet', 'fileHMLOPropertyInfo');
    $prepaidInterestReserve = Strings::showField('prepaidInterestReserve', 'fileHMLONewLoanInfo');
    $noOfMonthsPrepaid = Strings::showField('noOfMonthsPrepaid', 'fileHMLONewLoanInfo');
    $haveInterestreserve = Strings::showField('haveInterestreserve', 'fileHMLONewLoanInfo');
    if ($haveInterestreserve == '') {
        $haveInterestreserve = 'No';
    }
    $closingCostFinancingFee = Strings::showField('closingCostFinancingFee', 'fileHMLONewLoanInfo');
    $attorneyFee = Strings::showField('attorneyFee', 'fileHMLONewLoanInfo');
    $involvedPurchase = Strings::showField('involvedPurchase', 'fileHMLOPropertyInfo');
    $wholesaleFee = Strings::showField('wholesaleFee', 'fileHMLOPropertyInfo');
    $prePayExcessOf20percent = Strings::showField('prePayExcessOf20percent', 'fileHMLOPropertyInfo');
    $loanMadeWholly = Strings::showField('loanMadeWholly', 'fileHMLOPropertyInfo');
    $limitedOrNot = Strings::showField('limitedOrNot', 'fileHMLOPropertyInfo');
    $balloonPayment = Strings::showField('balloonPayment', 'fileHMLOPropertyInfo');
    $seekingCashRefinance = Strings::showField('seekingCashRefinance', 'fileHMLOPropertyInfo');
    $seekingCash = Strings::showField('seekingCash', 'fileHMLOPropertyInfo');
    $seekingFund = Strings::showField('seekingFund', 'fileHMLOPropertyInfo');
    $requiredConstructionReservesFinanced = Strings::showField('requiredConstructionReservesFinanced', 'fileHMLOPropertyInfo');

    $ownPropertyFreeAndClear = Strings::showField('ownPropertyFreeAndClear', 'fileHMLONewLoanInfo');
    $lien1MaturityDate = Dates::formatDateWithRE(Strings::showField('lien1MaturityDate', 'fileHMLONewLoanInfo'), 'YMD', 'm/d/Y');
    $lien2MaturityDate = Dates::formatDateWithRE(Strings::showField('lien2MaturityDate', 'fileHMLONewLoanInfo'), 'YMD', 'm/d/Y');
    $lean1CurrentDefault = Strings::showField('lean1CurrentDefault', 'fileHMLONewLoanInfo');
    $lean2CurrentDefault = Strings::showField('lean2CurrentDefault', 'fileHMLONewLoanInfo');
    $desiredLoanAmount = Strings::showField('desiredLoanAmount', 'fileHMLONewLoanInfo');
    $earnestDeposit = Strings::showField('earnestDeposit', 'fileHMLONewLoanInfo');
    $otherDownPayment = Strings::showField('otherDownPayment', 'fileHMLONewLoanInfo');
    $escrowFees = Strings::showField('escrowFees', 'fileHMLONewLoanInfo');
    $recordingFee = Strings::showField('recordingFee', 'fileHMLONewLoanInfo');
    $underwritingFees = Strings::showField('underwritingFees', 'fileHMLONewLoanInfo');
    $prePaidInterest = Strings::showField('prePaidInterest', 'fileHMLONewLoanInfo');
    $propertyTax = Strings::showField('propertyTax', 'fileHMLONewLoanInfo');
    $realEstateTaxes = Strings::showField('realEstateTaxes', 'fileHMLONewLoanInfo');
    $insurancePremium = Strings::showField('insurancePremium', 'fileHMLONewLoanInfo');
    $payOffLiensCreditors = Strings::showField('payOffLiensCreditors', 'fileHMLONewLoanInfo');
    $firstInstallmentOfTax = Strings::showField('firstInstallmentOfTax', 'fileHMLONewLoanInfo');
    $taxSale = Strings::showField('taxSale', 'fileHMLONewLoanInfo');
    $bufferAndMessengerFee = Strings::showField('bufferAndMessengerFee', 'fileHMLONewLoanInfo');
    $travelNotaryFee = Strings::showField('travelNotaryFee', 'fileHMLONewLoanInfo');
    $wireTransferFeeToTitle = Strings::showField('wireTransferFeeToTitle', 'fileHMLONewLoanInfo');
    $wireTransferFeeToEscrow = Strings::showField('wireTransferFeeToEscrow', 'fileHMLONewLoanInfo');
    $pastDuePropertyTaxes = Strings::showField('pastDuePropertyTaxes', 'fileHMLONewLoanInfo');
    $isLoanPaymentAmt = Strings::showField('isLoanPaymentAmt', 'fileHMLONewLoanInfo');
    $ownedFreeAndClear = Strings::showField('ownedFreeAndClear', 'fileHMLONewLoanInfo');
    $ownedSameEntity = Strings::showField('ownedSameEntity', 'fileHMLONewLoanInfo');
    $refinanceCurrentLoanBalance = Strings::showField('refinanceCurrentLoanBalance', 'fileHMLONewLoanInfo');
    $approvedAcquisition = Strings::showField('approvedAcquisition', 'fileHMLONewLoanInfo');
    $survey = Strings::showField('survey', 'fileHMLONewLoanInfo');
    $wholeSaleAdminFee = Strings::showField('wholeSaleAdminFee', 'fileHMLONewLoanInfo');
    $cityCountyTaxStamps = Strings::showField('cityCountyTaxStamps', 'fileHMLONewLoanInfo');

    $desiredInterestRateRangeFrom = Strings::showField('desiredInterestRateRangeFrom', 'fileHMLONewLoanInfo');
    $desiredInterestRateRangeTo = Strings::showField('desiredInterestRateRangeTo', 'fileHMLONewLoanInfo');

    //$daysUntilClose = Strings::showField('daysUntilClose', 'fileHMLONewLoanInfo');

    $exitFeePoints = Strings::showField('exitFeePoints', 'fileHMLONewLoanInfo');
    $exitFeeAmount = Strings::showField('exitFeeAmount', 'fileHMLONewLoanInfo');


    if ($extensionOption == 0) $extensionOption = '';
    if ($involvedPurchase == 'Yes') $involvedPurchaseDispOpt = 'display: table-cell;';
    if ($seekingCashRefinance == 'Yes') $seekingCashRefinanceDispOpt = 'display: table-cell;';
    if ($ownPropertyFreeAndClear == 'No') $ownPropertyFreeAndClearDispOpt = 'display: block;';

    $customLoanFeeDataStatus = 0;
    if (count($HMLOPCBasicLoanInfo) > 0 && $isFeeUpdated != 1) {
        foreach ($HMLOPCBasicLoanInfo as $i => $item) {
            $minRate = Strings::checkAndUpdateValue($minRate, $item['minRate']);
            $maxRate = Strings::checkAndUpdateValue($maxRate, $item['maxRate']);
            $originationPointsRate = Strings::checkAndUpdateValue($originationPointsRate, $item['originationPointsRate']);
            $originationPointsValue = Strings::checkAndUpdateValue($originationPointsValue, $item['originationPointsValue']);
            $processingFee = Strings::checkAndUpdateValue($processingFee, $item['processingFee']);
            $brokerPointsRate = Strings::checkAndUpdateValue($brokerPointsRate, $item['brokerPointsRate']);
            $brokerPointsValue = Strings::checkAndUpdateValue($brokerPointsValue, $item['brokerPointsValue']);
            $appraisalFee = Strings::checkAndUpdateValue($appraisalFee, $item['appraisalFee']);
            $applicationFee = Strings::checkAndUpdateValue($applicationFee, $item['applicationFee']);
            $drawsSetUpFee = Strings::checkAndUpdateValue($drawsSetUpFee, $item['drawsSetUpFee']);
            $estdTitleClosingFee = Strings::checkAndUpdateValue($estdTitleClosingFee, $item['estdTitleClosingFee']);
            $miscellaneousFee = Strings::checkAndUpdateValue($miscellaneousFee, $item['miscellaneousFee']);
            $closingCostFinanced = Strings::checkAndUpdateValue($closingCostFinanced, $item['closingCostFinanced']);
            $PCBorrCreditScoreRange = Strings::checkAndUpdateValue($PCBorrCreditScoreRange, $item['PCBorrCreditScoreRange']);
            $drawsFee = Strings::checkAndUpdateValue($drawsFee, $item['drawsFee']);

            $valuationBPOFee = Strings::checkAndUpdateValue($valuationBPOFee, $item['valuationBPOFee']);
            $valuationAVMFee = Strings::checkAndUpdateValue($valuationAVMFee, $item['valuationAVMFee']);
            $creditReportFee = Strings::checkAndUpdateValue($creditReportFee, $item['creditReportFee']);
            $backgroundCheckFee = Strings::checkAndUpdateValue($backgroundCheckFee, $item['backgroundCheckFee']);
            $floodCertificateFee = Strings::checkAndUpdateValue($floodCertificateFee, $item['floodCertificateFee']);
            $documentPreparationFee = Strings::checkAndUpdateValue($documentPreparationFee, $item['documentPreparationFee']);
            $wireFee = Strings::checkAndUpdateValue($wireFee, $item['wireFee']);
            $servicingSetUpFee = Strings::checkAndUpdateValue($servicingSetUpFee, $item['servicingSetUpFee']);
            $taxServiceFee = Strings::checkAndUpdateValue($taxServiceFee, $item['taxServiceFee']);
            $floodServiceFee = Strings::checkAndUpdateValue($floodServiceFee, $item['floodServiceFee']);
            $inspectionFees = Strings::checkAndUpdateValue($inspectionFees, $item['inspectionFees']);
            $projectFeasibility = Strings::checkAndUpdateValue($projectFeasibility, $item['projectFeasibility']);
            $dueDiligence = Strings::checkAndUpdateValue($dueDiligence, $item['dueDiligence']);
            $UccLienSearch = Strings::checkAndUpdateValue($UccLienSearch, $item['UccLienSearch']);
            $otherFee = Strings::checkAndUpdateValue($otherFee, $item['otherFee']);
            $taxImpoundsMonth = Strings::checkAndUpdateValue($taxImpoundsMonth, $item['taxImpoundsMonth']);
            $taxImpoundsMonthAmt = Strings::checkAndUpdateValue($taxImpoundsMonthAmt, $item['taxImpoundsMonthAmt']);
            $taxImpoundsFee = Strings::checkAndUpdateValue($taxImpoundsFee, $item['taxImpoundsFee']);
            $insImpoundsMonthAmt = Strings::checkAndUpdateValue($insImpoundsMonthAmt, $item['insImpoundsMonthAmt']);
            $insImpoundsFee = Strings::checkAndUpdateValue($insImpoundsFee, $item['insImpoundsFee']);
            $thirdPartyFees = Strings::checkAndUpdateValue($thirdPartyFees, $item['thirdPartyFees']);
            $insImpoundsMonth = Strings::checkAndUpdateValue($insImpoundsMonth, $item['insImpoundsMonth']);
            $closingCostFinancingFee = Strings::checkAndUpdateValue($closingCostFinancingFee, $item['closingCostFinancingFee']);
            $attorneyFee = Strings::checkAndUpdateValue($attorneyFee, $item['attorneyFee']);
            $escrowFees = Strings::checkAndUpdateValue($escrowFees, $item['escrowFees']);
            $recordingFee = Strings::checkAndUpdateValue($recordingFee, $item['recordingFee']);
            $underwritingFees = Strings::checkAndUpdateValue($underwritingFees, $item['underwritingFees']);
            $propertyTax = Strings::checkAndUpdateValue($propertyTax, $item['propertyTax']);
            $prePaidInterest = Strings::checkAndUpdateValue($prePaidInterest, $item['prePaidInterest']);
            $realEstateTaxes = Strings::checkAndUpdateValue($realEstateTaxes, $item['realEstateTaxes']);
            $insurancePremium = Strings::checkAndUpdateValue($insurancePremium, $item['insurancePremium']);
            $payOffLiensCreditors = Strings::checkAndUpdateValue($payOffLiensCreditors, $item['payOffLiensCreditors']);
            $bufferAndMessengerFee = Strings::checkAndUpdateValue($bufferAndMessengerFee, $item['bufferAndMessengerFee']);
            $travelNotaryFee = Strings::checkAndUpdateValue($travelNotaryFee, $item['travelNotaryFee']);
            $wireTransferFeeToTitle = Strings::checkAndUpdateValue($wireTransferFeeToTitle, $item['wireTransferFeeToTitle']);
            $wireTransferFeeToEscrow = Strings::checkAndUpdateValue($wireTransferFeeToEscrow, $item['wireTransferFeeToEscrow']);
            $pastDuePropertyTaxes = Strings::checkAndUpdateValue($pastDuePropertyTaxes, $item['pastDuePropertyTaxes']);
            $survey = Strings::checkAndUpdateValue($survey, $item['survey']);
            $wholeSaleAdminFee = Strings::checkAndUpdateValue($wholeSaleAdminFee, $item['wholeSaleAdminFee']);
            $customLoanFeeDataStatus = 1;
        }
    }

    if (Dates::IsEmpty($originalPurchaseDate)) {
        $originalPurchaseDate = '';
    } else {
        $originalPurchaseDate = Dates::formatDateWithRE($originalPurchaseDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($datesigned)) {
        $datesigned = '';
    } else {
        $datesigned = Dates::formatDateWithRE($datesigned, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($resaleClosingDate)) {
        $resaleClosingDate = '';
    } else {
        $resaleClosingDate = Dates::formatDateWithRE($resaleClosingDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($interestChargedFromDate)) {
        $interestChargedFromDate = '';
    } else {
        $interestChargedFromDate = Dates::formatDateWithRE($interestChargedFromDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($interestChargedEndDate)) {
        $interestChargedEndDate = '';
    } else {
        $interestChargedEndDate = Dates::formatDateWithRE($interestChargedEndDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($PAExpirationDate)) {
        $PAExpirationDate = '';
    } else {
        $PAExpirationDate = Dates::formatDateWithRE($PAExpirationDate, 'YMD', 'm/d/Y');
    }
}
$purchaseCloseDate = Strings::showField('closingDate', 'QAInfo'); /* Merge closing Date in Admin tab with Purchase / Target Closing Date in Loan info tab on Jul 26, 2017 - Ticket Id: 148701649*/
$desiredCloseDate = Strings::showField('desiredClosingDate', 'QAInfo');
if (count($fileHMLOPropertyInfo) > 0) {
    $typeOfHMLOLoanRequesting = trim(Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo'));
    $acceptedPurchase = Strings::showField('acceptedPurchase', 'fileHMLOPropertyInfo');
    $desiredFundingAmount = Strings::showField('desiredFundingAmount', 'fileHMLOPropertyInfo');
    $purposeOfLoan = Strings::showField('purposeOfLoan', 'fileHMLOPropertyInfo');
    $useOfFunds = Strings::showField('useOfFunds', 'fileHMLOPropertyInfo');
    $haveCurrentLoanBal = Strings::showField('haveCurrentLoanBal', 'fileHMLOPropertyInfo');
    $doYouHaveInvoiceToFactor = Strings::showField('haveInvoiceToFactor', 'fileHMLOPropertyInfo');
    $heldWith = Strings::showField('heldWith', 'fileHMLOPropertyInfo');
    $balance = Strings::showField('balance', 'fileHMLOPropertyInfo');
    $amount = Strings::showField('amount', 'fileHMLOPropertyInfo');
    $requiredLoanAmount = Strings::showField('requiredLoanAmount', 'fileHMLOPropertyInfo');
    $exitStrategy = Strings::showField('exitStrategy', 'fileHMLOPropertyInfo');
    $rehabCost = Strings::showField('rehabCost', 'fileHMLOPropertyInfo');
    $propertyNeedRehab = Strings::showField('propertyNeedRehab', 'fileHMLOPropertyInfo');
    $isThisGroundUpConstruction = Strings::showField('isThisGroundUpConstruction', 'fileHMLOPropertyInfo');
    $lotStatus = Strings::showField('lotStatus', 'fileHMLOPropertyInfo');
    $lotPurchasePrice = Strings::showField('lotPurchasePrice', 'fileHMLOPropertyInfo');
    $currentLotMarket = Strings::showField('currentLotMarket', 'fileHMLOPropertyInfo');
    $rehabRepairDetails = Strings::showField('rehabRepairDetails', 'fileHMLOPropertyInfo');
    $neededToCompleteRehab = Strings::showField('neededToCompleteRehab', 'fileHMLOPropertyInfo');
    $rehabCompanyName = Strings::showField('rehabCompanyName', 'fileHMLOPropertyInfo');
    $rehabContractorName = Strings::showField('rehabContractorName', 'fileHMLOPropertyInfo');
    $rehabGCLicense = Strings::showField('rehabGCLicense', 'fileHMLOPropertyInfo');
    $rehabContractorEmail = Strings::showField('rehabContractorEmail', 'fileHMLOPropertyInfo');
    $rehabContractorPhone = Strings::showField('rehabContractorPhone', 'fileHMLOPropertyInfo');
    $rehabStrategyPlans = Strings::showField('rehabStrategyPlans', 'fileHMLOPropertyInfo');
    $annualPremium = Strings::showField('annualPremium', 'fileHMLOPropertyInfo');
    $isHiredPerformRehab = Strings::showField('isHiredPerformRehab', 'fileHMLOPropertyInfo');
    $loanTerm = Strings::showField('loanTerm', 'fileHMLOPropertyInfo');
    $prePaymentPenalty = Strings::showField('prePaymentPenalty', 'fileHMLOPropertyInfo');
    $lienPosition = Strings::showField('lienPosition', 'fileHMLOPropertyInfo');
    $propEquity = Strings::showField('propEquity', 'fileHMLOPropertyInfo');
    $maxAmtToPutDown = Strings::showField('maxAmtToPutDown', 'fileHMLOPropertyInfo');
    $approvedLoanAmt = Strings::showField('approvedLoanAmt', 'fileHMLOPropertyInfo');
    $HMLOEstateHeldIn = Strings::showField('HMLOEstateHeldIn', 'fileHMLOPropertyInfo');
    $paymentReserves = Strings::showField('paymentReserves', 'fileHMLOPropertyInfo');
    $requiredConstruction = Strings::showField('requiredConstruction', 'fileHMLOPropertyInfo');
    $contingencyReserve = Strings::showField('contingencyReserve', 'fileHMLOPropertyInfo');
    $percentageTotalLoan = Strings::showField('percentageTotalLoan', 'fileHMLOPropertyInfo');
    $isBlanketLoan = Strings::showField('isBlanketLoan', 'fileHMLOPropertyInfo');
    $typeOfSale = Strings::showField('typeOfSale', 'fileHMLOPropertyInfo');
    $docTypeLoanterms = Strings::showField('docType', 'fileHMLOPropertyInfo');
    $assetCollateralized = Strings::showField('assetCollateralized', 'fileHMLOPropertyInfo');
    $rentalIncomePerMonth = Strings::showField('rentalIncomePerMonth', 'fileHMLOPropertyInfo');
    $isTherePrePaymentPenalty = Strings::showField('isTherePrePaymentPenalty', 'fileHMLOPropertyInfo');
    $loanSigning = Strings::showField('loanSigning', 'fileHMLOPropertyInfo');
    $courtOrderNecessary = Strings::showField('courtOrderNecessary', 'fileHMLOPropertyInfo');
    $loanPurpose = Strings::showField('loanPurpose', 'fileHMLOPropertyInfo');
    $securityInstrument = Strings::showField('securityInstrument', 'fileHMLOPropertyInfo');

    $rehabToBeMade = Strings::showField('rehabToBeMade', 'fileHMLOPropertyInfo');
    $rehabTime = Strings::showField('rehabTime', 'fileHMLOPropertyInfo');
    $isSubjectUnderConst = Strings::showField('isSubjectUnderConst', 'fileHMLOPropertyInfo');
    $areKnownHazards = Strings::showField('areKnownHazards', 'fileHMLOPropertyInfo');
    $areProReports = Strings::showField('areProReports', 'fileHMLOPropertyInfo');
    $isSubjectSS = Strings::showField('isSubjectSS', 'fileHMLOPropertyInfo');
    $changeInCircumstance = Strings::showField('changeInCircumstance', 'fileHMLOPropertyInfo');
    $changeDescription = Strings::showField('changeDescription', 'fileHMLOPropertyInfo');
    $useOfProceeds = Strings::showField('useOfProceeds', 'fileHMLOPropertyInfo');
}
if ($fileHMLOBackGroundInfo > 0) {
    $isBorBorrowedDownPayment = Strings::showField('isBorBorrowedDownPayment', 'fileHMLOBackGroundInfo');
    $isBorIntendToOccupyPropAsPRI = Strings::showField('isBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
    $isCoBorIntendToOccupyPropAsPRI = Strings::showField('isCoBorIntendToOccupyPropAsPRI', 'fileHMLOBackGroundInfo');
    $isBorPersonallyGuaranteeLoan = Strings::showField('isBorPersonallyGuaranteeLoan', 'fileHMLOBackGroundInfo');
    $borBorrowedDownPaymentExpln = Strings::showField('borBorrowedDownPaymentExpln', 'fileHMLOBackGroundInfo');
}

/* Estimated Project Cost */
$epcArray = BaseHTML::sectionAccess(['sId' => 'EPC', 'opt' => $fileTab]);
if (count($estimatedProjectCostArray) > 0) {
    if (BaseHTML::fieldAccess(['fNm' => 'landAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $landAcquisition = trim($estimatedProjectCostArray['landAcquisition']);
    if (BaseHTML::fieldAccess(['fNm' => 'newBuildingConstruction', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $newBuildingConstruction = trim($estimatedProjectCostArray['newBuildingConstruction']);
    if (BaseHTML::fieldAccess(['fNm' => 'constructionContingency', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $constructionContingency = trim($estimatedProjectCostArray['constructionContingency']);
    if (BaseHTML::fieldAccess(['fNm' => 'businessAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $businessAcquisition = trim($estimatedProjectCostArray['businessAcquisition']);
    if (BaseHTML::fieldAccess(['fNm' => 'landAndBusinessAcquisition', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $landAndBusinessAcquisition = trim($estimatedProjectCostArray['landAndBusinessAcquisition']);
    if (BaseHTML::fieldAccess(['fNm' => 'buildingOrLeasehold', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $buildingOrLeasehold = trim($estimatedProjectCostArray['buildingOrLeasehold']);
    if (BaseHTML::fieldAccess(['fNm' => 'acquisitionOfMachineryEquipment', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $acquisitionOfMachineryEquipment = trim($estimatedProjectCostArray['acquisitionOfMachineryEquipment']);
    if (BaseHTML::fieldAccess(['fNm' => 'acquisitionOfFurnitureFixtures', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $acquisitionOfFurnitureFixtures = trim($estimatedProjectCostArray['acquisitionOfFurnitureFixtures']);
    if (BaseHTML::fieldAccess(['fNm' => 'inventoryPurchase', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $inventoryPurchase = trim($estimatedProjectCostArray['inventoryPurchase']);
    if (BaseHTML::fieldAccess(['fNm' => 'workingCapital', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $workingCapital = trim($estimatedProjectCostArray['workingCapital']);
    if (BaseHTML::fieldAccess(['fNm' => 'refinancingExistingBusinessDebt', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $refinancingExistingBusinessDebt = trim($estimatedProjectCostArray['refinancingExistingBusinessDebt']);
    //if (BaseHTML::fieldAccess(array('fNm' => 'otherCostText', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1)))
    $otherCostText = trim($estimatedProjectCostArray['otherCostText']); //no FF control
    if (BaseHTML::fieldAccess(['fNm' => 'otherCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $otherCost = trim($estimatedProjectCostArray['otherCost']);
    if (BaseHTML::fieldAccess(['fNm' => 'otherCost2', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $otherCost2 = trim($estimatedProjectCostArray['otherCost2']);
    //if (BaseHTML::fieldAccess((array('fNm' => 'otherCostText2', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1)))
    $otherCostText2 = trim($estimatedProjectCostArray['otherCostText2']); //no FF control
    //if (BaseHTML::fieldAccess(array('fNm' => 'estimatedProjectCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1))) $estimatedProjectCost = trim($estimatedProjectCostArray["estimatedProjectCost"]);
    if (BaseHTML::fieldAccess(['fNm' => 'lessOwnerEquityToBeInjected', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $lessOwnerEquityToBeInjected = trim($estimatedProjectCostArray['lessOwnerEquityToBeInjected']);
    if (BaseHTML::fieldAccess(['fNm' => 'lessSellerCarryBack', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $lessSellerCarryBack = trim($estimatedProjectCostArray['lessSellerCarryBack']);
    //if (BaseHTML::fieldAccess(array('fNm' => 'loanRequestedForProject', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1))) $loanRequestedForProject = trim($estimatedProjectCostArray["loanRequestedForProject"]);
    if (BaseHTML::fieldAccess(['fNm' => 'franchiseFee', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $franchiseFee = trim($estimatedProjectCostArray['franchiseFee']);
    if (BaseHTML::fieldAccess(['fNm' => 'closingCost', 'sArr' => $epcArray, 'opt' => 'D', 'tf' => 1])) $closingCost = trim($estimatedProjectCostArray['closingCost']);

    $estimatedProjectCost = Strings::replaceCommaValues($landAcquisition) + Strings::replaceCommaValues($newBuildingConstruction) + Strings::replaceCommaValues($constructionContingency) +
        Strings::replaceCommaValues($businessAcquisition) + Strings::replaceCommaValues($landAndBusinessAcquisition) + Strings::replaceCommaValues($buildingOrLeasehold) + Strings::replaceCommaValues($acquisitionOfMachineryEquipment) +
        Strings::replaceCommaValues($acquisitionOfFurnitureFixtures) + Strings::replaceCommaValues($inventoryPurchase) + Strings::replaceCommaValues($workingCapital) + Strings::replaceCommaValues($refinancingExistingBusinessDebt) +
        Strings::replaceCommaValues($otherCost) + Strings::replaceCommaValues($otherCost2) + Strings::replaceCommaValues($franchiseFee) + Strings::replaceCommaValues($closingCost);
    $estProjectCost = round($estimatedProjectCost, 2);

    $loanRequestedForProject = ($estimatedProjectCost - (Strings::replaceCommaValues($lessOwnerEquityToBeInjected) + Strings::replaceCommaValues($lessSellerCarryBack)));
    $loanRequestedForProject = round($loanRequestedForProject, 2);
}
/* Estimated Project Cost */

$taxes1 = Strings::showField('taxes1', 'incomeInfo');
$insurance1 = Strings::showField('insurance1', 'incomeInfo');

if ($isTherePrePaymentPenalty == 'Yes') $prepayentSectionDisplay = 'display: block;';
//empty for CRB
if (in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
    $lien1Terms = Strings::showField('lien1Terms', 'LMRInfo');
} else {
    //other PCs
    if ($isHMLO == 1 && $lien1Terms == '') {
        $lien1Terms = LoanTerms::INTEREST_ONLY;
    }
}
if ($isLoanPaymentAmt == 'SMP') {
    $lien1Terms = '';
}

//	if($isHMLO == 1 && $loanTerm == '')				$loanTerm ='360 Months';      /** Bydefault loan term dropdown should be 'select'_ card308**/
if ($isHMLO == 1 && $prePaymentPenalty == '') $prePaymentPenalty = 'None';
if ($isHMLO == 1 && $lienPosition == '') $lienPosition = '1';
//if ($isHMLO == 1 && $downPaymentPercentage == '') $downPaymentPercentage = '20';

if (Dates::IsEmpty($purchaseCloseDate)) {
    $purchaseCloseDate = '';
} else {
    $purchaseCloseDate = Dates::formatDateWithRE($purchaseCloseDate, 'YMD', 'm/d/Y');
}

$dateDiff = [];
$dateDiff['lastPaymentMade'] = date('Y-m-d');
$dateDiff['futureDate'] = $purchaseCloseDate;
$daysUntilClose = Integers::calculateNoOfDaysBehind($dateDiff);
if ($daysUntilClose < 0) {
    $daysUntilClose = '';
}

if (Dates::IsEmpty($desiredCloseDate)) {
    $desiredCloseDate = '';
} else {
    $desiredCloseDate = Dates::formatDateWithRE($desiredCloseDate, 'YMD', 'm/d/Y');
}

if (Dates::IsEmpty($loanTermExpireDate)) {
    $loanTermExpireDate = '';
} else {
    $loanTermExpireDate = Dates::formatDateWithRE($loanTermExpireDate, 'YMD', 'm/d/Y');
}

require 'HMLOLoanTermsCalculation.php';
/* Story : 28638 Default the Lender Name (HMLOLender) in loan file  to Branch Company Name. */
if ($PCID == glPCID::PCID_3187 && $HMLOLender == '') {
    $HMLOLender = $myFileInfo['BranchInfo']['company'];
}
if (!$HMLOLender) $HMLOLender = Strings::showField('processingCompanyName', 'PCInfo');
if ($HMLOLender == 'Array') $HMLOLender = '';
/*********** - Clear Lender Name given as Array**/


$disabledInputForClient = 1;
if ($userGroup == 'Client') $disabledInputForClient = 0;

$includeTaxesInsuranceHOA = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->includeTaxesInsuranceHOA;
?>
<script>
    $(function () {
        $('#purchaseCloseDate').change(function () {
            var date1 = $('#purchaseCloseDate').val();
            if (date1 == '') return;
            var date2 = new Date();
            dateDiffInDays = (date_diff_indays(date2, date1));
            if (dateDiffInDays < 0) {
                $('#daysUntilClose').val('');
            } else {
                $('#daysUntilClose').val(dateDiffInDays);
            }
        });
    });
</script>
<?php
/*
if ($typeOfHMLOLoanRequesting == 'Line of Credit') {
<style>
	.HMLOTotalLoanAmt { background-color: transparent; }
</style>
 } 
*/
?>
<input type="hidden" name="isHMLOOpt" value="<?php echo $isHMLO ?>"/>
<input type="hidden" name="isEFOOpt" value="<?php echo $isEF; ?>"/>
<input type="hidden" name="isFeeUpdated" value="1"/>
<input type="hidden" name="midFico" id="midFicoScore" value="<?php echo $fileHMLOInfo['midFicoScore']; ?>"/>
<input type="hidden" name="fixflipProp" id="borNoOfREPropertiesCompleted"
       value="<?php echo $fileHMLOExperienceInfo['borNoOfREPropertiesCompleted']; ?>"/>
<input type="hidden" name="rehabGround" id="borRehabPropCompleted"
       value="<?php echo $fileHMLOExperienceInfo['borRehabPropCompleted']; ?>"/>

<?php if ($isHMLO == 1 || $isEF == 1) { ?>    <!-- HMLO Layout Modification May 09, 2017-->
    <?php require 'LMRequest/sections/loanSettingForm.php'; ?>
    <?php require 'HMLOLoanTermsForm.php'; /** Loan Terms Section. **/ ?>

    <?php
    if (count($secArr = BaseHTML::sectionAccess2(['sId' => 'ACF', 'opt' => $fileTab])) > 0) { // Get Active Fields only...
        loanForm::pushSectionID('ACF');

        if (count(Arrays::getValueFromArray('ACF', $fieldsInfo)) > 0) {
            ?>
            <div class="col-md-12 <?php echo loanForm::showField('guidelinewarning'); ?>"
                 id="divGuidelinesErrorMsg"></div>
            <?php
        }
    }
    ?>

    <!-- Estimated Project Cost -->

    <?php require 'estimatedProjectCost.php'; ?>

    <div class="clearfix"></div>

    <?php require 'HMLOFeesAndCostsSection.php'; ?>

    <div class="row <?php if ($PCID == 4208 || $PCID == 4636 || $PCID == 4642) {
        echo 'd-none';
    } ?>">
        <div class="col-md-6">
            <div class="card card-custom RequiredReservesCard">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            Required Liquidity Reserves
                        </h3>
                        <i class="popoverClass fas fa-info-circle text-primary"
                           data-html="true"
                           data-content="This section is used to indicate required liquidity in the borrower's financial accounts.
                           For example a bank account. These amounts will not increase or be included in any loan amounts."></i>
                    </div>
                    <div class="card-toolbar ">
                        <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                              data-card-tool="toggle"
                              data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
                        <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                              data-card-tool="reload"
                              data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </span>
                        <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                              data-card-tool="toggle"
                              data-section="RequiredReservesCard"
                              data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
                    </div>
                </div>
                <div class="card-body RequiredReservesCard_body">
                    <?php if (in_array('Required Reserves', $lockedSections)) {
                        $BackupAllowToEdit = $allowToEdit;
                        $allowToEdit = false;
                    } ?>
                    <table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                        <tr class=""
                            style="">
                            <td width="60%">
                                <label class="font-weight-bold">
                                    <i class="fa fa-info-circle text-primary"
                                       data-toggle="tooltip"
                                       data-trigger="hover"
                                       data-html="true"
                                       title=""
                                       data-original-title="This amount is the monthly payment multiplied by the specified number of months."></i>
                                    Interest / Payment Reserves
                                </label>
                                <?php if (!glCustomJobForProcessingCompany::isPC_CV3($PCID)) { ?>
                                    <input type="checkbox"
                                           name="includeTaxesInsuranceHOA"
                                           id="includeTaxesInsuranceHOA"
                                           value="1"
                                           onclick="calculatePaymentReserves();calculateTotalRequiredReserves();"
                                        <?php echo Strings::isChecked(1, $includeTaxesInsuranceHOA); ?>
                                    >
                                    Include Taxes, Insurance & HOA
                                <?php } ?>
                            </td>
                            <td width="20%">
                                <?php
                                if ($allowToEdit && $disabledInputForClient) { ?>
                                    <select name="paymentReserves"
                                            id="paymentReserves"
                                            class="form-control"
                                            onchange="calculatePaymentReserves('loanModForm', 'paymentReservesAmt');calculateTotalRequiredReserves();"
                                            tabindex="<?php echo $tabIndex++; ?>">
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($i = 0; $i < count($glPaymentReserves); $i++) {
                                            $payReserves = trim($glPaymentReserves[$i]);
                                            $sOpt = Arrays::isSelected($payReserves, $paymentReserves);
                                            $payReservesMonth = $payReserves . ' Month';
                                            if ($payReserves > 1) $payReservesMonth .= 's';
                                            ?>
                                            <option value="<?php echo $payReserves ?>" <?php echo $sOpt ?>><?php echo $payReservesMonth; ?></option>
                                        <?php } ?>
                                    </select>
                                    <?php
                                } else {
                                    if ($paymentReserves == 0) {
                                    } else {
                                        echo $paymentReserves . ' %';
                                    }
                                }
                                ?>

                            </td>
                            <td width="20%"><h5>$
                                    <span id="paymentReservesAmt"><?php echo Currency::formatDollarAmountWithDecimal($paymentReservesAmt) ?></span>
                                </h5></td>
                        </tr>
                        <tr class=""
                            style="">
                            <td width="60%">
                                <label class="font-weight-bold">
                                    <i id="lessPrePayInterestReserveToolTip"
                                       class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                       data-html="true"
                                       data-formula="<?php echo HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTip; ?>"
                                       title="<?php echo HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTipWithValues
                                           ? (HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTip . '<hr>' . HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTipWithValues)
                                           : HMLOLoanTermsCalculation::$lessPrePayInterestReserveToolTip; ?>">
                                    </i>
                                    Less Pre-paid interest Reserve
                                </label>
                            </td>
                            <td width="20%"></td>
                            <td width="20%">
                                <h5>
                                    $
                                    <span id="lessPrePayInterestReserve"><?php echo Currency::formatDollarAmountWithDecimalZeros($prepaidInterestReserve); ?></span>
                                </h5>
                            </td>
                        </tr>
                        <tr class=""
                            style="">
                            <td width="60%">
                                <label class="font-weight-bold">
                                    <i id="totalInterestPaymentReserveRequiredToolTip"
                                       class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                       data-html="true"
                                       data-formula="<?php echo HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTip; ?>"
                                       title="<?php echo HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTipWithValues
                                           ? (HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTip . '<hr>' . HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTipWithValues)
                                           : HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequiredToolTip;
                                       ?>">
                                    </i>
                                    Total Interest Payment Reserve Required
                                </label>
                            </td>
                            <td width="20%"></td>
                            <td width="20%">
                                <h5>
                                    $
                                    <span id="totalInterestPaymentReserveRequired"><?php echo Currency::formatDollarAmountWithDecimalZeros(HMLOLoanTermsCalculation::$totalInterestPaymentReserveRequired); ?></span>
                                </h5>
                            </td>
                        </tr>

                        <?php
                        if ($isEF != 1) {
                            if ($doesPropertyNeedRehabDispDiv) {
                                $doesPropertyNeedRehabDispDiv = 'tableRow';
                            }
                            if ($contigencyCls) {
                                $contigencyCls = 'tableRow';
                            }
                            ?>
                            <tr class="even doesPropertyNeedRehabDispDiv"
                                style="<?php echo $doesPropertyNeedRehabDispDiv ?>">
                                <td class="contigencyCls"
                                    width="60%"
                                    style="<?php echo $contigencyCls ?>">
                                    <label class="font-weight-bold"
                                           style="">
                                        <i class="fa fa-info-circle text-primary"
                                           data-toggle="tooltip"
                                           data-trigger="hover"
                                           data-html="true"
                                           title="This amount is the specified % of a rehab amount not being included in the financing of the loan."></i>
                                        % Required Construction /<br>Rehab Budget Not Financed
                                    </label>
                                </td>
                                <td width="20%"
                                    class=""
                                    style="">
                                    <?php
                                    if ($allowToEdit && $disabledInputForClient) { ?>
                                        <select name="requiredConstruction"
                                                id="requiredConstruction"
                                                class="form-control"
                                                onchange="calculateRequiredConstruction('requiredConstructionAmt');calculateTotalRequiredReserves();"
                                                tabindex="<?php echo $tabIndex++; ?>">
                                            <option value=""> - Select -</option>
                                            <?php
                                            foreach ($glRequiredConstruction as $eachRequiredConstruction) {
                                                $eachRequiredConstruction = trim($eachRequiredConstruction);
                                                $sOpt = Arrays::isSelected($eachRequiredConstruction, $requiredConstruction);
                                                ?>
                                                <option value="<?php echo $eachRequiredConstruction ?>" <?php echo $sOpt ?>><?php echo $eachRequiredConstruction ?></option>
                                            <?php } ?>
                                        </select>
                                        <?php
                                    } else {
                                        echo $requiredConstruction ? $requiredConstruction . ' %' : '';
                                    }
                                    ?>
                                </td>
                                <td class="contigencyCls"
                                    style="<?php echo $contigencyCls ?>">
                                    <h5 class=""
                                        style="">
                                        $ <span
                                                id="requiredConstructionAmt"><?php echo Currency::formatDollarAmountWithDecimal($requiredConstructionAmt) ?></span>
                                    </h5>
                                </td>
                            </tr>
                            <tr class="doesPropertyNeedRehabDispDiv"
                                style="<?php echo $doesPropertyNeedRehabDispDiv ?>">
                                <td width="60%"
                                    class="">
                                    <label class="font-weight-bold">
                                        <i class="fa fa-info-circle text-primary popoverClass"
                                           data-trigger="hover"
                                           data-html="true"
                                           title="% Contingency Reserve"
                                           id="contingencyReserveAmtTooltip"
                                           data-content="<?=
                                           HMLOLoanTermsCalculation::$contingencyReserveTooltipWithValues ?
                                               HMLOLoanTermsCalculation::$contingencyReserveTooltip.'<hr>'.HMLOLoanTermsCalculation::$contingencyReserveTooltipWithValues : HMLOLoanTermsCalculation::$contingencyReserveTooltip ; ?>"></i>
                                        % Contingency Reserve
                                    </label>
                                </td>
                                <td width="20%">
                                    <div class=""
                                         style="">
                                    <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                        <select name="contingencyReserve"
                                                id="contingencyReserve"
                                                class="form-control"
                                                onchange="loanCalculation.calculateContingencyReserve();calculateTotalRequiredReserves();"
                                                tabindex="<?php echo $tabIndex++; ?>">
                                            <option value=""> - Select -</option>
                                            <?php
                                            foreach ($glRequiredConstruction as $contingency) {
                                                   $sOpt = Arrays::isSelected($contingency, $contingencyReserve);?>
                                                <option value="<?php echo $contingency ?>" <?php echo $sOpt ?>><?php echo $contingency ?></option>
                                            <?php } ?>
                                        </select>
                                        <?php
                                    } else {
                                        echo $contingencyReserve ? $contingencyReserve . ' %' : '';
                                    }
                                    ?>
                                    </div>
                                </td>
                                <td><h5>$
                                        <span id="contingencyReserveAmt"><?php echo Currency::formatDollarAmountWithDecimal($contingencyReserveAmt) ?></span>
                                    </h5>
                                </td>
                            </tr>
                        <?php } ?>
                        <tr class=""
                            style="">
                            <td width="60%">
                                <label class="font-weight-bold">
                                    <i class="fa fa-info-circle text-primary"
                                       data-toggle="tooltip"
                                       data-trigger="hover"
                                       data-html="true"
                                       title=""
                                       data-original-title="This amount is the specified % of the total loan amount."></i>
                                    % of Total Loan Amount
                                </label>
                            </td>
                            <td width="20%">
                                <?php
                                if ($allowToEdit && $disabledInputForClient) { ?>
                                    <input type="text" class="form-control"
                                           name="percentageTotalLoan"
                                           placeholder="0.0"
                                           id="percentageTotalLoan"
                                           value="<?php echo $percentageTotalLoan; ?>"
                                           onblur="calculateTotalRequiredReserves();"
                                    >
                                    <?php
                                } else {
                                        echo $percentageTotalLoan ? $percentageTotalLoan . ' %' : '';
                                }
                                ?>
                            </td>
                            <td width="20%"><h5>$
                                    <span id="percentageTotalLoanAmount"><?php echo Currency::formatDollarAmountWithDecimal($percentageTotalLoanAmount) ?></span>
                                </h5></td>
                        </tr>
                        <tr class="bg-secondary">
                            <td colspan="2">
                                <label class="font-weight-bold">
                                    <i id="totalRequiredReservesTooltip"
                                       class="fa fa-info-circle text-primary popoverClass"
                                       data-html="true"
                                       data-formula="<?php echo HMLOLoanTermsCalculation::$totalRequiredReservesTooltip; ?>"
                                       data-content="<?php echo HMLOLoanTermsCalculation::$totalRequiredReservesTooltipWithValues
                                           ? (HMLOLoanTermsCalculation::$totalRequiredReservesTooltip . '<hr>' . HMLOLoanTermsCalculation::$totalRequiredReservesTooltipWithValues)
                                           : HMLOLoanTermsCalculation::$totalRequiredReservesTooltip;
                                       ?>">
                                    </i>
                                    Total Required Reserves</label>
                            </td>
                            <td class="h5">$<span id="totalRequiredReserves" class="pl-2"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$totalRequiredReserves) ?></span>
                            </td>
                        </tr>
                    </table>
                    <?php
                    if (in_array('Required Reserves', $lockedSections)) {
                        //$BackupAllowToEdit = $allowToEdit;
                        $allowToEdit = $BackupAllowToEdit;
                    }
                    ?>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card card-custom cashToCloseCard">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            Cash-to-Close
                        </h3>
                    </div>
                    <div class="card-toolbar">
                        <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                              data-card-tool="toggle"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
                        <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                              data-card-tool="reload"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </span>
                        <span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                              data-card-tool="toggle"
                              data-section="cashToCloseCard"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
                    </div>
                </div>

                <div class="card-body cashToCloseCard_body">
                    <?php if (in_array('Required Reserves', $lockedSections)) {
                        $BackupAllowToEdit = $allowToEdit;
                        $allowToEdit = false;
                    } ?>
                    <table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                        <tr class="hideFieldsForLTC2"
                            style="<?php echo HMLOLoanTermsCalculation::$hideFieldsForLTC2; ?>">
                            <td class="p-2"
                                style="width: 60%">
                                <label class="font-weight-bold">
                                    <i class="fa fa-info-circle text-primary popoverClass ml-2"
                                   data-html="true"
                                   id="closingCostNotFinancedTooltip"
                                   title="Closing Costs Not Financed"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$closingCostNotFinancedTooltip; ?>"
                                   data-content="<?php echo HMLOLoanTermsCalculation::$closingCostNotFinancedTooltipWithValues ?
                                       (HMLOLoanTermsCalculation::$closingCostNotFinancedTooltip . '<hr>' . HMLOLoanTermsCalculation::$closingCostNotFinancedTooltipWithValues)
                                       : HMLOLoanTermsCalculation::$closingCostNotFinancedTooltip; ?>"></i>
                                Closing Costs Not Financed
                                </label>
                            </td>
                            <td class="h5">$
                                <span id="closingCostNotFinanced"><?php echo Currency::formatDollarAmountWithDecimal($closingCostNotFinanced) ?></span>
                            </td>
                        </tr>
                        <tr class="even downPaymentField hideFieldsForLTC2 "
                            style="<?php echo $downPaymentFieldDispOpt; ?> <?php echo HMLOLoanTermsCalculation::$hideFieldsForLTC2; ?>">
                            <td class="p-2">
                                <label class="font-weight-bold">
                                    <i class="fa fa-info-circle text-primary popoverClass ml-2"
                                       data-html="true"
                                       id="downPaymentHtmlTooltip"
                                       title="Down Payment"
                                       data-formula="<?php echo HMLOLoanTermsCalculation::$downPaymentHtmlTooltip; ?>"
                                       data-content="<?php echo HMLOLoanTermsCalculation::$downPaymentHtmlTooltipWithValues; ?>"></i>
                                    Overall Down Payment</label>
                            </td>
                            <td class="h5">$
                                <span id="downPayment"><?php echo Currency::formatDollarAmountWithDecimal($maxAmtToPutDown) ?></span>
                            </td>
                        </tr>
                        <tr class="hideFieldsForLTC2 "
                            style="<?php echo HMLOLoanTermsCalculation::$hideFieldsForLTC2; ?>">
                            <td class="p-2"><label class="font-weight-bold">Earnest Deposit</label></td>
                            <td>
                                <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               name="earnestDeposit"
                                               id="earnestDeposit"
                                               class="form-control"
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal($earnestDeposit) ?>"
                                               size="20"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                               onblur="currencyConverter(this, this.value);calculateTotalCashToClose();"
                                        />

                                    </div>
                                <?php } else { ?>
                                    <h5>$ <?php echo Currency::formatDollarAmountWithDecimal($earnestDeposit) ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr class="even hideFieldsForLTC2 "
                            style="<?php echo HMLOLoanTermsCalculation::$hideFieldsForLTC2; ?>">
                            <td class="p-2"><label class="font-weight-bold">Paid Outside Escrow</label></td>
                            <td>
                                <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               name="otherDownPayment"
                                               id="otherDownPayment"
                                               placeholder="0.00"
                                               value="<?php echo Currency::formatDollarAmountWithDecimal($otherDownPayment) ?>"
                                               size="20"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off"
                                               class="form-control"
                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                               onblur="currencyConverter(this, this.value);calculateTotalCashToClose();"
                                        />
                                    </div>
                                <?php } else { ?>
                                    <h5>$ <?php echo Currency::formatDollarAmountWithDecimal($otherDownPayment) ?></h5>
                                <?php } ?>
                            </td>
                        </tr>
                        <tr class="bg-secondary">
                            <td class="p-2">
                                <label class="font-weight-bold">
                                    <i class="fa fa-info-circle text-primary popoverClass ml-2"
                                   data-html="true"
                                   id="totalCashToCloseTooltip"
                                   title="Total Cash to Close"
                                   data-formula="<?php echo HMLOLoanTermsCalculation::$totalCashToCloseTooltip; ?>"
                                   data-content="<?php echo HMLOLoanTermsCalculation::$totalCashToCloseTooltipWithValues ?
                                       (HMLOLoanTermsCalculation::$totalCashToCloseTooltip . '<hr>' . HMLOLoanTermsCalculation::$totalCashToCloseTooltipWithValues)
                                       : HMLOLoanTermsCalculation::$totalCashToCloseTooltip ?>"></i>
                                Total Cash to Close
                                </label>
                            </td>
                            <td class="h5">$
                                <span id="totalCashToClose"><?php echo Currency::formatDollarAmountWithDecimal($totalCashToClose) ?></span>
                            </td>
                        </tr>
                    </table>

                    <?php
                    if (in_array('Required Reserves', $lockedSections)) {
                        //$BackupAllowToEdit = $allowToEdit;
                        $allowToEdit = $BackupAllowToEdit;
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" name="addRentableSqFt" id="addRentableSqFt"
           value="<?php echo $addRentableSqFtForCashFlow; ?>">
    <input type="hidden" name="noUnitsOccupied" id="noUnitsOccupied"
           value="<?php echo $noUnitsOccupiedForCashFlow; ?>">
    <?php require 'subjectPropertyCashFlow.php'; ?>
    <div class="clear pad2"></div>
    <!-- Additional Questions Start -->
    <?php require 'additionalQuestions.php'; ?>
    <?php require 'loanOriginatorInfo.php'; ?>
    <!-- Additional Questions End -->
    <!-- Additional Terms & Requirements -->
    <div class="card card-custom additionalQuestionCard ">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <?php echo loanForm::toggleSectionV2(
                'additionalQuestion',
                true,
                true,
                '',
                'Additional Terms &amp; Requirements for Loan Processing &amp; Underwriting',
                $userRole == 'Manager' ? 'This section is controlled in system settings-->Custom Loan Guidelines. You can control the content based on loan program as well.' : ''
            ); ?>
        </div>
        <div class="card-body additionalQuestionCard_body">
            <div class="row  ">
                <div class="col-md-12">
                    <?php echo loanForm::textarea(
                        'expectForDueDiligence',
                        $allowToEdit && $userGroup != 'Client',
                        $tabIndex++,
                        Strings::showField('expectForDueDiligence', 'fileHMLOPropertyInfo') ? urldecode(Strings::showField('expectForDueDiligence', 'fileHMLOPropertyInfo')) : $reqForLoanProUnderwriting,
                        'tinyMceClass'
                    ); ?>
                </div>
            </div>
        </div>
    </div>
    <?php if ($allowToEdit) { ?>
        <div style="text-align: center">
            <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveBtn" value="Save"
                   tabindex="<?php echo $tabIndex++; ?>">
            <input type="submit" class="btn btn-primary btnSave" name="btnSave" id="saveNextBtn" value="Save & Next"
                   tabindex="<?php echo $tabIndex++; ?>"
                   onclick="if(this.disabled==false) {return true;} else {return false;}">
            <?php
            if ($PCID == glPCID::PCID_CRB) {
                $package = glCustomJobForProcessingCompany::getTermSheetPackageId($fileloanProgram);
            } else if(glCustomJobForProcessingCompany::isPC_CentrifundLLC($PCID)) {
                $package =  packageId::TRANSACTION_SUMMARY;
            }
            if (!$package) {
                $package = packageId::LOAN_TERM_SHEET;
            }

            if ($isHMLO == 1 && $activeTab == 'HMLI') {
                $url = CONST_SITE_URL . 'package/pkgController.php?rId=' . cypher::myEncryption($LMRResponseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption($package) . '&opt=sample';
                ?>
                <a href="<?php echo $url; ?>" target="_blank" style="" class="btn btn-primary">
                    <?php if(glCustomJobForProcessingCompany::isPC_CentrifundLLC($PCID)){?>
                        View Transaction Summary
                    <?php } else {?>View Term Sheet<?php }?></a>
                <?php
            }
            ?>
        </div>
    <?php } ?>

<?php } ?>
<script type="text/javascript">
    $('select.js-example-basic-multiple').select2();
</script>

<!-- HMLONewLoanInfoForm.php -->
