class loan_tab {
    static multiValue() {
        let val = parseInt($('#tblCustomFieldTypeId').val());
        let checkbox = [10, 13, 11, 12].includes(val);
        let address = [17].includes(val);

        if (checkbox) {
            $('#OptionsDiv').show();
        } else {
            $('#OptionsDiv').hide();
        }

        if (address) {
            $('#AddressDiv').show();
        } else {
            $('#AddressDiv').hide();
        }

    }
}

$(function () {
    $('#tblCustomFieldTypeId').on('change', function () {
        loan_tab.multiValue();
    });
    loan_tab.multiValue();

    $('#custom_field_form').on('submit', function (e) {

        let isValid = true;

        $(this).find('.validate').each(function () {
            let value = $(this).val().trim();
            let fieldId = $(this).attr('id');
            let labelText = $("label[for='" + fieldId + "']").text();

            if (value === '') {
                toastrNotification(labelText + ' is required.', 'error');
                $(this).focus();
                isValid = false;
                return false; // break out of .each loop
            }
        });
        if (!isValid) {
            e.preventDefault(); // stop form submission
        }
    });

});