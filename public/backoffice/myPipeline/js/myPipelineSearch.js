$(function () {
    $("select#eId").select2({
        width: '100%',
        scrollAfterSelect: false,
        closeOnSelect: false,
        placeholder: "Enter Branch Name To Search",
        allowClear: false,
        dropdownAutoWidth: false,
        multiple: true,
        ajax: {
            delay: 250,
            url: siteSSLUrl + "JQFiles/getBranches.php",
            dataType: 'json',
            data: function (params) {
                return {
                    query: params.term, // search term
                    page: params.page,
                    PCID: document.LMRReport.searchPCID.value,
                    opt: 'json',
                };
            },
            processResults: function (data,) {
                return {
                    results: data.items,
                };
            },
            cache: true
        },
        minimumInputLength: 0,
    });

    $("select#broker").select2({
        width: '100%',
        scrollAfterSelect: false,
        closeOnSelect: false,
        placeholder: "Enter Broker Name To Search",
        allowClear: false,
        dropdownAutoWidth: false,
        multiple: true,
        ajax: {
            delay: 250,
            url: siteSSLUrl + "JQFiles/getBrokers.php",
            dataType: 'json',
            data: function (params) {
                return {
                    query: params.term, // search term
                    PCID: document.LMRReport.searchPCID.value,
                    eId: $('#eId').val(),
                    opt: 'json',
                };
            },
            processResults: function (data,) {
                return {
                    results: data.items,
                };
            },
            cache: false
        },
        minimumInputLength: 0,
    });

    $("select#loanOfficerSearch").select2({
        width: '100%',
        scrollAfterSelect: false,
        closeOnSelect: false,
        placeholder: "Enter Loan Officer Name To Search",
        allowClear: false,
        dropdownAutoWidth: false,
        multiple: true,
        //selectOnClose: true,
        //theme: "classic",
        ajax: {
            delay: 250,
            url: siteSSLUrl + "JQFiles/getBrokers.php",
            dataType: 'json',
            data: function (params) {
                return {
                    query: params.term, // search term
                    PCID: document.LMRReport.searchPCID.value,
                    eId: $('#eId').val(),
                    opt: 'json',
                    agentType: 1,
                };
            },
            processResults: function (data,) {
                return {
                    results: data.items,
                };
            },
            cache: false
        },
        minimumInputLength: 0,
    });

    $("select#employeeId").select2({
        width: '100%',
        scrollAfterSelect: false,
        closeOnSelect: false,
        placeholder: "Enter Loan Officer Name To Search",
        allowClear: false,
        dropdownAutoWidth: false,
        multiple: true,
        ajax: {
            delay: 250,
            url: siteSSLUrl + "JQFiles/getEmployees.php",
            dataType: 'json',
            data: function (params) {
                return {
                    query: params.term, // search term
                    PCID: document.LMRReport.searchPCID.value,
                    opt: 'json',
                    AE: 1,
                };
            },
            processResults: function (data,) {
                return {
                    results: data.items,
                };
            },
            cache: false
        },
        minimumInputLength: 0,
    });


    $("select#contactSearchId").select2({
        width: '100%',
        scrollAfterSelect: false,
        closeOnSelect: false,
        placeholder: "Enter Contact Name To Search",
        allowClear: false,
        dropdownAutoWidth: false,
        multiple: true,
        ajax: {
            delay: 250,
            url: siteSSLUrl + "JQFiles/getContacts.php",
            dataType: 'json',
            data: function (params) {
                return {
                    query: params.term, // search term
                    PCID: document.LMRReport.searchPCID.value,
                    contactId: $('#contactId').val(),
                    opt: 'json',
                };
            },
            processResults: function (data,) {
                return {
                    results: data.items,
                };
            },
            cache: false
        },
        minimumInputLength: 0,
    });
});
