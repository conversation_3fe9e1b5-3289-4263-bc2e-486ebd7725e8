<?php

use models\Controllers\backoffice\myPipeline;
use models\PageVariables;
use models\Request;

global $pageNumber, $sortOpt, $orderBy, $viewPrivateNotes, $fileType;
global $massStatusValue, $massStatusCnt, $employeeId, $secondaryBrokerNumber;
global $brokerNumb, $leadSource, $searchTerm, $clientID, $AllPCInfo;
global $PCID, $userGroup, $userNumber, $startDate, $endDate, $executiveId;
global $priorityLevel, $primaryStatus, $myStaleDay, $lenderName, $LMRClientTypes;
global $propertyState, $billingDueDate, $notesType, $op, $recNumbEnd;
global $searchField, $statusOpt, $allECount, $maxN4Export, $LMRDataCnt;
global $modifiedStartDate, $modifiedEndDate, $closingStartDate, $closingEndDate;
global $multiplePrimaryStatus, $multipleModuleCode, $isPCSLM, $WFStepIDs;
global $isPCHMLO, $userRole, $Breadcrumb, $allowCFPBAuditing, $loggedInUserPCID;
global $RESTPaymentDetailsArray, $HOMEPaymentDetailsArray, $userSeeBilling, $loanOfficerSearch,
       $WFID, $WFSID, $WFSNotCompletedId, $LMRInternalClientType, $servicingStatusCodeArray,
       $referringParty, $paymentBased, $contactsNumbers;

$HOMETotalOwed = 0;
$RESTTotalOwed = 0;

$mySavedViews = myPipeline::getSavedViews();

$selectedView = null;
foreach ($mySavedViews as $view) {
    if (($_REQUEST['savedView'] ?? null) == $view->hash) {
        $selectedView = $view;
    }
}
//pr($selectedView);
//pr($mySavedViews);
//exit;

$userAutomationControlAccess = myPipeline::$userAutomationControlAccess;
if ($userGroup != 'Employee') {
    $userAutomationControlAccess = 0;
}
$selectedInternalLoanProgram = implode("','", $LMRInternalClientType);
if ($selectedInternalLoanProgram) {
    $selectedInternalLoanProgram = "'" . $selectedInternalLoanProgram . "'";
}
$selectedServicingStatus = implode("','", $servicingStatusCodeArray);
if ($selectedServicingStatus) {
    $selectedServicingStatus = "'" . $selectedServicingStatus . "'";
}

$hidden_values = [
    'pageNumber'                   => $pageNumber,
    'savedView'                    => Request::GetClean('savedView') ?? '',
    'sortBy'                       => $sortOpt,
    'orderBy'                      => $orderBy,
    'noOfSelectedClient'           => '',
    'selectedFileNo'               => '',
    'seePrivate'                   => $viewPrivateNotes,
    'reportId'                     => myPipeline::$reportId,
    'fileType'                     => $fileType,
    'massStatusValue'              => $massStatusValue,
    'selMassOpt'                   => '',
    'massStatusCnt'                => $massStatusCnt,
    'MassFiles'                    => '',
    'UnselectedMassFiles'          => '',
    'massStatusId'                 => myPipeline::$reportId,
    'selectedEmpID'                => $employeeId,
    'selectedSecondaryBrokerID'    => $secondaryBrokerNumber,
    'selectedBrokerID'             => $brokerNumb,
    'massSearch'                   => $searchTerm,
    'clientID'                     => $clientID,
    'selVal'                       => '',
    'noOfFiles'                    => '',
    'selValMassEmail'              => '',
    'optUseMyNameAndEmail'         => $AllPCInfo['useMyNameAndEmail'],
    'noOfFilesMassEmail'           => '',
    'PCID'                         => $PCID,
    'userGroup'                    => $userGroup,
    'userNumber'                   => $userNumber,
    'brokerNumb'                   => $brokerNumb,
    'startDate'                    => $startDate,
    'eDate'                        => $endDate,
    'executiveId'                  => $executiveId,
    'processingCompanyID'          => $PCID,
    'priLevel'                     => $priorityLevel,
    'myStaleDay'                   => $myStaleDay,
    'paymentBasedAmt'              => $paymentBased,
    'lenName'                      => $lenderName,
    'LMRCTypes'                    => $LMRClientTypes,
    'propState'                    => $propertyState,
    'bDD'                          => $billingDueDate,
    'ntsType'                      => $notesType,
    'op'                           => $op,
    'searchFields'                 => $searchField,
    'subStatus'                    => $statusOpt,
    'allCount'                     => $allECount,
    'maxN4Export'                  => $maxN4Export,
    'LMRDataCnt'                   => $LMRDataCnt,
    'massSearchTerm'               => $searchTerm,
    'massLeadSource'               => $leadSource,
    'modStartDate'                 => $modifiedStartDate,
    'modEndDate'                   => $modifiedEndDate,
    'multipleStatus'               => $multiplePrimaryStatus,
    'multipleModuleStatus'         => $multipleModuleCode,
    'lenderNames'                  => $lenderName,
    'isPCSLM'                      => $isPCSLM,
    'WFStepIDs'                    => $WFStepIDs,
    'isPCHMLO'                     => $isPCHMLO,
    'userAutomationControlAccess'  => $userAutomationControlAccess,
    'loanOfficerIds'               => $loanOfficerSearch,
    'selectedWFID'                 => implode(',', $WFID),
    'selectedWFSID'                => implode(',', $WFSID),
    'selectedWFSNotCompletedId'    => implode(',', $WFSNotCompletedId),
    'selectedInternalLoanPrograms' => $selectedInternalLoanProgram,
    'selectedServicingStatusCodes' => $selectedServicingStatus,
    'referringPartySearchValue'    => $referringParty,
    'selectedContactsIds'          => $contactsNumbers,
    'allowToViewAutomationPopup'   => PageVariables::User()->allowToViewAutomationPopup
];

?>
<form name="LMRReport"
      id="LMRReport"
      method="POST"
      class="form"
      action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>"
      onsubmit="return validateSearchFormFieldsInPipeline();">

    <?php foreach ($hidden_values as $name => $value) { ?>
        <input type="hidden" name="<?php echo htmlspecialchars($name); ?>" id="<?php echo htmlspecialchars($name); ?>"
               value="<?php echo htmlentities($value); ?>"/>
    <?php } ?>

    <div class="card card-custom">
        <div class="card-header card-header-tabs-line">
            <div class="card-title">
                <h3 class="card-label"><?php echo $Breadcrumb['title']; ?></h3>
            </div>
            <div class="card-toolbar">
                <div
                        class="tooltipClass card-title  btn-text-primary btn-hover-primary btn-icon m-1 "
                >
                    <a href="https://help.lendingwise.com/knowledge/save-pipeline" target="_blank"><i
                                class="fas fa-info-circle text-primary"></i></a>&nbsp;&nbsp;
                    <span style="cursor: pointer" class="btn btn-primary" onclick="myPipeline.showModal();">Save Current View</span>
                </div>

                <?php if ($mySavedViews) { ?>
                    <div class="tooltipClass card-title  btn-text-primary btn-hover-primary btn-icon m-1 ">
                        <div class="dropdown">
                            <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php echo $selectedView ? $selectedView->name : 'Saved Views' ?>
                            </button>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                <?php foreach ($mySavedViews as $view) { ?>
                                    <div class="dropdown-item">
                                        <div style="cursor: pointer;width: 100%;"
                                             data-hash="<?php echo $view->hash; ?>"
                                             onclick="myPipeline.loadView(this);"><?php echo $view->name; ?>
                                        </div>
                                        <div style="text-align: right;float: right;">
                                            <a style="color:#f64e60;font-weight: bold;"
                                               href="#"
                                               data-hash="<?php echo $view->hash; ?>"
                                               data-name="<?php echo htmlentities($view->name); ?>"
                                               onclick="myPipeline.deleteView(this);"
                                            >X</a>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                <?php } ?>


                <div title="Advanced Search"
                     class="tooltipClass card-title btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1 "
                     data-toggle="collapse" data-target="#collapseOne1">
                    <i class="flaticon-search text-primary"></i>
                </div>

                <?php
                if (!($userGroup == 'Auditor'
                    || $userGroup == 'Sales'
                    || $userRole == 'CFPB Auditor'
                    || $userRole == 'Auditor Manager'
                    || ($fileType == 'CFPB' && $allowCFPBAuditing == 1)
                )) {
                    require __DIR__ . '/myPipelineColumns.php';
                }

                ?>


            </div>
        </div>

        <div class="card-body p-0">
            <div class="accordion accordion-toggle-arrow" id="accordionExample1">
                <div id="collapseOne1" class="collapse  p-4 " data-parent="#accordionExample1">
                    <?php require __DIR__ . '/myPipelineSearch.php'; ?></div>
            </div>
            <?php require __DIR__ . '/myPipelineTab.php'; ?>
        </div>
    </div>


    <?php

    if ($recNumbEnd > 0) {
        echo "<div  class='row'><div class='col-lg-12'>";
        $myForm = 'LMRReport';
        require __DIR__ . '/../pageLinks.php';
        echo '</div></div>';
    }
    if ($userRole != 'Sales') {
        if (($userGroup == 'Super' || ($fileType == 2 && $userRole == 'Manager')) || ($userGroup == 'Super' || $fileType == 4)) {
            if (count($RESTPaymentDetailsArray) > 0) {
                $RESTTotalOwed = 0;
                $RESTTotalPaid = 0;
                $RESTBalance = 0;
                $RESTTotalOwed = trim($RESTPaymentDetailsArray['TotalOwed']);
                $RESTTotalPaid = trim($RESTPaymentDetailsArray['TotalPaid']);
                $RESTBalance = trim($RESTPaymentDetailsArray['Balance']);
                if ($userRole == 'Manager') {
                    $RESTTotalOwed = '<b>Balance</b>: ' . $RESTBalance;
                } else {
                    $RESTTotalOwed = '<b>Total Owed</b>: ' . $RESTTotalOwed . '<br><b>Total Paid</b>: ' . $RESTTotalPaid . '<br><b>Balance</b>: ' . $RESTBalance;
                }
            }
            if (count($HOMEPaymentDetailsArray) > 0) {
                $HOMETotalOwed = trim($HOMEPaymentDetailsArray['TotalOwed']);
                $HOMETotalPaid = trim($HOMEPaymentDetailsArray['TotalPaid']);
                $HOMEBalance = $HOMETotalOwed - $HOMETotalPaid;
                if ($userRole == 'Manager') {
                    $HOMETotalOwed = '<b>Balance</b>: ' . $HOMEBalance;
                } else {
                    $HOMETotalOwed = '<b>Total Owed</b>: ' . $HOMETotalOwed . '<br><b>Total Paid</b>: ' . $HOMETotalPaid . '<br><b>Balance</b>: ' . $HOMEBalance;
                }
            }
            ?>
            <script type="text/javascript">
                try {
                    document.getElementById('showRESTOwed').style.display = 'block';
                    <?php if(($userGroup == 'Super' && $fileType == 4) || ($userGroup == 'Employee' && $fileType == 4 && $userSeeBilling == 1) ) { ?>
                    document.getElementById('showRESTOwed').innerHTML = '<?php echo $HOMETotalOwed?>';
                    <?php } else { ?>
                    document.getElementById('showRESTOwed').innerHTML = '<?php echo $RESTTotalOwed?>';
                    <?php } ?>
                } catch (e) {
                }
            </script>
        <?php }
    } ?>
</form>

<script>
    let __WFSId = null;
    let __WFSNotCompletedId = null;
</script>
<script src="<?php echo CONST_BO_URL; ?>myPipeline/js/myPipeline.js?<?php echo CONST_JS_VERSION; ?>"></script>

<?php require __DIR__ . '/modals/saveView.php'; ?>
