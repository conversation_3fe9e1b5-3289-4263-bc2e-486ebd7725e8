<?php
//global variables
global $fileType, $userRole, $subStDate, $subEndDate, $searchTerm, $searchFieldArray,
       $RESTPCName, $RESTProcId, $RESTExecutiveName, $RESTBranchId;
global $mySalesRep, $paymentStatus, $pageNumber,
       $noOfRecordsPerPage, $PCName, $PCID, $allowCFPBAuditing;
global $branchName, $executiveId, $branchName, $userGroup, $brokerName,
       $secondaryBrokerName, $secondaryBrokerNumber;
global $employeeId, $employeeName, $lenderName, $qryLen, $billingDueDate,
       $myStaleDay;
global $brokerNumb, $notesType, $priorityLevel, $activeFile,
       $loanAuditStatus;
global $CFPBAuditStatus, $startDate, $endDate, $modifiedStartDate, $modifiedEndDate,
       $closingStartDate, $closingEndDate, $leadSource, $workflowInfo;
global $WFID, $workflowSetpsInfo, $WFSID, $stateArray, $propertyStateArray,
       $PCModuleInfoKeys, $PCModuleInfo, $searchModuleCodeArray, $loanProgramInfo, $servicingStatusCodeArray;
global $PCClientTypeInfoArray, $LMRClientTypeArray, $PCSubStatusInfo, $statusOptArray,
       $WFStatus, $PCStatusInfoArray, $PCStatusInfo;
global $fileStatusArray, $lenderListArray, $lenderNameArray, $workflowSetps,
       $WFStepIDsArray;
global $openHouseDate, $allowExcelDownload, $allCount, $exportIp, $userSeeBilling;
global $maturityStartDate, $maturityEndDate, $multipleModuleCode, $LMRInternalClientType, $WFSNotCompletedId;
global $appraisalStartDate, $appraisalEndDate;
global $receivedStartDate, $receivedEndDate, $externalBroker;
global $LMRDataCnt;
global $disclosureSentDateStart, $disclosureSentDateEnd, $paymentBased;

use models\composite\oBranch\getBranches;
use models\composite\oBroker\listAllAgents;
use models\composite\oEmployee\getPCEmployeeList;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glServicingSubStatus;
use models\Controllers\backoffice\myPipeline;
use models\Controllers\backoffice\myPipeline\myPipelineSearch;
use models\composite\oContacts\contactsList;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;
use models\standard\UserAccess;

$PCStatusArray = [];
$selectedBranchesInfo = [];
$selectedBrokersInfo = [];
$selectedLoanOfficerInfo = [];

if (myPipelineSearch::$branchList) {
    $selectedBranchesTemp = getBranches::getReport([
        'execID' => myPipelineSearch::$branchList,
    ]);
    $selectedBranchesInfo = $selectedBranchesTemp['branchList'];
}
if (myPipelineSearch::$brokerList) {
    $selectedBrokersInfo = listAllAgents::getReport([
        'agentId' => myPipelineSearch::$brokerList,
    ]);
}

if (myPipelineSearch::$contactsList) {

    $selectedContacsInfo = contactsList::getReport([
        'CID' => myPipelineSearch::$contactsList,
    ]);
}
if (myPipelineSearch::$loanOfficerList) {
    $selectedLoanOfficerInfo = listAllAgents::getReport([
        'agentId' => myPipelineSearch::$loanOfficerList,
    ]);
}
$selectedEmployeeList = [];
if (myPipelineSearch::$employeeList) {
    $selectedEmployeeList = getPCEmployeeList::getReport([
        'empId' => myPipelineSearch::$employeeList,
    ]);
}


if ($userRole == 'Super') {
    if ($PCName != '' && $PCID > 0) { ?>
        <input type="hidden" id="searchPCID" name="searchPCID" value="<?php echo $PCID ?>">
    <?php } else { ?>
        <input type="hidden" id="searchPCID" name="searchPCID" value="0">
    <?php }
} else { ?>
    <input type="hidden" id="searchPCID" name="searchPCID" value="<?php echo $PCID ?>">
<?php } ?>

<div class="row mb-2">
    <?php if ($userRole == 'REST' || $fileType == 2 || $fileType == 4) { ?>
        <div class="col-lg-3 mb-2">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold mb-0">Select Submission Date:</label>
                <div class="input-daterange input-group">
                    <input type="text" name="subStDate" id="subStDate"
                           class="form-control form-controller-solid  dateNewClass" value="<?php echo $subStDate; ?>"
                           size="13" maxlength="10" placeholder="From Date"><label for="subStDate"></label>
                    <div class="input-group-append">
                            <span class="input-group-text">
                                <i class="la la-ellipsis-h"></i>
                            </span>
                    </div>
                    <input type="text" name="subEndDate" id="subEndDate"
                           class="form-control form-controller-solid  dateNewClass" value="<?php echo $subEndDate ?>"
                           size="13" maxlength="10" placeholder="To Date"><label for="subEndDate"></label>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-2">
            <label class="text-dark-50 font-weight-bold  mb-0" for="searchText">Search Text</label>
            <div class="input-group">
                <input type="text" name="searchTerm" id="searchTerm" autocomplete="off"
                       class="form-control form-controller-solid" value="<?php echo $searchTerm; ?>"
                       placeholder="Type text to search" onchange="changeFieldColor('searchField', this);">
            </div>
        </div>
        <div class="col-lg-3 mb-2">
            <label class="text-dark-50 font-weight-bold mb-0">Select Search Field</label>
            <select name="searchField[]" id="searchField" multiple="" class="chzn-select">
                <?php
                $searchKeyFields = array_keys(myPipeline::$searchFields);
                foreach ($searchKeyFields as $s => $searchKeyField) {
                    $sOpt = Arrays::isSelectedArray($searchFieldArray, $searchKeyField);
                    echo "<option value=\"" . $searchKeyField . "\" " . $sOpt . '>' . myPipeline::$searchFields[$searchKeyField] . '</option>';
                }
                ?>
            </select>
        </div>
        <?php if ($userRole == 'REST' || $userRole == 'Super') { ?>
            <div class="col-lg-3 mb-lg-0 mb-6">
                <label class="text-dark-50 font-weight-bold  mb-0" for="RESTPCName">Type PC To Search</label>
                <input type="text" name="RESTPCName" id="RESTPCName" value="<?php echo $RESTPCName ?>" size="29"
                       placeholder="Type PC name to search" autocomplete="off"
                       class="form-control form-controller-solid  datatable-input"
                       onblur="if(this.value === '') { document.LMRReport.RESTProcId.value = 0; }"/>
                <input type="hidden" id="RESTProcId" name="RESTProcId" value="<?php echo $RESTProcId ?>" size="10"/>
            </div>
            <div class="col-lg-3 mb-lg-0 mb-6">
                <label class="text-dark-50 font-weight-bold  mb-0" for="RESTExecutiveName">Type Branch Name To
                    Search</label>
                <input type="text" name="RESTExecutiveName" id="RESTExecutiveName"
                       value="<?php echo $RESTExecutiveName ?>" size="29" autocomplete="off"
                       placeholder="Type Branch name to search"
                       class="form-control form-controller-solid  datatable-input"
                       onblur="if(this.value === '') { document.LMRReport.RESTBranchId.value = '3178f12db7e77c19'; }"/>
                <input type="hidden" id="RESTBranchId" name="RESTBranchId"
                       value="<?php echo cypher::myEncryption($RESTBranchId) ?>"/>
            </div>
            <?php if ($fileType != 4) { ?>
                <div class="col-lg-3 mb-lg-0 mb-6">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="mySalesRep">All Rep</label>
                    <select name="mySalesRep" id="mySalesRep"
                            class="choice form-control form-controller-solid  datatable-input">
                        <option value=""> - All Rep -</option>
                        <?php foreach (myPipeline::$glSalesRepresentativeArray as $salesRepKey => $salesRep) { ?>
                            <option value="<?php echo $salesRepKey ?>" <?php echo Arrays::isSelected($salesRepKey, $mySalesRep); ?>><?php echo $salesRep ?></option>
                        <?php } ?>
                    </select>
                </div>
            <?php } ?>
            <div class="col-lg-3 mb-lg-0 mb-6">
                <label class="text-dark-50 font-weight-bold  mb-0" id="paymentStatus">All/Payment Status</label>
                <select name="paymentStatus" id="paymentStatus"
                        class="choice form-control form-controller-solid  datatable-input">
                    <option value=""> All / Payment Status</option>
                    <option value="Payment Due" <?php echo Arrays::isSelected($paymentStatus, 'Payment Due') ?>> Payment
                        Due
                    </option>
                    <option value="Paid" <?php echo Arrays::isSelected($paymentStatus, 'Paid') ?>> Paid</option>
                    <option value="Delinquent" <?php echo Arrays::isSelected($paymentStatus, 'Delinquent') ?>>
                        Delinquent
                    </option>
                </select>
            </div>
        <?php } ?>
        <div class="col-lg-3 mb-lg-0 mb-6">
            <label class="text-dark-50 font-weight-bold  mb-0" for="noOfRecordsPerPage">No Of Records Per Page</label>
            <select name="noOfRecordsPerPage" id="noOfRecordsPerPage" class="form-control"
                    onchange="resetPageNum('<?php echo $pageNumber ?>')">
                <option value="25" <?php echo Arrays::isSelected($noOfRecordsPerPage, '25') ?>>Show 25 per page</option>
                <option value="50" <?php echo Arrays::isSelected($noOfRecordsPerPage, '50') ?>>Show 50 per page</option>
                <option value="75" <?php echo Arrays::isSelected($noOfRecordsPerPage, '75') ?>>Show 75 per page</option>
                <option value="100" <?php echo Arrays::isSelected($noOfRecordsPerPage, '100') ?>>Show 100 per page
                </option>
            </select>
        </div>
    <?php }


    if ($fileType != 2 && $fileType != 4 && $userRole != 'REST') {
    if ($userRole == 'Super' || $userRole == 'Auditor' || $userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($fileType == 'CFPB' && $allowCFPBAuditing == 1)) {
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6 listAllPC_Filter <?php echo UserAccess::userSearchFieldClass('listAllPC_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
            <label class="text-dark-50 font-weight-bold  mb-0" for="PCName">List all
                PCs</label>
            <div class="input-group">
                <input type="text" name="PCName" id="PCName"
                       class="form-control form-controller-solid  datatable-input"
                       value="<?php echo Strings::stripQuote($PCName) ?>" size="23"
                       placeholder="Type PC Name To Search"
                       autocomplete="off"
                       onblur="if(this.value === '') { document.LMRReport.searchPCID.value = 0; }"/>
                <div class="input-group-append"><span class="input-group-text"><a
                                href="javascript:listAllPCs();"><i
                                    class="flaticon-refresh text-dark"></i></a></span>
                </div>
            </div> <!-- processing company Div -->
        </div>
    <?php }
    if ($userRole == 'Branch') {
        if (in_array($PCID, myPipeline::$agentPCException)) {
            ?>
            <div class="col-lg-3 mb-lg-0 mb-6 listAllBranch_Filter <?php echo UserAccess::userSearchFieldClass('listAllBranch_Filter', myPipeline::$userSearchFieldsPreference); ?> ">
                <label class="text-dark-50 font-weight-bold  mb-0" for="branchName">List all
                    Branches</label>
                <div class="input-group">
                    <input type="text" name="branchName" id="branchName"
                           class="form-control form-controller-solid  datatable-input"
                           value="<?php echo Strings::stripQuote($branchName) ?>"
                           size="25" placeholder="Type Branch Name To Search" autocomplete="off"
                           onblur="if(this.value === '') { document.LMRReport.eId.value = '3178f12db7e77c19'; }"/>
                    <input type="hidden" id="eId" name="eId"
                           value="<?php echo cypher::myEncryption($executiveId) ?>">
                    <div class="input-group-append"><span class="input-group-text"><a
                                    href="javascript:listAllBranches();"><i
                                        class="flaticon-refresh text-dark"></i></a></span>
                    </div>
                </div>
            </div>
            <?php
        } else {
            ?>
            <input type="hidden" id="eId" name="eId" value="<?php echo cypher::myEncryption($executiveId) ?>">
            <?php
        }
    } else {
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6 listAllBranch_Filter   <?php echo UserAccess::userSearchFieldClass('listAllBranch_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="eId">List all Branches</label>
                <select class="form-control " id="eId" name="eId[]" multiple="multiple">
                    <?php foreach ($selectedBranchesInfo as $eachBranchInfo) { ?>
                        <option value="<?php echo cypher::myEncryption($eachBranchInfo['executiveId']); ?>"
                                selected><?php echo $eachBranchInfo['LMRExecutive'] . ' - ' . $eachBranchInfo['executiveEmail']; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php }


    if ($userGroup == 'Super'
        || $userGroup == 'Branch'
        || $userGroup == 'Employee'
        || ($userGroup == 'Agent'
            && (in_array($PCID, myPipeline::$PCException)
                || in_array($PCID, myPipeline::$agentPCException)
                || $externalBroker > 0
            ))) {
        /** Only First American mitigators Agents to see all files under Lead status  **/
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6 listAllBroker_Filter <?php echo UserAccess::userSearchFieldClass('listAllBroker_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="broker">List all Brokers</label>
                <select class="form-control " id="broker" name="broker[]" multiple="multiple">
                    <?php foreach ($selectedBrokersInfo as $eachBrokerInfo) { ?>
                        <option value="<?php echo($eachBrokerInfo['brokerNumber']); ?>"
                                selected><?php echo $eachBrokerInfo['bName'] . ' ' . $eachBrokerInfo['bLName'] . ' - ' . $eachBrokerInfo['bEmail']; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>

        <?php if (!$externalBroker) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 listAllLoanOfficer_Filter <?php echo UserAccess::userSearchFieldClass('listAllLoanOfficer_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="loanOfficerSearch">List all Loan
                        Officers</label>
                    <select class="form-control " id="loanOfficerSearch" name="loanOfficerSearch[]"
                            multiple="multiple">
                        <?php foreach ($selectedLoanOfficerInfo as $eachLoanOfficer) { ?>
                            <option value="<?php echo($eachLoanOfficer['brokerNumber']); ?>"
                                    selected><?php echo $eachLoanOfficer['bName'] . ' ' . $eachLoanOfficer['bLName'] . ' - ' . $eachLoanOfficer['bEmail']; ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php } ?>

    <?php }
    if ($userRole != 'Branch' && $userRole != 'Agent') {
        if ($PCID == 1545 && PageVariables::$userNumber == 7910) { /*  hide the advanced search >> employee filter for PC = Synergy Attorney Services, LLC (M. Pillar Gracia) on May 17, 2016  */
            ?>
            <input type="hidden" id="employeeId" name="employeeId" value="<?php echo $employeeId ?>">
        <?php } else {
            ?>
            <div class="col-lg-3 mb-lg-0 mb-6 listAllEmployees_Filter <?php echo UserAccess::userSearchFieldClass('listAllEmployees_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="employeeId">List all Employees</label>
                    <select class="form-control " id="employeeId" name="employeeId[]" multiple="multiple">
                        <?php foreach ($selectedEmployeeList as $eachEmployee) { ?>
                            <option value="<?php echo($eachEmployee['AID']); ?>"
                                    selected><?php echo $eachEmployee['processorName'] . '(' . $eachEmployee['role'] . ')'; ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>


            <!-- Employee Div -->
        <?php }
    } ?>

    <div class="col-lg-3 mb-lg-0  clientBilling_Filter <?php echo UserAccess::userSearchFieldClass('clientBilling_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="billingDueDate">Client Billing</label>
            <select name="billingDueDate" class="form-control form-controller-solid  chzn-select "
                    data-placeholder="Select Client Billing"
                    id="billingDueDate">
                <option value=""></option>
                <option value="pastDue" <?php echo Arrays::isSelected('pastDue', $billingDueDate) ?>>Past Due
                </option>
                <option value="thisWeek" <?php echo Arrays::isSelected('thisWeek', $billingDueDate) ?>>Due this week
                </option>
                <option value="twoWeek" <?php echo Arrays::isSelected('twoWeek', $billingDueDate) ?>>Due in 2 weeks
                </option>
                <option value="threeWeek" <?php echo Arrays::isSelected('threeWeek', $billingDueDate) ?>>Due in 3
                    weeks
                </option>
            </select>
        </div>
    </div> <!-- Billing Div -->

    <div class="col-lg-3 mb-lg-0 mb-6 fileIdle_Filter <?php echo UserAccess::userSearchFieldClass('fileIdle_Filter', myPipeline:: $userSearchFieldsPreference ?? []); ?> ">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="staleDay">File Idle</label>
            <select name="staleDay" id="staleDay" class="form-control form-controller-solid  chzn-select"
                    data-placeholder="Select File Idle">
                <option value=""></option>
                <?php
                foreach (myPipeline::$glStaleFileArray as $sd => $staleDay) {
                    $selOpt = '';
                    if ($myStaleDay == $staleDay) $selOpt = 'selected';
                    ?>
                    <option value="<?php echo $staleDay ?>" <?php echo $selOpt ?>>> <?php echo $staleDay ?>Days
                    </option>
                    <?php
                }
                ?>
            </select>
        </div>
    </div>
    <!-- File idle Div -->

    <div class="col-lg-3 mb-lg-0 mb-6 notesType_Filter <?php echo UserAccess::userSearchFieldClass('notesType_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="notesType">Notes Type</label>
            <select name="notesType" id="notesType"
                    class="form-control form-controller-solid  datatable-input chzn-select"
                    data-placeholder="Notes Type">
                <option value=""></option>
                <?php foreach (myPipeline::$glNotesTypeArray as $key => $noteType) { ?>
                    <option value="<?php echo trim($key) ?>" <?php echo Arrays::isSelected(trim($key), $notesType) ?>><?php echo trim($noteType) ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <!-- notes type Div-->

    <div class="col-lg-3 mb-lg-0 mb-6 priorityLevel_Filter <?php echo UserAccess::userSearchFieldClass('priorityLevel_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="priorityLevel">Priority</label>
            <select name="priorityLevel" id="priorityLevel"
                    class="form-control form-controller-solid chzn-select " data-placeholder="Select Priority">
                <option value=""></option>
                <?php foreach (myPipeline::$priorityLevelArray as $pl => $tempPriority) { ?>
                    <option value="<?php echo $tempPriority ?>" <?php echo Arrays::isSelected($tempPriority, $priorityLevel) ?>><?php echo $tempPriority ?></option>
                <?php } ?>
            </select>
        </div>
    </div> <!-- Priority Div-->

    <?php if ($userRole == 'Super' || $userRole == 'Branch' || $userRole == 'Agent' || $userRole == 'Manager' || $userRole == 'Auditor' || $userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager') { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 activeDeactiveFile_Filter <?php echo UserAccess::userSearchFieldClass('activeDeactiveFile_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
            <label class="text-dark-50 font-weight-bold  mb-0" for="activeFile">Active/Inactive Files</label>
            <div class="form-group">
                <select name="activeFile" class="form-control form-controller-solid  chzn-select"
                        data-placeholder="Select Active/Inactive Files"
                        id="activeFile">
                    <option value="1" <?php echo Arrays::isSelected('1', $activeFile); ?>><?php if ($userRole == 'Auditor' || $userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager') { ?>Submitted<?php } else { ?>Active<?php } ?>
                        Files
                    </option>
                    <option value="0" <?php echo Arrays::isSelected('0', $activeFile); ?>>Deactivated Files</option>
                </select>
            </div>
        </div>
        <?php
    } else {
        ?>
        <input type="hidden" name="activeFile" id="activeFile" value="1">
        <?php
    }
    ?>
    <div class="col-lg-3 mb-lg-0 mb-6 noActiveDeactive_Filter <?php echo UserAccess::userSearchFieldClass('noActiveDeactive_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="noOfRecordsPerPage">Num Of Records Per
                Page</label>
            <select name="noOfRecordsPerPage" class="form-control form-controller-solid  chzn-select"
                    data-placeholder="Select Num Of Records Per Page" id="noOfRecordsPerPage"
                    onchange="resetPageNum('<?php echo $pageNumber ?>')">
                <option value="25" <?php echo Arrays::isSelected($noOfRecordsPerPage, '25') ?>>Show 25 per page
                </option>
                <option value="50" <?php echo Arrays::isSelected($noOfRecordsPerPage, '50') ?>>Show 50 per page
                </option>
                <option value="75" <?php echo Arrays::isSelected($noOfRecordsPerPage, '75') ?>>Show 75 per page
                </option>
                <option value="100" <?php echo Arrays::isSelected($noOfRecordsPerPage, '100') ?>>Show 100 per page
                </option>
            </select>
        </div>
    </div> <!-- file create Div-->
    <?php
    if ($userGroup == 'Auditor' || $userGroup == 'CFPB Auditor' || $fileType == 'CFPB' || $userGroup == 'Auditor Manager') {
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6 loanAuditStatus_Filter <?php echo UserAccess::userSearchFieldClass('loanAuditStatus_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="loanAuditStatus">Loan Audit Status</label>
                <?php
                if ($userGroup == 'Auditor') { ?>
                    <select name="loanAuditStatus" id="loanAuditStatus"
                            class="form-control form-controller-solid  datatable-input"
                            class="choice">
                        <option value="All"> - All File Loan Audit Status -</option>
                        <?php for ($ggl = 0; $ggl < count(myPipeline::$loanAuditStatusArray); $ggl++) { ?>
                            <option value="<?php echo trim(myPipeline::$loanAuditStatusArray[$ggl]) ?>" <?php echo Arrays::isSelected($loanAuditStatus, trim(myPipeline::$loanAuditStatusArray[$ggl])) ?>><?php echo trim(myPipeline::$loanAuditStatusArray[$ggl]) ?></option>
                            <?php
                        }
                        ?>
                    </select>
                    <?php
                } else if ($userGroup == 'CFPB Auditor' || $fileType == 'CFPB' || $userGroup == 'Auditor Manager') {
                    ?>
                    <select name="CFPBAuditStatus" id="loanAuditStatus"
                            class="form-control form-controller-solid  datatable-input"
                            class="choice">
                        <option value="All"> - All File CFPB Audit Status -</option>
                        <?php
                        for ($ggl = 0; $ggl < count(myPipeline::$CFPBAuditStatusArray); $ggl++) {
                            ?>
                            <option value="<?php echo trim(myPipeline::$CFPBAuditStatusArray[$ggl]) ?>" <?php echo Arrays::isSelected($CFPBAuditStatus, trim(myPipeline::$CFPBAuditStatusArray[$ggl])) ?>><?php echo trim(myPipeline::$CFPBAuditStatusArray[$ggl]) ?></option>
                            <?php
                        }
                        ?>
                    </select>
                    <?php
                }
                ?>
            </div>
        </div>
        <?php
    }
    ?>


    <div class="col-lg-3 mb-lg-0 mb-2 leadSource_Filter <?php echo UserAccess::userSearchFieldClass('leadSource_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <label class="text-dark-50 font-weight-bold  mb-0" for="leadSource">List
            all Lead Source</label>
        <div class="input-group">
            <input type="text" name="leadSource" class="form-control form-controller-solid " id="leadSource"
                   value="<?php echo Strings::stripQuote($leadSource) ?>"
                   size="23" autocomplete="off" placeholder="Type lead source to search"/>
            <div class="input-group-append"><span class="input-group-text"><a
                            href="javascript:listAllLeadSources(this.value);"><i
                                class="flaticon-refresh text-dark"></i></a></span></div>
        </div>
    </div> <!-- Lead Source Div -->


    <div class="col-lg-3 mb-lg-0 mb-6 referringParty_Filter <?php echo UserAccess::userSearchFieldClass('referringParty_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <!-- Broker/Referring Party Start. -->
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0 " for="referringParty">Referring Party</label>
            <input type="text"
                   name="referringParty"
                   class="form-control form-controller-solid  "
                   id="referringParty"
                   value="<?php echo Arrays::getArrayValue('referringParty', $_REQUEST); ?>" size="23"
                   autocomplete="off"
                   placeholder="Type broker/referring party."/>
        </div> <!-- Broker/Referring Party End. -->
    </div>


    <div class="col-lg-3 mb-lg-0 mb-6 workflow_Filter <?php echo UserAccess::userSearchFieldClass('workflow_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0"
                   for="WFID">Select Workflows <img class="WFStepsLoaderDiv hide" src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif" alt="">
            </label>
            <select name="WFID[]"
                    id="WFID"
                    class="form-control chzn-selectShowSelectAll"
                    data-live-search="true"
                    data-actions-box="true"
                    title="Select Workflows"
                    data-live-search-placeholder="Search Workflows"
                    multiple
                    onchange="getWorkflowStepsForPipeline(this.value, '<?php echo $PCID ?>')">
                <?php
                foreach ($workflowInfo as $workflow) { ?>
                    <option value="<?php echo trim($workflow['WFID']); ?>"
                        <?php echo Arrays::isSelectedArray($WFID, trim($workflow['WFID'])) ?>><?php echo trim($workflow['WFName']) ?></option>
                    <?php
                }
                ?>
            </select>
        </div>
    </div>


    <div class="col-lg-3 mb-lg-0 mb-6 workflowStep_Filter <?php echo UserAccess::userSearchFieldClass('workflowStep_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> "
         onclick="validateWorkflowForPipeline()">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="WFSId">Select Completed Workflow Steps</label>
            <div class="WFStepsDiv">
            </div>
            <select name="WFSId[]"
                    id="WFSId"
                    class="form-control chzn-selectShowSelectAll"
                    data-live-search="true"
                    data-actions-box="true"
                    title="Select Completed Workflow Steps"
                    data-live-search-placeholder="Search Completed Workflow Steps"
                    multiple>
                <?php
                $workflowInfoById = Arrays::buildKeyByValue($workflowInfo, 'WFID');
                foreach ($workflowSetpsInfo as $eachWorkflowId => $workflowStepsArray) { ?>
                    <optgroup label="<?php echo $workflowInfoById[$eachWorkflowId][0]['WFName']; ?>">
                        <?php foreach ($workflowStepsArray as $eachWorkFlowStep) {
                            if ($eachWorkFlowStep['LMRID'] == 0) { ?>
                                <option value="<?php echo trim($eachWorkFlowStep['WFSID']) ?>"
                                        data-html="true"
                                        data-content="<?php echo trim($eachWorkFlowStep['steps']) ?>"
                                    <?php echo Arrays::isSelectedArray($WFSID, trim($eachWorkFlowStep['WFSID'])) ?>><?php echo trim($eachWorkFlowStep['steps']) ?></option>
                            <?php }
                        } ?>
                    </optgroup>
                <?php } ?>
            </select>
        </div>
    </div>

    <div class="col-lg-3 mb-lg-0 mb-6 workflowStep_Filter <?php echo UserAccess::userSearchFieldClass('workflowStep_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> "
         onclick="validateWorkflowForPipeline()">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="WFSNotCompletedId">Select Not Completed Workflow
                Steps </label>
            <select
            <select name="WFSNotCompletedId[]"
                    id="WFSNotCompletedId"
                    class="form-control chzn-selectShowSelectAll"
                    data-live-search="true"
                    data-actions-box="true"
                    title="Select Not Completed Workflow Steps"
                    data-live-search-placeholder="Search Not Completed Workflow Steps"
                    multiple>
                <?php foreach ($workflowSetpsInfo as $eachWorkflowId => $workflowStepsArray) { ?>
                    <optgroup label="<?php echo $workflowInfoById[$eachWorkflowId][0]['WFName']; ?>">
                        <?php foreach ($workflowStepsArray as $eachWorkFlowStep) { ?>
                            <option value="<?php echo trim($eachWorkFlowStep['WFSID']) ?>" <?php echo Arrays::isSelectedArray($WFSNotCompletedId, trim($eachWorkFlowStep['WFSID'])) ?>><?php echo trim($eachWorkFlowStep['steps']) ?></option>
                        <?php } ?>
                    </optgroup>
                <?php } ?>
            </select>
        </div>
    </div>


    <div class="col-lg-3 mb-lg-0 mb-6 propertyState propertyState_Filter  <?php echo UserAccess::userSearchFieldClass('propertyState_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="propertyState">Property State</label>
            <select data-placeholder=" - Select Property State - " name="propertyState[]" id="propertyState"
                    class="chzn-select form-control form-controller-solid " multiple="">
                <?php
                for ($s = 0; $s < count($stateArray); $s++) {
                    $sOpt = '';
                    $sOpt = Arrays::isSelectedArray($propertyStateArray, $stateArray[$s]['stateCode']);
                    echo "<option value=\"" . $stateArray[$s]['stateCode'] . "\" " . $sOpt . '>' . $stateArray[$s]['stateName'] . '</option>';
                }
                ?>
            </select>
        </div>
    </div> <!-- state to search Div -->

    <div class="col-lg-3 mb-lg-0 mb-6 fileType_Filter <?php echo UserAccess::userSearchFieldClass('fileType_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="multipleModuleCode">Select File Types</label>
            <select data-placeholder="All File Types" name="multipleModuleCode[]" id="multipleModuleCode"
                    class="chzn-select  form-control form-controller-solid  choice_b" multiple=""
                <?php if ($userGroup == 'Super') { ?> onchange="getLoanPrograms();" <?php } ?> >
                <?php
                $PCModuleInfoKeys = [];
                $PCModuleInfoKeys = array_keys($PCModuleInfo);
                for ($k = 0; $k < count($PCModuleInfoKeys ?? []); $k++) {
                    $PCModuleCode = '';
                    $PCModuleName = '';
                    $tempArray = [];
                    $PCModuleCode = trim($PCModuleInfoKeys[$k]);
                    if (isset($PCModuleInfo[$PCModuleCode]['moduleName'])) {
                        $PCModuleName = trim($PCModuleInfo[$PCModuleCode]['moduleName']);
                    } else { /* Super user module type.. */
                        $PCModuleName = trim($PCModuleInfo[$PCModuleCode]);
                    }
                    ?>
                    <option value="<?php echo trim($PCModuleCode) ?>" <?php echo Arrays::isSelectedArray(myPipeline::$searchModuleCodeArray, $PCModuleCode) ?>><?php echo trim($PCModuleName); ?></option>
                    <?php
                }
                ?>
            </select>
        </div>
    </div>

    <?php
    $serviceTypeLabel = '- Select Service Type -';
    if (in_array('HMLO', $PCModuleInfoKeys ?? [])) {
        $serviceTypeLabel = '- Select Loan Programs -';
    }
    ?>
    <div class="col-lg-3 mb-lg-0 mb-6  LMRClientType LMRClientType_Filter <?php echo UserAccess::userSearchFieldClass('LMRClientType_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="LMRClientType">Select Loan Programs </label>
            <select data-placeholder="<?php echo $serviceTypeLabel ?>" name="LMRClientType[]" id="LMRClientType"
                    class="chzn-select form-control form-controller-solid " multiple="">
                <?php
                if ($userRole == 'Super' && $PCID == 0) {
                    for ($lp = 0; $lp < count($loanProgramInfo); $lp++) {
                        $sel = '';
                        $LMRClientType = $_REQUEST['LMRClientType'] ?? [];
                        if (in_array($loanProgramInfo[$lp]['serviceType'], $LMRClientType)) {
                            $sel = ' selected ';
                        }
                        echo "<option value=\"" . $loanProgramInfo[$lp]['serviceType'] . "\" $sel>" . $loanProgramInfo[$lp]['serviceType'] . '</option>';
                    }

                } else {
                $tempLMRClientModuleCode = null;
                foreach ($PCClientTypeInfoArray

                as $lc => $PCClientType) {
                $LMRClientCode = trim($PCClientType['LMRClientType']);
                $LMRClientTypeVal = trim($PCClientType['serviceType']);
                $LMRClientModuleCode = trim($PCClientType['moduleCode']);
                if ($tempLMRClientModuleCode != $LMRClientModuleCode) {
                if ($tempLMRClientModuleCode) {
                    echo '</optgroup>';
                }
                ?>
                <optgroup label="<?php echo trim($PCClientType['moduleName']) ?>">
                    <?php } ?>
                    <option value="<?php echo $LMRClientCode ?>" <?php echo Arrays::isSelectedArray($LMRClientTypeArray, $LMRClientCode) ?>><?php echo $LMRClientTypeVal ?></option>
                    <?php
                    $tempLMRClientModuleCode = $LMRClientModuleCode;
                    }
                    }

                    ?>
                </optgroup>
            </select>
        </div>
    </div>
    <?php
    if ($userGroup == 'Employee' || $userGroup == 'Super'
        || ($userGroup == 'Agent' && PageVariables::$allowToAccessInternalLoanProgram && PageVariables::$externalBroker )) {
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6  LMRInternalClientType LMRInternalClientType_Filter <?php echo UserAccess::userSearchFieldClass('LMRInternalClientType_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="LMRInternalClientType">Select Internal Loan
                    Programs </label>
                <select data-placeholder="Select Internal Loan Programs" name="LMRInternalClientType[]"
                        id="LMRInternalClientType"
                        class="chzn-select form-control form-controller-solid " multiple="">
                    <?php
                    foreach (myPipeline::$PCInternalServicesModule as $eachModule => $eachModuleInternalLoanArray) { ?>
                        <optgroup label="<?php echo trim($eachModule) ?>">
                            <?php foreach ($eachModuleInternalLoanArray as $eachInternalLoan) { ?>
                                <option value="<?php echo $eachInternalLoan['STCode']; ?>" <?php if (in_array($eachInternalLoan['STCode'], $LMRInternalClientType)) {
                                    echo ' selected ';
                                } ?>><?php echo $eachInternalLoan['serviceType']; ?></option>
                            <?php } ?>
                        </optgroup>
                    <?php }
                    ?>
                </select>
            </div>
        </div>
    <?php } ?>


    <div class="col-lg-3 mb-lg-0 mb-6  statusOpt clientSubstatus_Filter <?php echo UserAccess::userSearchFieldClass('clientSubstatus_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="statusOpt">Client File Substatus</label>
            <select data-placeholder=" - Select Client File Substatus - " name="statusOpt[]" id="statusOpt"
                    class="chzn-select form-control form-controller-solid " multiple="">
                <?php
                $tempModulesCodeArr = [];
                for ($j = 0; $j < count($PCSubStatusInfo); $j++) {
                    $moduleCode = $PCSubStatusInfo[$j]['moduleName'];
                    $category = $PCSubStatusInfo[$j]['category'];
                    $tempModulesCodeArr[$moduleCode][$category][] = $PCSubStatusInfo[$j];
                }

                $moduleKeys = [];
                $moduleKeys = array_keys($tempModulesCodeArr);
                for ($j = 0; $j < count($moduleKeys); $j++) {
                    $categoryKeys = [];

                    $moduleCode = $moduleKeys[$j];
                    $categoryArr = $tempModulesCodeArr[$moduleCode];
                    $categoryKeys = array_keys($categoryArr);
                    ?>
                    <option value="" class="optnGrp" disabled><?php echo $moduleCode ?></option>
                    <?php
                    for ($k = 0; $k < count($categoryKeys); $k++) {
                        $category = '';
                        $substatusArr = [];
                        $category = $categoryKeys[$k];
                        $substatusArr = $tempModulesCodeArr[$moduleCode][$category];
                        ?>
                        <optgroup label="<?php echo $category ?>">
                            <?php

                            for ($l = 0; $l < count($substatusArr); $l++) {
                                $PFSID = '';
                                $sOpt = '';
                                $substatus = '';
                                $PFSID = trim($substatusArr[$l]['PFSID']);
                                $substatus = trim($substatusArr[$l]['substatus']);
                                ?>
                                <option value="<?php echo $PFSID ?>" <?php echo Arrays::isSelectedArray($statusOptArray, $PFSID) ?>><?php echo $substatus ?></option>
                                <?php
                            }
                            ?>
                        </optgroup>
                        <?php
                    }
                }
                ?>
            </select>
        </div>
    </div>
</div> <!-- end of row -->

    <div class="row mb-2">

        <div class="col-lg-3 mb-lg-0 mb-6 searchTerm">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="searchTerm">Search Text</label>
                <input type="text" name="searchTerm" id="searchTerm"
                       value="<?php echo Strings::stripQuote($searchTerm) ?>"
                       class="search form-control form-controller-solid " size="40"
                       placeholder="Type text to search & select field" autocomplete="off"
                       onchange="myPipeline.changeFieldColor('searchField', this);"/>
            </div>
        </div>
        <div class="col-lg-3 mb-lg-0 mb-6 searchField">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="searchField">Select Field To Search</label>
                <select data-placeholder=" - Select Field to search - " name="searchField[]" id="searchField"
                        class="chzn-select form-control form-controller-solid " <?php if (glCustomJobForProcessingCompany::canSearchMultipleFields($PCID)) {
                    echo 'multiple';
                } ?>
                        onchange="myPipeline.changeFieldColor('searchTerm', this);">
                    <?php if (!glCustomJobForProcessingCompany::canSearchMultipleFields($PCID)) {
                        echo '<option value="">Select One...</option>';
                    } ?>
                    <?php
                    $searchKeyFields = array_keys(myPipeline::$searchFields);
                    foreach ($searchKeyFields as $s => $field) {
                        $sOpt = Arrays::isSelectedArray($searchFieldArray, $field);
                        echo "<option value=\"" . $field . "\" " . $sOpt . '>' . myPipeline::$searchFields[$field] . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div>
        <?php
        if ($userGroup == 'Super' || ($userGroup != 'Auditor' && $userGroup != 'CFPB Auditor' && $fileType != 'CFPB' && $userGroup != 'Auditor Manager')) {
            if (count($PCStatusInfoArray) > 0) {
                $PCStatusArray = $PCStatusInfoArray;
            } else {
                $PCStatusArray = $PCStatusInfo;
            }

            ?>
            <div class="col-lg-3 mb-lg-0 mb-6 multiplePrimaryStatus multiplePrimaryStatus_Filter <?php echo UserAccess::userSearchFieldClass('multiplePrimaryStatus_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="multiplePrimaryStatus">Primary
                        Status</label>
                    <?php
                    $tempModulesCodeArr = [];
                    foreach ($PCStatusArray as $j => $item) {
                        $tempModulesCodeArr[$item['moduleName']][] = $item;
                    }
                    $moduleKeys = array_keys($tempModulesCodeArr);
                    ?>
                    <select data-placeholder=" - Select file primary status - " name="multiplePrimaryStatus[]"
                            id="multiplePrimaryStatus" class="chzn-select form-control form-controller-solid "
                            multiple="">
                        <?php
                        foreach ($moduleKeys as $k => $key) {
                            ?>
                            <optgroup label="<?php echo $key ?>">
                                <?php
                                $tempKeys = $tempModulesCodeArr[$key];
                                foreach ($tempKeys as $plm => $item) {
                                    if ($PCID > 0) {
                                        $pStatus = trim($item['PSID']);
                                    } else {
                                        $pStatus = trim($item['primaryStatus']);
                                    }
                                    ?>
                                    <option <?php if ($PCID > 0) { ?>
                                        value="<?php echo trim($item['PSID']) ?>" <?php } else { ?>
                                        value="<?php echo trim($item['primaryStatus']) ?>" <?php }
                                    echo Arrays::isSelectedArray($fileStatusArray, $pStatus) ?>><?php echo trim($item['primaryStatus']) ?></option>
                                    <?php
                                }
                                ?>
                            </optgroup>
                            <?php
                        }
                        ?>
                    </select>
                </div>
            </div>
            <!-- insert filetypesearch here-->
            <?php
        }
        $PCModuleInfoKeys = [];
        $PCModuleInfoKeys = array_keys($PCModuleInfo);
        if (!in_array('HMLO', $PCModuleInfoKeys)) {
            ?>
            <div class="col-lg-3 mb-lg-0 mb-6 lenderName lenderName_Filter <?php echo UserAccess::userSearchFieldClass('lenderName_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
                <label class="text-dark-50 font-weight-bold  mb-0" for="lenderName">Lender name</label>
                <div class="form-group">
                    <select data-placeholder=" - Type Lender Name To Search - " name="lenderName[]" id="lenderName"
                            class="chzn-select form-control form-controller-solid " multiple="">
                        <?php
                        for ($ggl = 0; $ggl < count($lenderListArray); $ggl++) {
                            ?>
                            <option value="<?php echo trim($lenderListArray[$ggl]['company']) ?>" <?php echo Arrays::isSelectedArray($lenderNameArray, addslashes(trim($lenderListArray[$ggl]['company']))) ?>><?php echo trim($lenderListArray[$ggl]['company']) ?></option>
                            <?php
                        }
                        ?>
                    </select>
                </div>
            </div>

            <?php
        }
        if (in_array($PCID, myPipeline::$accessSecondaryWFPC ?? [])) {
            ?>
            <div class="col-lg-3 mb-lg-0 mb-6 WFStepID WFStepID_Filter <?php echo UserAccess::userSearchFieldClass('WFStepID_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
                <label class="text-dark-50 font-weight-bold  mb-0" for="WFStepID">Current Secondary Status</label>
                <div class="form-group">
                    <select data-placeholder=" - Current Secondary Status - " name="WFStepID[]" id="WFStepID"
                            class="chzn-select form-control form-controller-solid " multiple="">
                        <?php
                        for ($i = 0;
                        $i < count($workflowInfo);
                        $i++) {
                        $workflowID = 0;
                        $tempArray = [];
                        $t = 0;
                        $WFName = '';
                        $workflowID = trim($workflowInfo[$i]['WFID']);
                        $WFName = trim($workflowInfo[$i]['WFName']);
                        if (array_key_exists($workflowID, $workflowSetps)) {
                            $tempArray = $workflowSetps[$workflowID];
                        }
                        for ($j = 0;
                        $j < count($tempArray);
                        $j++) {
                        $steps = '';
                        $stepID = 0;
                        $sOpt = '';
                        $steps = trim($tempArray[$j]['steps']);
                        $stepID = trim($tempArray[$j]['WFSID']);

                        if ($t == 0) {
                        ?>
                        <optgroup label="<?php echo $WFName ?>" style="text-align:center;">
                            <?php
                            }
                            ?>
                            <option value="<?php echo $stepID ?>" <?php echo Arrays::isSelectedArray($WFStepIDsArray, $stepID) ?>><?php echo $steps ?></option>
                            <?php
                            $t++;
                            }
                            }
                            ?>
                    </select>
                </div>
            </div>
            <?php
        }

        if (in_array('HMLO', $PCModuleInfoKeys) || in_array('loc', $PCModuleInfoKeys)) {
            $glServicingSubStatus = glServicingSubStatus::$glServicingSubStatus;
            ?>
            <div class="col-lg-3 mb-lg-0 mb-6 servicingStatus_Filter <?php echo UserAccess::userSearchFieldClass('servicingStatus_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="servicingStatusCode">Select Servicing
                        Status</label>
                    <select data-placeholder="Servicing Status" name="servicingStatusCode[]" id="servicingStatusCode"
                            class="chzn-select  form-control form-controller-solid  choice_b" multiple="">
                        <?php
                        $PCModuleInfoKeys = [];
                        $PCModuleInfoKeys = array_keys($PCModuleInfo);
                        foreach ($glServicingSubStatus as $servicingsubStatusKey => $servicingsubStatusValue) {
                            ?>
                            <option value="<?php echo trim($servicingsubStatusKey) ?>" <?php echo Arrays::isSelectedArray($servicingStatusCodeArray, $servicingsubStatusKey) ?>><?php echo trim($servicingsubStatusValue); ?></option>
                            <?php
                        }
                        ?>
                    </select>
                </div>
            </div>
        <?php } ?>


        <div class="col-lg-3 mb-lg-0 mb-6 paymentBased_Filter <?php echo UserAccess::userSearchFieldClass('paymentBased_Filter', myPipeline:: $userSearchFieldsPreference ?? []); ?> ">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="staleDay">Payment Based</label>
                <select name="paymentBased" id="paymentBased" class="form-control form-controller-solid  chzn-select"
                        data-placeholder="Select Payment Based">
                    <option value=""></option>
                    <?php
                    foreach (myPipeline::$glPaymentBasedArray as $paymentBasedKey => $paymentBasedValue) {
                        $selOpt = '';
                        if ($paymentBased == $paymentBasedKey) $selOpt = 'selected';
                        ?>
                        <option value="<?php echo $paymentBasedKey ?>" <?php echo $selOpt ?>> <?php echo $paymentBasedValue ?>
                        </option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </div>


        <div class="col-lg-3 mb-lg-0 mb-6 contacts_Filter <?php echo UserAccess::userSearchFieldClass('contacts_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="contactSearchId">List all Contacts</label>
                <select class="form-control " id="contactSearchId" name="contactSearchId[]"
                        multiple="multiple">
                    <?php foreach ($selectedContacsInfo as $eachContactInfo) { ?>
                        <option value="<?php echo($eachContactInfo['CID']); ?>"
                                selected><?php echo $eachContactInfo['contactName'] . ' ' . $eachContactInfo['contactLName']; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    </div>
<div class="row">


    <div class="col-lg-3 mb-lg-0 mb-6 fileCreatedDate_Filter <?php echo UserAccess::userSearchFieldClass('fileCreatedDate_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="stDate">File Created</label>
            <div class="input-daterange input-group">
                <input type="text" name="stDate" id="stDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $startDate ?>"
                       size="12"
                       maxlength="10" placeholder="From"/>
                <div class="input-group-append">
															<span class="input-group-text">
																<i class="la la-ellipsis-h"></i>
															</span>
                </div>
                <input type="text" name="endDate" id="endDate"
                       class=" form-control form-controller-solid dateNewClass"
                       value="<?php echo $endDate ?>"
                       size="10"
                       maxlength="10" placeholder="To"/><label for="endDate"></label>
            </div>
        </div>
    </div>

    <div class="col-lg-3 mb-lg-0 mb-6 fileModifiedDate_Filter <?php echo UserAccess::userSearchFieldClass('fileModifiedDate_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="modifiedStartDate">File Modified</label>
            <div class="input-daterange input-group">
                <input type="text" name="modifiedStartDate"
                       class="form-control form-controller-solid  dateNewClass"
                       id="modifiedStartDate"
                       value="<?php echo $modifiedStartDate ?>" size="13" maxlength="10"
                       placeholder="From"/>
                <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                </div>
                <input type="text" name="modifiedEndDate" id="modifiedEndDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $modifiedEndDate ?>" size="10" maxlength="10"
                       placeholder="To"/><label for="modifiedEndDate"></label>
            </div>
        </div>
    </div>

    <div class="col-lg-3 mb-lg-0 mb-6 closingDate_Filter <?php echo UserAccess::userSearchFieldClass('closingDate_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="closingStartDate">Closing Date</label>
            <div class="input-daterange input-group">
                <input type="text" name="closingStartDate" id="closingStartDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $closingStartDate ?>" size="12" maxlength="10"
                       placeholder="From"/>
                <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                </div>
                <input type="text" name="closingEndDate" id="closingEndDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $closingEndDate ?>" size="10" maxlength="10"
                       placeholder="To"/><label for="closingEndDate"></label>
            </div>
        </div>
    </div>

    <div class="col-lg-3 mb-lg-0 mb-6 maturityDate_Filter <?php echo UserAccess::userSearchFieldClass('maturityDate_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="maturityStartDate">Maturity Date</label>
            <div class="input-daterange input-group">
                <input type="text" name="maturityStartDate" id="maturityStartDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $maturityStartDate ?>" size="12" maxlength="10"
                       placeholder="From"/>
                <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                </div>
                <input type="text" name="maturityEndDate" id="maturityEndDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $maturityEndDate ?>" size="10" maxlength="10"
                       placeholder="To"/><label for="maturityEndDate"></label>
            </div>
        </div>
    </div>

    <div class="col-lg-3 mb-lg-0 mb-6 appraisalOrderDate_Filter <?php echo UserAccess::userSearchFieldClass('appraisalOrderDate_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="appraisalStartDate">Appraisal Order Date</label>
            <div class="input-daterange input-group">
                <input type="text" name="appraisalStartDate" id="appraisalStartDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $appraisalStartDate ?>" size="12" maxlength="10"
                       placeholder="From"/>
                <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                </div>
                <input type="text" name="appraisalEndDate" id="appraisalEndDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $appraisalEndDate ?>" size="10" maxlength="10"
                       placeholder="To"/><label for="appraisalEndDate"></label>
            </div>
        </div>
    </div>

    <div class="col-lg-3 mb-lg-0 mb-6 receivedDate_Filter <?php echo UserAccess::userSearchFieldClass('receivedDate_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="receivedStartDate">Received Date</label>
            <div class="input-daterange input-group">
                <input type="text" name="receivedStartDate" id="receivedStartDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $receivedStartDate ?>" size="12" maxlength="10"
                       placeholder="From"/>
                <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                </div>
                <input type="text" name="receivedEndDate" id="receivedEndDate"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $receivedEndDate ?>" size="10" maxlength="10"
                       placeholder="To"/><label for="receivedEndDate"></label>
            </div>
        </div>
    </div>

    <div class="col-lg-3 mb-lg-0 mb-6 disclosureSentDate_Filter <?php echo UserAccess::userSearchFieldClass('disclosureSentDate_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?>">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="disclosureSentDateStart">Disclosure Sent
                Date</label>
            <div class="input-daterange input-group">
                <input type="text" name="disclosureSentDateStart" id="disclosureSentDateStart"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $disclosureSentDateStart; ?>" size="12" maxlength="10"
                       placeholder="From"/>
                <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                </div>
                <input type="text" name="disclosureSentDateEnd" id="disclosureSentDateEnd"
                       class="form-control form-controller-solid  dateNewClass"
                       value="<?php echo $disclosureSentDateEnd; ?>" size="10" maxlength="10"
                       placeholder="To"/><label for="receivedEndDate"></label>
            </div>
        </div>
    </div>


    <?php
    if ($fileType == 'LA') {
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6 paymentStatus_Filter <?php echo UserAccess::userSearchFieldClass('paymentStatus_Filter', myPipeline::$userSearchFieldsPreference ?? []); ?> ">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="paymentStatus">Payment Status</label>
                <select name="paymentStatus" class="form-control form-controller-solid  choice" id="paymentStatus">
                    <option value=""> All / Payment Status</option>
                    <option value="Payment Due" <?php echo Arrays::isSelected($paymentStatus, 'Payment Due') ?>>
                        Payment
                        Due
                    </option>
                    <option value="Paid" <?php echo Arrays::isSelected($paymentStatus, 'Paid') ?>> Paid</option>
                    <option value="Delinquent" <?php echo Arrays::isSelected($paymentStatus, 'Delinquent') ?>>
                        Delinquent
                    </option>
                </select>
            </div>
        </div>
        <?php
    }
    ?>


    <div class="col-lg-3 mb-lg-0 mb-6 d-none">
        <input class="btn btn-primary mr-2" type="submit" name="but_submit" value="Search"
               onclick="resetPageNum('1');">
        <input class="btn btn-secondary" type="reset" name="but_reset" value="Reset"
               onclick="clearAllFormValues();">
    </div> <!-- submit button Div-->

    <?php
    if ($allowExcelDownload == 1 && $allCount > 0 && $fileType != 'LA') {
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6">
            <a href="javascript:convertIntoXLS()"
               id='data=<?php echo urlencode(serialize($exportIp)) ?>'
               title="Click to export as xls"><i class="fa fa-file-excel-o"></i>
            </a>
        </div>
        <?php
    }
    }
    ?>
</div>

<div class="row mt-1">
    <div class="col-lg-12 text-right">
        <button class="btn btn-primary btn-primary--icon mr-2" name="but_submit" value="search"
                onclick="resetPageNum('1');" type="submit">
													<span>
														<i class="la la-search"></i>
														<span>Search</span>
													</span>
        </button>
        <button class="btn btn-secondary btn-secondary--icon mr-2" type="reset" name="but_reset" value="Reset"
                onclick="clearAllFormValues();">
													<span>
														<i class="la la-close"></i>
														<span>Reset</span>
													</span>
        </button>
        <?php
        if ($allowExcelDownload == 1 && $allCount > 0 && $fileType != 'LA') { // _customPage.php ?>
            <span class="btn1 d-none"
                  data-footerhide='hide'
                  id="exportXLSBtn"
                  data-href="<?php echo CONST_URL_POPS; ?>generateFilesReportXls.php?PCID=<?php echo $PCID; ?>"
                  data-wsize='modal-xl'
                  data-name='Export Client Data'
                  data-toggle='modal'
                  data-target='#exampleModal1'
                  data-callback="generateFilesReportXlsCallback"
                  data-query_string=''>
                <i class="ki ki-solid-plus icon-nm"></i>
            </span>
            <a class="btn btn-secondary btn-secondary exportXLS tooltipClass"
                <?php if ($LMRDataCnt) { ?> onclick="fetchIntoXLS()" <?php } ?>><i
                        class="far fa-file-excel text-success popoverClass" <?php if (!$LMRDataCnt) { ?> data-content="No Records To Fetch" <?php } ?>></i>
            </a>
            <?php
        }
        ?>
    </div>
</div>

<script>
    function generateFilesReportXlsCallback(elem) {
        let query_string = $(elem).data('query_string');
        $('#exportClientFilesForm #query_string').val(query_string);
    }
</script>
<?php
Strings::includeMyScript([
    '/backoffice/myPipeline/js/myPipelineSearch.js',
]);
?>
