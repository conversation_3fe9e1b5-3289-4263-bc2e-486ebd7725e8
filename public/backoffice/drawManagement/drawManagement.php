<?php

use models\PageVariables;
use models\cypher;
use models\composite\oDrawManagement\SowTemplateManager;
use models\standard\Strings;
use models\Request;

$pcId = isset(PageVariables::$PCID) ? cypher::encrypt(PageVariables::$PCID) : '';
if (empty($pcId) && $_REQUEST['pcid']) $pcId = REQUEST::GetClean('pcid');
$maxCategories = SowTemplateManager::MAX_CATEGORIES;
Strings::includeMyScript(['/assets/js/drawManagement.js']);
Strings::includeMyCSS(['/assets/css/components/drawManagement.css']);
$config = 'lender';
?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Scope of Work Template
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass" data-card-tool="toggle" data-section="workCategoriesCard" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                        <i class="ki icon-nm ki-arrow-down"></i>
                    </a>
                </div>
            </div>

            <div class="card-body workCategoriesCard">
                <!-- Stepper UI-->
                <div class="stepper-container mb-10">
                    <div class="step active" data-step="categories">
                        <span class="step-icon">1</span>
                        <span class="step-label">Categories</span>
                    </div>
                    <div class="step" data-step="line-items">
                        <span class="step-icon">2</span>
                        <span class="step-label">Line Items</span>
                    </div>
                </div>
                <!-- Stepper Content -->
                <div class="step-content">
                    <div id="content-step-categories">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0">Categories</h4>
                                <p class="text-muted mb-0">Manage up to <?php echo $maxCategories; ?> categories for your scope of work template.</p>
                            </div>
                            <button class="btn btn-sm btn-primary" id="openAddCategoryModalBtn"><i class="fas fa-plus"></i> Add Category</button>
                        </div>
                        <div class="categories-container sortable">
                            <!-- Categories will be loaded here dynamically -->
                            <template id="category-item-template">
                                <div class="category-item" data-category-id="" data-name="" data-description="">
                                    <div class="category-details">
                                        <h5 class="category-name"></h5>
                                        <p class="category-description"></p>
                                    </div>
                                    <div class="actions">
                                        <a href="#" class="edit-category-btn"><i class="fas fa-edit"></i></a>
                                        <a href="#" class="delete-category-btn"><i class="fas fa-trash-alt"></i></a>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="d-flex justify-content-center mt-4">
                            <button type="button" class="btn btn-primary save-cat" disabled>Save</button>
                            <button type="button" class="btn btn-primary save-cat mr-5 ml-5" id="save-next" disabled>Save & Next</button>
                            <button type="button" class="btn btn-secondary switch-step" data-target-step="line-items">Next</button>
                        </div>
                    </div>
                    <div id="content-step-line-items" style="display: none;">
                        <!-- ... Line items content ... -->
                         <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0">Line Items</h4>
                                <p class="text-muted mb-0">Manage line items for each category.</p>
                            </div>
                        </div>
                        <div id="lineItemCategoriesContainer">
                            <!-- Line items will be loaded here dynamically -->
                        </div>
                        <div class="d-flex justify-content-center mt-4">
                            <button type="button" class="btn btn-secondary switch-step" data-target-step="categories">Previous</button>
                            <button type="button" class="btn btn-primary save-line-items ml-2">Save</button> <!-- Or whatever the final action is -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12 mb-4">
        <div class="card card-custom">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Settings
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);" class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass collapsed" data-card-tool="toggle" data-section="drawTemplateSettings" data-toggle="tooltip" data-placement="top" title="Toggle Card" data-original-title="Toggle Card">
                        <i class="ki icon-nm ki-arrow-down"></i>
                    </a>
                </div>
            </div>

            <div class="card-body drawTemplateSettings collapse">
                <div class="form-group row align-items-center">
                    <label class="col-lg-4">Allow Borrowers to Add / Edit Categories?</label>
                    <div class="col-lg-2">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control" type="checkbox"
                                value="" id="allowBorrowersAddEditCategories" onchange="toggleSwitch('allowBorrowersAddEditCategoriesTog','allowBorrowersAddEditCategoriesVal','1','0' );"/>
                                <span></span>
                            </label>
                        </span>
                    </div>
                </div>

                <div class="form-group row align-items-center">
                    <label class="col-lg-4">Allow Borrowers to Remove Your Template's Categories?</label>
                    <div class="col-lg-2">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control" type="checkbox"
                                value="" id="allowBorrowersDeleteCategories" onchange="toggleSwitch('allowBorrowersDeleteCategoriesTog','allowBorrowersDeleteCategoriesVal','1','0' );"/>
                                <span></span>
                            </label>
                        </span>
                    </div>
                </div>

                <div class="form-group row align-items-center">
                    <label class="col-lg-4">Allow Borrowers to Add / Edit Line Items?</label>
                    <div class="col-lg-2">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control" type="checkbox"
                                value="" id="allowBorrowersAddEditLineItems" onchange="toggleSwitch('allowBorrowersAddEditLineItemsTog','allowBorrowersAddEditLineItemsVal','1','0' );"/>
                                <span></span>
                            </label>
                        </span>
                    </div>
                </div>

                <div class="form-group row align-items-center">
                    <label class="col-lg-4">Allow Borrowers to Remove Your Template's Line Items?</label>
                    <div class="col-lg-2">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control" type="checkbox"
                                value="" id="allowBorrowersDeleteLineItems" onchange="toggleSwitch('allowBorrowersDeleteLineItemsTog','allowBorrowersDeleteLineItemsVal','1','0' );"/>
                                <span></span>
                            </label>
                        </span>
                    </div>
                </div>

                <div class="form-group row align-items-center">
                    <label class="col-lg-4">Allow Borrowers to submit for Scope of Work Revisions?</label>
                    <div class="col-lg-2">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control" type="checkbox"
                                value="" id="allowBorrowersSOWRevisions" onchange="toggleSwitch('allowBorrowersSOWRevisionsTog','allowBorrowersSOWRevisionsVal','1','0' );"/>
                                <span></span>
                            </label>
                        </span>
                    </div>
                </div>

                <div class="form-group row align-items-center">
                    <label class="col-lg-4">Allow Borrowers to request more then rehab cost financed during a revision?</label>
                    <div class="col-lg-2">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control" type="checkbox"
                                value="" id="allowBorrowersExceedFinancedRehabCostOnRevision" onchange="toggleSwitch('allowBorrowersExceedFinancedRehabCostOnRevisionTog','allowBorrowersExceedFinancedRehabCostOnRevisionVal','1','0' );"/>
                                <span></span>
                            </label>
                        </span>
                    </div>
                </div>
                <div class="form-group row align-items-center">
                    <label class="col-lg-4">Draw Fee</label>
                    <div class=col-lg-4>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text" id="inputGroup-sizing-default">$</span>
                            </div>
                            <input type="text" class="form-control" aria-label="Draw Fees" aria-describedby="inputGroup-sizing-default" placeholder="0.00">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryFormModal" tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">Category</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="categoryForm" name="categoryForm">
                    <input type="hidden" id="modalCategoryId" name="modalCategoryId">
                    <div class="form-group">
                        <label for="modalCategoryName">Category Name</label>
                        <input type="text" class="form-control input-sm" id="modalCategoryName" name="modalCategoryName" required>
                        <div class="invalid-feedback text-danger">Category name is required.</div>
                    </div>
                    <div class="form-group">
                        <label for="modalCategoryDescription">Description</label>
                        <textarea class="form-control" id="modalCategoryDescription" name="modalCategoryDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveCategoryModalBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" role="dialog" aria-labelledby="confirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmationModalBody">Are you sure you want to proceed with this action?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="confirmModalCancelBtn" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmModalConfirmBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<?php include 'partials/_line-item-category-card.php'; ?>
<?php include 'partials/_line-item-row-lender.php'; ?>

<div id="drawManagement">
    <input type="hidden" id="pcid" value="<?php echo $pcId; ?>">
    <input type="hidden" id="maxCategories" value="<?php echo $maxCategories; ?>">
</div>
