<?php

use models\composite\oAffiliate\checkAffiliateAEExist;
use models\composite\oAffiliate\checkPromoCodeExist;
use models\composite\oAffiliate\generatePromoCode;
use models\composite\oAffiliate\insertAffiliateInfo;
use models\composite\oBranch\getBranchModulesForServices;
use models\composite\oBranch\savePreferredAgentForBranch;
use models\composite\oBroker\saveAgentInfo;
use models\composite\oBroker\saveAgentPCs;
use models\composite\oClient\getClientInfo;
use models\composite\oClient\saveClientAssetsInfo;
use models\composite\oClient\saveClientLOCheckingSavingInfo;
use models\composite\oClient\saveOrUpdateBorrowerAlternateNes;
use models\composite\oClient\savePCClientBackgroundInfo;
use models\composite\oClient\savePCClientExperienceInfo;
use models\composite\oClient\updateClientRecord;
use models\composite\oClient\uploadClientDocs;
use models\composite\oContacts\deleteFileContact;
use models\composite\oContacts\saveContacts;
use models\composite\oContacts\saveFileContacts;
use models\composite\oCreditMemo\saveCreditMemo;
use models\composite\oEquipmentFinancing\saveEquipmentFinancingInfo;
use models\composite\oFile\getFileInfo;
use models\composite\oFileDoc\saveFileDocument;
use models\composite\oFileEmail\LeadNotifierForPCs;
use models\composite\oFileEmail\sendEmailOnHMLOFileCreation;
use models\composite\oFileEmail\sendHMLORequestEmail;
use models\composite\oFileUpdate\assignUserToFile;
use models\composite\oFileUpdate\collateralSave;
use models\composite\oFileUpdate\estimatedProjectCostSave;
use models\composite\oFileUpdate\generateFileIdAsLoanNumber;
use models\composite\oFileUpdate\postDataToEndPoint;
use models\composite\oFileUpdate\propertyManagementSave;
use models\composite\oFileUpdate\saveBlanketLoanPropInfo;
use models\composite\oFileUpdate\saveClientInfo;
use models\composite\oFileUpdate\saveEquipmentInformation;
use models\composite\oFileUpdate\saveFeeSchedule;
use models\composite\oFileUpdate\saveFileNotes;
use models\composite\oFileUpdate\saveFileRentRoll;
use models\composite\oFileUpdate\saveFileSubstatusChange;
use models\composite\oFileUpdate\saveGiftsOrGrants;
use models\composite\oFileUpdate\saveOtherNewMortgageLoansOnProperty;
use models\composite\oFileUpdate\savePropertyManagementDocs;
use models\composite\oFileUpdate\saveSaleMethodInfo;
use models\composite\oFileUpdate\saveSellerInfo;
use models\composite\oFileUpdate2\saveBllingFee;
use models\composite\oHMLOInfo\saveAdditionalGuarantorsInfo;
use models\composite\oHMLOInfo\saveHMLOInfo;
use models\composite\oHMLOInfo\saveHMLOQAInfo;
use models\composite\oHMLOInfo\updateClientProfile;
use models\composite\oIncExp\creditorsLiabilitiesSave;
use models\composite\oLMProposal\saveLMProposalInfo;
use models\composite\oLoanOrigination\getLOScheduleOfRealEstate;
use models\composite\oLoanOrigination\LOLiabilitiesInfoSave;
use models\composite\oLoanOrigination\saveCoBEmployementInfo;
use models\composite\oLoanOrigination\saveEmployementInfo;
use models\composite\oLoanOrigination\saveFinanceAndSecurities;
use models\composite\oLoanOrigination\saveLOAssetsInfo;
use models\composite\oLoanOrigination\scheduleRealEstateSave;
use models\composite\oLockFile\lockFileOperation;
use models\composite\oPackage\getLibPackage;
use models\constants\BCForm;
use models\constants\getAccountantContactTypeID;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glMimeTypes;
use models\constants\gl\glNortheastLendingLeadNotifyPCs;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\contingentLiabilities as contingentLiabilitiesController;
use models\Controllers\LMRequest\fileAdminInfo;
use models\Controllers\LMRequest\generalContractor;
use models\Controllers\LMRequest\HUD as HUDController;
use models\Controllers\LMRequest\HUDCalculation;
use models\Controllers\LMRequest\insuranceAgentInfo;
use models\Controllers\LMRequest\partnerShips;
use models\Controllers\LMRequest\Property;
use models\Controllers\LMRequest\refinanceMortgage;
use models\CustomField;
use models\cypher;
use models\Database2;
use models\FileStorage;
use models\lendingwise\tblAutomatedRuleRequestV2;
use models\lendingwise\tblFile;
use models\lendingwise\tblRemoteUrl;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;
use models\UploadServer;

session_start();
require '../includes/util.php';
require 'initPageVariables.php';
require 'getPageVariables.php';

global $govtInfoArray, $userGroup, $DIYUser, $allowClientToCreateHMLOFile,
       $userNumber, $userRole, $userName,
       $allowAutomation;

$glMimeTypes = glMimeTypes::$glMimeTypes;
$glNortheastLendingLeadNotifyPCs = glNortheastLendingLeadNotifyPCs::$glNortheastLendingLeadNotifyPCs;


UserAccess::checkReferrerPgs(['url' => 'LMRequest.php, myPipeline.php']);

UserAccess::CheckAdminUse();

//Allow automated rules repeat code here
//This is only for use confirmation if required
$allowRepeat = Request::GetClean('allowRepeat') ?? '';
$triggerRule = Request::GetClean('triggerRule') ?? 'No';
$LMRId = $_POST['LMRID'] ?? 0;

if ($allowAutomation == 1 && $LMRId > 0 && $allowRepeat == 'check') {
    include('automatedRulesActionController.php');
    exit();
}


require 'checkValidEmailAddress.php';
/** Check the borrower email is valid else return stop the save process and return **/


$LMRClientType = [];
$LMRInternalLoanProgram = [];
$fileModule = [];
$resultArray = [];
$docArray = [];
$CIDArray = [];
$CIDArray1 = [];
$substatusID = [];
$getClientInfo = [];
$scheduleOfRealEstate = [];

$cnt = 0;
$publicUser = 0;
$OSID = 0;
$PCID = 0;
$LMRId = 0;
$encryptedBId = 0;
$clientId = 0;
$existingBRID = 0;
$existingAID = 0;
$executiveId = 0;
$brokerNumber = 0;
$CFPBFileUser = '';
$isSysNotesPrivate = 0;
$goToTab = '';
$activeTab = '';
$btnValue = '';
$assetId = 0;
$responseId = 0;
$UGroup = '';
$upCnt = 0;
$newLMRId = 0;
$borrowerLName = '';
$recordDate = '';
$oldFPCID = 0;

$contactIP = [];
$brokerNumber = '';
$secondaryBrokerNumber = '';
$selectedModuleServiceKeys = $propPicUploadCntArr = [];
$existingBranchSelectedServices = [];
$titleAttorneyID = 0;
$titleAttorneyName = '';
$titleAttorneyFirmName = '';
$titleAttorneyPhoneNumber1 = '';
$titleAttorneyPhoneNumber2 = '';
$titleAttorneyPhoneNumber3 = '';
$titleAttorneyPhoneNumberExt = '';
$titleAttorneyEmail = '';
$titleAttorneyCity = '';
$titleAttorneyPhoneNumber = '';
$titleAttorneyAddress = '';
$titleAttorneyState = '';
$titleAttorneyZip = '';
$titleAttorneyPhone = '';
$titleAttorneyBarNo = '';

$proIncPh1 = '';
$proIncPh2 = '';
$proIncPh3 = '';
$proIncPhExt = '';
$proIncPhone = '';
$proIncFax1 = '';
$proIncFax2 = '';
$proIncFax3 = '';
$proIncFax = '';
$SID = 0;
$op = '';
$isSave = '';
$welcomeCallDate = '';
$borrowerCallBack = '';
$phoneNumber = '';
$brokerPhone = '';
$cellNo = '';
$entityName = '';
$QAId = 0;
$proIncPh = '';
$selClientId = '';
$coBSsnNumber = $coBPhoneNumber = $coBCellNumber = $ssn = '';
$branchNotes = '';
$agentNotes = '';
$notes = '';
$existingSAID = '';
$loanNumber = $propPicUploadCnt = 0;
$titleCo = $titleNotes = $titleContactLName = $contact = $titlePhoneNumber = $tilteCellNo = $titleFax = $titletollFree = '';
$schedulePropAddr = $scheduleStatus = $propType = $presentMarketValue = $amountOfMortgages = $grossRentalIncome = $mortgagePayments = $insMaintTaxMisc = $netRentalIncome = $LOSRID = $paymentMethod = '';

$escrowID = $escrowName = $escrowOfficerLName = $escrowOfficerFirmName = $escrowOfficerEmail = $escrowOfficerPhone = $escrowOfficertollFree = $escrowOfficerFax = $escrowOfficerCell = $escrowNo = '';
$proInsCnt = 0;
$isFssUpdated = '';
$fileRow = '';
$fileTab = '';
$postedLMRId = 0;


if (isset($_POST['publicUser'])) $publicUser = trim($_POST['publicUser']);
if (isset($_POST['OSID'])) $OSID = trim($_POST['OSID']);
if (isset($_POST['encryptedPCID'])) $PCID = trim(cypher::myDecryption($_POST['encryptedPCID']));
if (isset($_POST['encryptedLId'])) {
    $LMRId = cypher::myDecryption(trim($_POST['encryptedLId']));
    $postedLMRId = cypher::myDecryption($_POST['encryptedLId']) ?? 0;
} elseif (!$LMRId) {
    $LMRId = Request::GetClean('LMRID') ?? 0;
}
if (glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) {
    unset($_POST['loanNumber']);
    unset($_REQUEST['loanNumber']);
}

if (isset($_POST['encryptedBId'])) $encryptedBId = trim(cypher::myDecryption($_POST['encryptedBId']));
if (isset($_POST['encryptedCId'])) $clientId = trim(cypher::myDecryption($_POST['encryptedCId']));
if (isset($_POST['encryptedRId'])) $responseId = trim(cypher::myDecryption(trim($_POST['encryptedRId'])));
if (isset($_POST['encryptedEId'])) $existingBRID = trim(cypher::myDecryption(trim($_POST['encryptedEId'])));
if (isset($_POST['encryptedBId'])) $existingAID = trim(cypher::myDecryption(trim($_POST['encryptedBId'])));
if (isset($_POST['encryptedSBId'])) $existingSAID = trim(cypher::myDecryption(trim($_POST['encryptedSBId'])));
if (isset($_POST['SID'])) $SID = trim($_POST['SID']);
if (isset($_POST['isSave'])) $isSave = trim($_POST['isSave']);
if (isset($_POST['RUID'])) $RUID = trim($_POST['RUID']);
if (isset($_POST['paymentMethod'])) $paymentMethod = trim($_POST['paymentMethod']);

if ($userGroup == 'Super' || $userGroup == 'Employee' || $userGroup == 'Agent' || $DIYUser == 1) {
    if (isset($_POST['branchId'])) $executiveId = trim(cypher::myDecryption(trim($_POST['branchId'])));
} else {
    if (isset($_POST['encryptedEId'])) $executiveId = trim(cypher::myDecryption(trim($_POST['encryptedEId'])));
}

if ($userGroup == 'Super' || $userGroup == 'Employee' || $userGroup == 'Branch' || $userGroup == 'Agent' || $publicUser == 1) {
    if (isset($_POST['agentId'])) $brokerNumber = trim(cypher::myDecryption(trim($_POST['agentId'])));
    if (isset($_POST['secondaryAgentId'])) $secondaryBrokerNumber = trim(cypher::myDecryption(trim($_POST['secondaryAgentId'])));
} else {
    if (isset($_POST['encryptedBId'])) $brokerNumber = trim(cypher::myDecryption(trim($_POST['encryptedBId'])));
    if (isset($_POST['secondaryAgentId'])) $secondaryBrokerNumber = trim(cypher::myDecryption(trim($_POST['secondaryAgentId'])));
}
if (!isset($_POST['secondaryAgentId'])) {
    $secondaryBrokerNumber = trim(cypher::myDecryption(trim($_POST['loanOfficerID'])));
}
if (isset($_POST['CFPBFileUser'])) $CFPBFileUser = trim($_POST['CFPBFileUser']);

if ($userGroup == 'Super' || $userGroup == 'Auditor' || $userGroup == 'Client' || $CFPBFileUser == 1) {
    if (isset($_POST['selectedPC'])) $PCID = $_POST['selectedPC'];
}
if ($publicUser == 1) {
    if (isset($_POST['encryptedPCID'])) $PCID = trim(cypher::myDecryption($_POST['encryptedPCID']));
}

if (isset($_REQUEST['LMRClientType'])) $LMRClientType = $_REQUEST['LMRClientType'];
if (isset($_REQUEST['LMRInternalLoanProgram'])) $LMRInternalLoanProgram = $_REQUEST['LMRInternalLoanProgram'];

if (isset($_REQUEST['isSysNotesPrivate'])) $isSysNotesPrivate = trim($_REQUEST['isSysNotesPrivate']);

if (isset($_REQUEST['fileModule'])) $fileModule = $_REQUEST['fileModule'];

if ($userGroup == 'Client' && $allowClientToCreateHMLOFile == 1 && $LMRId == 0) {
    if (isset($_POST['agentId'])) $brokerNumber = trim(cypher::myDecryption(trim($_POST['agentId'])));
    if (isset($_POST['secondaryAgentId'])) $secondaryBrokerNumber = trim(cypher::myDecryption(trim($_POST['secondaryAgentId'])));
    if (isset($_POST['branchId'])) $executiveId = trim(cypher::myDecryption(trim($_POST['branchId'])));
}

if (isset($_POST['goToTab'])) $goToTab = trim($_POST['goToTab']);
if (isset($_POST['activeTab'])) $activeTab = trim($_POST['activeTab']);
if (isset($_POST['btnSave'])) $btnValue = trim($_POST['btnSave']);
if (isset($_POST['assetId'])) $assetId = trim($_POST['assetId']);


if (isset($_POST['proIncFax1'])) $proIncFax1 = trim($_POST['proIncFax1']);
if (isset($_POST['proIncFax2'])) $proIncFax2 = trim($_POST['proIncFax2']);
if (isset($_POST['proIncFax3'])) $proIncFax3 = trim($_POST['proIncFax3']);
if (isset($_POST['op'])) $op = trim($_POST['op']);
if (isset($_POST['substatusID'])) $substatusID = $_POST['substatusID'];

if (isset($_POST['phoneNumber'])) $phoneNumber = trim($_POST['phoneNumber']);
if (isset($_POST['brokerPhone'])) $brokerPhone = trim($_POST['brokerPhone']);
if (isset($_POST['cellNo'])) $_REQUEST['cell'] = trim($_POST['cellNo']);
if (isset($_POST['welcomeCallDate'])) $welcomeCallDate = trim($_POST['welcomeCallDate']);
if (isset($_POST['borrowerCallBack'])) $borrowerCallBack = trim($_POST['borrowerCallBack']);
if (isset($_POST['entityName'])) $entityName = trim($_POST['entityName']);
if (isset($_POST['titleAttorneyPhoneNumber'])) $titleAttorneyPhoneNumber = $_POST['titleAttorneyPhoneNumber'];
if (isset($_POST['proIncPh'])) $proIncPh = $_POST['proIncPh'];
if (isset($_POST['coBSsnNumber'])) $_REQUEST['cSsn'] = $_POST['coBSsnNumber'];
if (isset($_POST['coBPhoneNumber'])) $_REQUEST['cPhone1'] = $_POST['coBPhoneNumber'];
if (isset($_POST['coBCellNumber'])) $coBCellNumber = trim($_POST['coBCellNumber']);
if (isset($_POST['exitStrategy'])) $exitStrategy = trim($_POST['exitStrategy']);
if (isset($_POST['ssn'])) $ssn = trim($_POST['ssn']);
if (isset($_POST['selClientId'])) $selClientId = trim($_POST['selClientId']);
if (isset($_POST['QAId'])) $QAId = trim($_POST['QAId']);
if (isset($_POST['loanNumber'])) $loanNumber = trim($_POST['loanNumber']);

if (isset($_POST['schedulePropAddr'])) $schedulePropAddr = $_POST['schedulePropAddr'];
if (isset($_POST['schedulePropCity'])) $schedulePropCity = $_POST['schedulePropCity'];
if (isset($_POST['schedulePropState'])) $schedulePropState = $_POST['schedulePropState'];
if (isset($_POST['schedulePropZip'])) $schedulePropZip = $_POST['schedulePropZip'];
if (isset($_POST['scheduleStatus'])) $scheduleStatus = $_POST['scheduleStatus'];
if (isset($_POST['propType'])) $propType = $_POST['propType'];
if (isset($_POST['presentMarketValue'])) $presentMarketValue = $_POST['presentMarketValue'];
if (isset($_POST['amountOfMortgages'])) $amountOfMortgages = $_POST['amountOfMortgages'];
if (isset($_POST['grossRentalIncome'])) $grossRentalIncome = $_POST['grossRentalIncome'];
if (isset($_POST['mortgagePayments'])) $mortgagePayments = $_POST['mortgagePayments'];
if (isset($_POST['insMaintTaxMisc'])) $insMaintTaxMisc = $_POST['insMaintTaxMisc'];
if (isset($_POST['netRentalIncome'])) $netRentalIncome = $_POST['netRentalIncome'];
if (isset($_POST['LOSRID'])) $LOSRID = trim(cypher::myDecryption($_POST['LOSRID']));
if (isset($_POST['propIncNotes'])) $propIncNotes = trim($_POST['propIncNotes']);
if (isset($_POST['proInsCnt'])) $proInsCnt = trim($_POST['proInsCnt']);
if (isset($_POST['schedulePropUnit'])) $schedulePropUnit = $_POST['schedulePropUnit'];
if (isset($_POST['schedulePropCountry'])) $schedulePropCountry = $_POST['schedulePropCountry'];
if (isset($_POST['paidAtOrBeforeClose'])) $paidAtOrBeforeClose = $_POST['paidAtOrBeforeClose'];
if (isset($_POST['paidAtOrBeforeCloseAnother'])) $paidAtOrBeforeCloseAnother = $_POST['paidAtOrBeforeCloseAnother'];

if (isset($_POST['escrowID'])) $escrowID = $_POST['escrowID'];
if (isset($_POST['recordDate'])) $recordDate = $_POST['recordDate'];
if (isset($_POST['oldFPCID'])) $oldFPCID = $_POST['oldFPCID'];

if (isset($_POST['fileRow'])) $fileRow = trim($_POST['fileRow']);
if (isset($_POST['fileTabNew'])) $fileTab = trim($_POST['fileTabNew']);

if (isset($_REQUEST['LOLID'])) $LOLID = $_REQUEST['LOLID'];
if (isset($_REQUEST['nameAddrOfCompany'])) $nameAddrOfCompany = $_REQUEST['nameAddrOfCompany'];
if (isset($_REQUEST['monthlyPaymentExpenses'])) $monthlyPayment = $_REQUEST['monthlyPaymentExpenses'];
if (isset($_REQUEST['monthsLeftToPay'])) $monthsLeftToPay = $_REQUEST['monthsLeftToPay'];
if (isset($_REQUEST['unpaidBalanceExpenses'])) $unpaidBalance = $_REQUEST['unpaidBalanceExpenses'];
if (isset($_REQUEST['accountNo'])) $accountNo = $_REQUEST['accountNo'];
if (isset($_REQUEST['liabilityAccType'])) $liabilityAccType = $_REQUEST['liabilityAccType'];
if (isset($_REQUEST['liabilityAtorBeforeClose'])) $liabilityAtorBeforeClose = $_REQUEST['liabilityAtorBeforeClose'];
if (isset($_REQUEST['deletedLOLId'])) $deletedLOLId = $_REQUEST['deletedLOLId'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ChangeLog::LogChanges(
        tblFile::class,
        $LMRId,
        basename(__FILE__, '.php'),
        $_REQUEST,
        $userNumber
    );
}

//Request::Dump();

$isBorrowermailEdited = $_POST['isBorrowermailEdited'] ?? '';
$originalClientId = $_POST['originalClientId'] ?? '';
$propPicUploadCntArr = $_POST['propPicUploadCnt'] ?? [];
$propPicUploadCnt = count($propPicUploadCntArr ?? []);

if (isset($_POST['spcf_taxes1'])) {
    $_REQUEST['taxes1'] = $_POST['spcf_taxes1'];
}
if (isset($_POST['spcf_annualPremium'])) {
    $_REQUEST['annualPremium'] = $_POST['spcf_annualPremium'];
}

if ($_REQUEST['fileTypesTxt'] != '') {
    $fileTypesTxtArray = explode(',', $_REQUEST['fileTypesTxt']);
    if (in_array('loc', $fileTypesTxtArray)) {
        //$_REQUEST["typeOfHMLOLoanRequesting"] = 'Purchase';  //commented, lets make sure we allow the users choice first so see next if statement
        if (!isset($_REQUEST['typeOfHMLOLoanRequesting'])) {
            $_REQUEST['typeOfHMLOLoanRequesting'] = 'Purchase';
        }
    }
}

if (($brokerNumber == '' || $brokerNumber == 0)) {
    if ($_REQUEST['dummyBrokerId'] == '' || $_REQUEST['dummyBrokerId'] == '0') {

        $dummyBrokerArray = [];
        $dummyEmailAgent = $PCID . '@dummyAgentemail.com';
        $AEPartnerCode = 0;
        $regDate = date('Y-m-d');
        $dummyBrokerArray = [
            'firstName'                                     => 'null',
            'lastName'                                      => '',
            'company'                                       => '',
            'email'                                         => $dummyEmailAgent,
            'phoneNumber'                                   => '',
            'registerDate'                                  => '',
            'promoCode'                                     => '',
            'pwd'                                           => $PCID,
            'city'                                          => '',
            'cellNumber'                                    => '',
            'fax'                                           => '',
            'agreedTC'                                      => 0,
            'allowAgentToAccessLMRDocs'                     => 0,
            'listAllAgents'                                 => 0,
            'allowAgentToCreateTasks'                       => 0,
            'allowAgentToSeeDashboard'                      => 0,
            'allowedToDeleteUplodedDocs'                    => 0,
            'allowedToEditOwnNotes'                         => 0,
            'seeBilling'                                    => 0,
            'allowToSendMarketingEmailForBRBO'              => 0,
            'allowToSeeWebForms'                            => 0,
            'allowToViewMarketPlace'                        => 0,
            'allowToLockLoanFileAgent'                      => 0,
            'allowToupdateFileAndClient'                    => 0,
            'allowToSendMassEmail'                          => 0,
            'sendNewDealAlert'                              => 0,
            'allowWorkflowEdit'                             => 0,
            'allowAgentToGetBorrowerUploadDocsNotification' => 0,
            'allowEmailCampaign'                            => 0,
        ];

        $ip = [
            'email' => $dummyEmailAgent,
        ];
        $AEPartnerCode = checkAffiliateAEExist::getReport($ip);

        if ($AEPartnerCode > 1) {
            $promoCode = $AEPartnerCode;
        } else {
            do {
                $promoCode = generatePromoCode::getReport();
                $ip = ['promoCode' => $promoCode];
                $opt = checkPromoCodeExist::getReport($ip);
            } while ($opt);

            $affiliateInfoArray = [
                'firstName'  => 'null',
                'lastName'   => '',
                'company'    => '',
                'email'      => $dummyEmailAgent,
                'pwd'        => '',
                'webAddress' => '',
                'promoCode'  => $promoCode,
                'siteName'   => 'TLP',
            ];
            insertAffiliateInfo::getReport($affiliateInfoArray);
        }
        $brokerArray = saveAgentInfo::getReport($dummyBrokerArray);

        if (count($brokerArray) > 0) {
            if (array_key_exists('brokerNumber', $brokerArray)) $brokerNumber = $brokerArray['brokerNumber'];
            if ($brokerNumber > 0) {
                $agentPCArray = [
                    'PCID'         => $PCID,
                    'brokerNumber' => $brokerNumber,
                ];
                savePreferredAgentForBranch::getReport([
                    'branchId' => $executiveId,
                    'agentId'  => $brokerNumber,
                    'PCID'     => $PCID,
                ]);

                saveAgentPCs::getReport($agentPCArray);
            }
        }
    } else {
        if (isset($_REQUEST['dummyBrokerId'])) {
            if ($_REQUEST['dummyBrokerId'] != '' && $_REQUEST['dummyBrokerId'] != '0') {
                $brokerNumber = $_REQUEST['dummyBrokerId'];
            }
        }
    }
}

$titlePhoneNumber = preg_replace('/[^0-9]/', '', $titlePhoneNumber);
$tilteCellNo = preg_replace('/[^0-9]/', '', $tilteCellNo);
$titleFax = preg_replace('/[^0-9]/', '', $titleFax);
$titletollFree = preg_replace('/[^0-9]/', '', $titletollFree);

if ($selClientId > 0) {
    $getClientInfo = getClientInfo::getReport([
        'CID'   => $selClientId,
        'email' => '',
        'PCID'  => $PCID,
    ]);
}
/* Get Client Information */
if (array_key_exists('getClientInfo', $getClientInfo)) $getClientInfo = $getClientInfo['getClientInfo'];
if (array_key_exists('clientInfo', $getClientInfo)) $clientInfo = $getClientInfo['clientInfo'][0];
if (array_key_exists('scheduleOfRealEstate', $getClientInfo)) $scheduleOfRealEstate = $getClientInfo['scheduleOfRealEstate'];


if (isset($_POST['op'])) $op = trim($_POST['op']);

if ($op == '') $op = cypher::myEncryption('edit');

$brokerPhone = preg_replace('/[^0-9]/', '', $brokerPhone);                                // | Phone Number Formatting
$_REQUEST['REBrokerPhoneNumber'] = $brokerPhone;

if (trim($entityName) != '') {
    $_REQUEST['borrowerUnderEntity'] = 'Yes';
}

$titleAttorneyPhone = preg_replace('/[^0-9]/', '', $titleAttorneyPhoneNumber);    // | Phone Number Formatting
$proIncPhone = preg_replace('/[^0-9]/', '', $proIncPh);                    // | Insurance Company Phone.

$proIncFax = $proIncFax1 . $proIncFax2 . $proIncFax3; // Insurance Company Fax.

/**
 * Task : Create New Files Via Summery Tab For "HMLO". - #*********
 * Desc : Create and update New File Section.
 */
//userGroup secondaryAgent for Loan Officer
$userGroup = Strings::GetSess('externalBroker') ? 'secondaryAgent' : $userGroup;

$fileInArray = [
    'p'                => $_POST,
    'LMRId'            => $LMRId,
    'responseId'       => $responseId,
    'branchId'         => $executiveId,
    'agentId'          => $brokerNumber,
    'secondaryAgentId' => $secondaryBrokerNumber,
    'clientId'         => $clientId,
    'UID'              => $userNumber,
    'URole'            => $userRole,
    'PCID'             => $PCID,
    'SID'              => $SID,
    'OSID'             => $OSID,
    'userName'         => $userName,
    'userGroup'        => $userGroup,
    'publicUser'       => $publicUser,
    'saveTab'          => 'CI',
];
if (!glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) {
    $fileInArray['loanNumber'] = $loanNumber;
}

$clientEmail = '';

$fileInArray['getClientInfo'] = $getClientInfo;
$resultArray = saveClientInfo::getReport($fileInArray);
if (count($resultArray) > 0) {
    if (array_key_exists('LMRId', $resultArray)) $LMRId = $resultArray['LMRId'];
    if (array_key_exists('upCnt', $resultArray)) $upCnt = $resultArray['upCnt'];
    if (array_key_exists('responseId', $resultArray)) $responseId = $resultArray['responseId'];
    if (array_key_exists('newLMRId', $resultArray)) $newLMRId = $resultArray['newLMRId'];
    if (array_key_exists('clientEmail', $resultArray)) $clientEmail = $resultArray['clientEmail'];
    if (array_key_exists('clientId', $resultArray)) $clientId = $resultArray['clientId'];

    if(!$postedLMRId){
        $notesArray['processorNotes'] = PageVariables::$userName . ' created a new file via ' .(($activeTab == 'LI') ? 'Full App' : 'Quick App');
        $notesArray['displayIn'] = 'NH';
        $notesArray['clientId'] = $clientId;
        $notesArray['privateNotes'] = 0;
        $notesArray['isSysNotes'] = 1;
        $notesArray['fileID'] = $LMRId;
        saveFileNotes::getReport($notesArray);
    }

    if ($upCnt > 0) Strings::SetSess('msg', 'Updated Successfully');
    else  Strings::SetSess('msg', 'Error while update.');
}

fileAdminInfo::$LMRId = $LMRId;
fileAdminInfo::save();

Property::$LMRId = $LMRId;
Property::$userGroup = $userGroup;
Property::$userNumber = $userNumber;
Property::initSave();


$fileInArray['LMRId'] = $LMRId;
if ($LMRId > 0) {
    saveOrUpdateBorrowerAlternateNes::getReport([
        'LMRID'           => $LMRId, 'alternateFName' => $_POST['alternateFName'],
        'alternateMName'  => $_POST['alternateMName'],
        'alternateLName'  => $_POST['alternateLName'],
        'alternateNameID' => $_POST['alternateNameID'],
    ]);
}

if (!$postedLMRId && glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) {
    generateFileIdAsLoanNumber::getReport($LMRId);
}
/* contingentLiabilities */
contingentLiabilitiesController::saveData($_REQUEST['contingentLiabilitiesFields'], $LMRId, $PCID);
/* contingentLiabilities */
/* estimatedProjectCostSave */
$estimatedProjectCostSave = estimatedProjectCostSave::getReport($fileInArray);
/* estimatedProjectCostSave */
/* Creditors / Liabilities (Non-Real Estate) */
creditorsLiabilitiesSave::getReport($fileInArray);
/* Creditors / Liabilities (Non-Real Estate) */
/* CollateralSave */
collateralSave::getReport($fileInArray);
/* CollateralSave */
/* Credit Memo */
saveCreditMemo::getReport($fileInArray);
/* Credit Memo */

saveSaleMethodInfo::getReport($fileInArray);
saveFeeSchedule::getReport($fileInArray);
saveEquipmentInformation::getReport($fileInArray);
saveOtherNewMortgageLoansOnProperty::getReport($fileInArray);
saveGiftsOrGrants::getReport($fileInArray);

if (in_array($_POST['typeOfHMLOLoanRequesting'], [
    typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
    , typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
    , typeOfHMLOLoanRequesting::DELAYED_PURCHASE
    , typeOfHMLOLoanRequesting::DELAYED_PURCHASE
    , typeOfHMLOLoanRequesting::COMMERCIAL_RATE_TERM_REFINANCE
    , typeOfHMLOLoanRequesting::REFINANCE
    , typeOfHMLOLoanRequesting::RATE_TERM_REFINANCE
])) {
    refinanceMortgage::saveData($_REQUEST['rcmFields'], $LMRId);
}
partnerShips::saveData($_REQUEST['partnerShipFields'], $LMRId);


$InArray = [
    'LMRId'                    => $LMRId,
    'LOLID'                    => $LOLID,
    'nameAddrOfCompany'        => $nameAddrOfCompany,
    'monthlyPayment'           => $monthlyPayment,
    'monthsLeftToPay'          => $monthsLeftToPay,
    'unpaidBalance'            => $unpaidBalance,
    'accountNo'                => $accountNo,
    'liabilityAccType'         => $liabilityAccType,
    'liabilityAtorBeforeClose' => $liabilityAtorBeforeClose,
    'deletedLOLId'             => $deletedLOLId,
];
LOLiabilitiesInfoSave::getReport($InArray);
/* Property Management Save */
$propMgmtRowId = propertyManagementSave::getReport($fileInArray);
//save the multiple docs
$fileUploadCount = count($_FILES['pmRealEstatePropMgmtDoc']['name'] ?? []);
if ($fileUploadCount > 0) {

    for ($f = 0; $f < $fileUploadCount; $f++) {
        // File Details
        $fileSrc_name = '';
        $tmp_name = '';
        $file_type = '';
        $file_size = '';
        $fileExtension = '';
        $docName = '';

        if (isset($_FILES['pmRealEstatePropMgmtDoc']['name'][$f])) $fileSrc_name = trim($_FILES['pmRealEstatePropMgmtDoc']['name'][$f]);
        if (isset($_FILES['pmRealEstatePropMgmtDoc']['tmp_name'][$f])) $tmp_name = trim($_FILES['pmRealEstatePropMgmtDoc']['tmp_name'][$f]);
        if (isset($_FILES['pmRealEstatePropMgmtDoc']['type'][$f])) $file_type = trim($_FILES['pmRealEstatePropMgmtDoc']['type'][$f]);
        if (isset($_FILES['pmRealEstatePropMgmtDoc']['size'][$f])) $file_size = trim($_FILES['pmRealEstatePropMgmtDoc']['size'][$f]);

        if ($fileSrc_name != '') {
            $docName = Strings::removeDisAllowedChars($fileSrc_name);
            $info = pathinfo($docName);
            if (count($info) > 0) {
                $fileExtension = $info['extension'];
            }
            $docName = str_ireplace('.' . $fileExtension, '', $fileSrc_name);
        }

        $tempArray = [
            'fileSrc_name' => $fileSrc_name,
            'tmp_name'     => $tmp_name,
            'file_type'    => $file_type,
            'file_size'    => $file_size,
            'docName'      => $docName,
            'docCategory'  => 'Prop. Manager Cert.',
        ];

        if ($file_size > 0) $docArray[] = $tempArray;
    }
    //Upload File


    for ($m = 0; $m < count($docArray); $m++) {
        if (in_array($docArray[$m]['file_type'], $glMimeTypes)) {
            if ($docArray[$m]['file_size'] > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
                Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
            } else {
                if ($docArray[$m]['fileSrc_name'] != '') {
                    $file_name = Strings::removeDisAllowedChars($docArray[$m]['fileSrc_name']);
                    $info = pathinfo($file_name);
                    if (count($info) > 0) $fileExtension = $info['extension'];
                    $infoArray = [
                        'fileExtension'      => $fileExtension,
                        'LMRID'              => $LMRId,
                        'PCID'               => $PCID,
                        'oldFPCID'           => $PCID,
                        'uploadedBy'         => $userNumber,
                        'userGroup'          => $userGroup,
                        'uploadingUserType'  => $userGroup,
                        'docName'            => Strings::stripQuote($docArray[$m]['docName']),
                        'docCategory'        => $docArray[$m]['docCategory'],
                        'recordDate'         => $recordDate,
                        'propertyManagement' => 1,
                        'tmpFileContent'     => base64_encode(FileStorage::getFile(dirname($docArray[$m]['tmp_name']) . '/' . basename($docArray[$m]['tmp_name']))),

                    ];
                    $tempDocName = Strings::stripQuote($docArray[$m]['docName']);
                    $infoArray['saveNotes'] = 1;
                    $infoArray['saveNotesDocs'] = $tempDocName;
                    //FILENAME. DATE . EXT
                    $fileName = Strings::stripQuote($docArray[$m]['docName']);
                    $encFilePath = Strings::stripQuote($docArray[$m]['docName']) . '-' . $m . '-' . date('Y-m-d-H-i-s');
                    $encFile = $encFilePath . '.' . $fileExtension;

                    $docId = saveFileDocument::getReport($infoArray);
                    //update encDocFileName to maintain sync with tblLMRFileDocs & tblPropertyManagementDocs
                    $encFile = Strings::removeDisAllowedChars(Strings::stripQuote($fileName)) . '_' . Strings::removeDisAllowedChars(Strings::stripQuote($borrowerLName)) . '_' . cypher::myEncryption($docId) . '.' . $fileExtension;
                    if ($docId > 0) {
                        $infoArray['fileDocName'] = $encFile;
                        $res = UploadServer::upload($infoArray);
                        if ($res == 'Success') ;
                        // save to tblPropertyManagementDocs
                        if ($docArray[$m]['fileSrc_name'] != '') {
                            $params['tblPropertyManagementId'] = $propMgmtRowId;
                            $params['PCID'] = $PCID;
                            $params['LMRId'] = $LMRId;
                            $params['pmFile'] = $fileName;
                            $params['encPMFile'] = $encFile;
                            $params['activeStatus'] = 1;
                            savePropertyManagementDocs::getReport($params);
                        }
                    }
                }
            }
        } else {
            Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
        }
    }
}
/* Property Management Save */


/**
 * Desc : Get Substatus Info For new loan file. - Pivotal - #154749877
 */
if (count($substatusID) > 0 && $newLMRId > 0) {
    /* Ignore to save the file notes when the status came from Summary while new file creation */
    $subInput = [
        'FID'   => $newLMRId, 'FRID' => $responseId, 'SSID' => $substatusID, 'chk' => 1, 'UID' => $userNumber,
        'URole' => $userGroup, 'isSysNotesPrivate' => $isSysNotesPrivate, 'saveTab' => 'LI',
    ];
    saveFileSubstatusChange::getReport($subInput);
}

/** Add Contacts Section Start **/
insuranceAgentInfo::save($LMRId);
generalContractor::save($LMRId);


$titleAttorneyIDArray = [];
if (isset($_POST['titleAttorneyID'])) $titleAttorneyIDArray = $_POST['titleAttorneyID'];
for ($i = 0; $i < count($titleAttorneyIDArray); $i++) {
    $titleAttorneyID = 0;
    $titleAttorneyIDOnLoad = 0;
    if (isset($_POST['titleAttorneyID'])) $titleAttorneyID = trim($_POST['titleAttorneyID'][$i]);
    if (isset($_POST['titleAttorneyIDOnLoad'])) $titleAttorneyIDOnLoad = trim($_POST['titleAttorneyIDOnLoad'][$i]);
    $tContArry = [];
    if (Arrays::checkValueExists($i, $_POST['titleAttorneyName']) != '' || Arrays::checkValueExists($i, $_POST['titleAttorneyFirmName']) != '') {
        if (isset($_POST['titleAttorneyName'])) $tContArry['contactName'] = trim($_POST['titleAttorneyName'][$i]);
        if (isset($_POST['titleAttorneyFirmName'])) $tContArry['companyName'] = trim($_POST['titleAttorneyFirmName'][$i]);
        if (isset($_POST['titleAttorneyPhoneNumber'])) $tContArry['phone'] = Strings::getNumberValue($_POST['titleAttorneyPhoneNumber'][$i]);
        if (isset($_POST['titleAttorneyEmail'])) $tContArry['email'] = trim($_POST['titleAttorneyEmail'][$i]);
        if (isset($_POST['titleAttorneyAddress'])) $tContArry['address'] = trim($_POST['titleAttorneyAddress'][$i]);
        if (isset($_POST['titleAttorneyCity'])) $tContArry['city'] = trim($_POST['titleAttorneyCity'][$i]);
        if (isset($_POST['titleAttorneyState'])) $tContArry['state'] = trim($_POST['titleAttorneyState'][$i]);
        if (isset($_POST['titleAttorneyZip'])) $tContArry['zipCode'] = trim($_POST['titleAttorneyZip'][$i]);
        if (isset($_POST['titleAttorneyBarNo'])) $tContArry['barNo'] = trim($_POST['titleAttorneyBarNo'][$i]);
    }

    if ($titleAttorneyID == 0) {
        if ($titleAttorneyIDOnLoad > 0) {
            deleteFileContact::getReport([
                'FileID' => $LMRId,
                'cRole'  => 'Attorney',
                'CID'    => $titleAttorneyIDOnLoad,
            ]);
        }
    }
    if ($titleAttorneyID > 0 && $titleAttorneyIDOnLoad != $titleAttorneyID) {
        deleteFileContact::getReport([
            'FileID' => $LMRId,
            'cRole'  => 'Attorney',
            'CID'    => $titleAttorneyIDOnLoad,
        ]);
    }

    if (count($tContArry) > 0) {
        $tContArry['CID'] = $titleAttorneyID;
        $tContArry['contactType'] = '3';
        $tContArry['cRole'] = 'Attorney';
        if ($titleAttorneyID > 0) $CIDArray[] = ['CID' => $titleAttorneyID, 'cRole' => 'Attorney'];

        $contactIP[] = $tContArry;
    }
}

$deletedAttorneyId = '';
$deletedAttorneyIdArray = [];
if (isset($_POST['deletedAttorneyId'])) $deletedAttorneyId = $_POST['deletedAttorneyId'];
if ($deletedAttorneyId != '') {
    $deletedAttorneyIdArray = explode(',', $deletedAttorneyId);
}
for ($i = 0; $i < count($deletedAttorneyIdArray); $i++) {
    deleteFileContact::getReport([
        'FileID' => $LMRId,
        'cRole'  => 'Attorney',
        'CID'    => trim($deletedAttorneyIdArray[$i]),
    ]);
}


$escrowIDArray = [];
if (isset($_POST['escrowID'])) $escrowIDArray = $_POST['escrowID'];
for ($i = 0; $i < count($escrowIDArray); $i++) {
    $escrowID = 0;
    $escrowIDOnLoad = 0;
    $tContArry = [];
    if (isset($_POST['escrowID'])) $escrowID = trim($_POST['escrowID'][$i]);
    if (isset($_POST['escrowIDOnLoad'])) $escrowIDOnLoad = trim($_POST['escrowIDOnLoad'][$i]);
    $tContArry = [];
    if (Arrays::checkValueExists($i, $_POST['escrowOfficer']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowOfficerLName']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowOfficerFirmName']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowOfficerPhone']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowOfficerEmail']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowOfficertollFree']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowOfficerFax']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowOfficerCell']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowNo']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowAddress']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowCity']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowState']) != ''
        || Arrays::checkValueExists($i, $_POST['escrowZip']) != ''
    ) {
        if (isset($_POST['escrowOfficer'])) $tContArry['contactName'] = trim($_POST['escrowOfficer'][$i]);
        if (isset($_POST['escrowOfficerLName'])) $tContArry['contactLName'] = trim($_POST['escrowOfficerLName'][$i]);
        if (isset($_POST['escrowOfficerFirmName'])) $tContArry['companyName'] = trim($_POST['escrowOfficerFirmName'][$i]);
        if (isset($_POST['escrowOfficerPhone'])) $tContArry['phone'] = Strings::getNumberValue($_POST['escrowOfficerPhone'][$i]);
        if (isset($_POST['escrowOfficerEmail'])) $tContArry['email'] = trim($_POST['escrowOfficerEmail'][$i]);
        if (isset($_POST['escrowOfficertollFree'])) $tContArry['tollFree'] = Strings::getNumberValue($_POST['escrowOfficertollFree'][$i]);
        if (isset($_POST['escrowOfficerFax'])) $tContArry['fax'] = Strings::getNumberValue($_POST['escrowOfficerFax'][$i]);
        if (isset($_POST['escrowOfficerCell'])) $tContArry['contactCell'] = Strings::getNumberValue($_POST['escrowOfficerCell'][$i]);
        if (isset($_POST['escrowNo'])) $tContArry['barNo'] = trim($_POST['escrowNo'][$i]);
        if (isset($_POST['escrowAddress'])) $tContArry['address'] = trim($_POST['escrowAddress'][$i]);
        if (isset($_POST['escrowCity'])) $tContArry['city'] = trim($_POST['escrowCity'][$i]);
        if (isset($_POST['escrowState'])) $tContArry['state'] = trim($_POST['escrowState'][$i]);
        if (isset($_POST['escrowZip'])) $tContArry['zipCode'] = trim($_POST['escrowZip'][$i]);
    }
    if (!$escrowID) {
        if ($escrowIDOnLoad > 0) {
            deleteFileContact::getReport([
                'FileID' => $LMRId,
                'cRole'  => 'Escrow',
                'CID'    => $escrowIDOnLoad,
            ]);
        }
    }
    if ($escrowID > 0 && $escrowIDOnLoad != $escrowID) {
        deleteFileContact::getReport([
            'FileID' => $LMRId,
            'cRole'  => 'Escrow',
            'CID'    => $escrowIDOnLoad,
        ]);
    }
    if ($escrowID > 0 && $escrowIDOnLoad != $escrowID) {
        deleteFileContact::getReport([
            'FileID' => $LMRId,
            'cRole'  => 'Escrow',
            'CID'    => $escrowIDOnLoad,
        ]);
    }

    if (count($tContArry) > 0) {
        $tContArry['CID'] = $escrowID;
        $tContArry['contactType'] = '41';
        $tContArry['cRole'] = 'Escrow';
        if ($escrowID > 0) $CIDArray[] = ['CID' => $escrowID, 'cRole' => 'Escrow'];

        $contactIP[] = $tContArry;
    }
}

$deletedEscrowId = '';
$deletedEscrowIdArray = [];
if (isset($_POST['deletedEscrowId'])) {
    $deletedEscrowId = $_POST['deletedEscrowId'];
}
if ($deletedEscrowId != '') {
    $deletedEscrowIdArray = explode(',', $deletedEscrowId);
}
for ($i = 0; $i < count($deletedEscrowIdArray); $i++) {
    deleteFileContact::getReport([
        'FileID' => $LMRId,
        'cRole'  => 'Escrow',
        'CID'    => trim($deletedEscrowIdArray[$i]),
    ]);
}

$financialAdvisorIDArray = [];
if (isset($_POST['financialAdvisorID'])) $financialAdvisorIDArray = $_POST['financialAdvisorID'];
for ($i = 0; $i < count($financialAdvisorIDArray); $i++) {
    $financialAdvisorID = 0;
    $financialAdvisorIDOnLoad = 0;
    $tContArry = [];
    if (isset($_POST['financialAdvisorID'])) $financialAdvisorID = trim($_POST['financialAdvisorID'][$i]);
    if (isset($_POST['financialAdvisorIDOnLoad'])) $financialAdvisorIDOnLoad = trim($_POST['financialAdvisorIDOnLoad'][$i]);
    $tContArry = [];
    if (Arrays::checkValueExists($i, $_POST['financialAdvisorFirstName']) != '' ||
        Arrays::checkValueExists($i, $_POST['financialAdvisorLastName']) != '' ||
        Arrays::checkValueExists($i, $_POST['financialAdvisorCompanyName']) != '') {
        if (isset($_POST['financialAdvisorFirstName'])) $tContArry['contactName'] = trim($_POST['financialAdvisorFirstName'][$i]);
        if (isset($_POST['financialAdvisorLastName'])) $tContArry['contactLName'] = trim($_POST['financialAdvisorLastName'][$i]);
        if (isset($_POST['financialAdvisorCompanyName'])) $tContArry['companyName'] = trim($_POST['financialAdvisorCompanyName'][$i]);
        if (isset($_POST['financialAdvisorPhone'])) $tContArry['phone'] = Strings::getNumberValue($_POST['financialAdvisorPhone'][$i]);
        if (isset($_POST['financialAdvisorEmail'])) $tContArry['email'] = trim($_POST['financialAdvisorEmail'][$i]);
        if (isset($_POST['financialAdvisorWebsite'])) $tContArry['website'] = trim($_POST['financialAdvisorWebsite'][$i]);
        if (isset($_POST['financialAdvisorAddress'])) $tContArry['address'] = trim($_POST['financialAdvisorAddress'][$i]);
        if (isset($_POST['financialAdvisorCity'])) $tContArry['city'] = trim($_POST['financialAdvisorCity'][$i]);
        if (isset($_POST['financialAdvisorState'])) $tContArry['state'] = trim($_POST['financialAdvisorState'][$i]);
        if (isset($_POST['financialAdvisorZip'])) $tContArry['zipCode'] = trim($_POST['financialAdvisorZip'][$i]);
        if (isset($_POST['financialAdvisorNotes'])) $tContArry['description'] = trim($_POST['financialAdvisorNotes'][$i]);
    }
    if ($financialAdvisorID == 0) {
        if ($financialAdvisorIDOnLoad > 0) {
            deleteFileContact::getReport([
                'FileID' => $LMRId,
                'cRole'  => 'Financial Advisor',
                'CID'    => $financialAdvisorIDOnLoad,
            ]);
        }
    }
    if ($financialAdvisorID > 0 && $financialAdvisorIDOnLoad != $financialAdvisorID) {
        deleteFileContact::getReport([
            'FileID' => $LMRId,
            'cRole'  => 'Financial Advisor',
            'CID'    => $financialAdvisorIDOnLoad,
        ]);
    }
    if (count($tContArry) > 0) {
        $tContArry['CID'] = $financialAdvisorID;
        $tContArry['contactType'] = '52';
        $tContArry['cRole'] = 'Financial Advisor';
        if ($financialAdvisorID > 0) $CIDArray[] = ['CID' => $financialAdvisorID, 'cRole' => 'Financial Advisor'];

        $contactIP[] = $tContArry;
    }
}

$deletedFinancialAdvisorId = '';
$deletedFinancialAdvisorIdArray = [];
if (isset($_POST['deletedFinancialAdvisorId'])) $deletedFinancialAdvisorId = $_POST['deletedFinancialAdvisorId'];
if ($deletedFinancialAdvisorId != '') {
    $deletedFinancialAdvisorIdArray = explode(',', $deletedFinancialAdvisorId);
}
for ($i = 0; $i < count($deletedFinancialAdvisorIdArray); $i++) {
    deleteFileContact::getReport([
        'FileID' => $LMRId,
        'cRole'  => 'Financial Advisor',
        'CID'    => trim($deletedFinancialAdvisorIdArray[$i]),
    ]);
}

$accountantContactTypeID = getAccountantContactTypeID::init();

$accountantIDArray = [];
if (isset($_POST['accountantID'])) $accountantIDArray = $_POST['accountantID'];
for ($i = 0; $i < count($accountantIDArray); $i++) {
    $accountantID = 0;
    $accountantIDOnLoad = 0;
    $tContArry = [];
    if (isset($_POST['accountantID'])) $accountantID = trim($_POST['accountantID'][$i]);
    if (isset($_POST['accountantIDOnLoad'])) $accountantIDOnLoad = trim($_POST['accountantIDOnLoad'][$i]);
    $tContArry = [];
    if (Arrays::checkValueExists($i, $_POST['accountantFirstName']) != '' ||
        Arrays::checkValueExists($i, $_POST['accountantLastName']) != '' ||
        Arrays::checkValueExists($i, $_POST['accountantCompanyName']) != '') {
        if (isset($_POST['accountantFirstName'])) $tContArry['contactName'] = trim($_POST['accountantFirstName'][$i]);
        if (isset($_POST['accountantLastName'])) $tContArry['contactLName'] = trim($_POST['accountantLastName'][$i]);
        if (isset($_POST['accountantCompanyName'])) $tContArry['companyName'] = trim($_POST['accountantCompanyName'][$i]);
        if (isset($_POST['accountantPhone'])) $tContArry['phone'] = Strings::getNumberValue($_POST['accountantPhone'][$i]);
        if (isset($_POST['accountantEmail'])) $tContArry['email'] = trim($_POST['accountantEmail'][$i]);
        if (isset($_POST['accountantWebsite'])) $tContArry['website'] = trim($_POST['accountantWebsite'][$i]);
        if (isset($_POST['accountantAddress'])) $tContArry['address'] = trim($_POST['accountantAddress'][$i]);
        if (isset($_POST['accountantCity'])) $tContArry['city'] = trim($_POST['accountantCity'][$i]);
        if (isset($_POST['accountantState'])) $tContArry['state'] = trim($_POST['accountantState'][$i]);
        if (isset($_POST['accountantZip'])) $tContArry['zipCode'] = trim($_POST['accountantZip'][$i]);
        if (isset($_POST['accountantNotes'])) $tContArry['description'] = trim($_POST['accountantNotes'][$i]);
    }
    if ($accountantID == 0) {
        if ($accountantIDOnLoad > 0) {
            deleteFileContact::getReport([
                'FileID' => $LMRId,
                'cRole'  => 'Accountant',
                'CID'    => $accountantIDOnLoad,
            ]);
        }
    }
    if ($accountantID > 0 && $accountantIDOnLoad != $accountantID) {
        deleteFileContact::getReport([
            'FileID' => $LMRId,
            'cRole'  => 'Accountant',
            'CID'    => $accountantIDOnLoad,
        ]);
    }
    if (count($tContArry) > 0) {
        $tContArry['CID'] = $accountantID;
        $tContArry['contactType'] = getAccountantContactTypeID::$accountantContactTypeID;
        $tContArry['cRole'] = 'Accountant';
        if ($accountantID > 0) $CIDArray[] = ['CID' => $accountantID, 'cRole' => 'Financial Advisor'];

        $contactIP[] = $tContArry;
    }
}

$deletedaccountantId = '';
$deletedaccountantIdArray = [];
if (isset($_POST['deletedaccountantId'])) $deletedaccountantId = $_POST['deletedaccountantId'];
if ($deletedaccountantId != '') {
    $deletedaccountantIdArray = explode(',', $deletedaccountantId);
}
for ($i = 0; $i < count($deletedaccountantIdArray); $i++) {
    deleteFileContact::getReport([
        'FileID' => $LMRId,
        'cRole'  => 'Accountant',
        'CID'    => trim($deletedaccountantIdArray[$i]),
    ]);
}

$representativeIDArray = [];
if (isset($_POST['representativeID'])) $representativeIDArray = $_POST['representativeID'];
for ($i = 0; $i < count($representativeIDArray); $i++) {
    $representativeID = 0;
    $representativeIDOnLoad = 0;
    $tContArry = [];
    if (isset($_POST['representativeID'])) $representativeID = trim($_POST['representativeID'][$i]);
    if (isset($_POST['representativeIDOnLoad'])) $representativeIDOnLoad = trim($_POST['representativeIDOnLoad'][$i]);
    $tContArry = [];
    if (Arrays::checkValueExists($i, $_POST['contact']) != '' || Arrays::checkValueExists($i, $_POST['titleContactLName']) != '' || Arrays::checkValueExists($i, $_POST['titleCo']) != '') {
        if (isset($_POST['contact'])) $tContArry['contactName'] = trim($_POST['contact'][$i]);
        if (isset($_POST['titleContactLName'])) $tContArry['contactLName'] = trim($_POST['titleContactLName'][$i]);
        if (isset($_POST['titleCo'])) $tContArry['companyName'] = trim($_POST['titleCo'][$i]);
        if (isset($_POST['titlePhoneNumber'])) $tContArry['phone'] = Strings::getNumberValue($_POST['titlePhoneNumber'][$i]);
        if (isset($_POST['sales2Email'])) $tContArry['email'] = trim($_POST['sales2Email'][$i]);
        if (isset($_POST['titletollFree'])) $tContArry['tollFree'] = Strings::getNumberValue($_POST['titletollFree'][$i]);
        if (isset($_POST['titleFax'])) $tContArry['fax'] = Strings::getNumberValue($_POST['titleFax'][$i]);
        if (isset($_POST['tilteCellNo'])) $tContArry['contactCell'] = Strings::getNumberValue($_POST['tilteCellNo'][$i]);
        if (isset($_POST['titleNotes'])) $tContArry['description'] = trim($_POST['titleNotes'][$i]);
        if (isset($_POST['titleAddress'])) $tContArry['address'] = trim($_POST['titleAddress'][$i]);
        if (isset($_POST['titleCity'])) $tContArry['city'] = trim($_POST['titleCity'][$i]);
        if (isset($_POST['titleState'])) $tContArry['state'] = trim($_POST['titleState'][$i]);
        if (isset($_POST['titleZip'])) $tContArry['zipCode'] = trim($_POST['titleZip'][$i]);
    }
    if (!$representativeID) {
        if ($representativeIDOnLoad > 0) {
            deleteFileContact::getReport([
                'FileID' => $LMRId,
                'cRole'  => 'Title Rep',
                'CID'    => $representativeIDOnLoad,
            ]);
        }
    }
    if ($representativeID > 0 && $representativeIDOnLoad != $representativeID) {
        deleteFileContact::getReport([
            'FileID' => $LMRId,
            'cRole'  => 'Title Rep',
            'CID'    => $representativeIDOnLoad,
        ]);
    }
    if (count($tContArry) > 0) {
        $tContArry['CID'] = $representativeID;
        $tContArry['contactType'] = '1';
        $tContArry['cRole'] = 'Title Rep';
        if ($representativeID > 0) $CIDArray[] = ['CID' => $representativeID, 'cRole' => 'Title Rep'];

        $contactIP[] = $tContArry;
    }
}
$deletedTitleId = '';
$deletedTitleIdArray = [];
if (isset($_POST['deletedTitleId'])) $deletedTitleId = $_POST['deletedTitleId'];
if ($deletedTitleId != '') {
    $deletedTitleIdArray = explode(',', $deletedTitleId);
}
for ($i = 0; $i < count($deletedTitleIdArray); $i++) {
    deleteFileContact::getReport([
        'FileID' => $LMRId,
        'cRole'  => 'Title Rep',
        'CID'    => trim($deletedTitleIdArray[$i]),
    ]);
}

if (in_array('LM', $fileModule) || in_array('SS', $fileModule)) {
    if (isset($_POST['attorneyID'])) {
        $tContArry = [];
        if (Arrays::checkValueExists('attorneyName', $_POST) != ''
            || Arrays::checkValueExists('attorneyFirmName', $_POST) != '') {
            if (isset($_POST['attorneyName'])) $tContArry['contactName'] = trim($_POST['attorneyName']);
            if (isset($_POST['attorneyFirmName'])) $tContArry['companyName'] = trim($_POST['attorneyFirmName']);
            if (isset($_POST['attorneyLastName'])) $tContArry['contactLName'] = trim($_POST['attorneyLastName']);
            if (isset($_POST['attorneyPhone'])) $tContArry['phone'] = trim($_POST['attorneyPhone']);
            if (isset($_POST['attorneyEmail'])) $tContArry['email'] = trim($_POST['attorneyEmail']);
            if (isset($_POST['attorneyAddress'])) $tContArry['address'] = trim($_POST['attorneyAddress']);
            if (isset($_POST['attorneyCity'])) $tContArry['city'] = trim($_POST['attorneyCity']);
            if (isset($_POST['attorneyState'])) $tContArry['state'] = trim($_POST['attorneyState']);
            if (isset($_POST['attorneyZip'])) $tContArry['zipCode'] = trim($_POST['attorneyZip']);
            if (isset($_POST['attorneyFax'])) $tContArry['fax'] = trim($_POST['attorneyFax']);
            if (isset($_POST['attorneyCell'])) $tContArry['contactCell'] = trim($_POST['attorneyCell']);
        } else {
            deleteFileContact::getReport([
                'FileID' => $LMRId,
                'cRole'  => 'Attorney',
            ]);
        }
        if (count($tContArry) > 0) {
            $tContArry['CID'] = $_POST['attorneyID'];
            $tContArry['contactType'] = '3';
            $tContArry['cRole'] = 'Attorney';
            if (is_array($_POST['attorneyID']) && count($_POST['attorneyID'])) {
                $CIDArray[] = [
                    'CID'   => $_POST['attorneyID'],
                    'cRole' => 'Attorney',
                ];
            }

            $contactIP[] = $tContArry;
        }
    }
    /**Trustee Info Save**/
    if (isset($_POST['trusteeID'])) {

        $tContArry = [];
        if (Arrays::checkValueExists('trusteeName', $_POST) != '' || Arrays::checkValueExists('trusteeFirmName', $_POST) != '' || Arrays::checkValueExists('trusteeLastName', $_POST) != '') {
            if (isset($_POST['trusteeName'])) $tContArry['contactName'] = trim($_POST['trusteeName']);
            if (isset($_POST['trusteeFirmName'])) $tContArry['companyName'] = trim($_POST['trusteeFirmName']);
            if (isset($_POST['trusteePhone'])) $tContArry['phone'] = Strings::getNumberValue($_POST['trusteePhone']);
            if (isset($_POST['trusteeEmail'])) $tContArry['email'] = trim($_POST['trusteeEmail']);
            if (isset($_POST['trusteeLastName'])) $tContArry['contactLName'] = trim($_POST['trusteeLastName']);
            if (isset($_POST['trusteeAddress'])) $tContArry['address'] = trim($_POST['trusteeAddress']);
            if (isset($_POST['trusteeCity'])) $tContArry['city'] = trim($_POST['trusteeCity']);
            if (isset($_POST['trusteeState'])) $tContArry['state'] = trim($_POST['trusteeState']);
            if (isset($_POST['trusteeZip'])) $tContArry['zipCode'] = trim($_POST['trusteeZip']);
            if (isset($_POST['trusteeFax'])) $tContArry['fax'] = Strings::getNumberValue($_POST['trusteeFax']);
            if (isset($_POST['trusteeCellNo'])) $tContArry['contactCell'] = Strings::getNumberValue($_POST['trusteeCellNo']);
        } else {
            deleteFileContact::getReport([
                'FileID' => $LMRId,
                'cRole'  => 'Trustee',
            ]);
        }

        if (count($tContArry) > 0) {
            $tContArry['CID'] = $_POST['trusteeID'];
            $tContArry['contactType'] = '23';
            $tContArry['cRole'] = 'Trustee';
            if (is_array($_POST['trusteeID']) && count($_POST['trusteeID'])) {
                $CIDArray[] = ['CID' => $_POST['trusteeID'], 'cRole' => 'Trustee'];
            }

            $contactIP[] = $tContArry;
        }
    }
}
//Lender Information
if (Request::isset('serviceLenderID')) {
    $tContArry = [];
    if (Arrays::checkValueExists('lenderEntityType', $_POST)) {
        if (Request::isset('lenderEntityType')) $tContArry['entityType'] = Request::GetClean('lenderEntityType');
    } else {
        deleteFileContact::getReport([
            'FileID' => $LMRId,
            'cRole'  => 'Lender',
        ]);
    }
    if (count($tContArry) > 0) {
        $tContArry['CID'] = Request::GetClean('serviceLenderID');
        $tContArry['contactType'] = '22';
        $tContArry['cRole'] = 'Lender';
        if (Request::GetClean('serviceLenderID') > 0) {
            $CIDArray[] = ['CID' => $_POST['serviceLenderID'], 'cRole' => 'Lender'];
        }
        $contactIP[] = $tContArry;
    }
}

/** Add Contacts Section End **/

if (isset($_REQUEST['fileLock'])) {
    $lockTabOpt = '';
    $lockSectionOpt = '';
    $fileLockedStatus = 0;

    if (isset($_REQUEST['lockTabOpt'])) {
        $lockTabOpt = $_REQUEST['lockTabOpt'];
    }
    if (isset($_REQUEST['lockSectionOpt'])) {
        $lockSectionOpt = $_REQUEST['lockSectionOpt'];
    }
    if (isset($_REQUEST['fileLock'])) {
        $fileLockedStatus = $_REQUEST['fileLock'];
    }

    // if($_REQUEST['fileLock'] == 1){
    $a = [
        'LMRId'            => $LMRId,
        'lockedUID'        => $userNumber,
        'lockedUserRole'   => $userRole,
        'lockedBy'         => $userName,
        'lockTabOpt'       => $lockTabOpt,
        'lockSectionOpt'   => $lockSectionOpt,
        'fileLockedStatus' => $fileLockedStatus,
    ];
    $res = lockFileOperation::getReport($a);
}


$HMLOInput = [
    'p'                      => $_POST,
    'LMRId'                  => $LMRId,
    'saveOpt'                => 'HMLOSummary',
    'saveTab'                => 'HMLOSummary',
    'encryptedBId'           => $encryptedBId,
    'PCID'                   => $PCID,
    'URole'                  => $userGroup,
    'UID'                    => $userNumber,
    'userName'               => $userName,
    'assetId'                => $assetId,
    'UGroup'                 => $UGroup,
    'LMRClientType'          => $LMRClientType,
    'clientId'               => $clientId,
    'newLMRId'               => $newLMRId,
    'LMRInternalLoanProgram' => $LMRInternalLoanProgram,
];

if ($LMRId > 0) {

    /** Save And Update File Contacts .**/
    if (count($contactIP) > 0) {
        $inArray['PCID'] = $PCID;
        $inArray['contacts'] = $contactIP;
        $CIDArray1 = saveContacts::getReport($inArray);
    }

    $CIDArray = array_merge($CIDArray, $CIDArray1);

    if (count($CIDArray) > 0) {
        saveFileContacts::getReport(['LMRId' => $LMRId, 'CID' => $CIDArray]);
    }

    $arrBG =
        [
            'CID'                                    => cypher::myEncryption($clientId),
            'PCID'                                   => cypher::myEncryption($PCID),
            'executiveId'                            => cypher::myEncryption($executiveId),
            'encClientId'                            => cypher::myEncryption($clientId),
            'userRole'                               => cypher::myEncryption($userRole),
            'isClient'                               => 1,
            'isBorUSCitizen'                         => $_REQUEST['isBorUSCitizen'],
            'borOrigin'                              => $_REQUEST['borOrigin'],
            'borVisaStatus'                          => $_REQUEST['borVisaStatus'],
            'borDecalredBankruptExpln'               => $_REQUEST['borDecalredBankruptExpln'],
            'borOutstandingJudgementsExpln'          => $_REQUEST['borOutstandingJudgementsExpln'],
            'borActiveLawsuitsExpln'                 => $_REQUEST['borActiveLawsuitsExpln'],
            'borPropertyTaxLiensExpln'               => $_REQUEST['borPropertyTaxLiensExpln'],
            'borObligatedInForeclosureExpln'         => $_REQUEST['borObligatedInForeclosureExpln'],
            'borDelinquentExpln'                     => $_REQUEST['borDelinquentExpln'],
            'borOtherFraudRelatedCrimesExpln'        => $_REQUEST['borOtherFraudRelatedCrimesExpln'],
            'borBackgroundExplanation'               => $_REQUEST['borBackgroundExplanation'],
            'borDesignatedBeneficiaryAgreement'      => $_REQUEST['borDesignatedBeneficiaryAgreement'],
            'borDesignatedBeneficiaryAgreementExpln' => $_REQUEST['borDesignatedBeneficiaryAgreementExpln'],
            //'sync' => 'sync',
        ];
    /** Save HMLO and Guarantors **/
    $HMLOInput['getClientInfo'] = $getClientInfo;
    $cnt = saveHMLOInfo::getReport($HMLOInput);
    if (is_array($cnt)) {
        $cnt = count($cnt);
    }

    $_POST['userGroup'] = $userGroup;
    $_POST['userNumber'] = $userNumber;
    $_POST['userName'] = $userName;
    $_POST['CID'] = $_POST['encryptedCId'];
    //Borrower Profile Sync with the Loan File
    //only if the age of the loan file is less than 30 days
    //not for CV3
    if (LMRequest::borrowerProfileSync() && !glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
        $_POST['sync'] = 'sync';
        $arrBG['sync'] = 'sync';
        savePCClientBackgroundInfo::getReport($arrBG);
        savePCClientExperienceInfo::getReport($_POST);
        //borrower profile (tblClient)
        $borrowerProfileSync = [
            'CID' => $clientId,
        ];
        if (Request::isset('borrowerLName') && Request::GetClean('borrowerLName') != '') {
            $borrowerProfileSync['borrowerLName'] = Request::GetClean('borrowerLName');
        }
        if (Request::isset('borrowerDOB') && Request::GetClean('borrowerDOB') != '') {
            $borrowerProfileSync['borrowerDOB'] = Dates::formatDateWithRE(Request::GetClean('borrowerDOB'), 'MDY', 'Y-m-d');
        }
        if (Request::isset('borrowerPOB') && Request::GetClean('borrowerPOB') != '') {
            $borrowerProfileSync['borrowerPOB'] = Request::GetClean('borrowerPOB');
        }
        if (Request::isset('ssn') && Request::GetClean('ssn') != '') {
            $borrowerProfileSync['ssn'] = Strings::numberOnly(Request::GetClean('ssn'));
        }
        if (Request::isset('phoneNumber') && Request::GetClean('phoneNumber') != '') {
            $borrowerProfileSync['phoneNumber'] = Strings::cleanPhoneNo(Request::GetClean('phoneNumber'));
        }
        if (Request::isset('cellNo') && Request::GetClean('cellNo') != '') {
            $borrowerProfileSync['cellNo'] = Strings::cleanPhoneNo(Request::GetClean('cellNo'));
        }
        if (Request::isset('serviceProvider') && Request::GetClean('serviceProvider') != '') {
            $borrowerProfileSync['serviceProvider'] = Request::GetClean('serviceProvider');
        }
        if (Request::isset('borrowerSecondaryEmail') && Request::GetClean('borrowerSecondaryEmail') != '') {
            $borrowerProfileSync['borrowerSecondaryEmail'] = Request::GetClean('borrowerSecondaryEmail');
        }
        if (Request::isset('presentAddress') && Request::GetClean('presentAddress') != '') {
            $borrowerProfileSync['presentAddress'] = Request::GetClean('presentAddress');
        }
        if (Request::isset('presentCity') && Request::GetClean('presentCity') != '') {
            $borrowerProfileSync['presentCity'] = Request::GetClean('presentCity');
        }
        if (Request::isset('presentState') && Request::GetClean('presentState') != '') {
            $borrowerProfileSync['presentState'] = Request::GetClean('presentState');
        }
        if (Request::isset('presentZip') && Request::GetClean('presentZip') != '') {
            $borrowerProfileSync['presentZip'] = Request::GetClean('presentZip');
        }
        if (Request::isset('methodOfContact') && Request::GetClean('methodOfContact') != '') {
            $borrowerProfileSync['methodOfContact'] = implode(',', Request::GetClean('methodOfContact'));
        }
        if (Request::isset('driverLicenseState') && Request::GetClean('driverLicenseState') != '') {
            $borrowerProfileSync['driverLicenseState'] = Request::GetClean('driverLicenseState');
        }
        if (Request::isset('driverLicenseNumber') && Request::GetClean('driverLicenseNumber') != '') {
            $borrowerProfileSync['driverLicenseNumber'] = Request::GetClean('driverLicenseNumber');
        }
        //HMDA Info Sync
        if (Request::isset('PublishBInfo') && Request::GetClean('PublishBInfo') != '') {
            $borrowerProfileSync['PublishBInfo'] = Request::GetClean('PublishBInfo');
        }
        if (Request::isset('BEthnicity') && Request::GetClean('BEthnicity') != '') {
            $borrowerProfileSync['BEthnicity'] = Request::GetClean('BEthnicity');
        }
        if (Request::isset('BRace') && Request::GetClean('BRace') != '') {
            $borrowerProfileSync['BRace'] = Request::GetClean('BRace');
        }
        if (Request::isset('BGender') && Request::GetClean('BGender') != '') {
            $borrowerProfileSync['BGender'] = Request::GetClean('BGender');
        }
        if (Request::isset('BVeteran') && Request::GetClean('BVeteran') != '') {
            $borrowerProfileSync['BVeteran'] = Request::GetClean('BVeteran');
        }
        if (Request::isset('bFiEthnicity') && Request::GetClean('bFiEthnicity') != '') {
            $borrowerProfileSync['bFiEthnicity'] = Request::GetClean('bFiEthnicity');
        }
        if (Request::isset('bFiEthnicitySub') && Request::GetClean('bFiEthnicitySub') != '') {
            $borrowerProfileSync['bFiEthnicitySub'] = Request::GetClean('bFiEthnicitySub');
        }
        if (Request::isset('bFiEthnicitySubOther') && Request::GetClean('bFiEthnicitySubOther') != '') {
            $borrowerProfileSync['bFiEthnicitySubOther'] = Request::GetClean('bFiEthnicitySubOther');
        }
        if (Request::isset('bFiSex') && Request::GetClean('bFiSex') != '') {
            $borrowerProfileSync['bFiSex'] = Request::GetClean('bFiSex');
        }
        if (Request::isset('bFiRace') && Request::GetClean('bFiRace') != '') {
            $borrowerProfileSync['bFiRace'] = Request::GetClean('bFiRace');
        }
        if (Request::isset('bFiRaceSub') && Request::GetClean('bFiRaceSub') != '') {
            $borrowerProfileSync['bFiRaceSub'] = Request::GetClean('bFiRaceSub');
        }
        if (Request::isset('bFiRaceAsianOther') && Request::GetClean('bFiRaceAsianOther') != '') {
            $borrowerProfileSync['bFiRaceAsianOther'] = Request::GetClean('bFiRaceAsianOther');
        }
        if (Request::isset('bFiRacePacificOther') && Request::GetClean('bFiRacePacificOther') != '') {
            $borrowerProfileSync['bFiRacePacificOther'] = Request::GetClean('bFiRacePacificOther');
        }
        if (Request::isset('bDemoInfo') && Request::GetClean('bDemoInfo') != '') {
            $borrowerProfileSync['bDemoInfo'] = Request::GetClean('bDemoInfo');
        }

        updateClientRecord::borrowerProfileSyncData($borrowerProfileSync);
        //assets info (tblClientAssetsInfo)
        saveClientAssetsInfo::getReport([
            'CID'  => $clientId,
            'sync' => 'sync',
        ]);
        $cnt += saveClientLOCheckingSavingInfo::getReport([
            'p' => $_POST,
            'CID' => $clientId,
            'sync' => 'sync',
        ]);
    }

    /** Save And Update HMLO Fields .**/
    saveAdditionalGuarantorsInfo::getReport($HMLOInput);

    /** Upload Client Document **/
    $docIP = ['CID' => $clientId, 'userGroup' => $userGroup, 'userName' => $userName, 'userNumber' => $userNumber, 'p' => $_POST];
    uploadClientDocs::getReport($docIP);

    $inputArray = ['p' => $_POST, 'LMRId' => $LMRId, 'clientId' => $clientId];
    $inputArray['getClientInfo'] = $getClientInfo;
    saveLOAssetsInfo::getReport($inputArray);
    saveFinanceAndSecurities::getReport($inputArray);
    saveSellerInfo::getReport($inputArray);
    /** New Assets Fields .**/

    /**
     * param inputs $inputArray like LMRId, client id, option.
     * Check borrower prfile related info has changed.
     * Card #600 - Updating Borrower profile logic.
     * Added by Suresh Kasinathan <<EMAIL>>
     */
    if (Arrays::getArrayValue('confirmType', $_REQUEST) != '' && $clientId > 0) {
        updateClientProfile::getReport([
            'LMRId' => $LMRId,
            'CID'   => $clientId,
            'opt'   => Arrays::getArrayValue('confirmType', $_REQUEST),
        ]);
    }
}
/* Card_103_Schedule_Of_Real_Estate_Gets_Duplicated */
$ipArray = ['LMRId' => $LMRId];
$scheduleOfRealEstate = getLOScheduleOfRealEstate::getReport($ipArray);
/* End of Card_103_Schedule_Of_Real_Estate_Gets_Duplicated */
$scheduleestate = [
    'LMRId'                   => $LMRId,
    'schedulePropAddr'        => $_POST['schedulePropAddr'],
    'schedulePropCity'        => $_POST['schedulePropCity'],
    'schedulePropState'       => $_POST['schedulePropState'],
    'schedulePropZip'         => $_POST['schedulePropZip'],
    'scheduleStatus'          => $_POST['scheduleStatus'],
    'propType'                => $_POST['propType'],
    'presentMarketValue'      => $_POST['presentMarketValue'],
    'amountOfMortgages'       => $_POST['amountOfMortgages'],
    'grossRentalIncome'       => $_POST['grossRentalIncome'],
    'mortgagePayments'        => $_POST['mortgagePayments'],
    'insMaintTaxMisc'         => $_POST['insMaintTaxMisc'],
    'netRentalIncome'         => $_POST['netRentalIncome'],
    'scheduleID'              => $_POST['scheduleID'],
    'titledUnder'             => $_POST['titledUnder'],
    'datePurchased'           => $_POST['datePurchased'],
    'purchasePrice'           => $_POST['purchasePrice'],
    'valueofImprovementsMade' => $_POST['valueofImprovementsMade'],
    'intendedOccupancy'       => $_POST['intendedOccupancy'],
    'p'                       => $_POST,
];
scheduleRealEstateSave::getReport($scheduleestate);

if (in_array($PCID, $glNortheastLendingLeadNotifyPCs) && $newLMRId > 0) {
    LeadNotifierForPCs::getReport(['LMRId' => $newLMRId]);
}
/** Branch Changed Notifiy Section */
if ($existingBRID != $executiveId && $LMRId > 0) {
    $branchIDs = $executiveId;
    if ($existingBRID > 0) {
        $branchIDs .= ', ' . $existingBRID;
        $branchInfo = [];
        if ($branchIDs != '') {
            $branchInfo = \models\composite\oBranch\getMyDetails::getReport(['executiveId' => $branchIDs]);
        }
        if (count($branchInfo) > 0) {
            $branchNotes = 'Branch changed from ';
            if (array_key_exists($existingBRID, $branchInfo)) {
                $branchNotes .= $branchInfo[$existingBRID]['LMRExecutive'];
            }
            if (array_key_exists($executiveId, $branchInfo)) {
                $branchNotes .= ' to ' . $branchInfo[$executiveId]['LMRExecutive'] . '.';
            }
        }
    }

    if ($executiveId > 0 && $existingBRID > 0 && count($LMRClientType) > 0) {
        $selectedModuleServices = getBranchModulesForServices::getReport(['branchID' => $existingBRID, 'STCode' => implode(',', $LMRClientType)]);
        if (count($selectedModuleServices) > 0) $selectedModuleServiceKeys = array_keys($selectedModuleServices);
        for ($i = 0; $i < count($selectedModuleServiceKeys); $i++) {
            $tempSelArray = [];
            $tempSelArray = $selectedModuleServices[$selectedModuleServiceKeys[$i]];
            for ($j = 0; $j < count($tempSelArray); $j++) {
                $existingBranchSelectedServices[] = $tempSelArray[$j]['STCode'];
                $existingBranchSelectedModules[] = $tempSelArray[$j]['moduleCode'];
            }
        }
    }
}
/** Branch Changed Notifiy Section */


/* Title Report */
$fileSrc_name = '';
$tmp_name = '';
$file_type = '';
$file_size = '';
$fileExtension = '';
if (isset($_FILES['titleReport']['name'])) $fileSrc_name = trim($_FILES['titleReport']['name']);
if (isset($_FILES['titleReport']['tmp_name'])) $tmp_name = trim($_FILES['titleReport']['tmp_name']);
if (isset($_FILES['titleReport']['type'])) $file_type = trim($_FILES['titleReport']['type']);
if (isset($_FILES['titleReport']['size'])) $file_size = trim($_FILES['titleReport']['size']);
if ($fileSrc_name != '') {
    $docName = Strings::removeDisAllowedChars($fileSrc_name);
    $info = pathinfo($docName);
    if (count($info) > 0) {
        $fileExtension = $info['extension'];
    }
    $docName = str_ireplace('.' . $fileExtension, '', $fileSrc_name);
}
if ($tmp_name != '') {
    $tempArray = [
        'fileSrc_name' => $fileSrc_name,
        'tmp_name'     => $tmp_name,
        'file_type'    => $file_type,
        'file_size'    => $file_size,
        'docName'      => $docName,
        'docCategory'  => 'Title Report',
    ];
    $docArray[] = $tempArray;
}

/* Proof of sale (HUD) Upload */
for ($i = 7; $i <= 15; $i++) {
    $fileSrc_name = '';
    $tmp_name = '';
    $file_type = '';
    $file_size = '';
    $fileExtension = '';
    $docName = '';
    if (isset($_FILES['proofOfSale' . $i]['name'])) $fileSrc_name = trim($_FILES['proofOfSale' . $i]['name']);
    if (isset($_FILES['proofOfSale' . $i]['tmp_name'])) $tmp_name = trim($_FILES['proofOfSale' . $i]['tmp_name']);
    if (isset($_FILES['proofOfSale' . $i]['type'])) $file_type = trim($_FILES['proofOfSale' . $i]['type']);
    if (isset($_FILES['proofOfSale' . $i]['size'])) $file_size = trim($_FILES['proofOfSale' . $i]['size']);
    if ($fileSrc_name != '') {
        $docName = Strings::removeDisAllowedChars($fileSrc_name);
        $info = pathinfo($docName);
        if (count($info) > 0) {
            $fileExtension = $info['extension'];
        }
        $docName = str_ireplace('.' . $fileExtension, '', $fileSrc_name);
    }
    if ($file_size > 0) {
        $tempArray = [
            'fileSrc_name' => $fileSrc_name,
            'tmp_name'     => $tmp_name,
            'file_type'    => $file_type,
            'file_size'    => $file_size,
            'docName'      => $docName,
            'docCategory'  => 'Proof of sale (HUD)-' . $i,
            'section'      => 'coBorrExp',
        ];                  //for c0-borr exp section HUD files should upload in upload folder :card462
        $docArray[] = $tempArray;
    }
}

/**
 *
 * Property Insurance Coverage upload Multiple Upload On Feb 03, 2017
 **/

for ($i = 0; $i <= $proInsCnt; $i++) {
    $fileSrc_name = '';
    $tmp_name = '';
    $file_type = '';
    $file_size = '';
    $fileExtension = '';
    $docName = '';
    if (isset($_FILES['propertyInsuranceCoverage' . $i]['name'])) $fileSrc_name = trim($_FILES['propertyInsuranceCoverage' . $i]['name']);
    if (isset($_FILES['propertyInsuranceCoverage' . $i]['tmp_name'])) $tmp_name = trim($_FILES['propertyInsuranceCoverage' . $i]['tmp_name']);
    if (isset($_FILES['propertyInsuranceCoverage' . $i]['type'])) $file_type = trim($_FILES['propertyInsuranceCoverage' . $i]['type']);
    if (isset($_FILES['propertyInsuranceCoverage' . $i]['size'])) $file_size = trim($_FILES['propertyInsuranceCoverage' . $i]['size']);
    if ($fileSrc_name != '') {
        $docName = Strings::removeDisAllowedChars($fileSrc_name);
        $info = pathinfo($docName);
        if (count($info) > 0) {
            $fileExtension = $info['extension'];
        }
        $docName = str_ireplace('.' . $fileExtension, '', $fileSrc_name);
    }

    $tempArray = [
        'fileSrc_name' => $fileSrc_name,
        'tmp_name'     => $tmp_name,
        'file_type'    => $file_type,
        'file_size'    => $file_size,
        'docName'      => $docName,
        'docCategory'  => 'Property Insurance Coverage',
    ];

    if ($file_size > 0) $docArray[] = $tempArray;
}

/* pictures of property saving code*/
if ($LMRId > 0) {
    for ($i = 0; $i < $propPicUploadCnt; $i++) {
        $fileSrc_name = '';
        $tmp_name = '';
        $file_type = '';
        $file_size = '';
        $fileExtension = '';
        $docName = '';
        if (isset($_FILES['propertyUploadPic']['name'][$i])) $fileSrc_name = trim($_FILES['propertyUploadPic']['name'][$i]);
        if (isset($_FILES['propertyUploadPic']['tmp_name'][$i])) $tmp_name = trim($_FILES['propertyUploadPic']['tmp_name'][$i]);
        if (isset($_FILES['propertyUploadPic']['type'][$i])) $file_type = trim($_FILES['propertyUploadPic']['type'][$i]);
        if (isset($_FILES['propertyUploadPic']['size'][$i])) $file_size = trim($_FILES['propertyUploadPic']['size'][$i]);
        if ($fileSrc_name != '') {
            $docName = Strings::removeDisAllowedChars($fileSrc_name);
            $info = pathinfo($docName);
            if (count($info) > 0) {
                $fileExtension = $info['extension'];
            }
            $docName = str_ireplace('.' . $fileExtension, '', $fileSrc_name);
        }

        $tempArray = [
            'fileSrc_name' => $fileSrc_name,
            'tmp_name'     => $tmp_name,
            'file_type'    => $file_type,
            'file_size'    => $file_size,
            'docName'      => $docName,
            'docCategory'  => 'House Pictures',
        ];

        if ($file_size > 0) $docArray[] = $tempArray;
    }

}

$insCnt = 0;
$tempDocName = '';

$lmrRes = tblFile::Get(['LMRId' => $LMRId]);

$oldFPCID = $oldFPCID == 0 ? $lmrRes->oldFPCID : 0;
$recordDate = $recordDate == '' ? $lmrRes->recordDate : '';
$borrowerLName = $borrowerLName == '' ? $lmrRes->borrowerLName : '';

for ($m = 0; $m < count($docArray); $m++) {
    if (in_array($docArray[$m]['file_type'], $glMimeTypes)) {
        if ($docArray[$m]['file_size'] > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
            Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
        } else {
            if ($docArray[$m]['fileSrc_name'] != '') {
                $file_name = Strings::removeDisAllowedChars($docArray[$m]['fileSrc_name']);
                $info = pathinfo($file_name);
                if (count($info) > 0) $fileExtension = $info['extension'];
                $infoArray = [
                    'fileExtension'     => $fileExtension,
                    'LMRID'             => $LMRId,
                    'PCID'              => $oldFPCID,
                    'userGroup'         => $userGroup,
                    'userNumber'        => $userNumber,
                    'borrowerLName'     => $borrowerLName,
                    'userName'          => $userName,
                    'uploadedBy'        => $userNumber,
                    'uploadingUserType' => $userGroup,
                    'docName'           => Strings::stripQuote($docArray[$m]['docName']),
                    'docCategory'       => $docArray[$m]['docCategory'],
                    'isSysNotesPrivate' => $isSysNotesPrivate,
                    'recordDate'        => $recordDate,
                    'oldFPCID'          => $oldFPCID,
                    'tmpFileContent'    => base64_encode(FileStorage::getFile(dirname($docArray[$m]['tmp_name']) . '/' . basename($docArray[$m]['tmp_name']))),

                ];

                if ($m > 0) $tempDocName .= ', ';
                $tempDocName .= Strings::stripQuote($docArray[$m]['docName']);
                if (($m + 1) == count($docArray)) {
                    $infoArray['saveNotes'] = 1;
                    $infoArray['saveNotesDocs'] = $tempDocName;
                }
                $docId = 0;
                $res = '';

                if (!$docArray[$m]['section']) $infoArray['propertyDocs'] = 1;     //for c0-borr exp section HUD files should upload in upload folder :card462

                if ($docArray[$m]['docCategory'] == 'House Pictures') $infoArray['propertyDocs'] = 0;
                if ($docArray[$m]['docCategory'] != 'Prop. Manager Cert.') {
                    $docId = saveFileDocument::getReport($infoArray);
                }

                if ($docId > 0) {
                    $infoArray['fileDocName'] = Strings::removeDisAllowedChars(Strings::stripQuote($docArray[$m]['docName'])) . '_' . Strings::removeDisAllowedChars($borrowerLName) . '_' . cypher::myEncryption($docId) . '.' . $fileExtension;
                    $res = UploadServer::upload($infoArray);
                    if ($res == 'Success') $insCnt++;
                }
            }
        }
    } else {
        Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
    }
}
/* Title Report */

/** previous Agent and branch notes **/
if ($existingAID != $brokerNumber && $LMRId > 0) {
    $agentIDs = $brokerNumber;
    if ($existingAID > 0) {
        $agentIDs .= ', ' . $existingAID;
        $agentInfo = [];
        if ($agentIDs != '') {
            $agentInfo = \models\composite\oBroker\getMyDetails::getReport(['agentId' => $agentIDs]);
        }
        if (count($agentInfo) > 0) {
            $agentNotes = 'Broker changed from ';
            if (array_key_exists($existingAID, $agentInfo)) {
                $agentNotes .= $agentInfo[$existingAID]['brokerName'];
            }
            if (array_key_exists($brokerNumber, $agentInfo)) {
                $agentNotes .= ' to ' . $agentInfo[$brokerNumber]['brokerName'] . '.';
            }
        }
    }
}

if ($existingSAID != $secondaryBrokerNumber && $LMRId > 0) {
    $secondaryAgentIDs = $secondaryBrokerNumber;
    if ($existingSAID > 0) {
        $secondaryAgentIDs .= ', ' . $existingSAID;
        $secondaryAgentInfo = [];
        if ($secondaryAgentIDs != '') {
            $secondaryAgentInfo = \models\composite\oBroker\getMyDetails::getReport(['agentId' => $secondaryAgentIDs]);
        }
        if (count($secondaryAgentInfo) > 0) {
            $secondaryAgentNotes = 'Loan Officer changed from ';
            if (array_key_exists($existingSAID, $secondaryAgentInfo)) {
                $secondaryAgentNotes .= $secondaryAgentInfo[$existingSAID]['brokerName'];
            }
            if (array_key_exists($secondaryBrokerNumber, $secondaryAgentInfo)) {
                $secondaryAgentNotes .= ' to ' . $secondaryAgentInfo[$secondaryBrokerNumber]['brokerName'] . '.';
            }
        }
    }
}

$append = '';
if ($branchNotes != '') {
    $notes = $branchNotes;
    $append = '<br>';
}
if ($agentNotes != '') $notes .= $append . $agentNotes;
if ($secondaryAgentNotes != '') $notes .= $append . $secondaryAgentNotes;
if ($notes != '') {
    $exeId = 0;
    $empId = 0;
    $brId = 0;
    if ($userGroup == 'Super') {
        $exeId = 0;
        $empId = 0;
        $brId = 0;
    } else if ($userGroup == 'Employee') {
        $exeId = 0;
        $empId = $userNumber;
        $brId = 0;
    } else if ($userGroup == 'Branch') {
        $exeId = $userNumber;
        $empId = 0;
        $brId = 0;
    } else if ($userGroup == 'Agent') {
        $empId = 0;
        $exeId = 0;
        $brId = $userNumber;
    }

    $ip = [
        'processorNotes' => $notes,
        'fileID'         => $LMRId,
        'privateNotes'   => $isSysNotesPrivate,
        'employeeId'     => $empId,
        'executiveId'    => $exeId,
        'brokerNumber'   => $brId,
        'isSysNotes'     => 1,
    ];
    saveFileNotes::getReport($ip); // refactored
    $ip = [];
}

/** previous Agent and branch notes **/

saveEquipmentFinancingInfo::getReport([
    'p'          => $_POST,
    'LMRId'      => $LMRId,
    'UID'        => $userNumber,
    'URole'      => $userRole,
    'PCID'       => $PCID,
    'vendorName' => Arrays::getArrayValue('vendorName', $_POST),
]);

/** Assign user to file **/
if ($newLMRId > 0 && $userGroup == 'Employee') {
    $inArray['LMRId'] = trim($LMRId);
    $inArray['UID'] = [$userNumber];
    $inArray['URole'] = 'Employee';
    $inArray['assignedByUID'] = $userNumber;
    $inArray['assignedByURole'] = $userGroup;
    $inArray['opt'] = 'CI';
    $updateCnt = assignUserToFile::getReport($inArray); // refactored
}
/** Assign user to file **/

/** Add Default Fee.. **/
if ($newLMRId > 0 && (glCustomJobForProcessingCompany::is($PCID))) {
    $feeType = [
        BCForm::CONST_BILLING_COM_RETAINER_FEE,
        BCForm::CONST_BILLING_COM_RECURRING_FEE,
    ];  /* 2, 47 */
    saveBllingFee::getReport(['LMRId' => $LMRId, 'feeType' => $feeType]);
}

$QAId = saveHMLOQAInfo::getReport($govtInfoArray);
$cnt += $QAId;

if (isset($_REQUEST['AddiontalEmplInfo'])) {
    saveEmployementInfo::saveEmplInfo($_REQUEST['AddiontalEmplInfo'], cypher::myEncryption($LMRId)); // refactored
}
if (isset($_REQUEST['AdditionalCoBorEmplInfo'])) {
    saveCoBEmployementInfo::saveEmplInfo($_REQUEST['AdditionalCoBorEmplInfo'], cypher::myEncryption($LMRId));
}

/** Add Default Fee.. **/
if ($RUID > 0) {
    $item = tblRemoteUrl::Get(['LMRId' => $LMRId, 'RUID' => $RUID]);
    $item->paymentMethod = $paymentMethod;
    $item->Save();
} elseif ($LMRId > 0) {
    //if the LMRId is not created in tblFile, stop inserting the data into child tables (check $LMRId > 0 )
    $item = new tblRemoteUrl();
    $item->LMRId = $LMRId;
    $item->paymentMethod = $paymentMethod;
    $item->Save();
}
require 'savePaymentInfo.php';
/**
 * Lead notification email for client portal new file creation.
 */
if ($LMRId > 0 && $userGroup == 'Client') {
    $attachmentArray = [];

    $LMRArray = getFileInfo::getReport([
        'LMRId' => $LMRId,
    ]);
    $myFileInfo = $LMRArray[$LMRId];

    $LMRInfoArray = $myFileInfo['LMRInfo'];

    if (count($LMRInfoArray) > 0) {

        $fileCreatedDate = $recordDate = $LMRInfoArray['recordDate'];
        $FPCID = $LMRInfoArray['FPCID'];
        $executiveId = $LMRInfoArray['FBRID'];
        $oldFPCID = $LMRInfoArray['oldFPCID'];
        $borrowerName = $LMRInfoArray['borrowerName'];
        $borrowerLName = $LMRInfoArray['borrowerLName'];
        $defaultBrokerNumber = $LMRInfoArray['brokerNumber'];

        $inArray = [];
        /**
         * Desc : Get Package Save Path - (#154749877)
         */
        $tempFileCreatedDate = '';
        $tempFileCreatedDate = str_replace('-', '', $fileCreatedDate);
        $dest = $oldFPCID . '/' . date('Y', strtotime($tempFileCreatedDate)) . '/' . date('m', strtotime($tempFileCreatedDate)) . '/' . date('d', strtotime($tempFileCreatedDate));
        /**
         * Desc : HMLO Loan Application Details.- (#154749877)
         */
        $WFPkgID = '';
        if ($activeTab == 'LI') {
            $WFPkgID = 887;
            $webformName = 'Full Loan App';
        } else {
            $WFPkgID = 886;
            $webformName = 'Quick App';
        }

        $pkgAllSelectedArray = getLibPackage::getReport(['PKGID' => $WFPkgID]); // Get Package Information...
        $docName = $pkgAllSelectedArray[$WFPkgID]['pkgName'];
        $docId = $pkgAllSelectedArray[$WFPkgID]['PKGID'];
        $esign = $pkgAllSelectedArray[$WFPkgID]['esign'];
        $packageType = $pkgAllSelectedArray[$WFPkgID]['packageType'];
        $selectedPkgUrl = trim($pkgAllSelectedArray[$WFPkgID]['filePath']);
        $inArray['userNumber'] = $userNumber;
        $inArray['userGroup'] = $userGroup;
        $inArray['LMRId'] = $LMRId;
        $inArray['pkgID'] = $docId;
        $inArray['responseId'] = $responseId;
        $inArray['printOutput'] = 'n';
        $inArray['actions'] = 'Emailed';
        $inArray['attach'] = 'y';
        $inArray['PCID'] = $PCID;
        $inArray['LMRArray'] = $LMRArray;
        generateAppropriatePkg($inArray);

        /**
         * Desc : Generate and Get Attachment of "HMLO Loan Application".- (#154749877)
         */
        $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . Strings::removeDisAllowedChars(stripslashes($docName)) . '_' . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($docId)) . '.pdf{' . $docName . '.pdf';

        $LMREmailSent = $LMRInfoArray['LMREmailSent'];
        $ip = [];
        if ($LMREmailSent == 0) {
            $ip['LMRId'] = $LMRId;
            $ip['userIPAddr'] = $_SERVER['REMOTE_ADDR'];
            $ip['webformName'] = $webformName;
            $ip['typeOfHMLOLoanRequesting'] = $_POST['typeOfHMLOLoanRequesting'];
            $ip['purchaseCloseDate'] = $_POST['purchaseCloseDate'];
            $ip['attachment'] = $attachmentArray;
            $ip['userGroup'] = $userGroup;
        }
        $ip['fileRow'] = $fileRow;
        $ip['typeOfForm'] = $fileTab;
        if (in_array('HMLO', $fileModule)) {
            sendEmailOnHMLOFileCreation::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
        } else {
            sendHMLORequestEmail::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
        }
        $ip = [];
    }
}
//send email notification based on the user (BO, Branch, Broker, Loan Officer) permissions
if ($upCnt > 0 && $userGroup != 'Client') { // loan file insert / update - to make sure file is created with no errors
    //hidden field condition
    if ($fileRow == 'Insert') { // only for file create/Insert
        $LMRArray = getFileInfo::getReport([
            'LMRId' => $LMRId,
        ]);
        $myFileInfo = $LMRArray[$LMRId];
        $LMRInfoArray = $myFileInfo['LMRInfo'];
        if (count($LMRInfoArray) > 0) {

            $fileCreatedDate = $recordDate = $LMRInfoArray['recordDate'];
            $FPCID = $LMRInfoArray['FPCID'];
            $executiveId = $LMRInfoArray['FBRID'];
            $oldFPCID = $LMRInfoArray['oldFPCID'];
            $borrowerName = $LMRInfoArray['borrowerName'];
            $borrowerLName = $LMRInfoArray['borrowerLName'];
            $defaultBrokerNumber = $LMRInfoArray['brokerNumber'];

            $inArray = [];
            $tempFileCreatedDate = '';
            $tempFileCreatedDate = str_replace('-', '', $fileCreatedDate);
            $dest = $oldFPCID . '/' . date('Y', strtotime($tempFileCreatedDate)) . '/' . date('m', strtotime($tempFileCreatedDate)) . '/' . date('d', strtotime($tempFileCreatedDate));
            $WFPkgID = '';
            if ($activeTab == 'LI') {
                $WFPkgID = 887;
                $webformName = 'Full Loan App';
            } else {
                $WFPkgID = 886;
                $webformName = 'Quick App';
            }

            $pkgAllSelectedArray = getLibPackage::getReport(['PKGID' => $WFPkgID]); // Get Package Information...
            $docName = $pkgAllSelectedArray[$WFPkgID]['pkgName'];
            $docId = $pkgAllSelectedArray[$WFPkgID]['PKGID'];
            $esign = $pkgAllSelectedArray[$WFPkgID]['esign'];
            $packageType = $pkgAllSelectedArray[$WFPkgID]['packageType'];
            $selectedPkgUrl = trim($pkgAllSelectedArray[$WFPkgID]['filePath']);
            $inArray['userNumber'] = $userNumber;
            $inArray['userGroup'] = $userGroup;
            $inArray['LMRId'] = $LMRId;
            $inArray['pkgID'] = $docId;
            $inArray['responseId'] = $responseId;
            $inArray['printOutput'] = 'n';
            $inArray['actions'] = 'Emailed';
            $inArray['attach'] = 'y';
            $inArray['PCID'] = $PCID;
            $inArray['LMRArray'] = $LMRArray;
            generateAppropriatePkg($inArray);
            $attachmentArray[] = CONST_PATH_LMR_FILE_DOCS . $dest . '/' . $LMRId . '/' . Strings::removeDisAllowedChars(stripslashes($docName)) . '_' . Strings::removeDisAllowedChars(Strings::undoHTMLEntitiesForPDF(stripslashes($borrowerLName))) . '_' . trim(cypher::myEncryption($responseId)) . '_' . trim(cypher::myEncryption($docId)) . '.pdf{' . $docName . '.pdf';
            $ip = [];
            $LMREmailSent = $LMRInfoArray['LMREmailSent'];
            if ($LMREmailSent == 0) {
                $ip['LMRId'] = $LMRId;
                $ip['userIPAddr'] = $_SERVER['REMOTE_ADDR'];
                $ip['webformName'] = $webformName;
                $ip['typeOfHMLOLoanRequesting'] = $_POST['typeOfHMLOLoanRequesting'];
                $ip['purchaseCloseDate'] = $_POST['purchaseCloseDate'];
                $ip['attachment'] = $attachmentArray;
                $ip['userGroup'] = $userGroup;
            }

            if (in_array('HMLO', $fileModule)) {
                sendEmailOnHMLOFileCreation::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
            } else {
                sendHMLORequestEmail::getReport($ip, $defaultBrokerNumber, $executiveId, $myFileInfo);
            }

            $ip = []; // this needs to be reset
        }
    }
}
//end send email notification based on the user (BO, Branch, Broker, Loan Officer) permissions

if ($isBorrowermailEdited == 'edited') {
    // need to update all loan files
    $updateqry = " UPDATE tblFile SET clientId = '" . $clientId . "', borrowerEmail='" . $clientEmail . "',
      enc_borrowerEmail   = '" . cypher::myEncryption($clientEmail) . "' WHERE clientId = " . $originalClientId . ';';
    $updatecnt = Database2::getInstance()->update($updateqry);

    $updatecqry = ' UPDATE tblClient SET activeStatus = 1 WHERE CID = ' . $originalClientId . ';';
    $updateccnt = Database2::getInstance()->update($updatecqry);
}

/* saving part of lien1,lien2 and foreclosure */
if (in_array('LM', $fileModule) || in_array('SS', $fileModule)) {
    $qu = '';
    $qs = '';
    $qc = '';
    $qv = '';
    if (isset($_REQUEST['noticeReceivedDate'])) {
        $noticeReceivedDate = '';
        $noticeReceivedDate = trim($_REQUEST['noticeReceivedDate']);
        if (Dates::IsEmpty($noticeReceivedDate)) {
            $noticeReceivedDate = '0000-00-00';
        } else {
            $noticeReceivedDate = trim(Dates::formatDateWithRE($noticeReceivedDate, 'MDY', 'Y-m-d'));
        }
        $qc .= $qs . ' noticeReceivedDate ';
        $qv .= $qs . " '" . trim($noticeReceivedDate) . "' ";

        $qu .= $qs . " noticeReceivedDate = '" . trim($noticeReceivedDate) . "' ";
        $qs = ', ';
    }
    if (isset($_REQUEST['summonDate'])) {
        $summonDate = '';
        $summonDate = trim($_REQUEST['summonDate']);
        if (Dates::IsEmpty($summonDate)) {
            $summonDate = '0000-00-00';
        } else {
            $summonDate = trim(Dates::formatDateWithRE($summonDate, 'MDY', 'Y-m-d'));
        }
        $qc .= $qs . ' summonDate ';
        $qv .= $qs . " '" . trim($summonDate) . "' ";
        $qu .= $qs . " summonDate = '" . trim($summonDate) . "' ";
        $qs = ', ';
    }
    if (isset($_REQUEST['appliedModificationDate'])) {
        $appliedModificationDate = '';
        $appliedModificationDate = trim($_REQUEST['appliedModificationDate']);
        if (Dates::IsEmpty($appliedModificationDate)) {
            $appliedModificationDate = '0000-00-00';
        } else {
            $appliedModificationDate = trim(Dates::formatDateWithRE($appliedModificationDate, 'MDY', 'Y-m-d'));
        }
        $qc .= $qs . ' appliedModificationDate ';
        $qv .= $qs . " '" . trim($appliedModificationDate) . "' ";
        $qu .= $qs . " appliedModificationDate = '" . trim($appliedModificationDate) . "' ";
        $qs = ', ';
    }
    if (isset($_REQUEST['receivedModificationOfferDate'])) {
        $receivedModificationOfferDate = '';
        $receivedModificationOfferDate = trim($_REQUEST['receivedModificationOfferDate']);
        if (Dates::IsEmpty($receivedModificationOfferDate)) {
            $receivedModificationOfferDate = '0000-00-00';
        } else {
            $receivedModificationOfferDate = trim(Dates::formatDateWithRE($receivedModificationOfferDate, 'MDY', 'Y-m-d'));
        }
        $qc .= $qs . ' receivedModificationOfferDate ';
        $qv .= $qs . " '" . trim($receivedModificationOfferDate) . "' ";
        $qu .= $qs . " receivedModificationOfferDate = '" . trim($receivedModificationOfferDate) . "' ";
        $qs = ', ';
    }
    if (isset($_REQUEST['bankruptcyFilingDate'])) {
        $bankruptcyFilingDate = '';
        $bankruptcyFilingDate = trim($_REQUEST['bankruptcyFilingDate']);
        if (Dates::IsEmpty($bankruptcyFilingDate)) {
            $bankruptcyFilingDate = '0000-00-00';
        } else {
            $bankruptcyFilingDate = trim(Dates::formatDateWithRE($bankruptcyFilingDate, 'MDY', 'Y-m-d'));
        }
        $qc .= $qs . ' bankruptcyFilingDate ';
        $qv .= $qs . " '" . trim($bankruptcyFilingDate) . "' ";
        $qu .= $qs . " bankruptcyFilingDate = '" . trim($bankruptcyFilingDate) . "' ";
        $qs = ', ';
    }
    if (isset($_REQUEST['bankruptcyDischargeDate'])) {
        $bankruptcyDischargeDate = '';
        $bankruptcyDischargeDate = trim($_REQUEST['bankruptcyDischargeDate']);
        if (Dates::IsEmpty($bankruptcyDischargeDate)) {
            $bankruptcyDischargeDate = '0000-00-00';
        } else {
            $bankruptcyDischargeDate = trim(Dates::formatDateWithRE($bankruptcyDischargeDate, 'MDY', 'Y-m-d'));
        }
        $qc .= $qs . ' bankruptcyDischargeDate ';
        $qv .= $qs . " '" . trim($bankruptcyDischargeDate) . "' ";
        $qu .= $qs . " bankruptcyDischargeDate = '" . trim($bankruptcyDischargeDate) . "' ";
        $qs = ', ';
    }
    if (isset($_REQUEST['dateOfOffer'])) {
        $dateOfOffer = '';
        $dateOfOffer = trim($_REQUEST['dateOfOffer']);
        if (Dates::IsEmpty($dateOfOffer)) {
            $dateOfOffer = '0000-00-00';
        } else {
            $dateOfOffer = trim(Dates::formatDateWithRE($dateOfOffer, 'MDY', 'Y-m-d'));
        }
        $qc .= $qs . ' dateOfOffer ';
        $qv .= $qs . " '" . trim($dateOfOffer) . "' ";
        $qu .= $qs . " dateOfOffer = '" . trim($dateOfOffer) . "' ";
        $qs = ', ';
    }
    $QAFieldsArray = ['thirtyDaysLate', 'isHomeownerServed', 'attorneyNumber', 'jurisDiction', 'indexNo', 'filedBankruptcy', 'bankruptcyChapter', 'isPaymentsPlanBehind', 'bankruptcyDispositionStatus', 'bankruptcyCaseNumb', 'propertyIncludedBankruptcy', 'isMtgReaffirmed', 'foreClosureNotes', 'stayInHome', 'appliedModification', 'homeListedForSale', 'saleForHowLong', 'saleByOwner', 'OPAPhone', 'receiveOfferOnProperty', 'amountOfOffer', 'moreThanOneProperty', 'noOfProperties', 'noOfMortgages', 'delinquentOnPropTax', 'delinquentTaxAmount', 'delinquentTaxYear', 'notOnMortgageNote', 'borrowerNatural', 'serviceMember', 'serviceMemberOrder', 'serviceMemberSurvivor', 'borrowerConvicted', 'noOfPeopleDependent', 'noOfvehiclesOwned'];
    /** DIRECT Variables **/


    for ($f = 0; $f < count($QAFieldsArray); $f++) {
        if (isset ($_REQUEST["$QAFieldsArray[$f]"])) {
            $qc .= $qs . " $QAFieldsArray[$f] ";
            $postVal = HTTP::escapeQuoteForPOST($_REQUEST["$QAFieldsArray[$f]"]);
            if ($QAFieldsArray[$f] == 'amountOfOffer' || $QAFieldsArray[$f] == 'delinquentTaxAmount') {
                $postVal = Strings::replaceCommaValues($_REQUEST["$QAFieldsArray[$f]"]);
            }
            $qv .= $qs . " '" . $postVal . "' ";
            $qu .= $qs . " $QAFieldsArray[$f] = '" . $postVal . "' ";
            $qs = ', ';
        }
    }
    $resultArray = [];
    $q = ' SELECT QAID FROM tblQAInfo WHERE LMRId = ' . $LMRId;
    $resultArray = Database2::getInstance()->fetchRecords(['qry' => $q, 'rec' => 'single']);
    if (count($resultArray) > 0) {
        if ($qu != '') {
            $qry = ' UPDATE tblQAInfo SET ' . $qu . ' WHERE LMRId = ' . $LMRId . ';';
            $rsL = Database2::getInstance()->update($qry);
        }
    } else {
        if ($qc != '' && $qv != '') {
            $qry = ' INSERT INTO tblQAInfo (LMRId, ' . $qc . ") VALUES ('" . $LMRId . "', " . $qv . ');';
            $rsL = Database2::getInstance()->insert($qry);
        }
    }

    saveLMProposalInfo::getReport(['LMRId' => $LMRId, 'opt' => 'MI', 'DIYUser' => '1', 'UID' => $userNumber, 'UGroup' => $userGroup]);

    $PID = $SSID = 0;
    $qrySel = " SELECT PID, 'proposalInfo' as myOpt FROM tblProposalInfo WHERE LMRId = " . $LMRId . ';';
    $qrySel .= " SELECT SSID, 'shortSaleInfo' as myOpt FROM tblShortSale where LMRId = " . $LMRId . ';';
    $resultArray = Database2::getInstance()->fetchMultiResultSet(['qry' => $qrySel, 'keyParam' => 'myOpt', 'rec' => 'single']);
    if (array_key_exists('proposalInfo', $resultArray)) $PID = trim($resultArray['proposalInfo']['PID']);
    if (array_key_exists('shortSaleInfo', $resultArray)) $SSID = trim($resultArray['shortSaleInfo']['SSID']);

    if (isset($_REQUEST['yearsInProp'])) {
        if ($PID > 0) {
            $qry = " UPDATE tblProposalInfo SET yearsInProp = '" . trim($_REQUEST['yearsInProp']) . "' where LMRId = " . $LMRId . ' and PID = ' . $PID . ';';
            $rsL = Database2::getInstance()->update($qry);
        } else {
            $qry = " INSERT INTO tblProposalInfo(LMRId,yearsInProp) VALUES ('" . $LMRId . "','" . trim($_REQUEST['yearsInProp']) . "');";
            $rsL = Database2::getInstance()->insert($qry);
        }
    }

    $propertyListedDate = '';
    if (isset($_REQUEST['listingDate'])) $propertyListedDate = trim($_REQUEST['listingDate']);
    if (Dates::IsEmpty($propertyListedDate)) {
        $propertyListedDate = '0000-00-00';
    } else {
        $propertyListedDate = Dates::formatDateWithRE($propertyListedDate, 'MDY', 'Y-m-d');
    }
    if ($SSID > 0 && (isset($_REQUEST['listingPrice']) || isset($_REQUEST['listingDate']) || isset($_REQUEST['OPAPhone']) || isset($_REQUEST['offerPropertyAgentName']) || isset($_REQUEST['offerPropertyAgencyName']))) {
        $qry = ' UPDATE tblShortSale SET ';
        $columns = [];
        $params = [];
        $params['LMRId'] = $LMRId;
        $params['SSID'] = $SSID;

        if (isset($_REQUEST['listingPrice'])) {
            $columns [] = " listingPrice = :listingPrice ";
            $params['listingPrice'] = $_REQUEST['listingPrice'];
        }
        if (isset($_REQUEST['listingDate'])) {
            $columns [] = " listingDate = :listingDate ";
            $params['listingDate'] = $propertyListedDate;
        }
        if (isset($_REQUEST['OPAPhone'])) {
            $columns [] = " realtorPhoneNumber = :realtorPhoneNumber ";
            $params['realtorPhoneNumber'] = trim($_REQUEST['OPAPhone']);
        }
        if (isset($_REQUEST['offerPropertyAgentName'])) {
            $columns [] = " realtor = :realtor ";
            $params['realtor'] = trim($_REQUEST['offerPropertyAgentName']);
        }
        if (isset($_REQUEST['offerPropertyAgencyName'])) {
            $columns [] = " agency = :agency ";
            $params['agency'] = trim($_REQUEST['offerPropertyAgencyName']);
        }

        if (sizeof($columns)) {
            $qry .= implode(', ', $columns);
            $qry .= ' WHERE LMRId = :LMRId and SSID = :SSID ;';
            $rsL = Database2::getInstance()->update($qry, $params);
        }
    } else if ($SSID == 0) {
        $qry = ' INSERT INTO tblShortSale(LMRId';

        if (isset($_REQUEST['listingPrice'])) $qry .= ', listingPrice';
        if (isset($_REQUEST['listingDate'])) $qry .= ', listingDate';
        if (isset($_REQUEST['OPAPhone'])) $qry .= ', realtorPhoneNumber';
        if (isset($_REQUEST['offerPropertyAgentName'])) $qry .= ', realtor';
        if (isset($_REQUEST['offerPropertyAgencyName'])) $qry .= ', agency';

        $params = [];
        $params['LMRId'] = $LMRId;

        $qry .= " ) VALUES( :LMRId ";
        if (isset($_REQUEST['listingPrice'])) {
            $qry .= ", :listingPrice ";
            $params['listingPrice'] = $_REQUEST['listingPrice'];
        }
        if (isset($_REQUEST['listingDate'])) {
            $qry .= ", :propertyListedDate ";
            $params['propertyListedDate'] = $propertyListedDate;
        }
        if (isset($_REQUEST['OPAPhone'])) {
            $qry .= ", :OPAPhone ";
            $params['OPAPhone'] = $_REQUEST['OPAPhone'];
        }
        if (isset($_REQUEST['offerPropertyAgentName'])) {
            $qry .= ", :offerPropertyAgentName ";
            $params['offerPropertyAgentName'] = $_REQUEST['offerPropertyAgentName'];
        }
        if (isset($_REQUEST['offerPropertyAgencyName'])) {
            $qry .= ", :offerPropertyAgencyName ";
            $params['offerPropertyAgencyName'] = $_REQUEST['offerPropertyAgencyName'];
        }
        $qry .= ');';
        $rsL = Database2::getInstance()->insert($qry, $params);
    }
    $CIDArray = [];
    $ip = [];
    $realtorID = isset($_POST['realtorID']) ? trim($_POST['realtorID']) : 0;
    $offerPropertyAgentName = isset($_POST['offerPropertyAgentName']) ? trim($_POST['offerPropertyAgentName']) : '';
    $offerPropertyAgencyName = isset($_POST['offerPropertyAgencyName']) ? trim($_POST['offerPropertyAgencyName']) : '';
    $OPAPhone = isset($_POST['OPAPhone']) ? trim($_POST['OPAPhone']) : '';
    if ($realtorID == 0) {
        if ($offerPropertyAgentName != '') {
            $ip[] = ['contactName' => $offerPropertyAgentName, 'companyName' => $offerPropertyAgencyName, 'phone' => $OPAPhone, 'contactType' => '2', 'cRole' => 'Realtor'];
        } else {
            $CIDArray[] = ['CID' => $realtorID, 'cRole' => 'Realtor'];
        }
    } else {
        if ($offerPropertyAgentName != '') {
            $ip[] = ['CID' => $realtorID, 'contactName' => $offerPropertyAgentName, 'companyName' => $offerPropertyAgencyName, 'phone' => $OPAPhone, 'contactType' => '2', 'cRole' => 'Realtor'];
        }
        $CIDArray[] = ['CID' => $realtorID, 'cRole' => 'Realtor'];
    }

    $CIDArray1 = [];
    if (count($ip ?? []) > 0) {
        $inArray = [
            'LMRId'    => $LMRId,
            'PCID'     => $PCID,
            'contacts' => $ip,
        ];
        $CIDArray1 = saveContacts::getReport($inArray);
    }
    $CIDArray = array_merge($CIDArray, $CIDArray1);
    if (count($CIDArray) > 0) {
        saveFileContacts::getReport(['LMRId' => $LMRId, 'CID' => $CIDArray]);
    }


    $repName1 = $_REQUEST['repName1'] ?? '';
    $lender1PhNo = $_REQUEST['lender1PhoneNo'] ?? '';
    $lender1FaxNo = $_REQUEST['lender1FaxNo'] ?? '';
    $lender1Cell = $_REQUEST['lender1Cell'] ?? '';
    $lender1Address = $_REQUEST['lender1Address'] ?? '';
    $lender1City = $_REQUEST['lender1City'] ?? '';
    $lender1State = $_REQUEST['lender1State'] ?? '';
    $lender1Zip = $_REQUEST['lender1Zip'] ?? '';
    $lender1Email = $_REQUEST['lender1Email'] ?? '';
    $lender1Notes = $_REQUEST['lender1Notes'] ?? '';
    $repName2 = $_REQUEST['repName2'] ?? '';
    $lender2PhNo = $_REQUEST['lender2PhoneNo'] ?? '';
    $lender2FaxNo = $_REQUEST['lender2FaxNo'] ?? '';
    $lender2Cell = $_REQUEST['lender2Cell'] ?? '';
    $lender2Address = $_REQUEST['lender2Address'] ?? '';
    $lender2City = $_REQUEST['lender2City'] ?? '';
    $lender2State = $_REQUEST['lender2State'] ?? '';
    $lender2Zip = $_REQUEST['lender2Zip'] ?? '';
    $lender2Email = $_REQUEST['lender2Email'] ?? '';
    $lender2Notes = $_REQUEST['lender2Notes'] ?? '';

    $qrySel = ' select SSID from tblShortSale where LMRId = ' . $LMRId;
    $ssresultArray = [];
    $ssresultArray = Database2::getInstance()->fetchRecords(['qry' => $qrySel, 'rec' => 'single']);
    $SSID = 0;
    if (count($ssresultArray) > 0) {
        $SSID = trim($ssresultArray['SSID']);
    }
    if ($SSID > 0) {
        // update

        $qrySS = " update tblShortSale set lender1RepName = '" . HTTP::escapeQuoteForPOST($repName1) . "',lender1Fax = '" . $lender1FaxNo . "', lender1PhoneNumber = '" . $lender1PhNo . "', lender1Email = '" . $lender1Email . "',lender1Notes = '" . HTTP::escapeQuoteForPOST($lender1Notes) . "' , lender1Cell = '" . $lender1Cell . "' , lender1Address = '" . HTTP::escapeQuoteForPOST($lender1Address) . "', lender1City = '" . $lender1City . "' , lender1State = '" . $lender1State . "', lender1Zip = '" . $lender1Zip . "' , lender2RepName = '" . HTTP::escapeQuoteForPOST($repName2) . "',lender2Fax = '" . $lender2FaxNo . "', lender2PhoneNumber = '" . $lender2PhNo . "', lender2Email = '" . $lender2Email . "',lender2Notes = '" . HTTP::escapeQuoteForPOST($lender2Notes) . "' , lender2Cell = '" . $lender2Cell . "' , lender2Address = '" . HTTP::escapeQuoteForPOST($lender2Address) . "', lender2City = '" . $lender2City . "' , lender2State = '" . $lender2State . "', lender2Zip = '" . $lender2Zip . "' where SSID = " . $SSID . ' and LMRId = ' . $LMRId;
        $cntRep = Database2::getInstance()->update($qrySS);
    } else {
        // insert
        $qrySS = 'insert into tblShortSale(LMRId,lender1RepName,lender1PhoneNumber,lender1Fax,lender1Email,lender1Notes,lender1Cell , lender1Address , lender1City , lender1State , lender1Zip,lender2RepName,lender2PhoneNumber,lender2Fax,lender2Email,lender2Notes,lender2Cell , lender2Address , lender2City , lender2State , lender2Zip ) values(' . $LMRId . ",'" . HTTP::escapeQuoteForPOST($repName1) . "', '" . $lender1PhNo . "','" . $lender1FaxNo . "','" . $lender1Email . "','" . HTTP::escapeQuoteForPOST($lender1Notes) . "','" . $lender1Cell . "','" . HTTP::escapeQuoteForPOST($lender1Address) . "','" . $lender1City . "','" . $lender1State . "','" . $lender1Zip . "','" . HTTP::escapeQuoteForPOST($repName2) . "', '" . $lender2PhNo . "','" . $lender2FaxNo . "','" . $lender2Email . "','" . HTTP::escapeQuoteForPOST($lender2Notes) . "','" . $lender2Cell . "','" . HTTP::escapeQuoteForPOST($lender2Address) . "','" . $lender2City . "','" . $lender2State . "','" . $lender2Zip . "' ) ";
        $cntRep = Database2::getInstance()->insert($qrySS);
    }
}


$noOfPropShown = '';
$typeOfHMLOLoanRequesting = '';
$annualPropTaxes1 = 0;
$isHMLOOpt = $_POST['isHMLOOpt'] ?? '';
if (isset($_POST['noOfPropertiesAcquiring'])) $noOfPropShown = trim($_POST['noOfPropertiesAcquiring']);
if (isset($_POST['annualPropTaxes1'])) $annualPropTaxes1 = floatval(trim(Strings::replaceCommaValues($_POST['annualPropTaxes1'])));
if (isset($_POST['typeOfHMLOLoanRequesting'])) $typeOfHMLOLoanRequesting = trim($_POST['typeOfHMLOLoanRequesting']);

if ($noOfPropShown == '') {
    if (isset($_POST['noOfPropertiesAcquiring_mirror'])) $noOfPropShown = trim($_POST['noOfPropertiesAcquiring_mirror']);
}

for ($i = 2; $i <= $noOfPropShown; $i++) {
    if (isset($_POST['taxes1' . $i])) {
        $annualPropTaxes1 += floatval(trim(Strings::replaceCommaValues($_POST['taxes1' . $i])));
    }
}

if ($typeOfHMLOLoanRequesting == 'Blanket Loan') {
    $taxes1 = $annualPropTaxes1;
    $_POST['taxes1'] = $annualPropTaxes1;
}

if ($LMRId > 0 && ($isHMLOOpt == 1)) {

    $HMLOBlanketLoanPropInfo = [
        'p'             => $_POST,
        'LMRId'         => $LMRId,
        'noOfPropShown' => $noOfPropShown,
    ];
    saveBlanketLoanPropInfo::getReport($HMLOBlanketLoanPropInfo);
}

if (isset($_REQUEST['rentroll'])) {
    $ipArray['FileCreatedDate'] = $_POST['recordDate'];
    $ipArray['LMRId'] = $LMRId;
    $ipArray['PCID'] = $_POST['FPCID'];
    saveFileRentRoll::getReport($ipArray);
}

//check if the PC is enabled for Automation
if ($allowAutomation) { //$allowAutomation set in getPageVariables.php
    include('automatedRulesActionController.php');
}

//CV3 sync Processing & Underwriting Fee to the HUD tab
//File Create Only
if (glCustomJobForProcessingCompany::isPC_CV3(intval($PCID)) && !$postedLMRId) {
    LMRequest::$LMRId = intval($LMRId);
    LMRequest::$PCID = intval($PCID);
    HUDController::getUnderwritingFeeHUD($PCID);
    HUDController::getProcessingFeeHUD($PCID);
}

//CV3 sync Processing & Underwriting Fee to the HUD tab | New logic
if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
    LMRequest::$LMRId = $LMRId;
    LMRequest::$PCID = $PCID;
    HUDController::getSetUnderwritingFeeHUD();
    HUDController::getSetProcessingFeeHUD();
}

HUDController::getFeasibilityFeeHUD($PCID);
//re-calculate HUD
HUDCalculation::process((int)$LMRId);

if (isset($_POST['MERSID'])) {
    $lmrRes->getTblFileHMLONewLoanInfo_by_fileID()->MERSID = Request::GetClean('MERSID');
    $lmrRes->getTblFileHMLONewLoanInfo_by_fileID()->Save();
}

tblAutomatedRuleRequestV2::Trigger((int)$LMRId, (int)$PCID); // last
CustomField::Save(
    tblFile::class,
    intval($LMRId),
    intval($PCID),
    intval(PageVariables::$userNumber ?: 0),
    PageVariables::$userRole ?: '',
    PageVariables::$userGroup ?: ''
);
Database2::saveLogQuery();

if ($cnt > 0) {
    if (!Strings::GetSess('msg')) {
        Strings::SetSess('msg', 'Updated Successfully');
    }
} else  Strings::SetSess('msg', 'Error while save.');

if ($userRole == 'Branch') $redirect = CONST_URL_BRSSL;
else if ($userRole == 'Agent') $redirect = CONST_URL_AG_SSL;
else if (($userRole == 'Client') && (preg_match('/client_new\//i', $_SERVER['HTTP_REFERER']))) $redirect = CONST_URL_CL_NEW_SSL;
else if ($userRole == 'Client') $redirect = CONST_URL_CL_SSL;
else    $redirect = CONST_URL_BOSSL;

$redirect .= 'LMRequest.php?eId=' . cypher::myEncryption($executiveId) . '&lId=' . cypher::myEncryption($LMRId) . '&rId=' . cypher::myEncryption($responseId) . '&op=' . trim($op) . '&tabOpt=';
if ($btnValue == 'Save' || $isSave == 1) $redirect .= $activeTab;
else $redirect .= $goToTab;

if ((trim($_POST['OSID']) != trim($_POST['primaryStatus'])) && $PCID == 4069 && $_POST['primaryStatus'] == 78881) {
    $salesForceID = $_POST['projectName'] ?? '';
    if ($salesForceID != '') {
        postDataToEndPoint::getReport(['salesForceID' => $salesForceID]);
    }
}
header('Location: ' . $redirect);

exit();
