<?php
namespace pages\backoffice\loan\servicing;

use models\Controllers\backoffice\LMRequest;
use models\cypher;
use models\LoanMenu;
use models\PageVariables;
use models\Request;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

LoanMenu::preInit();

LoanMenu::initForms();


?>

<div id="divLoader"><img src="<?php echo IMG_PROGRESS_BAR; ?>" alt=""></div>
<div class="subheader py-0 py-lg-6 subheader-solid" id="kt_subheader">
    <div class="container-fluid d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
        <?php

        //permission to view, $shareThisFile value assigned in getPageVariables.php


        if (LoanMenu::$LMRId == 0 && LoanMenu::$isSLM == 1) {
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6
                        class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    Create Student <PERSON><PERSON>d File</h6></div>
            <?php
        } else if (LoanMenu::$PCID == 170 && LoanMenu::$LMRId == 0) {
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6
                        class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    Debt Revision Modification Short Sale Portal</h6></div>
            <?php
        } else if (LoanMenu::$PCID == 1434 && LoanMenu::$branchReferralCode == '365320') {
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6
                        class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    Create LDA Report File</h6></div>
            <?php
        } else if (LoanMenu::$LMRId == 0) { /* Changed the Label for PC = THE	FAME GROUP = 1494  04 May, 2016 */
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6
                        class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    <?php if ((LoanMenu::$PCID == 2 || LoanMenu::$PCID == 820 || LoanMenu::$PCID == 1494) && (PageVariables::$publicUser == 1 && LoanMenu::$agentReferralCode > 1)) { ?>
                        Create Lead Application
                    <?php } else { ?>
                        Create Loan File
                    <?php } ?>
                </h6></div>
            <?php
        } else {
            if (LoanMenu::$LMRId > 0) { ?>
                <div class="d-flex align-items-center flex-wrap mr-2">
                    <?php
                    if (LoanMenu::$isHOALien == 1 && PageVariables::$publicUser != 1) {
                        ?>
                        <h6 class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                            File <?php if (trim(LoanMenu::$propertyAddress)) echo ' of ' . ucwords(LoanMenu::$propertyAddress); ?>
                        </h6>
                        <?php
                    } else {
                        ?>
                        <h6 class="text-dark font-weight-bold mt-2 mb-2 mr-2">
                            <?php echo LoanMenu::getFileTitle(); ?>
                        </h6>
                        <?php
                    } ?>
                    <i data-title='<?php echo htmlentities(LoanMenu::$borrowerName); ?>'
                       data-content='<?php echo htmlentities(LoanMenu::$BorInfoMO, ENT_QUOTES | ENT_SUBSTITUTE | ENT_DISALLOWED) ?>'
                       data-placement="bottom"
                       class="fas fa-info-circle text-primary manualPopover">
                    </i>
                </div>
                <div class="d-flex align-items-center flex-wrap">
                    <?php
                    if (LoanMenu::$isMF == 1) {
                        LoanMenu::$glNotesTypeArray = LoanMenu::$glFUModulesNotesTypeArray;
                    }

                    $processorCommentsNotes = '';

                    if (count(LoanMenu::$processorCommentsArray) > 0) {
                        $notesEmpInfo = LMRequest::myFileInfo()->notesEmpInfo(LoanMenu::$viewPrivateNotes, LoanMenu::$viewPublicNotes);
                        $notesAgentInfo = LMRequest::myFileInfo()->notesAgentInfo(LoanMenu::$viewPrivateNotes, LoanMenu::$viewPublicNotes);
                        $notesBranchInfo = LMRequest::myFileInfo()->notesBranchInfo(LoanMenu::$viewPrivateNotes, LoanMenu::$viewPublicNotes);
                        $notesClientInfo = LMRequest::myFileInfo()->notesClientInfo(LoanMenu::$viewPrivateNotes, LoanMenu::$viewPublicNotes);

                        $nCnt = 0;
                        foreach (LoanMenu::$processorCommentsArray as $i => $comment) {
                            $commentsBy = '';
                            $processorCommentsDetail = '';

                            $processorComments = Strings::replaceProcessedHeader(rawurldecode(trim($comment['processorComments'])));
                            $notesDate = trim($comment['notesDate']);
                            $privateNotes = trim($comment['private']);
                            $notesEmpId = trim($comment['employeeId']);
                            $notesExeId = trim($comment['executiveId']);
                            $notesAgentId = trim($comment['brokerNumber']);
                            $notesClientId = trim($comment['clientId']);
                            $notesType = trim($comment['notesType']);
                            $isSysNotes = trim($comment['isSysNotes']);
                            $processorName = trim($comment['processorName']);
                            $updatedUserType = trim($comment['updatedUserType']);

                            if (array_key_exists($notesType, LoanMenu::$glNotesTypeArray ?? [])) {
                                $notesType = LoanMenu::$glNotesTypeArray[$notesType];
                            }

                            if ($notesEmpId > 0) {
                                if (array_key_exists($notesEmpId, $notesEmpInfo)) $commentsBy = trim($notesEmpInfo[$notesEmpId]['processorName']);
                            } elseif ($notesExeId > 0) {
                                if (array_key_exists($notesExeId, $notesBranchInfo)) $commentsBy = trim($notesBranchInfo[$notesExeId]['LMRExecutive']);
                            } elseif ($notesAgentId > 0) {
                                if (array_key_exists($notesAgentId, $notesAgentInfo)) $commentsBy = trim($notesAgentInfo[$notesAgentId]['agentName']);
                            } elseif ($notesClientId > 0) {
                                if (array_key_exists($notesClientId, $notesClientInfo)) $commentsBy = trim($notesClientInfo[$notesClientId]['clientName']);
                            } elseif ($processorName) {
                                $commentsBy = $processorName . '- (' . $updatedUserType . ')';
                            } else {
                                $commentsBy = 'Admin';
                            }
                            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                            $ipArray['outputZone'] = PageVariables::$userTimeZone;
                            $ipArray['inputTime'] = $notesDate;
                            $notesDate = Dates::timeZoneConversion($ipArray);
                            $notesDate = Dates::formatDateWithRE($notesDate, 'YMD_HMS', 'M j, Y h:i A') . ' - ' . PageVariables::$userTimeZone;


                            if ($comment['displayIn'] == 'NH' || $comment['displayIn'] == 'BO' || $comment['displayIn'] == 'SH') {
                                $processorCommentsDetail .= '<span class="font-weight-bold">' . trim($processorComments) . '</span>';
                                if (!preg_match('/Request Status Update:/i', $processorComments)) {
                                    if ($commentsBy != 'Admin') {
                                        $processorCommentsDetail .= "<span class='d-flex flex-row mt-1'><span class=\"text-muted flex-fill ml-auto pl-4 col-md-8\">- " . $commentsBy . ' (' . $notesDate . ')</span>';
                                        $processorCommentsDetail .= "<span class=\"badge label label-inline float-right   bg-warning-o-50 flex-fill ml-auto col-md-4 text-truncate\">" . $notesType . '</span></span>';
                                    } else {
                                        $processorCommentsDetail .= "<span class='d-flex flex-row mt-1'><span class=\"text-muted flex-fill ml-auto pl-4 col-md-12\">- " . $commentsBy . ' (' . $notesDate . ')</span>';
                                    }
                                }
                            }
                            if (($i + 1) % 2 == 0) {
                                $clsName = 'bg-gray-100';
                            } else {
                                $clsName = '';
                            }
                            if (LoanMenu::$showSysGenNote == 0 && $isSysNotes == 1) {
                                doNothing();
                            } else {
                                if (PageVariables::$viewPrivateNotes == 1) {
                                    $nCnt++;
                                    $processorCommentsNotes .= "<li class=\"list-group-item px-2 py-1 " . $clsName . " \">" . $processorCommentsDetail . '</li>';
                                } else if ($privateNotes == 0) {
                                    $nCnt++;
                                    $processorCommentsNotes .= "<li class=\"list-group-item px-2 py-1 " . $clsName . " \">" . $processorCommentsDetail . '</li>';
                                }

                            }

                            if ($nCnt == 10) break;
                        }
                    }
                    if (PageVariables::$publicUser == 1
                        || PageVariables::$userRole == 'CFPB Auditor'
                        || PageVariables::$userRole == 'Auditor Manager'
                        || (PageVariables::$allowCFPBAuditing == 1 && LoanMenu::$allowUserToUpdateCFPBFile == 1 && LMRequest::myFileInfo()->LMRInfo()->FPCID != LoanMenu::$PCID)
                    ) { /* CFPB auditor disable notes icon START */
                        doNothing();
                    } else {
                        if (LoanMenu::$viewPrivateNotes == 1 || LoanMenu::$viewPublicNotes == 1) {

                            if ($processorCommentsNotes == '') {
                                $commentIcon = 'fa-comment-medical'; ?>
                                <?php
                            } else {
                                $commentIcon = 'fa-comments'; ?>
                                <?php
                            }
                            $processorCommentHeaderButton = '<a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                                            href="#"
                                            id="notesIcon"                                            
                                            data-href="' . CONST_URL_POPS . 'addNotes.php"
                                            data-wsize = "modal-xl"
                                            data-name="File: ' . htmlentities(LoanMenu::$borrowerName) . '> Add Note"
                                            data-toggle="modal" data-target="#exampleModal1"
                                            data-id="rId=' . cypher::myEncryption(LoanMenu::$LMRResponseId) . '&exID=' . cypher::myEncryption(LoanMenu::$executiveId) . '&LMRId=' . cypher::myEncryption(LoanMenu::$LMRId) . '&opt=fileTab&FPCID=' . cypher::myEncryption(LMRequest::myFileInfo()->LMRInfo()->FPCID) . '&UR=' . cypher::myEncryption(PageVariables::$userRole) . '&isHMLO=' . PageVariables::$isHMLO . '&showSaveBtn=1"><i class="icon-md fas ' . $commentIcon . '  "></i> </a>';


                            $processorCommentsNotesList = '<ul class="list-group p-1 font-size-sm">' . Strings::stripQuote($processorCommentsNotes) . '</ul>';
                            $processorCommentsNotesFinal = '<div class="card card-custom">
 <div class="card-header">
 <div class="card-title">
        <h3 class="card-label">Latest 10 notes are listed below</h3>
</div>
        <div class="card-toolbar">
            ' . $processorCommentHeaderButton . '
        </div>
 </div>
 <div class="card-body p-0 note-scrollbar" id="note-style-3">
     <div class="note-force-overflow">
     ' . $processorCommentsNotesList . '
     </div>
 </div>
</div>';

                            ?>
                            <span id="divListNotes<?php echo LoanMenu::$LMRId ?>">
                <span id="status-bar">
                    <span class="status-infos">
                        <span class="lireplace">
                            <?php echo $processorCommentHeaderButton; ?>
                            <div class="result-block card-body p-0 "><span class="arrow"></span>
                                <?php echo $processorCommentsNotesFinal; ?>
                            </div>
                        </span>
                    </span>
                </span>
            </span>

                            <?php


                        }
                    }
                    /**
                     * Allow Loan version Email Send to Client. pivotal #154715300
                     */

                    if (PageVariables::$userGroup != 'Client' && LoanMenu::$LMRId > 0) {
                        LoanMenu::$allowToSendBorrower = 1;
                        LoanMenu::$allowToSendAgent = 1;
                        ?>


                        <a data-href="<?php echo CONST_URL_POPS; ?>sendFileEmail.php"
                           data-name="<?php echo htmlentities(LoanMenu::$borrowerName); ?> > Send An Email"
                           data-wsize='modal-xl'
                           data-id="LMRId=<?php echo cypher::myEncryption(LoanMenu::$LMRId) ?>&ft=<?php echo cypher::myEncryption(LoanMenu::$fileModuleCodeForWebForm); ?>&FPCID=<?php echo cypher::myEncryption(LMRequest::myFileInfo()->LMRInfo()->FPCID) ?>&BO=<?php echo cypher::myEncryption(LoanMenu::$allowToSendBorrower) ?>&UType=<?php echo cypher::myEncryption(PageVariables::$userGroup) ?>&BR=<?php echo cypher::myEncryption(LoanMenu::$allowToSendAgent) ?>"
                           data-toggle='modal' data-target='#exampleModal1' href='#'
                           data-content="Compose Email"
                           data-placement="bottom"
                           class=" popoverClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 p-0"
                           id="sendEmail">
                   <span class="svg-icon svg-icon svg-icon-2x"><svg
                               xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                               width="24px" height="24px" viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,12 C19,12.5522847 18.5522847,13 18,13 L6,13 C5.44771525,13 5,12.5522847 5,12 L5,3 C5,2.44771525 5.44771525,2 6,2 Z M7.5,5 C7.22385763,5 7,5.22385763 7,5.5 C7,5.77614237 7.22385763,6 7.5,6 L13.5,6 C13.7761424,6 14,5.77614237 14,5.5 C14,5.22385763 13.7761424,5 13.5,5 L7.5,5 Z M7.5,7 C7.22385763,7 7,7.22385763 7,7.5 C7,7.77614237 7.22385763,8 7.5,8 L10.5,8 C10.7761424,8 11,7.77614237 11,7.5 C11,7.22385763 10.7761424,7 10.5,7 L7.5,7 Z"
              fill="#000000" opacity="0.3"/>
        <path d="M3.79274528,6.57253826 L12,12.5 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 Z"
              fill="#000000"/>
    </g>
</svg></span> </a>


                        <a data-href="<?php echo CONST_URL_POPS; ?>mailHistory.php"
                           data-name="Automation Emails/Email History"
                           data-wsize='modal-xl'
                           data-footerhide='hide'
                           data-id="LMRId=<?php echo cypher::myEncryption(LoanMenu::$LMRId) ?>&amp;ft=<?php echo cypher::myEncryption(LoanMenu::$fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(LMRequest::myFileInfo()->LMRInfo()->FPCID) ?>&amp;BO=<?php echo cypher::myEncryption(LoanMenu::$allowToSendBorrower) ?>&amp;UType=<?php echo cypher::myEncryption(PageVariables::$userGroup) ?>&amp;BR=<?php echo cypher::myEncryption(LoanMenu::$allowToSendAgent) ?>"
                           data-toggle='modal' data-target='#exampleModal1' href='javascript:void(0)'
                           data-content="Scheduled Emails/Email History"
                           data-placement="bottom"
                           class=" popoverClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 p-0"
                           id="mailHistory">
                   <span class="svg-icon svg-icon svg-icon-2x"><!--begin::Svg Icon | path:C:\wamp64\www\keenthemes\themes\metronic\theme\html\demo1\dist/../src/media/svg/icons\Communication\Snoozed-mail.svg--><svg
                               xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                               width="24px" height="24px" viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M12.9835977,18 C12.7263047,14.0909841 9.47412135,11 5.5,11 C4.98630124,11 4.48466491,11.0516454 4,11.1500272 L4,7 C4,5.8954305 4.8954305,5 6,5 L20,5 C21.1045695,5 22,5.8954305 22,7 L22,16 C22,17.1045695 21.1045695,18 20,18 L12.9835977,18 Z M19.1444251,6.83964668 L13,10.1481833 L6.85557487,6.83964668 C6.4908718,6.6432681 6.03602525,6.77972206 5.83964668,7.14442513 C5.6432681,7.5091282 5.77972206,7.96397475 6.14442513,8.16035332 L12.6444251,11.6603533 C12.8664074,11.7798822 13.1335926,11.7798822 13.3555749,11.6603533 L19.8555749,8.16035332 C20.2202779,7.96397475 20.3567319,7.5091282 20.1603533,7.14442513 C19.9639747,6.77972206 19.5091282,6.6432681 19.1444251,6.83964668 Z"
              fill="#000000"/>
        <path d="M8.4472136,18.1055728 C8.94119209,18.3525621 9.14141644,18.9532351 8.89442719,19.4472136 C8.64743794,19.9411921 8.0467649,20.1414164 7.5527864,19.8944272 L5,18.618034 L5,14.5 C5,13.9477153 5.44771525,13.5 6,13.5 C6.55228475,13.5 7,13.9477153 7,14.5 L7,17.381966 L8.4472136,18.1055728 Z"
              fill="#000000" fill-rule="nonzero" opacity="0.3"/>
    </g>
</svg><!--end::Svg Icon--></span>
                        </a>


                        <?php //} ?>

                        <?php
                    }

                    if (PageVariables::$userGroup == 'Client' && !(array_key_exists(LoanMenu::$primaryStatusId, LoanMenu::$permissionsToEdit))) {
                        ?>
                        <a data-href="<?php echo CONST_URL_POPS; ?>requestToAllowEditFile.php"
                           href="javascript:void(0)"
                           data-name="File: <?php echo LoanMenu::$borrowerName; ?> > Request To Update File"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 tooltipClass"
                           title="Request To Update File" style="text-decoration: none;" data-toggle='modal'
                           data-target='#exampleModal1'
                           data-wsize='modal-xl'
                           data-id="rId=<?php echo cypher::myEncryption(LoanMenu::$LMRResponseId); ?>&exID=<?php echo cypher::myEncryption(LoanMenu::$executiveId); ?>&LMRId=<?php echo cypher::myEncryption(LoanMenu::$LMRId); ?>&UR=<?php echo cypher::myEncryption(PageVariables::$userRole); ?>"><i
                                    class="fa fa-edit"></i></a>
                        <?php
                    }

                    if (in_array(LoanMenu::$PCID, LoanMenu::$showAssignedEmployeeToPC) && LoanMenu::$activeTab == 'CI') {
                        if (LoanMenu::$allowToUpdateFileAdminSection == 1
                            && LoanMenu::$op != 'view'
                            && PageVariables::$userRole != 'Auditor'
                        ) {
                            $assignedEmpLink = '';
                            $assignAppendComma = '';


                            if (count(LMRequest::myFileInfo()->AssignedBOStaffInfo()) == 0) {
                                $assignedEmpLink = 'Click to assign employee';
                            }

                            foreach (LMRequest::myFileInfo()->AssignedBOStaffInfo() as $item) {
                                $assignedEmpLink .= '<span id=ass_' . trim($item['AID']) . '>';
                                $assignedEmpLink .= $assignAppendComma;

                                if (trim($item['activeStatus']) == 0) {
                                    $assignedEmpLink .= '<span class=red>';
                                }

                                if (trim(trim($item['role']))) {
                                    $assignedEmpLink .= '<b>' . trim($item['role']) . ': </b> ';
                                }
                                $assignedEmpLink .= trim($item['processorName']);

                                if (trim($item['activeStatus']) == 0) {
                                    $assignedEmpLink .= '</span>';
                                }
                                $assignedEmpLink .= '</span><br>';
                            }
                            ?>
                            <div class="left padl5">
                                <div class="left with-children-tip pad5"><a
                                            class="fa fa-users fa-2x tip-bottom popuplink asgn_emp"
                                            style="text-decoration:none;"
                                            href="<?php echo CONST_URL_POPS; ?>assignEmployee.php"
                                            name=" <?php echo LoanMenu::$borrowerName ?> > Assigning Employees"
                                            id="LMRId=<?php echo cypher::myEncryption(LoanMenu::$LMRId) ?>&amp;PCID=<?php echo cypher::myEncryption(LoanMenu::$PCID) ?>&amp;BRID=<?php echo cypher::myEncryption(LoanMenu::$executiveId) ?>&tab=CI"
                                            title="<?php echo $assignedEmpLink ?>"></a></div>
                                <div id="divListAssignEmp"
                                     style="display: none;"><?php echo $assignedEmpLink ?></div>
                            </div>
                            <?php
                        }
                    }
                    if (trim(LoanMenu::$mortgageNotes) && PageVariables::$userGroup != 'Client' && PageVariables::$publicUser != 1) { ?>

                        <a type="button"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 manualPopover "
                           data-html="true"
                           data-trigger="focus"
                           data-placement="left"
                           data-title='Client Notes'
                           data-content='<?php echo Strings::escapeQuoteNew(nl2br(LoanMenu::$mortgageNotes)); ?>'
                           id="clientNotes">
                            <i
                                    class="flaticon2-file-1 "></i>
                        </a>


                        <?php
                    }
                    if (PageVariables::$userGroup != 'Client' && PageVariables::$publicUser != 1) {
                        if (in_array(LoanMenu::$PCID, LoanMenu::$ShowSaleDate) && LoanMenu::$salesDate) {
                            ?>
                            <div class="left pad10"><h4>Sales Date: <?php echo LoanMenu::$salesDate ?> </h4></div>
                            <?php
                        }
                        if (in_array(LoanMenu::$PCID, LoanMenu::$showTotalOwedAndOutstandingBalance) && LoanMenu::$totalBalanceOwed > 0) {
                            ?>
                            <div class="left pad10 <?php echo LoanMenu::$clsColor ?>">
                                <span><b>Outstanding Balance: <?php echo Currency::formatDollarAmountWithDecimal(LoanMenu::$totalBalanceOwed) ?> </b></span>
                            </div>
                            <?php
                        }
                    }


                    if (PageVariables::$isHMLO == 1) {
                        if (PageVariables::$publicUser != 1 && PageVariables::$userGroup != 'Client' && PageVariables::$allowToEdit) {
                            ?>
                            <label for="file-download" style="margin-top: 7px;">
                                <a title="Fannie Mae 3.4 Export"
                                   href="/mismo/fm34Export.php?lId=<?php echo cypher::myEncryption(LoanMenu::$LMRId); ?>"
                                   class="tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                                   id="fm34Export"><img src="/assets/images/fm32red.png" style="width:20px"></a>
                            </label>
                            <label for="file-upload" class="" style="margin-top: 7px;">
                                <a title="Fannie Mae 3.4 Import"
                                   href="/backoffice/loan/fm34?eId=&lId=<?php echo Request::GetClean('lId'); ?>&rId=&tabOpt=fm34"
                                   class="tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                                   id="fm34Import"><img
                                            src="/assets/images/fm32blue.png" style="width:20px"></a>
                            </label>                            <?php
                        }
                    }

                    if (LoanMenu::$LMRId > 0 && PageVariables::$shareThisFile == 1 || PageVariables::$userRole == 'Super') { ?>

                        <!-- Button trigger modal -->
                        <a data-id="LMRId=<?php echo cypher::myEncryption(LoanMenu::$LMRId); ?>&amp;ft=<?php echo cypher::myEncryption(LoanMenu::$fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(LMRequest::myFileInfo()->LMRInfo()->FPCID); ?>&amp;BO=<?php echo cypher::myEncryption(LoanMenu::$allowToSendBorrower); ?>&amp;UType=<?php echo cypher::myEncryption(PageVariables::$userGroup); ?>&amp;BR=<?php echo cypher::myEncryption(LoanMenu::$allowToSendAgent); ?>"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 popoverClass"
                           data-href="/backoffice/loan/share_this_file"
                           data-toggle='modal' data-target='#exampleModal1'
                           data-footerhide="true"
                           data-wsize='modal-xl'
                           data-content="Share This File"
                           data-placement="bottom"
                           data-name='Share This File'
                           id="shareThisFile">
                            <i class="fa fa-share" aria-hidden="true"></i>
                        </a>
                        <!-- POC Update popup link -->
                        <a data-id="LMRId=<?php echo cypher::myEncryption(LoanMenu::$LMRId); ?>&amp;ft=<?php echo cypher::myEncryption(LoanMenu::$fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(LMRequest::myFileInfo()->LMRInfo()->FPCID); ?>&amp;BO=<?php echo cypher::myEncryption(LoanMenu::$allowToSendBorrower); ?>&amp;UType=<?php echo cypher::myEncryption(PageVariables::$userGroup); ?>&amp;BR=<?php echo cypher::myEncryption(LoanMenu::$allowToSendAgent); ?>"
                           class="pocPopUp hidden"
                           data-href="<?php echo CONST_URL_POPS; ?>submitOfferPopUp.php"
                           data-toggle='modal' data-target='#exampleModal2'
                           data-wsize='modal-xl' data-footerhide="false"
                           data-content="Update Point of contact"
                           data-placement="bottom"
                           data-name='Update Point of contact'>
                            POC
                        </a>


                    <?php }

                    if (LoanMenu::$LMRId > 0) {

                        if (LoanMenu::$addedToFav == 1) { ?>
                            <a data-html="true" title="Remove from favorites"
                               data-placement="bottom"
                               class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 addAsFav tooltipClass"
                               data-file-id="<?php echo cypher::myEncryption(LoanMenu::$LMRId); ?>"
                               data-opt="<?php echo('remove') ?>"
                               id="favoriteRemove">
                                <i class="fa fa-star text-warning"></i>
                            </a>
                        <?php } else { ?>
                            <a data-html="true" title="Add to favorites"
                               data-placement="bottom"
                               class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 addAsFav tooltipClass"
                               data-file-id="<?php echo cypher::myEncryption(LoanMenu::$LMRId); ?>"
                               data-opt="<?php echo('add') ?>"
                               id="favoriteAdd">
                                <i class="fa fa-star"></i>
                            </a>
                        <?php }
                    } ?>
                </div>
            <?php }
        }
        if (LoanMenu::$LMRId == 0 && PageVariables::$publicUser != 1 && PageVariables::$userGroup != 'Client' && PageVariables::$allowToEdit && (
                Request::GetClean('tabOpt') == 'LI'
                || Request::GetClean('tabOpt') == 'QAPP'
                || Request::GetClean('tabOpt') == 1003
            )) { // I could specify tabs but people cant reach any except fa and qa, so it's fine.
            ?>
            <label for="file-upload" class="" style="margin-top: 7px;">
                <a title="Fannie Mae 3.4 Import"
                   href="/backoffice/loan/fm34?eId=&lId=&rId=&tabOpt=fm34"
                   class="tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"><img
                            src="/assets/images/fm32blue.png" style="width:20px"></a>
            </label>
        <?php }
        ?>
    </div>
</div>
