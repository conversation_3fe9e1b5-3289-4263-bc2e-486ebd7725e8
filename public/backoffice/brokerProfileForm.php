<?php
global $PCID, $loanOfficerId;

use models\composite\oChecklist\getBrokerRequiredDocs;
use models\composite\oThirdPartyServices\getThirdPartyServicesUserDetails;
use models\constants\accessRestrictionPC;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glApprovedReferralFee;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPCID;
use models\constants\gl\glRAMAccessPC;
use models\constants\gl\glThirdPartyServicesCRA;
use models\constants\gl\glTypeOfLoansOffered;
use models\constants\gl\glUserGroup;
use models\constants\gl\glYearsOfExperience;
use models\constants\methodOfContactArray;
use models\constants\SMSServiceProviderArray;
use models\constants\timeZoneArray;
use models\Controllers\backoffice\createAgent;
use models\lendingwise\tblCompanyLicenseDetails;
use models\lendingwise\tblPersonalLicenseDetails;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$glRAMAccessPC = glRAMAccessPC::$glRAMAccessPC;
$accessRestrictionPC = accessRestrictionPC::$accessRestrictionPC;
$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$methodOfContactArray = methodOfContactArray::$methodOfContactArray;
$timeZoneArray = timeZoneArray::$timeZoneArray;
$glBrokerPartnerTypeArray = glCustomJobForProcessingCompany::getBrokerPartnerType($PCID, createAgent::$publicUser);
$glTypeOfLoansOffered = glTypeOfLoansOffered::$glTypeOfLoansOffered;
$glYearsOfExperience = glYearsOfExperience::$glYearsOfExperience;
$glApprovedReferralFee = glApprovedReferralFee::$glApprovedReferralFee;

$allowAutomation = createAgent::Processor()->allowAutomation;

$stateArray = Arrays::fetchStates();

/*
 * If allowEmpToCreateAgent is active or 1, Logged-in user are able to create and edit Loan officer mortgage broker.
 * Suresh Kasinathan changed on March 15, 2019.
 */
$docArray = [];
$docArray['PCID'] = $PCID;

$docDetails = getBrokerRequiredDocs::getReport($docArray);

if (createAgent::$agentType == 0) {
    $ttTitle = 'Broker';
    createAgent::$externalBroker = 0;
} else {
    $ttTitle = 'Loan Officer';
    createAgent::$externalBroker = 1;
}
if (!createAgent::$brokerNo && glCustomJobForProcessingCompany::isPC_CRB($PCID)) {
    createAgent::$typeOfLoansOffered = [3]; // CRE Lending
}

createAgent::$typeOfLoansOffered ??= [];


$approvedReferralFee = createAgent::Agent()->approvedReferralFee;

$glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
$thirdPartyServiceCSRArray = explode(',', PageVariables::$thirdPartyServiceCSR);
$thirdPartyServicesUserDetails = getThirdPartyServicesUserDetails::getReport('Agent', createAgent::Agent()->userNumber);

?>
<div class="card card-custom mb-2">
    <div class="card-header card-header-tabs-line bg-gray-100 agentLoanOfficerCard">
        <div class="card-title">
            <?php if (createAgent::$agentType == 1) {
                echo 'Loan Officer Info';
            } else {
                echo 'Broker Info';
            } ?>
        </div>
        <div class="card-toolbar ">
            <a href="javascript:void(0);"
               class="tooltipClass card-title btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1"
               data-card-tool="toggle"
               data-section="agentLoanOfficerCard"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
               data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>
    <div class="card-body agentLoanOfficerCard_body accordion">
        <div class="row">
            <div class="col-lg-6 mb-7 partner_type <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>" style="<?php if (createAgent::$externalBroker == 1) {
                echo 'display:none;';
            } ?>">
                <label class="font-weight-bold" for="brokerPartnerType">Broker/Partner Type</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <select name="brokerPartnerType" id="brokerPartnerType"
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileDataInputs($PCID, createAgent::$externalBroker) ?>"
                            <?php if ($PCID == glPCID::PCID_PROD_CV3) { ?>onchange="validateFields(this.value)" <?php } else { ?> onchange="Agent.showAndHideLicenseeFields(this.value)" <?php } ?>>
                        <option value="">- Select -</option>
                        <?php
                        foreach ($glBrokerPartnerTypeArray as $bpKey => $myBrokerPartnerType) { ?>
                            <option value="<?php echo $bpKey; ?>" <?php echo Arrays::isSelected($bpKey, createAgent::Agent()->brokerPartnerType); ?>><?php echo $myBrokerPartnerType; ?></option>
                        <?php } ?>
                    </select>
                <?php } else {
                    echo '<h5>' . $glBrokerPartnerTypeArray[createAgent::Agent()->brokerPartnerType] . '</h5>';
                } ?>
            </div>

        </div>
        <div class="form-group row">
            <!--    <div class="text-on-pannel text-primary">Loan Officer/Broker</div>-->
            <div class="col-lg-6">
                <label class="font-weight-bold" for="email">Email<span class="required" aria-required="true"> * </span></label>
                <?php if (createAgent::$brokerNo > 0) { ?>
                    <div class="input-group">
                        <input class="form-control form-controller-solid  datatable-input"
                               type="text"
                               value="<?php echo createAgent::Agent()->email; ?>"
                               disabled>
                        <input type="hidden"
                               name="email"
                               id="email"
                               value="<?php echo createAgent::Agent()->email; ?>">
                        <div class=\"input-group-append\">
                            <span class=\"input-group-text\">
                                <a data-id="userId=<?php echo cypher::myEncryption(createAgent::$brokerNo); ?>&userType=<?php echo cypher::myEncryption('Agent'); ?>&pcid=<?php echo cypher::myEncryption($PCID); ?>&email=<?php echo createAgent::Agent()->email; ?>"
                                   href=''
                                   data-href="<?php echo CONST_URL_POPS . "changeEmailPopup.php"; ?>"
                                   data-name="<?php echo htmlspecialchars(createAgent::Agent()->firstName . ' ' . createAgent::Agent()->lastName); ?> > Change Email "
                                   data-toggle='modal'
                                   data-target='#exampleModal1'><i class='fas fa-pencil-alt'
                                                                   title="Click here to change email address"></i>
                                </a>
                            </span>
                        </div>
                    </div>
                <?php } else { ?>
                    <input class="form-control  mandatory" type="text" name="email" id="email"
                           onblur="return checkAgentEmailExists()" autocomplete="off">
                <?php } ?>
            </div>

            <?php if (createAgent::$brokerNo > 0) {
            } else { ?>
                <div class="col-lg-6">
                    <label class="font-weight-bold" for="emailConfirm">Confirm Email<span class="required"
                                                                                          aria-required="true"> * </span></label>
                    <input class="form-control  mandatory" type="text" name="emailConfirm" id="emailConfirm"
                           autocomplete="off">
                </div>
            <?php } ?>
        </div>
        <div class="form-group row">
            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="firstName">First Name<span class="required"
                                                                                aria-required="true"> * </span></label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control  mandatory" type="text" name="firstName" id="firstName"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->firstName) ?>">
                <?php } else {
                    echo '<h5>' . htmlspecialchars(createAgent::Agent()->firstName) . '</h5>';
                } ?>
            </div>
            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="lastName">Last Name<span class="required"
                                                                              aria-required="true"> * </span></label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control  mandatory " type="text" name="lastName" id="lastName"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->lastName) ?>">
                <?php } else {
                    echo '<h5>' . htmlspecialchars(createAgent::Agent()->lastName) . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7 <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>">
                <label class="font-weight-bold" for="company">Company<span class="required"
                                                                           aria-required="true"> * </span></label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control mandatory" type="text" name="company" id="company"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->company); ?>">
                <?php } else {
                    echo '<h5>' . htmlspecialchars(createAgent::Agent()->company) . '</h5>';
                } ?>
            </div>
            <?php if (createAgent::$publicUser != 1) { ?>
                <div class="col-lg-6 mb-7" id="agentLoginDiv1">
                    <div class="form-group row align-items-center">
                        <label class="col-lg-3 font-weight-bold">Allowed to login?
                            <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                               data-toggle="tooltip"
                               title="This will enable or disable ability to login. Also, your billing is tied to # of users with login rights enabled."></i>
                        </label>
                        <?php if (createAgent::$PCAllowToCreate == 1 || createAgent::Agent()->allowAgentToLogin == 1) { ?>
                            <div class="col-lg-4">
                            <span class="switch switch-icon">
                                <label class="font-weight-bold">
                                    <input class="form-control agentUserPermission" <?php if (createAgent::Agent()->allowAgentToLogin == '1') { ?> checked="checked" <?php } ?> type="checkbox"
                                           value="<?php echo createAgent::Agent()->allowAgentToLogin ?>" id="agentLogin"
                                           onchange="toggleSwitch('agentLogin', 'allowAgentToLogin', '1', '0' );">
                                    <input type="hidden" name="allowAgentToLogin" id="allowAgentToLogin"
                                           value="<?php echo createAgent::Agent()->allowAgentToLogin ?>">
                                    <span></span>
                                </label>
                            </span>
                            </div>
                        <?php } else {
                            $allowEmpToLogin = 0; /* Force the user login rights OFF */
                            ?>
                            <div class="col-lg-4 h5">
                                <?php echo 'No'; ?>
                                <span class="form-text text-muted">
                                (Note: You cannot edit this field since you have exceeded the # of allowed users)
                            </span>
                                <input type="hidden" name="allowAgentToLogin"
                                       value="<?php echo createAgent::Agent()->allowAgentToLogin; ?>">
                            </div>
                        <?php } ?>
                    </div>
                </div>
            <?php } else { ?>
                <input type="hidden" name="allowAgentToLogin"
                       value="<?php echo createAgent::Agent()->allowAgentToLogin; ?>">
            <?php } ?>

            <div class="col-lg-6 mb-7 <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>">
                <label class="font-weight-bold" for="phoneNumber">Phone Number/Toll Free</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input type="text" class="form-control  mask_phone" name="phoneNumber" id="phoneNumber"
                           value="<?php echo createAgent::Agent()->phoneNumber; ?>" autocomplete="off"
                           placeholder="(___) ___ - ____ Ext ____">
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->phoneNumber . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold"
                       for="cellNumber"><?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) { ?>Cell Number<?php } else { ?>Phone Number<?php } ?>
                    <span class="required" aria-required="true"> * </span></label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input type="text" class="form-control mask_cellnew mandatory" name="cellNumber"
                           id="cellNumber" value="<?php echo createAgent::Agent()->cellNumber; ?>" autocomplete="off"
                           placeholder="(___) ___ - ____">
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->cellNumber . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="serviceProvider">Service Provider</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <div class="input-group">
                        <select name="serviceProvider" id="serviceProvider" class="form-control">
                            <option value="">- Select -</option>
                            <?php
                            $spKeyArray = array_keys($SMSServiceProviderArray);
                            for ($sp = 0; $sp < count($spKeyArray); $sp++) {
                                $spKey = $spKeyArray[$sp];
                                $myServiceProvider = $SMSServiceProviderArray[$spKey];
                                ?>
                                <option value="<?php echo $spKey; ?>" <?php echo Arrays::isSelected($spKey, createAgent::Agent()->serviceProvider); ?>><?php echo $myServiceProvider; ?></option>
                            <?php } ?>
                        </select>
                        <div class="input-group-append tooltipAjax" data-toggle="tooltip"
                             title="Please select service provider if you would like to receive task reminders via SMS">
                        <span class="input-group-text ">
                           <i class='fas fa-info-circle text-primary'></i>
                        </span>
                        </div>
                    </div>
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->phoneNumber . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7 d-none <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>">
                <label class="font-weight-bold" for="fax">Fax</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input type="text" class="form-control mask_cellnew" name="fax" id="fax"
                           value="<?php echo createAgent::Agent()->fax; ?>" autocomplete="off"
                           placeholder="(___) ___ - ____">
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->fax . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7">
                <script>
                    $(document).ready(function () {
                        $('#address').on('input', function () {
                            address_lookup.InitLegacy($(this));
                        });
                    });
                </script>
                <label class="font-weight-bold" for="address">Address</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control
                    <?php if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                        echo glCustomJobForProcessingCompany::validateBrokerProfileDataInputs($PCID, createAgent::$externalBroker);
                    } else {
                        echo glCustomJobForProcessingCompany::validateBrokerProfileFields($PCID, createAgent::$publicUser);

                    } ?>"
                           type="text"
                           name="address"
                           id="address"
                           data-address="address"
                           data-city="city"
                           data-state="state"
                           data-zip="zip"
                           data-unit="suiteNumber"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->addr); ?>">
                <?php } else {
                    echo '<h5>' . htmlspecialchars(createAgent::Agent()->addr) . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7 <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>">
                <label class="font-weight-bold" for="suiteNumber">Suite #</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control "
                           type="text"
                           name="suiteNumber"
                           id="suiteNumber"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->suiteNumber); ?>">
                <?php } else {
                    echo '<h5>' . htmlspecialchars(createAgent::Agent()->suiteNumber) . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="city">City</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileDataInputs($PCID, createAgent::$externalBroker) ?>"
                           type="text"
                           name="city"
                           id="city"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->city); ?>">
                <?php } else {
                    echo '<h5>' . htmlspecialchars(createAgent::Agent()->city) . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="state">State</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <select name="state"
                            id="state"
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileDataInputs($PCID, createAgent::$externalBroker) ?>"
                            onchange="populateStateTimeZone('newBrokerForm', 'state', 'timeZone');">
                        <option value=""> - Select -</option>
                        <?php
                        for ($j = 0; $j < count($stateArray); $j++) {
                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), createAgent::Agent()->state);
                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                        }
                        ?>
                    </select>
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->state . '</h5>';
                } ?>
            </div>
            <div class="col-lg-6 mb-7 d-none">
                <label class="font-weight-bold" for="timeZone">Time Zone</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <select name="timeZone" id="timeZone" class="form-control ">
                        <?php foreach ($timeZoneArray as $tz => $timeZone) { ?>
                            <option value="<?php echo $tz ?>" <?php echo Arrays::isSelected($tz, createAgent::Agent()->timeZone); ?>><?php echo $timeZone; ?></option>
                        <?php } ?>
                    </select>
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->timeZone . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="zip">Zip Code</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileDataInputs($PCID, createAgent::$externalBroker) ?>"
                           type="text"
                           name="zip"
                           id="zip"
                           value="<?php echo createAgent::Agent()->zipCode; ?>">
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->zipCode . '</h5>';
                } ?>
            </div>

            <!--  agent type code need to write -->
            <?php if (PageVariables::$userRole != 'Agent' && createAgent::$publicUser != 1) { ?>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="externalBroker">Agent Type</label>
                    <input type="hidden" name="dummyBrokerId" id="dummyBrokerId"
                           value="<?php echo createAgent::$dummyBrokerId; ?>">
                    <?php
                    if (isset($_GET['bId'])) { ?>
                        <input type="hidden" name="existingAgentRole" id="existingAgentRole"
                               value="<?php echo createAgent::$externalBroker; ?>">
                        <input type="hidden" name="overRideMort2Loan" id="overRideMort2Loan" value="">
                        <?php
                    }
                    if (createAgent::$allowAgentEdit) { ?>
                        <div class="col-lg-6 ">
                            <div class="radio-inline externalBrokerClass">
                                <label for="externalBrokerLN" class="radio radio-solid font-weight-bolder">
                                    <input type="radio" name="externalBroker" id="externalBrokerLN"
                                           onchange="showHidePartTyEin('externalBrokerLN','externalBrokerBR');"
                                           value="1" <?php if (createAgent::$externalBroker == '1') {
                                        echo 'checked';
                                    } ?>>
                                    <span></span>
                                    Loan Officer
                                </label>
                                <label for="externalBrokerBR" class="radio radio-solid font-weight-bolder">
                                    <input type="radio" name="externalBroker" id="externalBrokerBR"
                                           onchange="showHidePartTyEin('externalBrokerBR','externalBrokerLN');"
                                           value="0" <?php if (createAgent::$externalBroker == '0') {
                                        echo 'checked';
                                    } ?> >
                                    <span></span>
                                    Broker
                                </label>
                            </div>
                        </div>
                    <?php } else {
                        echo '<h5>' . createAgent::$externalBroker . '</h5>';
                    } ?>
                </div>
            <?php } else { ?>
                <input type="hidden" name="externalBroker" value="0"/>
            <?php } ?>

            <div class="col-lg-6 mb-7 approvedReferralFee_disp <?php if (createAgent::$publicUser || createAgent::$externalBroker) {
                echo 'secHide';
            } ?>">
                <label class="font-weight-bold" for="approvedReferralFee">Approved Referral Fee</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <select name="approvedReferralFee" id="approvedReferralFee"
                            class="form-control ">
                        <option value="">- Select -</option>
                        <?php
                        foreach ($glApprovedReferralFee as $key => $value) { ?>
                            <option value="<?php echo $key; ?>" <?php echo Arrays::isSelected($key, $approvedReferralFee); ?>><?php echo $value; ?></option>
                        <?php } ?>
                    </select>
                <?php } else {
                    echo '<h5>' . $glApprovedReferralFee[$approvedReferralFee] . '</h5>';
                } ?>
            </div>


            <?php
            $pcModules = array_map(function ($modules) {
                return $modules['moduleCode'];
            }, createAgent::$modulePCArray ?? []);

            if (in_array('HMLO', $pcModules) || in_array('loc', $pcModules)) {
                $modOpt = 1;
            }
            if ($modOpt) { ?>
            <div class="col-lg-6 mb-7 license <?php echo glCustomJobForProcessingCompany::hideLicenseInWebForm($PCID, createAgent::$publicUser); ?>
                <?php echo glCustomJobForProcessingCompany::showHideLicenseFields($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>">
                <label class="font-weight-bold" for="license">License #
                    <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                       data-toggle="tooltip"
                       title="Add State License # separated by comma"></i>
                </label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileLicence($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           type="text" name="license" id="license"
                           value="<?php echo createAgent::Agent()->license; ?>">
                <?php } else {
                    echo glCustomJobForProcessingCompany::hideLicenseInWebForm($PCID, createAgent::$publicUser);
                    echo '<h5>' . createAgent::Agent()->license . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7 EIN <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>" style="<?php if (createAgent::$externalBroker == 1) {
                echo 'display:none;';

            } ?>">
                <label class="font-weight-bold" for="eniNumber">EIN#</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           type="text" name="eniNumber" id="eniNumber"
                           value="<?php echo createAgent::Agent()->eniNumber; ?>">
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->eniNumber . '</h5>';
                } ?>
            </div>

            <div class="col-lg-6 mb-7 <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>">
                <label class="font-weight-bold" for="prefCommunication">Preferred Communication</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <select class="chzn-select form-control form-controller-solid
                        <?php echo glCustomJobForProcessingCompany::validateBrokerProfileForRefferalPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                            data-placeholder="Please Select Preferred Communication"
                            name="prefCommunication[]"
                            id="prefCommunication"
                            multiple="">
                        <?php
                        $methodOfContactKeyArray = [];
                        $methodOfContactKeyArray = array_keys($methodOfContactArray);
                        for ($se = 0; $se < count($methodOfContactKeyArray); $se++) {
                            $sOpt = '';
                            $methodContact = '';
                            $methodContact = trim($methodOfContactKeyArray[$se]);
                            if (in_array($methodContact, createAgent::$prefCommunicationArray ?? [])) {
                                $sOpt = 'selected';
                            }

                            echo "<option value=\"" . trim($methodContact) . "\" " . $sOpt . '>' . $methodOfContactArray[$methodContact] . '</option>';
                        } ?>
                    </select>
                    <?php
                } else {
                    $selectServices = '';
                    $j = 0;
                    $methodOfContactKeyArray = [];
                    $methodOfContactKeyArray = array_keys($methodOfContactArray);
                    for ($i = 0; $i < count($methodOfContactKeyArray); $i++) {
                        $methodContact = '';
                        $methodContact = trim($methodOfContactKeyArray[$i]);
                        if (in_array($methodContact, createAgent::$prefCommunicationArray ?? [])) {
                            if ($j > 0) $selectServices .= ', ';
                            $selectServices .= trim($methodOfContactArray[$methodContact]);
                            $j++;
                        }
                    }
                    ?>
                    <h5><?php echo $selectServices; ?></h5>
                <?php } ?>
            </div>

            <div class="col-lg-6 mb-7 <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>">
                <label class="font-weight-bold" for="website">Website</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           type="text" name="website" id="website"
                           value="<?php echo createAgent::Agent()->website; ?>">
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->website . '</h5>';
                } ?>
            </div>

            <div class="col-md-6 mb-7">
                <label class="font-weight-bold">Avatar</label>
                <?php if (createAgent::Agent()->avatar) { ?>
                    <i class="fa fa-copy copy-link tooltipClass text-primary"
                       title="Click To Copy Avatar Link"
                       id="<?php echo createAgent::Agent()->getURL(); ?>"></i>
                <?php } ?>
                <input type="FILE"
                       name="agentAvatar"
                       id="agentAvatar"
                       size="30"
                       autocomplete="off"
                       class="agentAvatar form-control">
                <?php if (createAgent::Agent()->avatar) { ?>
                    <div class="symbol symbol-100 mr-5 mt-2 ">
                        <div class="symbol-label"
                             style="background-image:url('<?php echo createAgent::Agent()->getURL(); ?>')"></div>

                        Merge Tag: <?php echo createAgent::Agent()->getMergeTag(); ?>

                        <i class="fa fa-copy copy-link tooltipClass text-primary"
                           title="Click To Copy Merge tag Path"
                           id="<?php echo createAgent::Agent()->getMergeTag(); ?>"></i>
                    </div>
                <?php } ?>
                <span class="form-text text-muted">(The file types allowed are JPEG,PNG,GIF.)</span>
            </div>

            <div class="form-group row col-lg-12 m-0 mb-4 px-0 companyPersonalNMLS <?php echo glCustomJobForProcessingCompany::showHideLicenseFields($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                 id="licenseInformation">
                <label class="bg-secondary py-2 col-lg-12"><b>License Information</b></label>
            </div>
            <div class="col-md-12 companyPersonalNMLS <?php echo glCustomJobForProcessingCompany::showHideLicenseFields($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>">
                <div class="row">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-lg-12 mb-7  <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                                echo 'd-none';
                            } ?>">
                                <label class="font-weight-bold" for="haveCompanyNMLS">
                                    Do you have Company NMLS #?
                                </label>
                                <?php
                                if (createAgent::$allowAgentEdit) { ?>
                                    <div class="col-lg-6 ">
                                        <div class="radio-inline haveCompanyNMLSClass">
                                            <label for="haveCompanyNMLSYes"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="haveCompanyNMLS" id="haveCompanyNMLSYes"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       onchange="showHideNMLS(this.value,'Company');"
                                                       value="Yes" <?php if (createAgent::Agent()->haveCompanyNMLS == 'Yes') {
                                                    echo 'checked';
                                                } ?>>
                                                <span></span>
                                                Yes
                                            </label>
                                            <label for="haveCompanyNMLSNo" class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="haveCompanyNMLS" id="haveCompanyNMLSNo"
                                                       onchange="showHideNMLS(this.value,'Company');"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       value="No" <?php if (createAgent::Agent()->haveCompanyNMLS == 'No') {
                                                    echo 'checked';
                                                } ?> >
                                                <span></span>
                                                No
                                            </label>
                                        </div>
                                    </div>
                                <?php } else {
                                    echo '<h5>' . createAgent::Agent()->haveCompanyNMLS . '</h5>';
                                } ?>
                            </div>


                            <div class="col-lg-12 mb-7 CompanyNMLSDiv <?php if (($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) || createAgent::Agent()->haveCompanyNMLS != 'Yes') {
                                echo 'd-none';
                            } ?>">
                                <label class="font-weight-bold" for="NMLSLicense">Company NMLS #
                                </label>
                                <?php if (createAgent::$allowAgentEdit) { ?>
                                    <input class="form-control <?php if (createAgent::Agent()->haveCompanyNMLS == 'Yes') {
                                        echo 'mandatory';
                                    } else {
                                        echo 'ignoreValidation';
                                    } ?>"
                                           type="text" name="NMLSLicense" id="NMLSLicense"
                                           value="<?php echo createAgent::Agent()->NMLSLicense; ?>">
                                <?php } else {
                                    echo '<h5>' . createAgent::Agent()->NMLSLicense . '</h5>';
                                } ?>
                            </div>

                            <div class="col-lg-12 mb-7">
                                <label class="font-weight-bold" for="haveCompanyStateLicense">Do you have Company State
                                    License #?</label>
                                <?php
                                if (createAgent::$allowAgentEdit) { ?>
                                    <div class="col-lg-6 ">
                                        <div class="radio-inline haveCompanyStateLicenseClass">
                                            <label for="haveCompanyStateLicenseYes"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="haveCompanyStateLicense"
                                                       id="haveCompanyStateLicenseYes"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       onchange="showHideStateLicense(this.value,'Company');addOrRemoveMadatorForLicenseState('<?php echo createAgent::Agent()->brokerPartnerType; ?>' ,'Company');"
                                                       value="Yes" <?php if (createAgent::Agent()->haveCompanyStateLicense == 'Yes') {
                                                    echo 'checked';
                                                } ?>>
                                                <span></span>
                                                Yes
                                            </label>
                                            <label for="haveCompanyStateLicenseNo"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="haveCompanyStateLicense"
                                                       id="haveCompanyStateLicenseNo"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       onchange="showHideStateLicense(this.value,'Company');addOrRemoveMadatorForLicenseState('<?php echo createAgent::Agent()->brokerPartnerType; ?>' ,'Company')"
                                                       value="No" <?php if (createAgent::Agent()->haveCompanyStateLicense == 'No') {
                                                    echo 'checked';
                                                } ?> >
                                                <span></span>
                                                No
                                            </label>
                                        </div>
                                    </div>
                                <?php } else {
                                    echo '<h5>' . createAgent::Agent()->haveCompanyStateLicense . '</h5>';
                                } ?>
                            </div>


                            <div class="col-lg-12 mb-7 companyStateLicenseDiv
                            <?php echo glCustomJobForProcessingCompany::hideAndShowStateLicense(createAgent::$externalBroker, createAgent::Agent()->haveCompanyStateLicense) ?>">
                                <div class="card card-custom">
                                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                                        <div class="card-title">
                                            <h3 class="card-label">
                                                Company State License #
                                            </h3>
                                        </div>
                                        <div class="card-toolbar ">

            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none cursor-pointer" data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
                                            <span class="btn btn-icon btn-sm btn-hover-light-primary d-none cursor-pointer"
                                                  data-card-tool="remove"
                                                  data-toggle="tooltip" data-placement="top" title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </span>
                                            <span
                                                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass
                                         cursor-pointer"
                                                    data-card-tool="toggle"
                                                    data-toggle="tooltip"
                                                    data-placement="top"
                                                    title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
                                        </div>
                                    </div>
                                    <div class="card-body companyLicenseCard_body">
                                        <input type="hidden"
                                               class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileStateLicense($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType, createAgent::Agent()->haveCompanyStateLicense); ?>"
                                               name="companyStateLicense" id="companyStateLicense">
                                        <?php $cl_i = 1;
                                        $companyLicenseInfo = tblCompanyLicenseDetails::GetAll(['AID' => createAgent::$brokerNo]);
                                        if (!sizeof($companyLicenseInfo ?? [])) {
                                            $companyLicenseInfo = [
                                                new tblCompanyLicenseDetails(),
                                            ];
                                        }
                                        foreach ($companyLicenseInfo as $item) { ?>
                                            <div class="row companyLicenseSection"
                                                 id="companyLicenseSectionId_<?php echo $cl_i; ?>">
                                                <input type="hidden" name="companyLicense[<?php echo $cl_i; ?>][id]"
                                                       value="<?php echo $item->id; ?>"/>
                                                <div class="col-lg-4 mb-7">
                                                    <label class="font-weight-bold"
                                                           for="companyState_<?php echo $cl_i; ?>">State</label>
                                                    <?php if (createAgent::$allowAgentEdit) { ?>
                                                        <select name="companyLicense[<?php echo $cl_i; ?>][state]"
                                                                id="companyState_<?php echo $cl_i; ?>"
                                                                class="form-control companyStateLicenseCls <?php echo glCustomJobForProcessingCompany::validateBrokerProfileStateLicense($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType, createAgent::Agent()->haveCompanyStateLicense) ?>">
                                                            <option value=""> - Select -</option>
                                                            <?php
                                                            for ($j = 0; $j < count($stateArray); $j++) {
                                                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $item->state);
                                                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                                            }
                                                            ?>
                                                        </select>
                                                    <?php } else {
                                                        echo '<h5>' . $item->state . '</h5>';
                                                    } ?>
                                                </div>
                                                <div class="col-lg-5 mb-7">
                                                    <label class="font-weight-bold"
                                                           for="companyStateLicenseNumber_<?php echo $cl_i; ?>">State
                                                        License
                                                        #</label>
                                                    <?php if (createAgent::$allowAgentEdit) { ?>
                                                        <input type="text"
                                                               name="companyLicense[<?php echo $cl_i; ?>][licenseNumber]"
                                                               id="companyStateLicenseNumber_<?php echo $cl_i; ?>"
                                                               value="<?php echo $item->licenseNumber; ?>"
                                                               class="form-control">
                                                    <?php } else {
                                                        echo '<h5>' . $item->licenseNumber . '</h5>';
                                                    } ?>
                                                </div>
                                                <div class="pt-8 col-md-3">
                                                    <a class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass <?php if ($cl_i < sizeof($companyLicenseInfo)) {
                                                        echo 'd-none';
                                                    } ?> cloneFormSection addButton<?php echo $cl_i; ?>"
                                                       onclick="SectionForm.cloneFormSection(this)"
                                                       href="javascript:void(0)"
                                                       data-clone-section="companyLicenseSection"
                                                       data-increment-section="incrementClassPS"
                                                       data-max-section=""
                                                       title="Click to add new Row.">
                                                        <i class=" icon-md fas fa-plus "></i>
                                                    </a>
                                                    <a class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeFromSection removeButton<?php echo $cl_i; ?>"
                                                       href="javascript:void(0)"
                                                       onclick="SectionForm.removeFromSection(this)"
                                                       data-clone-section="companyLicenseSection"
                                                       data-increment-section="incrementClassPS"
                                                       data-page="deleteCompanyLicenseDetails.php"
                                                       data-id="<?php echo cypher::myEncryption($item->id); ?>"
                                                       title="Click to Remove Row.">
                                                        <i class=" icon-md fas fa-minus-circle "></i>
                                                    </a>
                                                </div>
                                            </div>
                                            <?php $cl_i++;
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-lg-12 mb-7 ">
                                <label class="font-weight-bold" for="havePersonalNMLS">Do you have Personal NMLS
                                    #?</label>
                                <?php
                                if (createAgent::$allowAgentEdit) { ?>
                                    <div class="col-lg-6 ">
                                        <div class="radio-inline havePersonalNMLSClass">
                                            <label for="havePersonalNMLSYes"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="havePersonalNMLS" id="havePersonalNMLSYes"
                                                       onchange="showHideNMLS(this.value,'Personal');"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       value="Yes" <?php if (createAgent::Agent()->havePersonalNMLS == 'Yes') {
                                                    echo 'checked';
                                                } ?>>
                                                <span></span>
                                                Yes
                                            </label>
                                            <label for="havePersonalNMLSNo"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="havePersonalNMLS" id="havePersonalNMLSNo"
                                                       onchange="showHideNMLS(this.value,'Personal');"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       value="No" <?php if (createAgent::Agent()->havePersonalNMLS == 'No') {
                                                    echo 'checked';
                                                } ?> >
                                                <span></span>
                                                No
                                            </label>
                                        </div>
                                    </div>
                                <?php } else {
                                    echo '<h5>' . createAgent::Agent()->havePersonalNMLS . '</h5>';
                                } ?>
                            </div>

                            <div class="col-lg-12 mb-7 PersonalNMLSDiv <?php if (createAgent::Agent()->havePersonalNMLS != 'Yes') {
                                echo 'd-none';
                            } ?>">
                                <label class="font-weight-bold" for="personalNMLSLicense">Personal NMLS #
                                </label>
                                <?php if (createAgent::$allowAgentEdit) { ?>
                                    <input class="form-control <?php if (createAgent::Agent()->havePersonalNMLS == 'Yes') {
                                        echo 'mandatory';
                                    } else {
                                        echo 'ignoreValidation';
                                    } ?>"
                                           type="text" name="personalNMLSLicense" id="personalNMLSLicense"
                                           value="<?php echo createAgent::Agent()->personalNMLSLicense; ?>">
                                <?php } else {
                                    echo '<h5>' . createAgent::Agent()->personalNMLSLicense . '</h5>';
                                } ?>
                            </div>

                            <div class="col-lg-12 mb-7">
                                <label class="font-weight-bold" for="havePersonalStateLicense">
                                    Do you have Personal State License #?</label>
                                <?php
                                if (createAgent::$allowAgentEdit) { ?>
                                    <div class="col-lg-6 ">
                                        <div class="radio-inline havePersonalStateLicenseClass">
                                            <label for="havePersonalStateLicenseYes"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="havePersonalStateLicense"
                                                       id="havePersonalStateLicenseYes"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       onchange="showHideStateLicense(this.value,'Personal');addOrRemoveMadatorForLicenseState('<?php echo createAgent::Agent()->brokerPartnerType; ?>' ,'Personal');"
                                                       value="Yes" <?php if (createAgent::Agent()->havePersonalStateLicense == 'Yes') {
                                                    echo 'checked';
                                                } ?>>
                                                <span></span>
                                                Yes
                                            </label>
                                            <label for="havePersonalStateLicenseNo"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="havePersonalStateLicense"
                                                       id="havePersonalStateLicenseNo"
                                                       class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileHasNMLSState($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType); ?>"
                                                       onchange="showHideStateLicense(this.value,'Personal');addOrRemoveMadatorForLicenseState('<?php echo createAgent::Agent()->brokerPartnerType; ?>' ,'Personal');"
                                                       value="No" <?php if (createAgent::Agent()->havePersonalStateLicense == 'No') {
                                                    echo 'checked';
                                                } ?> >
                                                <span></span>
                                                No
                                            </label>
                                        </div>
                                    </div>
                                <?php } else {
                                    echo '<h5>' . createAgent::Agent()->havePersonalStateLicense . '</h5>';
                                } ?>
                            </div>

                            <div class="col-lg-12 mb-7 personalStateLicenseDiv <?php echo glCustomJobForProcessingCompany::hideAndShowStateLicense(createAgent::$externalBroker, createAgent::Agent()->havePersonalStateLicense) ?>">
                                <div class="card card-custom">
                                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                                        <div class="card-title">
                                            <h3 class="card-label">
                                                Personal State License #
                                            </h3>
                                        </div>
                                        <div class="card-toolbar ">

                                         <span
                                                 class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                                                 data-card-tool="toggle"
                                                 data-toggle="tooltip"
                                                 data-placement="top"
                                                 title="Toggle Card">
                                            <i class="ki ki-arrow-down icon-nm"></i>
                                        </span>
                                        </div>
                                    </div>
                                    <div class="card-body personalLicenseCard_body">
                                        <input type="hidden"
                                               class="<?php echo glCustomJobForProcessingCompany::validateBrokerProfileStateLicense($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType, createAgent::Agent()->havePersonalStateLicense) ?>"
                                               name="personalStateLicense" id="personalStateLicense">
                                        <?php $pl_i = 1;
                                        $personalLicenseInfo = tblPersonalLicenseDetails::GetAll(['AID' => createAgent::$brokerNo]);
                                        if (!sizeof($personalLicenseInfo)) {
                                            $personalLicenseInfo = [
                                                new tblPersonalLicenseDetails(),
                                            ];
                                        }
                                        foreach ($personalLicenseInfo as $item) { ?>
                                            <div class="row personalLicenseSection"
                                                 id="personalLicenseSectionId_<?php echo $pl_i; ?>">
                                                <input type="hidden" name="personalLicense[<?php echo $pl_i; ?>][id]"
                                                       value="<?php echo $item->id; ?>"/>
                                                <div class="col-lg-4 mb-7">
                                                    <label class="font-weight-bold" for="state">State</label>
                                                    <?php if (createAgent::$allowAgentEdit) { ?>
                                                        <select name="personalLicense[<?php echo $pl_i; ?>][state]"
                                                                id="personalState_<?php echo $pl_i; ?>"
                                                                class="form-control personalStateLicenseCls <?php echo glCustomJobForProcessingCompany::validateBrokerProfileStateLicense($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType, createAgent::Agent()->havePersonalStateLicense) ?>">
                                                            <option value=""> - Select -</option>
                                                            <?php
                                                            for ($j = 0; $j < count($stateArray); $j++) {
                                                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $item->state);
                                                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                                                            }
                                                            ?>
                                                        </select>
                                                    <?php } else {
                                                        echo '<h5>' . $item->state . '</h5>';
                                                    } ?>
                                                </div>
                                                <div class="col-lg-5 mb-7">
                                                    <label class="font-weight-bold" for="stateLicenseNo">State License
                                                        #</label>
                                                    <?php if (createAgent::$allowAgentEdit) { ?>
                                                        <input type="text"
                                                               name="personalLicense[<?php echo $pl_i; ?>][licenseNumber]"
                                                               id="personalStateLicenseNumber_<?php echo $pl_i; ?>"
                                                               value="<?php echo $item->licenseNumber; ?>"
                                                               class="form-control">
                                                    <?php } else {
                                                        echo '<h5>' . $item->licenseNumber . '</h5>';
                                                    } ?>
                                                </div>
                                                <div class="pt-8 col-md-3">
                                                    <a class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass <?php if ($pl_i < sizeof($personalLicenseInfo)) {
                                                        echo 'd-none';
                                                    } ?> cloneFormSection addButton<?php echo $pl_i; ?>"
                                                       onclick="SectionForm.cloneFormSection(this)"
                                                       href="javascript:void(0)"
                                                       data-clone-section="personalLicenseSection"
                                                       data-increment-section="incrementClassPS"
                                                       data-max-section=""
                                                       title="Click to add new Row.">
                                                        <i class=" icon-md fas fa-plus "></i>
                                                    </a>
                                                    <a class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeFromSection removeButton<?php echo $pl_i; ?>"
                                                       href="javascript:void(0)"
                                                       onclick="SectionForm.removeFromSection(this)"
                                                       data-clone-section="personalLicenseSection"
                                                       data-increment-section="incrementClassPS"
                                                       data-page="deletePersonalLicenseDetails.php"
                                                       data-id="<?php echo cypher::myEncryption($item->id); ?>"
                                                       title="Click to Remove Row.">
                                                        <i class=" icon-md fas fa-minus-circle "></i>
                                                    </a>
                                                </div>
                                            </div>
                                            <?php $pl_i++;
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-lg-6 mb-7 <?php if (createAgent::$publicUser) {
                echo 'd-none';
            } ?>">
                <label class="font-weight-bold" for="state">States Authorized to Originate</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <select name="statesAuthorizedToOriginate[]" id="statesAuthorizedToOriginate"
                            data-placeholder="Please Select States"
                            multiple
                            class="chzn-select form-control">
                        <?php
                        for ($j = 0; $j < count($stateArray); $j++) {
                            $sOpt = Arrays::isSelectedArray(createAgent::$statesAuthorizedToOriginateArray, trim($stateArray[$j]['stateCode']));
                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                        }
                        ?>
                    </select>
                <?php } else {
                    echo '<h5>' . implode(',', createAgent::$statesAuthorizedToOriginateArray ?? []) . '</h5>';
                } ?>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-6 mb-7  DRE_Class <?php if ($PCID == glPCID::PCID_11_CAPITAL_FINANCE && createAgent::$publicUser == 1) {
                echo 'secHide';
            } ?>">

                <label class="font-weight-bold" for="DRE">DRE#</label>
                <?php if (createAgent::$allowAgentEdit) { ?>
                    <input class="form-control " type="text" name="DRE" id="DRE"
                           value="<?php echo createAgent::Agent()->DRE; ?>">
                <?php } else {
                    echo '<h5>' . createAgent::Agent()->DRE . '</h5>';
                } ?>
            </div>

            <?php } ?>

            <?php if (!createAgent::$externalBroker) { ?>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="ssnNumber">Social Security Number</label>
                    <input type="text" name="ssnNumber" id="ssnNumber"
                           value="<?php echo createAgent::Agent()->ssnNumber; ?>" size="25"
                           maxlength="15"
                           class="form-control mask_ssn <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="linkedInURL">LinkedIn URL</label>
                    <input type="text" name="linkedInURL" id="linkedInURL"
                           value="<?php echo createAgent::Agent()->linkedInURL; ?>"
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileDataInputs($PCID, createAgent::$externalBroker) ?>"
                           autocomplete="off">
                </div>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="typeOfLoansOffered">Type Of Loans Offered</label>
                    <select name="typeOfLoansOffered[]" id="typeOfLoansOffered"
                            class="form-control chzn-select <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?> "
                            multiple
                            onchange="showAndHideLoanTypeFields()" data-placeholder="select type of loans offered">
                        <option value=""></option>
                        <?php
                        foreach ($glTypeOfLoansOffered as $loansOfferedKey => $loansOfferedValue) { ?>
                            <option value="<?php echo $loansOfferedKey; ?>" <?php if (in_array($loansOfferedKey, createAgent::$typeOfLoansOffered ?? [])) {
                                echo ' selected ';
                            } ?>>
                                <?php echo $loansOfferedValue; ?></option>
                        <?php } ?>
                    </select>
                    <input type="hidden" value="<?php echo implode(',', createAgent::$typeOfLoansOffered ?? []); ?>"
                           name="typeOfLoansOfferedOnLoad">
                </div>

                <div class="agencyMortgageLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceWithAgencyMortgage">Years of experience with Agency
                        Mortgage Lending?</label>
                    <select name="broker[<?php echo glTypeOfLoansOffered::MORTGAGE_LENDING; ?>][experienceWith]"
                            id="experienceWithAgencyMortgage"
                        <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                            data-placeholder="select Years of experience with">
                        <option value=""></option>
                        <?php
                        foreach ($glYearsOfExperience as $experienceKey => $experienceValue) { ?>
                            <option value="<?php echo $experienceKey; ?>" <?php echo Arrays::isSelected($experienceKey, createAgent::$experienceWithMortgage); ?>>
                                <?php echo $experienceValue; ?></option>
                        <?php } ?>
                    </select>
                    <input type="hidden" name="broker[<?php echo glTypeOfLoansOffered::MORTGAGE_LENDING; ?>][ID]"
                           value="<?php echo createAgent::$IDMortgage; ?>"/>
                </div>
                <div class="agencyMortgageLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="unitsFunded">of units funded in last 12 months for Agency
                        Mortgage Lending</label>
                    <input type="number"
                           name="broker[<?php echo glTypeOfLoansOffered::MORTGAGE_LENDING; ?>][unitsFunded]"
                           id="unitsFunded" value="<?php echo createAgent::$unitsFundedMortgage; ?>"
                        <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="agencyMortgageLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="volumnFunded">Loan Volume Funded in last 12 months for Agency
                        Mortgage Lending</label>
                    <input type="number"
                        <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                           name="broker[<?php echo glTypeOfLoansOffered::MORTGAGE_LENDING; ?>][volumnFunded]"
                           id="volumnFunded" value="<?php echo createAgent::$volumnFundedMortgage; ?>"
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="agencyMortgageLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceAndBackground">Detailed experience & background with
                        Agency Mortgage Lending</label>
                    <textarea
                            name="broker[<?php echo glTypeOfLoansOffered::MORTGAGE_LENDING; ?>][experienceAndBackground]"
                            id="experienceAndbackground"
                            <?php if (!in_array(glTypeOfLoansOffered::MORTGAGE_LENDING, createAgent::$typeOfLoansOffered)) {
                                echo ' disabled ';
                            } ?>
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"><?php echo createAgent::$experienceAndBackgroundMortgage; ?></textarea>
                </div>


                <div class="privateLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceWithPrivateLending">Years of experience with Private
                        Lending?</label>
                    <select name="broker[<?php echo glTypeOfLoansOffered::PRIVATE_LENDING; ?>][experienceWith]"
                            id="experienceWithPrivateLending"
                        <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                            data-placeholder="select Years of experience with">
                        <option value=""></option>
                        <?php
                        foreach ($glYearsOfExperience as $experienceKey => $experienceValue) { ?>
                            <option value="<?php echo $experienceKey; ?>" <?php echo Arrays::isSelected($experienceKey, createAgent::$experienceWithPrivateLending); ?>>
                                <?php echo $experienceValue; ?></option>
                        <?php } ?>
                    </select>
                    <input type="hidden" name="broker[<?php echo glTypeOfLoansOffered::PRIVATE_LENDING; ?>][ID]"
                           value="<?php echo createAgent::$IDPrivateLending; ?>"/>
                </div>
                <div class="privateLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="unitsFundedForPrivateLending">of units funded in last 12
                        months for Private Lending</label>
                    <input type="number"
                           name="broker[<?php echo glTypeOfLoansOffered::PRIVATE_LENDING; ?>][unitsFunded]"
                           id="unitsFundedForPrivateLendinig"
                           value="<?php echo createAgent::$unitsFundedPrivateLending; ?>"
                        <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="privateLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="volumnFundedForPrivateLending">Loan Volume Funded in last 12
                        months for Private Lending</label>
                    <input type="number"
                           name="broker[<?php echo glTypeOfLoansOffered::PRIVATE_LENDING; ?>][volumnFunded]"
                        <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                           id="volumnFundedForPrivateLending"
                           value="<?php echo createAgent::$volumnFundedPrivateLending; ?>"
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="privateLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceAndBackgroundPrivateLending">Detailed experience &
                        background with Private Lending</label>
                    <textarea
                            name="broker[<?php echo glTypeOfLoansOffered::PRIVATE_LENDING; ?>][experienceAndBackground]"
                            id="experienceAndbackgroundPrivateLending"
                            <?php if (!in_array(glTypeOfLoansOffered::PRIVATE_LENDING, createAgent::$typeOfLoansOffered)) {
                                echo ' disabled ';
                            } ?>
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                    ><?php echo createAgent::$experienceAndBackgroundPrivateLending; ?></textarea>
                </div>

                <div class="creLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceWithCRELending">Years of experience with CRE
                        Lending?</label>
                    <select name="broker[<?php echo glTypeOfLoansOffered::CRE_LENDING; ?>][experienceWith]"
                            id="experienceWithCRELending"
                        <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                            data-placeholder="select Years of experience with">
                        <option value=""></option>
                        <?php
                        foreach ($glYearsOfExperience as $experienceKey => $experienceValue) { ?>
                            <option value="<?php echo $experienceKey; ?>" <?php echo Arrays::isSelected($experienceKey, createAgent::$experienceWithCRELending); ?>>
                                <?php echo $experienceValue; ?></option>
                        <?php } ?>
                    </select>
                    <input type="hidden" name="broker[<?php echo glTypeOfLoansOffered::CRE_LENDING; ?>][ID]"
                           value="<?php echo createAgent::$IDCRELending; ?>"/>
                </div>
                <div class="creLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="unitsFundedForCRELending">of units funded in last 12 months for
                        CRE
                        Lending</label>
                    <input type="number" name="broker[<?php echo glTypeOfLoansOffered::CRE_LENDING; ?>][unitsFunded]"
                           id="unitsFundedForCRELending" value="<?php echo createAgent::$unitsFundedCRELending; ?>"
                        <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="creLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="volumnFundedForCRELending">Loan Volume Funded in last 12
                        months CRE Lending</label>
                    <input type="number" name="broker[<?php echo glTypeOfLoansOffered::CRE_LENDING; ?>][volumnFunded]"
                           id="volumnFundedForCRELending" value="<?php echo createAgent::$volumnFundedCRELending; ?>"
                        <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                            echo ' disabled ';
                        } ?>
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="creLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceAndBackgroundCRELending">Detailed experience &
                        background
                        with CRE lending</label>
                    <textarea name="broker[<?php echo glTypeOfLoansOffered::CRE_LENDING; ?>][experienceAndBackground]"
                              id="experienceAndbackgroundCRELending"
                              <?php if (!in_array(glTypeOfLoansOffered::CRE_LENDING, createAgent::$typeOfLoansOffered)) {
                                  echo ' disabled ';
                              } ?>
                              class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                    ><?php echo createAgent::$experienceAndBackgroundCRELending; ?></textarea>
                </div>

                <div class="businessLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceWithBusinessLending">Years of experience with
                        Business
                        Lending?</label>
                    <select name="broker[<?php echo glTypeOfLoansOffered::BUSINESS_LENDING; ?>][experienceWith]"
                            id="experienceWithBusinessLending" <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                        echo ' disabled ';
                    } ?>
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                            data-placeholder="select Years of experience with">
                        <option value=""></option>
                        <?php
                        foreach ($glYearsOfExperience as $experienceKey => $experienceValue) { ?>
                            <option value="<?php echo $experienceKey; ?>" <?php echo Arrays::isSelected($experienceKey, createAgent::$experienceWithBusinessLending); ?>>
                                <?php echo $experienceValue; ?></option>
                        <?php } ?>
                    </select>
                    <input type="hidden" name="broker[<?php echo glTypeOfLoansOffered::BUSINESS_LENDING; ?>][ID]"
                           value="<?php echo createAgent::$IDBusinessLending; ?>">
                </div>
                <div class="businessLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="unitsFundedForBusinessLending">of units funded in last 12
                        months for Business Lending</label>
                    <input type="number" <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                        echo ' disabled ';
                    } ?>
                           name="broker[<?php echo glTypeOfLoansOffered::BUSINESS_LENDING; ?>][unitsFunded]"
                           id="unitsFundedForBusinessLending"
                           value="<?php echo createAgent::$unitsFundedBusinessLending; ?>"
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="businessLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="volumnFundedForBusinessLending">Loan Volume Funded in last 12
                        months Business Lending</label>
                    <input type="number" <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                        echo ' disabled ';
                    } ?>
                           name="broker[<?php echo glTypeOfLoansOffered::BUSINESS_LENDING; ?>][volumnFunded]"
                           id="volumnFundedForBusinessLending"
                           value="<?php echo createAgent::$volumnFundedBusinessLending; ?>"
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="businessLending col-lg-6 mb-7 <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                    echo ' d-none ';
                } ?>">
                    <label class="font-weight-bold" for="experienceAndBackgroundBusinessLending">Detailed experience &
                        background with Business Lending</label>
                    <textarea <?php if (!in_array(glTypeOfLoansOffered::BUSINESS_LENDING, createAgent::$typeOfLoansOffered)) {
                        echo ' disabled ';
                    } ?>
                            name="broker[<?php echo glTypeOfLoansOffered::BUSINESS_LENDING; ?>][experienceAndBackground]"
                            id="experienceAndBackgroundBusinessLending"
                            class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"><?php echo createAgent::$experienceAndBackgroundBusinessLending; ?></textarea>
                </div>


                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="ofEmployees"># of employees</label>
                    <input type="number" name="ofEmployees" id="ofEmployees"
                           value="<?php echo createAgent::Agent()->ofEmployees; ?>"
                           class="form-control <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>

                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="states">States that you broker loans in?</label>
                    <?php if (createAgent::$allowAgentEdit) { ?>
                        <div class="checkbox-list float-right">
                            <label class="checkbox">
                                <input type="checkbox"
                                       class="chosen-select selectALlCheckBox "
                                       data-id="statesId"
                                       id="selectALlCheckBox_States"/>
                                <span></span>Select All
                            </label>
                        </div>
                        <select name="states[]" id="statesId"
                                class="form-control chzn-select <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                                data-placeholder="please select state" multiple>
                            <option value=""></option>
                            <?php
                            for ($j = 0; $j < count($stateArray); $j++) {
                                $sOpt = Arrays::isSelectedArray(createAgent::$statesCodeArray, trim($stateArray[$j]['stateCode']));
                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else {
                        echo '<h5>' . createAgent::Agent()->state . '</h5>';
                    } ?>
                </div>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="bestServeYourNeeds">Please write a few sentences about your
                        relevant experience
                        and why you want to broker loans with us? (min 25 words)
                    </label>
                    <textarea type="text" name="bestServeYourNeeds" id="bestServeYourNeeds"
                              class="form-control <?php if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                                  echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType);
                              } else {
                                  echo glCustomJobForProcessingCompany::validateBrokerProfileFields($PCID, createAgent::$publicUser);
                              } ?>"><?php echo htmlspecialchars(createAgent::Agent()->bestServeYourNeeds); ?></textarea>
                </div>
                <?php
                $isLogo = $logoExit = false;
                $mainDivWidth = 'col-lg-6';
                $logoSrc = '';
                if (createAgent::Agent()->logo) {
                    $mainDivWidth = 'col-lg-4';
                    $isLogo = true;
                    $logoExit = file_exists(CONST_BROKER_LOGO_PATH . createAgent::Agent()->logo);
                    if ($logoExit) {
                        $logoSrc = CONST_BROKER_LOGO_URL . createAgent::Agent()->logo;
                    } else {
                        $mainDivWidth = 'col-lg-6';
                    }
                }
                ?>
                <div class="<?php echo $mainDivWidth; ?> mb-7">
                    <label class="font-weight-bold" for="logo">
                        Logo
                    </label>
                    <input type="file" class="agentLogo form-control" name="logo" id="logo">
                    <span class="form-text text-muted">(The file types allowed are JPEG, PNG, GIF.)</span>
                </div>
                <?php if ($isLogo && $logoExit) { ?>
                    <div class="col-lg-1 mb-7">
                        <a class="manualPopover change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 my-10"
                           data-name="Broker Logo"
                           data-html="true" data-content="<img src='<?php echo $logoSrc; ?>'>"><i
                                    class="fa fa-eye"></i>
                        </a>
                    </div>
                    <div class="col-lg-1 mb-7">
                        <a class="change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 my-10"
                           href="javascript:deleteBrokerLogo('<?php echo cypher::myEncryption(createAgent::$brokerNo); ?>', '<?php echo createAgent::$externalBroker; ?>')"
                           data-html="true" data-toggle="tooltip">
                            <i class="tooltipClass flaticon2-trash" title="Click to Delete Logo"></i>
                        </a>
                    </div>
                <?php } ?>
                <div class="form-group row col-lg-12 m-0 mb-4 px-0">
                    <label class="bg-secondary  py-2  col-lg-12"><b>Primary Contact </b>
                        <i class="popoverClass fas fa-info-circle text-primary "
                           data-html="true"
                           data-content="Please provide an additional contact for you or your office to help with any admin-related items on your behalf.
                       If you are a Loan Officer with a principal broker please enter their info here."></i></label>
                </div>
                <div class="col-lg-12 mb-7">
                    <div class="checkbox-inline">
                        <label class="checkbox checkbox-outline checkbox-outline-2x checkbox-primary font-weight-bold"
                               for="useSameContactInfo">
                            <input type="checkbox"
                                   name="useSameContactInfo"
                                   class="useSameContactInfo"
                                   id="useSameContactInfo"
                                   onclick="populateTheBrokerData()"
                                   value="1" <?php if (createAgent::Agent()->useSameContactInfo) {
                                echo 'checked';
                            } ?>/>
                            <span></span>
                            Use same contact info as above
                        </label>
                    </div>
                </div>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="primaryContactFName">Primary contact First Name</label>
                    <input type="text" name="primaryContactFName" id="primaryContactFName"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->primaryContactFName); ?>"
                           class="form-control useSameContactInfo <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="primaryContactLName">Primary contact Last Name</label>
                    <input type="text" name="primaryContactLName" id="primaryContactLName"
                           value="<?php echo htmlspecialchars(createAgent::Agent()->primaryContactLName); ?>"
                           class="form-control useSameContactInfo <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="primaryContactPhone">Primary contact Phone</label>
                    <input type="text" name="primaryContactPhone" id="primaryContactPhone"
                           value="<?php echo createAgent::Agent()->primaryContactPhone; ?>"
                           class="form-control mask_cellnew useSameContactInfo <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="primaryContactEmail">Primary contact Email</label>
                    <input type="email" name="primaryContactEmail" id="primaryContactEmail"
                           value="<?php echo createAgent::Agent()->primaryContactEmail; ?>"
                           class="form-control useSameContactInfo <?php echo glCustomJobForProcessingCompany::validateBrokerProfileBasedOnBrokerPartner($PCID, createAgent::$externalBroker, createAgent::Agent()->brokerPartnerType) ?>"
                           autocomplete="off">
                </div>
                <div class="col-md-12 separator separator-dashed my-2 separator12"></div>
            <?php } ?>

            <?php
            if (!createAgent::$brokerNo) {
                if (!glCustomJobForProcessingCompany::hideBrokerRegistrationPassword($PCID) && createAgent::$publicUser) {
                    $tempPassword = (random_int(11111111, 99999999));
                    echo '<input type="hidden" name="pwd" id="pwd" value="' . $tempPassword . '">';
                } else {
                    ?>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="pwd">Password<span class="required"
                                                                                aria-required="true"> * </span></label>
                        <input class="form-control  mandatory" type="password" name="pwd" id="pwd" autocomplete="off">
                    </div>

                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="confirmPwd">Confirm Password<span class="required"
                                                                                               aria-required="true"> * </span></label>
                        <input class="form-control  mandatory" type="password" name="confirmPwd" id="confirmPwd">
                    </div>
                <?php }
            }
            ?>
            <?php if (createAgent::$publicUser != 1) { ?>
                <div class="col-lg-6 mb-7">
                    <div class="form-group row pt-6">
                        <label class="font-weight-bold col-lg-6" for="executiveId">
                            Assigned to Branch(s)
                            <span class="required" aria-required="true"> * </span>
                            <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                               data-toggle="tooltip"
                               title="All Loan Officer/Broker must be assigned to at least branch. Remember, every client file must be assigned to an &quot;Loan Officer/Broker&quot; and a &quot;Branch&quot;"></i>
                            <span class="form-text text-muted text-danger">You need to link this Loan Officer/Broker to at least 1 branch
                            </span></label>
                        <div class="col-lg-6" id="branchId_container">
                            <select data-placeholder="Select Branches"
                                    name="executiveId[]"
                                    id="executiveId"
                                    class="form-control chzn-select"
                                <?php if (createAgent::$externalBroker == '0') { ?>
                                    onchange="getBranchLoanOfficers(this.value)"
                                <?php } ?>
                                    multiple="">
                                <?php
                                foreach (createAgent::$executiveInfoArray as $j => $item) {
                                    $LMRExecutive = trim($item['LMRExecutive']);
                                    $executiveId = trim($item['executiveId']);
                                    ?>
                                    <option value="<?php echo $executiveId; ?>" <?php echo Arrays::isSelectedArray(createAgent::$preferredBranchArray, $executiveId); ?>><?php echo $LMRExecutive; ?></option>
                                    <?php
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>

                <?php if (createAgent::$externalBroker == '0') { ?>
                    <div class="col-lg-6 mb-7">
                        <div class="form-group row pt-6">
                            <label class="font-weight-bold col-lg-6" for="loanOfficersId">
                                Assigned Loan Officer(s)
                            </label>
                            <div class="col-lg-6" id="loanOfficersId_container">
                                <select data-placeholder="Select Loan Officers"
                                        name="loanOfficersId[]"
                                        id="loanOfficersId"
                                        class="form-control chzn-select"
                                        multiple="">
                                    <?php
                                    foreach (createAgent::$agentList as $j => $item) {
                                        if ($item->externalBroker) {
                                            ?>
                                            <option value="<?php echo $item->brokerNumber; ?>"
                                                <?php echo Arrays::isSelectedArray(createAgent::$brokerAssociateLOArray, $item->brokerNumber); ?>>
                                                <?php echo $item->bName . ' ' . $item->bLName . ' - ' . $item->bEmail; ?>
                                            </option>
                                            <?php
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            <?php } ?>

            <?php if (createAgent::$publicUser && $loanOfficerId) { ?>
                <input type="hidden" name="loanOfficersId[]" value="<?php echo $loanOfficerId; ?>">
            <?php } ?>
        </div>  <!-- row end-->
    </div>
</div>
<?php if ($modOpt && (count($docDetails) > 0)) { ?>
    <div class="card card-custom mb-2">
        <div class="card-header card-header-tabs-line bg-gray-100 uploadApplicableCard">
            <div class="card-title">
                <div data-toggle="collapse" data-target="#agentDocsCollapse">Upload Applicable Files
                    <i class="tooltipClass fa fa-info-circle text-primary"
                       title="To update the required docs here,go to settings--> Doc Settings"></i>
                </div>
            </div>
            <div class="card-toolbar">
                <a href="javascript:void(0);"
                   class="tooltipClass card-title btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1"
                   data-card-tool="toggle"
                   data-section="uploadApplicableCard"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body uploadApplicableCard_body accordion" <?php if (createAgent::$publicUser != 1) { ?> style="display: none;" <?php } ?>>
            <?php
            foreach ($docDetails as $i => $item) {
                $keyValue = $brokeruploaddocurl = '';
                if (!(is_array(createAgent::$brokerDocsFinalInfo))) {
                    createAgent::$brokerDocsFinalInfo = [];
                } // Live warnings

                $uploadDocUrl = '';
                $keyValue = array_search($item['ID'], createAgent::$brokerDocsFinalInfo);
                if (trim($keyValue) != '') {
                    $docName = createAgent::$brokerDocsInfo[$keyValue]['fileName'];
                    $dN = createAgent::$brokerDocsInfo[$keyValue]['docName'];
                    $displayDocName = createAgent::$brokerDocsInfo[$keyValue]['displayDocName'];
                    $fCreatedName = str_replace('-', '', createAgent::Agent()->registerDate);
                    $dir = CONST_PATH_BROKER_DOC;
                    $dir .= '/' . date('Y', strtotime($fCreatedName));
                    $dir .= '/' . date('m', strtotime($fCreatedName));
                    $dir .= '/' . date('d', strtotime($fCreatedName));
                    $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($docName) . '&fd=' . cypher::myEncryption($dir . '/' . createAgent::$brokerNo) . '&opt=enc';
                    if ($displayDocName == '') {
                        $displayDocName = $dN;
                    }
                    $brokeruploaddocurl = "<a href=\"$uploadDocUrl\" target=\"_blank\">" . $displayDocName . '</a>';
                }
                if (createAgent::$publicUser == 1 && $item['isEnableInwebForm'] == 1) {
                    ?>
                    <div class="form-group  row">
                        <div class="col-lg-4"><?php echo $item['name']; ?></div>
                        <div class="col-lg-4">
                            <input type="text" class="form-control brokerdocs chooseDocUploadName"
                                   name="displayDocName[]"
                                   id="docNameDriver_<?php echo $item['ID']; ?>"
                                   placeholder="Document Name">
                            <input type="hidden" name="docCategory[]"
                                   id="driverLicence_<?php echo $item['ID']; ?>"
                                   value="<?php echo $item['docCategory']; ?>">
                            <input type="hidden" name="docID[]" id="docID_<?php echo $item['ID']; ?>"
                                   value="<?php echo $item['ID']; ?>">
                        </div>
                        <div class="col-lg-4">
                            <input class="form-control brokerfiledocs chooseDocUploadFile" type="FILE"
                                   name="fileSrc[]"
                                   id="fileSrcDriver_<?php echo $item['ID']; ?>">
                            <span>
                                <?php if ($uploadDocUrl != '') {
                                    echo $brokeruploaddocurl;
                                } ?>
                            </span>
                        </div>
                    </div>

                <?php } else if (createAgent::$publicUser != 1 && $item['isRequired'] == 1) { ?>
                    <div class="form-group row">
                        <div class="col-lg-4">
                            <?php echo $item['name']; ?>
                        </div>
                        <div class="col-lg-4">
                            <input type="text" class="form-control chooseDocUploadName brokerdocs"
                                   name="displayDocName[]"
                                   id="docNameDriver_<?php echo $item['ID']; ?>"
                                   value=""
                                   placeholder="Document Name" maxlength="250">
                            <input type="hidden" name="docCategory[]"
                                   id="driverLicence_<?php echo $item['ID']; ?>"
                                   value="<?php echo $item['docCategory']; ?>">
                            <input type="hidden" name="docID[]" id="docID_<?php echo $item['ID']; ?>"
                                   value="<?php echo $item['ID']; ?>">
                        </div>
                        <div class="col-lg-4">
                            <input class="form-control chooseDocUploadFile brokerfiledocs" type="FILE"
                                   name="fileSrc[]"
                                   id="fileSrcDriver_<?php echo $item['ID']; ?>">
                            <span>
                                <?php if ($uploadDocUrl != '') {
                                    echo $brokeruploaddocurl;
                                } ?>
                            </span>
                        </div>
                    </div>
                <?php }
            } ?>
        </div>
    </div>
<?php } ?>
<?php if (createAgent::$publicUser != 1) { ?>
    <div class="card card-custom mb-2">
        <div class="card-header card-header-tabs-line bg-gray-100">
            <div class="card-title">
                <h3 class="card-label">User Permission Settings</h3>
            </div>
            <div class="card-toolbar">
                <a href="javascript:void(0);"
                   class="tooltipClass card-title btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1"
                   data-card-tool="toggle" data-section="uploadApplicableCard" data-toggle="tooltip"
                   data-placement="top"
                   title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
            </div>
        </div>
        <div class="card-body accordion" id="detailedAgentPermission" style="display: none">
            <?php
            if (createAgent::$publicUser != 1) {
                if (PageVariables::$userRole == 'Super'
                    || PageVariables::$userRole == 'Manager'
                    || (PageVariables::$userRole == 'Branch' && createAgent::$allowLMRToEditAgentProfile == 1)
                    || (PageVariables::$allowEmpToSeeAgent == 1 && PageVariables::$allowEmpToCreateAgent == 1)
                ) { ?>
                    <div class="card card-custom example example-compact">
                        <div class="card-header">
                            <h3 class="card-title">Editing and Visibility Permissions</h3>
                        </div>
                        <div class="card-body">
                            <div class="row hidden">
                                <label class="bg-secondary  py-2  col-lg-12"><b>If you want to restrict access use the
                                        data
                                        below</b></label>
                            </div>
                            <div class="row">
                                <?php if (PageVariables::$userRole == 'Super') { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Select Processing Company</label>
                                            <div class="col-lg-4">
                                                <input type="text" name="pcName" id="pcName"
                                                       value="<?php echo createAgent::Processor()->processingCompanyName; ?>"
                                                       size="30"
                                                       placeholder="Type PC Name to suggest" class="form-control"
                                                       onblur="if(this.value == '') { clearPCID(); }">
                                                <input type="hidden" id="PCID" name="PCID"
                                                       value="<?php echo createAgent::Processor()->PCID; ?>">
                                            </div>
                                        </div>
                                    </div>
                                <?php } else {
                                    if (in_array('HMLO', createAgent::$moduleCodeArray)) { ?>
                                        <input type="hidden" id="PCID" name="PCID"
                                               value="<?php echo createAgent::Processor()->PCID; ?>">
                                        <?php
                                    } else { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Processing Company</label>
                                                <div class="col-lg-4">
                                                    <input type="text"
                                                           value="<?php echo createAgent::Processor()->processingCompanyName; ?>"
                                                           disabled>
                                                    <input type="hidden" id="PCID" name="PCID"
                                                           value="<?php echo createAgent::Processor()->PCID; ?>">
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                } ?>
                            </div>
                            <div <?php if (createAgent::Agent()->allowAgentToLogin == 1 || createAgent::$brokerNo > 0) { ?> style="display:block;"  <?php } else { ?> style="display:none;" <?php } ?>>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to edit file(s)?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If Yes, the user will be able to edit all files, regardless of the file status-related permission. If NO, the user will NOT be able to edit any loan files in the system. If Yes for 'Follow Company Settings' the user will only be able to edit files based on the platform settings-> File Status-> User Type Permissions."></i>
                                            </label>
                                            <div class="col-lg-4 ">
                                                <div class="radio-inline">
                                                    <label for="allowAgentToEditLMRFile"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="allowAgentToEditLMRFile"
                                                               id="allowAgentToEditLMRFile"
                                                               value="1" <?php echo Strings::isChecked(createAgent::Agent()->allowAgentToEditLMRFile, '1') ?> >
                                                        <span></span>
                                                        Yes
                                                    </label>
                                                    <label for="allowAgentToEditLMRFile1"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="allowAgentToEditLMRFile"
                                                               id="allowAgentToEditLMRFile1"
                                                               value="0" <?php echo Strings::isChecked(createAgent::Agent()->allowAgentToEditLMRFile, '0') ?>>
                                                        <span></span>
                                                        No
                                                    </label>
                                                    <label for="allowAgentToEditLMRFile2"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="allowAgentToEditLMRFile"
                                                               id="allowAgentToEditLMRFile2"
                                                               value="2" <?php echo Strings::isChecked(createAgent::Agent()->allowAgentToEditLMRFile, '2') ?> >
                                                        <span></span>
                                                        <a target="_blank"
                                                           href="<?php echo CONST_BO_URL; ?>createProcessingCompany.php?pcId=<?php echo cypher::myEncryption($PCID) ?>&tabNumb=6">
                                                            Follow Company Settings
                                                        </a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                    } else { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to "send homeowner link" for real time
                                                    updates on files?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary "
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="<?php if (createAgent::Processor()->isPLO == 1 || PageVariables::$userGroup == 'Super') { ?>If you purchased the Private Label option, your client will have access to login and view their file in real time.<?php } else { ?>This is a private label option<?php } ?>"></i>
                                                </label>
                                                <?php if (createAgent::Processor()->isPLO == 1 || PageVariables::$userGroup == 'Super') { ?>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::$allowAgentToSendHomeownerLink == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::$allowAgentToSendHomeownerLink ?>"
                                                               id="allowToSendHomeownerLink"
                                                               onchange="toggleSwitch('allowToSendHomeownerLink', 'allowAgentToSendHomeownerLink','1','0' );"/>
                                                        <input type="hidden" name="allowAgentToSendHomeownerLink"
                                                               id="allowAgentToSendHomeownerLink"
                                                               value="<?php echo createAgent::$allowAgentToSendHomeownerLink ?>">
                                                    <span></span>
                                                    </label>
                                                      <i class="tooltipAjax fas fa-lock ml-2" data-html="true"
                                                         data-toggle="tooltip"
                                                         title="If you purchased the Private Label option, your client will have access to login and view their file in real time."></i>
                                                </span>
                                                    </div>
                                                <?php } else { ?>
                                                    <div class="col-lg-4"><?php if (createAgent::$allowAgentToSendHomeownerLink == '0') { ?> class="switch-off left pad2" <?php } else { ?> class="pad2 switch-on left" <?php } ?>
                                                        <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::$allowAgentToSendHomeownerLink == 1) { ?> checked="checked" <?php } ?>/>
                                                        <input type="hidden" name="allowAgentToSendHomeownerLink"
                                                               id="allowAgentToSendHomeownerLink"
                                                               value="<?php echo createAgent::$allowAgentToSendHomeownerLink ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                        <i class="tooltipAjax fas fa-lock"
                                                           title="This feature is available with Private Label package"></i>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">
                                                Would you like this Loan Officer/Broker to be able to change status and
                                                substatus if the file is editable for them?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If Yes, the user can update the admin level info like status update, sub status, loan program, etc.. even if the Platform settings--> file status based permission is set to NOT Editable."></i>
                                            </label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" id="allowToUploadFile"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowedToUpdateFiles == 1) { ?> checked="checked" <?php } ?>
                                                value="<?php echo createAgent::Agent()->allowedToUpdateFiles ?>"
                                                       onchange="toggleSwitch('allowToUploadFile', 'allowedToUpdateFiles','1','0' );"/>
                                                <input type="hidden" name="allowedToUpdateFiles"
                                                       id="allowedToUpdateFiles"
                                                       value="<?php echo createAgent::Agent()->allowedToUpdateFiles ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to access private notes?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, the user will be able to see notes marked as private."></i>
                                            </label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowToAccessPrivateNotes == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowToAccessPrivateNotes ?>"
                                                       id="allowToAccessNotes"
                                                       onchange="toggleSwitch('allowToAccessNotes', 'allowToAccessPrivateNotes','1','0' );"/>
                                                <input type="hidden" name="allowToAccessPrivateNotes"
                                                       id="allowToAccessPrivateNotes"
                                                       value="<?php echo createAgent::Agent()->allowToAccessPrivateNotes ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to access public notes? </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->allowAgentToSeePublicNotes == 1) { ?> checked="checked" <?php } ?>
                                                    value="<?php echo createAgent::Agent()->allowAgentToSeePublicNotes ?>"
                                                           id="allowToAccessPublicNotes"
                                                           onchange="toggleSwitch('allowToAccessPublicNotes', 'allowAgentToSeePublicNotes','1','0' );"/>
                                                    <input type="hidden" name="allowAgentToSeePublicNotes"
                                                           id="allowAgentToSeePublicNotes"
                                                           value="<?php echo createAgent::Agent()->allowAgentToSeePublicNotes ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>

                                    <?php
                                    if (PageVariables::$userRole != 'Branch') {
                                        if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                        } else { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allowed to create files? </label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowAgentToCreateFiles == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->allowAgentToCreateFiles ?>"
                                                               id="allowToCreateFile"
                                                               onchange="toggleSwitch('allowToCreateFile', 'allowAgentToCreateFiles','1','0' );"/>
                                                        <input type="hidden" name="allowAgentToCreateFiles"
                                                               id="allowAgentToCreateFiles"
                                                               value="<?php echo createAgent::Agent()->allowAgentToCreateFiles ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to create tasks?</label>
                                                <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control"
                                                   type="checkbox" <?php if (createAgent::Agent()->allowAgentToCreateTasks == 1) { ?> checked="checked" <?php } ?>
                                            value="<?php echo createAgent::Agent()->allowAgentToCreateTasks ?>"
                                                   id="allowToCreateTasks"
                                                   onchange="toggleSwitch('allowToCreateTasks', 'allowAgentToCreateTasks','1','0' );"/>
                                            <input type="hidden" name="allowAgentToCreateTasks"
                                                   id="allowAgentToCreateTasks"
                                                   value="<?php echo createAgent::Agent()->allowAgentToCreateTasks ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <?php if (!in_array($PCID, $accessRestrictionPC)) { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allow to Excel Export File Data?</label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowedToExcelReport == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->allowedToExcelReport ?>"
                                                               id="allowToExcelReportId"
                                                               onchange="toggleSwitch('allowToExcelReportId', 'allowedToExcelReport','1','0' );"/>
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <input type="hidden" name="allowedToExcelReport" id="allowedToExcelReport"
                                               value="<?php echo createAgent::Agent()->allowedToExcelReport ?>">

                                        <?php if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                        } else { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allowed to edit/delete their own
                                                        notes?</label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowedToEditOwnNotes == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->allowedToEditOwnNotes ?>"
                                                               id="allowToEditNotes"
                                                               onchange="toggleSwitch('allowToEditNotes','allowedToEditOwnNotes','1','0' );"/>
                                                        <input type="hidden" name="allowedToEditOwnNotes"
                                                               id="allowedToEditOwnNotes"
                                                               value="<?php echo createAgent::Agent()->allowedToEditOwnNotes ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to see billing section? </label>
                                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->seeBilling == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::Agent()->seeBilling ?>"
                                                           id="allowToSeeBilling"
                                                           onchange="toggleSwitch('allowToSeeBilling','seeBilling','1','0' );showEditSwitch('editSwitch','seeBilling');"/>
                                                    <input type="hidden" name="seeBilling" id="seeBilling"
                                                           value="<?php echo createAgent::Agent()->seeBilling ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to Edit Bank or Card Information?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary "
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="If Yes, the user can edit the data in the bank and card info fields under the billing tab."></i>
                                                </label>
                                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->allowToEditCCInfo == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::Agent()->allowToEditCCInfo ?>"
                                                           id="allowToSeeBillingSection"
                                                           onchange="toggleSwitch('allowToSeeBillingSection', 'allowToEditCCInfo','1','0' );"/>
                                                    <input type="hidden" name="allowToEditCCInfo"
                                                           id="allowToEditCCInfo"
                                                           value="<?php echo createAgent::Agent()->allowToEditCCInfo ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                        if (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Manager' || (PageVariables::$allowEmpToSeeAgent == 1 && PageVariables::$allowEmpToCreateAgent == 1)) {
                                            if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                            } else { ?>
                                                <div class="col-6">
                                                    <div class="form-group row align-items-center">
                                                        <label class="col-lg-8">Subscribe to HOME Report?</label>
                                                        <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if (createAgent::Agent()->subscribeToHOME == 1) { ?> checked="checked" <?php } ?>
                                                            value="<?php echo createAgent::Agent()->subscribeToHOME ?>"
                                                                   id="subscribeToHOMEForPC"
                                                                   onchange="toggleSwitch('subscribeToHOMEForPC', 'subscribeToHOME','1','0' );"/>
                                                            <input type="hidden" name="subscribeToHOME"
                                                                   id="subscribeToHOME"
                                                                   value="<?php echo createAgent::Agent()->subscribeToHOME ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-group row align-items-center">
                                                        <label class="col-lg-8"> Would you like to allow this Loan
                                                            Officer/Broker to edit commission?</label>
                                                        <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if (createAgent::Agent()->allowAgentToEditCommission == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo createAgent::Agent()->allowAgentToEditCommission ?>"
                                                                   id="allowToagentEditCommission"
                                                                   onchange="toggleSwitch('allowToagentEditCommission', 'allowAgentToEditCommission','1','0' );"/>
                                                            <input type="hidden" name="allowAgentToEditCommission"
                                                                   id="allowAgentToEditCommission"
                                                                   value="<?php echo createAgent::Agent()->allowAgentToEditCommission ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-group row align-items-center">
                                                        <label class="col-lg-8"> Would you like to allow this Loan
                                                            Officer/Broker to see commission?</label>
                                                        <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if (createAgent::Agent()->allowAgentToSeeCommission == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo createAgent::Agent()->allowAgentToSeeCommission ?>"
                                                                   id="allowToAgentSeeCommission"
                                                                   onchange="toggleSwitch('allowToAgentSeeCommission', 'allowAgentToSeeCommission','1','0' );"/>
                                                            <input type="hidden" name="allowAgentToSeeCommission"
                                                                   id="allowAgentToSeeCommission"
                                                                   value="<?php echo createAgent::Agent()->allowAgentToSeeCommission ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php } ?>
                                        <?php }
                                        if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                        } else { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Lock Loan Officer/Broker so it cannot be
                                                        deleted?
                                                        <i class="tooltipAjax fas fa-info-circle text-primary "
                                                           data-html="true" data-toggle="tooltip"
                                                           title="If you use the iframe codes for this Loan Officer/Broker in your website, then lock this Loan Officer/Broker which will NOT be allowed to delete because deleting it will break your website"></i>
                                                    </label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->isPrimary == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->isPrimary ?>"
                                                               id="primaryAgent"
                                                               onchange="toggleSwitch('primaryAgent', 'isPrimary','1','0' );"/>
                                                        <input type="hidden" name="isPrimary" id="isPrimary"
                                                               value="<?php echo createAgent::Agent()->isPrimary ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allow user to submit loan audit?</label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowToLASubmit == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->allowToLASubmit ?>"
                                                               id="LASubmission"
                                                               onchange="toggleSwitch('LASubmission', 'allowToLASubmit','1','0' );"/>
                                                        <input type="hidden" name="allowToLASubmit" id="allowToLASubmit"
                                                               value="<?php echo createAgent::Agent()->allowToLASubmit ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                    <?php } ?>
                                    <?php if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                    } else { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Send email (about new features, lenders and
                                                    marketing)</label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->receiveUpdates == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->receiveUpdates ?>"
                                                       id="receiveUpdateId"
                                                       onchange="toggleSwitch('receiveUpdateId', 'receiveUpdates','1','0' );"/>
                                                <input type="hidden" name="receiveUpdates" id="receiveUpdates"
                                                       value="<?php echo createAgent::Agent()->receiveUpdates ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <?php if (PageVariables::$userRole != 'Branch') {
                                        if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                        } else { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allowed to upgrade/downgrade DIY clients
                                                        sometimes</label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->changeDIYPlan == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->changeDIYPlan ?>"
                                                               id="allowToChangeDIYId"
                                                               onchange="toggleSwitch('allowToChangeDIY', 'changeDIYPlan','1','0' );"/>
                                                        <input type="hidden" name="changeDIYPlan" id="changeDIYPlan"
                                                               value="<?php echo createAgent::Agent()->changeDIYPlan ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <?php if ($modOpt) { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">
                                                        Allow to edit interest rate / Cost of Capital / Yield spread?
                                                    </label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowEditToIR == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->allowEditToIR ?>"
                                                               id="allowToEditToIRId"
                                                               onchange="toggleSwitch('allowToEditToIRId', 'allowEditToIR','1','0' );"/>
                                                        <input type="hidden" name="allowEditToIR" id="allowEditToIR"
                                                               value="<?php echo createAgent::Agent()->allowEditToIR ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <?php if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                                        } else { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Do you want to send File Designation
                                                        information
                                                        to your clients with public notes?
                                                        <i class="tooltipAjax fas fa-info-circle text-primary "
                                                           data-html="true" data-toggle="tooltip"
                                                           title="If you choose no, when you send emails to your clients using the public notes feature they will not receive assigned employee or assigned Loan Officer/Broker contact details I.E. Name, Phone # , and Email Address."></i>
                                                    </label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowToSendFileDesignation == 1) { ?> checked="checked" <?php } ?>
                                                        value="<?php echo createAgent::Agent()->allowToSendFileDesignation ?>"
                                                               id="sendFileDesignation"
                                                               onchange="toggleSwitch('sendFileDesignation','allowToSendFileDesignation','1','0' );"/>
                                                        <input type="hidden" name="allowToSendFileDesignation"
                                                               id="allowToSendFileDesignation"
                                                               value="<?php echo createAgent::Agent()->allowToSendFileDesignation ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <?php
                                        if (in_array($PCID, $glRAMAccessPC) && PageVariables::$userRole == 'Super') {
                                            /** Show RAM tab **/ ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allowed to access RAM?</label>
                                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowToAccessRAM == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowToAccessRAM ?>"
                                                       id="AgentToAccessRAM"
                                                       onchange="toggleSwitch('AgentToAccessRAM', 'allowAgentToAccessRAM','1','0' );"/>
                                                <input type="hidden" name="allowAgentToAccessRAM"
                                                       id="allowAgentToAccessRAM"
                                                       value="<?php echo createAgent::Agent()->allowToAccessRAM ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                    <?php } ?>
                                    <?php if ((PageVariables::$userRole == 'Manager' || PageVariables::$userRole == 'Administrator' || PageVariables::$userRole == 'Super') && (in_array(createAgent::$processingCompanyId, $accessSecondaryWFPC))) {
                                        ?>
                                        <div class="col-lg-6 mb-7">
                                            <label class="font-weight-bold">Assigned Workflows</label>
                                            <select class="form-control" data-placeholder=" - Select Workflow - "
                                                    name="WFID[]"
                                                    id="WFID"
                                                    class="chzn-select odd" multiple="" style="width:250px;">
                                                <?php
                                                foreach (createAgent::$PCWorkflowArray as $w => $item) {
                                                    ?>
                                                    <option value="<?php echo trim($item['WFID']) ?>" <?php echo Arrays::isSelectedArray(createAgent::$assignedWFIDs, trim($item['WFID'])) ?>><?php echo trim($item['WFName']) ?></option>
                                                    <?php
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to Lock/unlock loan information?</label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowToLockLoanFileAgent == 1) { ?> checked="checked" <?php } ?>
                                                                       value="<?php echo createAgent::Agent()->allowToLockLoanFileAgent ?>"
                                                       id="allowToLockLoanFileAgentId"
                                                       onchange="toggleSwitch('allowToLockLoanFileAgentId', 'allowToLockLoanFileAgent','1','0' );"/>
                                                <input type="hidden" name="allowToLockLoanFileAgent"
                                                       id="allowToLockLoanFileAgent"
                                                       value="<?php echo createAgent::Agent()->allowToLockLoanFileAgent ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Display in webforms?</label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" type="checkbox"
                                                       value="<?php echo createAgent::Agent()->allowToSeeWebForms ?>"
                                                       id="allowToDispWebForms"
                                                       <?php if (createAgent::Agent()->allowToSeeWebForms == 1) { ?> checked="checked" <?php } ?>
                                                            onchange="toggleSwitch('allowToDispWebForms', 'allowToSeeWebForms','1','0' );"/>
                                                <input type="hidden" name="allowToSeeWebForms" id="allowToSeeWebForms"
                                                       value="<?php echo createAgent::Agent()->allowToSeeWebForms ?>">

                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (PageVariables::$userRole == 'Manager' || PageVariables::$userRole == 'Super') { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allow access to Internal Loan Program?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary <?php if (!$externalBroker) {
                                                        echo 'd-none';
                                                    } ?> "
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="This will enable the Loan Officer to add internal loan programs on loan application,
                                                        and it will also let the loan officer search via the main pipeline with a filter to search
                                                        internal loan programs"></i>
                                                </label>
                                                <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if (createAgent::Agent()->allowToAccessInternalLoanProgram == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo createAgent::Agent()->allowToAccessInternalLoanProgram ?>"
                                                                   id="allowToAccessInternalLoanProgramId"
                                                                   onchange="toggleSwitch('allowToAccessInternalLoanProgramId','allowToAccessInternalLoanProgram','1','0' );"/>
                                                              <input type="hidden"
                                                                     name="allowToAccessInternalLoanProgram"
                                                                     id="allowToAccessInternalLoanProgram"
                                                                     value="<?php echo createAgent::Agent()->allowToAccessInternalLoanProgram ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>

                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to Copy file?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title=" If turned on, users will be able to utilize the copy file feature from the pipeline."></i></label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if (createAgent::Agent()->allowToCopyFile == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo createAgent::Agent()->allowToCopyFile ?>"
                                                                   id="allowToCopyFileId"
                                                                   onchange="toggleSwitch('allowToCopyFileId','allowToCopyFile','1','0' );"/>
                                                              <input type="hidden" name="allowToCopyFile"
                                                                     id="allowToCopyFile"
                                                                     value="<?php echo createAgent::Agent()->allowToCopyFile ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to view Contact List?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, user will be able to create/View Contacts.."></i></label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if (createAgent::Agent()->allowToViewContactsList == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo createAgent::Agent()->allowToViewContactsList ?>"
                                                                   id="allowToViewContactsListId"
                                                                   onchange="toggleSwitch('allowToViewContactsListId','allowToViewContactsList','1','0' );"/>
                                                              <input type="hidden"
                                                                     name="allowToViewContactsList"
                                                                     id="allowToViewContactsList"
                                                                     value="<?php echo createAgent::Agent()->allowToViewContactsList ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- row div -->
                            </div> <!-- mail login div -->
                        </div>
                    </div>
                    <div class="card card-custom example example-compact">
                        <div class="card-header">
                            <h3 class="card-title">Documents and Notifications</h3>
                        </div>
                        <div class="card-body">
                            <div <?php if (createAgent::Agent()->allowAgentToLogin == 1 || createAgent::$brokerNo > 0) { ?> style="display:block;"  <?php } else { ?> style="display:none;" <?php } ?>>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Notify if Borrowers/Clients upload a document?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If Yes, this user will receive notifications from doc uploads (only if they are assigned to the file)."></i>
                                            </label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowAgentToGetBorrowerUploadDocsNotification == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowAgentToGetBorrowerUploadDocsNotification ?>"
                                                       id="allowAgentToGetBorrowerUploadDocs"
                                                       onchange="toggleSwitch('allowAgentToGetBorrowerUploadDocs', 'allowAgentToGetBorrowerUploadDocsNotification','1','0' );"/>
                                                <input type="hidden"
                                                       name="allowAgentToGetBorrowerUploadDocsNotification"
                                                       id="allowAgentToGetBorrowerUploadDocsNotification"
                                                       value="<?php echo createAgent::Agent()->allowAgentToGetBorrowerUploadDocsNotification ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if (PageVariables::$userRole != 'Branch') { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to delete/replace uploaded docs/e-signed
                                                    docs/binder docs
                                                    <i class="tooltipAjax fas fa-info-circle text-primary "
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="If Yes, user can delete the uploaded docs which he has uploaded. If No, user can not delete any of the document"></i>
                                                </label>
                                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->allowedToDeleteUplodedDocs == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::Agent()->allowedToDeleteUplodedDocs ?>"
                                                           id="allowToUploadDeleteDocs"
                                                           onchange="toggleSwitch('allowToUploadDeleteDocs', 'allowedToDeleteUplodedDocs','1','0' );"/>
                                                    <input type="hidden" name="allowedToDeleteUplodedDocs"
                                                           id="allowedToDeleteUplodedDocs"
                                                           value="<?php echo createAgent::Agent()->allowedToDeleteUplodedDocs ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to access & administer docs
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, the user will be able to view all uploaded docs, rename them, change the status of them, and add required doc types when needed. If NO, they will only be able to see & upload required docs with user specific permissions."></i>
                                            </label>
                                            <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control"
                                                   type="checkbox" <?php if (createAgent::Agent()->allowAgentToAccessLMRDocs == 1) { ?> checked="checked" <?php } ?>
                                                   value="<?php echo createAgent::Agent()->allowAgentToAccessLMRDocs ?>"
                                                   id="allowToaccessLMRDocs"
                                                   onchange="toggleSwitch('allowToaccessLMRDocs', 'allowAgentToAccessLMRDocs','1','0' );showEditSwitch('editSwitch','seeBilling');"/>
                                            <input type="hidden" name="allowAgentToAccessLMRDocs"
                                                   id="allowAgentToAccessLMRDocs"
                                                   value="<?php echo createAgent::Agent()->allowAgentToAccessLMRDocs ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Notify if new file is created?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="Will notify this user if a new file is created with them as the <?php echo $ttTitle; ?>"></i>
                                            </label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->sendNewDealAlert == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->sendNewDealAlert ?>"
                                                       id="sendDealAlertId"
                                                       onchange="toggleSwitch('sendDealAlertId', 'sendNewDealAlert','1','0' );"/>
                                                <input type="hidden" name="sendNewDealAlert" id="sendNewDealAlert"
                                                       value="<?php echo createAgent::Agent()->sendNewDealAlert ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    if (PageVariables::$userRole != 'Branch') {
                                        if (in_array('HMLO', createAgent::$moduleCodeArray)) { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Send email (Allowed to Send Marketing to
                                                        Broker's borrowers)
                                                        <i class="tooltipAjax fas fa-info-circle text-primary "
                                                           data-html="true" data-toggle="tooltip"
                                                           title="This protects the Broker's borrowers, so that only the broker can send marketing emails, not users in the back office or branch level."></i>
                                                    </label>
                                                    <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->allowToSendMarketingEmailForBRBO == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::Agent()->allowToSendMarketingEmailForBRBO ?>"
                                                           id="allowedToSendMarketingEmailId"
                                                           onchange="toggleSwitch('allowToSendMarketingEmailForBRBO','allowToSendMarketingEmailForBRBO','1','0' );"/>
                                                    <input type="hidden" name="allowToSendMarketingEmailForBRBO"
                                                           id="allowToSendMarketingEmailForBRBO"
                                                           value="<?php echo createAgent::Agent()->allowToSendMarketingEmailForBRBO ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php }
                                    } ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to send faxes?</label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowToSendFax == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowToSendFax ?>"
                                                       id="allowToUserSendFaxId"
                                                       onchange="toggleSwitch('allowToUserSendFaxId', 'allowToSendFax','1','0' );"/>
                                                <input type="hidden" name="allowToSendFax" id="allowToSendFax"
                                                       value="<?php echo createAgent::Agent()->allowToSendFax ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if ((PageVariables::$userRole == 'Manager' || PageVariables::$userRole == 'Super')) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to share file? <i
                                                            class="tooltipAjax fas fa-info-circle text-primary "
                                                            data-html="true"
                                                            data-toggle="tooltip"
                                                            title="if Yes, Share this file is enabled on the file."></i></label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->shareThisFile == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->shareThisFile ?>"
                                                       id="sharethisfile"
                                                       onchange="toggleSwitch('sharethisfile','shareThisFile','1','0' );"/>
                                                <input type="hidden" name="shareThisFile" id="shareThisFile"
                                                       value="<?php echo createAgent::Agent()->shareThisFile; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- New permissions added -->
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Notify if Back Office users upload a document?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="Requires user is assigned to file"></i>
                                                </label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" type="checkbox"
                                                       value="<?php echo createAgent::Agent()->notifyBODocUpload; ?>"
                                                       id="notifyBODocUploadCheckbox" <?php if (createAgent::Agent()->notifyBODocUpload == 1) { ?> checked="checked" <?php } ?>
                                                       onchange="toggleSwitch('notifyBODocUploadCheckbox', 'notifyBODocUpload','1','0' );"/>
                                                <input type="hidden" name="notifyBODocUpload" id="notifyBODocUpload"
                                                       value="<?php echo createAgent::Agent()->notifyBODocUpload; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Notify if Branch users upload a document?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="Requires user is assigned to file"></i>
                                                </label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" type="checkbox"
                                                       value="<?php echo createAgent::Agent()->notifyBranchDocUpload; ?>"
                                                       id="notifyBranchDocUploadCheckbox" <?php if (createAgent::Agent()->notifyBranchDocUpload == 1) { ?> checked="checked" <?php } ?>
                                                       onchange="toggleSwitch('notifyBranchDocUploadCheckbox', 'notifyBranchDocUpload','1','0' );"/>
                                                <input type="hidden" name="notifyBranchDocUpload"
                                                       id="notifyBranchDocUpload"
                                                       value="<?php echo createAgent::Agent()->notifyBranchDocUpload; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                        if (createAgent::$externalBroker == '1') { //LO ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Notify if Broker users upload a document?
                                                        <i class="tooltipAjax fas fa-info-circle text-primary"
                                                           data-html="true"
                                                           data-toggle="tooltip"
                                                           title="Requires user is assigned to file"></i>
                                                    </label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control" type="checkbox"
                                                               value="<?php echo createAgent::Agent()->notifyBrokerDocUpload; ?>"
                                                               id="notifyBrokerDocUploadCheckbox" <?php if (createAgent::Agent()->notifyBrokerDocUpload == 1) { ?> checked="checked" <?php } ?>
                                                               onchange="toggleSwitch('notifyBrokerDocUploadCheckbox', 'notifyBrokerDocUpload','1','0' );"/>
                                                        <input type="hidden" name="notifyBrokerDocUpload"
                                                               id="notifyBrokerDocUpload"
                                                               value="<?php echo createAgent::Agent()->notifyBrokerDocUpload; ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } elseif (createAgent::$externalBroker == '0') { //Broker?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Notify if Loan Officer users upload a
                                                        document?
                                                        <i class="tooltipAjax fas fa-info-circle text-primary"
                                                           data-html="true"
                                                           data-toggle="tooltip"
                                                           title="Requires user is assigned to file"></i>
                                                    </label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control" type="checkbox"
                                                               value="<?php echo createAgent::Agent()->notifyLODocUpload; ?>"
                                                               id="notifyLODocUploadCheckbox" <?php if (createAgent::Agent()->notifyLODocUpload == 1) { ?> checked="checked" <?php } ?>
                                                                onchange="toggleSwitch('notifyLODocUploadCheckbox', 'notifyLODocUpload','1','0' );"/>
                                                        <input type="hidden" name="notifyLODocUpload"
                                                               id="notifyLODocUpload"
                                                               value="<?php echo createAgent::Agent()->notifyLODocUpload; ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Notify if document upload request is sent to the
                                                    borrower/client?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="Requires user is assigned to file"></i>
                                                </label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" type="checkbox"
                                                       value="<?php echo createAgent::Agent()->notifyDocUploadRequest; ?>"
                                                       id="notifyDocUploadRequestCheckbox" <?php if (createAgent::Agent()->notifyDocUploadRequest == 1) { ?> checked="checked" <?php } ?>
                                                        onchange="toggleSwitch('notifyDocUploadRequestCheckbox', 'notifyDocUploadRequest','1','0' );"/>
                                                <input type="hidden" name="notifyDocUploadRequest"
                                                       id="notifyDocUploadRequest"
                                                       value="<?php echo createAgent::Agent()->notifyDocUploadRequest; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 <?php if (!createAgent::$externalBroker) {
                                            echo ' d-none ';
                                        } ?>">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to assign Back Office Employees
                                                </label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" type="checkbox"
                                                       value="<?php echo createAgent::Agent()->allowToAssignBOEmployee; ?>"
                                                       id="allowToAssignBOEmployeeCheckbox"
                                                    <?php if (createAgent::Agent()->allowToAssignBOEmployee == 1) { ?> checked="checked" <?php } ?>
                                                        onchange="toggleSwitch('allowToAssignBOEmployeeCheckbox', 'allowToAssignBOEmployee','1','0' );"/>
                                                <input type="hidden" name="allowToAssignBOEmployee"
                                                       id="allowToAssignBOEmployee"
                                                       value="<?php echo createAgent::Agent()->allowToAssignBOEmployee; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-6 <?php if (!createAgent::$externalBroker) {
                                            echo ' d-none ';
                                        } ?>">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to see all Brokers
                                                </label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" type="checkbox"
                                                       value="<?php echo createAgent::Agent()->allowToSeeAllBrokers; ?>"
                                                       id="allowToSeeAllBrokersCheckbox"
                                                    <?php if (createAgent::Agent()->allowToSeeAllBrokers == 1) { ?> checked="checked" <?php } ?>
                                                        onchange="toggleSwitch('allowToSeeAllBrokersCheckbox', 'allowToSeeAllBrokers','1','0' );"/>
                                                <input type="hidden" name="allowToSeeAllBrokers"
                                                       id="allowToSeeAllBrokers"
                                                       value="<?php echo createAgent::Agent()->allowToSeeAllBrokers; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- //New permissions added// -->
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card card-custom example example-compact">
                        <div class="card-header">
                            <h3 class="card-title">Special Reports or Features</h3>
                        </div>
                        <div class="card-body">
                            <div <?php if (createAgent::Agent()->allowAgentToLogin == 1 || createAgent::$brokerNo > 0) { ?> style="display:block;"  <?php } else { ?> style="display:none;" <?php } ?>>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Enable Two-Factor Authentication (2FA)?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="An additional layer of security for your account. A 2nd form of authentication can be sent by email or SMS message to provide you with a pin to enter on login. If you choose sms please be sure you have set your cell phone number on your profile"></i>
                                            </label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->enable2FAAuthentication == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->enable2FAAuthentication ?>"
                                                       id="allowAuthentication"
                                                       onchange="toggleSwitch('allowAuthentication', 'enable2FAAuthentication','1','0' );showEditSwitch('2FASelection','enable2FAAuthentication');"/>
                                                <input type="hidden" name="enable2FAAuthentication"
                                                       id="enable2FAAuthentication"
                                                       value="<?php echo createAgent::Agent()->enable2FAAuthentication ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                        <div class="form-group row align-items-center 2FASelection" <?php if (createAgent::Agent()->enable2FAAuthentication == 0) {
                                            echo "style='display:none;'";
                                        } ?>>
                                            <div class="col-lg-12">
                                                <div class="radio-inline">
                                                    <label for="TwoFATypeEmail"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="TwoFAType" id="TwoFATypeEmail"
                                                               value="email" <?php if (createAgent::Agent()->TwoFAType == 'email') {
                                                            echo 'checked';
                                                        } ?> >
                                                        <span></span>
                                                        Email
                                                    </label>
                                                    <label for="TwoFATypeSms"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="TwoFAType" id="TwoFATypeSms"
                                                               value="sms" <?php if (createAgent::Agent()->TwoFAType == 'sms') {
                                                            echo 'checked';
                                                        } ?>>
                                                        <span></span>
                                                        SMS
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if (PageVariables::$userRole != 'Branch') { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to see dashboard?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="If No, The entire dashboard page will be disabled. The user will go straight to their pipeline."></i>
                                                </label>
                                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->allowAgentToSeeDashboard == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::Agent()->allowAgentToSeeDashboard ?>"
                                                           id="allowToseeDashboard"
                                                           onchange="toggleSwitch('allowToseeDashboard', 'allowAgentToSeeDashboard','1','0' );"/>
                                                    <input type="hidden" name="allowAgentToSeeDashboard"
                                                           id="allowAgentToSeeDashboard"
                                                           value="<?php echo createAgent::Agent()->allowAgentToSeeDashboard ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                        <?php if (PageVariables::$isPCAllowEmailCampaign && (PageVariables::$userRole == 'Manager' || PageVariables::$userGroup == glUserGroup::USER_GROUP_SUPER)) { ?>

                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allowed to mass email?</label>
                                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowEmailCampaign == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowEmailCampaign ?>"
                                                       id="allowedMassEmail"
                                                       onchange="toggleSwitch('allowedMassEmail','allowEmailCampaign','1','0' );"/>
                                                <input type="hidden" name="allowEmailCampaign" id="allowEmailCampaign"
                                                       value="<?php echo createAgent::Agent()->allowEmailCampaign ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <?php if (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Manager') { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allowed to mass update
                                                        <i class="tooltipAjax fas fa-info-circle text-primary "
                                                           data-html="true"
                                                           data-toggle="tooltip"
                                                           title="This will enable an additional column in the main pipeline. For Managers, they will be able to do functions which include deactivating files, update status, Assign Employees and many others. For Non-Managers, Branch(s), and Agents (Loan Officer and Brokers), they will only be allowed to Deactivate files."></i></label>
                                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowToMassUpdate == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowToMassUpdate ?>"
                                                       id="allowToMassUpdateCheckbox"
                                                       onchange="toggleSwitch('allowToMassUpdateCheckbox','allowToMassUpdate','1','0' );"/>
                                                <input type="text"
                                                       name="allowToMassUpdate"
                                                       id="allowToMassUpdate"
                                                       value="<?php echo createAgent::Agent()->allowToMassUpdate; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>


                                        <?php if ($modOpt && (PageVariables::$userRole == 'Super' || PageVariables::$userGroup == 'Employee')) { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8">Allow to view workflow</label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowWorkflowEdit == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo createAgent::Agent()->allowWorkflowEdit ?>"
                                                               id="allowToWorkflowEdit"
                                                               onchange="toggleSwitch('allowToWorkflowEdit', 'allowWorkflowEdit','1','0' );"/>
                                                        <input type="hidden" name="allowWorkflowEdit"
                                                               id="allowWorkflowEdit"
                                                               value="<?php echo createAgent::Agent()->allowWorkflowEdit ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                    <?php } ?>
                                    <?php if (createAgent::Processor()->allowToCreateAloware == 1) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allow to access CallWise?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary "
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="CallWise is a 3rd party service that allows your team to make calls, and see & send text messages inside the loan file--> CallWise tab. Contact us to activate this service."></i>
                                                </label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" id="allowToCreateAlo"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowToCreateAloware == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowToCreateAloware ?>"
                                                       onchange="toggleSwitch('allowToCreateAlo', 'allowToCreateAloware','1','0' );"/>
                                                <input type="hidden" name="allowToCreateAloware"
                                                       id="allowToCreateAloware"
                                                       value="<?php echo createAgent::Agent()->allowToCreateAloware ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <?php if ((PageVariables::$userRole == 'Manager' || PageVariables::$userRole == 'Super')) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to Submit Offers?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="If Yes, Submit Offer is enabled."></i>
                                                </label>
                                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if (createAgent::Agent()->allowToSubmitOffer == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->allowToSubmitOffer ?>"
                                                       id="allowToSubmitOffer"
                                                       onchange="toggleSwitch('allowToSubmitOffer','allowtosubmitoffer','1','0' );"/>
                                                <input type="hidden" name="allowToSubmitOffer" id="allowtosubmitoffer"
                                                       value="<?php echo createAgent::Agent()->allowToSubmitOffer; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <?php if (PageVariables::$allowPCUsersToMarketPlace) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Enable Marketplace Tab?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary "
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="If yes, user will be able to use the marketplace feature."></i>
                                                </label>
                                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->allowToViewMarketPlace == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::Agent()->allowToViewMarketPlace ?>"
                                                           id="allowToViewMarketPlaceForm"
                                                           onchange="toggleSwitch('allowToViewMarketPlaceForm', 'allowToViewMarketPlace','1','0' );"/>
                                                    <input type="hidden" name="allowToViewMarketPlace"
                                                           id="allowToViewMarketPlace"
                                                           value="<?php echo createAgent::Agent()->allowToViewMarketPlace ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>

                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Enable Servicing Tab?
                                                <i class="tooltipAjax fas fa-info-circle text-primary "
                                                   data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, user will be able to use the servicing feature."></i>
                                            </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if (createAgent::Agent()->allowServicing == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo createAgent::Agent()->allowServicing ?>"
                                                           id="allowServicingCheckbox"
                                                           onchange="toggleSwitch('allowServicingCheckbox', 'allowServicing','1','0' );"/>
                                                    <input type="hidden" name="allowServicing"
                                                           id="allowServicing"
                                                           value="<?php echo createAgent::Agent()->allowServicing ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if ($allowAutomation && in_array(PageVariables::$userRole, ['Super', 'Manager'])) { //check for PC automation ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">
                                                    Display any automations that are triggered by this user?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary "
                                                       data-html="true"
                                                       data-toggle="tooltip" title=""
                                                       data-original-title="If yes, user will be able to see the automations that are triggered."></i>
                                                </label>
                                                <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if (createAgent::Agent()->allowToViewAutomationPopup == 1) { ?> checked="checked" <?php } ?>
                                                                   id="allowToViewAutomationPopupCheckbox"
                                                                   onchange="toggleSwitch('allowToViewAutomationPopupCheckbox', 'allowToViewAutomationPopup','1','0' );"/>
                                                            <input type="hidden" name="allowToViewAutomationPopup"
                                                                   id="allowToViewAutomationPopup"
                                                                   value="<?php echo createAgent::Agent()->allowToViewAutomationPopup; ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <?php if ((PageVariables::$userRole == 'Manager' || PageVariables::$userRole == 'Super')) { ?>
                                        <?php if (PageVariables::$pcAcqualifyStatus > 0 && createAgent::$pcAcqualifyId > 0) { ?>
                                            <div class="col-6">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-lg-8"> Allow To View Credit Screen?
                                                        <i class="tooltipAjax fas fa-info-circle text-primary "
                                                           data-html="true"
                                                           data-toggle="tooltip"
                                                           title="If yes, user will be able to View Credit Screen"></i>
                                                    </label>
                                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if (createAgent::Agent()->allowToViewCreditScreening == 1) { ?> checked="checked" <?php } ?>
                                                        value="<?php echo createAgent::Agent()->allowToViewCreditScreening ?>"
                                                               id="allowToViewCreditMenu"
                                                               onchange="toggleSwitch('allowToViewCreditMenu','allowToViewCreditScreening','1','0' );"/>
                                                        <input type="hidden" name="allowToViewCreditScreening"
                                                               id="allowToViewCreditScreening"
                                                               value="<?php echo createAgent::Agent()->allowToViewCreditScreening; ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } ?>
                                    <?php } ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Enable Pricing Engine?</label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" <?php if (PageVariables::$pcPriceEngineStatus == 0) {
                                                    echo 'disabled';
                                                } ?>
                                                       type="checkbox" <?php if (createAgent::Agent()->userPriceEngineStatus == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->userPriceEngineStatus ?>"
                                                       id="userPriceEngineStatusCheckbox"
                                                       onchange="toggleSwitch('userPriceEngineStatusCheckbox', 'userPriceEngineStatus','1','0' );
                                                       showEditSwitch('priceEngineSwitch','userPriceEngineStatus');"/>
                                                <input type="hidden" name="userPriceEngineStatus"
                                                       id="userPriceEngineStatus"
                                                       value="<?php echo createAgent::Agent()->userPriceEngineStatus ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6"></div>
                                    <div class="col-6 priceEngineSwitch"
                                         style="<?php echo createAgent::$showPriceEngineSwitch; ?>">
                                        <div class="form-group row">
                                            <label class="col-3">Login</label>
                                            <div class="col-6">
                                                <input type="text" name="loanpassLogin" id="loanpassLogin"
                                                       class="form-control" autocomplete="off"
                                                       value="<?php echo createAgent::Agent()->loanpassLogin; ?>"
                                                    <?php if (PageVariables::$pcPriceEngineStatus == 0) {
                                                        echo 'disabled';
                                                    } ?>>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 priceEngineSwitch"
                                         style="<?php echo createAgent::$showPriceEngineSwitch; ?>">
                                        <div class="form-group row">
                                            <label class="col-3">Password</label>
                                            <div class="col-6">
                                                <input type="password" name="loanpassPassword" id="loanpassPassword"
                                                       class="form-control" autocomplete="off"
                                                       value="<?php echo createAgent::Agent()->loanpassPassword; ?>"
                                                    <?php if (PageVariables::$pcPriceEngineStatus == 0) {
                                                        echo 'disabled';
                                                    } ?>>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } else {
                    ?>
                    <input type="hidden" name="allowAgentToLogin"
                           value="<?php echo createAgent::Agent()->allowAgentToLogin; ?>">
                    <input type="hidden" name="allowAgentToAccessLMRDocs"
                           value="<?php echo createAgent::Agent()->allowAgentToAccessLMRDocs; ?>">
                    <input type="hidden" name="allowAgentToSendHomeownerLink"
                           value="<?php echo createAgent::Agent()->allowAgentToSendHomeownerLink; ?>">
                    <input type="hidden" name="allowedToUpdateFiles"
                           value="<?php echo createAgent::Agent()->allowedToUpdateFiles; ?>">
                    <input type="hidden" name="allowToAccessPrivateNotes"
                           value="<?php echo createAgent::Agent()->allowToAccessPrivateNotes; ?>">
                    <input type="hidden" name="allowAgentToCreateFiles"
                           value="<?php echo createAgent::Agent()->allowAgentToCreateFiles; ?>">
                    <input type="hidden" name="allowAgentToCreateTasks"
                           value="<?php echo createAgent::Agent()->allowAgentToCreateTasks; ?>">
                    <input type="hidden" name="allowAgentToSeeDashboard"
                           value="<?php echo createAgent::Agent()->allowAgentToSeeDashboard; ?>">
                    <input type="hidden" name="allowedToDeleteUplodedDocs"
                           value="<?php echo createAgent::Agent()->allowedToDeleteUplodedDocs; ?>">
                    <input type="hidden" name="allowedToEditOwnNotes"
                           value="<?php echo createAgent::Agent()->allowedToEditOwnNotes; ?>">
                    <input type="hidden" name="seeBilling" value="<?php echo createAgent::Agent()->seeBilling; ?>">
                    <input type="hidden" name="allowToEditCCInfo"
                           value="<?php echo createAgent::Agent()->allowToEditCCInfo; ?>">
                    <input type="hidden" name="isPrimary" value="<?php echo createAgent::Agent()->isPrimary; ?>">
                    <?php
                } ?>


                <?php if (in_array('HMLO', createAgent::$moduleCodeArray)) {
                } else { ?>
                    <div class="form-group row">
                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8 font-weight-bold">Assign employee(s) to this Loan
                                    Officer/Broker</label>
                            </div>
                        </div>
                        <table width="100%">
                            <tr>
                                <th width="40%" colspan="2"></th>
                                <th width="20%">Not assigned</th>
                                <th width="20%">Assigned,allow communication <i
                                            class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                            data-toggle="tooltip"
                                            title="The Loan Officer/Broker will ONLY be able to communicate with the selected people."></i>
                                </th>
                                <th width="20%">Assigned,Do not" allow communication</th>
                            </tr>
                            <tr>
                                <td colspan="5">
                                    <div id="empId_container">
                                        <table width="100%">
                                            <?php
                                            $e = 0;
                                            $employeeIds = '';
                                            foreach (createAgent::$employeeInfoArray as $emp => $item) {
                                                $allowToCommunicate = '';
                                                $employeeName = trim($item['processorName']);
                                                $employeeRole = trim($item['role']);
                                                $employeeId = trim($item['AID']);
                                                $employeeName = ucwords($employeeName) . ' (' . $employeeRole . ')';
                                                if ($e > 0) {
                                                    $employeeIds .= ',';
                                                }
                                                $employeeIds .= $employeeId;
                                                if (count(createAgent::$preferredEIdArray) > 0) {
                                                    if (array_key_exists($employeeId, createAgent::$preferredEIdArray)) {
                                                        $allowToCommunicate = createAgent::$preferredEIdArray[$employeeId]['allowToCommunicate'];
                                                        $allowToCommunicate = trim($allowToCommunicate);
                                                    }
                                                }
                                                $e++;
                                                ?>
                                                <tr <?php if (($emp + 1) % 2 == 0) { ?> class="even bbg" <?php } else { ?> class="odd bbg" <?php } ?>>
                                                    <td width="40%" colspan="2" style="text-align:left;">
                                                        <b><?php echo $employeeName; ?></b></td>
                                                    <td width="20%" style="text-align:center;">
                                                        <input type="radio" name="employee_<?php echo $employeeId; ?>"
                                                               value="" <?php echo Strings::isChecked($allowToCommunicate, ''); ?>>
                                                    </td>
                                                    <td width="20%" style="text-align:center;">
                                                        <input type="radio" name="employee_<?php echo $employeeId; ?>"
                                                               <?php if (in_array('HMLO', createAgent::$moduleCodeArray)) { ?>value="0"
                                                               <?php } else { ?>value="1" <?php } ?>
                                                            <?php
                                                            if (createAgent::$brokerNo > 0) {
                                                                echo Strings::isChecked($allowToCommunicate, '1');
                                                            } else {
                                                                echo 'checked';
                                                            }
                                                            ?>>
                                                    </td>
                                                    <td width="20%" style="text-align:center;">
                                                        <input type="radio" name="employee_<?php echo $employeeId; ?>"
                                                               value="0" <?php echo Strings::isChecked($allowToCommunicate, '0'); ?>>
                                                    </td>
                                                </tr>
                                                <?php
                                            }
                                            ?>
                                        </table>
                                    </div>
                                </td>
                            </tr>

                        </table>
                    </div>
                <?php } ?>

                <input type="hidden" name="employeeIds" id="employeeIds" value="<?php echo $employeeIds; ?>">


                <?php
                if (PageVariables::$userRole == 'Branch') {
                    ?>
                    <input type="hidden" name="executiveId[]" id="employeeIds"
                           value="<?php echo PageVariables::$userNumber; ?>">
                <?php } ?>
                <!--<div>&nbsp;&nbsp;&nbsp;* Your Added Loan Officer/Mortgage Broker(s) are presumed as Agreed to Terms and Conditions.</div>-->
            <?php } ?>
        </div>
    </div>
    <?php
    if (PageVariables::$userRole == 'Super'
        || PageVariables::$userRole == 'Manager'
        || (PageVariables::$userRole == 'Branch' && createAgent::$allowLMRToEditAgentProfile == 1)
        || (PageVariables::$allowEmpToSeeAgent == 1 && PageVariables::$allowEmpToCreateAgent == 1)
    ) {
        if ((PageVariables::$userRole == 'Manager' || PageVariables::$userRole == 'Super') && PageVariables::$glThirdPartyServices > 0) { ?>
            <div class="card card-custom mb-2">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label">Integrations</h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="javascript:void(0)"
                           class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                           data-card-tool="toggle" data-section="">
                            <i class="ki icon-nm ki-arrow-down"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body accordion" style="display: none">
                    <div class="form-group row">
                        <?php if (PageVariables::$glThirdPartyServices > 0) { ?>
                            <div class="card card-custom col-md-12 mb-3">
                                <div class="card-header card-header-tabs-line bg-gray-100">
                                    <div class="card-title">
                                        <h3 class="card-label">Credit Reporting Agencies:</h3>
                                    </div>
                                    <div class="card-toolbar" id="craHeader"
                                         onclick="Validation.controlChildElements('craHeader','craProducts','showHide')">
                                                        <span class="switch switch-icon">
                                                            <label>
                                                                <input class="form-control"
                                                                       type="checkbox" <?php if (createAgent::Agent()->thirdPartyServices == 1) { ?> checked="checked" <?php } ?>
                                                                       value="<?php echo createAgent::Agent()->thirdPartyServices ?>"
                                                                       id="thirdPartyLink"
                                                                       onchange="toggleSwitch('thirdPartyLink','thirdPartyServices','1','0' );showEditSwitch('thirdPartySwitch','thirdPartyServices');"/>
                                                                <input type="hidden" name="thirdPartyServices"
                                                                       id="thirdPartyServices"
                                                                       value="<?php echo createAgent::Agent()->thirdPartyServices ?>">
                                                                <span></span>
                                                            </label>
                                                        </span>
                                        <div class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                                             data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title=""
                                             data-original-title="Toggle Card">
                                            <i class="ki icon-nm ki-arrow-down"></i>
                                        </div>
                                    </div>
                                </div>


                                <div class="row pt-4" id="craProducts" style="display: none;">
                                    <?php foreach ($glThirdPartyServicesCRA as $cra => $craValue) {
                                        if (in_array($cra, $thirdPartyServiceCSRArray)) {
                                            $thirdPartyPassword = $thirdPartyServicesUserDetails['cra_' . $cra]['password'];
                                            ?>
                                            <div class="col-6 thirdPartySwitch pr-6"
                                                 style="<?php echo createAgent::$showThirdPartyEditSwitch; ?>">
                                                <div class="form-group row align-items-center">
                                                    <h6 class="col-lg-12"> <?= $craValue->Name; ?> </h6>
                                                    <label class="col-lg-2">User Name</label>
                                                    <div class="col-lg-10">
                                                        <input type="text"
                                                               name="<?= 'thirdPartyServicesData[' . $cra . '][username]' ?>"
                                                               id="thirdPartyUsername" class="form-control"
                                                               value="<?= $thirdPartyServicesUserDetails['cra_' . $cra]['username']; ?>">
                                                    </div>
                                                    <label class="col-lg-2">Password</label>
                                                    <div class="col-lg-10">
                                                        <input type="text" autocomplete="off"
                                                               name="<?= 'thirdPartyServicesData[' . $cra . '][password]' ?>"
                                                               id="thirdPartyPassword" class="form-control"
                                                               value="<?= $thirdPartyPassword ? '*****' . substr($thirdPartyPassword, -4) : ''; ?>">
                                                    </div>
                                                </div>
                                                <hr>
                                            </div>

                                        <?php }
                                    } ?>
                                </div>
                            </div>
                        <?php }
                        if (PageVariables::$glThirdPartyServicesLegalDocs > 0) { ?>
                            <div class="card card-custom col-md-12 mb-3">
                                <div class="card-header card-header-tabs-line bg-gray-100">
                                    <div class="card-title">
                                        <h3 class="card-label">Legal Docs:</h3>
                                    </div>
                                    <div class="card-toolbar" id="legalDocsHeader" onclick="Validation.controlChildElements('legalDocsHeader','legalDocsProducts','showHide')">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" id="thirdPartyLinkLegalDocs"
                                                       type="checkbox" <?php if (createAgent::Agent()->thirdPartyServicesLegalDocs == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo createAgent::Agent()->thirdPartyServicesLegalDocs ?>"
                                                       onchange="toggleSwitch('thirdPartyLinkLegalDocs', 'thirdPartyServicesLegalDocs','1','0' );showEditSwitch('thirdPartySwitchLegalDocs','thirdPartyServicesLegalDocs');"/>
                                                <input type="hidden" name="thirdPartyServicesLegalDocs" id="thirdPartyServicesLegalDocs"
                                                       value="<?php echo createAgent::Agent()->thirdPartyServicesLegalDocs ?>" >
                                                <span></span>
                                            </label>
                                        </span>
                                        <div class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                                             data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card" style="visibility: hidden;">
                                            <i class="ki icon-nm ki-arrow-down"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    <?php } ?>
    <div class="col-md-12 mt-5">
        <?php if (PageVariables::$userRole == 'Super'
            || createAgent::$brokerNo > 0
            || createAgent::$PCAllowToCreate == 1
            || PageVariables::$userRole == 'Branch'
            || PageVariables::$userRole == 'Manager'
            || (PageVariables::$allowEmpToSeeAgent == 1 && PageVariables::$allowEmpToCreateAgent == 1) || PageVariables::$userRole == 'Agent') { ?>
            <input type="hidden" name="statusOpt" id="statusOpt" value="1">
            <div class="row d-flex justify-content-center">
                <button type="submit" name="submit" id="submit" class="btn btn-primary font-weight-bold">Save
                </button>
            </div>
        <?php } ?>
    </div>
<?php } ?>
<script type="text/javascript">
    $('.brokerfiledocs').change(function () {
        var filename = $(this).val();
        var idval = (this.id).split('_');
        var lastIndex = filename.lastIndexOf("\\");
        if (lastIndex >= 0) {
            filename = filename.substring(lastIndex + 1);
            var fileDocsName = filename.substr(0, filename.lastIndexOf('.'));
        }
        $('#docNameDriver_' + idval[1]).val(fileDocsName);
    });

    $(".selectALlCheckBox").click(function () {
        selectedIDAll = $(this).attr('data-id');
        if (this.checked) {
            selectAllOptions(selectedIDAll);
        } else {
            removeAllOptions(selectedIDAll);
        }
    });

    $("input[name='allowAgentToEditLMRFile']").click(function () {
        let _allowAgentToEditLMRFile = parseInt($("input[name=allowAgentToEditLMRFile]:checked").val());
        if (_allowAgentToEditLMRFile === 1 || _allowAgentToEditLMRFile === 2) {
            $('#allowToupdateFileAndClientRow').css('display', 'table-row')
        } else {
            $('#allowToupdateFileAndClientRow').hide();
        }
    });

    function showEditSwitch(targetName, parent) {
        var pVal = $('#' + parent).val();
        if (pVal == 1) {
            $("." + targetName).css("display", "table-row");
        } else {
            $("." + targetName).css("display", "none");
        }
    }

    function showAndHideLoanTypeFields() {
        let typeOfLoansOffered = $('#typeOfLoansOffered').val();
        $('.agencyMortgageLending').addClass('d-none');
        $('.privateLending').addClass('d-none');
        $('.creLending').addClass('d-none');
        $('.businessLending').addClass('d-none');
        $(".agencyMortgageLending :input").prop("disabled", true);
        $(".privateLending :input").prop("disabled", true);
        $(".creLending :input").prop("disabled", true);
        $(".businessLending :input").prop("disabled", true);
        for (let i = 0; i < typeOfLoansOffered.length; i++) {
            let val = typeOfLoansOffered[i];
            switch (val) {
                case '1':
                    $('.agencyMortgageLending').removeClass('d-none');
                    $(".agencyMortgageLending :input").prop("disabled", false);
                    break;
                case '2':
                    $('.privateLending').removeClass('d-none');
                    $(".privateLending :input").prop("disabled", false);
                    break;
                case '3':
                    $('.creLending').removeClass('d-none');
                    $(".creLending :input").prop("disabled", false);
                    break;
                case '4':
                    $('.businessLending').removeClass('d-none');
                    $(".businessLending :input").prop("disabled", false);
                    break;
            }
        }
        let brokerPartnerType = $('#brokerPartnerType').val();
        if (brokerPartnerType != '2' && brokerPartnerType != '5') {
            addRemoveMandatoryForLoansOfferd('add');
        } else {
            addRemoveMandatoryForLoansOfferd('');
        }
    }

    function populateTheBrokerData() {
        if ($('#useSameContactInfo').is(':checked')) {
            $('#primaryContactFName').val($('#firstName').val());
            $('#primaryContactLName').val($('#lastName').val());
            $('#primaryContactPhone').val($('#cellNumber').val());
            $('#primaryContactEmail').val($('#email').val());
            $('.useSameContactInfo').prop('readonly', true);
        } else {
            $('#primaryContactFName').val('');
            $('#primaryContactLName').val('');
            $('#primaryContactPhone').val('');
            $('#primaryContactEmail').val('');
            $('.useSameContactInfo').prop('readonly', false);
        }
    }

    function getBranchLoanOfficers() {
        console.log({
            func: 'getBranchLoanOfficers',
        });
        let executiveId = $('#executiveId').val();
        /**
         * Get PCID in the case of getting the broker list on select the branch for respective PC
         */

        $.ajax({
            type: 'POST',
            url: siteSSLUrl + "backoffice/getBranchLoanOfficers.php",
            data: jQuery.param(
                {
                    'executiveId': executiveId.toString(),
                    'PCID': $("#PCID").val(),
                    'option': 'list',
                }
            ),
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            success: function (agentList) {

                var obj = $.parseJSON(agentList);

                $('#loanOfficersId').empty();
                var optionAgent = '<option value="">- Select -</option>';
                var optionSecondaryAgent = optionAgent;
                var agentFileAccess = userRole = '';


                $.each(obj, function (agentId, agentInfo) {
                    agentInfoArray = agentInfo.split("^^@@^^");
                    agentInfo1 = agentInfoArray[0];
                    externalBroker = agentInfoArray[1];
                    userRole = agentInfoArray[3];
                    isExternalBroker = agentInfoArray[4];
                    if (externalBroker == '1') {
                        optionSecondaryAgent += '  <option value="' + agentId + '">' + agentInfo1 + '</option>\n';
                    }

                });

                $('#loanOfficersId').append(optionSecondaryAgent).trigger("chosen:updated");

            }
        });
    }

    var LWFormControls = function () {
        var _formSubmitValidation = function () {
            var formIdToSubmit = $('#newBrokerForm');
            jQuery.validator.addMethod(
                "validateUploadFormNew",
                function (value, element) {
                    /* if (this.optional(element)) {
                    return true;
                    }*/
                    return isFileUploaded();
                },
                jQuery.validator.messages.validateUploadFormNew
            );
            jQuery.validator.addMethod("haveCompanyNMLSValidation", function (value, element) {
                return !(!$("input[name=haveCompanyNMLS]:checked").val() && ($('#brokerPartnerType').val() == '2' || $('#brokerPartnerType').val() == '1'));
            }, "Please Select Company NMLS License");
            jQuery.validator.addMethod("havePersonalNMLSValidation", function (value, element) {

                return !(!$("input[name=havePersonalNMLS]:checked").val() && ($('#brokerPartnerType').val() == '2' || $('#brokerPartnerType').val() == '1'));
            }, "Please Select Personal NMLS License");
            jQuery.validator.addMethod("haveCompanyStateLicenseValidation", function (value, element) {
                return !(!$("input[name=haveCompanyStateLicense]:checked").val() && ($('#brokerPartnerType').val() == '2' || $('#brokerPartnerType').val() == '1'));
            }, "");
            jQuery.validator.addMethod("havePersonalStateLicenseValidation", function (value, element) {

                return !(!$("input[name=havePersonalStateLicense]:checked").val() && ($('#brokerPartnerType').val() == '2' || $('#brokerPartnerType').val() == '1'));
            }, "");
            jQuery.validator.addMethod("companyStateLicenseValidation", function (value, element) {
                    var selectCount = 0;
                    $(".companyStateLicenseCls").each(function () {
                        if (!$(this).val()) {
                            selectCount++;
                        }
                    });
                    if (selectCount > 0) {
                        return false;
                    } else {
                        return true;
                    }
                }, "Please select Company State License"
            )
            ;
            jQuery.validator.addMethod("personalStateLicenseValidation", function (value, element) {
                var selectCount = 0;
                $(".personalStateLicenseCls").each(function () {
                    if (!$(this).val()) {
                        selectCount++;
                    }
                });
                return selectCount <= 0;
            }, "Please select Personal State License");
            jQuery.validator.addMethod("emptyValue", function (value, element) {
                return value.trim() != "";
            }, "");
            jQuery.validator.addMethod("agreeTC", function (value, element) {
                return $("input[name=agreeTC]:checked").val();
            }, "Please Select Terms And Conditions");
            jQuery.validator.addMethod("cellNumberValidation", function (value, element) {
                let _validationNumber = value.replace(/[^0-9]/ig, "");
                return _validationNumber.length === 10;
                //  return this.optional(element) || /^http:\/\/mycorporatedomain.com/.test(value);
            }, "Please Enter Valid Cell Number");
            jQuery.validator.addMethod("captchaValidation", function (value, element) {
                if (!$('#publicUser').val()) {
                    return true;
                } else {
                    let captchaSettings = 1;
                    captchaSettings = <?php echo RECAPTCHA_DISABLE; ?>;
                    var checkCaptcha = $('#validCaptcha').val();
                    if (!captchaSettings && checkCaptcha == 1) {
                        var response = grecaptcha.getResponse();
                        if (response.length == 0) {
                            //reCaptcha not verified
                            $('#human_valid').removeClass('hidden');
                            $('.submit').removeAttr('disabled');
                            return false;
                        } else {
                            //hide note
                            $('#human_valid').addClass('hidden');
                            return true;
                        }
                    } else {
                        return true;
                    }
                }
            }, "");
            formIdToSubmit.validate({
                ignore: ".ignoreValidation",
                rules: {
                    email: {
                        required: true,
                        email: true
                    },
                    emailConfirm: {
                        equalTo: "#email",
                        email: true
                    },
                    firstName: {
                        emptyValue: true,
                        required: true,
                    },
                    lastName: {
                        emptyValue: true,
                        required: true,
                    },
                    company: {
                        emptyValue: true,
                        required: true,
                    },
                    'typeOfLoansOffered[]': "required",
                    cellNumber: {
                        cellNumberValidation: true,
                    },
                    haveCompanyNMLS: {
                        haveCompanyNMLSValidation: true,
                    },
                    havePersonalNMLS: {
                        havePersonalNMLSValidation: true,
                    },
                    haveCompanyStateLicense: {
                        haveCompanyStateLicenseValidation: true,
                    },
                    havePersonalStateLicense: {
                        havePersonalStateLicenseValidation: true,
                    },
                    companyStateLicense: {
                        companyStateLicenseValidation: true,
                    },
                    personalStateLicense: {
                        personalStateLicenseValidation: true,
                    },
                    externalBroker: "required",
                    'executiveId[]': "required",
                    pwd: {
                        required: true,
                    },
                    confirmPwd: {
                        equalTo: "#pwd"
                    },
                    'displayDocName[]': {
                        validateUploadFormNew: true,
                    },
                    address: "required",
                    city: "required",
                    zip: "required",
                    state: "required",
                    brokerPartnerType: "required",
                    license: "required",
                    eniNumber: "required",
                    'prefCommunication[]': "required",
                    website: "required",
                    NMLSLicense: "required",
                    personalNMLSLicense: "required",
                    ssnNumber: "required",
                    linkedInURL: "required",
                    ofEmployees: "required",
                    'states[]': "required",
                    agreeTC: "required",
                    bestServeYourNeeds: "required",
                    primaryContactFName: "required",
                    primaryContactLName: "required",
                    primaryContactEmail: "required",
                    primaryContactPhone: "required",
                    'broker[1][experienceWith]': "required",
                    'broker[2][experienceWith]': "required",
                    'broker[3][experienceWith]': "required",
                    'broker[4][experienceWith]': "required",
                    'broker[1][unitsFunded]': "required",
                    'broker[2][unitsFunded]': "required",
                    'broker[3][unitsFunded]': "required",
                    'broker[4][unitsFunded]': "required",
                    'broker[1][volumnFunded]': "required",
                    'broker[2][volumnFunded]': "required",
                    'broker[3][volumnFunded]': "required",
                    'broker[4][volumnFunded]': "required",
                    'broker[1][experienceAndBackground]': "required",
                    'broker[2][experienceAndBackground]': "required",
                    'broker[3][experienceAndBackground]': "required",
                    'broker[4][experienceAndBackground]': "required",
                    validCaptcha: {
                        captchaValidation: true,
                    },
                },
                messages: {
                    email: {
                        required: "Please Enter E-mail address.",
                        email: "Please Enter Valid E-mail address."
                    },

                    emailConfirm: "Please Confirm Your E-mail address.",
                    firstName: "Please Enter the First Name",
                    lastName: "Please Enter the Last Name",
                    company: "Please Enter the Company",
                    'typeOfLoansOffered[]': "Please Select the Type of Loans Offered",
                    cellNumber: {
                        cellNumberValidation: "Please Enter Valid Cell Number",
                    },
                    haveCompanyNMLS: {
                        haveCompanyNMLSValidation: "Please Select Company NMLS License"
                    },
                    havePersonalNMLS: {
                        havePersonalNMLSValidation: "Please Select Personal NMLS License"
                    },
                    haveCompanyStateLicense: {
                        haveCompanyStateLicenseValidation: "Please Select Company Do You Have State License"
                    },
                    havePersonalStateLicense: {
                        havePersonalStateLicenseValidation: "Please Select Personal Do You have Personal License"
                    },
                    companyStateLicense: {
                        companyStateLicenseValidation: "Please Select Company State License",
                    },
                    personalStateLicense: {
                        personalStateLicenseValidation: "Please Select Personal State License",
                    },
                    externalBroker: 'Please Select Agent Type',
                    'executiveId[]': 'Please select one or more branches',
                    pwd: {
                        required: "Enter Your Password.",
                    },
                    confirmPwd: "Password does not match.",
                    'displayDocName[]': {
                        validateUploadFormNew: "Please upload file",
                    },
                    address: "Please Enter Address",
                    city: "Please Enter City",
                    zip: "Please Enter Zip Code",
                    state: "Please Select State",
                    brokerPartnerType: "Please Select Broker/Partner Type",
                    license: "Please Enter License #",
                    eniNumber: "Please Enter EIN #",
                    'prefCommunication[]': "Please Select Preferred Communication",
                    website: "Please Enter Website",
                    NMLSLicense: "Please Enter Company NMLS License #",
                    personalNMLSLicense: "Please Enter Personal NMLS License #",
                    ssnNumber: "Please Enter Social Security Number",
                    linkedInURL: "Please Enter LinkedIn URL",
                    ofEmployees: "Please Enter # of employees",
                    'states[]': "Please Select States",
                    agreeTC: "Please Select Terms And Conditions",
                    bestServeYourNeeds: "Please write a few sentences about your relevant experience",
                    primaryContactFName: "Please Enter Primary Contact First Name",
                    primaryContactLName: "Please Enter Primary Contact Last Name",
                    primaryContactEmail: "Please Enter Primary Contact Email",
                    primaryContactPhone: "Please Enter Primary Contact Phone",
                    'broker[1][experienceWith]': "Please Enter Years of experience with Agency Mortgage Lending",
                    'broker[2][experienceWith]': "Please Enter Years of experience with Private Lending",
                    'broker[3][experienceWith]': "Please Enter Years of experience with CRE Lending",
                    'broker[4][experienceWith]': "Please Enter Years of experience with Business Lending",
                    'broker[1][unitsFunded]': "Please Enter of units funded in last 12 months for Agency Mortgage Lending",
                    'broker[2][unitsFunded]': "Please Enter of units funded in last 12 months for Private Lending",
                    'broker[3][unitsFunded]': "Please Enter of units funded in last 12 months for CRE Lending",
                    'broker[4][unitsFunded]': "Please Enter of units funded in last 12 months for Business Lending",
                    'broker[1][volumnFunded]': "Please Enter Loan Volumefunded in last 12 months for Agency Mortgage Lending",
                    'broker[2][volumnFunded]': "Please Enter Loan Volumefunded in last 12 months for Private Lending",
                    'broker[3][volumnFunded]': "Please Enter Loan Volumefunded in last 12 months for CRE Lending",
                    'broker[4][volumnFunded]': "Please Enter Loan Volumefunded in last 12 months for Business Lending",
                    'broker[1][experienceAndBackground]': "Please Enter Detailed experience & background with Agency Mortgage Lending",
                    'broker[2][experienceAndBackground]': "Please Enter Detailed experience & background with Private Lending",
                    'broker[3][experienceAndBackground]': "Please Enter Detailed experience & background with CRE Lending",
                    'broker[4][experienceAndBackground]': "Please Enter Detailed experience & background with Business Lending",
                    validCaptcha: {
                        captchaValidation: "",
                    },

                },
                errorElement: "em",
                errorPlacement: function (error, element) {
                    // Add the `invalid-feedback` class to the error element
                    error.addClass("invalid-feedback");

                    if (element.prop("type") === "checkbox") {
                        error.insertAfter(element.next("label"));
                    } else if (element.prop("name") === 'externalBroker') {
                        error.insertAfter(".externalBrokerClass");
                    } else {
                        if (element.prop("name") === 'role') {
                            element.next('.input-group-append').after(error);
                        } else {
                            error.insertAfter(element);
                        }
                    }
                },
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-valid").removeClass("is-invalid");
                },
                submitHandler: function (form) {

                    ajaxUrl = $(formIdToSubmit).attr('action');
                    formData = $(formIdToSubmit).serialize();
                    formData = new FormData($('#newBrokerForm')[0]);

                    var ajaxRequest = $.ajax({
                        url: ajaxUrl,
                        type: "POST",
                        data: formData,
                        processData: false,
                        contentType: false,
                        beforeSend: function () {
                            eval("document.newBrokerForm.submit.disabled = true");
                            $(".submit").addClass("disabled");
                        },
                        complete: function () {
                            //UnBlockDiv('employeeCreateDiv');
                        },
                        success: function (response, status, xhr) {
                            //closeModal();
                            res = JSON.parse(response);
                            if (parseInt(res.code) === 100) {
                                toastrNotification(res.msg, 'success');
                                setTimeout(function () {
                                    window.location.href = res.redirecturl;
                                }, 3000);

                            } else {
                                toastrNotification(res.msg, 'error');
                            }
                        },
                        error: function (jqXhr, textStatus, errorMessage) {
                            toastrNotification(errorMessage, 'error');
                        }
                    });
                }
            });
        }
        return {
            // public functions
            init: function () {
                _formSubmitValidation();
            }
        };
    }();

    $(document).ready(function () {
        LWFormControls.init();
    });
</script>
<script>
    $(document).ready(function () {
        const forbiddenChars = /[\/\\:*?"<>|]/; // Forbidden characters pattern

        // For text inputs with the picsOfProp class
        $(document).on('mouseout', '.chooseDocUploadName', function () {
            var fileName = $(this).val();

            // Check for two consecutive periods (..)
            if (fileName.indexOf('..') !== -1) {
                toastr.error("Invalid file name: '..' is not allowed.", "Error", {
                    timeOut: 3000,
                    closeButton: true,
                    progressBar: true,
                });
                $(this).val(''); // Clear the field
                return;
            }

            // Check for forbidden characters
            if (forbiddenChars.test(fileName)) {
                toastr.error("Invalid file name: The file name cannot contain any of the following characters: / \\ : * ? \" < > |", "Error", {
                    timeOut: 3000,
                    closeButton: true,
                    progressBar: true,
                });
                $(this).val(''); // Clear the field
                return;
            }
        });

        // For file chooser inputs with multiple classes
        $(document).on('change', '.chooseDocUploadFile', function () {
            // Check if a file is selected
            if (this.files && this.files[0]) {
                var fileName = this.files[0].name;

                // Check for two consecutive periods using regex
                if (/\.\./.test(fileName)) {
                    toastr.error("Error: The file name cannot contain consecutive periods. Please rename your file and try again.");
                    $(this).val(''); // Clear the input to force re-selection
                    return;
                }
            }
        });
    });
</script>
<!-- brokerProfileForm.php -->
