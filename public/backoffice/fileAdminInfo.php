<?php
// Section Header: Admin Info - sectionID: Admin
global $oBroker, $moduleCode, $PCID, $fileHMLONewLoanInfo,
       $brokerNumber, $userUrl, $clientId, $executiveId, $borrowerNumberOfDeals,
       $PCListArray, $publicUser, $DIYUser, $allowClientToCreateHMLOFile, $LMRId, $agentNumber, $userName,
       $userNumber, $allowToChangeOrAssignBranchForFile, $tabIndex, $branchList, $externalBroker, $dummyBrokerId, $moduleRequested,
       $fileModuleInfo, $fileTab, $activeTab, $servicesRequested, $LMRClientTypeInfo, $fileLP, $glLMRClientTypeArray, $myFileInfo, $internalLoanProgramList,
       $propDetailsProcess, $PCStatusInfo, $PCSubStatusInfo,
       $fileSubstatusInfo, $substatusArray, $isHMLOSelOpt, $isHMLO, $allowToViewCreditScreening, $pcAcqualifyStatus, $acqualifyClientCreditInfo,
       $acqualifyClientId, $showStartLoanNumber, $receivedDate, $borrowerCallBack, $welcomeCallDate, $purchaseCloseDate, $hearingDate, $isHOALien,
       $propMgmntContactID, $tempPropMgmntContactName, $propMgmntContactPerson, $tabIndexNo, $propMgmntCompany, $propMgmntContactEmail, $propMgmntAddress,
       $propMgmntCity, $stateArray, $propMgmntState, $propMgmntZip, $propMgmntZip, $propMgmntPhNo1, $propMgmntPhone, $propMgmntNotes,
       $clientSelectReferralCode, $HMLOPCBasicSBALoanProductInfoArray, $agentFileAccess, $BrokerInfo;
global $allowUserToUpdateCFPBFile;
global $fileMC;

use models\composite\oAffiliate\getBranchAffiliateSubmissionCode;
use models\composite\oBroker\getBrokerStatusInfo;
use models\composite\oFile\getCoBorrowerNumberOfDeals;
use models\composite\oFile\getFileInfo\PCSubStatusInfo;
use models\constants\borrowerStatusArray;
use models\constants\brokerStatusArray;
use models\constants\gl\glApprovedReferralFee;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\globalSBALoanProductsCat;
use models\constants\gl\glPCID;
use models\constants\gl\glPropDetailsProcess;
use models\constants\gl\glSendPSChangeNotificationToPC;
use models\constants\gl\glUserGroup;
use models\constants\showFieldsArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\Database2;
use models\CustomField;
use models\cypher;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\Controllers\LMRequest\fileAdminInfo;
use models\myFileInfo;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\lendingwise\tblContacts;

LMRequest::setLMRId($LMRId);
$agentList = LMRequest::$agentList;

$globalSBALoanProductsCat = globalSBALoanProductsCat::$globalSBALoanProductsCat;
$glPropDetailsProcess = glPropDetailsProcess::$glPropDetailsProcess;
if ($PCID == glPCID::PCID_PROD_CV3) {
    $brokerStatusArray = brokerStatusArray::$brokerStatusArrayCV3;
} else {
    $brokerStatusArray = brokerStatusArray::$brokerStatusArray;
}
$borrowerStatusArray = borrowerStatusArray::$borrowerStatusArray;
$glSendPSChangeNotificationToPC = glSendPSChangeNotificationToPC::$glSendPSChangeNotificationToPC;

$activeStatusInfo = [];
$brokerStatusInfo = [];
$brokerStatus = $createdDate = '';
$brokerActiveStatus = '';
$brokerStatusVal = 'not selected';
if (Strings::showField('userNumber', 'BrokerInfo') > 0) {
    $brokerStatusInfo = getBrokerStatusInfo::getReport(['brokerNumber' => Strings::showField('userNumber', 'BrokerInfo'), 'PCID' => $PCID]);
    if (array_key_exists('activeInfo', $brokerStatusInfo)) $activeStatusInfo = $brokerStatusInfo['activeInfo'];
    $brokerActiveStatus = $brokerStatusInfo['statusInfo'][0]['activeStatus'] ?? 0;
    if (count($activeStatusInfo)) {
        $brokerStatus = $activeStatusInfo[0]['brokerStatus'];
    }
    if ($brokerStatus > 0) $brokerStatusVal = $brokerStatusArray[$brokerStatus];
}

$sBrokerStatusInfo = [];
$sBrokerStatus = '';
$sBrokerStatusVal = 'not selected';
if (Strings::showField('userNumber', 'SecondaryBrokerInfo') > 0) {
    $sBrokerStatusInfo = getBrokerStatusInfo::getReport(['brokerNumber' => Strings::showField('userNumber', 'SecondaryBrokerInfo'), 'PCID' => $PCID]);
    if (array_key_exists('activeInfo', $sBrokerStatusInfo)) $activeStatusInfo = $sBrokerStatusInfo['activeInfo'];
    if (count($activeStatusInfo)) $sBrokerStatus = $activeStatusInfo[0]['brokerStatus'];
    if ($sBrokerStatus > 0) $sBrokerStatusVal = $brokerStatusArray[$sBrokerStatus];
}

$brokerAddr = '';
$brokerDealPoints = '';
$brokerDealFee = '';
$brokerQuotedinterestRate = '';
$accessWholesalePricing = '';
if (count($fileHMLONewLoanInfo) > 0) {
    $brokerDealPoints = $fileHMLONewLoanInfo['brokerDealPoints'];
    $brokerDealFee = $fileHMLONewLoanInfo['brokerDealFee'];
    $brokerQuotedinterestRate = $fileHMLONewLoanInfo['brokerQuotedinterestRate'];
    $accessWholesalePricing = $fileHMLONewLoanInfo['accessWholesalePricing'];
}
$createdDate = Strings::showField('recordDate', 'LMRInfo');
if (Dates::IsEmpty($createdDate)) {
    $createdDate = '';
} else {
    $createdDate = Dates::formatDateWithRE($createdDate, 'YMD', 'm/d/Y');
}

/* Broker Info */
$brokerName = ucwords(Strings::showField('firstName', 'BrokerInfo') . ' ' . Strings::showField('lastName', 'BrokerInfo'));
$brokerCompany = ucwords(Strings::showField('company', 'BrokerInfo'));
$brokerEmail = Strings::showField('email', 'BrokerInfo');
$brokerAddress = ucwords(Strings::showField('addr', 'BrokerInfo'));
$brokerCity = ucfirst(Strings::showField('city', 'BrokerInfo'));
$brokerState = Strings::showField('state', 'BrokerInfo');
$brokerZip = Strings::showField('zip', 'BrokerInfo');
$brokerPhone = Strings::formatPhoneNumber(Strings::showField('phoneNumber', 'BrokerInfo'));
$brokerCell = Strings::formatPhoneNumber(Strings::showField('cellNumber', 'BrokerInfo'));
$brokerFax = Strings::formatPhoneNumber(Strings::showField('fax', 'BrokerInfo'));
$brokerLicense = Strings::showField('license', 'BrokerInfo');
$brokerNMLSLicense = Strings::showField('NMLSLicense', 'BrokerInfo');
$brokerStatus = Strings::showField('brokerStatus', 'BrokerInfo');
$brokerPartnerType = Strings::showField('brokerPartnerType', 'BrokerInfo');
$approvedReferralFee = Strings::showField('approvedReferralFee', 'BrokerInfo');
$brokerStatusText = ($brokerStatus) ? brokerStatusArray::$brokerStatusArray[$brokerStatus] : 'n/a';
/**  Broker Authorized States  */
$brokerStatesAuthorizedView = 0;
$brokerStatesAuthorizedDisplay = '';
if (!glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
    $brokerStatesAuthorized = $BrokerInfo['statesAuthorizedToOriginate'];
    if ($brokerStatesAuthorized) {
        $brokerStatesAuthorizedView = 1;
        $brokerAuthorizedStates = explode(',', $brokerStatesAuthorized);
        $brokerStatesAuthorizedCount = count($brokerAuthorizedStates);
        $brokerStatesAuthorizedNames = [];
        foreach ($brokerAuthorizedStates as $stateKey) {
            $brokerStatesAuthorizedNames[] = Strings::convertState($stateKey);
        }
        if ($brokerStatesAuthorizedCount) {
            $brokerStatesAuthorizedDisplay = '<span
        class="cursor-pointer text-primary"
        data-toggle="tooltip"
        data-html="true"
        data-placement="top"
        title="' . implode(', ', $brokerStatesAuthorizedNames) . '">' . $brokerStatesAuthorizedCount . ' State(s)</span>';
        }
    }
}
/** // Broker Authorized States // */
$glBrokerPartnerTypeArray = glCustomJobForProcessingCompany::getBrokerPartnerType($PCID, $publicUser);
$appStr = '';
if (trim($brokerAddress)) {
    $brokerAddr = $brokerAddress;
    $appStr = ', ';
}
if (trim($brokerCity)) {
    $brokerAddr .= $appStr . $brokerCity;
    $appStr = ', ';
}
if (trim($brokerState)) {
    $brokerAddr .= $appStr . $brokerState;
    $appStr = ', ';
}
if (trim($brokerZip)) {
    $brokerAddr .= $appStr . $brokerZip;
    $appStr = '';
}

if (isset($_REQUEST['allowToEdit'])) {
    $allowToEdit = false;
}

if (PageVariables::$userGroup == 'Employee') {
    if (PageVariables::$allowEmpToCreateAgent == 1) {
        $brokerName = '<a target="_blank" href="createAgent.php?bId=' . cypher::myEncryption($brokerNumber) . '">' . $brokerName . '</a>';
    }
}
if (PageVariables::$userGroup == 'Branch') {
    if (PageVariables::$allowBranchToEditAgentProfile == 1) {
        $brokerName = '<a target="_blank" href="createAgent.php?bId=' . cypher::myEncryption($brokerNumber) . '">' . $brokerName . '</a>';
    }
}
$brokerContactInfo = '<table class="table  table-borderless "><tr><td><b>Name: </b></td><td> ' . $brokerName . '</td></tr> ';

if (trim($brokerCompany)) {
    $brokerContactInfo .= ' <tr><td><b> Company: </b></td><td> ' . $brokerCompany . '</td></tr> ';
}
if (trim($brokerEmail)) {
    $brokerContactInfo .= ' <tr><td><b> Email: </b></td><td><a href = "mailto:' . $brokerEmail . '" > ' . $brokerEmail . '</a ></td></tr > ';
}
/*if (trim($brokerAddr)) {
    //$brokerContactInfo .= ' <tr><td><b> Addr: </b></td><td> ' . $brokerAddr . '</td></tr> ';
}*/
if (trim($brokerPhone)) {
    $brokerContactInfo .= ' <tr><td><b> Phone: </b></td><td> ' . $brokerPhone . '</td></tr> ';
}
if (trim($brokerCell)) {
    $brokerContactInfo .= ' <tr><td><b> Cell: </b></td><td> ' . $brokerCell . '</td></tr> ';
}
if (trim($brokerFax)) {
    $brokerContactInfo .= ' <tr><td><b> Fax: </b></td><td> ' . $brokerFax . '</td></tr> ';
}
if (trim($brokerLicense)) {
    $brokerContactInfo .= ' <tr><td><b> License#: </b></td><td> ' . $brokerLicense . '</td></tr> ';
}
if (trim($brokerNMLSLicense)) {
    $brokerContactInfo .= ' <tr><td><b> NMLS License#: </b></td><td> ' . $brokerNMLSLicense . '</td></tr> ';
}

if (trim($brokerDealPoints)) {
    $brokerContactInfo .= ' <tr><td><b> How many points are you charging on this deal ?: </b></td><td> ' . $brokerDealPoints . '</td></tr> ';
}
if (trim($brokerDealFee)) {
    $brokerContactInfo .= ' <tr><td><b> How much of a processing fee are you charging on this deal ?: </b></td><td> ' . Currency::formatDollarAmountWithDecimal($brokerDealFee) . '</td></tr> ';
}
if (trim($brokerQuotedinterestRate)) {
    $brokerContactInfo .= ' <tr><td><b> Quoted interest rate to the borrower on this deal ?: </b></td><td> ' . $brokerQuotedinterestRate . '</td></tr> ';
}
if (trim($accessWholesalePricing)) {
    $brokerContactInfo .= ' <tr><td><b>if you are not a Correspondent Partner, are you interested in having access to Triumph’s capital at wholesale pricing ?: </b></td><td> ' . $accessWholesalePricing . '</td></tr> ';
}
$brokerContactInfo .= '<tr><td><b> Broker/Partner Type :</b></td><td>' . $glBrokerPartnerTypeArray[$brokerPartnerType] . '</td></tr>';
$brokerContactInfo .= '<tr><td><b> Approved Referral Fee :</b></td><td>' . glApprovedReferralFee::$glApprovedReferralFee[$approvedReferralFee] . '</td></tr>';
if (PageVariables::$userGroup == 'Employee' || PageVariables::$userRole == 'Branch' || PageVariables::$userRole == 'Super' || (PageVariables::$userRole == 'Agent' && $externalBroker > 0)) {
    $brokerContactInfo .= ' <tr><td><b> Status: </b></td> ';
    if (PageVariables::$userGroup == 'Employee') {
        $brokerContactInfo .= ' <td class="with-children-tip">
 <a href="#" 
        id = "updateBrokerStatus" 
        data-type = "select" 
        data-pk="' . cypher::myEncryption(Strings::showField('userNumber', 'BrokerInfo') . '-' . $PCID) . '"
        data-value="' . $brokerStatus . '"
        data-title = "Broker Status" 
        class="editable editable-click editable-open tip-bottom"
           data-original-title = "" title = "Click to change broker status."> ' . $brokerStatusVal . '</a></td> ';
    } else {
        $brokerContactInfo .= '<td> ' . $brokerStatusVal . '</td> ';
    }
    $brokerContactInfo .= '</tr> ';
}
if ($brokerStatesAuthorizedView) {
    $brokerContactInfo .= '<tr><td><b>States Authorized: </b></td>';
    $brokerContactInfo .= '<td>' . $brokerStatesAuthorizedDisplay . '</td>';
    $brokerContactInfo .= '</tr>';
}
$brokerContactInfo .= '</table> ';
/* End of Broker */
$borrowerName = ucwords(Strings::showField('borrowerName', 'LMRInfo') . ' ' . Strings::showField('borrowerLName', 'LMRInfo'));
$borrowerHyperLink = ucwords(Strings::showField('borrowerName', 'LMRInfo') . ' ' . Strings::showField('borrowerLName', 'LMRInfo'));
$borrowerEmail = Strings::showField('borrowerEmail', 'LMRInfo');
$internalInfoCreditLine = Strings::showField('internalInfoCreditLine', 'clientInfo');
$internalInfoStatus = Strings::showField('internalInfoStatus', 'clientInfo');

if (PageVariables::$userGroup == 'Employee') {
    if (PageVariables::$allowToViewAllFiles == 1) {
        $borrowerHyperLink = ' <a  target = "_blank" href = "' . $userUrl . 'clientCreate.php?cId=' . cypher::myEncryption($clientId) . '&encEId=' . cypher::myEncryption($executiveId) . '"> ' . $borrowerName . '</a> ';
    }
}
$borrowerContactInfo = '<table class="table  table-borderless ">
<tr><td><b><u>Borrower: </u></b></td></tr>
<tr><td><b> Name: </b></td><td> ' . $borrowerHyperLink . '</td></tr> ';

if (trim($borrowerEmail)) {
    $borrowerContactInfo .= ' <tr><td><b> Email: </b></td><td><a href = "mailto:' . $borrowerEmail . '"> ' . $borrowerEmail . '</a></td></tr> ';
}
if (Arrays::getArrayValue('numberOfDeals', $borrowerNumberOfDeals) > 0) {
    $borrowerContactInfo .= ' <tr><td><b># of Deals: </b></td><td><a href="myPipeline.php?searchTerm=' . $borrowerEmail . '&amp;searchField=tl.borrowerEmail&amp;activeFile=1" target="_blank">' . Arrays::getArrayValue('numberOfDeals', $borrowerNumberOfDeals) . '</a></td></tr>';
}
if (trim($internalInfoCreditLine)) {
    $borrowerContactInfo .= '<tr><td><b>Credit Line: </b></td>';
    if (PageVariables::$userRole == 'Manager') {
        $borrowerContactInfo .= '<td class="with-children-tip"><a href="#" id="internalInfoCreditLine" data-type="text" data-pk="' . cypher::myEncryption(Strings::showField('CID', 'clientInfo') . '-' . $PCID) . '" data-value="' . $internalInfoCreditLine . '" data-title="" class="editable editable-click editable-open tip-bottom" data-original-title="Credit Line" title="Click to change Credit Line.">' . $internalInfoCreditLine . '</a></td>';

    } else {
        $borrowerContactInfo .= '<td>' . $internalInfoCreditLine . '</td>';
    }
    $borrowerContactInfo .= '</tr>';
}
if (trim($internalInfoStatus)) {
    $borrowerContactInfo .= '<tr><td><b>Borrower Status: </b></td>';
    if (PageVariables::$userGroup == 'Employee') {
        $borrowerContactInfo .= '<td class="with-children-tip"><a href="#" id="internalInfoStatus" data-type="select" data-pk="' . cypher::myEncryption(Strings::showField('CID', 'clientInfo') . '-' . $PCID) . '" data-value="' . $internalInfoStatus . '" data-title="Borrower Status" class="editable editable-click editable-open tip-bottom" data-original-title="" title="Click to change Borrower status.">' . $internalInfoStatus . '</a></td>';
    } else {
        $borrowerContactInfo .= '<td>' . $internalInfoStatus . '</td>';
    }
    $borrowerContactInfo .= '</tr>';
}
$borrowerContactInfo .= '</table>';

//Co-Borrower Details
$coBorrowerInfo = '';
if (LMRequest::File()->isCoBorrower) {
    //Co-Borrower Number of Deals
    $coBorrowerNumberOfDeals = getCoBorrowerNumberOfDeals::getReport(LMRequest::File()->coBorrowerEmail, (int)$PCID);
    $coBorrowerInfo .= '<table class="table table-borderless">
<tr><td><b><u>Co-Borrower: </u></b></td></tr>';
    $coBorrowerInfo .= '<tr><td><b> Name: </b></td><td> ' . LMRequest::File()->coBorrowerFName . ' ' . LMRequest::File()->coBorrowerLName . '</td></tr> ';
    $coBorrowerInfo .= ' <tr><td><b> Email: </b></td><td>' . LMRequest::File()->coBorrowerEmail . '</td></tr> ';
    if ($coBorrowerNumberOfDeals) {
        $coBorrowerInfo .= ' <tr><td><b># of Deals: </b></td><td><a href="myPipeline.php?searchTerm=' . LMRequest::File()->coBorrowerEmail . '&amp;searchField=tl.coBorrowerEmail&amp;activeFile=1" target="_blank">' . $coBorrowerNumberOfDeals . '</a></td></tr>';
    }
    $coBorrowerInfo .= '</table>';
}

/* Loan officer  end */
$sBrokerName = ucwords(Strings::showField('firstName', 'SecondaryBrokerInfo') . ' ' . Strings::showField('lastName', 'SecondaryBrokerInfo'));
$sBrokerCompany = ucwords(Strings::showField('company', 'SecondaryBrokerInfo'));
$sBrokerEmail = Strings::showField('email', 'SecondaryBrokerInfo');
$sBrokerAddress = ucwords(Strings::showField('addr', 'SecondaryBrokerInfo'));
$sBrokerCity = ucfirst(Strings::showField('city', 'SecondaryBrokerInfo'));
$sBrokerState = Strings::showField('state', 'SecondaryBrokerInfo');
$sBrokerZip = Strings::showField('zip', 'SecondaryBrokerInfo');
$sBrokerPhone = Strings::formatPhoneNumber(Strings::showField('phoneNumber', 'SecondaryBrokerInfo'));
$sBrokerCell = Strings::formatPhoneNumber(Strings::showField('cellNumber', 'SecondaryBrokerInfo'));
$sBrokerFax = Strings::formatPhoneNumber(Strings::showField('fax', 'SecondaryBrokerInfo'));
$sBrokerLicense = Strings::showField('license', 'SecondaryBrokerInfo');
$sBrokerNMLSLicense = Strings::showField('NMLSLicense', 'SecondaryBrokerInfo');
$appStr = '';
if (trim($sBrokerAddress)) {
    $sBrokerAddr = $sBrokerAddress;
    $appStr = ', ';
}
if (trim($sBrokerCity)) {
    $sBrokerAddr .= $appStr . $sBrokerCity;
    $appStr = ', ';
}
if (trim($sBrokerState)) {
    $sBrokerAddr .= $appStr . $sBrokerState;
    $appStr = ', ';
}
if (trim($sBrokerZip)) {
    $sBrokerAddr .= $appStr . $sBrokerZip;
    $appStr = '';
}
if (PageVariables::$userGroup == 'Employee') {
    if (PageVariables::$allowEmpToCreateAgent == 1) {
        $sBrokerName = '<a target="_blank" href="createAgent.php?bId=' . cypher::myEncryption(LMRequest::File()->secondaryBrokerNumber) . '">' . $sBrokerName . '</a>';
    }
}
if (PageVariables::$userGroup == 'Branch') {
    if (PageVariables::$allowBranchToEditAgentProfile == 1) {
        $sBrokerName = '<a target="_blank" href="createAgent.php?bId=' . cypher::myEncryption(LMRequest::File()->secondaryBrokerNumber) . '">' . $sBrokerName . '</a>';
    }
}
$sBrokerContactInfo = '<table class="table  table-borderless "><tr><td><b>Name: </b></td><td>' . $sBrokerName . '</td></tr>';
if (trim($sBrokerCompany)) {
    $sBrokerContactInfo .= '<tr><td><b>Company: </b></td><td>' . $sBrokerCompany . '</td></tr>';
}
if (trim($sBrokerEmail)) {
    $sBrokerContactInfo .= '<tr><td><b>Email: </b></td><td><a href="mailto:' . $sBrokerEmail . '">' . $sBrokerEmail . '</a></td></tr>';
}
/*if (trim($sBrokerAddr)) {
    // $sBrokerContactInfo .= '<tr><td><b>Addr: </b></td><td>' . $sBrokerAddr . '</td></tr>';
}*/
if (trim($sBrokerPhone)) {
    $sBrokerContactInfo .= '<tr><td><b>Phone: </b></td><td>' . $sBrokerPhone . '</td></tr>';
}
if (trim($sBrokerCell)) {
    $sBrokerContactInfo .= '<tr><td><b>Cell: </b></td><td>' . $sBrokerCell . '</td></tr>';
}
if (trim($sBrokerFax)) {
    $sBrokerContactInfo .= '<tr><td><b>Fax: </b></td><td>' . $sBrokerFax . '</td></tr>';
}
if (trim($sBrokerLicense)) {
    $sBrokerContactInfo .= ' <tr><td><b> License#: </b></td><td> ' . $sBrokerLicense . '</td></tr> ';
}
if (trim($sBrokerNMLSLicense)) {
    $sBrokerContactInfo .= ' <tr><td><b> NMLS License#: </b></td><td> ' . $sBrokerNMLSLicense . '</td></tr> ';
}


if (PageVariables::$userGroup == 'Employee' || PageVariables::$userRole == 'Branch' || PageVariables::$userRole == 'Super') {
    $sBrokerContactInfo .= '<tr><td><b>Status: </b></td>';
    if (PageVariables::$userGroup == 'Employee') {
        $sBrokerContactInfo .= '<td class="with-children-tip"><a href="#" id="updateBrokerStatus" data-type="select" data-pk="' . cypher::myEncryption(Strings::showField('userNumber', 'SecondaryBrokerInfo') . '-' . $PCID) . '" data-value="' . $sBrokerStatus . '" data-title="Broker Status" class="editable editable-click editable-open tip-bottom" data-original-title="" title="Click to change sBroker status.">' . $sBrokerStatusVal . '</a></td>';
    } else {
        $sBrokerContactInfo .= '<td>' . $sBrokerStatusVal . '</td>';
    }
    $sBrokerContactInfo .= '</tr>';
}
$sBrokerContactInfo .= '</table>';
/* Loan officer  end */
$CICustomShowField = 1;

$myFileInfoObject = new myFileInfo();
$myFileInfoObject->LMRId = $LMRId;

fileAdminInfo::$fileAdminInfo = $myFileInfoObject->getFileAdminInfo();
?>
<!-- Select Control Systems. -->
<div class="row mb-2">
    <div class="col-lg-12">
        <div class="card card-custom adminSectionCard " id="adminSectionId">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label"><?php echo BaseHTML::getSectionHeading('Admin'); ?></h3>
                    <?php if (trim(BaseHTML::getSectionTooltip('Admin'))) { ?>&nbsp;
                        <i class="popoverClass fas fa-info-circle text-primary"
                           data-html="true"
                           data-content="<?php echo BaseHTML::getSectionTooltip('Admin'); ?>"></i>
                    <?php } ?>
                </div>
                <div class="card-toolbar">
                    <span
                            style="cursor: pointer"
                            class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="toggle"
                            data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
                    <span
                            style="cursor: pointer"
                            class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none" data-card-tool="reload"
                            data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                        <i class="ki ki-reload icon-nm"></i>
                    </span>
                    <span
                            style="cursor: pointer"
                            class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                            data-card-tool="toggle"
                            data-section="adminSectionCard"
                            data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </span>
                </div>
            </div>
            <div class="card-body adminSectionCard_body">
                <div class="row">
                    <div class="col-md-8">
                        <!-- Select Pc Start-->
                        <?php if (PageVariables::$userRole == 'Super') { ?>
                            <div class="form-group row">
                                <label class="col-md-4 font-weight-bold" for="selectedPC">Select the PC:</label>
                                <div class="col-md-8">
                                    <select class="form-control input-sm mandatory" name="selectedPC" id="selectedPC"
                                            onchange="getBranchesAndAgents(this.value);">
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($pc = 0; $pc < count($PCListArray); $pc++) {
                                            $tempPCID = 0;
                                            $processingCompany = '';
                                            $processingSta = 1;
                                            $tempPCID = trim($PCListArray[$pc]['PCID']);
                                            $processingCompany = trim($PCListArray[$pc]['processingCompanyName']);
                                            $processingSta = trim($PCListArray[$pc]['activeStatus']);
                                            ?>
                                            <option
                                                    value="<?php echo $tempPCID ?>" <?php echo Arrays::isSelected($tempPCID, $PCID); ?> <?php if ($processingSta == 0) { ?> class="red" <?php } ?>><?php echo $processingCompany ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        <?php } else {
                            if (PageVariables::$userRole == 'CFPB Auditor' || PageVariables::$userRole == 'Auditor Manager'
                                || (PageVariables::$allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1 && Strings::showField('FPCID', 'LMRInfo') != $PCID)
                            ) { /* CFPB auditor Maintain their Own PC notes */
                                ?>
                                <input type="hidden" name="CFPBFileUser" id="CFPBFileUser" value="1">
                                <input type="hidden" name="selectedPC" id="selectedPC"
                                       value="<?php echo Strings::showField('FPCID', 'LMRInfo'); ?>">
                                <?php
                            } else {
                                ?>
                                <input type="hidden" name="selectedPC" id="selectedPC" value="<?php echo $PCID; ?>">
                                <?php
                            }
                        }
                        ?>
                        <!-- Select Pc End-->
                        <?php
                        if (($publicUser == 1 || $DIYUser == 1) || (PageVariables::$userGroup == 'Client' && $allowClientToCreateHMLOFile == 0) || (PageVariables::$userGroup == 'Client' && $allowClientToCreateHMLOFile == 1 && $LMRId > 0)) {
                            ?>
                            <input type="hidden" name="branchId" id="branchId"
                                   value="<?php echo cypher::myEncryption($executiveId); ?>">
                            <input type="hidden" name="agentId" id="agentId"
                                   value="<?php echo cypher::myEncryption($agentNumber); ?>">
                        <?php } else { ?>
                            <!-- Select Branch Start-->
                            <div class="form-group row">
                                <label class="col-md-4 font-weight-bold"
                                       for="branchId"> <?php if (PageVariables::$userRole == 'Branch') {
                                    } else if ($allowToEdit) { ?>Select the <?php } ?>Branch:</label>
                                <div class="col-md-8">
                                    <?php
                                    if (PageVariables::$userRole == 'Branch' || PageVariables::$userRole == 'CFPB Auditor' || PageVariables::$userRole == 'Auditor Manager' || (PageVariables::$allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)) {
                                        if ((PageVariables::$allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1) || PageVariables::$userRole == 'CFPB Auditor' || PageVariables::$userRole == 'Auditor Manager') {
                                            echo '<h3>' . Strings::showField('LMRExecutive', 'BranchInfo') . '</h3>';
                                        } else {
                                            echo '<h3>' . $userName . '</h3>';
                                        }
                                        if (PageVariables::$userRole == 'Branch') {
                                            ?>
                                            <input type="hidden" name="branchId" id="branchId"
                                                   value="<?php echo cypher::myEncryption(PageVariables::$userNumber); ?>">
                                            <?php
                                        } else {
                                            ?>
                                            <input type="hidden" name="branchId" id="branchId"
                                                   value="<?php echo cypher::myEncryption($executiveId); ?>">
                                            <?php
                                        }
                                    } else if ($LMRId == 0
                                        || ($allowToEdit && ($allowToChangeOrAssignBranchForFile == 1 || ($PCID == 1492 && PageVariables::$userRole != 'Manager' && $LMRId == 0)
                                                || ($PCID == 1492 && PageVariables::$userRole == 'Manager'))
                                            && PageVariables::$userRole != 'CFPB Auditor' && PageVariables::$userRole != 'Auditor Manager')
                                    ) {
                                        /* The J. Freeman Firm PCID - 1492 If permission = No Non manager assign the branch to file during creation after follow the permission, manager allow to assign the branch for both create and edit Nov - 19 - 2015 */

                                        if (PageVariables::$userRole == 'Client' && $LMRId == 0) {
                                            if ($clientSelectReferralCode > 0) {
                                                $clientAssignedBranch = 0;
                                                $branchData = getBranchAffiliateSubmissionCode::getReport(['affiliateCode' => $clientSelectReferralCode]);
                                                $executiveId = $branchData['executiveId'];
                                            }
                                        }
                                        ?>
                                        <div id="branchId_container">
                                            <select class="form-control input-sm mandatory" name="branchId"
                                                    id="branchId" <?php if (!$PCID) {
                                                echo 'disabled';
                                            } ?>
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    onchange="getModules('loanModForm', '<?php echo $PCID ?>','<?php echo $moduleCode; ?>');
                                                            getServiceTypes('loanModForm');<?php
                                                    if (PageVariables::$userRole == 'Agent' && $externalBroker == 1 && LMRequest::File()->secondaryBrokerNumber > 0 && $PCID == 3138) {
                                                        echo 'getBranchAgentsLOVersionHouseMax(this.value);';
                                                    } else {
                                                        echo 'getBranchAgents(this.value);';
                                                    } if(!$publicUser){
                                                        echo 'LMRequest.getTermsAndConditions(this.value);';
                                                    }
                                                    ?> LMRequest.lastUpdatedParams('Branch'); " <?php //} ?>>
                                                <option value=''> - Select -</option>
                                                <?php
                                                if (count($branchList) == 1 && !$LMRId && !$publicUser) {
                                                    $executiveId = trim($branchList[0]['executiveId']);
                                                }
                                                for ($j = 0; $j < count($branchList); $j++) {
                                                    $dispBranchName = '';
                                                    $sOpt = '';
                                                    $sOpt = Arrays::isSelected(trim($branchList[$j]['executiveId']), $executiveId);
                                                    $dispBranchName = trim($branchList[$j]['LMRExecutive']);
                                                    if (PageVariables::$userRole == 'Super') $dispBranchName .= ' - ' . trim($branchList[$j]['company']);
                                                    echo "<option value=\"" . trim(cypher::myEncryption(trim($branchList[$j]['executiveId']))) . "\" " . $sOpt . '>' . ucwords($dispBranchName) . '</option>';
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    <?php } else { ?>
                                        <input type="hidden" name="branchId" id="branchId"
                                               value="<?php echo cypher::myEncryption($executiveId); ?>">
                                        <h5><?php echo Strings::showField('LMRExecutive', 'BranchInfo'); ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        <?php } ?>
                        <!-- Select Branch End -->

                        <!-- Select Agent Start -->
                        <div class="form-group row">
                            <?php if (PageVariables::$userRole == 'Client' && $LMRId > 0) { ?>
                                <input type="hidden" name="agentId" id="agentId"
                                       value="<?php echo cypher::myEncryption($brokerNumber); ?>">
                            <?php } else { ?>
                                <label class="col-md-4 font-weight-bold"
                                       for="agentId"><?php if ($allowToEdit) { ?>Select Broker/Partner<?php } else { ?>Broker/Partner<?php } ?>

                                    <!-- Loan officer can create Broker -->
                                    <?php if (PageVariables::$userRole == 'Agent' && $externalBroker == 1) { ?>
                                        <a href="javascript:void(0);"
                                           data-href="<?php echo CONST_URL_POPS; ?>addNewBroker.php"
                                           data-toggle='modal' data-target='#exampleModal1' data-wsize='modal-xxl'
                                           data-name="Add/View Broker" title="Add/View Broker"
                                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                                           data-id='loanOfficerId=<?php echo cypher::myEncryption($userNumber); ?>&PCID=<?php echo cypher::myEncryption($PCID) ?>&externalBroker=0&userRole=<?php echo cypher::myEncryption(PageVariables::$userRole); ?>&LMRId=<?php echo cypher::myEncryption($LMRId); ?>&userGroup=<?php echo cypher::myEncryption(PageVariables::$userGroup); ?>'>
                                            <i class="icon-blue fa fa-user valign-middle  tip-bottom"
                                               title="Add/View Broker"></i>
                                        </a>
                                    <?php } ?>
                                    <!-- end of Loan officer can create broker -->
                                </label>


                                <div class="col-md-8">
                                    <input type="hidden" id="dummyBrokerId" name="dummyBrokerId"
                                           value="<?php echo $dummyBrokerId; ?>">
                                    <?php if ((PageVariables::$userRole == 'Agent' && $externalBroker == 0) || (PageVariables::$userRole == 'Agent' && $externalBroker == 1 && $LMRId > 0 && $agentFileAccess == 2 && count($BrokerInfo) > 0 && !strpos($BrokerInfo['email'], '@dummyAgentemail.com'))
                                        || PageVariables::$userRole == 'CFPB Auditor' || PageVariables::$userRole == 'CFPB Auditor' || (PageVariables::$allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)) {
                                        if ((PageVariables::$allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1) || PageVariables::$userRole == 'CFPB Auditor'
                                            || PageVariables::$userRole == 'CFPB Auditor' || (count($BrokerInfo) > 0 && $LMRId > 0)) {
                                            echo '<h3>' . trim(Strings::showField('firstName', 'BrokerInfo')) . ' ' . trim(Strings::showField('lastName', 'BrokerInfo')) . '</h3>';
                                        } else {
                                            echo '<h3>' . $userName . '</h3>';
                                        }
                                        ?>
                                        <input type="hidden" name="agentId" id="agentId"
                                               value="<?php if ($brokerNumber > 0) {
                                                   echo cypher::myEncryption($brokerNumber);
                                               } else {
                                                   echo cypher::myEncryption($userNumber);
                                               } ?>">
                                        <?php
                                    } else {
                                        /**
                                         *
                                         * Description    : Allow to change/assign branch or broker/agent for a file in employee Profile
                                         * Date        : March 15, 2017
                                         * Author        : Venkatesh
                                         **/
                                        if ((($allowToEdit && $allowToChangeOrAssignBranchForFile == 1) || $LMRId == 0)
                                            && PageVariables::$userRole != 'CFPB Auditor'
                                        ) {
                                            ?>
                                            <div id="agentId_container">
                                                <select class="form-control input-sm "
                                                        name="agentId"
                                                        id="agentId"
                                                        data-original-value="<?php echo cypher::myEncryption($brokerNumber); ?>"
                                                        tabindex="<?php echo $tabIndex++; ?>" <?php if (!$PCID) {
                                                    echo 'disabled';
                                                } ?> onchange="LMRequest.lastUpdatedParams('Broker');">
                                                    <option value=''> - Select -</option>
                                                    <?php if ($LMRId > 0 || PageVariables::$userGroup == 'Branch') {
                                                        foreach ($agentList as $j => $item) {
                                                            if ($item->externalBroker == 0) {
                                                                $brokerName = trim($item->bName) . ' ' . trim($item->bLName);
                                                                $sOpt = Arrays::isSelected(trim($item->brokerNumber), $brokerNumber);
                                                                $dispAgentName = $brokerName . ' - ' . trim($item->bEmail);
                                                                $hideDummyAgent = '';
                                                                if ($brokerNumber == $dummyBrokerId) {
                                                                    $sOpt = '';
                                                                }

                                                                if ($dummyBrokerId == $item->brokerNumber) {
                                                                    $hideDummyAgent = 'display:none;';
                                                                }
                                                                echo "<option style='$hideDummyAgent' value=\"" . trim(cypher::myEncryption(trim($item->brokerNumber))) . "\" " . $sOpt . '>' . ucwords($dispAgentName) . '</option>';

                                                            }
                                                        }
                                                    }
                                                    ?>
                                                </select>


                                            </div>
                                        <?php } else { ?>
                                            <input type="hidden" name="agentId" id="agentId"
                                                   value="<?php echo cypher::myEncryption($brokerNumber); ?>">
                                            <h5><?php echo $brokerName; ?></h5>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>

                            <?php } ?>
                        </div>

                        <!-- Select Secondary Agent Start -->
                        <div class="form-group row">
                            <?php if ((PageVariables::$userRole == 'Client' && $LMRId > 0)) { ?>
                                <input type="hidden" name="secondaryAgentId" id="secondaryAgentId"
                                       value="<?php echo cypher::myEncryption(LMRequest::File()->secondaryBrokerNumber); ?>">
                            <?php } else { ?>
                                <label class="col-md-4 font-weight-bold"
                                       for="secondaryAgentId"><?php if ($allowToEdit) { ?>Select the Loan Officer<?php } else { ?>Loan Officer<?php } ?></label>
                                <div class="col-md-8">
                                    <?php
                                    if ((PageVariables::$userRole == 'Agent' && $externalBroker == 1 && LMRequest::File()->secondaryBrokerNumber > 0)
                                        || PageVariables::$userRole == 'CFPB Auditor'
                                        || (PageVariables::$allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)
                                        || (PageVariables::$userRole == 'Agent' && $externalBroker == 0 && LMRequest::File()->secondaryBrokerNumber > 0)
                                    ) {
                                        if ((PageVariables::$allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)
                                            || PageVariables::$userRole == 'CFPB Auditor'
                                            || LMRequest::File()->secondaryBrokerNumber > 0
                                        ) {
                                            echo '<h3>' . trim(Strings::showField('firstName', 'SecondaryBrokerInfo')) . ' ' . trim(Strings::showField('lastName', 'SecondaryBrokerInfo')) . '</h3>';

                                        } else {
                                            echo '<h3>' . $userName . '</h3>';
                                        }
                                        ?>
                                        <input type="hidden" name="secondaryAgentId" id="secondaryAgentId"
                                               value="<?php if (LMRequest::File()->secondaryBrokerNumber > 0) {
                                                   echo cypher::myEncryption(LMRequest::File()->secondaryBrokerNumber);
                                               } else {
                                                   echo cypher::myEncryption($userNumber);
                                               } ?>">
                                        <?php
                                    } else {
                                        if ((($allowToEdit && $allowToChangeOrAssignBranchForFile == 1) || $LMRId == 0)
                                            && PageVariables::$userRole != 'CFPB Auditor'
                                        ) {
                                            if (PageVariables::$userRole == 'Agent' && $externalBroker == 1) {
                                                echo '<h3>' . $userName . '</h3>'; ?>
                                                <input type="hidden" name="secondaryAgentId" id="secondaryAgentId"
                                                       readonly
                                                       value="<?php echo cypher::myEncryption($userNumber); ?>">
                                            <?php } else { ?>
                                                <div id="secondaryAgentId_container">
                                                    <select class="form-control input-sm "
                                                            name="secondaryAgentId"
                                                            id="secondaryAgentId"
                                                            data-original-value=<?php echo cypher::myEncryption(LMRequest::File()->secondaryBrokerNumber); ?>
                                                            tabindex="<?php echo $tabIndex++; ?>" <?php if (!$PCID) {
                                                        echo 'disabled';
                                                    } ?> onchange="LMRequest.lastUpdatedParams('LO');">
                                                        <option value=''> - Select -</option>
                                                        <?php if ($LMRId > 0 || PageVariables::$userGroup == 'Branch') {
                                                            foreach ($agentList as $j => $item) {
                                                                if ($item->externalBroker == 1) {
                                                                    $sBrokerName = trim($item->bName) . ' ' . trim($item->bLName);
                                                                    $sOpt = Arrays::isSelected(trim($item->brokerNumber), LMRequest::File()->secondaryBrokerNumber);
                                                                    $dispAgentName = $sBrokerName . ' - ' . trim($item->bEmail);

                                                                    echo "<option  value=\"" . trim(cypher::myEncryption(trim($item->brokerNumber))) . "\" " . $sOpt . '>' . ucwords($dispAgentName) . '</option>';

                                                                }
                                                            }
                                                        }
                                                        ?>
                                                    </select>
                                                    <?php if (PageVariables::$userRole == 'Agent' && $externalBroker == 1) { ?>
                                                        <input
                                                                type="hidden" name="loanOfficerID"
                                                                id="loanOfficerID"><?php } ?>
                                                </div>
                                                <?php
                                            }
                                        } else { ?>
                                            <input type="hidden" name="secondaryAgentId" id="secondaryAgentId"
                                                   value="<?php echo cypher::myEncryption(LMRequest::File()->secondaryBrokerNumber); ?>">
                                            <h5><?php echo $sBrokerName; ?></h5>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>
                            <?php } ?>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-4 font-weight-bold" for="fileModule">File Type</label>
                            <div class="col-md-8">
                                <?php
                                if ($allowToEdit) {
                                    ?>
                                    <div class="left" id="module_container">
                                        <select class="mandatory chzn-select" data-placeholder="Select File Type"
                                                name="fileModule[]"
                                                id="fileModule"
                                                tabindex="<?php echo $tabIndex++; ?>"
                                                onchange="getServiceTypes('loanModForm'); showOrHideProperServiceDiv(this.value); formControl.clearLoanProgramFields('LMRClientType',this.value); formControl.controlFormFields(this.id, '','LMRClientType','fileType');"
                                                multiple="">
                                            <?php
                                            $moduleCnt = 0;
                                            if (count($moduleRequested) > 0) $moduleCnt = count($moduleRequested);
                                            for ($j = 0; $j < $moduleCnt; $j++) {
                                                $moduleCode = '';
                                                $sOpt = '';
                                                $moduleName = '';
                                                $moduleCode = trim($moduleRequested[$j]['moduleCode']);
                                                $moduleName = trim($moduleRequested[$j]['moduleName']);
                                                //if(array_key_exists($moduleCode, $glLMRClientTypeArray)) $LMRClientType = trim($glLMRClientTypeArray[$LMRClientTypeCode]);
                                                $chk = '';
                                                $chk = Strings::isKeyChecked($fileModuleInfo, 'moduleCode', $moduleCode);
                                                if (trim($chk) == 'checked') $chk = 'selected ';
                                                ?>
                                                <option <?php echo trim($chk); ?>
                                                        value="<?php echo $moduleCode; ?>"><?php echo $moduleName; ?></option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                    <?php
                                } else {
                                    $selectServices = '';
                                    $j = 0;
                                    for ($i = 0; $i < count($fileModuleInfo); $i++) {
                                        if ($i > 0) $selectServices .= ', ';
                                        $selectServices .= trim($fileModuleInfo[$i]['moduleName']);
                                    }
                                    ?>
                                    <h5><?php echo $selectServices; ?></h5>
                                    <?php
                                }
                                ?>
                            </div>
                        </div>
                        <?php
                        $secArr = BaseHTML::sectionAccess2(['sId' => 'Admin', 'opt' => $fileTab]); /* Field Access.. */
                        loanForm::pushSectionID('Admin');
                        if ($activeTab == 'CI' && !$LMRId) {
                            $CICustomShowField = 0;
                            foreach (showFieldsArray::$showCIFieldsArray['Admin'] as $showFieldKey => $showFieldValue) {
                                if (array_key_exists($showFieldValue, $secArr)) {
                                    $secArr[$showFieldValue]['fieldDisplay'] = 1;
                                    loanForm::showFieldCustom('Admin', $showFieldValue);
                                }
                            }
                        }
                        ?>
                        <div class="form-group row">

                            <label class="col-md-4 font-weight-bold"
                                   for="LMRClientType"><?php echo (BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $secArr, 'opt' => 'L'])) ? BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $secArr, 'opt' => 'L']) : 'What kind of program are you looking for?'; ?></label>
                            <div class="col-md-8">
                                <?php
                                if ($allowToEdit) { ?>
                                    <div id="service_container">
                                        <select data-placeholder="Select Loan Program"
                                                name="LMRClientType[]"
                                                id="LMRClientType"
                                                tabindex="<?php echo $tabIndex++; ?>"
                                            <?php if ($activeTab == 'LI' || $activeTab == 'QAPP') { ?>
                                                onchange="formControl.controlFormFields('fileModule', '',this.id,'loanProgram');
                                                        fixAdditionalLoanProgChosen(this.value);
                                                        populateDualFieldForLP(this.value, 'LMRClientType_mirror');
                                                        populatePCBasicLoanInfo('loanModForm', this.value, '<?php echo $PCID ?>', '', '');
                                                        getPCMinMaxLoanGuidelines('loanModForm', '<?php echo $PCID ?>');
                                                        LMRequest.lastUpdatedParams('LoanProgram');
                                                <?php if (LMRequest::$PCID == glPCID::PCID_PROD_CV3) { ?>
                                                        loanInfoV2Form.showHidePropertyRehabCv3(this.value);
                                                        loanInfoV2Form.hideInitialLoanAmountCV3();
                                                <?php } ?>
                                                        "
                                            <?php } ?>
                                                class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $secArr, 'opt' => 'M']); ?>">
                                            <option></option>
                                            <?php
                                            if ($LMRId > 0 || PageVariables::$userGroup == glUserGroup::USER_GROUP_BRANCH) {
                                                foreach ($servicesRequested as $j => $item) {
                                                    $LMRClientTypeCode = trim($item['LMRClientType']);
                                                    $LMRClientType = trim($item['serviceType']);
                                                    $chk = Strings::isKeyChecked($LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                                                    $displayOption = '';
                                                    if ($LMRClientTypeCode == 'TBD' && $LMRClientTypeInfo[0]['ClientType'] != 'TBD') {
                                                        $displayOption = "style = 'display:none;' ";
                                                    }
                                                    if (trim($chk) == 'checked') {
                                                        $chk = 'selected ';
                                                    }
                                                    if ($item['internalLoanProgram'] == 0) { ?>
                                                        <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                                value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                                        <?php
                                                    }
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <?php
                                } else {
                                    $selectServices = '';
                                    $j = 0;
                                    for ($i = 0; $i < count($LMRClientTypeInfo); $i++) {
                                        if (array_key_exists($LMRClientTypeInfo[$i]['ClientType'], $glLMRClientTypeArray)) {
                                            if ($j > 0) $selectServices .= ', ';
                                            $selectServices .= trim($glLMRClientTypeArray[$LMRClientTypeInfo[$i]['ClientType']]);
                                            $j++;
                                        }
                                    }
                                    if (in_array('TBD', $fileLP) && $LMRId > 0) {
                                        $selectServices = 'TBD';
                                    }
                                    ?>
                                    <h5 style="width:380px;"><?php echo $selectServices; ?></h5>
                                <?php } ?>
                            </div>
                        </div>


                        <div
                                class="form-group row sbaLoanProduct_disp <?php echo loanForm::showField('sbaLoanProduct'); ?>">
                            <?php echo loanForm::label2(
                                'sbaLoanProduct',
                                'col-md-4'
                            ); ?>
                            <div class="col-md-8">
                                <?php
                                if ($allowToEdit) { ?>
                                    <select data-placeholder="Select SBA Loan Product" name="sbaLoanProduct"
                                            id="sbaLoanProduct"
                                            tabindex="<?php echo $tabIndex++; ?>"
                                            class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'sbaLoanProduct', 'sArr' => $secArr, 'opt' => 'M']); ?>">
                                        <option></option>
                                        <?php
                                        if (count($HMLOPCBasicSBALoanProductInfoArray) > 0) {
                                            foreach ($HMLOPCBasicSBALoanProductInfoArray as $eachSBALoanProductID) { ?>
                                                <option
                                                        value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                    echo 'selected';
                                                } ?>><?php echo $globalSBALoanProductsCat[$eachSBALoanProductID]; ?></option>
                                            <?php }
                                        } else {
                                            foreach ($globalSBALoanProductsCat as $eachSBALoanProductID => $eachSBALoanProductVal) { ?>
                                                <option
                                                        value="<?php echo $eachSBALoanProductID; ?>" <?php if (Strings::showField('sbaLoanProduct', 'ResponseInfo') == $eachSBALoanProductID) {
                                                    echo 'selected';
                                                } ?>><?php echo $eachSBALoanProductVal; ?></option>
                                            <?php }
                                        } ?>
                                    </select>
                                    <?php
                                } else { ?>
                                    <h5><?php echo $globalSBALoanProductsCat[Strings::showField('sbaLoanProduct', 'ResponseInfo')]; ?></h5>
                                <?php } ?>
                            </div>
                        </div>


                        <div
                                class="form-group row  additionalLoanProgram_disp <?php echo loanForm::showField('additionalLoanProgram'); ?>">
                            <?php echo loanForm::label2(
                                'additionalLoanProgram',
                                'col-md-4'
                            ); ?>
                            <div class="col-md-8">
                                <?php if ($allowToEdit) { ?>
                                    <select
                                            class=" chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'additionalLoanProgram', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            data-placeholder="Select Additional Loan Program"
                                            id="LMRadditionalLoanProgram"
                                            onchange="formControl.controlFormFields('fileModule', '','LMRClientType','');"
                                            name="LMRadditionalLoanProgram[]" multiple="">
                                        <?php
                                        if ($LMRId > 0 || PageVariables::$userGroup == 'Branch') {
                                            $serviceCnt = 0;
                                            if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                                            for ($j = 0; $j < $serviceCnt; $j++) {
                                                $LMRClientTypeCode = '';
                                                $sOpt = '';
                                                $LMRClientType = '';
                                                $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                                $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                                $chk = '';
                                                if (in_array($LMRClientTypeCode, $myFileInfo['LMRadditionalLoanprograms'] ?? [])) {
                                                    $chk = 'selected ';
                                                }

                                                $displayOption = '';
                                                if ($LMRClientTypeCode == 'TBD') {
                                                    $displayOption = "style = 'display:none;' ";
                                                }

                                                if ($servicesRequested[$j]['internalLoanProgram'] == 0) { ?>
                                                    <option <?php echo $chk; ?> <?php echo $displayOption; ?>
                                                            value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                                    <?php
                                                }
                                            }
                                        } ?>
                                    </select>
                                <?php } else {
                                    $selectAdditionalServices = '';
                                    $j = 0;
                                    for ($i = 0; $i < count($myFileInfo['LMRadditionalLoanprograms'] ?? []); $i++) {
                                        $addKeyVal = '';
                                        $addKeyVal = array_search($myFileInfo['LMRadditionalLoanprograms'][$i], array_column($servicesRequested, 'STCode'));
                                        if ($addKeyVal) {
                                            if ($j > 0) $selectAdditionalServices .= ', ';
                                            $selectAdditionalServices .= trim($servicesRequested[$addKeyVal]['serviceType']);
                                            $j++;
                                        }
                                    }
                                    ?>
                                    <h5 style="width:380px;"><?php echo $selectAdditionalServices; ?></h5>
                                <?php } ?>
                            </div>
                        </div>
                        <?php if (
                            in_array(PageVariables::$userGroup, ['Super', 'Employee'])
                            || (PageVariables::$userGroup == 'Branch' && PageVariables::$allowToAccessInternalLoanProgram == 1)
                            || (PageVariables::$userGroup == 'Agent' && PageVariables::$allowToAccessInternalLoanProgram == 1)
                        ) { ?>

                            <div
                                    class="form-group row  internalLoanProgram_disp <?php echo loanForm::showField('internalLoanProgram'); ?>">
                                <?php echo loanForm::label2(
                                    'internalLoanProgram',
                                    'col-md-4'
                                ); ?>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="hidden" name="LMRInternalLoanProgramShadow" value="">
                                        <select data-placeholder="Select Internal Loan Program"
                                                class=" chzn-select <?php echo BaseHTML::fieldAccess(['fNm' => 'internalLoanProgram', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                id="LMRInternalLoanProgram"
                                                onchange="formControl.controlFormFields('fileModule', '','LMRClientType','');"
                                                name="LMRInternalLoanProgram[]" multiple="">
                                            <option></option>
                                            <?php $serviceCnt = 0;
                                            if (count($internalLoanProgramList) > 0) $serviceCnt = count($internalLoanProgramList);
                                            for ($j = 0; $j < $serviceCnt; $j++) {
                                                $LMRClientTypeCode = '';
                                                $sOpt = '';
                                                $LMRClientType = '';
                                                $LMRClientTypeCode = trim($internalLoanProgramList[$j]['LMRClientType']);
                                                $LMRClientType = trim($internalLoanProgramList[$j]['serviceType']);
                                                if ($internalLoanProgramList[$j]['internalLoanProgram'] == '1') {
                                                    $chk = '';
                                                    if ($PCID == 4326 && $LMRId == 0) {  // **exception** PCID = 4326 (BD Capital)
                                                        if ($LMRClientType == 'To Be Determined') {
                                                            $chk = 'selected';
                                                        }
                                                    } else { // old code
                                                        if (in_array($LMRClientTypeCode, $myFileInfo['LMRInternalLoanprograms'] ?? [])) {
                                                            $chk = 'selected ';
                                                        }
                                                    }
                                                    ?>
                                                    <option <?php echo $chk; ?>
                                                            value="<?php echo $LMRClientTypeCode; ?>"><?php echo $LMRClientType; ?></option>
                                                    <?php
                                                }
                                            } ?>
                                        </select>
                                    <?php } else {
                                        $selectInternalServices = '';
                                        $j = 0;
                                        for ($i = 0; $i < count($myFileInfo['LMRInternalLoanprograms']); $i++) {
                                            $addKeyVal = '';
                                            $addKeyVal = array_search($myFileInfo['LMRInternalLoanprograms'][$i], array_column($internalLoanProgramList, 'LMRClientType'));
                                            if (is_numeric($addKeyVal)) {
                                                if ($j > 0) $selectInternalServices .= ', ';
                                                $selectInternalServices .= trim($internalLoanProgramList[$addKeyVal]['serviceType']);
                                                $j++;
                                            }
                                        }
                                        ?>
                                        <h5 style="width:380px;"><?php echo $selectInternalServices; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        <?php } ?>

                        <div
                                class="form-group row inProcessGrp propDetailsProcess_disp <?php echo loanForm::showField('propDetailsProcess'); ?>">
                            <?php echo loanForm::label2(
                                'propDetailsProcess',
                                'col-md-4'
                            ); ?>
                            <div class="col-md-8">
                                <?php if ($allowToEdit) { ?>
                                    <select
                                            class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            name="propDetailsProcess" id="propDetailsProcess"
                                            tabindex="<?php echo $tabIndex++; ?>"
                                            onchange="populatePropertyDetails(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'propDetailsProcess', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <option value=''> - Select -</option>
                                        <?php
                                        for ($i = 0;
                                             $i < count($glPropDetailsProcess);
                                             $i++) {
                                            $sOpt = '';
                                            $propDetals = '';
                                            $propDetals = trim($glPropDetailsProcess[$i]);
                                            $sOpt = Arrays::isSelected($propDetals, $propDetailsProcess);
                                            echo "<option value=\"" . $propDetals . "\" " . $sOpt . '>' . $propDetals . '</option>';
                                        }
                                        ?>
                                    </select>
                                <?php } else { ?>
                                    <b><?php echo $propDetailsProcess ?></b>
                                <?php } ?>                    </div>
                        </div>

                        <?php
                        if ((($PCID == glPCID::PCID_MORTGAGE_ASSISTANCE || $PCID == 2 || $PCID == 1545 || $PCID == 820) || (PageVariables::$userGroup == 'Client' && $allowClientToCreateHMLOFile == 1)) && $LMRId == 0) {
                            /* File creation default primary file status to "Lead"  for the PC Synergy Attorney Services = 1545,820 = Dave PC, Nov 25 2015 */
                            /* File creation default primary file status to "Lead"  for the Client Login HMLO, Mar 31 2017 */

                            for ($j = 0; $j < count($PCStatusInfo); $j++) {
                                if (strtolower(trim($PCStatusInfo[$j]['primaryStatus'])) == 'lead') {
                                    $primaryStatusId = trim($PCStatusInfo[$j]['PSID']);
                                    break;
                                }
                            }
                            ?>
                            <input type="hidden" name="primaryStatus" id="primaryStatus"
                                   value="<?php echo $primaryStatusId; ?>">
                            <?php
                        }
                        if ($publicUser != 1) { ?>
                            <input type="hidden" name="OSID" id="OSID"
                                   value="<?php echo Strings::showField('primeStatusId', 'ResponseInfo') ?>">
                        <?php }

                        if ((PageVariables::$allowToUpdateFileAdminSection == 1 && $allowToEdit) || ($LMRId == 0 && PageVariables::$userGroup != 'Client')) {
                            ?>

                            <div
                                    class="form-group row primaryStatus_disp <?php echo loanForm::showField('primaryStatus'); ?>">
                                <?php echo loanForm::label2(
                                    'primaryStatus',
                                    'col-md-4'
                                ); ?>
                                <div class="col-md-8">
                                    <div id="primaryStatus_container">
                                        <?php
                                        $tempModulesCodeArr = [];
                                        for ($j = 0;
                                             $j < count($PCStatusInfo);
                                             $j++) {
                                            $moduleCode = $PCStatusInfo[$j]['moduleName'];
                                            $tempModulesCodeArr[$moduleCode][] = $PCStatusInfo[$j];
                                        }
                                        $moduleKeys = [];
                                        $moduleKeys = array_keys($tempModulesCodeArr);
                                        if (PageVariables::$userRole == 'Agent' && (in_array($PCID, $glSendPSChangeNotificationToPC)) && $LMRId == 0) {/* File creation Allow Agent to assign primary status for the PC NVA Financial Services = 1456,820 = Dave PC, Sep 09 2016 */
                                        ?>
                                        <select
                                                class="form-control input-sm primaryStatus <?php echo BaseHTML::fieldAccess(['fNm' => 'primaryStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="primaryStatus" id="primaryStatus"
                                                tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'primaryStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <?php } else {  /* File creation default primary file status to "Lead"  for the PC Synergy Attorney Services = 1545,820 = Dave PC, Nov 25 2015 */ ?>
                                            <select
                                                    class="form-control input-sm primaryStatus <?php echo BaseHTML::fieldAccess(['fNm' => 'primaryStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    name="primaryStatus"
                                                    id="primaryStatus" <?php if (($PCID == glPCID::PCID_MORTGAGE_ASSISTANCE || $PCID == 2 || $PCID == 1545 || $PCID == 820) && $LMRId == 0
                                                || (PageVariables::$userRole == 'Agent' && ($PCID == 820 || $PCID == 1545) && $LMRId == 0)) { ?>  disabled <?php } ?>
                                                    tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'primaryStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <?php } ?>
                                                <option value=""> - Select -</option>
                                                <?php
                                                for ($k = 0;
                                                     $k < count($moduleKeys);
                                                     $k++) {
                                                    ?>
                                                    <optgroup label="<?php echo $moduleKeys[$k] ?>">
                                                        <?php
                                                        $tempKeys = $tempModulesCodeArr[$moduleKeys[$k]];
                                                        for ($plm = 0;
                                                             $plm < count($tempKeys);
                                                             $plm++) {
                                                            $PSID = 0;
                                                            $sOpt = '';
                                                            if ($primaryStatusId > 0) {
                                                                if (trim($tempKeys[$plm]['PSID']) == $primaryStatusId) $sOpt = 'selected';
                                                            }
                                                            $showHidePrimaryStatusGroup = '';
                                                            if (PageVariables::$userGroup == 'Employee') {
                                                                if ((trim($tempKeys[$plm]['allowBOToEditFile']) == 0)
                                                                    && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                                    $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                                }
                                                            } elseif (PageVariables::$userGroup == 'Branch') {
                                                                if ((trim($tempKeys[$plm]['allowOthersToUpdate']) == 0)
                                                                    && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                                    $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                                }
                                                            } elseif (PageVariables::$userGroup == 'Agent' && $externalBroker == 0) {
                                                                if ((trim($tempKeys[$plm]['allowAgentToEditFile']) == 0)
                                                                    && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                                    $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                                }
                                                            } elseif (PageVariables::$userGroup == 'Agent' && $externalBroker == 1) {
                                                                if ((trim($tempKeys[$plm]['allowLoanOfficerToEditFile']) == 0)
                                                                    && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                                                                    $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                                }
                                                            } elseif (PageVariables::$userGroup == 'Client') {
                                                                if (trim($tempKeys[$plm]['allowClientToEditFile']) == 0) {
                                                                    $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                                                                }
                                                            }

                                                            ?>
                                                            <option <?php echo $showHidePrimaryStatusGroup; ?>
                                                                    value="<?php echo trim($tempKeys[$plm]['PSID']) ?>" <?php echo $sOpt ?>><?php echo trim($tempKeys[$plm]['primaryStatus']) ?></option>
                                                            <?php
                                                        }
                                                        ?>
                                                    </optgroup>
                                                <?php } ?>
                                            </select>
                                    </div>
                                </div>
                            </div>

                            <div
                                    class="form-group row substatusID_disp <?php echo loanForm::showField('substatusID'); ?>">
                                <?php echo loanForm::label2(
                                    'substatusID',
                                    'col-md-4',
                                    'These sub-statuses act as &quot;Tags&quot; and you may select more than 1 sub-status.<br>If you want to remove sub-statuses, you must de-select them from the company profile while logged in as a Manager.<br>If you need more sub-status options, please email us.'
                                ); ?>
                                <div class="col-md-8">
                                    <?php if ($allowToEdit) { ?>
                                        <select data-placeholder="Select File Sub Status"
                                                tabindex="<?php echo $tabIndex++; ?>" <?php if ($LMRId == 0) { ?> name="substatusID[]" id="substatusID" <?php } else { ?> name="LMRProcessorStatus" id="LMRProcessorStatus" <?php } ?>
                                                class="chzn-select odd <?php echo BaseHTML::fieldAccess(['fNm' => 'substatusID', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                multiple="">
                                            <?php
                                            $subStatuaCnt = 0;
                                            $tempModulesCodeArr = $tempArr = [];

                                            if ($LMRId > 0) {
                                            } else {
                                                foreach ($PCSubStatusInfo as $subSTMC => $subSTArray) {
                                                    for ($sm = 0;
                                                         $sm < count($subSTArray);
                                                         $sm++) {
                                                        $tempArr[] = $subSTArray[$sm];
                                                    }
                                                }
                                                $PCSubStatusInfo = array_values($tempArr);
                                            }
                                            $subStatuaCnt = count($PCSubStatusInfo);
                                            foreach ($PCSubStatusInfo as $j => $item) {
                                                if (is_object($item)) {
                                                    /* @var PCSubStatusInfo $item */
                                                    $moduleCode = $item->moduleName;
                                                    $category = $item->category;
                                                    $tempModulesCodeArr[$moduleCode][$category][] = $item->toArray();
                                                } else {
                                                    /* @var array $item */
                                                    $moduleCode = $item['moduleName'];
                                                    $category = $item['category'];
                                                    $tempModulesCodeArr[$moduleCode][$category][] = $item;
                                                }
                                            }

                                            $moduleKeys = array_keys($tempModulesCodeArr);
                                            foreach ($moduleKeys as $j => $moduleCode) {
                                                $categoryArr = $tempModulesCodeArr[$moduleCode];
                                                $categoryKeys = array_keys($categoryArr);
                                                ?>
                                                <option class="optnGrp" disabled
                                                        value=""><?php echo $moduleCode ?></option>
                                                <?php
                                                foreach ($categoryKeys as $k => $category) {
                                                    $substatusArr = $tempModulesCodeArr[$moduleCode][$category];
                                                    ?>
                                                    <optgroup label="<?php echo $category ?>">
                                                        <?php

                                                        foreach ($substatusArr as $l => $item) {
                                                            $sOpt = '';
                                                            $PFSID = trim($item['PFSID']);
                                                            $substatus = trim($item['substatus']);
                                                            $chk = is_object($fileSubstatusInfo) ? $fileSubstatusInfo->substatusId == $PFSID : Strings::isKeyChecked($fileSubstatusInfo, 'substatusId', $PFSID);
                                                            if (trim($chk) == 'checked') $chk = 'selected ';
                                                            ?>
                                                            <option <?php echo $chk ?>
                                                                    value="<?php echo $PFSID ?>"><?php echo $substatus ?></option>
                                                            <?php
                                                        }
                                                        ?>
                                                    </optgroup>
                                                    <?php
                                                }
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        $substatusName = [];
                                        $subStatuaCnt = count($substatusArray);
                                        for ($j = 0;
                                             $j < $subStatuaCnt;
                                             $j++) {
                                            $substatusName[] = trim($substatusArray[$j]['substatus']);
                                        }
                                        ?>
                                        <h5><?php echo implode(', ', array_unique($substatusName)) ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                    <?php if ($CICustomShowField) { ?>
                        <div class="col-md-4">
                            <?php if ($LMRId > 0) { ?>
                                <div class="card card-custom gutter-b">
                                    <div class="card-header card-header-tabs-line bg-gray-100 ">
                                        <div class="card-toolbar">

                                            <ul class="nav nav-tabs nav-bold nav-tabs-line ">

                                                <?php if ($LMRId > 0 && (strpos($brokerEmail, '@dummyAgentemail.com') === false) && $brokerActiveStatus) { ?>
                                                    <li class="nav-item">
                                                        <a class="nav-link active" data-toggle="tab"
                                                           href="#kt_tab_pane_Broker">
                                                            <span class="nav-text">Broker/Partner Info</span>
                                                        </a>
                                                    </li>
                                                <?php } ?>

                                                <?php if ($LMRId > 0 && LMRequest::File()->secondaryBrokerNumber > 0) { ?>
                                                    <li class="nav-item">
                                                        <a class="nav-link <?php if (LMRequest::File()->secondaryBrokerNumber > 0 &&
                                                            ($LMRId > 0 && strpos($brokerEmail, '@dummyAgentemail.com'))) echo 'active' ?>"
                                                           data-toggle="tab"
                                                           href="#kt_tab_pane_Loan_officer">
                                                            <span class="nav-text">Loan Officer Info </span></a>
                                                    </li>
                                                <?php } ?>
                                                <?php if ($LMRId > 0) { ?>
                                                    <li class="nav-item">
                                                        <a class="nav-link <?php if (($LMRId > 0 && strpos($brokerEmail, '@dummyAgentemail.com'))
                                                            && ($LMRId > 0 && LMRequest::File()->secondaryBrokerNumber == 0) || ($LMRId > 0 && !$brokerActiveStatus)) echo 'active'; ?>"
                                                           data-toggle="tab" href="#kt_tab_pane_Borrower">
                                                            <span class="nav-text"> Borrower Overview</span></a>
                                                    </li>
                                                <?php } ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="card-body px-2">
                                        <div class="tab-content" id="myTabContentAdminSection">
                                            <div
                                                    class="tab-pane fade show <?php if ((($LMRId > 0 && (strpos($brokerEmail, '@dummyAgentemail.com') === false))
                                                            && ($LMRId > 0 && LMRequest::File()->secondaryBrokerNumber == 0) && $brokerActiveStatus)
                                                        || (($LMRId > 0 && (strpos($brokerEmail, '@dummyAgentemail.com') === false))
                                                            && ($LMRId > 0 && LMRequest::File()->secondaryBrokerNumber > 0))) echo 'active'; ?>"
                                                    id="kt_tab_pane_Broker" role="tabpanel"
                                                    aria-labelledby="kt_tab_pane_Broker">

                                                <?php if ($LMRId > 0 && (strpos($brokerEmail, '@dummyAgentemail.com') === false) && $brokerActiveStatus) { ?>

                                                    <!-- Mortgage Broker Info Start -->
                                                    <div class="d-flex justify-content-between">
                                                        <div class="table-responsive">
                                                            <?php
                                                            if ($brokerContactInfo) {
                                                                echo $brokerContactInfo;
                                                            } ?>
                                                        </div>

                                                        <?php if (PageVariables::$userGroup == 'Super' || PageVariables::$userGroup == 'Employee' || PageVariables::$userRole == 'Branch') { ?>
                                                        <a data-href="<?php echo CONST_URL_POPS; ?>addNewAgent.php"
                                                           data-wsize='modal-xl'
                                                           data-name='Add New Broker'
                                                           data-toggle='modal' data-target='#exampleModal1'
                                                           data-id='executiveId=<?php echo cypher::myEncryption($executiveId) ?>&PCID=<?php echo $PCID ?>&isHMLO=<?php echo $isHMLOSelOpt ?>'
                                                           data-content="<?php if ($isHMLO == 1) { ?>Select Add new broker to add a broker to your broker list.<?php } else { ?>If the your desired Broker is missing, please add new Mortgage Broker and<br>make sure the Broker is assigned to the Branch<?php } ?>"

                                                           class="btn btn-sm btn-success btn-text-primary  btn-icon m-1 popoverClass">
                                                                <i class="icon-md fas fa-plus "></i>
                                                            </a><?php } ?>
                                                    </div>
                                                    <!-- Mortgage Broker Info End -->

                                                <?php } ?>

                                            </div>
                                            <div
                                                    class="tab-pane fade <?php if (LMRequest::File()->secondaryBrokerNumber > 0 && ($LMRId > 0
                                                            && strpos($brokerEmail, '@dummyAgentemail.com'))) echo 'active show' ?>"
                                                    id="kt_tab_pane_Loan_officer" role="tabpanel"
                                                    aria-labelledby="kt_tab_pane_Loan_officer">
                                                <?php if ($LMRId > 0 && LMRequest::File()->secondaryBrokerNumber > 0) { ?>
                                                    <!-- Loan Officer Info Start -->

                                                    <div class="d-flex justify-content-between">
                                                        <div class="table-responsive">
                                                            <?php
                                                            if ($sBrokerContactInfo) {
                                                                echo $sBrokerContactInfo;
                                                            }
                                                            ?>
                                                        </div>
                                                        <?php if (PageVariables::$userGroup == 'Super' || PageVariables::$userGroup == 'Employee' || PageVariables::$userRole == 'Branch') { ?>
                                                            <a data-href="<?php echo CONST_URL_POPS; ?>addNewAgent.php"
                                                               data-wsize='modal-xl'
                                                               data-name='Add New Loan Officer'
                                                               data-toggle='modal' data-target='#exampleModal1'
                                                               data-id='executiveId=<?php echo cypher::myEncryption($executiveId) ?>&PCID=<?php echo $PCID ?>&isHMLO=<?php echo $isHMLOSelOpt ?>&externalBroker=1'
                                                               data-content="<?php if ($isHMLO == 1) { ?>Select Add new Loan Officer to add a Loan officer to your Loan Officer list.<?php } else { ?>If the your desired Loan officer is missing, please add new Loan Officer and<br>make sure the Loan Officer is assigned to the Branch<?php } ?>"

                                                               class="btn btn-sm btn-success btn-text-primary  btn-icon m-1 popoverClass">
                                                                <i class="icon-md fas fa-plus "></i>
                                                            </a>
                                                        <?php } ?>
                                                    </div>

                                                    <!-- Loan Officer Info End -->
                                                <?php } ?>
                                            </div>
                                            <div
                                                    class="tab-pane fade <?php if (($LMRId > 0 && strpos($brokerEmail, '@dummyAgentemail.com'))
                                                        && ($LMRId > 0 && LMRequest::File()->secondaryBrokerNumber == 0) || ($LMRId > 0 && !$brokerActiveStatus)) echo ' active show'; ?>"
                                                    id="kt_tab_pane_Borrower" role="tabpanel"
                                                    aria-labelledby="kt_tab_pane_Borrower">
                                                <?php if ($LMRId > 0) { ?>
                                                    <!-- Borrower Info Start -->

                                                    <div class="d-flex justify-content-between">
                                                        <div class="table-responsive">
                                                            <?php
                                                            if ($borrowerContactInfo) {
                                                                echo $borrowerContactInfo;
                                                            }
                                                            //Co-Borrower Info
                                                            if ($coBorrowerInfo) {
                                                                echo $coBorrowerInfo;
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>

                                                    <!-- End Info Start -->
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php

                                if ($allowToViewCreditScreening > 0 && $pcAcqualifyStatus > 0) {

                                    $acuqlifyParams['firstName'] = Strings::showField('borrowerFName', 'LMRInfo');
                                    $acuqlifyParams['lastName'] = Strings::showField('borrowerLName', 'LMRInfo');
                                    $acuqlifyParams['addrLine1'] = Strings::showField('mailingAddress', 'LMRInfo');
                                    $acuqlifyParams['city'] = Strings::showField('mailingCity', 'LMRInfo');
                                    $acuqlifyParams['state'] = Strings::showField('mailingState', 'LMRInfo');
                                    $acuqlifyParams['zip'] = Strings::showField('mailingZip', 'LMRInfo');
                                    $acuqlifyParams['dob'] = Strings::showField('borrowerDOB', 'LMRInfo');
                                    $acuqlifyParams['email'] = Strings::showField('borrowerEmail', 'LMRInfo');
                                    $acuqlifyParams['phoneNumber'] = Strings::formatCellNumberWithOut(Strings::showField('cellNumber', 'LMRInfo'));
                                    ?>
                                    <div class="card card-custom gutter-b" id="CreditScreening">
                                        <div class="card-header card-header-tabs-line bg-gray-100 ">
                                            <div class="card-title">
                                                <h3 class="card-label">Credit Screening</h3>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <?php
                                            $clientCreditScoreVal = '';
                                            if (count($acqualifyClientCreditInfo) > 0) {
                                                if (isset($acqualifyClientCreditInfo[$acqualifyClientId])) {
                                                    $acqualifynotificationList = "<div class='row p-4'>";
                                                    foreach ($acqualifyClientCreditInfo[$acqualifyClientId] as $clientCreditKey => $clientCreditVal) {
                                                        if (!in_array($clientCreditKey, ['id', 'accountId', 'clientId', 'lmrid', 'applicantIdentifier', 'userNumber', 'userGroup', 'message'])) {
                                                            if ($clientCreditKey == 'submittedDate') {
                                                                $clientCreditVal = Dates::formatDateWithRE($clientCreditVal, 'YMD', 'm/d/Y');
                                                            }
                                                            if ($clientCreditKey == 'score') {
                                                                $clientCreditScoreVal = $clientCreditVal;
                                                            }
                                                            $acqualifynotificationList .= "<div class='col-md-3 border '>" . ucwords(str_replace('_', ' ', $clientCreditKey)) . "</div> <div class='col-md-3 border'> <b>" . $clientCreditVal . '</b></div>';
                                                        }
                                                    }
                                                    $acqualifynotificationList .= '</div>';
                                                    if ($clientCreditScoreVal) {
                                                        echo 'Credit Score: ' . $clientCreditScoreVal;
                                                    }
                                                    ?>
                                                    <i class="fas fa-info-circle text-primary manualPopover"
                                                       data-html="true"
                                                       data-content="<?php echo $acqualifynotificationList; ?>"></i>
                                                    <?php
                                                }
                                                echo '<br>';
                                                ?>
                                                <?php
                                            } ?>
                                            <a href="javascript:submitToAcqualify();" id="submitToAcqualify"
                                               class="btn btn-sm  btn-primary btn-text-white   m-1 tooltipClass"
                                               data-info="<?php echo base64_encode(json_encode($acuqlifyParams)); ?>">Submit
                                                Soft Credit Pull</a>
                                        </div>
                                    </div>
                                <?php }
                            } ?>
                        </div>
                    <?php } ?>
                </div>

                <?php if ($allowToEdit && PageVariables::$userGroup != 'Client' && $CICustomShowField) { ?>
                    <div class="row">
                        <div class=" col-md-6 loanNumber_disp <?php echo loanForm::showField('loanNumber'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'loanNumber',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($showStartLoanNumber == 1 && !glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) { ?>
                                        <div class="input-group">
                                            <input
                                                    class="form-control  <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    type="text" name="loanNumber" id="loanNumber"
                                                    placeholder="Loan Number"
                                                <?php if (($fileTab == 'FA' || $fileTab == 'QA') && (trim(Strings::isKeyChecked($fileModuleInfo, 'moduleCode', 'LM')) == 'checked' || trim(Strings::isKeyChecked($fileModuleInfo, 'moduleCode', 'SS')) == 'checked')) { ?> onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                                    value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                                    maxlength="45"
                                                    size="25" autocomplete="off" tabindex="<?php echo $tabIndex++; ?>"
                                                    readonly>
                                            <?php if (!Strings::showField('loanNumber', 'LMRInfo')) { ?>
                                                <div class="input-group-append">
                                                    <span class="input-group-text" id="getLoanNo">
                                                        <a style="text-decoration:none;"
                                                           class="fa fa-refresh"
                                                           onclick="getAvailableLoanNo('<?php echo cypher::myEncryption($PCID) ?>','loanNumber');"
                                                           title="Click to auto create loan number.">
                                                            <i class="tooltipClass flaticon2-reload text-success"></i>
                                                        </a>
                                                    </span>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    <?php } else if ($allowToEdit) { ?>
                                        <input
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                type="text" name="loanNumber" id="loanNumber" placeholder="Loan Number"
                                            <?php if (($fileTab == 'FA' || $fileTab == 'QA') && (trim(Strings::isKeyChecked($fileModuleInfo, 'moduleCode', 'LM')) == 'checked' || trim(Strings::isKeyChecked($fileModuleInfo, 'moduleCode', 'SS')) == 'checked')) { ?> onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                                value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                                maxlength="45"
                                                size="25"
                                                autocomplete="off"
                                                tabindex="<?php echo $tabIndex++; ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $secArr, 'opt' => 'I']);
                                            if (glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) {
                                                echo 'readonly';
                                            }
                                            ?> >
                                    <?php } else { ?>
                                        <h5><?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 leadSource_disp <?php echo loanForm::showField('leadSource'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'leadSource',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <input
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    type="text" name="leadSource" id="leadSource"
                                                    value="<?php echo htmlentities(Strings::showField('leadSource', 'ResponseInfo')); ?>"
                                                    placeholder=" - Type Lead Source - " maxlength="48"
                                                    tabindex="<?php echo $tabIndex ?>" size="40" autocomplete="off"
                                                    onclick="listAllLeadSource(this.value);
                                            populateDualField(this.value, 'leadSource_mirror');"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'leadSource', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            <div class="input-group-append popoverClass"
                                                 data-content="Click to remove Lead Source">
                                                <span class="input-group-text"
                                                      onclick="clearLeadSourceVal('loanModForm', 'leadSource');">
                                                    <i class="fa fa-times text-danger"></i>
                                                </span>
                                            </div>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo htmlentities(Strings::showField('leadSource', 'ResponseInfo')); ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <?php
                        $style = 'style=display:none';
                        if (Strings::showField('leadSource', 'ResponseInfo') == 'Other') {
                            $style = 'style=display:flex';
                            $style = '';
                        }

                        //          echo '>>'.loanForm::getPropertyLength('hereAbout',new tblFileHMLOPropInfo()).'<<';
                        //        echo '>>'.tblFileHMLOPropInfo::getPropertyLength('hereAbout','tblFileHMLOPropInfo').'<<';

                        //exit;
                        ?>
                        <div id="refDiv" class="col-md-6" <?php echo $style; ?> >
                            <div class="form-group row">
                                <label class="col-md-5 font-weight-bold" for="hereAbout">How did you hear about
                                    us?</label>
                                <div class="col-md-7">
                            <textarea class="form-control input-sm " name="hereAbout" id="hereAbout"
                                      tabindex="<?php echo $tabIndex++; ?>"
                                      placeholder="Let us know how you heard about us."><?php echo Strings::showField('hereAbout', 'fileHMLOPropertyInfo'); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <?php if (PageVariables::$userRole == 'Super' || PageVariables::$userGroup == 'Employee') { ?>
                            <div class=" col-md-6 projectName_disp <?php echo loanForm::showField('projectName'); ?>">
                                <div class="form-group row">
                                    <?php echo loanForm::label2(
                                        'projectName',
                                        'col-md-5'
                                    ); ?>
                                    <div class="col-md-7">
                                        <?php if ($allowToEdit) { ?>
                                            <input
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'projectName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                    type="text" name="projectName" id="projectName"
                                                    value="<?php echo htmlentities(Strings::showField('projectName', 'ResponseInfo')); ?>"
                                                    placeholder="Project name"
                                                    maxlength="45"
                                                    autocomplete="off"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'projectName', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?>
                                            <h5><?php echo htmlentities(Strings::showField('projectName', 'ResponseInfo')); ?></h5>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                        <div class=" col-md-6 referringParty_disp <?php echo loanForm::showField('referringParty'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'referringParty',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                type="text" name="referringParty" id="referringParty"
                                                value="<?php echo htmlentities(Strings::showField('referringParty', 'fileHMLOPropertyInfo')); ?>"
                                                placeholder="Referring Party"
                                                autocomplete="off" tabindex="<?php echo $tabIndex++ ?>"
                                                maxlength="45"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'referringParty', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <?php } else { ?>
                                        <h5><?php echo htmlentities(Strings::showField('referringParty', 'fileHMLOPropertyInfo')); ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class=" col-md-6 receivedDate_disp <?php echo loanForm::showField('receivedDate'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'receivedDate',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend receivedDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                                            </div>
                                            <input
                                                    class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'receivedDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                    type="text" name="receivedDate" placeholder="MM/DD/YYYY"
                                                    tabindex="<?php echo $tabIndex ?>" id="receivedDate"
                                                    value="<?php echo $receivedDate ?>" maxlength="10"
                                                    size="12" <?php echo BaseHTML::fieldAccess(['fNm' => 'receivedDate', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else { ?>
                                        <div class="left"><h5> <?php echo $receivedDate ?></h5></div>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-6 borrowerCallBack_disp <?php echo loanForm::showField('borrowerCallBack'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'borrowerCallBack',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend borrowerCallBack">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                                            </div>
                                            <input
                                                    class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCallBack', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                    type="text" name="borrowerCallBack" placeholder="MM/DD/YYYY"
                                                    id="borrowerCallBack" value="<?php echo $borrowerCallBack ?>"
                                                    maxlength="10"
                                                    size="12"
                                                    tabindex="<?php echo $tabIndex ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'borrowerCallBack', 'sArr' => $secArr, 'opt' => 'I']); ?>>

                                        </div>
                                    <?php } else { ?>
                                        <div class="left"><h5> <?php echo $borrowerCallBack ?></h5></div><?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-6 welcomeCallDate_disp <?php echo loanForm::showField('welcomeCallDate'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'welcomeCallDate',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend welcomeCallDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                                            </div>
                                            <input
                                                    class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'welcomeCallDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                    type="text" name="welcomeCallDate" id="welcomeCallDate"
                                                    placeholder="MM/DD/YYYY"
                                                    value="<?php echo $welcomeCallDate ?>" maxlength="10" size="12"
                                                    tabindex="<?php echo $tabIndex ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'welcomeCallDate', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else { ?>
                                        <div class="left"><h5> <?php echo $welcomeCallDate ?></h5></div>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-6 purchaseCloseDate_disp <?php echo loanForm::showField('purchaseCloseDate'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'purchaseCloseDate',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7 font-weight-bold">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend closingDate_1">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                                            </div>
                                            <input
                                                    class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'purchaseCloseDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                    type="text" name="purchaseCloseDate"
                                                    id="closingDate_1" value="<?php echo $purchaseCloseDate ?>"
                                                    size="10"
                                                    tabindex="<?php echo $tabIndex ?>" autocomplete="off"
                                                    placeholder="MM/DD/YYYY"
                                                    onchange="populateDualDateForHMLONewLoan(this.value, 'loanModForm', 'datesigned');populateDualField(this.value, 'closingDate');">
                                        </div>
                                    <?php } else { ?>
                                        <div class="left"><h5> <?php echo $purchaseCloseDate ?></h5></div>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-6 targetClosingDate_disp <?php echo loanForm::showField('targetClosingDate'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'targetClosingDate',
                                    'col-md-5'
                                );
                                ?>
                                <div class="col-md-7 font-weight-bold">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend ">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                                            </div>
                                            <input
                                                    class="form-control input-sm dateNewClass <?php echo BaseHTML::fieldAccess(['fNm' => 'targetClosingDate', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                    type="text"
                                                    name="targetClosingDate"
                                                    id="targetClosingDate"
                                                    value="<?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y'); ?>"
                                                    size="10"
                                                    tabindex="<?php echo $tabIndex ?>"
                                                    autocomplete="off"
                                                    placeholder="MM/DD/YYYY">
                                        </div>
                                    <?php } else { ?>
                                        <div class="left">
                                            <h5> <?php echo Dates::formatDateWithRE(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y') ?></h5>
                                        </div>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>


                        <div class=" col-md-6 hearingDate_disp <?php echo loanForm::showField('hearingDate'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    'hearingDate',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend hearingDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                                            </div>
                                            <input type="text" name="hearingDate" id="hearingDate"
                                                   value="<?php echo $hearingDate; ?>"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'hearingDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'hearingDate', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   autocomplete="off" placeholder="MM/DD/YYYY"
                                                   tabindex="<?php echo $tabIndex ?>"/>
                                        </div>
                                    <?php } else { ?>
                                        <h5> <?php echo $hearingDate; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class=" col-md-6">
                            <div class="form-group row">
                                <label class="col-md-5 font-weight-bold">Created Date</label>
                                <div class="col-md-7">
                                    <?php
                                    echo '<h5>' . $createdDate . '</h5>';
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php if ($LMRId > 0 && $publicUser == 0) { ?>
                            <div class="col-md-6">
                                <div class="form-group row">
                                    <label class="col-md-5 font-weight-bold">File ID</label>
                                    <div class="col-md-7">
                                        <?php
                                        echo '<h5>' . $LMRId . '</h5>';
                                        ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                        <div class=" col-md-6 3rdPartyFileId_disp <?php echo loanForm::showField('3rdPartyFileId'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label2(
                                    '3rdPartyFileId',
                                    'col-md-5'
                                ); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => '3rdPartyFileId', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               type="text" name="3rdPartyFileId" id="_3rdPartyFileId"
                                               value="<?php echo htmlentities(Strings::showField('3rdPartyFileId', 'ResponseInfo')); ?>"
                                               maxlength="60"
                                               autocomplete="off"
                                               tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => '3rdPartyFileId', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <h5><?php echo htmlentities(Strings::showField('3rdPartyFileId', 'ResponseInfo')); ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class=" col-md-6  <?php echo loanForm::showField('disclosureSentDate'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('disclosureSentDate', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <div class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </div>
                                            </div>
                                            <input type="text"
                                                   data-column="tblFileAdminInfo"
                                                   name="disclosureSentDate"
                                                   id="disclosureSentDate"
                                                   class="autosaveFileAdminInfo form-control input-sm dateNewClass
                                               <?php echo BaseHTML::fieldAccess(['fNm' => 'disclosureSentDate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   tabindex="<?php echo $tabIndex; ?>"
                                                   placeholder="MM/DD/YYYY"
                                                   value="<?php echo Dates::formatDateWithRE(fileAdminInfo::$fileAdminInfo->disclosureSentDate, 'YMD', 'm/d/Y'); ?>"
                                                   autocomplete="off"
                                                   data-table="tblProperties"
                                                   data-id="<?php echo fileAdminInfo::$fileAdminInfo->LMRId; ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'disclosureSentDate', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo Dates::formatDateWithRE(fileAdminInfo::$fileAdminInfo->disclosureSentDate, 'YMD', 'm/d/Y'); ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 <?php echo loanForm::showField('loanDocumentDate'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('loanDocumentDate', 'col-md-5'); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <div class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </div>
                                            </div>
                                            <input type="text"
                                                   data-column="loanDocumentDate"
                                                   name="loanDocumentDate"
                                                   id="loanDocumentDate"
                                                   class="autosaveFileAdminInfo form-control input-sm dateNewClass
                                               <?php echo BaseHTML::fieldAccess(['fNm' => 'loanDocumentDate', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   tabindex="<?php echo $tabIndex; ?>"
                                                   placeholder="MM/DD/YYYY"
                                                   value="<?php echo Dates::formatDateWithRE(fileAdminInfo::$fileAdminInfo->loanDocumentDate, 'YMD', 'm/d/Y'); ?>"
                                                   autocomplete="off"
                                                   data-table="tblFileAdminInfo"
                                                   data-id="<?php echo fileAdminInfo::$fileAdminInfo->LMRId; ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'loanDocumentDate', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo Dates::formatDateWithRE(fileAdminInfo::$fileAdminInfo->loanDocumentDate, 'YMD', 'm/d/Y'); ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                    </div>
                <?php } ?>

                <?php if (LMRequest::File()->getTblProcessingCompany_by_FPCID()->MERSID) { ?>
                    <div class="row">

                        <div class="col-md-6 <?php echo loanForm::showField('MERSID'); ?>">
                            <div class="form-group row">
                                <?php
                                echo loanForm::label(
                                    'MERSID',
                                    'col-md-5 ',
                                    'Click the green refresh icon to auto-generate an 18-digit MIN ID, or manually enter your own by enabling user level permission for "Allowed to manually set MIN ID #"',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'MERSID',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'MIN #'
                                    ),
                                );
                                ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <input
                                                    type="number"
                                                    name="MERSID"
                                                    id="MERSID"
                                                    class="form-control"
                                                    value="<?php echo LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->MERSID; ?>"
                                                    autocomplete="off"
                                                    maxlength="18"
                                                    <?php if (!PageVariables::User()->manualMERS) { ?>readonly<?php } ?>
                                                    size="12"
                                                    onblur="validateMERSID(this.id)"
                                                    tabindex="<?php echo $tabIndex++; ?>"/>

                                            <div class="input-group-append">
                                                    <span class="input-group-text" id="getMERSID">
                                                        <a style="text-decoration:none;"
                                                           class="fa fa-refresh"
                                                           onclick="getMERSID('<?php echo cypher::myEncryption($PCID) ?>',
                                                                   '<?php if (!PageVariables::$identifierForMinAutoGeneration) {
                                                               echo cypher::myEncryption(LMRequest::$LMRId);
                                                           } else {
                                                               echo cypher::myEncryption(Strings::showField('loanNumber', 'LMRInfo'));
                                                           } ?>', 'MERSID');"
                                                           title="Click to auto create MERS ID.">
                                                            <i class="tooltipClass flaticon2-reload text-success"></i>
                                                        </a>
                                                    </span>
                                            </div>
                                        </div>
                                        <?php
                                    } else {
                                        echo '<h5>' . LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->MERSID . '</h5>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } ?>

                <?php echo CustomField::RenderForTabSection(
                    PageVariables::$PCID,
                    tblFile::class,
                    LMRequest::$LMRId,
                    'Admin',
                    $fileTab,
                    $activeTab,
                    LMRequest::myFileInfo()->getFileTypes(),
                    LMRequest::myFileInfo()->getLoanPrograms()
                ); ?>
            </div>


        </div>
    </div>
</div>

<!-- Property Management Start -->
<?php
if ($isHOALien == 1 && $publicUser != 1) {
    /** Hide fields **/
    $dispOpt = '';
} else {
    $dispOpt = 'display:none;';
}
//$dispOpt = 'display:flex;';
?>
<div class="row mb-2 " id="propertyManagementInfo"
     style="<?php echo $dispOpt; ?>">
    <div class="col-lg-6">
        <div class="card card-custom ">
            <div class="card-header  bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">Property Management</h3>

                    <input type="hidden" name="propMgmntContactID" id="propMgmntContactID"
                           value="<?php echo $propMgmntContactID ?>"/>
                    <input type="hidden" name="tempPropMgmntContactName" id="tempPropMgmntContactName"
                           value="<?php echo $tempPropMgmntContactName ?>"/>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntContactPerson">Contact Person</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <div class="input-group">

                                    <input type="text" name="propMgmntContactPerson" id="propMgmntContactPerson"
                                           value="<?php echo $propMgmntContactPerson ?>"
                                           class="form-control input-sm"
                                           tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off"
                                           onclick="populateContactName('PM', this.value);"/>
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <i class="fa fa-times text-danger" onclick="removeFileContacts('PM');"></i>
                                        </span>
                                    </div>
                                </div>
                            <?php } else { ?>
                                <h5><?php echo $propMgmntContactPerson ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntCompany">Property Management
                            Company</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="propMgmntCompany" id="propMgmntCompany"
                                       value="<?php echo $propMgmntCompany ?>" class="form-control input-sm"
                                       tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off">
                            <?php } else { ?>
                                <h5><?php echo $propMgmntCompany ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntContactEmail">Email</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="propMgmntContactEmail" id="propMgmntContactEmail"
                                       value="<?php echo $propMgmntContactEmail ?>"
                                       class="form-control input-sm"
                                       tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off">
                            <?php } else { ?>
                                <h5><?php echo $propMgmntContactEmail ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntAddress">Address</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="propMgmntAddress" id="propMgmntAddress"
                                       value="<?php echo $propMgmntAddress ?>" class="form-control input-sm"
                                       tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off">
                            <?php } else { ?>
                                <h5><?php echo $propMgmntAddress ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntCity">City</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="propMgmntCity" id="propMgmntCity"
                                       value="<?php echo $propMgmntCity ?>" class="form-control input-sm"
                                       tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off">
                            <?php } else { ?>
                                <h5><?php echo $propMgmntCity ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntState">State</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <select name="propMgmntState" id="propMgmntState" class="form-control input-sm"
                                        tabindex="<?php echo $tabIndexNo++; ?>">
                                    <option value=""> - Select -</option>
                                    <?php
                                    for ($s = 0;
                                         $s < count($stateArray);
                                         $s++) {
                                        $sOpt = '';
                                        $sOpt = Arrays::isSelected(trim($stateArray[$s]['stateCode']), $propMgmntState);
                                        echo "<option value=\"" . trim($stateArray[$s]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$s]['stateName']) . '</option>';
                                    }
                                    ?>
                                </select>
                            <?php } else { ?>
                                <h5><?php echo $propMgmntState ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntZip">Zip</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="propMgmntZip" id="propMgmntZip"
                                       class="form-control input-sm"
                                       value="<?php echo $propMgmntZip ?>" maxlength="68"
                                       tabindex="<?php echo $tabIndexNo++; ?>" autocomplete="off">
                            <?php } else { ?>
                                <h5><?php echo $propMgmntZip ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-6">
                        <label class="col-md-12 font-weight-bold" for="propMgmntPhNo">Phone</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <input type="text" name="propMgmntPhNo" class="form-control input-sm mask_phone"
                                       id="propMgmntPhNo" value="<?php echo $propMgmntPhNo1 ?>"
                                       tabindex="<?php echo $tabIndexNo++; ?>">
                            <?php } else { ?>
                                <h5><?php echo Strings::formatPhoneNumber($propMgmntPhone); ?></h5>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="form-group col-md-12">
                        <label class="col-md-12 font-weight-bold" for="propMgmntNotes">Notes</label>
                        <div class="col-md-12">
                            <?php if ($allowToEdit) { ?>
                                <textarea name="propMgmntNotes" id="propMgmntNotes" class="form-control "
                                          tabindex="<?php echo $tabIndexNo++; ?>"><?php echo $propMgmntNotes ?></textarea>
                            <?php } else { ?>
                                <h5><?php echo $propMgmntNotes ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div><!-- row close -->
            </div>
        </div>
    </div>
    <!-- HOA has been moved to seprate section with Form Field Controls -->
</div>


<script type="text/javascript">
    $('#updateBrokerStatus').editable({
        prepend: "not selected",
        source: <?php echo json_encode($brokerStatusArray); ?>,
        url: siteSSLUrl + 'backoffice/updateFileBrokerStatus.php',
        response: function (upStatus) {
        }
    });

    $('#internalInfoStatus').editable({
        prepend: "not selected",
        source: <?php echo json_encode($borrowerStatusArray); ?>,
        url: siteSSLUrl + 'backoffice/updateFileBorrowerStatus.php',
        response: function (upStatus) {
        }
    });

    $('#internalInfoCreditLine').editable({
        url: siteSSLUrl + 'backoffice/updateFileBorrowerStatus.php',
        response: function (upStatus) {
        }
    });
</script>
<style>
    .primaryStatus option:disabled {
        color: red !important;
    }
</style>
<!-- fileAdminInfo.php -->
