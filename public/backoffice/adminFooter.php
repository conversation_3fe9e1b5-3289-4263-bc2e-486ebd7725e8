<?php
global $userRole, $userGroup, $LMRId, $borrowerFullName, $fileModuleCodeForWebForm, $allowToSendBorrower;
global $allowToSendAgent, $userName;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\cypher;
use models\lendingwise\tblProcessingCompany;
use models\PageVariables;
use models\standard\Custify;
use models\standard\Strings;

$getCurrentPageFromURL = Strings::getCurrentPageFromURL();
$PCID = PageVariables::$PCID;
$pcInfo = tblProcessingCompany::Get(['PCID' => $PCID]);

if ($userRole != '') {
    $chkHttps = '';
    if (isset($_SERVER['HTTPS'])) {
        $chkHttps = trim($_SERVER['HTTPS']);
    }

    if (!isset($tabNumb)) {
        $tabNumb = '';
    }
    if ($userGroup != 'Client' && $LMRId > 0 && CURRENT_PAGE == 'LMRequest.php') {
        if (isset($LMRId)) { ?>

            <div class="float-right d-none" style="position: fixed; right: 1%; bottom: 6%;  ">
                <ul>
                    <li class="div-text-align-center">
                        <div class="with-children-tip div-text-align-center"
                             style="border-radius: 10px !important;">
                            <a title="This new compose email feature supports multiple recipients in To, Cc, and Bcc for one email. The original compose email,  sends unique email to each recipient"
                               style="text-decoration:none; color: #3399cc !important;"
                               href="<?php echo CONST_URL_POPS; ?>sendFileEmail.php"
                               name="<?php echo htmlentities($borrowerFullName); ?> > Send An Email"
                               id="LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;ft=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) ?>&amp;BO=<?php echo cypher::myEncryption($allowToSendBorrower) ?>&amp;UType=<?php echo cypher::myEncryption($userGroup) ?>&amp;BR=<?php echo cypher::myEncryption($allowToSendAgent) ?>"
                               class="tip-left bs-tooltip-left fa fa-2x  ">

                                <img
                                        id="LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;ft=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) ?>&amp;BO=<?php echo cypher::myEncryption($allowToSendBorrower) ?>&amp;UType=<?php echo cypher::myEncryption($userGroup) ?>&amp;BR=<?php echo cypher::myEncryption($allowToSendAgent) ?>"
                                        src="../assets/images/newemail.png"
                                        style="width: 44px;"
                                        alt="Send Email"/>

                            </a>
                        </div>
                    </li>

                </ul>

            </div>
            <?php
        }
    }
}
require 'googleAnalytics.php';

?>

    <div id="sessionTimeoutWarning"></div>
    <script>

        if (!(typeof hideLoader == 'function')) {
            function hideLoader() {
                try {
                    document.getElementById("divLoader").style.display = "none";
                } catch (e) {
                }
            }
        }
        addLoadEvent(function () {
            setTimeout("hideLoader()", 200);
        });

        function addLoadEvent(func) {
            let oldonload = window.onload;
            if (typeof window.onload != 'function') window.onload = func;
            else {
                window.onload = function () {
                    if (oldonload) {
                        oldonload();
                    }
                    func();
                }
            }
        }

        var timeout = 0, time_ms = '';
        try {
            timeout = time_ms;
        } catch (e) {
        }
        var initialSessionTimeoutMessage = 'Your session has been expired. Please Login!';
        var sessionTimeoutCountdownId = 'sessionTimeoutCountdown';

        function checkMySession() {
            var sessionTimeoutWarningDialog = $("#sessionTimeoutWarning");
            $(sessionTimeoutWarningDialog).html(initialSessionTimeoutMessage);
            $(sessionTimeoutWarningDialog).dialog({
                title: 'Session Expiration Warning',
                autoOpen: false,
                closeOnEscape: false,
                draggable: false,
                width: 460,
                minHeight: 50,
                modal: true,
                buttons: {
                    OK: function () {
                        $(this).dialog('close');
                    }
                },
                resizable: false,
                open: function () {
                    $('body').css('overflow', 'hidden');
                },
                close: function () {
                    window.location.href = "adminLogout.php";
                    $('body').css('overflow', 'auto');
                }
            });
            $(document).bind('idle.idleTimer', function () {
                $(sessionTimeoutWarningDialog).dialog('open');

            });
            $.idleTimer(timeout);
        }

        if (timeout > 0) {
            $(document).ready(function () {
                checkMySession();
            });
        }

        /** Change the select option to gray & black **/

        $(".choice").change(function () {
            if ($(this).val() === "0" || $(this).val() === "") $(this).addClass("empty");
            else $(this).removeClass("empty")
        });
        $(".choice").change();

        /** end **/
    </script>


<?php
if (($userRole == 'Super' || $userGroup == 'Employee' || $userRole == 'Branch' || $userRole == 'Agent') &&
    CONST_ENVIRONMENT != 'staging' && $_SERVER['HTTP_HOST'] == 'app.lendingwise.com') {  //dont show if not logged in ch13184
    //dont show if not logged in ch13184
    $PCIDSKIP = [3198, 3580];  //3198-global, 3580-lendingwise-dave  from ch20087
    if (!in_array($PCID, $PCIDSKIP)) {
        ?>
        <script>
            (function (w, d, s, o, f, js, fjs) {
                w['ReleaseNotesWidget'] = o;
                w[o] = w[o] || function () {
                    (w[o].q = w[o].q || []).push(arguments)
                };
                js = d.createElement(s), fjs = d.getElementsByTagName(s)[0];
                js.id = o;
                js.src = f;
                js.async = 1;
                fjs.parentNode.insertBefore(js, fjs);
            }
            (window, document, 'script', 'rnw', 'https://s3.amazonaws.com/cdn.releasenotes.io/v1/bootstrap.js'));

            rnw('init', {
                account: 'lendingwise.releasenotes.io',
                //selector: '.updates', // changes the selector to apply the badge and link to
                title: 'Latest Updates from lendingwise', // changes the main title
                auto_show_unseen: true // if enabled, the release notes will auto open if the user has unseen releases
            });
        </script>
        <?php
    }
}
?>
<?php
/*This keep alive is depricated.  Look at public/includes/chk_session.php instead

*/
/* if ($userRole == 'Super' || $_SESSION[userNumber] == 8649 && $_SESSION[userGroup] == "Employee") {
    ?>
    <script type="text/javascript" src="../lib/jQuery-keepAlive/jquery.keepAlive.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $.fn.keepAlive({timer: 7200000});
            setTimeout(function () {
                $.fn.keepAlive('stop');
            }, ********);
        });
    </script>
    <?php
} */
?>

    <script>
        // This will initiate Upscope connection. It's important it is added to all pages, even when the user is not logged in.
        (function (w, u, d) {
            var i = function () {
                i.c(arguments)
            };
            i.q = [];
            i.c = function (args) {
                i.q.push(args)
            };
            var l = function () {
                var s = d.createElement('script');
                s.type = 'text/javascript';
                s.async = true;
                s.src = 'https://code.upscope.io/mwGLZiMhqb.js';
                var x = d.getElementsByTagName('script')[0];
                x.parentNode.insertBefore(s, x);
            };
            if (typeof u !== "function") {
                w.Upscope = i;
                l();
            }
        })(window, window.Upscope, document);

        Upscope('init');
    </script>

    <script>
        // If the user is logged in, optionally identify them with the following method.
        // You can call Upscope('updateConnection', {}); at any time.
        Upscope('updateConnection', {
            // Set the user ID below. If you don't have one, set to undefined.
            uniqueId: "undefined",

            // Set the username or email below (e.g. ["John Smith", "<EMAIL>"]).
            identities: ["<?php echo htmlentities(PageVariables::$userFName) . " " . htmlentities(PageVariables::$userLName)?>", "<?php echo htmlentities(PageVariables::$processorAssignedCompany); ?>"]
        });
    </script>


    <script>
        // Prevent Bootstrap dialog from blocking focusin tinymce fix
        $(document).on('focusin', function (e) {
            if ($(e.target).closest(".tox-tinymce, .tox-tinymce-aux, .moxman-window, .tam-assetmanager-root").length) {
                e.stopImmediatePropagation();
            }
        });
    </script>

<?php
if (($_ENV['BUGSNAGJS_ENABLED'] ?? null) == 'true') {
    ?>
    <script src="//d2wy8f7a9ursnm.cloudfront.net/v7/bugsnag.min.js"></script>
    <script>Bugsnag.start('<?php echo $_ENV['BUGSNAGJS_API_KEY'] ?? '' ?>')</script>
    <?php
}

?>

    <script>
        // try {
        //     something.risky()
        // } catch (e) {
        //     Bugsnag.notify(e)
        // }
    </script>


<?php
if (!glCustomJobForProcessingCompany::hideWalkthrough(intval($PCID)) && PageVariables::$userRole && (in_array('HMLO', PageVariables::$myModulesArray) || in_array('loc', PageVariables::$myModulesArray))) {
    $help = $pcInfo->VIPSupport == 1 ? 'VIPhelp' : 'help';
    ?>
    <script>
        (function () {
            let url = new URL(window.location.href);
            let params = new URLSearchParams(url.search);

            // Append your parameter if it doesn't already exist
            if (!params.has('supp')) {
                params.append('supp', '<?php echo $help?>');
                url.search = params.toString();

                // Use history.replaceState to update the URL without refreshing the page
                window.history.replaceState({}, '', url.toString());
            }
        })();

    </script>
    <?php
    if (CONST_ENVIRONMENT == 'production') {
        ?>
        <script type="text/javascript">
            (function (w, d, u) {
                w.$productFruits = w.$productFruits || [];
                w.productFruits = w.productFruits || {};
                w.productFruits.scrV = '2';
                let a = d.getElementsByTagName('head')[0];
                let r = d.createElement('script');
                r.async = 1;
                r.src = u;
                a.appendChild(r);
            })(window, document, 'https://app.productfruits.com/static/script.js');
        </script>
        <script type="text/javascript">
            $productFruits.push(['init', 'cGKfZz5ie3gELThR', 'en', {
                username: '<?php echo htmlentities(PageVariables::$userFName) . " " . htmlentities(PageVariables::$userLName)?>',
                email: '<?php echo PageVariables::$userEmail?>',
                firstname: '<?php echo htmlentities(PageVariables::$userFName)?>',
                role: '<?php echo htmlentities(PageVariables::$userRole)?>',
                signUpAt: '<?php echo $pcInfo->recordDate; ?>',
                props: {
                    pcid: '<?php echo $PCID?>',
                    vipsupp: <?php echo $pcInfo->VIPSupport ?? '0'; ?>,
                    docwiz: <?php echo $pcInfo->docWizard ?? '0'; ?>,
                    marketpl: <?php echo $pcInfo->allowPCToMarketPlace ?? '0'; ?>,
                    automation: <?php echo $pcInfo->allowAutomation ?? '0'; ?>,
                    filetypes: '<?php echo json_encode(PageVariables::$myModulesArray) ?? []; ?>',
                    // myCustomProp3: ['first', 'second'],
                }
            }]);
        </script>

        <script type="text/javascript" id="hs-script-loader" async defer
                src="//js-na1.hs-scripts.com/21536117.js"></script>

        <style> #hubspot-messages-iframe-container iframe {
                right: 100px !important;
            }</style>
        <?php
    }
}
?>
<?php
if (CONST_ENVIRONMENT == 'production' && $PCID && PageVariables::$userNumber && PageVariables::$userEmail && $pcInfo) {
    echo Custify::getCustifyScript($PCID, $pcInfo);
    echo Custify::trackSaves();
}


?>