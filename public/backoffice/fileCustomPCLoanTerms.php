<?php
global $myFileInfo, $PCID;

use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\glRateLockPeriod;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;

$glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;
glHMLOCreditScoreRange::$glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange($PCID);
$HMLOPCTransactionType = [];
$HMLOPCPropertyType = [];
$HMLOPCBasicEntityType = [];
$HMLOPCExtnOption = [];
$HMLOPCLoanTerm = [];
$HMLOPCOccupancy = [];
$HMLOPCState = [];
$HMLOPCNiches = [];
$HMLOPCBasicLoanInfo = [];
$PCBasicLoanTabFileIdExists = [];
$glHMLOExtensionOptionKey = [];
$PCSelectedTransactionType = [];
$PCSelectedExtnOption = [];
$PCSelectedLoanTerm = [];
$PCSelectedOccupancy = [];
$PCBasicLoanTabLMRIDsExists = [];
$fileHMLONewLoanInfo = [];
$HMLOPCAmortizationValPCLoanTermsArray = [];
$HMLOPCAmortizationValPCLoanTerms = [];

$minRate = '';
$maxRate = '';
$originationPointsRate = '';
$originationPointsValue = '';
$processingFee = '';
$brokerPointsRate = '';
$brokerPointsValue = '';
$appraisalFee = '';
$applicationFee = '';
$drawsSetUpFee = '';
$estdTitleClosingFee = '';
$miscellaneousFee = '';
$closingCostFinanced = '';
$PCBorrCreditScoreRange = '';
/**
 * Description: Fetch the Loan Terms Tab Restriction
 * Developer  : Viji & Venkatesh
 * Date         : Aug 21, 2017
 */
$HMLOPCBasicRateLockPeriodInfo = [];
$HMLOPCBasicLoanExitStrategyInfo = [];
if (count($myFileInfo) > 0) {

    if (array_key_exists('HMLOPCTransactionType', $myFileInfo)) $HMLOPCTransactionType = $myFileInfo['HMLOPCTransactionType'];
    if (array_key_exists('HMLOPCPropertyType', $myFileInfo)) $HMLOPCPropertyType = $myFileInfo['HMLOPCPropertyType'];
    if (array_key_exists('HMLOPCBasicEntityType', $myFileInfo)) $HMLOPCBasicEntityType = $myFileInfo['HMLOPCBasicEntityType'];
    if (array_key_exists('HMLOPCExtnOption', $myFileInfo)) $HMLOPCExtnOption = $myFileInfo['HMLOPCExtnOption'];
    if (array_key_exists('HMLOPCLoanTerm', $myFileInfo)) $HMLOPCLoanTerm = $myFileInfo['HMLOPCLoanTerm'];
    if (array_key_exists('HMLOPCOccupancy', $myFileInfo)) $HMLOPCOccupancy = $myFileInfo['HMLOPCOccupancy'];
    if (array_key_exists('HMLOPCState', $myFileInfo)) $HMLOPCState = $myFileInfo['HMLOPCState'];
    if (array_key_exists('HMLOPCNiches', $myFileInfo)) $HMLOPCNiches = $myFileInfo['HMLOPCNiches'];
    if (array_key_exists('HMLOPCBasicLoanInfo', $myFileInfo)) $HMLOPCBasicLoanInfo = $myFileInfo['HMLOPCBasicLoanInfo'];
    if (array_key_exists('PCBasicLoanTabFileIdExists', $myFileInfo)) $PCBasicLoanTabFileIdExists = $myFileInfo['PCBasicLoanTabFileIdExists'];
    if (array_key_exists('fileHMLONewLoanInfo', $myFileInfo)) $fileHMLONewLoanInfo = $myFileInfo['fileHMLONewLoanInfo'];
    if (array_key_exists('HMLOPCAmortizationValInfo', $myFileInfo)) $HMLOPCAmortizationValInfo = $myFileInfo['HMLOPCAmortizationValInfo'];
    if (array_key_exists('HMLOPCBasicMinSeasoningPersonalBankruptcyInfo', $myFileInfo)) $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo = $myFileInfo['HMLOPCBasicMinSeasoningPersonalBankruptcyInfo'];
    if (array_key_exists('HMLOPCBasicMinSeasoningBusinessBankruptcyInfo', $myFileInfo)) $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo = $myFileInfo['HMLOPCBasicMinSeasoningBusinessBankruptcyInfo'];
    if (array_key_exists('HMLOPCBasicMinSeasoningForeclosureInfo', $myFileInfo)) $HMLOPCBasicMinSeasoningForeclosureInfo = $myFileInfo['HMLOPCBasicMinSeasoningForeclosureInfo'];
    if (array_key_exists('HMLOPCBasicSBALoanProductInfo', $myFileInfo)) $HMLOPCBasicSBALoanProductInfo = $myFileInfo['HMLOPCBasicSBALoanProductInfo'];
    if (array_key_exists('HMLOPCBasicEquipmentTypeInfo', $myFileInfo)) $HMLOPCBasicEquipmentTypeInfo = $myFileInfo['HMLOPCBasicEquipmentTypeInfo'];
    if (array_key_exists('HMLOPCBasicEntitityStateFormationInfo', $myFileInfo)) $HMLOPCBasicEntitityStateFormationInfo = $myFileInfo['HMLOPCBasicEntitityStateFormationInfo'];
    if (array_key_exists('HMLOPCBasicPaymentFrequencyInfo', $myFileInfo)) $HMLOPCBasicPaymentFrequencyInfo = $myFileInfo['HMLOPCBasicPaymentFrequencyInfo'];
    if (array_key_exists('HMLOPCBasicLoanPurposeInfo', $myFileInfo)) $HMLOPCBasicLoanPurposeInfo = $myFileInfo['HMLOPCBasicLoanPurposeInfo'];
    if (array_key_exists('HMLOPCBasicMinTimeInBusinessInfo', $myFileInfo)) $HMLOPCBasicMinTimeInBusinessInfo = $myFileInfo['HMLOPCBasicMinTimeInBusinessInfo'];
    if (array_key_exists('HMLOPCBasicRateLockPeriodInfo', $myFileInfo)) $HMLOPCBasicRateLockPeriodInfo = $myFileInfo['HMLOPCBasicRateLockPeriodInfo'];
    if (array_key_exists('HMLOPCBasicLoanExitStrategyInfo', $myFileInfo)) $HMLOPCBasicLoanExitStrategyInfo = $myFileInfo['HMLOPCBasicLoanExitStrategyInfo'];
}

if (count($PCBasicLoanTabFileIdExists) > 0) {
    for ($i = 0; $i < count($PCBasicLoanTabFileIdExists); $i++) {
        $PCBasicLoanTabLMRIDsExists[] = $PCBasicLoanTabFileIdExists[$i]['fileID'];
    }
}

if (count($glHMLOExtensionOption) > 0) {
    $glHMLOExtensionOptionKey = array_keys($glHMLOExtensionOption);
}

for ($k = 0; $k < count($HMLOPCTransactionType); $k++) {
    $PCSelectedTransactionType[] = $HMLOPCTransactionType[$k]['transactionType'];
}

if (count($PCSelectedTransactionType) > 0) {
    $gltypeOfHMLOLoanRequesting = $PCSelectedTransactionType;
    gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting = $gltypeOfHMLOLoanRequesting;
}

for ($k = 0; $k < count($HMLOPCExtnOption); $k++) {
    $PCSelectedExtnOption[] = $HMLOPCExtnOption[$k]['extnOption'];
}

if (count($PCSelectedExtnOption) > 0) {
    $glHMLOExtensionOptionKey = $PCSelectedExtnOption;
}


for ($k = 0; $k < count($HMLOPCLoanTerm); $k++) {
    $PCSelectedLoanTerm[] = $HMLOPCLoanTerm[$k]['loanTerm'];
}

if (count($PCSelectedLoanTerm) > 0) {
    if(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm
        && !in_array(LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm, $PCSelectedLoanTerm)){
        $PCSelectedLoanTerm[] = LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm;
    }
    glHMLOLoanTerms::$glHMLOLoanTerms = $PCSelectedLoanTerm;
}

for ($k = 0; $k < count($HMLOPCOccupancy); $k++) {
    $PCSelectedOccupancy[] = $HMLOPCOccupancy[$k]['occupancy'];
}

if (count($PCSelectedOccupancy) > 0) {
    glHMLOHouseType::$glHMLOHouseType = $PCSelectedOccupancy;
}


for ($k = 0; $k < count($HMLOPCAmortizationValInfo ?? []); $k++) {
    $HMLOPCAmortizationValPCLoanTermsArray[] = $HMLOPCAmortizationValInfo[$k]['AmortizationVal'];
}
if (count($HMLOPCAmortizationValPCLoanTermsArray) > 0) {
    $HMLOPCAmortizationValPCLoanTerms = $HMLOPCAmortizationValPCLoanTermsArray;
}

$HMLOPCBasicRateLockPeriodData = [];
foreach ($HMLOPCBasicRateLockPeriodInfo as $HMLOPCBasicRateLockPeriod) {
    $HMLOPCBasicRateLockPeriodData[] = $HMLOPCBasicRateLockPeriod['rateLockPeriod'];
}
glRateLockPeriod::$glRateLockPeriod = sizeof($HMLOPCBasicRateLockPeriodData) ? $HMLOPCBasicRateLockPeriodData : glRateLockPeriod::$glRateLockPeriod;
glHMLOExitStrategy::$filteredOptions = !empty($HMLOPCBasicLoanExitStrategyInfo) ? array_combine(array_column($HMLOPCBasicLoanExitStrategyInfo, 'exitStrategy'),array_column($HMLOPCBasicLoanExitStrategyInfo, 'exitStrategy')) : glHMLOExitStrategy::getExitStrategy($PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType);


if (count($fileHMLONewLoanInfo) == 0) {
    if (count($HMLOPCBasicLoanInfo) > 0) {
        for ($i = 0; $i < count($HMLOPCBasicLoanInfo); $i++) {
            $minRate = $HMLOPCBasicLoanInfo[$i]['minRate'];
            $maxRate = $HMLOPCBasicLoanInfo[$i]['maxRate'];
            $originationPointsRate = $HMLOPCBasicLoanInfo[$i]['originationPointsRate'];
            $originationPointsValue = $HMLOPCBasicLoanInfo[$i]['originationPointsValue'];
            $processingFee = $HMLOPCBasicLoanInfo[$i]['processingFee'];
            $brokerPointsRate = $HMLOPCBasicLoanInfo[$i]['brokerPointsRate'];
            $brokerPointsValue = $HMLOPCBasicLoanInfo[$i]['brokerPointsValue'];
            $appraisalFee = $HMLOPCBasicLoanInfo[$i]['appraisalFee'];
            $applicationFee = $HMLOPCBasicLoanInfo[$i]['applicationFee'];
            $drawsSetUpFee = $HMLOPCBasicLoanInfo[$i]['drawsSetUpFee'];
            $estdTitleClosingFee = $HMLOPCBasicLoanInfo[$i]['estdTitleClosingFee'];
            $miscellaneousFee = $HMLOPCBasicLoanInfo[$i]['miscellaneousFee'];
            $closingCostFinanced = $HMLOPCBasicLoanInfo[$i]['closingCostFinanced'];
            $PCBorrCreditScoreRange = $HMLOPCBasicLoanInfo[$i]['PCBorrCreditScoreRange'];

            $valuationBPOFee = $HMLOPCBasicLoanInfo[$i]['valuationBPOFee'];
            $valuationAVMFee = $HMLOPCBasicLoanInfo[$i]['valuationAVMFee'];
            $creditReportFee = $HMLOPCBasicLoanInfo[$i]['creditReportFee'];
            $backgroundCheckFee = $HMLOPCBasicLoanInfo[$i]['backgroundCheckFee'];
            $floodCertificateFee = $HMLOPCBasicLoanInfo[$i]['floodCertificateFee'];
            $documentPreparationFee = $HMLOPCBasicLoanInfo[$i]['documentPreparationFee'];
            $wireFee = $HMLOPCBasicLoanInfo[$i]['wireFee'];
            $servicingSetUpFee = $HMLOPCBasicLoanInfo[$i]['servicingSetUpFee'];
            $taxServiceFee = $HMLOPCBasicLoanInfo[$i]['taxServiceFee'];
            $floodServiceFee = $HMLOPCBasicLoanInfo[$i]['floodServiceFee'];
            $inspectionFees = $HMLOPCBasicLoanInfo[$i]['inspectionFees'];
            $projectFeasibility = $HMLOPCBasicLoanInfo[$i]['projectFeasibility'];
            $dueDiligence = $HMLOPCBasicLoanInfo[$i]['dueDiligence'];
            $UccLienSearch = $HMLOPCBasicLoanInfo[$i]['UccLienSearch'];
            $otherFee = $HMLOPCBasicLoanInfo[$i]['otherFee'];
            $taxImpoundsMonth = $HMLOPCBasicLoanInfo[$i]['taxImpoundsMonth'];
            $taxImpoundsMonthAmt = $HMLOPCBasicLoanInfo[$i]['taxImpoundsMonthAmt'];
            $taxImpoundsFee = $HMLOPCBasicLoanInfo[$i]['taxImpoundsFee'];
            $insImpoundsMonthAmt = $HMLOPCBasicLoanInfo[$i]['insImpoundsMonthAmt'];
            $insImpoundsFee = $HMLOPCBasicLoanInfo[$i]['insImpoundsFee'];
            $thirdPartyFees = $HMLOPCBasicLoanInfo[$i]['thirdPartyFees'];
            $insImpoundsMonth = $HMLOPCBasicLoanInfo[$i]['insImpoundsMonth'];
        }
    }
} else {
    if (count($HMLOPCBasicLoanInfo) > 0) {
        for ($i = 0; $i < count($HMLOPCBasicLoanInfo); $i++) {
            $minRate = $HMLOPCBasicLoanInfo[$i]['minRate'];
            $maxRate = $HMLOPCBasicLoanInfo[$i]['maxRate'];
            $PCBorrCreditScoreRange = $HMLOPCBasicLoanInfo[$i]['PCBorrCreditScoreRange'];

        }
    }
}

if ($PCBorrCreditScoreRange) {
    glHMLOCreditScoreRange::$glHMLOCreditScoreRange = explode(',', $PCBorrCreditScoreRange);

    $fileFICOScores = array_unique(array_filter([
        LMRequest::myFileInfo()->fileHMLOInfo()->borCreditScoreRange,
        LMRequest::myFileInfo()->fileHMLOInfo()->coBorCreditScoreRange
    ]));

    if (!empty($fileFICOScores)) {
        $uniqueFICOScores = array_diff($fileFICOScores, glHMLOCreditScoreRange::$glHMLOCreditScoreRange);
        glHMLOCreditScoreRange::$glHMLOCreditScoreRange = array_merge(glHMLOCreditScoreRange::$glHMLOCreditScoreRange, $uniqueFICOScores);
    }
}


/**
 * Get Custom Loan Guidelines From PC..
 */
if (count($HMLOPCPropertyType) > 0) {
    $propertyTypeKeyArray = [];
    for ($pt = 0; $pt < count($HMLOPCPropertyType); $pt++) {
        $propertyTypeKeyArray[] = $HMLOPCPropertyType[$pt]['propertyType'];
    }
    LMRequest::$propertyTypeKeys = $propertyTypeKeyArray;
}
if (count($HMLOPCState) > 0) {
    $stateKeyArray = [];
    for ($pt = 0; $pt < count($HMLOPCState); $pt++) {
        $stateKeyArray[] = $HMLOPCState[$pt]['stateCode'];
    }
    LMRequest::$customLoanGuidelinesStateKeys = $stateKeyArray;
}
/*for ($k = 0; $k < count($HMLOPCBasicMinSeasoningPersonalBankruptcyInfo ?? []); $k++) {
    $HMLOPCBasicMinSeasoningPersonalBankruptcyInfoArray[] = $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo[$k]['MinSeasoningPersonalBankruptcyVal'];
}*/
/*for ($k = 0; $k < count($HMLOPCBasicMinSeasoningBusinessBankruptcyInfo ?? []); $k++) {
    $HMLOPCBasicMinSeasoningBusinessBankruptcyInfoArray[] = $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo[$k]['MinSeasoningBusinessBankruptcyVal'];
}*/
/*for ($k = 0; $k < count($HMLOPCBasicMinSeasoningForeclosureInfo ?? []); $k++) {
    $HMLOPCBasicMinSeasoningForeclosureInfoArray[] = $HMLOPCBasicMinSeasoningForeclosureInfo[$k]['MinSeasoningForeclosureVal'];
}*/
for ($k = 0; $k < count($HMLOPCBasicSBALoanProductInfo ?? []); $k++) {
    $HMLOPCBasicSBALoanProductInfoArray[] = $HMLOPCBasicSBALoanProductInfo[$k]['SBALoanProductVal'];
}
for ($k = 0; $k < count($HMLOPCBasicEquipmentTypeInfo ?? []); $k++) {
    $HMLOPCBasicEquipmentTypeInfoArray[] = $HMLOPCBasicEquipmentTypeInfo[$k]['equipmentTypeVal'];
}
for ($k = 0; $k < count($HMLOPCBasicEntitityStateFormationInfo ?? []); $k++) {
    $HMLOPCBasicEntitityStateFormationInfoArray[] = $HMLOPCBasicEntitityStateFormationInfo[$k]['sateCode'];
}
for ($k = 0; $k < count($HMLOPCBasicPaymentFrequencyInfo ?? []); $k++) {
    $HMLOPCBasicPaymentFrequencyInfoArray[] = $HMLOPCBasicPaymentFrequencyInfo[$k]['paymentFrequencyVal'];
}
for ($k = 0; $k < count($HMLOPCBasicLoanPurposeInfo ?? []); $k++) {
    $HMLOPCBasicLoanPurposeInfoArray[] = $HMLOPCBasicLoanPurposeInfo[$k]['purposeName'];
}
/*for ($k = 0; $k < count($HMLOPCBasicMinTimeInBusinessInfo ?? []); $k++) {
    $HMLOPCBasicMinTimeInBusinessInfoArray[] = $HMLOPCBasicMinTimeInBusinessInfo[$k]['minTimeVal'];
}*/
foreach ($HMLOPCBasicEntityType as $et) {
    $HMLOPCBasicEntityTypeInfoArray[] = $et['entityType'];
}
