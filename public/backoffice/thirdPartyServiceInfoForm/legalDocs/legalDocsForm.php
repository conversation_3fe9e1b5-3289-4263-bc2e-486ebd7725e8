<?php
global $allowToEdit, $thirdPartyServiceCSRArray, $thirdPartyServicesProductsArray, $fileMC;
global $PCID, $ccInfo, $stateArray, $gltypeOfHMLOLoanRequesting, $HMLOPCAmortizationValPCLoanTerms, $amortizationType;
global $HMLOPCBasicEntitityStateFormationInfoArray, $entityStateOfFormation;

use models\composite\oFile\getFileInfo;
use models\constants\accrualTypes;
use models\constants\gl\glBorrowerVestingInfo;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glHMLOAmortization;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\glPCID;
use models\constants\gl\glRateIndex;
use models\constants\gl\glThirdPartyGeraciConstants;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanSetting;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\lendingwise\tblThirdPartyServiceLegalDocs;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

include_once __DIR__ . "/signerForm.php";
include_once __DIR__ . "/guarantorForm.php";

$thirdPartyFileModuleInfo = LMRequest::myFileInfo()->fileModuleInfo();
$LMRId = $thirdPartyFileModuleInfo[0]->fileID;
$fileDetails = getFileInfo::getReport(['LMRId' => $LMRId])[$LMRId];
$fileContacts = $fileDetails['fileContacts'];
$fileHMLONewLoanInfo = $fileDetails['fileHMLONewLoanInfo'];
$fileHMLOPropertyInfo = $fileDetails['fileHMLOPropertyInfo'];

$legalDocsDataRaw = tblThirdPartyServiceLegalDocs::GetAll(['PCID' => $PCID, 'LMRId' => $LMRId]);
$legalDocsDataRaw = end($legalDocsDataRaw) ?? null;
$legalDocsData = json_decode($legalDocsDataRaw->form_data, true);
$legalDocsData = $legalDocsData ?? json_decode(stripslashes($legalDocsDataRaw->form_data), true);
foreach ($legalDocsData as $key => $value) {
    if (!is_array($value) && !is_object($value)) {
        $legalDocsData[$key] = stripslashes($value);
    }
}

$loanSetting = loanSetting::$loanSetting;
if (!$loanSetting['totalTerms']) {
    loanSetting::init($LMRId);
}

$stateArrayGeraci = glThirdPartyGeraciConstants::getStates();
$glEntityTypeArray = glThirdPartyGeraciConstants::getEntityTypes();

$rateIndexArray = array_filter(glRateIndex::$glRateIndex, function($index) {
    return $index !== 'Farmer mac' && $index !== 'Libor';
});

$primaryPropertyInfo = Property::$primaryPropertyInfo;
$realestateapi_id = Property::$primaryPropertyInfo->realestateapi_id;
$fileTab = loanForm::$fileTab;
$secArrLT = BaseHTML::sectionAccess2(['sId' => 'LT', 'opt' => $fileTab]);
$secArrFC = BaseHTML::sectionAccess2(['sId' => 'FC', 'opt' => $fileTab]);

$guarantors = [];
$guarantors = $legalDocsData['guarantors'] ?? [];
if (empty($guarantors)) {
    $guarantorsIndexed = [];
    $keyMapping = [
        'memberName' => 'name',
        'memberTitle' => 'title',
        'memberCategory' => 'type',
        'memberAddress' => 'address'
    ];
    foreach ($fileDetails['fileMemberOfficerInfo'] ?? [] as $item) {
        if (!empty($item['memberName'])) {
            $newItem = $item;
            foreach ($keyMapping as $oldKey => $newKey) {
                if (isset($newItem[$oldKey])) {
                    $newItem[$newKey] = $newItem[$oldKey];
                    unset($newItem[$oldKey]);
                }
            }
            $newItem['signer'] = [];
            $guarantorsIndexed[$item['memberId']] = $newItem;
        }
    }
    foreach ($guarantorsIndexed as $id => &$item) {
        $parentId = $item['parent_id'];
        if ($parentId === null) {
            $guarantors[$id] = &$item;
        } else {
            if (isset($guarantorsIndexed[$parentId])) {
                $guarantorsIndexed[$parentId]['signer'][$id] = &$item;
            }
        }
    }
}

?>

<div class="form-group row col-lg-12 m-0 mb-4 px-0">
    <label class="font-weight-bold bg-secondary py-2 col-lg-12"><b>Geraci Lightning Docs</b></label>
</div>

<!-- Borrower Information -->
<div class="col-md-12 mb-5 borrowerInformationSection">
    <div class="card card-custom borrowerInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Borrower Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="borrowerInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body borrowerInformationSubSectionBody" id="borrowerInformationDiv">
            <input type="hidden" class="legalDocs_fields" name="request_type" id="request_type_legalDocs" value="Save">
            <input type="hidden" class="legalDocs_fields" name="request_for" id="request_for" value="legalDocs">
            <input type="hidden" class="legalDocs_fields" name="notificationEmail" id="notificationEmail" value="<?php echo Strings::showField('borrowerEmail', 'LMRInfo'); ?>"/>

            <div class="row">
                <div class="col-md-6 borrowerEntityOrIndividual_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="borrowerEntityOrIndividual"> Is borrower an Entity or Individual? </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="borrowerEntityOrIndividual" id="borrowerEntityOrIndividual"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'entity', ['borrowerTypeEntityFieldsDiv']);">
                                <option value="entity">Entity</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6 entityName_disp borrowerTypeEntityFieldsDiv">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5" for="entityName"> Entity Name </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="entityName" id="entityName"
                                       placeholder="Entity Name" value="<?= $legalDocsData['entityName'] ?? $fileDetails['fileHMLOEntityInfo']['entityName'] ?>" maxlength="100" size="50" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 entityType_disp borrowerTypeEntityFieldsDiv">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5" for="entityType"> Entity Type </label>
                        <div class="col-md-7">
                            <select name="entityType" class="form-control mandatory legalDocs_fields" id="entityType">
                                <option value=""> - Select -</option>
                                <?php
                                $entityType = $legalDocsData['entityType'] ?? $fileDetails['fileHMLOEntityInfo']['entityType'];
                                if (count($HMLOPCBasicEntityTypeInfoArray ?? []) > 0) {
                                    foreach ($HMLOPCBasicEntityTypeInfoArray as $HMLOPCBasicEntityType) {
                                        $isSelected = $HMLOPCBasicEntityType == $entityType ? 'selected' : '';
                                        ?>
                                        <option value="<?php echo $HMLOPCBasicEntityType; ?>" <?php echo $isSelected; ?>><?php echo $HMLOPCBasicEntityType; ?></option>
                                    <?php   }
                                } else {
                                    $borrowerEntityType = $glEntityTypeArray;
                                    unset($borrowerEntityType[0]);
                                    foreach ($borrowerEntityType as $glEntity) {
                                        $glEntity = trim($glEntity);
                                        $sOpt = Arrays::isSelected($glEntity, $entityType);
                                        echo "<option value=\"" . $glEntity . "\" " . $sOpt . '>' . $glEntity . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 entityStateOfFormation_disp borrowerTypeEntityFieldsDiv">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="entityStateOfFormation"> State of Organization </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="entityStateOfFormation" id="entityStateOfFormation">
                                <option value=''> - Select -</option>
                                <?php
                                $entityStateOfFormation = $legalDocsData['entityStateOfFormation'] ?? $fileDetails['fileHMLOEntityInfo']['entityStateOfFormation'];
                                if (count($HMLOPCBasicEntitityStateFormationInfoArray ?? []) > 0) {
                                    foreach ($stateArrayGeraci as $state) {
                                        if (in_array(trim($state['stateCode']), $HMLOPCBasicEntitityStateFormationInfoArray)) {
                                            $sOpt = Arrays::isSelected(trim($state['stateCode']), $entityStateOfFormation);
                                            echo "<option value=\"" . trim($state['stateCode']) . "\" " . $sOpt . '>' . trim($state['stateName']) . '</option>';
                                        }
                                    }
                                } else {
                                    foreach ($stateArrayGeraci as $state) {
                                        $sOpt = Arrays::isSelected(trim($state['stateCode']), $entityStateOfFormation);
                                        echo "<option value=\"" . trim($state['stateCode']) . "\" " . $sOpt . '>' . trim($state['stateName']) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 entityAddress_disp borrowerTypeEntityFieldsDiv">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="entityAddress"> Address </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="entityAddress" id="entityAddress"
                                       placeholder="Entity Address" value="<?= $legalDocsData['entityAddress'] ?? $fileDetails['fileHMLOEntityInfo']['entityAddress'] ?>" maxlength="100" size="50" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 entityCity_disp borrowerTypeEntityFieldsDiv">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="entityCity"> City </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="entityCity" id="entityCity"
                                       placeholder="Entity City" value="<?= $legalDocsData['entityCity'] ?? $fileDetails['fileHMLOEntityInfo']['entityCity'] ?>" maxlength="45" size="25" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 entityState_disp borrowerTypeEntityFieldsDiv">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="entityState"> State </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="entityState" id="entityState">
                                <option value=''> - Select -</option>
                                <?php
                                foreach ($stateArrayGeraci as $state) {
                                    $sOpt = Arrays::isSelected(trim($state['stateCode']), $legalDocsData['entityState'] ?? $fileDetails['fileHMLOEntityInfo']['entityState']);
                                    echo "<option value=\"" . trim($state['stateCode']) . "\" " . $sOpt . '>' . trim($state['stateName']) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 entityZip_disp borrowerTypeEntityFieldsDiv">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="entityZip"> Zip </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="entityZip" id="entityZip"
                                       placeholder="Entity Zip" value="<?= $legalDocsData['entityZip'] ?? $fileDetails['fileHMLOEntityInfo']['entityZip'] ?>" maxlength="10" size="10" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6 entityEmailOnBehalfOfBorrower_disp borrowerTypeEntityFieldsDiv">
                    <?php $emailOnBehalfOfBorrower = $legalDocsData['emailOnBehalfOfBorrower'] ?? ''; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="emailOnBehalfOfBorrower"> Who should receive mail concerning the loan on behalf of Borrower? </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="emailOnBehalfOfBorrower" id="emailOnBehalfOfBorrower"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'Other', ['entityEmailOnBehalfOfBorrowerName_disp']);">
                                <option value=""> - Select -</option>
                                <option value="Borrower" <?= $emailOnBehalfOfBorrower === "Borrower" ? 'selected' : '' ?> >Borrower</option>
                                <option value="Other" <?= $emailOnBehalfOfBorrower === "Other" ? 'selected' : '' ?> >Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 entityEmailOnBehalfOfBorrowerName_disp borrowerTypeEntityFieldsDiv" <?= $emailOnBehalfOfBorrower === "Other" ? '' : 'style="display: none"' ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="emailOnBehalfOfBorrowerName"> Third Party's Receiver Name </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="emailOnBehalfOfBorrowerName" id="emailOnBehalfOfBorrowerName"
                                       placeholder="Third Party's Name" value="<?= $legalDocsData['emailOnBehalfOfBorrowerName'] ?? ''; ?>" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Borrower Signer Information -->
<div class="col-md-12 mb-5 borrowerSignerInformationSection borrowerTypeEntityFieldsDiv">
    <div class="card card-custom borrowerSignerInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Borrower Signer Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="borrowerSignerInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body borrowerSignerInformationSubSectionBody">
            <div id="signerNestedDiv">
                <?php
                $entitySignerCounter = 1;
                $entitySigners = $legalDocsData['signer'] ?? [];
                if (!empty($entitySigners)) {
                    $entitySignerTotal = count($entitySigners);
                    foreach ($entitySigners as $value) {
                        echo generateSigner($entitySignerCounter, '', $value, $entitySignerCounter !== $entitySignerTotal);
                        $entitySignerCounter++;
                    }
                } else {
                    echo generateSigner();
                }
                ?>
            </div>
            <div class="col-md-12" >
                <span class="btn btn-success m-2 tooltipClass" id="signerNestedDiv_addButton" data-parent_identity=""
                      title="This is currently restricted to just 10 signers."
                      onclick="ThirdPartyServiceInfoFormLegalDocs.addEntity(this, 'signer');">
                    Add Signer&nbsp; <i class="icon-1x fas fa-plus-circle"></i>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Guarantor Information -->
<div class="col-md-12 mb-5 guarantorInformationSection">
    <div class="card card-custom guarantorInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Guarantor Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="guarantorInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body guarantorInformationSubSectionBody">
            <?php $isLoanGuaranty = $legalDocsData['isLoanGuaranty'] ?? ''; ?>
            <div class="col-md-6 isLoanGuaranty_disp">
                <div class="form-group row">
                    <label class="font-weight-bold col-md-5 missing_label " for="isLoanGuaranty"> Will this loan have a guaranty? </label>
                    <div class="col-md-7">
                        <select class="form-control mandatory legalDocs_fields" name="isLoanGuaranty" id="isLoanGuaranty"
                                onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'yes', ['loanGuarantyFieldsDiv']);">
                            <option value="" > - Select - </option>
                            <option value="yes" <?= $isLoanGuaranty === "yes" ? 'selected' : '' ?> >Yes</option>
                            <option value="no" <?= $isLoanGuaranty === "no" ? 'selected' : '' ?> >No</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="loanGuarantyFieldsDiv" id="guarantorNestedDiv" <?= $isLoanGuaranty === "yes" ? '' : 'style="display:none;"' ?> >
                <?php
                $guarantorCounter = 1;
                $guarantorTotal = count($guarantors);
                if (!empty($guarantors)) {
                    foreach ($guarantors as $value) {
                        if (!empty($value['name'])) {
                            echo generateGuarantor($guarantorCounter, '', $value, $guarantorCounter !== $guarantorTotal);
                            $guarantorCounter++;
                        }
                    }
                } else {
                    echo generateGuarantor();
                }
                ?>
            </div>
            <div class="col-md-12 loanGuarantyFieldsDiv" <?= $isLoanGuaranty === "yes" ? '' : 'style="display:none;"' ?> >
                <span class="btn btn-success m-2 tooltipClass" id="guarantorNestedDiv_addButton" data-parent_identity=""
                      title="This is currently restricted to just 10 guarantors."
                        onclick="ThirdPartyServiceInfoFormLegalDocs.addEntity(this, 'guarantor');">
                    Add Guarantor&nbsp; <i class="icon-1x fas fa-plus-circle"></i>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Lender Information -->
<div class="col-md-12 mb-5 lenderInformationSection">
    <div class="card card-custom lenderInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Lender Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="lenderInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body lenderInformationSubSectionBody" id="lenderInformationDiv">
            <?php $lenderDetails = $fileContacts['Lender']; ?>
            <div class="row">
                <input type="hidden" class="legalDocs_fields" name="lenderCID" id="lenderCID" value="<?= $legalDocsData['lenderCID'] ?? $lenderDetails['CID']; ?>" >
                <div class="col-md-6 lenderType_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5" for="lenderType"> Lender Type </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="lenderType" id="lenderType">
                                <option value=""> - Select -</option>
                                <?php
                                foreach ($glEntityTypeArray as $glEntity) {
                                    $glEntity = trim($glEntity);
                                    $sOpt = Arrays::isSelected($glEntity, $legalDocsData['lenderType'] ?? $lenderDetails['entityType']);
                                    echo "<option value=\"" . $glEntity . "\" " . $sOpt . '>' . $glEntity . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderName_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5" for="lenderName"> Lender Name </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="lenderName"
                                       id="lenderName" placeholder="Lender Name" onclick="ThirdPartyServiceInfoFormLegalDocs.searchContact();"
                                       onkeyup="ThirdPartyServiceInfoFormLegalDocs.checkContact();"
                                       value="<?= $legalDocsData['lenderName'] ?? $lenderDetails['contactName'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderStateOfFormation_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="lenderStateOfFormation"> State of Organization </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="lenderStateOfFormation" id="lenderStateOfFormation">
                                <option value=''> - Select -</option>
                                <?php
                                foreach ($stateArrayGeraci as $state) {
                                    $sOpt = Arrays::isSelected($state['stateCode'], $legalDocsData['lenderStateOfFormation'] ?? $lenderDetails['stateOfFormation']);
                                    echo "<option value=\"" . trim($state['stateCode']) . "\" " . $sOpt . ">" . trim($state['stateName']) . "</option>";
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderLoanServicer_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5" for="lenderLoanServicer"> Loan Servicer </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control mandatory legalDocs_fields" name="lenderLoanServicer" id="lenderLoanServicer">
                                    <option value=''> - Select -</option>
                                    <?php
                                    foreach (glThirdPartyGeraciConstants::$loanServicer as $loanServicer) {
                                        $sOpt = Arrays::isSelected($loanServicer, $legalDocsData['lenderLoanServicer'] ?? $lenderDetails['loanServicer']);
                                        echo "<option value=\"" . $loanServicer . "\" " . $sOpt . ">" . $loanServicer . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderNoticeTo_disp">
                    <?php $lenderNoticeTo = $legalDocsData['lenderNoticeTo']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="lenderNoticeTo"> Send notices to
                            Lender or Third Party on behalf of the Lender? </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="lenderNoticeTo" id="lenderNoticeTo" >
                                <option value=""> - Select -</option>
                                <option value="Lender" <?= $lenderNoticeTo === "Lender" ? 'selected' : '' ?> >Lender</option>
                                <option value="Third Party" <?= $lenderNoticeTo === "Third Party" ? 'selected' : '' ?> >Third Party</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderNoticeDeliverTo_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5" for="lenderNoticeDeliverTo"> Notice Deliver To </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="lenderNoticeDeliverTo"
                                       id="lenderNoticeDeliverTo" placeholder="Notice Deliver To" value="<?= $legalDocsData['lenderNoticeDeliverTo'] ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderAddress_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="lenderAddress"> Address </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="lenderAddress"
                                       id="lenderAddress" placeholder="Entity Address"
                                       value="<?= $legalDocsData['lenderAddress'] ?? $lenderDetails['address']; ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderCity_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="lenderCity"> City </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="lenderCity"
                                        id="lenderCity" placeholder="Entity City" value="<?= $legalDocsData['lenderCity'] ?? $lenderDetails['city']; ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderState_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="lenderState"> State </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="lenderState" id="lenderState">
                                <option value=''> - Select -</option>
                                <?php
                                foreach ($stateArrayGeraci as $state) {
                                    $sOpt = Arrays::isSelected(trim($state['stateCode']), $legalDocsData['lenderState'] ?? $lenderDetails['state']);
                                    echo "<option value=\"" . trim($state['stateCode']) . "\" " . $sOpt . '>' . trim($state['stateName']) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lenderZip_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="lenderZip"> Zip </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="lenderZip" id="lenderZip"
                                    placeholder="Entity Zip" value="<?= $legalDocsData['lenderZip'] ?? $lenderDetails['zip']; ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Property Information -->
<div class="col-md-12 mb-5 propertyInformationSection">
    <div class="card card-custom propertyInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Property Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="propertyInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body propertyInformationSubSectionBody" id="propertyInformationDiv">
            <div class="row">
                <div class="col-md-6 subjectPropertyGoverningLawState_disp">
                    <?php $subjectPropertyGoverningLawState = $legalDocsData['subjectPropertyGoverningLawState']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyGoverningLawState"> Governing Law State </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="subjectPropertyGoverningLawState" id="subjectPropertyGoverningLawState"
                                    onchange="Property.populateStateCounty(this, 'subjectPropertyDisputeCounty');" >
                                <option value=''> - Select -</option>
                                <?php
                                    foreach ($stateArrayGeraci as $state) {
                                        $sOpt = Arrays::isSelected($state['stateCode'], $subjectPropertyGoverningLawState);
                                        echo "<option value=\"" . $state['stateCode'] . "\" " . $sOpt . ">" . $state['stateName'] . "</option>";
                                    }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyDisputeCounty_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyDisputeCounty"> County wherein Disputes should be resolved </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control mandatory legalDocs_fields" name="subjectPropertyDisputeCounty" id="subjectPropertyDisputeCounty" >
                                    <option value=''> - Select -</option>
                                    <?php
                                    $stateCountyArray = Arrays::fetchStateCounty(['stateCode' => $subjectPropertyGoverningLawState]);
                                    foreach ($stateCountyArray as $county) {
                                        $sOpt = Arrays::isSelected($county, $legalDocsData['subjectPropertyDisputeCounty']);
                                        echo "<option value=\"" . $county . "\" " . $sOpt . ">" . $county . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyAddress_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyAddress"> Subject Address </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="subjectPropertyAddress" id="subjectPropertyAddress"
                                       placeholder="Subject Address" value="<?= $legalDocsData['subjectPropertyAddress'] ?? $primaryPropertyInfo->propertyAddress; ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyCity_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyCity"> Subject City </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="subjectPropertyCity" id="subjectPropertyCity"
                                       placeholder="Subject City" value="<?= $legalDocsData['subjectPropertyCity'] ?? $primaryPropertyInfo->propertyCity; ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyState_disp">
                    <?php $subjectPropertyState = $legalDocsData['subjectPropertyState'] ?? $primaryPropertyInfo->propertyState; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyState"> Subject State </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="subjectPropertyState" id="subjectPropertyState"
                                    onchange="Property.populateStateCounty(this, 'subjectPropertyCounty');" >
                                <option value=""> - Select -</option>
                                <?php
                                foreach (Property::$states as $stateCode => $stateName) {
                                    if (sizeof(LMRequest::$fileCustomLoanGuidelinePropertyStates) && !in_array($stateCode, LMRequest::$fileCustomLoanGuidelinePropertyStates)) {
                                        continue;
                                    } ?>
                                    <option value="<?php echo $stateCode; ?>"
                                        <?php echo Arrays::isSelected($stateCode, $subjectPropertyState); ?>>
                                        <?php echo $stateName; ?></option>
                                    <?php
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyZip_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyZip"> Subject Zip </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="subjectPropertyZip" id="subjectPropertyZip"
                                       placeholder="Subject Zip" value="<?= $legalDocsData['subjectPropertyZip'] ?? $primaryPropertyInfo->propertyZipCode; ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyCounty_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyCounty"> Subject County </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="subjectPropertyCounty" id="subjectPropertyCounty" >
                                <option value=''> - Select -</option>
                                <?php
                                Property::$propertyCountyInfo = Arrays::fetchStateCounty([
                                    'stateCode' => $subjectPropertyState
                                ]);
                                foreach (Property::$propertyCountyInfo as $eachCountyKey => $eachCountyName) {
                                    echo "<option value=\"" . trim($eachCountyName) . "\" " . Arrays::isSelected($eachCountyName, $legalDocsData['subjectPropertyCounty'] ?? $primaryPropertyInfo->propertyCounty) . '>' . trim($eachCountyName) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyParcelNo_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyParcelNo"> Parcel No </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="subjectPropertyParcelNo" id="subjectPropertyParcelNo"
                                       placeholder="Parcel No" value="<?= $legalDocsData['subjectPropertyParcelNo'] ?? $primaryPropertyInfo->propertyParcelNumber; ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyLienPosition_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyLienPosition"> Lien Position </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="subjectPropertyLienPosition" id="subjectPropertyLienPosition"
                                       placeholder="Lien Position" value="<?= $legalDocsData['subjectPropertyLienPosition'] ?? $fileHMLOPropertyInfo['lienPosition'] ?>" min="1" max="10" step="1" onkeyup="ThirdPartyServiceInfoFormLegalDocs.validateLienPosition(this);" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyBorrowerVesting_disp">
                    <?php $subjectPropertyBorrowerVesting = $legalDocsData['subjectPropertyBorrowerVesting'] ?? $fileDetails['FilePropInfo']['borVestingInfo']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyBorrowerVesting"> Borrower Vesting </label>
                        <div class="col-md-7">
                            <select name="subjectPropertyBorrowerVesting" id="subjectPropertyBorrowerVesting" class="form-control mandatory legalDocs_fields"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'Other', ['subjectPropertyBorrowerVestingOther_disp']);" >
                                <option value=""> - Select -</option>
                                <?php foreach (glBorrowerVestingInfo::$glBorrowerVestingInfo as $info) { ?>
                                    <option value="<?php echo $info; ?>" <?= Arrays::isSelected($info, $subjectPropertyBorrowerVesting) ?> >
                                        <?php echo $info; ?>
                                    </option>
                                <?php } ?>
                                <option value="Other" <?= $subjectPropertyBorrowerVesting === 'Other' ? "selected" : '' ?> > Other </option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 subjectPropertyBorrowerVestingOther_disp" <?= $subjectPropertyBorrowerVesting === "Other" ? '' : 'style="display:none;"'; ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="subjectPropertyBorrowerVestingOther"> Borrower Vesting Other </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="subjectPropertyBorrowerVestingOther" id="subjectPropertyBorrowerVestingOther"
                                       placeholder="Borrower Vesting Other" value="<?= $legalDocsData['subjectPropertyBorrowerVestingOther'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loan Information -->
<div class="col-md-12 mb-5 loanInformationSection">
    <div class="card card-custom loanInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Loan Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="loanInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body loanInformationSubSectionBody" id="loanInformationDiv">
            <div class="row">
                <div class="col-md-6 loanNumber_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label " for="loanNumber"> Loan Number </label>
                        <div class="col-md-7">
                            <?php if (PageVariables::$showStartLoanNumber == 1 && !glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) { ?>
                                <div class="input-group">
                                    <input class="form-control mandatory legalDocs_fields" type="text" name="loanNumber" id="loanNumber" placeholder="Loan Number"
                                        <?php if (trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'LM')) == 'checked' || trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'SS')) == 'checked') { ?> onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                            value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                            maxlength="45" size="25" autocomplete="off" readonly >
                                    <?php if (!Strings::showField('loanNumber', 'LMRInfo')) { ?>
                                        <div class="input-group-append">
                                            <span class="input-group-text" id="getLoanNo">
                                                <a style="text-decoration:none;"
                                                   class="fa fa-refresh"
                                                   onclick="getAvailableLoanNo('<?php echo cypher::myEncryption($PCID) ?>','loanNumber');"
                                                   title="Click to auto create loan number.">
                                                    <i class="tooltipClass flaticon2-reload text-success"></i>
                                                </a>
                                            </span>
                                        </div>
                                    <?php } ?>
                                </div>
                            <?php } else if ($allowToEdit) { ?>
                                <input class="form-control mandatory legalDocs_fields" type="text" name="loanNumber" id="loanNumber" placeholder="Loan Number"
                                    <?php if (($fileTab == 'FA' || $fileTab == 'QA') && (trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'LM')) == 'checked' || trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'SS')) == 'checked')) { ?> onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                        value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                        maxlength="45" size="25" autocomplete="off"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => BaseHTML::sectionAccess2(['sId' => 'Admin', 'opt' => $fileTab]), 'opt' => 'I']);
                                    if(glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)){
                                        echo 'readonly';
                                    }
                                    ?>  >
                            <?php } else { ?>
                                <h5><?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?></h5>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 loanTerm_disp">
                    <div class="row form-group">
                        <label class="font-weight-bold col-md-5" for="loanTerm"> Loan Term </label>
                        <div class="col-md-7">
                            <?php
                            $glHMLOLoanTerms = glHMLOLoanTerms::$glHMLOLoanTerms;
                            $loanTerm = Strings::showField('loanTerm', 'fileHMLOPropertyInfo');
                            $loanTermCRBStatus = '';
                            if (in_array($PCID, [glPCID::PCID_CRB, glPCID::PCID_DEV_DAVE])) {
                                if ($_GET['tabOpt'] == 'HMLI') {
                                    $CRB_totalTerms = $loanSetting['totalTerms'];
                                    $isAdjustable = $loanSetting['isAdjustable'];
                                    if ($isAdjustable != 'Fixed Rate') $loanTermCRBStatus = ' disabled ';
                                }
                                if ($fileTab == 'FA') {
                                    $loanTermCRBStatus = ' disabled ';
                                }
                            }
                                if (trim($loanTermCRBStatus) == 'disabled' && $_GET['tabOpt'] == 'HMLI') { ?>
                                    <input type="hidden" name="loanTermCRB" id="loanTermCRB"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $secArrLT, 'opt' => 'I']); ?>
                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $secArrLT, 'opt' => 'M']); ?>"
                                           value="<?php echo $loanTerm; ?>" >
                                <?php } ?>
                                <select class="form-control" name="loanTerm" id="loanTerm"
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $secArrLT, 'opt' => 'I']); ?>
                                    <?php echo $loanTermCRBStatus; ?> disabled >
                                    <option value=""> - Select -</option>
                                    <?php
                                    foreach ($glHMLOLoanTerms as $eachLoanTerm) {
                                        echo "<option value=\"" . $eachLoanTerm . "\" " . Arrays::isSelected($eachLoanTerm, $loanTerm) . '>' . $eachLoanTerm . '</option>';
                                    }
                                    ?>
                                </select>
                                <input type="hidden" class="legalDocs_fields" name="loanTerm" id="loanTerm" value="<?= $loanTerm ?>" >
                        </div>
                    </div>
                </div>
                <div class="col-md-6 loanAmount_disp">
                    <?php $totalLoanAmount = $fileDetails['ResponseInfo']['totalLoanAmount']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="loanAmount"> Loan Amount </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="text" class="form-control" name="loanAmount" id="loanAmount"
                                       value="<?php echo $totalLoanAmount = Currency::formatDollarAmountWithDecimal($totalLoanAmount) ?? ''; ?>" disabled >
                                <input type="hidden" class="legalDocs_fields" name="loanAmount" id="loanAmount" value="<?= $totalLoanAmount ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 interestRate_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="interestRate"> Interest Rate </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input type="text" class="form-control" name="interestRate" id="interestRate"
                                       placeholder="Interest Rate" value="<?= $interestRate = htmlentities($fileDetails['LMRInfo']['lien1Rate']); ?>" disabled >
                                <input type="hidden" class="legalDocs_fields" name="interestRate" id="interestRate" value="<?= $interestRate ?>" >
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 defaultInterestRate_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="defaultInterestRate"> Default Interest Rate </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="defaultInterestRate"
                                       id="defaultInterestRate" placeholder="Default Interest Rate"
                                       value="<?php echo htmlentities($legalDocsData['defaultInterestRate'] ?? $fileHMLOPropertyInfo['defaultInterestRate']); ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 accrualType_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="accrualType"> Accrual Type </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control" name="accrualType" id="accrualType" disabled >
                                    <?php
                                        $accrualType = $fileHMLOPropertyInfo['accrualType'] ?? accrualTypes::ACCRUAL_TYPE_30_360;
                                        foreach (accrualTypes::$accrualTypes as $k => $v) { ?>
                                            <option value="<?php echo $k; ?>" <?php if ($accrualType == $k) echo ' selected '; ?> >
                                                <?php echo $v; ?>
                                            </option>
                                    <?php } ?>
                                </select>
                                <input type="hidden" class="legalDocs_fields" name="accrualType" id="accrualType" value="<?= $accrualType ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 actualClosingDate_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="actualClosingDate">Actual Closing Date</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fa fa-calendar text-primary"></i></span>
                                </div>
                                <input class="form-control mandatory legalDocs_fields dateNewClass" type="text" name="actualClosingDate"
                                       id="actualClosingDate" value="<?= $legalDocsData['actualClosingDate'] ?? Dates::formatDateWithRE($fileDetails['QAInfo']['closingDate'] ?? '', 'YMD', 'm/d/Y'); ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 initialInterestOnlyPeriod_disp">
                    <?php
                    $initialInterestOnlyPeriod = $legalDocsData['initialInterestOnlyPeriod'];
                    $initialInterestOnlyPeriod_disp = $initialInterestOnlyPeriod === "no" ? 'style="display:none;"' : '';
                    ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="initialInterestOnlyPeriod">
                            Does the loan have an initial interest only period?
                        </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="initialInterestOnlyPeriod" id="initialInterestOnlyPeriod"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'yes', ['initialInterestOnlyPeriodDiv_disp']);" >
                                <option value="yes" <?= $initialInterestOnlyPeriod === "yes" ? 'selected' : '' ?> >Yes</option>
                                <option value="no" <?= $initialInterestOnlyPeriod === "no" ? 'selected' : '' ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6 interestOnlyMonths_disp initialInterestOnlyPeriodDiv_disp" <?= $initialInterestOnlyPeriod_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="interestOnlyMonths">
                            Number of months of interest-only payments
                        </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="interestOnlyMonths" id="interestOnlyMonths"
                                       placeholder="Number of Months" min="1" step="1" value="<?= $legalDocsData['interestOnlyMonths'] ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lien1Terms_disp initialInterestOnlyPeriodDiv_disp" <?= $initialInterestOnlyPeriod_disp ?> >
                    <div class="row form-group">
                        <label class="col-md-5 font-weight-bold" for="lien1Terms"> Amortization Period </label>
                        <div class="col-md-7">
                            <?php
                            $glHMLOAmortization = glHMLOAmortization::getForFile($fileMC, $HMLOPCAmortizationValPCLoanTerms);
                            $lien1Terms = $legalDocsData['lien1Terms'] ?? Strings::showField('lien1Terms', 'LMRInfo');
                            ?>
                            <select class="form-control mandatory legalDocs_fields" name="lien1Terms" id="lien1Terms" >
                                <option value=""> - Select -</option>
                                <?php
                                foreach ($glHMLOAmortization as $i => $amort) {
                                    $sOpt = Arrays::isSelected($amort, $lien1Terms);
                                    echo '<option value="' . $amort . '"' . $sOpt . '>' . $amort . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 variableRateLoan_disp">
                    <?php
                    $variableRateLoan = isset($legalDocsData['variableRateLoan']) ? $legalDocsData['variableRateLoan'] === "yes" : $loanSetting['isAdjustable'] === "Adjustable";
                    $variableRateLoan_disp = $variableRateLoan ? '' : 'style="display:none;"'
                    ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="variableRateLoan"> Variable Rate Loan? </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="variableRateLoan" id="variableRateLoan"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showVariableRateLoan(this);" >
                                <option value="" > - Select - </option>
                                <option value="yes" <?php if ($variableRateLoan) echo "selected" ?> >Yes</option>
                                <option value="no" <?php if (!$variableRateLoan) echo "selected" ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6 variableMarginPercent_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="variableMarginPercent"> Margin Percent to be added to index </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="variableMarginPercent" id="variableMarginPercent"
                                       placeholder="Margin Percent" value="<?= Currency::formatDollarAmountWithDecimal($legalDocsData['variableMarginPercent'] ?? $loanSetting["loanTerms"][0]->RateMargin) ?>" >
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 variableMonthsBeforeFirstChange_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="variableMonthsBeforeFirstChange"> Number of Months
                            Before First Payment Change Date </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="variableMonthsBeforeFirstChange"
                                       id="variableMonthsBeforeFirstChange" placeholder="Number of Months"
                                       value="<?= $legalDocsData['variableMonthsBeforeFirstChange'] ?? $loanSetting["loanTerms"][0]->TermYears ?>" min="1" step="1" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 variableMonthsBetweenChangeDates_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="variableMonthsBetweenChangeDates"> Months Between
                            Change Dates after First Payment Change Date </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="variableMonthsBetweenChangeDates"
                                       id="variableMonthsBetweenChangeDates" placeholder="Months Between Change Dates"
                                       value="<?= $legalDocsData['variableMonthsBetweenChangeDates'] ?? $loanSetting["loanTerms"][1]->TermYears ?? '' ?>" min="1" step="1">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 variableFirstCap_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="row form-group">
                        <label class="font-weight-bold col-md-5 " for="variableFirstCap"> The maximum % increase at the first rate adjustment </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input type="text" name="variableFirstCap" id="variableFirstCap" class="form-control mandatory legalDocs_fields"
                                       value="<?= $legalDocsData['variableFirstCap'] ?>" >
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 variableSubsequentCap_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="row form-group">
                        <label class="font-weight-bold col-md-5 " for="variableSubsequentCap"> The maximum % increase for subsequent rate adjustments </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input type="text" name="variableSubsequentCap" id="variableSubsequentCap" class="form-control mandatory legalDocs_fields"
                                       value="<?= $legalDocsData['variableSubsequentCap'] ?>" >
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 variableFloorRate_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="row form-group">
                        <label class="font-weight-bold col-md-5 " for="variableFloorRate"> Enter the Minimum Interest Rate % </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input type="text" name="variableFloorRate" id="variableFloorRate" class="form-control mandatory legalDocs_fields"
                                       value="<?= Currency::formatDollarAmountWithDecimal($legalDocsData['variableFloorRate'] ?? $loanSetting['floorRate']); ?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 variableCeilingRate_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="row form-group">
                        <label class="font-weight-bold col-md-5 " for="variableCeilingRate"> Enter the Lifetime Maximum
                            Interest Rate % Increase Above the Initial Rate </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input type="text" name="variableCeilingRate" id="variableCeilingRate" class="form-control mandatory legalDocs_fields"
                                       value="<?= $legalDocsData['variableCeilingRate'] ?>" >
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 variableRateIndex_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <div class="row form-group">
                        <label class="font-weight-bold col-md-5 " for="variableRateIndex"> Variable Interest Rate Index </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="variableRateIndex" id="variableRateIndex" >
                                <option value=""> - Select - </option>
                                <?php
                                $rateIndex = $legalDocsData['variableRateIndex'] ?? explode(',', trim(Strings::showField('rateIndex', 'fileHMLOPropertyInfo'), ','))[0];
                                foreach ($rateIndexArray ?? [] as $k) {
                                    $sel = $k === $rateIndex ? ' selected ' : '';
                                    ?>
                                    <option value="<?php echo trim($k); ?>" <?php echo $sel; ?> > <?php echo trim($k) ?> </option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 insertMERSLanguage_disp variableRateLoanDiv_disp" <?= $variableRateLoan_disp ?> >
                    <?php $insertMERSLanguage = $legalDocsData['insertMERSLanguage']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="insertMERSLanguage"> Insert MERS language in
                            Loan Documents </label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="insertMERSLanguage" id="insertMERSLanguage"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'yes', ['mersMINNumber_disp']);" >
                                <option value="yes" <?= $insertMERSLanguage === "yes" ? 'selected' : '' ?> >Yes</option>
                                <option value="no" <?= $insertMERSLanguage === "no" ? 'selected' : '' ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mersMINNumber_disp" <?= $variableRateLoan_disp ?> <?= $insertMERSLanguage === 'no' ? 'style="display:none;"' : '' ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="mersMINNumber"> Enter MERS MIN Number </label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="mersMINNumber" id="mersMINNumber"
                                       placeholder="MERS MIN Number" value="<?= $legalDocsData['mersMINNumber'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loan Features -->
<div class="col-md-12 mb-5 loanFeaturesSection">
    <div class="card card-custom loanFeaturesSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Loan Features</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="loanFeaturesSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body loanFeaturesSubSectionBody" id="loanFeaturesDiv">
            <div class="row">
                <div class="col-md-6 isLenderHoldback_disp">
                    <?php
                    $isLenderHoldback = $legalDocsData['isLenderHoldback'];
                    $isLenderHoldback_disp = $isLenderHoldback === "no" ? 'style="display:none;"' : '';
                    ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="isLenderHoldback">Is Lender reserving
                            funds for construction or rehab?</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="isLenderHoldback" id="isLenderHoldback"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'yes', ['isLenderHoldbackDiv_disp']);" >
                                <option value="yes" <?= $isLenderHoldback === "yes" ? 'selected' : '' ?> >Yes</option>
                                <option value="no" <?= $isLenderHoldback === "no" ? 'selected' : '' ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 rehabCostFinanced_disp isLenderHoldbackDiv_disp" <?= $isLenderHoldback_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="rehabCostFinanced">Amount withheld from Loan Proceeds</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control" type="text" name="rehabCostFinanced" id="rehabCostFinanced"
                                       placeholder="Amount withheld from Loan Proceeds"
                                       value="<?= $rehabCost = Currency::formatDollarAmountWithDecimal($fileDetails['fileHMLOInfo']['rehabCost']); ?>" disabled >
                                <input type="hidden" class="legalDocs_fields" name="rehabCostFinanced" id="rehabCostFinanced" value="<?= $rehabCost ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 isNonDutch_disp isLenderHoldbackDiv_disp" <?= $isLenderHoldback_disp ?> >
                    <?php $nonDutch = $fileHMLONewLoanInfo['isLoanPaymentAmt'] === "TLA" ? "yes" : "no"; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="isNonDutch">Does interest accrue on
                            reserved construction funds only when they are advanced? (Non-Dutch Interest)</label>
                        <div class="col-md-7">
                            <select class="form-control" name="isNonDutch" id="isNonDutch" disabled>
                                <option value="yes" <?= $nonDutch === "yes" ? "selected" : ''; ?> >Yes</option>
                                <option value="no" <?= $nonDutch === "no" ? "selected" : ''; ?> >No</option>
                            </select>
                            <input type="hidden" class="legalDocs_fields" name="isNonDutch" id="isNonDutch" value="<?= $nonDutch ?>" >
                        </div>
                    </div>
                </div>
                <div class="col-md-6 constructionReserveType_disp isLenderHoldbackDiv_disp" <?= $isLenderHoldback_disp ?> >
                    <?php $constructionReserveType = $legalDocsData['constructionReserveType'] ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="constructionReserveType">Type of Construction Reserve</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="constructionReserveType" id="constructionReserveType" >
                                <option value=""> - Select - </option>
                                <option value="Simple" <?= $constructionReserveType === "Simple" ? 'selected' : '' ?> >Simple</option>
                                <option value="Standard" <?= $constructionReserveType === "Standard" ? 'selected' : '' ?> >Standard</option>
                                <option value="Extensive" <?= $constructionReserveType === "Extensive" ? 'selected' : '' ?> >Extensive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 isPrepaymentPremium_disp">
                    <div class="form-group row">
                        <?php $prePaymentPenalty = $legalDocsData['isPrepaymentPremium'] ?? ($fileHMLOPropertyInfo['isTherePrePaymentPenalty'] === "Yes" ? "yes" : "no"); ?>
                        <label class="font-weight-bold col-md-5 missing_label" for="isPrepaymentPremium">Is there a prepayment premium?</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="isPrepaymentPremium" id="isPrepaymentPremium"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showPrepaymentPremium(this);" >
                                <option value=""> - Select - </option>
                                <option value="yes" <?= $prePaymentPenalty === "yes" ? "selected" : ''; ?> >Yes</option>
                                <option value="no" <?= $prePaymentPenalty === "no" ? "selected" : ''; ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6 prepaymentPremiumOptions_disp isPrepaymentPremiumDiv_disp" <?= $prePaymentPenalty === "no" ? 'style="display:none;"' : ''; ?> >
                    <?php $prepaymentPremiumOptions = $legalDocsData['prepaymentPremiumOptions']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="prepaymentPremiumOptions">Prepayment Premium Options</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="prepaymentPremiumOptions" id="prepaymentPremiumOptions"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showPrepaymentAdditionalFields(this);" >
                                <option value=""> - Select - </option>
                                <?php foreach (glThirdPartyGeraciConstants::$prepaymentPremiumOptions as $option) {
                                    $sOpt = Arrays::isSelected($option, $prepaymentPremiumOptions);
                                    echo '<option value="' . $option . '"' . $sOpt . '>' . $option . '</option>';
                                } ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 guaranteedInterestMonths_disp isPrepaymentPremiumDiv_disp" <?= $prepaymentPremiumOptions !== "Guaranteed Interest" ? 'style="display:none;"' : '' ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="guaranteedInterestMonths">Months of Guaranteed Interest</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="guaranteedInterestMonths" id="guaranteedInterestMonths"
                                       placeholder="Months of Guaranteed Interest" value="<?= $legalDocsData['guaranteedInterestMonths'] ?>" min="1" step="1">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 prepayStepYears_disp isPrepaymentPremiumDiv_disp" <?= $prepaymentPremiumOptions !== "Linear Step" ? 'style="display:none;"' : '' ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="prepayStepYears">Prepay Step Years</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="prepayStepYears" id="prepayStepYears"
                                       placeholder="Prepay Step Years" value="<?= $legalDocsData['prepayStepYears'] ?>" min="1" step="1">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 lockoutPeriod_disp isPrepaymentPremiumDiv_disp" <?= $prepaymentPremiumOptions !== "Lockout" ? 'style="display:none;"' : '' ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="lockoutPeriod">Lockout Period</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="lockoutPeriod" id="lockoutPeriod"
                                       placeholder="Lockout Period" value="<?= $legalDocsData['lockoutPeriod'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 includeDebtServiceReserve_disp">
                    <?php $debtServiceReserve = $legalDocsData['includeDebtServiceReserve'] ?? $fileHMLONewLoanInfo['haveInterestreserve']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="includeDebtServiceReserve">Include a Debt Service Reserve (Interest Reserve)?</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="includeDebtServiceReserve" id="includeDebtServiceReserve"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showDebtServiceReserve(this);" >
                                <option value=""> - Select - </option>
                                <option value="None" <?php if (in_array($debtServiceReserve, ['None', 'No'])) echo "selected"; ?> > None </option>
                                <option value="Dollar Amount" <?php if ($debtServiceReserve === 'Dollar Amount') echo "selected"; ?> > Dollar Amount </option>
                                <option value="Monthly Payments" <?php if ($debtServiceReserve === 'Monthly Payments') echo "selected"; ?> > Monthly Payments </option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 specificDollarAmount_disp" <?= $debtServiceReserve === 'Dollar Amount' ? '' : 'style="display:none;"'; ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="specificDollarAmount">Enter Specific Dollar Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="specificDollarAmount"
                                       id="specificDollarAmount" placeholder="Specific Dollar Amount"
                                       value="<?= Currency::formatDollarAmountWithDecimal($legalDocsData['specificDollarAmount'] ?? $fileHMLONewLoanInfo['prepaidInterestReserve']); ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 numMonthsToCalculate_disp" <?= $debtServiceReserve === 'Monthly Payments' ? '' : 'style="display:none;"'; ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="numMonthsToCalculate">Enter Number of Months to Calculate</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="numMonthsToCalculate" id="numMonthsToCalculate" placeholder="Months to Calculate"
                                       value="<?= $legalDocsData['numMonthsToCalculate'] ?? $fileHMLONewLoanInfo['noOfMonthsPrepaid'] ?>" min="1" step="1">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 includeTaxInsuranceImpounds_disp">
                    <?php
                    $includeTaxInsuranceImpounds = $legalDocsData['includeTaxInsuranceImpounds'];
                    $includeTaxInsuranceImpounds_disp = $includeTaxInsuranceImpounds === "no" ? 'style="display:none;"' : '';
                    ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="includeTaxInsuranceImpounds">Are there any
                            tax and/or insurance impounds?</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="includeTaxInsuranceImpounds" id="includeTaxInsuranceImpounds"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'yes', ['includeTaxInsuranceImpoundsDiv_disp']);" >
                                <option value="yes" <?= $includeTaxInsuranceImpounds === "yes" ? 'selected' : '' ?> >Yes</option>
                                <option value="no" <?= $includeTaxInsuranceImpounds === "no" ? 'selected' : '' ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6 initialTaxEscrowAmount_disp includeTaxInsuranceImpoundsDiv_disp" <?= $includeTaxInsuranceImpounds_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="initialTaxEscrowAmount">Initial Tax Escrow Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="initialTaxEscrowAmount"
                                       id="initialTaxEscrowAmount" placeholder="Initial Tax Escrow Amount"
                                       value="<?= Currency::formatDollarAmountWithDecimal($legalDocsData['initialTaxEscrowAmount'] ?? $fileHMLONewLoanInfo['taxImpoundsMonthAmt']); ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 initialInsuranceEscrowAmount_disp includeTaxInsuranceImpoundsDiv_disp" <?= $includeTaxInsuranceImpounds_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="initialInsuranceEscrowAmount">Initial Insurance Escrow Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="initialInsuranceEscrowAmount"
                                       id="initialInsuranceEscrowAmount" placeholder="Initial Insurance Escrow"
                                       value="<?= Currency::formatDollarAmountWithDecimal($legalDocsData['initialInsuranceEscrowAmount'] ?? $fileHMLONewLoanInfo['insImpoundsMonthAmt']); ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 initialFloodInsuranceImpound_disp includeTaxInsuranceImpoundsDiv_disp" <?= $includeTaxInsuranceImpounds_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="initialFloodInsuranceImpound">Initial Flood
                            Insurance Impound Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="initialFloodInsuranceImpound"
                                       id="initialFloodInsuranceImpound" placeholder="Initial Flood Insurance" value="<?= $legalDocsData['initialFloodInsuranceImpound'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 monthlyCapExImpound_disp includeTaxInsuranceImpoundsDiv_disp" <?= $includeTaxInsuranceImpounds_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="monthlyCapExImpound">Monthly CapEx Impound</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="monthlyCapExImpound"
                                       id="monthlyCapExImpound" placeholder="Monthly CapEx Impound" value="<?= $legalDocsData['monthlyCapExImpound'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 monthlyTaxPayment_disp">
                    <?php $monthlyTaxPayment = Currency::formatDollarAmountWithDecimal((float)$fileDetails['incomeInfo']['taxes1'] / 12) ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="monthlyTaxPayment">Monthly Tax Payment Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control" type="text" name="monthlyTaxPayment" id="monthlyTaxPayment"
                                       placeholder="Monthly Tax Payment Amount" value="<?= $monthlyTaxPayment ?>" disabled >
                                <input type="hidden" class="legalDocs_fields" name="monthlyTaxPayment" id="monthlyTaxPayment" value="<?= $monthlyTaxPayment ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 monthlyPropertyInsurance_disp">
                    <?php $monthlyPropertyInsurance = Currency::formatDollarAmountWithDecimal((float)$fileHMLOPropertyInfo['annualPremium'] / 12) ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="monthlyPropertyInsurance">Monthly Property Insurance Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control" type="text" name="monthlyPropertyInsurance" id="monthlyPropertyInsurance"
                                       placeholder="Monthly Property Insurance" value="<?= $monthlyPropertyInsurance ?>" disabled >
                                <input type="hidden" class="legalDocs_fields" name="monthlyPropertyInsurance" id="monthlyPropertyInsurance" value="<?= $monthlyPropertyInsurance ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 monthlyFloodInsurance_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="monthlyFloodInsurance">Monthly Flood Insurance Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="monthlyFloodInsurance" id="monthlyFloodInsurance"
                                       placeholder="Monthly Flood Insurance Amount" value="<?= $legalDocsData['monthlyFloodInsurance'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 conditionalRightExtension_disp">
                    <?php
                    $conditionalRightExtension = $legalDocsData['conditionalRightExtension'];
                    $conditionalRightExtension_disp = $conditionalRightExtension === "no" ? 'style="display:none;"' : '';
                    ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="conditionalRightExtension">Conditional Right to Extension?</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="conditionalRightExtension" id="conditionalRightExtension"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showConditionalRightExtension(this);" >
                                <option value="yes" <?= $conditionalRightExtension === "yes" ? 'selected' : '' ?> >Yes</option>
                                <option value="no" <?= $conditionalRightExtension === "no" ? 'selected' : '' ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"></div>
                <div class="col-md-6 numberOfExtensions_disp conditionalRightExtensionDiv_disp" <?= $conditionalRightExtension_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="numberOfExtensions">Number of Extensions</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="numberOfExtensions" id="numberOfExtensions"
                                       placeholder="Number of Extensions" min="1" step="1" value="<?= $legalDocsData['numberOfExtensions'] ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 monthsPerExtension_disp conditionalRightExtensionDiv_disp" <?= $conditionalRightExtension_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="monthsPerExtension">Number of Months for Each Extension</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="number" name="monthsPerExtension"
                                       id="monthsPerExtension" placeholder="Months for Each Extension" min="1" step="1" value="<?= $legalDocsData['monthsPerExtension'] ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 extensionFeeType_disp conditionalRightExtensionDiv_disp" <?= $conditionalRightExtension_disp ?> >
                    <?php $extensionFeeType = $legalDocsData['extensionFeeType']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="extensionFeeType">Extension Fee Type</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="extensionFeeType" id="extensionFeeType"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showExtensionFeeType(this);" >
                                <option value=""> - Select - </option>
                                <option value="None" <?= $extensionFeeType === "None" ? 'selected' : '' ?> >None</option>
                                <option value="Dollar" <?= $extensionFeeType === "Dollar" ? 'selected' : '' ?> >Dollar</option>
                                <option value="Percent" <?= $extensionFeeType === "Percent" ? 'selected' : '' ?> >Percent</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 amountOfExtensionFee_disp conditionalRightExtensionDiv_disp" <?= $conditionalRightExtension_disp ?> <?= $extensionFeeType !== "Dollar" ? 'style="display:none;"' : '' ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="amountOfExtensionFee">$ Amount of Extension Fee</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="amountOfExtensionFee"
                                       id="amountOfExtensionFee" placeholder="$ Amount of Extension Fee" value="<?= $legalDocsData['amountOfExtensionFee'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 percentOfLoanBalance_disp conditionalRightExtensionDiv_disp" <?= $conditionalRightExtension_disp ?> <?= $extensionFeeType !== "Percent" ? 'style="display:none;"' : '' ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="percentOfLoanBalance">% Percent of Loan Balance</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="percentOfLoanBalance"
                                       id="percentOfLoanBalance" placeholder="% Percent of Loan Balance" value="<?= $legalDocsData['percentOfLoanBalance'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 includeExitFee_disp">
                    <?php
                    $includeExitFee = $legalDocsData['includeExitFee'];
                    $includeExitFee_disp = $includeExitFee === "no" ? 'style="display:none;"' : '';
                    ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="includeExitFee">Include an Exit Fee?</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="includeExitFee" id="includeExitFee"
                                    onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'yes', ['includeExitFeeDiv_disp']);" >
                                <option value="yes" <?= $includeExitFee === "yes" ? 'selected' : '' ?> >Yes</option>
                                <option value="no" <?= $includeExitFee === "no" ? 'selected' : '' ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6 exitFeeAmount_disp includeExitFeeDiv_disp" <?= $includeExitFee_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="exitFeeAmount">Exit Fee Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="exitFeeAmount" id="exitFeeAmount"
                                       placeholder="Exit Fee Amount" value="<?= $legalDocsData['exitFeeAmount'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 includeACHDelivery_disp includeExitFeeDiv_disp" <?= $includeExitFee_disp ?> >
                    <?php $includeACHDelivery = $legalDocsData['includeACHDelivery']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="includeACHDelivery">Include ACH Delivery of Payments</label>
                        <div class="col-md-7">
                            <select class="form-control mandatory legalDocs_fields" name="includeACHDelivery" id="includeACHDelivery">
                                <option value="NA" <?= $includeACHDelivery === "NA" ? 'selected' : '' ?> >NA</option>
                                <option value="yes" <?= $includeACHDelivery === "yes" ? 'selected' : '' ?> >Yes</option>
                                <option value="no" <?= $includeACHDelivery === "no" ? 'selected' : '' ?> >No</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Title Information -->
<div class="col-md-12 mb-5 titleInformationSection">
    <div class="card card-custom titleInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Title Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="titleInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body titleInformationSubSectionBody" id="titleInformationDiv">
            <div class="row">
                <?php $titleRep = $fileContacts['Title Rep'] ?>
                <div class="col-md-6 titleCompanyName_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="titleCompanyName">Title Company Name</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="titleCompanyName" id="titleCompanyName"
                                       placeholder="Title Company Name" value="<?= $legalDocsData['titleCompanyName'] ?? $titleRep['companyName'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 titleOfficerContactName_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="titleOfficerContactName">Title Officer Contact Name</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="titleOfficerContactName"
                                       id="titleOfficerContactName" placeholder="Title Officer Contact"
                                       value="<?= $legalDocsData['titleOfficerContactName'] ?? $titleRep['contactName'] . ' ' . $titleRep['contactLName'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 titleOfficerContactEmail_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="titleOfficerContactEmail">Title Officer Contact Email</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="email" name="titleOfficerContactEmail"
                                       id="titleOfficerContactEmail" placeholder="Title Officer Contact Email"
                                       value="<?= $legalDocsData['titleOfficerContactEmail'] ?? $titleRep['email'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 titleOrderNumber_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="titleOrderNumber">Title Order Number</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="titleOrderNumber"
                                       id="titleOrderNumber" placeholder="Title Order Number"
                                       value="<?= $legalDocsData['titleOrderNumber'] ?? $fileDetails['FilePropInfo']['titleOrderNumber'] ?? ''; ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 titleReportEffectiveDate_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="titleReportEffectiveDate">Title Report Effective Date</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fa fa-calendar text-primary"></i></span>
                                </div>
                                <input class="form-control mandatory legalDocs_fields dateNewClass" type="text" name="titleReportEffectiveDate"
                                       id="titleReportEffectiveDate" value="<?= $legalDocsData['titleReportEffectiveDate'] ?? Dates::formatDateWithRE($fileDetails['FilePropInfo']['titleReportDate'] ?? '', 'YMD', 'm/d/Y'); ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 titleExceptionItems_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="titleExceptionItems">Exception Items to be
                            Deleted by Title</label>
                        <div class="col-md-7">
                            <div class="input-group">
                            <textarea class="form-control mandatory legalDocs_fields" name="titleExceptionItems" id="titleExceptionItems"
                                      placeholder="Enter exception items to be deleted" ><?= $legalDocsData['titleExceptionItems'] ?? ''; ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12"><div class="line-separator"></div></div>
                <div class="col-md-6 includeEscrowInfo_disp">
                    <?php
                    $includeEscrowInfo = $legalDocsData['includeEscrowInfo'];
                    $includeEscrowInfo_disp = $includeEscrowInfo === "no" ? 'style="display:none;"' : '';
                    ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="includeEscrowInfo">Include Separate
                            Escrow/Settlement Agent Information</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control mandatory legalDocs_fields" name="includeEscrowInfo" id="includeEscrowInfo"
                                        onchange="ThirdPartyServiceInfoFormLegalDocs.showHideFields(this, 'yes', ['includeEscrowInfoDiv_disp']);" >
                                    <option value="yes" <?= $includeEscrowInfo === "yes" ? 'selected' : '' ?> >Yes</option>
                                    <option value="no" <?= $includeEscrowInfo === "no" ? 'selected' : '' ?> >No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <?php $escrow = $fileContacts['Escrow']; ?>
                <div class="col-md-6 escrowCompanyName_disp includeEscrowInfoDiv_disp" <?= $includeEscrowInfo_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="escrowCompanyName">Escrow Company Name</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="escrowCompanyName" id="escrowCompanyName"
                                       placeholder="Escrow Company Name" value="<?= $legalDocsData['escrowCompanyName'] ?? $escrow['companyName'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 escrowOfficerContactName_disp includeEscrowInfoDiv_disp" <?= $includeEscrowInfo_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="escrowOfficerContactName">Escrow Officer Contact Name</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="escrowOfficerContactName"
                                       id="escrowOfficerContactName" placeholder="Escrow Officer Contact Name"
                                       value="<?= $legalDocsData['escrowOfficerContactName'] ?? $escrow['contactName'] . ' ' . $escrow['contactLName'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 escrowOfficerContactEmail_disp includeEscrowInfoDiv_disp" <?= $includeEscrowInfo_disp ?> >
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="escrowOfficerContactEmail">Escrow Officer Contact Email</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="email" name="escrowOfficerContactEmail"
                                       id="escrowOfficerContactEmail" placeholder="Escrow Officer Contact Email"
                                       value="<?= $legalDocsData['escrowOfficerContactEmail'] ?? $escrow['email'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settlement Information -->
<div class="col-md-12 mb-5 settleInformationOtherFeesSection">
    <div class="card card-custom settleInformationOtherFeesSubSection">
        <?php loanForm::pushSectionID('FC'); ?>
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Settlement Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="settleInformationOtherFeesSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body settleInformationLenderFeesSubSectionBody" id="settlementInformationOtherDiv">
            <div class="row">
                <div class="col-md-4 brokerFees_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-4 missing_label" for="brokerFees">Broker Fees</label>
                        <div class="col-md-8">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control" type="text" name="brokerFees" id="brokerFees" placeholder="Broker Fees"
                                       value="<?= $brokerFees = Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfo['brokerPointsValue']) ?? ''; ?>" disabled >
                            </div>
                            <input type="hidden" class="legalDocs_fields" name="brokerFees" id="brokerFees" value="<?= $brokerFees ?>" >
                        </div>
                    </div>
                </div>
                <div class="col-md-4 brokerFeesDescription_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="brokerFeesDescription">Broker Description</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="brokerFeesDescription" id="brokerFeesDescription"
                                       placeholder="Enter Lender Description" value="<?= stripslashes($legalDocsData['brokerFeesDescription']) ?: 'Broker Fees' ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 brokerFeesComment_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="brokerFeesComment">Broker Comment</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control legalDocs_fields" name="brokerFeesComment" id="brokerFeesComment">
                                    <option value=''> - Select -</option>
                                    <?php
                                    foreach (glThirdPartyGeraciConstants::$feeDeliveryTypes as $fee) {
                                        $sOpt = Arrays::isSelected($fee, stripslashes($legalDocsData['brokerFeesComment']));
                                        echo "<option value=\"" . $fee . "\" " . $sOpt . ">" . $fee . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 lenderFees_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-4 missing_label" for="lenderFees">Lender Fees</label>
                        <div class="col-md-8">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input class="form-control" type="text" name="lenderFees" id="lenderFees" placeholder="Lender Fees"
                                       value="<?= $lenderFees = Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfo['originationPointsValue']) ?? ''; ?>" disabled >
                                <input type="hidden" class="legalDocs_fields" name="lenderFees" id="lenderFees" value="<?= $lenderFees ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 lenderFeesDescription_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="lenderFeesDescription">Lender Description</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control legalDocs_fields" type="text" name="lenderFeesDescription" id="lenderFeesDescription"
                                       placeholder="Enter Lender Description" value="<?= stripslashes($legalDocsData['lenderFeesDescription']) ?: 'Lender Fees' ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 lenderFeesComment_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="lenderFeesComment">Lender Comment</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control legalDocs_fields" name="lenderFeesComment" id="lenderFeesComment">
                                    <option value=''> - Select -</option>
                                    <?php
                                    foreach (glThirdPartyGeraciConstants::$feeDeliveryTypes as $fee) {
                                        $sOpt = Arrays::isSelected($fee, stripslashes($legalDocsData['lenderFeesComment']));
                                        echo "<option value=\"" . $fee . "\" " . $sOpt . ">" . $fee . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12"><div class="line-separator"></div></div>

                <?php
                foreach (glThirdPartyGeraciConstants::$otherFeesArray as $otherFeeKey => $otherFeeData) {
                    $otherFeeLabel = $otherFeeData['label'];
                    $otherFeeValue = $otherFeeData['value'] ?? $otherFeeKey;
                    $legalDocsOtherFees = $legalDocsData['otherFees'] ?? [];
                    if (trim(BaseHTML::fieldAccess(['fNm' => $otherFeeLabel, 'sArr' => $secArrFC, 'opt' => 'D'])) === "secShow") {
                        $otherFeeLabel = loanForm::label($otherFeeLabel, 'col-md-5 ');
                ?>
                    <div class="row col-md-12" >
                        <div class="col-md-3 <?= $otherFeeKey ?>_disp">
                                <div class="form-group row">
                                    <?= $otherFeeLabel; ?>
                                    <div class="col-md-7">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input class="form-control" type="text" name="<?= $otherFeeKey ?>" id="<?= $otherFeeKey ?>" placeholder="Enter Fees"
                                                   value="<?= $fieldValue = Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfo[$otherFeeValue]) ?>" disabled >
                                            <input type="hidden" class="legalDocs_fields" name="otherFees[<?= $otherFeeKey ?>][fee]" id="<?= $otherFeeKey ?>" value="<?= $fieldValue ?>" >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-3 <?= $otherFeeKey ?>Description_disp">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-4 missing_label" for="<?= $otherFeeKey ?>Description">Description</label>
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <input class="form-control legalDocs_fields" type="text" name="otherFees[<?= $otherFeeKey ?>][description]" id="<?= $otherFeeKey ?>Description"
                                                   placeholder="Enter Description" value="<?= stripslashes($legalDocsOtherFees[$otherFeeKey]['description']) ?: trim(strip_tags($otherFeeLabel)) ?>" >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-3 <?= $otherFeeKey ?>Comment_disp">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-4 missing_label" for="<?= $otherFeeKey ?>Comment">Comment</label>
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <select class="form-control legalDocs_fields" name="otherFees[<?= $otherFeeKey ?>][comment]" id="<?= $otherFeeKey ?>Comment">
                                                <option value=''> - Select -</option>
                                                <?php
                                                foreach (glThirdPartyGeraciConstants::$feeDeliveryTypes as $fee) {
                                                    $sOpt = Arrays::isSelected($fee, stripslashes($legalDocsOtherFees[$otherFeeKey]['comment']));
                                                    echo "<option value=\"" . $fee . "\" " . $sOpt . ">" . $fee . "</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-3 <?= $otherFeeKey ?>PaidTo_disp">
                                <div class="form-group row">
                                    <label class="font-weight-bold col-md-4 missing_label" for="<?= $otherFeeKey ?>PaidTo">Paid To</label>
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <input class="form-control legalDocs_fields" type="text" name="otherFees[<?= $otherFeeKey ?>][paidTo]" id="<?= $otherFeeKey ?>PaidTo"
                                                   placeholder="Enter Paid To" value="<?= stripslashes($legalDocsOtherFees[$otherFeeKey]['paidTo']) ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </div>
                <?php
                    }
                }
                ?>
            </div>
        </div>
    </div>
</div>

<!-- Loan Preparer Information -->
<div class="col-md-12 mb-5 loanPreparerInformationSection">
    <div class="card card-custom loanPreparerInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Loan Preparer Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="loanPreparerInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body loanPreparerInformationSubSectionBody" id="loanPreparerInformationDiv">
            <div class="row">
                <div class="col-md-6 loanPreparerName_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="loanPreparerName">Loan Preparer Name</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="loanPreparerName" id="loanPreparerName"
                                       placeholder="Enter Loan Preparer Name" value="<?= $legalDocsData['loanPreparerName'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 loanPreparerAddressType_disp">
                    <?php $loanPreparerAddressType = $legalDocsData['loanPreparerAddressType']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="loanPreparerAddressType">Address of Preparer</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control mandatory legalDocs_fields" name="loanPreparerAddressType" id="loanPreparerAddressType">
                                    <option value=''> - Select -</option>
                                    <option value='Lender' <?= $loanPreparerAddressType === "Lender" ? 'selected' : '' ?> > Lender </option>
                                    <option value='Broker' <?= $loanPreparerAddressType === "Broker" ? 'selected' : '' ?> > Broker </option>
                                    <option value='Other' <?= $loanPreparerAddressType === "Other" ? 'selected' : '' ?> > Other </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 loanPreparerEmail_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="loanPreparerEmail">Email Address</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="email" name="loanPreparerEmail"
                                       id="loanPreparerEmail" placeholder="Enter Email Address" value="<?= $legalDocsData['loanPreparerEmail'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Closing Contact Information -->
<div class="col-md-12 mb-5 closingContactInformationSection">
    <div class="card card-custom closingContactInformationSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Closing Contact Information</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="closingContactInformationSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body closingContactInformationSubSectionBody" id="closingContactInformationDiv">
            <div class="row">
                <div class="col-md-6 closingContactName_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="closingContactName">Closing Contact Name</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="closingContactName" id="closingContactName"
                                       placeholder="Enter Closing Contact Name" value="<?= $legalDocsData['closingContactName'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 closingContactEmail_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="closingContactEmail">Closing Contact Email</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="email" name="closingContactEmail" id="closingContactEmail"
                                       placeholder="Enter Closing Contact Email" value="<?= $legalDocsData['closingContactEmail'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Documents to Include -->
<div class="col-md-12 mb-5 additionalDocumentsToIncludeSection">
    <div class="card card-custom additionalDocumentsToIncludeSubSection">
        <div class="card-header card-header-tabs-line bg-gray-100 ">
            <div class="card-title">
                <h3 class="card-label">Additional Documents to Include</h3>
            </div>
            <div class="card-toolbar">
                <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                      data-section="additionalDocumentsToIncludeSubSection" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki icon-nm ki-arrow-down"></i>
                </span>
            </div>
        </div>
        <div class="card-body settleInformationLenderFeesSubSectionBody" id="additionalDocumentsToIncludeDiv">
            <div class="row">
                <div class="col-md-6 includeW9_disp">
                    <?php $includeW9 = $legalDocsData['includeW9']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="includeW9">W9</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control mandatory legalDocs_fields" name="includeW9" id="includeW9">
                                    <option value="yes" <?= $includeW9 === "yes" ? 'selected' : '' ?> >Yes</option>
                                    <option value="no" <?= $includeW9 === "no" ? 'selected' : '' ?> >No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 includeFirstPaymentLetter_disp">
                    <?php $includeFirstPaymentLetter = $legalDocsData['includeFirstPaymentLetter']; ?>
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="includeFirstPaymentLetter">First Payment Letter</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <select class="form-control mandatory legalDocs_fields" name="includeFirstPaymentLetter" id="includeFirstPaymentLetter">
                                    <option value="yes" <?= $includeFirstPaymentLetter === "yes" ? 'selected' : '' ?> >Yes</option>
                                    <option value="no" <?= $includeFirstPaymentLetter === "no" ? 'selected' : '' ?> >No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 firstPaymentLetter_disp">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-5 missing_label" for="firstPaymentAmount">First Payment Amount</label>
                        <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control mandatory legalDocs_fields" type="text" name="firstPaymentAmount" id="firstPaymentAmount"
                                       placeholder="Enter First Payment Amount" value="<?= $legalDocsData['firstPaymentAmount'] ?>" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




<div class="form-group row">
    <label class="font-weight-bold col-md-12">&nbsp;</label>
    <div class="col-md-12" style="text-align:center;">
        <?php if ($allowToEdit) { ?>
            <input type="submit" name="btnSave" id="btnSave" class="btn btn-primary btn-sm legalDocs_fields" value="Save">
            <input type="submit" name="psSubmit" id="psSubmit_legalDocs" class="btn btn-primary btn-sm legalDocs_fields" value="Submit">
        <?php } ?>
    </div>
</div>
