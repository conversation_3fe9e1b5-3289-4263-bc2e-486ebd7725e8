class ThirdPartyServiceInfoForm {
    static enableTabData(tab) {
        let typesArray = ['cra', 'legalDocs'];
        typesArray.forEach(element => {
            $('.' + element + '_fields').attr('disabled', true);
        });
        $('.' + tab + '_fields').attr('disabled', false);
    }
}


$(function () {

    $(window).click(function () {
        $('#result_field_info').hide();
    });
    $('#result_field_info, #fieldInfo').click(function (event) {
        event.stopPropagation();
    });


    /**
     * Credit card information
     */
    $('.paymentThrough').click(function () {
        let cra = $('#cra').val();
        if (cra === '') {
            toastrNotification("Please select Credit Reporting Agency first", 'error');
            return false;
        }

        let val = $('input[name="paymentThrough"]:checked').val();
        //show section
        $('.hideThisSec').removeClass('hidden');
        if (val === 'Company Credit Card') {
            $('#cctitle').html('<b>Company Credit card information</b>');
        } else if (val === 'Borrower Credit Card') {
            $('#cctitle').html('<b>Borrower Credit card information</b>');
        } else if (val === 'One-Time Credit Card') {
            $('#cctitle').html('<b>One-Time Credit Card</b>');
        }

        if (val === 'One-Time Credit Card') {
            clear_form_elements('ccInfo_block');
            //CC Details
            $('#creditCardType').attr("style", "pointer-events:'';");
            $('#creditCardNumber_decrypt').attr('readonly', false);
            $('#cardNumberOnBack_decrypt').attr('readonly', false);
            $('#expirationMonth').attr("style", "pointer-events:'';");
            $('#expirationYear').attr("style", "pointer-events:'';");
            $('#cardHolderName').attr('readonly', false);
            //Billing Address
            $('#billingAddress1').attr('readonly', false);
            $('#billingCity').attr('readonly', false);
            $('#billingState').attr("style", "pointer-events:'';");
            $('#billingZip').attr('readonly', false);
            //hide Save btn
            $('#btnSave').hide();
        } else {
            //show Save btn
            $('#btnSave').show();
            $.ajax({
                type: 'POST',
                url: '/backoffice/getCreditCardInformation.php',
                data: jQuery.param({
                    'paymentThrough': val,
                    'FPCID': thirdPartyServiceInfo_PCID,
                    'LMRId': thirdPartyServiceInfo_LMRId,
                    'CRA': cra
                }),
                contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                success: function (myData) {
                    var obj = $.parseJSON(myData);
                    $('#creditCardType').val(obj.creditCardType);
                    $('#creditCardNumber').val(obj.creditCardNumber);
                    $('#expirationMonth').val(obj.expirationMonth);
                    $('#expirationYear').val(obj.expirationYear);
                    $('#cardHolderName').val(obj.cardHolderName);
                    $('#cardNumberOnBack').val(obj.cardNumberOnBack);
                    $('#cardNumberOnBack_decrypt').val(obj.cardNumberOnBack_decrypt);
                    $('#creditCardNumber_decrypt').val(obj.creditCardNumber_decrypt);
                    $('#billingAddress1').val(obj.billingAddress1);
                    $('#billingCity').val(obj.billingCity);
                    $('#billingState').val(obj.billingState);
                    $('#billingZip').val(obj.billingZip);

                    var isCCEmpty = false;
                    if (obj.creditCardType === '' || obj.creditCardNumber === '' || obj.expirationMonth === '' ||
                        obj.expirationYear === '' || obj.cardHolderName === '' || obj.cardNumberOnBack === '') {
                        isCCEmpty = true;
                    }

                    //CCC - readonly
                    if (val === 'Company Credit Card') {
                        //CC Details
                        $('#creditCardType').attr("style", "pointer-events: none;");
                        $('#creditCardNumber_decrypt').attr('readonly', true);
                        $('#cardNumberOnBack_decrypt').attr('readonly', true);
                        $('#expirationMonth').attr("style", "pointer-events: none;");
                        $('#expirationYear').attr("style", "pointer-events: none;");
                        $('#cardHolderName').attr('readonly', true);

                        //Billing Address
                        $('#billingAddress1').attr('readonly', true);
                        $('#billingCity').attr('readonly', true);
                        $('#billingState').attr("style", "pointer-events: none;");
                        $('#billingZip').attr('readonly', true);
                    } else if (val === 'Borrower Credit Card') {
                        //BCC - readonly with data (edit for no data)
                        if (jQuery.isEmptyObject(obj) || isCCEmpty) {
                            //Edit for once
                            //CC Details
                            $('#creditCardType').attr("style", "pointer-events:'';");
                            $('#creditCardNumber_decrypt').attr('readonly', false);
                            $('#cardNumberOnBack_decrypt').attr('readonly', false);
                            $('#expirationMonth').attr("style", "pointer-events:'';");
                            $('#expirationYear').attr("style", "pointer-events:'';");
                            $('#cardHolderName').attr('readonly', false);
                            //Billing Address
                            $('#billingAddress1').attr('readonly', false);
                            $('#billingCity').attr('readonly', false);
                            $('#billingState').attr("style", "pointer-events:'';");
                            $('#billingZip').attr('readonly', false);
                        } else {
                            //CC Details
                            $('#creditCardType').attr("style", "pointer-events: none;");
                            $('#creditCardNumber_decrypt').attr('readonly', true);
                            $('#cardNumberOnBack_decrypt').attr('readonly', true);
                            $('#expirationMonth').attr("style", "pointer-events: none;");
                            $('#expirationYear').attr("style", "pointer-events: none;");
                            $('#cardHolderName').attr('readonly', true);
                            //Billing Address
                            $('#billingAddress1').attr('readonly', true);
                            $('#billingCity').attr('readonly', true);
                            $('#billingState').attr("style", "pointer-events: none;");
                            $('#billingZip').attr('readonly', true);
                        }
                    }
                }
            });
        }
    });

    $('#cra').change(function () {
        console.log({
            func: '#cra.change',
        });
        const allElements = ["#thirdPartyServicesDiv", "#loanNumberDiv", "#sendCCInfoDiv"];
        const xactusElements = ["#thirdPartyServicesDiv", "#loanNumberDiv"];
        allElements.forEach(element => {$(element).hide()});

        let thisVal = $(this).val();
        if (thisVal !== '') {
            if (thisVal === 'xactus') {
                xactusElements.forEach(element => {$(element).show()});
                $(".paymentInfo").hide();
            } else {
                allElements.forEach(element => {$(element).show()});
            }

            let thirdPartyServices = $('#thirdPartyServices');
            thirdPartyServices.empty();
            let option = '<option value="">- Select -</option>';
            if (thisVal !== '') {
                for (const [key, value] of Object.entries(thirdPartyServiceInfo_services[thisVal])) {
                    if (key.includes('separator')) {
                        option += '<option disabled="">' + value + '</option>';
                        continue;
                    }
                    option += '<option value="' + key + '">' + value + '</option>';
                }
            }
            thirdPartyServices.append(option).trigger("chosen:updated");
        }
    });

    $('input[name=sendCCInfo]').on('click', function () {
        if (parseInt(this.value) === 1) {
            $('.paymentInfo').show();
        } else {
            $('.paymentInfo').hide();
            clear_form_elements('ccInfo_block');
        }
    });

    $('#thirdPartyServices').bind('change', function () {
        $('.consumer_credit_fields').hide();
        $('.coborrower_credit_fields').hide();
        $('.business_credit_fields').hide();
        $('.avm_fields').hide();
        $('.flood_fields').hide();
        $('.coborrower_flood_fields').hide();
        $('.mers_fields').hide();
        $('.criminal_record_fields').hide();
        $('.ssn_fields').hide();

        if (['borrowercreditreport', 'borrowersoftpull', 'borrowerfraud', 'socialsecurity'].includes(this.value)) {
            $('.consumer_credit_fields').show();
            if (this.value === 'socialsecurity') {
                $('.ssn_fields').show();
            }
        } else if (['coborrowercreditreport', 'coborrowersoftpull', 'coborrowerfraud'].includes(this.value)) {
            $('.coborrower_credit_fields').show();
        } else if (['jointcreditreport', 'jointsoftpull', 'jointfraud'].includes(this.value)) {
            $('.consumer_credit_fields').show();
            $('.coborrower_credit_fields').show();
        } else if (this.value === 'businesscreditreport') {
            $('.business_credit_fields').show();
        } else if (this.value === 'avm') {
            $('.avm_fields').show();
        } else if (this.value === 'flood') {
            $('.flood_fields').show();
        } else if (this.value === 'coborrowerflood') {
            $('.coborrower_flood_fields').show();
        } else if (this.value === 'jointflood') {
            $('.flood_fields').show();
            $('.coborrower_flood_fields').show();
        } else if (this.value === 'mers') {
            $('.mers_fields').show();
        } else if (this.value === 'criminalrecordreport') {
            $('.criminal_record_fields').show();
        }
    });

    if (thirdPartyServiceInfo_AX) {    // Ticket - https://trello.com/c/crTFKGZa
        $('.mask_CVV').inputmask('****');
        $('.mask_ccNumber').inputmask({"mask": "**** ****** *9999", "placeholder": "____ ______ _____"});
    } else {
        $('.mask_CVV').inputmask('***');
        $('.mask_ccNumber').inputmask({"mask": "**** **** **** 9999", "placeholder": "____ ____ ____ ____"});
    }

    $("#creditCardType").change(function () {
        if ($(this).val() === 'AX') {
            $('.mask_CVV').inputmask('****');
            $('.mask_ccNumber').inputmask({"mask": "**** ****** *9999", "placeholder": "____ ______ _____"});
        } else {
            $('.mask_CVV').inputmask('***');
            $('.mask_ccNumber').inputmask({
                "mask": "**** **** **** 9999",
                "placeholder": "____ ____ ____ ____"
            });
        }
    });

    $('input[type=radio][name=paymentThrough]').change(function () {
        setTimeout(
            function () {
                if ($("#creditCardType").val() === 'AX') {
                    $('.mask_CVV').inputmask('****');
                    $('.mask_ccNumber').inputmask({
                        "mask": "**** ****** *9999",
                        "placeholder": "____ ______ _____"
                    });
                } else {
                    $('.mask_CVV').inputmask('***');
                    $('.mask_ccNumber').inputmask({
                        "mask": "**** **** **** 9999",
                        "placeholder": "____ ____ ____ ____"
                    });
                }
            }, 1000);
    });

    $("#psSubmit, #psSubmit_legalDocs").click(function () {
        if (this.value === 'Submit') {
            $('#request_type').val('Submit');
            $('#request_type_legalDocs').val('Submit');
        } else {
            $('#request_type').val('Save');
            $('#request_type_legalDocs').val('Submit');
        }
    });
});
