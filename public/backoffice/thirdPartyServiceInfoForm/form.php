<?php
global $allowToEdit, $thirdPartyServiceCSRArray, $thirdPartyServicesProductsArray, $fileMC;
global $PCID, $ccInfo, $stateArray, $gltypeOfHMLOLoanRequesting;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glDate;
use models\cypher;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\Controllers\LMRequest\Property;
use models\standard\BaseHTML;
use models\standard\Strings;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Dates;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\PageVariables;
use models\constants\gl\glThirdPartyServicesCRA;

$glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
$thirdPartyFileModuleInfo = LMRequest::myFileInfo()->fileModuleInfo();

$propertyAddress = Property::$primaryPropertyInfo->propertyAddress;
$propertyCity = Property::$primaryPropertyInfo->propertyCity;
$propertyState = Property::$primaryPropertyInfo->propertyState;
$propertyZip = Property::$primaryPropertyInfo->propertyZipCode;
$realestateapi_id = Property::$primaryPropertyInfo->realestateapi_id;

$borrowerType = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->borrowerType ?? '';
if ($borrowerType !== 'Individual') {
    $borrower_flood_fields = '';
    $business_flood_fields = 'flood_fields';
} else {
    $business_flood_fields = '';
    $borrower_flood_fields = 'flood_fields';
}
?>

<div class="form-group row col-lg-12 m-0 mb-4 px-0">
    <label class="font-weight-bold bg-secondary py-2 col-lg-12"><b>Credit Reporting Agencies and Services</b></label>
</div>
<div class="row col-md-12">
    <input type="hidden" class="cra_fields" name="request_type" id="request_type" value="Save">
    <input type="hidden" class="cra_fields" name="borrowerType" id="borrowerType" value="<?= $borrowerType ?>">
    <input type="hidden" class="form-control input-sm cra_fields" name="notificationEmail" id="notificationEmail"
           value="<?php echo Strings::showField('borrowerEmail', 'LMRInfo'); ?>"/>
    <div class="col-md-6">
        <div class="form-group row ">
            <label class="font-weight-bold col-md-5" for="cra">Choose your Agency</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="cra" id="cra" class="form-control input-sm mandatory cra_fields">
                        <option value="">- Select CRA -</option>
                        <?php foreach ($glThirdPartyServicesCRA as $cra => $craValue) {
                            if (in_array($cra, $thirdPartyServiceCSRArray)) {
                                echo "<option value='" . $cra . "'>" . $craValue->Name . '</option>';
                            }
                        }
                        ?>
                    </select>
                <?php } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6" id="thirdPartyServicesDiv" style="display:none;">
        <div class="form-group row">
            <label class="font-weight-bold  col-md-5" for="thirdPartyServices">Services</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="thirdPartyServices" id="thirdPartyServices"
                            class="form-control input-sm mandatory cra_fields">
                        <option value="">- Select -</option>
                    </select>
                <?php } ?>
            </div>
        </div>
    </div>

    <div class="col-md-6 flood_fields coborrower_flood_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="typeOfHMLOLoanRequesting">Transaction Type</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="typeOfHMLOLoanRequesting" id="typeOfHMLOLoanRequesting"
                            class="form-control input-sm mandatory cra_fields">
                        <option value="">- Select -</option>
                        <?php
                        if (sizeof($gltypeOfHMLOLoanRequesting ?? []) == 0) {
                            $gltypeOfHMLOLoanRequesting = gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting;
                        }
                        if (in_array('EF', $fileMC) || in_array('loc', $fileMC)) {
                            $gltypeOfHMLOLoanRequesting[] = 'Equipment Financing';
                        }
                        if (!$gltypeOfHMLOLoanRequesting) {
                            $gltypeOfHMLOLoanRequesting = [];
                        }
                        sort($gltypeOfHMLOLoanRequesting);
                        for ($i = 0; $i < count($gltypeOfHMLOLoanRequesting); $i++) {
                            $sOpt = '';
                            $typeOfLoan = '';
                            $typeOfLoan = trim($gltypeOfHMLOLoanRequesting[$i]);
                            $sOpt = Arrays::isSelected($typeOfLoan, Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo'));
                            echo "<option value=\"" . $typeOfLoan . "\" " . $sOpt . '>' . $typeOfLoan . '</option>';
                        }
                        ?>
                    </select>
                <?php } ?>
            </div>
        </div>
    </div>

    <div class="col-md-6 avm_fields">
        <div class="form-group row">
            <label class="font-weight-bold  col-md-5" for="valuationModel">Valuation Models</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="valuationModel" id="valuationModel" class="form-control input-sm mandatory cra_fields">
                        <option value="">- Select -</option>
                        <option value="PASS">PASS</option>
                        <option value="HVE">Home Value Explorer (HVE)</option>
                        <option value="IVAL">IVAL</option>
                        <option value="GEOAVM">GEOAVM</option>
                    </select>
                <?php } ?>
            </div>
        </div>
    </div>
    <?php if (!glCustomJobForProcessingCompany::hideThirdPartyServiceSendCCInfo($PCID)) { ?>
        <div class="col-md-6" id="sendCCInfoDiv" style="display:none;">
            <div class="form-group row">
                <label class="font-weight-bold  col-md-5" for="sendCCInfo">Do you want to send Credit card info?</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="radio" class="cra_fields" name="sendCCInfo" id="sendCCInfo" value="1">Yes &nbsp;&nbsp;
                        <input type="radio" class="cra_fields" name="sendCCInfo" id="sendCCInfo" value="0"> No
                        <?php echo Strings::tooltip('Select NO, if you have one on file with your CRA or you are billed monthly.'); ?>
                    <?php } ?>
                </div>
            </div>
        </div>
        <div class="col-md-6 paymentInfo" style="display:none;">
            <div class="form-group row">
                <label class="font-weight-bold  col-md-5" for="">Payment Through</label>
                <div class="col-md-4 radio-list">
                    <?php if ($allowToEdit) { ?>
                        <label class="radio radio-outline">
                            <input type="radio" name="paymentThrough" id="paymentThroughC" class="paymentThrough mandatory cra_fields"
                                   value="Company Credit Card">
                            <span></span>
                            Company Credit Card
                        </label>
                        <label class="radio radio-outline">
                            <input type="radio" name="paymentThrough" id="paymentThroughB" class="paymentThrough cra_fields"
                                   value="Borrower Credit Card">
                            <span></span>
                            Borrower Credit Card
                        </label>
                        <label class="radio radio-outline">
                            <input type="radio" name="paymentThrough" id="paymentThroughO" class="paymentThrough cra_fields"
                                   value="One-Time Credit Card">
                            <span></span>
                            One-Time Credit Card
                        </label>

                    <?php } ?>
                </div>
            </div>
        </div>
    <?php } ?>
        <div class=" col-md-6 loanNumber_disp" id="loanNumberDiv" style="display:none;">
            <div class="form-group row">
                <label class="font-weight-bold col-md-5 missing_label " for="loanNumber"> Loan Number </label>
                <div class="col-md-7">
                    <?php $secArr = BaseHTML::sectionAccess2(['sId' => 'Admin', 'opt' => loanForm::$fileTab]); ?>
                    <?php if (PageVariables::$showStartLoanNumber == 1 && !glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) { ?>
                        <div class="input-group">
                            <input class="form-control mandatory cra_fields <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                    type="text" name="loanNumber" id="loanNumber" placeholder="Loan Number"
                                <?php if (trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'LM')) == 'checked' || trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'SS')) == 'checked') { ?> onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                    value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                    maxlength="45"
                                    size="25" autocomplete="off"
                                    readonly>
                            <?php if (!Strings::showField('loanNumber', 'LMRInfo')) { ?>
                                <div class="input-group-append">
                                    <span class="input-group-text" id="getLoanNo">
                                        <a style="text-decoration:none;" class="fa fa-refresh"
                                            onclick="getAvailableLoanNo('<?php echo cypher::myEncryption($PCID) ?>','loanNumber');"
                                            title="Click to auto create loan number.">
                                            <i class="tooltipClass flaticon2-reload text-success"></i>
                                        </a>
                                    </span>
                                </div>
                            <?php } ?>
                        </div>
                    <?php } else if ($allowToEdit) { ?>
                        <input class="form-control input-sm mandatory cra_fields <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                type="text" name="loanNumber" id="loanNumber" placeholder="Loan Number"
                            <?php if ((loanForm::$fileTab == 'FA' || loanForm::$fileTab == 'QA') && (trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'LM')) == 'checked' || trim(Strings::isKeyChecked($thirdPartyFileModuleInfo, 'moduleCode', 'SS')) == 'checked')) { ?> onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                maxlength="45" size="25" autocomplete="off"
                            <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $secArr, 'opt' => 'I']);
                            if(glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)){
                                echo 'readonly';
                            }
                            ?>  >
                    <?php } else { ?>
                        <h5><?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?></h5>
                    <?php } ?>
                </div>
            </div>
        </div>
</div>
<div class="row">
    <div class="col-md-6 creditCardInfo ccInfo_block paymentInfo hideThisSec hidden">
        <!-- Credit card Information Start -->

        <div class="form-group row col-lg-12 m-0 mb-4 px-0">
            <label id="cctitle" class="bg-secondary  py-2  col-lg-12"><b>Company Credit Card Information</b></label>
        </div>
        <div class="form-group row col-md-12 ">
            <label class="font-weight-bold  col-md-5" for="creditCardType">Type of Card</label>
            <div class="col-md-7">
                <select name="creditCardType" id="creditCardType" class="form-control input-sm mandatory cra_fields">
                    <option value=""> - Select Credit Card Type -</option>
                    <?php echo Strings::listCreditCardType(Arrays::getArrayValue('creditCardType', $ccInfo)); ?>
                </select>
            </div>
        </div>

        <div class="form-group row col-md-12 ">
            <label class="font-weight-bold  col-md-5" for="creditCardNumber_decrypt">Card #</label>
            <div class="col-md-5">
                <input type="text" name="creditCardNumber_decrypt" id="creditCardNumber_decrypt"
                       value="<?php echo Strings::ccMasking(Arrays::getArrayValue('creditCardNumber', $ccInfo)); ?>"
                       autocomplete="off" class="form-control input-sm mandatory mask_ccNumber cra_fields"
                       placeholder="Card Number">
                <input type="hidden" class="cra_fields" name="creditCardNumber" id="creditCardNumber"
                       value="<?php echo cypher::myEncryption(Arrays::getArrayValue('creditCardNumber', $ccInfo)); ?>">
            </div>
            <div class="col-md-2">
                <label for="cardNumberOnBack_decrypt" style="display:none;">CCV #</label>
                <input type="text" name="cardNumberOnBack_decrypt" id="cardNumberOnBack_decrypt"
                       value="<?php echo (Arrays::getArrayValue('cardNumberOnBack', $ccInfo) != '') ? 'XXX' : '' ?>"
                       autocomplete="off" class="form-control input-sm mandatory mask_CVV cra_fields" placeholder="CVV">
                <input type="hidden" class="cra_fields" name="cardNumberOnBack" id="cardNumberOnBack"
                       value="<?php echo cypher::myEncryption(Arrays::getArrayValue('cardNumberOnBack', $ccInfo)); ?>">
            </div>
        </div>

        <div class="form-group row col-md-12">
            <label class="font-weight-bold  col-md-5 hidden" for="expirationMonth">Exp Month</label>
            <label class="font-weight-bold  col-md-5 hidden" for="expirationYear">Exp Year</label>
            <label class="font-weight-bold  col-md-5" for="expirationDate">Exp Date</label>
            <div class="col-md-4">
                <select name="expirationMonth" id="expirationMonth" class="form-control input-sm mandatory cra_fields">
                    <option value=""> - Month -</option>
                    <?php echo Strings::listMonths(Arrays::getArrayValue('expirationMonth', $ccInfo)); ?>
                </select>
            </div>
            <div class="col-md-3">
                <select name="expirationYear" id="expirationYear" class="form-control input-sm mandatory cra_fields">
                    <option value=""> - Year -</option>
                    <?php echo Strings::listYears(Arrays::getArrayValue('expirationYear', $ccInfo)); ?>
                </select>
            </div>
        </div>

        <div class="form-group row col-md-12">
            <label class="font-weight-bold  col-md-5" for="cardHolderName">Card Holders Name on Card</label>
            <div class="col-md-7">
                <input type="text" name="cardHolderName" id="cardHolderName" autocomplete="off"
                       class="form-control input-sm mandatory cra_fields" placeholder="Card Holder Name"
                       value="<?php echo htmlentities(Arrays::getArrayValue('cardHolderName', $ccInfo)); ?>">
            </div>
        </div>
        <!-- Credit card info end -->
    </div>
    <div class="col-md-6 creditCardInfo ccInfo_block paymentInfo billingData hideThisSec hidden">

        <div class="form-group  col-lg-12 m-0 mb-4 px-0">
            <label class="font-weight-bold  bg-secondary  py-2  col-lg-12"><b>Billing Address</b></label>
        </div>

        <div class="form-group row col-md-12">
            <script>
                $(document).ready(function() {
                    $('#billingAddress1').on('input', function() {
                        address_lookup.InitLegacy($(this));
                    });
                });
            </script>
            <label class="font-weight-bold  col-md-5" for="billingAddress1">Address Line 1</label>
            <div class="col-md-7">
                <input type="text" name="billingAddress1" id="billingAddress1"
                       data-address="billingAddress1"
                       data-city="billingCity"
                       data-state="billingState"
                       data-zip="billingZip"
                       value="<?php echo htmlentities(Arrays::getArrayValue('billingAddress1', $ccInfo)); ?>"
                       autocomplete="off"
                       class="form-control input-sm mandatory cra_fields" placeholder="Address Line 1">
            </div>
        </div>

        <div class="form-group row col-md-12">
            <label class="font-weight-bold  col-md-5" for="billingCity">City</label>
            <div class="col-md-7">
                <input type="text" name="billingCity" id="billingCity"
                       value="<?php echo htmlentities(Arrays::getArrayValue('billingCity', $ccInfo)); ?>"
                       autocomplete="off" class="form-control input-sm mandatory cra_fields" placeholder="City">
            </div>
        </div>

        <div class="form-group row col-md-12">
            <label class="font-weight-bold  col-md-5" for="billingState">State</label>
            <div class="col-md-7">
                <select class="form-control input-sm mandatory cra_fields" name="billingState" id="billingState"
                        class="form-control input-sm">
                    <option value=""> - Select -</option>
                    <?php foreach ($stateArray as $s => $state) {
                        $sOpt = '';
                        if (Arrays::getArrayValue('billingState', $ccInfo) == $state['stateCode']) $sOpt = 'Selected';
                        echo '<option value="' . trim($state['stateCode']) . '" ' . $sOpt . '>' . $state['stateName'] . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="form-group row col-md-12">
            <label class="font-weight-bold  col-md-5" for="billingZip">Zip</label>
            <div class="col-md-7">
                <input type="text" name="billingZip" id="billingZip"
                       value="<?php echo Arrays::getArrayValue('billingZip', $ccInfo); ?>" autocomplete="off"
                       class="form-control input-sm zipCode mandatory cra_fields" placeholder="Zip">
            </div>
        </div>
    </div><!-- Billing Address End -->
</div>

<!-- Property Information -->
<div class="avm_fields flood_fields coborrower_flood_fields">
    <div class="row">
        <div class="form-group row col-lg-12 m-0 mb-4 px-0">
            <label class="font-weight-bold  bg-secondary  py-2  col-lg-12"><b>Property information</b></label>
        </div>
    </div>
    <div class="row col-md-12">
        <div class="col-md-6">
            <div class="form-group row">
                <script>
                    $(document).ready(function() {
                        $('#propertyAddress').on('input', function() {
                            address_lookup.InitLegacy($(this));
                        });
                    });
                </script>
                <label class="font-weight-bold  col-md-5" for="propertyAddress">Address</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="hidden" class="cra_fields" id="propertyAPIId"
                               name="realestateapi_id"
                               value="<?php echo $realestateapi_id;?>">
                        <input type="text" name="propertyAddress" id="propertyAddress"
                               data-address="propertyAddress"
                               data-city="propertyCity"
                               data-state="propertyState>"
                               data-zip="propertyZip"
                               data-api_id="propertyAPIId"
                               class="form-control input-sm mandatory cra_fields"
                               value="<?php echo $propertyAddress; ?>"
                               autocomplete="off"
                               placeholder="Address">
                    <?php } else {
                        echo '<h5>' . $propertyAddress . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group row">

                <label class="font-weight-bold  col-md-5" for="propertyCity">City</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="propertyCity" id="propertyCity"
                               class="form-control input-sm mandatory cra_fields"
                               value="<?php echo $propertyCity; ?>" autocomplete="off"
                               placeholder="City">
                    <?php } else {
                        echo '<h5>' . $propertyCity . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class=" col-md-6">
            <div class="form-group row">
                <label class="font-weight-bold  col-md-5" for="propertyState">State</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select name="propertyState" id="propertyState" class="form-control input-sm mandatory cra_fields">
                            <option value=''> - Select -</option>
                            <?php
                            for ($j = 0; $j < count($stateArray); $j++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $propertyState);
                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else {
                        echo '<h5>' . Strings::showField('propertyState', 'LMRInfo') . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group row">
                <label class="font-weight-bold  col-md-5" for="propertyZip">Zip</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input type="text" name="propertyZip" id="propertyZip"
                               class="form-control zipCode input-sm mandatory cra_fields"
                               value="<?php echo $propertyZip; ?>" placeholder="Zip"
                               autocomplete="off">
                    <?php } else {
                        echo '<h5>' . $propertyZip . '</h5>';
                    } ?>
                </div>
            </div>
        </div>

        <div class=" col-md-6 avm_fields">
            <div class="form-group row">
                <label class="font-weight-bold  col-md-5" for="homeValue">Property value(As-is)</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text" name="homeValue" class="form-control input-sm mandatory cra_fields"
                                   id="homeValue"
                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('homeValue', 'LMRInfo')) ?>"
                                   placeholder="0.00">
                        </div>
                    <?php } else {
                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(Strings::showField('homeValue', 'LMRInfo')) . '</b>';
                    } ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Property Information End -->

<!-- Business Entity Start -->
<div class="row ">
    <div class="business_credit_fields <?= $business_flood_fields ?> col-md-12">
        <div class="form-group row col-lg-12 m-0 mb-4 px-0">
            <label class="font-weight-bold  bg-secondary  py-2  col-lg-12"><b>Business Entity</b></label>
        </div>
    </div>
</div>

<div class="row col-md-12">
    <div class=" col-md-6 business_credit_fields <?= $business_flood_fields ?>" >
        <div class="form-group row">
            <label class="font-weight-bold  col-md-5" for="entityName">Entity Name</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="entityName" id="entityName"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('entityName', 'fileHMLOEntityInfo')); ?>"
                           autocomplete="off" placeholder="Entity Name">
                <?php } else {
                    echo '<h5>' . Strings::showField('entityName', 'fileHMLOEntityInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>

    <div class=" col-md-6 business_credit_fields">
        <div class="form-group row">
            <script>
                $(document).ready(function() {
                    $('#entityAddress').on('input', function() {
                        address_lookup.InitLegacy($(this));
                    });
                });
            </script>
            <label class="font-weight-bold  col-md-5" for="entityAddress">Address</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="entityAddress" id="entityAddress"
                           data-address="entityAddress"
                           data-city="entityCity"
                           data-state="entityState"
                           data-zip="entityZip"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('entityAddress', 'fileHMLOEntityInfo')); ?>"
                           autocomplete="off" placeholder="Address">
                <?php } else {
                    echo '<h5>' . Strings::showField('entityAddress', 'fileHMLOEntityInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>

    <div class=" col-md-6 business_credit_fields">
        <div class="form-group row">

            <label class="font-weight-bold  col-md-5" for="entityCity">City</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="entityCity" id="entityCity"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('entityCity', 'fileHMLOEntityInfo')); ?>"
                           autocomplete="off" placeholder="City">
                <?php } else {
                    echo '<h5>' . Strings::showField('entityCity', 'fileHMLOEntityInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>

    <div class="col-md-6 business_credit_fields">
        <div class="form-group row">

            <label class="font-weight-bold  col-md-5" for="entityState">State</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="entityState" id="entityState" class="form-control input-sm mandatory cra_fields">
                        <option value=''> - Select -</option>
                        <?php
                        for ($j = 0; $j < count($stateArray); $j++) {
                            $sOpt = '';
                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), Strings::showField('entityState', 'fileHMLOEntityInfo'));
                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                        }
                        ?>
                    </select>
                <?php } else {
                    echo '<h5>' . Strings::showField('entityState', 'fileHMLOEntityInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class=" col-md-6 business_credit_fields">
        <div class="form-group row">

            <label class="font-weight-bold  col-md-5" for="entityZip">Zip</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="entityZip" id="entityZip"
                           class="form-control mandatory zipCode input-sm cra_fields"
                           value="<?php echo htmlentities(Strings::showField('entityZip', 'fileHMLOEntityInfo')); ?>"
                           autocomplete="off" placeholder="Zip">
                <?php } else {
                    echo '<h5>' . Strings::showField('entityZip', 'fileHMLOEntityInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
</div>
<!-- Business Entity End -->

<!-- Borrower Information Start -->
<div class="row">
    <div class="form-group row col-lg-12 m-0 mb-4 px-0 consumer_credit_fields criminal_record_fields <?= $borrower_flood_fields ?> mers_fields avm_fields">
        <label class="font-weight-bold  bg-secondary  py-2  col-lg-12"><b>Borrower Information</b></label>
    </div>
</div>
<div class="row col-md-12">
    <div class="col-md-6 consumer_credit_fields criminal_record_fields <?= $borrower_flood_fields ?> mers_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="borrowerName">First Name</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="borrowerName" id="borrowerName"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo Strings::showField('borrowerFName', 'LMRInfo'); ?>"
                           placeholder="First Name"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('borrowerFName', 'LMRInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 consumer_credit_fields criminal_record_fields <?= $borrower_flood_fields ?> mers_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="borrowerLName">Last Name</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="borrowerLName" id="borrowerLName"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo Strings::showField('borrowerLName', 'LMRInfo'); ?>"
                           placeholder="Last Name"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('borrowerLName', 'LMRInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 consumer_credit_fields criminal_record_fields mers_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="ssnNumber">Social Security Number</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="ssnNumber" id="ssnNumber"
                           class="form-control input-sm mask_ssn mandatory cra_fields"
                           value="<?php echo Strings::showField('ssnNumber', 'LMRInfo'); ?>"
                           placeholder="___ - __ - ____"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('ssnNumber', 'LMRInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 consumer_credit_fields criminal_record_fields">
        <div class="form-group  row ">
            <label class="font-weight-bold  col-md-5" for="borrowerDOB">Date Of Birth</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <div class="input-group dobDates">
                        <div class="input-group-prepend borrowerDOB">
                                                <span class="input-group-text">
                                                     <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                        </div>
                        <input class="form-control input-sm mandatory dateNewClass cra_fields" id="borrowerDOB"
                               placeholder="MM/DD/YYYY"
                               type="text" name="borrowerDOB"
                               data-date-dob-start-date="<?php echo glDate::getMinRequirementDate(); ?>"
                               data-date-dob-end-date="<?php echo glDate::getMaxRequirementDate(); ?>"
                               value="<?php echo Dates::formatDateWithRE(Strings::showField('borrowerDOB', 'LMRInfo'), 'YMD', 'm/d/Y'); ?>"
                               autocomplete="off"/>
                    </div>
                <?php } else {
                    echo '<h5>' . Dates::formatDateWithRE(Strings::showField('borrowerDOB', 'LMRInfo'), 'YMD', 'm/d/Y') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 avm_fields">
        <div class="form-group  row ">
            <label class="font-weight-bold  col-md-5" for="phoneNumber">Phone Number</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="phoneNumber" id="phoneNumber"
                           class="form-control input-sm mask_home_phone mandatory cra_fields"
                           value="<?php echo Strings::showField('phoneNumber', 'LMRInfo'); ?>" autocomplete="off"
                           placeholder="xxx-xxx-xxxx">
                <?php } else {
                    echo '<h5>' . Strings::showField('phoneNumber', 'LMRInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>

    <div class="col-md-6 consumer_credit_fields criminal_record_fields">
        <div class="form-group row  ">
            <script>
                $(document).ready(function() {
                    $('#presentAddress').on('input', function() {
                        address_lookup.InitLegacy($(this));
                    });
                });
            </script>
            <label class="font-weight-bold  col-md-5" for="presentAddress">Address</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="presentAddress" id="presentAddress"
                           data-address="presentAddress"
                           data-city="presentCity"
                           data-state="presentState"
                           data-zip="presentZip"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo Strings::showField('presentAddress', 'file2Info'); ?>"
                           placeholder="Address"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('presentAddress', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6  consumer_credit_fields criminal_record_fields">
        <div class="form-group row">
            <label class="font-weight-bold  col-md-5" for="presentCity">City</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="presentCity" id="presentCity"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo Strings::showField('presentCity', 'file2Info'); ?>" placeholder="City"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('presentCity', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 consumer_credit_fields criminal_record_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="presentState">State</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="presentState" id="presentState" class="form-control input-sm mandatory cra_fields">
                        <option value=''> - Select -</option>
                        <?php
                        for ($j = 0; $j < count($stateArray); $j++) {
                            $sOpt = '';
                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), Strings::showField('presentState', 'file2Info'));
                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                        }
                        ?>
                    </select>
                <?php } else {
                    echo '<h5>' . Strings::showField('presentState', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 consumer_credit_fields criminal_record_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="presentZip">Zip</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="presentZip" id="presentZip"
                           class="form-control zipCode input-sm mandatory cra_fields"
                           value="<?php echo Strings::showField('presentZip', 'file2Info'); ?>" placeholder="Zip"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('presentZip', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 ssn_fields">
        <div class="form-group row ">
            <label class="font-weight-bold col-md-5" for="ssa89">Form SSA-89</label>
            <div class="col-md-7">
                <input type="file" name="ssa89" id="ssa89" class="form-control input-sm mandatory cra_fields"
                       accept=".pdf,application/pdf" >
                <span><i style="color: cornflowerblue; font-size: small;">(Only PDF files are allowed.)</i></span>
            </div>
        </div>
    </div>
</div>
<!-- Borrower Information End -->


<!-- Co-Borrower Information Start -->
<div class="row">
    <div class="col-md-12 coborrower_credit_fields coborrower_flood_fields">
        <div class="form-group row col-lg-12 m-0 mb-4 px-0">
            <label class="font-weight-bold  bg-secondary  py-2  col-lg-12"><b>Co-Borrower Information</b></label>
        </div>
    </div>
</div>

<div class="row col-md-12">
    <div class="col-md-6 coborrower_credit_fields coborrower_flood_fields">
        <div class="form-group row">
            <label class="font-weight-bold  col-md-5" for="coBorrowerFName">First Name</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="coBorrowerFName" id="coBorrowerFName"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('coBorrowerFName', 'LMRInfo')); ?>"
                           placeholder="First Name"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('coBorrowerFName', 'LMRInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 coborrower_credit_fields coborrower_flood_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="coBorrowerLName">Last Name</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="coBorrowerLName" id="coBorrowerLName"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('coBorrowerLName', 'LMRInfo')); ?>"
                           placeholder="Last Name"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('coBorrowerLName', 'LMRInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 coborrower_credit_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="coBSsnNumber">Social Security Number</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="coBSsnNumber" id="coBSsnNumber"
                           class="form-control input-sm mask_ssn mandatory cra_fields"
                           value="<?php echo Strings::showField('coBSsnNumber', 'LMRInfo'); ?>"
                           placeholder="xxx-xx-xxxx"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('coBSsnNumber', 'LMRInfo') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 coborrower_credit_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="coBorrowerDOB">Date Of Birth</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <div class="input-group dobDates">
                        <div class="input-group-prepend coBorrowerDOB">
                                                <span class="input-group-text">
                                                     <i class="fa fa-calendar text-primary icon-lg"></i>
                                                </span>
                        </div>
                        <input class="form-control input-sm mandatory dateNewClass cra_fields" id="coBorrowerDOB"
                               placeholder="MM/DD/YYYY"
                               type="text" name="coBorrowerDOB"
                               data-date-dob-start-date="<?php echo glDate::getMinRequirementDate(); ?>"
                               data-date-dob-end-date="<?php echo glDate::getMaxRequirementDate(); ?>"
                               value="<?php echo Dates::formatDateWithRE(Strings::showField('coBorrowerDOB', 'LMRInfo'), 'YMD', 'm/d/Y'); ?>"
                               autocomplete="off"/>
                    </div>
                <?php } else {
                    echo '<h5>' . Dates::formatDateWithRE(Strings::showField('coBorrowerDOB', 'LMRInfo'), 'YMD', 'm/d/Y') . '</h5>';
                } ?>
            </div>
        </div>
    </div>

    <div class="col-md-6 coborrower_credit_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="coBPresentAddress">Address</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <script>
                        $(document).ready(function() {
                            $('#coBPresentAddress<?php echo $nop; ?>').on('input', function() {
                                address_lookup.InitLegacy($(this));
                            });
                        });
                    </script>
                    <input type="text" name="coBPresentAddress"
                           id="coBPresentAddress"
                           data-address="coBPresentAddress"
                           data-city="coBPresentCity"
                           data-state="coBPresentState"
                           data-zip="coBPresentZip"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('coBPresentAddress', 'file2Info')); ?>"
                           placeholder="Address" autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('coBPresentAddress', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 coborrower_credit_fields">
        <div class="form-group row">
            <label class="font-weight-bold  col-md-5" for="coBPresentCity">City</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="coBPresentCity" id="coBPresentCity"
                           class="form-control input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('coBPresentCity', 'file2Info')); ?>"
                           placeholder="City"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('coBPresentCity', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 coborrower_credit_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="coBPresentState">State</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <select name="coBPresentState" id="coBPresentState"
                            class="form-control input-sm mandatory cra_fields">
                        <option value=''> - Select -</option>
                        <?php
                        for ($j = 0; $j < count($stateArray); $j++) {
                            $sOpt = '';
                            $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), Strings::showField('coBPresentState', 'file2Info'));
                            echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                        }
                        ?>
                    </select>
                <?php } else {
                    echo '<h5>' . Strings::showField('coBPresentState', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
    <div class="col-md-6 coborrower_credit_fields">
        <div class="form-group row ">
            <label class="font-weight-bold  col-md-5" for="coBPresentZip">Zip</label>
            <div class="col-md-7">
                <?php if ($allowToEdit) { ?>
                    <input type="text" name="coBPresentZip" id="coBPresentZip"
                           class="form-control zipCode input-sm mandatory cra_fields"
                           value="<?php echo htmlentities(Strings::showField('coBPresentZip', 'file2Info')); ?>"
                           placeholder="Zip"
                           autocomplete="off">
                <?php } else {
                    echo '<h5>' . Strings::showField('coBPresentZip', 'file2Info') . '</h5>';
                } ?>
            </div>
        </div>
    </div>
</div>
<!-- Co-Borrower Information End -->

<div class="form-group row">
    <label class="font-weight-bold col-md-12">&nbsp;</label>
    <div class="col-md-12" style="text-align:center;">
        <?php if ($allowToEdit) { ?>
            <input type="submit" name="btnSave" id="btnSave" class="btn btn-primary btn-sm cra_fields" value="Save">
            <input type="submit" name="psSubmit" id="psSubmit" class="btn btn-primary btn-sm cra_fields" value="Submit">
        <?php } ?>
    </div>
</div>
