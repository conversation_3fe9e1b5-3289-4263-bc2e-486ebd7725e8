<?php
global $userGroup, $fileType, $userRole, $fileIdArray, $propertyStateArray,
       $LMRClientTypeArray, $statusOptArray, $fileStatusArray, $PCStatusArray, $searchTerm, $searchFieldArray;
?>
<script type="text/javascript">
    $(document).ready(function () {

        $("input[type=reset]").click(function (evt) {      // get reset click
            evt.preventDefault();    // stop default reset behavior (or the rest of this code will run before the reset actually happens)
            form = $(this).parents("form").first();    // get the reset buttons form
            form[0].reset();    // actually reset the form
            form.find(".chzn-select").trigger("chosen:updated");    // tell all Chosen fields in the form that the values may have changed

        }); /** Reset the chosen select values on reset button click **/
        /* Trigger chosen select box */
        /*     try {
                 $(".chzn-select").chosen();
             } catch (e) {
             }*/
        /* Accordion */
        //accordionDiv();
        <?php
        use models\cypher;use models\PageVariables;use models\standard\Strings;if($userGroup == 'REST' || ($fileType == 2 && $userGroup == 'Super') || ($fileType == 4 && $userGroup == 'Super')) {
        if ($userGroup == 'REST') $qs = '?app=1';
        else if ($fileType == 4) $qs = '';
        else $qs = '';
        ?>
        branchRESTOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getBranches.php<?php echo $qs?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#RESTExecutiveName').val(replaceXMLProcess(value));
                document.LMRReport.RESTBranchId.value = data;
            }
        };
        $('#RESTExecutiveName').autocomplete(branchRESTOptions);
        <?php
        if ($userGroup == 'REST') $qs = '?app=1';
        else if ($fileType == 4) $qs = '?app=2';
        else $qs = '';
        ?>
        PCRESTOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getPCs.php<?php echo $qs?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#RESTPCName').val(replaceXMLProcess(value));
                document.LMRReport.RESTProcId.value = data;
            }
        };
        $('#RESTPCName').autocomplete(PCRESTOptions);
        <?php
        } else if($userGroup == 'Super' || $userGroup == 'Auditor' || $userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager' || $userGroup == 'Employee' || $userGroup == 'Branch' || $userGroup == 'Agent' || $fileType == 'CFPB') {
        if ($fileType == 4) {
            $qs = '?app=2';
        } else if ($userRole == 'Auditor') {
            $qs = '?auditorID=' . PageVariables::$userNumber;
        } else if ($fileType == 'CFPB') {
            $qs = '?fileType=' . $fileType;
        } else {
            $qs = '';
        }

        ?>
        PCOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getPCs.php<?php echo $qs?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#PCName').val(replaceXMLProcess(value));
                document.LMRReport.searchPCID.value = data;
                getPCServiceTypes();
            }
        };
        $('#PCName').autocomplete(PCOptions);
        <?php
        //    } else {
        /*
                if ($fileType == 4) {
                  $qs = '?app=2';
                  $qs1 = '&app=2';
                } else {
        */
        if ($userRole == 'Auditor') {
            $qs = '?auditorID=' . PageVariables::$userNumber;
            $qs1 = '&auditorID=' . PageVariables::$userNumber;
        } else {
            $qs = '';
            $qs1 = '';
        }

        //		}
        ?>
        var selExeId = 0;
        try {
            selExeId = document.LMRReport.eId.value;
        } catch (e) {
        }

        branchOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getBranches.php<?php echo $qs?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#branchName').val(replaceXMLProcess(value));
                document.LMRReport.eId.value = data;
            }
        };
        $('#branchName').autocomplete(branchOptions);
        options = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getBrokers.php?eId=' + selExeId + '<?php echo $qs1?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#brokerName').val(replaceXMLProcess(value));
                document.LMRReport.broker.value = data;
            }
        };
        $('#brokerName').autocomplete(options);

        employeeOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getEmployees.php<?php echo $qs?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#employeeName').val(replaceXMLProcess(value));
                document.LMRReport.employeeId.value = data;
            }
        };
        $('#employeeName').autocomplete(employeeOptions);

        lenderOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getLenderList.php?LMRId=<?php echo cypher::myEncryption(implode(', ', $fileIdArray))?>&opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#lenderName').val(replaceXMLProcess(value));
            }
        };
        $('#lenderName').autocomplete(lenderOptions);

        options = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getLeadSourceList.php',
            minChars: 1,
            onSelect: function (value, data) {
                $('#leadSource').val(replaceXMLProcess(value));
            }
        };
        $('#leadSource').autocomplete(options);
        <?php
        }
        ?>
    });

    <?php
    if($userRole == 'REST') {
    } else if($userRole == 'Super' || $userRole == 'Agent' || $userRole == 'Auditor' || $userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || $fileType == 'CFPB') {
    if ($fileType == 4) {
        $qs = '?app=2';
    } else if ($userRole == 'Auditor') {
        $qs = '?auditorID=' . PageVariables::$userNumber;
    } else if ($fileType == 'CFPB') {
        $qs = '?fileType=' . $fileType;
    } else {
        $qs = '';
    }
    ?>
    function listAllPCs() {
        PCOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getPCs.php<?php echo $qs?>',
            minChars: 0,
            onSelect: function (value, data) {
                $('#PCName').val(replaceXMLProcess(value));
                document.LMRReport.searchPCID.value = data;
                getPCServiceTypes();
            }
        };
        $('#PCName').focus();
        $('#PCName').val('');
        document.LMRReport.searchPCID.value = '';
        $('#PCName').autocomplete(PCOptions).onValueChange();
    }
    <?php
    }
    if($userRole == 'REST') {
    } else {
    /*
        if ($fileType == 4) {
              $qs = '&app=2';
            } else {
    */
    if ($userRole == 'Auditor') {
        $qs = '&auditorID=' . PageVariables::$userNumber;
    } else if ($fileType == 'CFPB') {
        $qs = '&fileType=' . $fileType;
    } else {
        $qs = '';
    }
    //	    }
    ?>
    function listAllBranches() {

        var PCID = 0;
        try {
            PCID = document.LMRReport.searchPCID.value;
        } catch (e) {
        }
        try {
            fileType = document.LMRReport.fileType.value;
        } catch (e) {
        }

        branchOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getBranches.php?PCID=' + PCID + '<?php echo $qs?>',
            minChars: 0,
            onSelect: function (value, data) {
                $('#branchName').val(replaceXMLProcess(value));
                document.LMRReport.eId.value = data;
            }
        };
        $('#branchName').focus();
        $('#branchName').val('');
        document.LMRReport.eId.value = '';
        $('#branchName').autocomplete(branchOptions).onValueChange();
    }

    function listAllAgents() {
        var selExeId = 0, PCID = 0;
        try {
            selExeId = document.LMRReport.eId.value;
        } catch (e) {
        }
        try {
            PCID = document.LMRReport.searchPCID.value;
        } catch (e) {
        }
        options = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getBrokers.php?eId=' + selExeId + '&PCID=' + PCID + '<?php echo $qs?>',
            minChars: 0,
            onSelect: function (value, data) {
                $('#brokerName').val(replaceXMLProcess(value));
                document.LMRReport.broker.value = data;
            }
        };
        $('#brokerName').focus();
        $('#brokerName').val('');
        document.LMRReport.broker.value = '';
        $('#brokerName').autocomplete(options).onValueChange();
    }

    function listAllLoanOfficer() {
        var selExeId = 0, PCID = 0;
        try {
            selExeId = document.LMRReport.eId.value;
        } catch (e) {
        }
        try {
            PCID = document.LMRReport.searchPCID.value;
        } catch (e) {
        }
        options = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getBrokers.php?eId=' + selExeId + '&PCID=' + PCID + '<?php echo $qs?>&agentType=1',
            minChars: 0,
            onSelect: function (value, data) {
                $('#secondaryBrokerName').val(replaceXMLProcess(value));
                document.LMRReport.secondaryBrokerNumber.value = data;
            }
        };
        $('#secondaryBrokerName').focus();
        $('#secondaryBrokerName').val('');
        document.LMRReport.secondaryBrokerNumber.value = '';
        $('#secondaryBrokerName').autocomplete(options).onValueChange();
    }

    function listAllEmployees() {
        var PCID = 0;
        try {
            PCID = document.LMRReport.searchPCID.value;
        } catch (e) {
        }
        employeeOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getEmployees.php?PCID=' + PCID + '&AE=1&' + '<?php echo $qs?>',
            minChars: 0,
            onSelect: function (value, data) {
                $('#employeeName').val(replaceXMLProcess(value));
                document.LMRReport.employeeId.value = data;
            }
        };
        $('#employeeName').focus();
        $('#employeeName').val('');
        document.LMRReport.employeeId.value = '';
        $('#employeeName').autocomplete(employeeOptions).onValueChange();
    }

    function listAllLenders() {
        lenderOptions = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getLenderList.php?LMRId=<?php echo cypher::myEncryption(implode(', ', $fileIdArray))?>&opt=APP',
            minChars: 0,
            onSelect: function (value, data) {
                $('#lenderName').val(replaceXMLProcess(value));
            }
        };
        $('#lenderName').focus();
        $('#lenderName').val('');
        $('#lenderName').autocomplete(lenderOptions).onValueChange();
    }

    function deleteMultiSelectValueInDB(name, val) {
        if (name == 'subStatus') {
            saveFileSubstatusChange('updateFileStatusForm', 'LMRId', 'rId', val, '0', '');
        }
    }

    function saveMultiSelectValueInDB(name, val) {
        if (name == 'subStatus') {
            saveFileSubstatusChange('updateFileStatusForm', 'LMRId', 'rId', val, '1', '');
            //QA, FA page
            if($('#isFssUpdated').length > 0) {
                $('#isFssUpdated').val('Yes');
                if($('#lastUpdatedFss').length > 0) $('#lastUpdatedFss').val(val);
            }
            if ($('#lastUpdatedParam').length > 0) {
                LMRequest.lastUpdatedParams('FSS');
                if($('#lastUpdatedFss').length > 0) $('#lastUpdatedFss').val(val);
            }
            //Pipeline page
            if ($('#isPfsFssUpdated').length > 0) {
                $('#isPfsFssUpdated').val('Yes');
            }
        }
    }

    function listAllLeadSources() {
        var selExeId = 0;
        try {
            selExeId = document.LMRReport.eId.value;
        } catch (e) {
        }
        options = {
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getLeadSourceList.php?eId=' + selExeId + '<?php echo $qs?>',
            minChars: 0,
            onSelect: function (value, data) {
                $('#leadSource').val(replaceXMLProcess(value));
            }
        };
        $('#leadSource').focus();
        $('#leadSource').val('');
        $('#leadSource').autocomplete(options).onValueChange();
    }
    <?php
    }
    ?>
    $('select').bind('liszt:ready', function () {
        <?php if (count($propertyStateArray) == 0 && count($LMRClientTypeArray) == 0 && count($statusOptArray) == 0) {
    } else { ?>
        changeChosenSelectPart1Ht();
        <?php } ?>

        <?php if ($searchTerm == '' && count($searchFieldArray) == 0 && count($fileStatusArray) == 0 && count($PCStatusArray) == 0 ){
    } else { ?>
        changeChosenSelectPart2Ht();
        <?php } ?>
    });

    function changeChosenSelectHt(x) {
        try {
            if (x.container_id == 'searchField_chzn' || x.container_id == 'lenderName_chzn' || x.container_id == 'multiplePrimaryStatus_chzn' || x.container_id == 'WFStepID_chzn') {
                changeChosenSelectPart2Ht();
            } else {
                changeChosenSelectPart1Ht();
            }
        } catch (e) {
        }
    }

    function changeChosenSelectPart1Ht() {
        var numbers_array = [$("#propertyState_chzn").height(), $("#LMRClientType_chzn").height(), $("#statusOpt_chzn").height()];
        var biggest = parseInt(Math.max.apply(null, numbers_array)) + 5;

        $(".propertyState").css("height", biggest);
        $(".LMRClientType").css("height", biggest);
        $(".statusOpt").css("height", biggest);
    }

    function changeChosenSelectPart2Ht() {
        try {
            var numbers_array = [$("#searchField_chzn").height(), $("#lenderName_chzn").height(), $("#multiplePrimaryStatus_chzn").height(), $("#WFStepID_chzn").height(), $("#multipleModuleCode_chzn").height()];
        } catch (e) {
        }
        var biggest = parseInt(Math.max.apply(null, numbers_array)) + 5;
        $(".searchTerm").css("height", biggest);
        $(".searchField").css("height", biggest);
        $(".lenderName").css("height", biggest);
        $(".multiplePrimaryStatus").css("height", biggest);
        try {
            $(".WFStepID").css("height", biggest);
        } catch (e) {
        }
    }

    /**
     * Get all client entity.
     */
    function getPCEntitys() {

        var searchPCID = $('#searchPCID').val();

        if (searchPCID > 0) {
            EntityOptions = {
                serviceUrl: siteSSLUrl + 'JQFiles/getAutoCompletedClientEntityInfo.php?PCID=' + searchPCID + '&autoCID=00',
                minChars: 0,	/* it should populate all the business entites on click yes radio button : card309 */
                onSelect: function (value, data) {
                    $('#entityName').val(value);
                    $('#CBEID').val(data);
                }
            };
            $('#entityName').autocomplete(EntityOptions).onValueChange(); // Passing value from matching email Entity Info End..
        }
    }
</script>
