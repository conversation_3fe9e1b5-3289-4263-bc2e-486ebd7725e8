<?php

global $ARVCALRELEASEDATE, $lockedSections, $allowToEdit, $fileTab, $typeOfHMLOLoanRequesting,
       $LMRClientTypeInfo, $propertyNeedRehab, $PCID, $isLoanPaymentAmt, $userGroup,
       $LMRInternalLoanprogramsSingleQuote, $userRole, $allowEditToIRBranch, $allowEditToIRAgent,
       $includeCCF, $activeTab, $borrowerActiveSectionDisp, $allowToLockLoanFile, $lockedFile, $lockedSectionTxt,
       $publicUser, $disabledInputForClient, $tabIndex, $LMRId, $servicesRequested, $fileLP, $glLMRClientTypeArray,
       $desiredCloseDate, $desiredLoanAmount, $desiredInterestRateRangeFrom, $desiredInterestRateRangeTo,
       $lienPosition, $fileMC, $typeOfSaleTR, $typeOfSale, $docTypeTR, $docTypeLoanterms,
       $HMLOLender, $loanTermExpireDate, $purchaseCloseDate, $finalLoanAmt, $loanTerm, $fldEditOpt,
       $calcInrBasedOnMonthlyPayment, $InrBasedOnMonthlyPayment, $HMLOPCAmortizationValPCLoanTerms, $amortizationType,
       $paymentFrequency, $isLoTxt, $totalMonthlyPayment,
       $totalMonthlyPaymentTooltip, $isTaxesInsEscrowed, $isTaxesInsEscrowedDispOpt, $taxes1, $netMonthlyPayment, $isBlanketLoan,
       $isBlanketLoanDiv, $isBlanketLoanDispOpt, $noOfPropertiesAcquiring, $ownedFreeAndClear, $ownedSameEntity,
       $transactionalFieldsDispOpt, $resalePrice, $resaleClosingDate, $doesPropertyNeedRehabSection,
       $transCommercialFieldsDispOpt, $commercialFieldsTDNCDispOpt, $costBasis, $homeValue,
       $doesPropertyNeedRehabDispTDDiv, $assessedValue, $isThisGroundUpConstruction, $approvedAcquisition,
       $commercialFieldsTDNDateCDispOpt, $originalPurchasePrice, $originalPurchaseDate, $commercialFieldsTDDispOpt,
       $maxAmtToPutDown, $downPaymentPercentage, $acquisitionPriceFinanced, $rehabConsCls, $lineOfCreditProp, $fldArr,
       $CORefiLTVPercentage, $CORTotalLoanAmt, $rehabCostPercentageFinanced, $rehabCostFinanced, $haveInterestreserve,
       $prepaidInterestReserve, $noOfMonthsPrepaid, $initialAdvance, $cashOutDiv, $payOffMortgage1, $payOffMortgage2,
       $payOffOutstandingTaxes, $closingCostFinance, $totalDrawsFunded, $currentLoanBalance, $LOCTotalLoanAmtHideDispOpt,
       $tLAToolTip, $totalLoanAmount, $closingCostFinanced, $totalCashOutAmt, $autoCalcTLAARVDisp, $autoCalARVToolTip,
       $autoCalcTLAARV, $maxArvPer, $maxArvPerDisp, $maxLTCPer, $totProjectCostLbl, $TPCToolTip, $maxLTCPer,
       $maxLTCPerDisp, $totalProjectCost, $NewTPCToolTip, $NewTotalProjectCost, $LOCTotalLoanAmtDispOpt,
       $LOCTotalLoanAmt, $landValue, $simpleARV, $ARV, $acquisitionLTVTD, $acquisitionLTV, $marketLTVTD,
       $marketLTV, $marketLTVToolTip, $LTCToolTip, $LTC, $perRehabCostFinanced, $NewLTCToolTip, $NewLoanToCost,
       $fieldsInfo, $refinanceSectionDispOpt, $refinanceCurrentLender, $assetSavingMoneyMarket, $costOfImprovementsMade,
       $refinanceSectionDispOpt, $refinanceMonthlyPayment, $refinanceCurrentRate, $seekingCashRefinance,
       $seekingCashRefinanceDispOpt, $seekingCash, $seekingFund, $refinanceCurrentLoanBalance,
       $LMRInternalLoanprogramsArray, $HMLOPCBasicPaymentFrequencyInfoArray,
       $lotPurchasePrice, $currentLotMarket, $costSpent, $lotStatus, $groundUpDispTDDiv, $exitFeePoints,
       $exitFeeAmount, $daysUntilClose, $paydownamount, $fileModuleInfo, $propertyState, $stateArray, $LTCInitialLoanAmountToolTip,
       $LTCInitialLoanAmount, $LTCMarketValueToolTip, $LTCMarketValue, $LTCInitialLoanAmountCls, $grossProfit, $grossProfitMargin,
       $useOfFunds, $showStartLoanNumber, $aggregateDSCR;

global $HMLOPCBasicRateLockPeriodData;

use models\composite\oPC\getPCHMLOBasicLoanInfoForFileLevel;
use models\composite\oPC\getPCInternalLoanGuidelines;
use models\composite\oPC\getPCMinMaxLoanGuidelines;
use models\constants\accrualTypes;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glDate;
use models\constants\gl\gldocType;
use models\constants\gl\glExpectForDueDiligence;
use models\constants\gl\glFrlDesLoanAmt;
use models\constants\gl\glFundingAs;
use models\constants\gl\glGroundUpConstruction;
use models\constants\gl\glHMLOAmortization;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLienPosition;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\glLotStatus;
use models\constants\gl\globalBankruptcyCat;
use models\constants\gl\globalBusinessBankruptcyCat;
use models\constants\gl\globalMinTimeInBusinessCat;
use models\constants\gl\glpaymentFrequency;
use models\constants\gl\glPCID;
use models\constants\gl\glPlansAndPermitStatus;
use models\constants\gl\glprincipalRepayment;
use models\constants\gl\glRateIndex;
use models\constants\gl\glRateLockPeriod;
use models\constants\gl\glRestrictCustomFields;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\constants\gl\glTypeOfSale;
use models\constants\gl\glYesNo;
use models\constants\typeOfHMLOLoanRequesting;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\loanPropertySummary;
use models\Controllers\LMRequest\loanSetting;
use models\Controllers\loanForm;
use models\CustomField;
use models\cypher;
use models\HMLOLoanTermsCalculation;
use models\HMLOLoanTermsCalculation\LTC2Variables;
use models\lendingwise\tblBuildingAnalysisNeed;
use models\lendingwise\tblBuildingAnalysisOutstanding;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblLMRClientType;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$glTypeOfSale = [];
$glHMLOHouseType = glHMLOHouseType::$glHMLOHouseType;
$glHMLOLoanTerms = glHMLOLoanTerms::$glHMLOLoanTerms;
$glHMLOLienPosition = glHMLOLienPosition::getLienPosition($PCID);
$glpaymentFrequency = glpaymentFrequency::$glpaymentFrequency;
$glprincipalRepayment = glprincipalRepayment::$glprincipalRepayment;
$glExpectForDueDiligence = glExpectForDueDiligence::$glExpectForDueDiligence;
$glLotStatus = glLotStatus::$glLotStatus;
$glRestrictCustomFields = glRestrictCustomFields::$glRestrictCustomFields;
$glFrlDesLoanAmt = glFrlDesLoanAmt::$glFrlDesLoanAmt;
$gldocType = gldocType::$gldocType;
$glTypeOfSale = glTypeOfSale::$glTypeOfSale;
$glRateLockPeriod = glRateLockPeriod::$glRateLockPeriod;


$loanTermCRBStatus = '';
if (in_array($PCID, [glPCID::PCID_CRB, glPCID::PCID_DEV_DAVE])) {
    if ($_GET['tabOpt'] == 'HMLI') {
        if (!loanSetting::$loanSetting['totalTerms']) {
            loanSetting::init($LMRId);
        }
        $CRB_totalTerms = loanSetting::$loanSetting['totalTerms'];
        $isAdjustable = loanSetting::$loanSetting['isAdjustable'];
        if ($isAdjustable != 'Fixed Rate') $loanTermCRBStatus = ' disabled ';
    }
    if ($fileTab == 'FA') {
        $loanTermCRBStatus = ' disabled ';
    }
}

$fileRecordedDate = Dates::formatDateWithRE(Strings::showField('recordDate', 'LMRInfo'), 'YMD', 'Y-m-d');
if (in_array('Loan Terms', $lockedSections)) {
    $BackupAllowToEdit = $allowToEdit;
    $allowToEdit = false;
    LMRequest::$allowToEdit = false;
}

$secArr = BaseHTML::sectionAccess2(['sId' => 'LT', 'opt' => $fileTab]);
loanForm::pushSectionID('LT');


$ACFsecArr = BaseHTML::sectionAccess(['sId' => 'ACF', 'opt' => $fileTab]); // Get Active Fields only....
$BORFsecArr = BaseHTML::sectionAccess(['sId' => 'BORF', 'opt' => $fileTab]); // Get Active Fields only....

$trialPaymentDate1 = '';
$trialPaymentDate1 = Strings::showField('trialPaymentDate1', 'ResponseInfo');
$trialPaymentDate1 = Dates::formatDateWithRE($trialPaymentDate1, 'YMD', 'm/d/Y');

//$rateIndexRes = tblFileHMLOPropInfo::getPCRateIndex($PCID);

glRateIndex::$PCID = $PCID;
$glRateIndex = new glRateIndex();
$glRateIndex = glCustomJobForProcessingCompany::hideRateIndexFields($PCID);

$rateIndex = Strings::showField('rateIndex', 'fileHMLOPropertyInfo');
$rateIndexArr = explode(',', $rateIndex);

if ($typeOfHMLOLoanRequesting == '' && $fileModuleInfo[0]['moduleCode'] != 'loc') {
    $typeOfHMLOLoanRequesting = 'Purchase';
}

/**
 * Description    : Get the selected Loan program guideline Prompt Msg like as (min / max Loan Amount & Max LTV).
 * Date            : Feb 16, 2018
 * Developer        : Viji and Venkatesh Raju.
 **/

$resultArray = [];
$maxLoanAmount = 0;
$loanPgm = $loanPgmDetails = $reqForLoanProUnderwriting = '';
$maxLTV = 0;
$minLoanAmount = $maxARV = $minMidFico = $maxMidFico = $minPropertyForFixFlop = $maxPropertyForFixFlop = $minPropertyForGrndConst = $maxPropertyForGrndConst = $maxPoints = $minPoints = $downPaymentPercent = 0;
$HMLOPCMinMaxLoanGuidelines = [];
$landValueCls = 'display: none;';
$landValueExtraCls = 'display: none;';

for ($i = 0; $i < count($LMRClientTypeInfo ?? []); $i++) {
    $loanPgm = $LMRClientTypeInfo[$i]['ClientType'];
}

if ($propertyNeedRehab == 'No') {
    $doesPropertyNeedRehabDispDiv = 'display: none;';
}

if ($loanPgm == 'CONS' ||
    $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::NEW_CONSTRUCTION_EXISTING_LAND) {
    $landValueCls = '';
}
if ($loanPgm == 'CONS'
    && ($typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
        || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::DELAYED_PURCHASE
        || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE
        || $typeOfHMLOLoanRequesting == typeOfHMLOLoanRequesting::REFINANCE)) {

    $landValueExtraCls = 'display: table-cell;';
}
$HMLOPCElgibleState = '';
if ($loanPgm != '') {
    $HMLOPCMinMaxLoanGuidelines = getPCMinMaxLoanGuidelines::getReport([
        'loanPgm'     => $loanPgm,
        'PCID'        => $PCID,
        'loanPurpose' => '']);
    $warningErrorsArray = getPCHMLOBasicLoanInfoForFileLevel::getReport(['loanPgm' => $loanPgm, 'PCID' => $PCID]);
    $HMLOPCElgibleStateArray = $warningErrorsArray['HMLOPCElgibleState'];
    $HMLOPCElgibleState = (Arrays::implode2dArray(',', $HMLOPCElgibleStateArray, 'stateCode'));
}
$accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
$accrualType = Strings::showField('accrualType', 'fileHMLOPropertyInfo');
if ($accrualType == '') {
    $accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
}
$minDSCR = null;
$loanGuideLineId = null;
if (count($HMLOPCMinMaxLoanGuidelines ?? []) > 0) {
    $maxLTV = $HMLOPCMinMaxLoanGuidelines['maxLTV'];
    $minLoanAmount = $HMLOPCMinMaxLoanGuidelines['minLoanAmount'];
    $maxLoanAmount = $HMLOPCMinMaxLoanGuidelines['maxLoanAmount'];
    $maxARV = $HMLOPCMinMaxLoanGuidelines['maxLTVAfterRehab'];
    $minRate = $HMLOPCMinMaxLoanGuidelines['minRate'];
    $maxRate = $HMLOPCMinMaxLoanGuidelines['maxRate'];
    $LGMaxLTC = $HMLOPCMinMaxLoanGuidelines['totalLTC'];
    $minMidFico = $HMLOPCMinMaxLoanGuidelines['minMidFico'];
    $maxMidFico = $HMLOPCMinMaxLoanGuidelines['maxMidFico'];
    $minPropertyForFixFlop = $HMLOPCMinMaxLoanGuidelines['minPropertyForFixFlop'];
    $maxPropertyForFixFlop = $HMLOPCMinMaxLoanGuidelines['maxPropertyForFixFlop'];
    $minPropertyForGrndConst = $HMLOPCMinMaxLoanGuidelines['minPropertyForGrndConst'];
    $maxPropertyForGrndConst = $HMLOPCMinMaxLoanGuidelines['maxPropertyForGrndConst'];
    $maxPoints = $HMLOPCMinMaxLoanGuidelines['maxPoints'];
    $minPoints = $HMLOPCMinMaxLoanGuidelines['minPoints'];
    $downPaymentPercent = $HMLOPCMinMaxLoanGuidelines['downPaymentPercentage'];
    $loanPgmDetails = urldecode($HMLOPCMinMaxLoanGuidelines['loanPgmDetails']);
    $reqForLoanProUnderwriting = urldecode($HMLOPCMinMaxLoanGuidelines['reqForLoanProUnderwriting']);

    $guideLineMinSeasoningBusinessBankruptcyVal = $HMLOPCMinMaxLoanGuidelines['MinSeasoningBusinessBankruptcyVal'];
    $guideLineMinSeasoningForeclosureVal = $HMLOPCMinMaxLoanGuidelines['MinSeasoningForeclosureVal'];
    $guideLineMinSeasoningPersonalBankruptcyVal = $HMLOPCMinMaxLoanGuidelines['MinSeasoningPersonalBankruptcyVal'];
    $guideLineMinTimeVal = $HMLOPCMinMaxLoanGuidelines['minTimeVal'];
    $minDSCR = $HMLOPCMinMaxLoanGuidelines['minDSCR'];
    $loanGuideLineId = $HMLOPCMinMaxLoanGuidelines['BLID'];
}
if ($reqForLoanProUnderwriting == '') {
    $reqForLoanProUnderwriting = $glExpectForDueDiligence;
}

$glHMLOAmortization = glHMLOAmortization::getForFile($fileMC, $HMLOPCAmortizationValPCLoanTerms);


if ($LMRInternalLoanprogramsSingleQuote != '') {
    $LMRInternalLoanGuidelines = getPCInternalLoanGuidelines::getReport(['loanPgm' => $LMRInternalLoanprogramsArray, 'PCID' => $PCID, 'loanPurpose' => Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo')]);
    ?>
    <input type="hidden" id="LMRInternalLoanGuidelines" value='<?php echo json_encode($LMRInternalLoanGuidelines); ?>'>
    <?php

}

if ($isLoanPaymentAmt == 'SMP') {
    $lien1Terms = '';
}

$editIR = '';
if (($userGroup == 'Branch' && $allowEditToIRBranch != 1) || ($userGroup == 'Agent' && $allowEditToIRAgent != 1)) {
    $editIR = ' readonly ';
} // https://www.pivotaltracker.com/story/show/160269268

$applicantConfirmed = Strings::showField('applicantConfirmed', 'fileHMLONewLoanInfo') ?? 0;

if (Dates::IsEmpty(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->LOISentDate)) {
    $LOISentDate = '';
} else {
    $LOISentDate = Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->LOISentDate, 'YMD', 'm/d/Y');
}
?>
<input type="hidden" id="minRate" name="minRate" value="<?php echo $minRate ?>"/>
<input type="hidden" id="maxRate" name="maxRate" value="<?php echo $maxRate ?>"/>
<input type="hidden" id="includeCCF" name="includeCCF" value="<?php echo $includeCCF ?>"/>
<input type="hidden" id="PCID" name="PCID" value="<?php echo $PCID ?>"/>
<input type="hidden" id="LGMaxLTV" name="LGMaxLTV" value="<?php echo $maxLTV ?>"/>
<input type="hidden" id="LGMinLoanAmount" name="LGMinLoanAmount" value="<?php echo $minLoanAmount ?>"/>
<input type="hidden" id="LGMaxLoanAmount" name="LGMaxLoanAmount" value="<?php echo $maxLoanAmount ?>"/>
<input type="hidden" id="LGMaxARV" name="LGMaxARV" value="<?php echo $maxARV ?>"/>
<input type="hidden" id="LGMaxLTC" name="LGMaxLTC" value="<?php echo $LGMaxLTC ?>"/>
<input type="hidden" id="LGMinMidfico" name="LGMinMidfico" value="<?php echo $minMidFico ?>"/>
<input type="hidden" id="LGMaxMidfico" name="LGMaxMidfico" value="<?php echo $maxMidFico ?>"/>
<input type="hidden" id="LGMinFixFlop" name="LGMinFixFlop" value="<?php echo $minPropertyForFixFlop ?>"/>
<input type="hidden" id="LGMaxFixFlop" name="LGMaxFixFlop" value="<?php echo $maxPropertyForFixFlop ?>"/>
<input type="hidden" id="LGMinGround" name="LGMinGround" value="<?php echo $minPropertyForGrndConst ?>"/>
<input type="hidden" id="LGMaxGround" name="LGMaxGround" value="<?php echo $maxPropertyForGrndConst ?>"/>
<input type="hidden" id="LGMaxOrgPoints" name="LGMaxOrgPoints" value="<?php echo $maxPoints ?>"/>
<input type="hidden" id="LGMinOrgPoints" name="LGMinOrgPoints" value="<?php echo $minPoints ?>"/>
<input type="hidden" id="LGDownPaymentPerc" name="LGDownPaymentPerc" value="<?php echo $downPaymentPercent ?>"/>
<input type="hidden" id="LGElgibleState" value="<?php echo $HMLOPCElgibleState; ?>">
<input type="hidden" id="filePropertyState" value="<?php echo $propertyState; ?>">
<input type="hidden" id="filePropertyStateFull"
       value="<?php echo Strings::getStateFullName($stateArray, $propertyState);; ?>">
<input type="hidden" id="activetab" name="activetab" value="<?php echo $activeTab ?>"/>
<input type="hidden" name="allowFormSubmit" id="allowFormSubmit" value="0">
<input type="hidden" name="setRehabDefaultVal" id="setRehabDefaultVal" value="">
<input type="hidden" name="fileRecordedDate" id="fileRecordedDate" value="<?php echo $fileRecordedDate ?>">
<input type="hidden" name="ARVCALRELEASEDATE" id="ARVCALRELEASEDATE" value="<?php echo $ARVCALRELEASEDATE ?>">
<input type="hidden" id="paydownamount" value="<?php echo $paydownamount; ?>">

<input type="hidden" id="MinSeasoningBusinessBankruptcyVal"
       value="<?php echo $guideLineMinSeasoningBusinessBankruptcyVal; ?>">
<input type="hidden" id="MinSeasoningForeclosureVal" value="<?php echo $guideLineMinSeasoningForeclosureVal; ?>">
<input type="hidden" id="MinSeasoningPersonalBankruptcyVal"
       value="<?php echo $guideLineMinSeasoningPersonalBankruptcyVal; ?>">
<input type="hidden" id="minTimeVal" value="<?php echo $guideLineMinTimeVal; ?>">

<input type="hidden" id="MinSeasoningBusinessBankruptcyValText"
       value="<?php echo globalBusinessBankruptcyCat::$globalBusinessBankruptcyCat[$guideLineMinSeasoningBusinessBankruptcyVal]; ?>">
<input type="hidden" id="MinSeasoningForeclosureValText"
       value="<?php echo globalBankruptcyCat::$globalBankruptcyCat[$guideLineMinSeasoningForeclosureVal]; ?>">
<input type="hidden" id="MinSeasoningPersonalBankruptcyValText"
       value="<?php echo globalBankruptcyCat::$globalBankruptcyCat[$guideLineMinSeasoningPersonalBankruptcyVal]; ?>">
<input type="hidden" id="minTimeValText"
       value="<?php echo globalMinTimeInBusinessCat::$globalMinTimeInBusinessCat[$guideLineMinTimeVal]; ?>">
<input type="hidden" id="minDSCR" value="<?php echo $minDSCR ?>"/>
<input type="hidden" id="loanGuideLineId" value="<?php echo $loanGuideLineId ?>"/>

<!-- HMLOLoanTermsForm.php -->
<?php if ($activeTab != 'CI') { ?>
    <div class="card card-custom HMLOLoanInfoSections propAddress borrowerActiveSection loantermsCard LT <?php if (count($secArr) <= 0) {
        echo 'secHide';
    } ?>" style="<?php echo $borrowerActiveSectionDisp; ?>">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    <?php echo BaseHTML::getSectionHeading('LT'); ?>
                </h3>
                <?php if (trim(BaseHTML::getSectionTooltip('LT')) != '') { ?>&nbsp;
                    <i class="popoverClass fas fa-info-circle text-primary "
                       data-html="true"
                       data-content="<?php echo BaseHTML::getSectionTooltip('LT'); ?>"></i>
                <?php } ?>
            </div>
            <div class="card-toolbar ">

                <?php if ((in_array($userRole, ['Super']) || $allowToLockLoanFile) && ($userRole != 'Client')) { ?>

                    <div id="loan_info_lock" class="d-flex flex-row bd-highlight mb-3">
                        <div class="p-2 font-weight-bold align-self-center"><i
                                    class="fa fa-info-circle text-primary tooltipClass mr-2"
                                    title="Locking the loan terms prohibits anyone from editing the terms including all employees, branch, broker/loan officer, & borrowers via their portal or webforms. Only users with permission to lock/unlock loans will be able to unlock the loan terms and make edits."></i>
                            Lock Loan File:
                        </div>
                        <input type="hidden" name="lockTabOpt" value="HMLI">
                        <input type="hidden" name="isFilelocked" id="isFilelocked" value="">
                        <input type="hidden" name="lockSectionOpt"
                               value="Loan Terms,Fees & Costs,Required Reserves,Cash-to-Close">
                        <div class="p-2 font-weight-bold align-self-center">
                                                        <span class="switch switch-icon">
                                    <label class="font-weight-bold">
                                        <input
                                                class="form-control" <?php if ($lockedFile == '1') { ?> checked="checked" <?php } ?>
                                               id="lockLoanFileId" type="checkbox"
                                                onchange="toggleSwitch('lockLoanFileId','lockLoanFileHidden','1','0' );">
                                        <input type="hidden" name="fileLock" id="lockLoanFileHidden"
                                               value="<?php echo $lockedFile ?>">
                                        <span></span>
                                    </label>
                                </span>
                        </div>
                    </div>
                <?php }
                echo $lockedSectionTxt;
                ?>

                <span href="javascript:void(0);"
                   class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                   data-card-tool="toggle"
                   data-section="loantermsCard"
                   data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </span>

            </div>
        </div>

        <div class="card-body loantermsCard_body">
            <div class="row">

                <div class="col-md-12"><!-- Row 1 start -->
                    <div class="row">
                        <?php
                        if ($publicUser != 1) { // Pivotal Task # : 153830484.

                            if (count($aSecArr = BaseHTML::sectionAccess(['sId' => 'Admin', 'opt' => $fileTab])) > 0) {
                                $loanprogramlabel = '';
                                $loanprogramlabel = BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $aSecArr, 'opt' => 'L']);
                                if ($userGroup == 'Super' || $userGroup == 'Employee' || $userGroup == 'Agent' || $userGroup == 'Branch') {
                                    ?>
                                    <div
                                            class="clsseperator col-md-3 LMRClientType_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $aSecArr, 'opt' => 'D']); ?>">
                                        <div class="row form-group">
                                            <label class="col-md-12 font-weight-bold"
                                                <?php if ($activeTab == 'LI' || $activeTab == 'QAPP') { ?>
                                                    for="LMRClientType_mirror"
                                                <?php } else { ?>
                                                    for="LMRClientType"
                                                <?php } ?>><?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $aSecArr, 'opt' => 'L']); ?>
                                                <i class="fa fa-info-circle text-primary tooltipClass" data-html="true"
                                                   title="<?php echo nl2br(htmlentities($loanPgmDetails)); ?>"></i>
                                                <?php echo loanForm::changeLog(
                                                    LMRequest::File()->getTblLMRClientType_by_LMRID()->CTID,
                                                    'ClientType',
                                                    tblLMRClientType::class,
                                                    'Loan Program',
                                                ); ?>
                                            </label>
                                            <div class="col-md-12">
                                                <?php if ($allowToEdit && $disabledInputForClient) { ?>
                                                    <div id="service_container">
                                                        <select
                                                            <?php if ($activeTab == 'LI' || $activeTab == 'QAPP') { ?>
                                                                name="LMRClientType_mirror[]"
                                                                id="LMRClientType_mirror"
                                                            <?php } else { ?>
                                                                name="LMRClientType"
                                                                id="LMRClientType"
                                                            <?php } ?>
                                                                onchange="
                                                                <?php if ($activeTab == 'LI' || $activeTab == 'QAPP') { ?>
                                                                        populateDualFieldForLP(this.value, 'LMRClientType');
                                                                <?php } ?>
                                                                        formControl.controlFormFields('',
                                                                        '<?php echo LMRequest::File()->getTblFileModules_by_fileID()->moduleCode; ?>', this.id,
                                                                        'loanProgram'
                                                                        );
                                                                        populatePCBasicLoanInfo(
                                                                        'loanModForm',
                                                                        this.value,
                                                                        '<?php echo $PCID; ?>',
                                                                        '<?php echo LMRequest::File()->getTblFileModules_by_fileID()->moduleCode; ?>',
                                                                        '<?php echo $activeTab; ?>'
                                                                        );
                                                                        getPCMinMaxLoanGuidelines('loanModForm', '<?php echo $PCID; ?>');
                                                                        showAndHideLandFieldsNew(this.value);
                                                                <?php if (LMRequest::$PCID == glPCID::PCID_PROD_CV3) { ?>
                                                                        loanInfoV2Form.showHidePropertyRehabCv3(this.value);
                                                                        loanInfoV2Form.hideInitialLoanAmountCV3();
                                                                <?php } ?>"
                                                                tabindex="<?php echo $tabIndex++; ?>"
                                                                class="<?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $aSecArr, 'opt' => 'M']); ?> form-control input-sm" <?php echo BaseHTML::fieldAccess(['fNm' => 'LMRClientType', 'sArr' => $aSecArr, 'opt' => 'I']); ?>>
                                                            <option value="">- Select -</option>
                                                            <?php
                                                            if ($LMRId > 0) {
                                                                $serviceCnt = 0;
                                                                if (count($servicesRequested) > 0) $serviceCnt = count($servicesRequested);
                                                                for ($j = 0; $j < $serviceCnt; $j++) {
                                                                    $LMRClientTypeCode = '';
                                                                    $sOpt = '';
                                                                    $LMRClientType = '';
                                                                    $LMRClientTypeCode = trim($servicesRequested[$j]['LMRClientType']);
                                                                    $LMRClientType = trim($servicesRequested[$j]['serviceType']);
                                                                    $chk = '';
                                                                    $chk = Strings::isKeyChecked($LMRClientTypeInfo, 'ClientType', $LMRClientTypeCode);
                                                                    if (trim($chk) == 'checked') $chk = 'selected ';
                                                                    $displayOption = '';
                                                                    if ($LMRClientTypeCode == 'TBD' && $LMRClientTypeInfo[0]['ClientType'] != 'TBD') {
                                                                        $displayOption = "style = 'display:none;' ";
                                                                    }
                                                                    if ($servicesRequested[$j]['internalLoanProgram'] == 0) {
                                                                        echo '<option ' . $chk . ' ' . $displayOption . " value=\"$LMRClientTypeCode\">$LMRClientType</option>";
                                                                    }
                                                                }
                                                            }
                                                            if (in_array('TBD', $fileLP) && $LMRId > 0) { ?>
                                                                <!--                                                                <option selected-->
                                                                <!--                                                                        value="--><?php //echo 'TBD'; ?><!--">--><?php //echo 'TBD'; ?><!--</option>-->
                                                            <?php }
                                                            ?>
                                                        </select>
                                                    </div>
                                                    <?php
                                                } else {
                                                    $selectServices = '';
                                                    $j = 0;
                                                    for ($i = 0; $i < count($LMRClientTypeInfo); $i++) {
                                                        if (array_key_exists($LMRClientTypeInfo[$i]['ClientType'], $glLMRClientTypeArray)) {
                                                            if ($j > 0) $selectServices .= ', ';
                                                            $selectServices .= trim($glLMRClientTypeArray[$LMRClientTypeInfo[$i]['ClientType']]);
                                                            $j++;
                                                        }
                                                    }
                                                    if (in_array('TBD', $fileLP) && $LMRId > 0) {
                                                        $selectServices = 'TBD';
                                                    }
                                                    echo '<b>' . $selectServices . '</b>';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                }
                            }
                            //if ($userGroup == 'Super' || $userGroup == 'Employee' || $userGroup == 'Agent' || $userGroup == 'Branch') {
                            ?>
                            <?php
                        }
                        //}
                        ?>

                        <div class="clsseperator  col-md-3 desiredClosingDate_disp <?php echo loanForm::showField('desiredClosingDate'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('desiredClosingDate', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend desiredClosingDate">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                            </div>
                                            <!--  data-date-start-date="05/18/2024"-->
                                            <input type="text"
                                                   class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredClosingDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                   name="desiredClosingDate"
                                                   id="desiredClosingDate"
                                                   data-future-date="true"
                                                   data-start-date="<?php echo glDate::getFutureDateOnly(); ?>"
                                                   value="<?php echo $desiredCloseDate ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" placeholder="MM/DD/YYYY"/>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . $desiredCloseDate . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div
                                class="clsseperator  col-md-3  desiredLoanAmount_disp <?php echo loanForm::showField('desiredLoanAmount'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('desiredLoanAmount', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) {
                                        $fldAcc = '';
                                        if (in_array($PCID, $glFrlDesLoanAmt)) {
                                            if ($userRole != 'Manager') {
                                                $fldAcc = ' readOnly ';
                                            }
                                        }
                                        ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredLoanAmount', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="desiredLoanAmount" id="desiredLoanAmount"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($desiredLoanAmount) ?>"
                                                   onblur="currencyConverter(this, this.value);"
                                                   placeholder="0.00"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" <?php echo $fldAcc . ' ' . BaseHTML::fieldAccess(['fNm' => 'desiredLoanAmount', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else { ?>
                                        <b>$ <?php echo Currency::formatDollarAmountWithDecimal($desiredLoanAmount) ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-3 <?php echo loanForm::showField('fundingDate'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label2(
                                    'fundingDate',
                                    'col-md-12 label_highlight font-weight-bold'
                                ); ?>
                                <div class="col-md-12">
                                    <?php
                                    if ($allowToEdit) {
                                        ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend fundingDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                            </div>
                                            <input
                                                    class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'fundingDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                    type="text" name="fundingDate" id="fundingDate"
                                                    value="<?php echo Dates::formatDateWithRE(Strings::showField('fundingDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y'); ?>"
                                                    TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                    placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'fundingDate', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else {
                                        echo "<div class=\"left\"><b>" . Dates::formatDateWithRE(Strings::showField('fundingDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y') . '</b></div>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <!-- Desired Interest Rate Range -->
                        <div
                                class="clsseperator col-md-3 desiredInterestRateRange_disp <?php echo loanForm::showField('desiredInterestRateRange'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('desiredInterestRateRange', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <input type="text"
                                                   name="desiredInterestRateRangeFrom"
                                                   id="desiredInterestRateRangeFrom"
                                                   placeholder="0.000"
                                                   class="input-sm form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   onblur="currencyConverter(this, this.value, 3);"
                                                   value="<?php echo number_format(Strings::replaceCommaValues($desiredInterestRateRangeFrom), 3); ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <div class="input-group-append">
                                                    <span class="input-group-text">
                                                        %
                                                    </span>
                                            </div>
                                            <input type="text"
                                                   name="desiredInterestRateRangeTo"
                                                   id="desiredInterestRateRangeTo"
                                                   placeholder="0.000"
                                                   onblur="currencyConverter(this, this.value, 3);"
                                                   class="input-sm form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="<?php echo number_format(Strings::replaceCommaValues($desiredInterestRateRangeTo), 3); ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'desiredInterestRateRange', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                            <div class="input-group-append">
                                                    <span class="input-group-text">
                                                        %
                                                    </span>
                                            </div>
                                        </div>
                                    <?php } else { ?>
                                        <b> <?php echo number_format(Strings::replaceCommaValues($desiredInterestRateRangeFrom), 3) . ' % '; ?>
                                            -
                                            <?php echo number_format(Strings::replaceCommaValues($desiredInterestRateRangeTo), 3) . ' % '; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <!-- Desired Interest Rate Range -->
                        <div
                                class=" clsseperator  col-md-3 isHouseProperty_disp <?php echo loanForm::showField('isHouseProperty'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('isHouseProperty', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'isHouseProperty', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="isHouseProperty" id="isHouseProperty"
                                                TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'isHouseProperty', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            for ($i = 0; $i < count($glHMLOHouseType); $i++) {
                                                $sOpt = '';
                                                $typeOfHouse = '';
                                                $typeOfHouse = trim($glHMLOHouseType[$i]);
                                                $sOpt = Arrays::isSelected($typeOfHouse, Strings::showField('isHouseProperty', 'FilePropInfo'));
                                                echo "<option value=\"" . $typeOfHouse . "\" " . $sOpt . '>' . $typeOfHouse . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('isHouseProperty', 'FilePropInfo') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" clsseperator  col-md-3 lienPosition_disp <?php echo loanForm::showField('lienPosition'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('lienPosition', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lienPosition', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="lienPosition" id="lienPosition"
                                                TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'lienPosition', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            $glHMLOLienPositionKeys = [];
                                            $glHMLOLienPositionKeys = array_keys($glHMLOLienPosition);
                                            for ($i = 0; $i < count($glHMLOLienPositionKeys); $i++) {
                                                $lienPos = $sOpt = '';
                                                $lienPos = trim($glHMLOLienPositionKeys[$i]);
                                                $sOpt = Arrays::isSelected($lienPos, $lienPosition);
                                                ?>
                                                <option
                                                        value="<?php echo $lienPos ?>" <?php echo $sOpt ?>><?php echo $glHMLOLienPosition[$lienPos] ?></option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        echo '<b>' . $lienPosition . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class="clsseperator col-md-3 typeOfHMLOLoanRequesting_disp <?php echo loanForm::showField('typeOfHMLOLoanRequesting'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label(
                                    'typeOfHMLOLoanRequesting',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                        'typeOfHMLOLoanRequesting',
                                        tblFileHMLOPropInfo::class
                                    )
                                ); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select name="typeOfHMLOLoanRequesting"
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfHMLOLoanRequesting', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                id="typeOfHMLOLoanRequesting"
                                                onchange="getPCMinMaxLoanGuidelines('loanModForm', '<?php echo $PCID ?>'); showAndHideCommercialFields(this.value);calculateCapRate();"
                                                tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfHMLOLoanRequesting', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            if (sizeof($gltypeOfHMLOLoanRequesting ?? []) == 0) {
                                                $gltypeOfHMLOLoanRequesting = gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting;
                                            }
                                            if (in_array('EF', $fileMC) || in_array('loc', $fileMC)) {
                                                $gltypeOfHMLOLoanRequesting[] = 'Equipment Financing';
                                            }
                                            if (!$gltypeOfHMLOLoanRequesting) {
                                                $gltypeOfHMLOLoanRequesting = [];
                                            }
                                            sort($gltypeOfHMLOLoanRequesting);
                                            for ($i = 0; $i < count($gltypeOfHMLOLoanRequesting); $i++) {
                                                $sOpt = '';
                                                $typeOfLoan = '';
                                                $typeOfLoan = trim($gltypeOfHMLOLoanRequesting[$i]);
                                                $sOpt = Arrays::isSelected($typeOfLoan, $typeOfHMLOLoanRequesting);
                                                echo "<option value=\"" . $typeOfLoan . "\" " . $sOpt . '>' . $typeOfLoan . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?>
                                        <b><?php echo Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo') ?></b><?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="clsseperator col-md-3 applicationLoanExitPlan_disp <?php echo loanForm::showField('applicationLoanExitPlan'); ?>">
                            <div class="row">
                                <?php echo loanForm::label(
                                    'applicationLoanExitPlan',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                        'applicationLoanExitPlan',
                                        tblFileHMLOPropInfo::class
                                    )
                                ); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::select(
                                        'applicationLoanExitPlan',
                                        $allowToEdit,
                                        $tabIndex++,
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->applicationLoanExitPlan,
                                        glHMLOExitStrategy::getApplicationLoanExitOptions($PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType),
                                        ' ',
                                        ' chzn-select ',
                                        ' ',
                                        'Please Select ' . loanForm::$permissions['applicationLoanExitPlan']->fieldLabel,
                                        ($PCID == glPCID::PCID_PROD_CV3 && $activeTab == 'HMLI')
                                    ); ?>
                                </div>
                            </div>
                        </div>

                        <?php loanForm::pushSectionID('AQ'); ?>
                        <div class="clsseperator col-md-3 exitStrategy_disp <?php echo loanForm::showField('exitStrategy'); ?>">
                            <div class="row">
                                <?php echo loanForm::label(
                                    'exitStrategy',
                                    'col-md-12 ',
                                    ''
                                ); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::select(
                                        'exitStrategy_mirror',
                                        $allowToEdit,
                                        $tabIndex++,
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->exitStrategy,
                                        glHMLOExitStrategy::$filteredOptions,
                                        'exitStrategyHideShow(this.value, \'exitStrategyExplain\', \'\');fileCommon.mirrorField(this,\'exitStrategy_mirror\');',
                                        ' chzn-select exitStrategy_mirror ',
                                        ' ',
                                        'Please Select ' . loanForm::$permissions['exitStrategy']->fieldLabel
                                    ); ?>
                                </div>
                            </div>
                        </div>
                        <?php loanForm::popSectionID(); ?>

                        <div class="clsseperator col-md-3 accountExecutiveLoanExitNotes_disp <?php echo loanForm::showField('accountExecutiveLoanExitNotes'); ?>">
                            <div class="row">
                                <?php echo loanForm::label(
                                    'accountExecutiveLoanExitNotes',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                        'accountExecutiveLoanExitNotes',
                                        tblFileHMLOPropInfo::class
                                    )
                                ); ?>
                                <div class="col-md-12">
                                    <?php echo loanForm::textarea(
                                        'accountExecutiveLoanExitNotes',
                                        $allowToEdit,
                                        $tabIndex++,
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->accountExecutiveLoanExitNotes,
                                        'validateMaxLength',
                                        '',
                                        '',
                                        '500',
                                    ); ?>
                                </div>
                            </div>
                        </div>


                        <div class="clsseperator col-md-3 useOfFundsDiv useOfFunds_disp <?php echo loanForm::showField('useOfFundsLT');
                        if (!in_array($typeOfHMLOLoanRequesting, [
                                typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE,
                                typeOfHMLOLoanRequesting::DELAYED_PURCHASE,
                                typeOfHMLOLoanRequesting::COMMERCIAL_CASH_OUT_REFINANCE,
                                typeOfHMLOLoanRequesting::LINE_OF_CREDIT
                        ])) {
                            echo ' d-none ';
                        } ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label(
                                    'useOfFundsLT',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                        'useOfFunds',
                                        tblFileHMLOPropInfo::class
                                    )
                                ); ?>
                                <div class="col-md-12">
                                    <?php
                                    if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                                        $useFundsMaxLength = loanForm::getFieldLength('useOfFunds', 'tblFileHMLOPropInfo');
                                    } else {
                                        $useFundsMaxLength = 5000;
                                    }
                                    echo loanForm::textarea(
                                        'useOfFundsLT',
                                        $allowToEdit,
                                        $tabIndex++,
                                        $useOfFunds,
                                        'validateMaxLength',
                                        'assignValueToUseOfFundsMirrorfield(this.value,this.id);',
                                        '',
                                        $useFundsMaxLength,
                                        'mirror'
                                    ); ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class="clsseperator  typeOfSale col-md-3 typeOfSale_disp <?php echo loanForm::showField('typeOfSale'); ?>"
                                style="<?php echo $typeOfSaleTR ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('typeOfSale', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfSale', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="typeOfSale" id="typeOfSale"
                                                TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'typeOfSale', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            sort($glTypeOfSale);
                                            for ($i = 0; $i < count($glTypeOfSale); $i++) {
                                                $sOpt = '';
                                                $proTypeOfSale = '';
                                                $proTypeOfSale = trim($glTypeOfSale[$i]);
                                                $sOpt = Arrays::isSelected($proTypeOfSale, $typeOfSale);
                                                echo "<option value=\"" . $proTypeOfSale . "\" " . $sOpt . '>' . $proTypeOfSale . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('typeOfSale', 'fileHMLOPropertyInfo') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div class="clsseperator col-md-3 PSAClosingDateClass <?php echo loanForm::showField('PSAClosingDate'); ?>"
                             style="<?php if($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE) { echo 'display:none;'; } ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('PSAClosingDate', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) {
                                        echo loanForm::date(
                                            'PSAClosingDate',
                                            $allowToEdit,
                                            $tabIndex++,
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->PSAClosingDate ?? '',
                                            ' dateNewClass '
                                        );
                                    } else {
                                        echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->PSAClosingDate, 'YMD', 'm/d/Y') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class="clsseperator  docType col-md-3 docType_disp <?php echo loanForm::showField('docType'); ?>"
                                style="<?php echo $docTypeTR ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('docType', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'docType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="docType" id="docType"
                                                TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'docType', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            sort($gldocType);
                                            for ($i = 0; $i < count($gldocType); $i++) {
                                                $sOpt = '';
                                                $prodocType = '';
                                                $prodocType = trim($gldocType[$i]);
                                                $sOpt = Arrays::isSelected($prodocType, $docTypeLoanterms);
                                                echo "<option value=\"" . $prodocType . "\" " . $sOpt . '>' . $prodocType . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('docType', 'fileHMLOPropertyInfo') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                    </div><!-- Row 1 End -->
                </div>

                <div class="col-md-12 separator separator-dashed my-2 separator12" id="loaninfoseperator"></div>


                <div class="col-md-12"><!-- Row 2 start -->
                    <div class="row">
                        <?php if ($publicUser != 1 && $userRole != 'Client') { ?>
                            <div
                                    class=" col-md-3 HMLOLender_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOLender', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('BORF');
                                    echo loanForm::label('HMLOLender', 'col-md-12 ');
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <input name="HMLOLender" id="HMLOLender"
                                                   type="text"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   maxlength="75"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOLender', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                   value="<?php echo htmlspecialchars($HMLOLender); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'HMLOLender', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>>
                                        <?php } else { ?><h5><?php echo $HMLOLender ?></h5><?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                        <?php loanForm::pushSectionID('BORF'); ?>
                        <div class=" col-md-3 <?php echo loanForm::showField('fundingAs'); ?>">
                            <div class="form-group">
                                <?php
                                echo loanForm::select(
                                    'fundingAs',
                                    LMRequest::$allowToEdit,
                                    1,
                                    Strings::showField('fundingAs', 'fileHMLONewLoanInfo'),
                                    glFundingAs::$glFundingAs,
                                    '',
                                    'chzn-select form-control',
                                    ' ',
                                    'Please Select ' . loanForm::$permissions['fundingAs']->fieldLabel,
                                    '',
                                    'data-value = "' . Strings::showField('fundingAs', 'fileHMLONewLoanInfo') . '"'
                                );
                                ?>
                            </div>
                        </div>
                        <?php loanForm::popSectionID(); ?>


                        <?php if ($publicUser != 1) { ?>
                            <div
                                    class=" col-md-3 loanLoanNumber_disp <?php echo loanForm::showField('loanLoanNumber'); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('LT');
                                    echo loanForm::label('loanLoanNumber', 'col-md-12 font-weight-bold label_highlight');
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php if ($showStartLoanNumber == 1 && !glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) { ?>
                                            <div class="input-group">
                                                <input
                                                        class="form-control  <?php echo BaseHTML::fieldAccess(['fNm' => 'loanNumber', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        type="text"
                                                    <?php if ($fileTab == 'FA' || $fileTab == 'QA') { ?>
                                                        name="loanLoanNumber" id="loanLoanNumber" <?php } else { ?>name="loanNumber" id="loanNumber" <?php } ?>
                                                        placeholder="Loan Number"
                                                    <?php if (($fileTab == 'FA' || $fileTab == 'QA') &&
                                                        (trim(Strings::isKeyChecked($fileModuleInfo, 'moduleCode', 'LM')) == 'checked'
                                                            || trim(Strings::isKeyChecked($fileModuleInfo, 'moduleCode', 'SS')) == 'checked')) { ?>
                                                        onchange="mirrorLoanNumber(this.id)" <?php } ?>
                                                        value="<?php echo htmlentities(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                                        maxlength="45"
                                                        size="25" autocomplete="off"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        readonly>
                                                <?php if (!Strings::showField('loanNumber', 'LMRInfo')) { ?>
                                                    <div class="input-group-append">
                                                    <span class="input-group-text" id="getLoanNo">
                                                        <a style="text-decoration:none;"
                                                           class="fa fa-refresh"
                                                            <?php if ($fileTab == 'FA' || $fileTab == 'QA') { ?>
                                                                onclick="getAvailableLoanNo('<?php echo cypher::myEncryption($PCID) ?>','loanLoanNumber');"
                                                            <?php } else { ?>
                                                                onclick="getAvailableLoanNo('<?php echo cypher::myEncryption($PCID) ?>','loanNumber');"
                                                            <?php } ?>
                                                           title="Click to auto create loan number.">
                                                            <i class="tooltipClass flaticon2-reload text-success"></i>
                                                        </a>
                                                    </span>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                        <?php } else if ($allowToEdit) { ?>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'loanLoanNumber', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                <?php if ($fileTab == 'FA' || $fileTab == 'QA') { ?> onchange="mirrorLoanNumber(this.id)" name="loanLoanNumber" id="loanLoanNumber" <?php } else { ?>name="loanNumber" id="loanNumber" <?php } ?>
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                   value="<?php echo htmlspecialchars(Strings::showField('loanNumber', 'LMRInfo')); ?>"
                                                   maxlength="50"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'loanLoanNumber', 'sArr' => $secArr, 'opt' => 'I']);
                                                if (glCustomJobForProcessingCompany::generateFileIDAsLoanNumber($PCID)) {
                                                    echo 'readonly';
                                                }
                                                ?> >
                                        <?php } else { ?>
                                            <b><?php echo Strings::showField('loanNumber', 'LMRInfo'); ?></b>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                        <?php if ($publicUser != 1) { ?>
                            <div class="col-md-3 LOISentDate_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'LOISentDate', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('BORF');
                                    echo loanForm::label(
                                        'LOISentDate',
                                        'col-md-12 font-weight-bold label_highlight',
                                        '',
                                    );
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php
                                        if ($allowToEdit && $disabledInputForClient) {
                                            ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend LOISentDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                                </div>
                                                <input
                                                        class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'LOISentDate', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?> dateNewClass"
                                                        type="text" name="LOISentDate" id="LOISentDate"
                                                        value="<?php echo $LOISentDate; ?>"
                                                        TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                        placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'LOISentDate', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>>
                                            </div>
                                        <?php } else {
                                            echo "<div class=\"left\"><b>" . $LOISentDate . '</b></div>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                            <div
                                    class="col-md-3 loanTermExpireDate_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTermExpireDate', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('BORF');
                                    echo loanForm::label(
                                        'loanTermExpireDate',
                                        'col-md-12 font-weight-bold label_highlight',
                                        '',
                                        loanForm::changeLog(
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                            'loanTermExpireDate',
                                            tblFileHMLONewLoanInfo::class
                                        )
                                    );
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php
                                        if ($allowToEdit && $disabledInputForClient) {
                                            ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend loanTermExpireDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                                </div>
                                                <input
                                                        class="form-control input-sm  <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTermExpireDate', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?> dateNewClass"
                                                        type="text" name="loanTermExpireDate" id="loanTermExpireDate"
                                                        value="<?php echo $loanTermExpireDate ?>"
                                                        TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                        placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTermExpireDate', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>>
                                            </div>
                                        <?php } else {
                                            echo "<div class=\"left\"><b>" . $loanTermExpireDate . '</b></div>';
                                        } ?>
                                    </div>
                                </div>
                            </div>

                            <?php
                            $adminSecArr = BaseHTML::sectionAccess(['sId' => 'Admin', 'opt' => $fileTab]); /* Field Access.. */
                            ?>
                            <div
                                    class="col-md-3 purchaseCloseDate_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'purchaseCloseDate', 'sArr' => $adminSecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('Admin');
                                    echo loanForm::label(
                                        'purchaseCloseDate',
                                        'col-md-12 ',
                                    );
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend closingDate">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar text-primary"></i>
                                                    </span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'purchaseCloseDate', 'sArr' => $adminSecArr, 'opt' => 'M']); ?> dateNewClass "
                                                       name="closingDate" id="closingDate"
                                                       value="<?php echo $purchaseCloseDate ?>"
                                                       TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                       onchange="populateDualDateForHMLONewLoan(this.value, 'loanModForm', 'datesigned');populateDualField(this.value, 'closingDate_1');getHMLOLoanInfoTotalMonthlyPayment();"
                                                       placeholder="MM/DD/YYYY"/>
                                            </div>
                                        <?php } else {
                                            echo '<b>' . $purchaseCloseDate . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>

                            <div
                                    class=" col-md-3 daysUntilClose_disp  <?php echo BaseHTML::fieldAccess(['fNm' => 'daysUntilClose', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('ACF');
                                    echo loanForm::label2(
                                        'daysUntilClose',
                                        'col-md-12 font-weight-bold justify-content-center align-self-center'
                                    );
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <input type="number"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'daysUntilClose', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                       name="daysUntilClose"
                                                       id="daysUntilClose" readonly
                                                       value="<?php echo $daysUntilClose ?>"
                                                       TABINDEX="<?php echo $tabIndex++; ?>"
                                                       autocomplete="off"
                                                       placeholder="0" <?php echo BaseHTML::fieldAccess(['fNm' => 'daysUntilClose', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?>/>
                                                <div class="input-group-append">
                                                    <span class="input-group-text">Days</span>
                                                </div>
                                            </div>
                                        <?php } else {
                                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($daysUntilClose) . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>


                        <?php } ?>
                        <?php if ($publicUser != 1) { ?>
                            <div
                                    data-sectionid="<?php echo loanForm::getSectionID(); ?>"
                                    class="col-md-3 firstPaymentDueDate_disp <?php echo loanForm::showField('firstPaymentDueDate'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('firstPaymentDueDate', 'col-md-12 font-weight-bold label_highlight'); ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend trialPaymentDate1">
                                        <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                        </span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'firstPaymentDueDates', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                       name="trialPaymentDate1" id="trialPaymentDate1"
                                                       value="<?php echo $trialPaymentDate1 ?>" autocomplete="off"
                                                       maxlength="10" placeholder="MM/DD/YYYY"
                                                       tabindex="<?php echo $tabIndex++; ?>"/>
                                            </div>
                                            <?php
                                        } else {
                                            echo '<h5>' . $trialPaymentDate1 . '</h5>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>

                        <div
                                class="col-md-3 maturityDate_disp <?php echo loanForm::showField('maturityDate'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('maturityDate', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend maturityDate">
                                        <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                        </span>
                                            </div>
                                            <input type="text" name="maturityDate" id="maturityDate"
                                                   value="<?php echo Dates::formatDateWithRE(Strings::showField('maturityDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y'); ?> "
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'maturityDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'maturityDate', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   placeholder="MM/DD/YYYY">
                                        </div>
                                    <?php } else {
                                        echo '<b>' . Dates::formatDateWithRE(Strings::showField('maturityDate', 'fileHMLOPropertyInfo'), 'YMD', 'm/d/Y') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <?php
                        if (isset($glRestrictCustomFields[$PCID])) { //https://www.pivotaltracker.com/story/show/161218497
                            if (in_array('finalLoanAmt', $glRestrictCustomFields[$PCID])) { ?>
                                <div
                                        class="col-md-3 <?php echo loanForm::showField('finalLoanAmt'); ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('finalLoanAmt', 'col-md-12 '); ?>
                                        <div class="col-md-12">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <span class="input-group-addon">$</span>
                                                    <input name="finalLoanAmt" id="finalLoanAmt"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'finalLoanAmt', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($finalLoanAmt); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'finalLoanAmt', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                </div>
                                            <?php } else { ?><h5><?php echo $finalLoanAmt ?></h5><?php } ?>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                        ?>

                        <div
                                class="col-md-3 loanTerm_disp <?php echo loanForm::showField('loanTerm'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label(
                                    'loanTerm',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                        'loanTerm',
                                        tblFileHMLOPropInfo::class
                                    )
                                ); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <?php if (trim($loanTermCRBStatus) == 'disabled' && $_GET['tabOpt'] == 'HMLI') { ?>
                                            <input type="hidden"
                                                   name="loanTermCRB"
                                                   id="loanTermCRB"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   value="<?php echo $loanTerm; ?>">
                                        <?php } ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="loanTerm"
                                                id="loanTerm"
                                                onchange="monthlyInterestRate(this.value, 0,0)"
                                                TABINDEX="<?php echo $tabIndex++; ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'loanTerm', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                            <?php echo $loanTermCRBStatus; ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            foreach ($glHMLOLoanTerms as $eachLoanTerm) {
                                                echo "<option value=\"" . $eachLoanTerm . "\" " . Arrays::isSelected($eachLoanTerm, $loanTerm) . '>' . $eachLoanTerm . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?>
                                        <input type="hidden" name="loanTerm" id="loanTerm"
                                               value="<?php echo Strings::showField('loanTerm', 'fileHMLOPropertyInfo') ?>">
                                        <?php echo '<b>' . Strings::showField('loanTerm', 'fileHMLOPropertyInfo') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <?php if ($publicUser != 1 && $userRole != 'Client') { ?>
                            <div
                                    class=" col-md-3 form-group rateIndex_disp <?php echo loanForm::showField('rateIndex'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('rateIndex', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php
                                        if ($allowToEdit) { ?>
                                        <select
                                                class="chzn-select form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'rateIndex', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="rateIndex[]"
                                                id="rateIndex"
                                                data-placeholder="Please select"
                                                multiple="" <?php echo BaseHTML::fieldAccess(['fNm' => 'rateIndex', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""></option>
                                            <?php
                                            for ($k = 0; $k < count(glRateIndex::$glRateIndex ?? []); $k++) {
                                                $sel = '';
                                                if (in_array(glRateIndex::$glRateIndex[$k], $rateIndexArr)) $sel = ' selected ';
                                                ?>
                                                <option
                                                        value="<?php echo trim(glRateIndex::$glRateIndex[$k]); ?>" <?php echo $sel; ?>><?php echo trim(glRateIndex::$glRateIndex[$k]) ?></option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                        <?php } else { ?>
                                            <?php echo '<b>' . implode(',',$rateIndexArr) . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($publicUser != 1 && $userRole != 'Client') { ?>
                            <div
                                    class=" col-md-3 spread_disp <?php echo loanForm::showField('spread'); ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('spread', 'col-md-12 '); ?>
                                    <div class="col-md-12">
                                        <?php
                                        if ($allowToEdit) {
                                            ?>
                                            <div class="input-group">
                                                <input
                                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'spread', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                        type="number"
                                                        name="spread"
                                                        id="spread"
                                                        value="<?php echo Currency::formatDollarAmountWithDecimalZerosLimit(Strings::showField('spread', 'fileHMLOPropertyInfo'), 8); ?>"
                                                        autocomplete="off"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'spread', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        <?php } else {
                                            echo '<b>' . Strings::showField('spread', 'fileHMLOPropertyInfo') . '&nbsp;%</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div
                                class=" col-md-3 lien1Rate_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="lien1Rate"><?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $BORFsecArr, 'opt' => 'L']); ?>
                                    <?php echo loanForm::changeLog(
                                        LMRequest::$LMRId,
                                        'lien1Rate',
                                        \models\lendingwise\tblFile::class,
                                        'Interest Rate'
                                    ); ?>
                                </label>
                                <div class="col-md-12">
                                    <?php
                                    if ($allowToEdit) {
                                        ?>
                                        <div class="input-group">
                                            <input
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                    type="text"
                                                    placeholder="0.0"
                                                    name="lien1Rate"
                                                    id="lien1Rate"
                                                <?php if (isset($lien1Rate)) { ?>
                                                    value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien1Rate', 'LMRInfo')), 3) ?>"
                                                <?php } else { ?>
                                                    value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('lien1Rate', 'LMRInfo')), 3) ?>"
                                                <?php } ?>
                                                    autocomplete="off"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    onchange="populateCostOfCaptial('loanModForm', this.value); updateLoanDetail(); validateMinMaxLoanGuidelines();"
                                                <?php echo $editIR; ?>
                                                <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo($PCID, $activeTab); ?>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Rate', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>
                                                <?php if (Strings::showField('lien1Rate', 'LMRInfo') > 0 && $fldEditOpt == 2) echo ' readonly '; ?> >
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                        <div class="note InRateRange"></div>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('lien1Rate', 'LMRInfo') . '&nbsp;%</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <!-- Rate Lock fields added on Mar 15, 2024 sc-48955 -->
                        <?php
                        loanForm::pushSectionID('BORF');
                        ?>
                        <div class="col-md-3  <?php echo loanForm::showField('rateLockPeriod'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                    'rateLockPeriod',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockPeriod',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Period'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                name="rateLockPeriod"
                                                id="rateLockPeriod"
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockPeriod', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?> chzn-select"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockPeriod', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>
                                                data-placeholder="Please Select <?php echo loanForm::getFieldLabel('rateLockPeriod'); ?>"
                                                onchange="loanTerms.hideOrShowRateLockFields(this.value);">
                                            <option value=""></option>
                                            <?php foreach ($glRateLockPeriod as $eachRateLockPeriod) { ?>
                                                <option value="<?php echo $eachRateLockPeriod; ?>" <?php echo Arrays::isSelected($eachRateLockPeriod, LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockPeriod); ?>>
                                                    <?php echo trim($eachRateLockPeriod); ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="rateLockFields col-md-3
                        <?php echo loanForm::showField('rateLockDate'); ?>">
                            <div class="row form-group ">
                                <?php
                                echo loanForm::label(
                                    'rateLockDate',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockDate',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Date'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend rateLockDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                            </div>
                                            <input
                                                    type="text"
                                                    name="rateLockDate"
                                                    id="rateLockDate"
                                                    value="<?php echo Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockDate, 'YMD', 'm/d/Y'); ?>"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockDate', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?> dateNewClass"
                                                    autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockDate', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>
                                                    placeholder="MM/DD/YYYY">
                                        </div>
                                    <?php } else {
                                        echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockDate, 'YMD', 'm/d/Y') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div class="rateLockFields col-md-3
                        <?php echo loanForm::showField('rateLockExpirationDate'); ?>">
                            <div class="row form-group">
                                <?php
                                $rateLockDateLabel = loanForm::$formFields['BORF']['rateLockDate']->fieldLabel;
                                $rateLockPeriodLabel = loanForm::$formFields['BORF']['rateLockPeriod']->fieldLabel;

                                $rateLockExpirationDateToolTip = $rateLockDateLabel . ' + ' . $rateLockPeriodLabel . ' + 30';
                                echo loanForm::label(
                                    'rateLockExpirationDate',
                                    'col-md-12 ',
                                    $rateLockExpirationDateToolTip,
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockExpirationDate',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Expiration Date'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend rateLockExpirationDate">
                                                <span class="input-group-text">
                                                    <i class="fa fa-calendar text-primary"></i>
                                                </span>
                                            </div>
                                            <input
                                                    type="text"
                                                    name="rateLockExpirationDate"
                                                    id="rateLockExpirationDate"
                                                    readonly
                                                    value="<?php echo Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExpirationDate, 'YMD', 'm/d/Y'); ?>"
                                                    tabindex="<?php echo $tabIndex++; ?>"
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockExpirationDate', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?> "
                                                    autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockExpirationDate', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>
                                                    placeholder="MM/DD/YYYY">
                                        </div>
                                    <?php } else {
                                        echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExpirationDate, 'YMD', 'm/d/Y') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div class="rateLockFields col-md-3
                        <?php echo loanForm::showField('rateLockExtension'); ?>">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                    'rateLockExtension',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockExtension',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Extension'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                name="rateLockExtension"
                                                id="rateLockExtension"
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockExtension', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockExtension', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>
                                                onchange="showOrHideDiv('rateLockExtension', 'rateLockNotesDiv')">
                                            <option value=""> Select</option>
                                            <option value="Yes" <?php echo Arrays::isSelected('Yes', LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExtension); ?>>
                                                Yes
                                            </option>
                                            <option value="No" <?php echo Arrays::isSelected('No', LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExtension); ?>>
                                                No
                                            </option>
                                        </select>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div id="rateLockNotesDiv" class="rateLockFields  col-md-3
                            <?php echo loanForm::showField('rateLockNotes'); ?>
                            <?php echo BaseHTML::parentFieldAccess(['fNm' => 'rateLockExtension', 'sArr' => $BORFsecArr, 'pv' => LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockExtension, 'av' => 'Yes']); ?> ">
                            <div class="row form-group">
                                <?php
                                echo loanForm::label(
                                    'rateLockNotes',
                                    'col-md-12 ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'rateLockNotes',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        'Rate Lock Notes'
                                    ),
                                );
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <textarea
                                                name="rateLockNotes"
                                                id="rateLockNotes"
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockNotes', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'rateLockNotes', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>
                                        ><?php echo LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->rateLockNotes; ?></textarea>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <!--// Rate Lock fields added on Mar 15, 2024 sc-48955 //-->
                        <?php
                        loanForm::popSectionID();
                        //if (($userGroup == 'Employee' || $userGroup == 'Branch' || $userGroup == 'Agent' || $userGroup == 'Super') && $publicUser != 1) {    // Pivotal Task # : 153830484. // Pivotal Task # : 153961351.
                        ?>
                        <?php if ($publicUser != 1 && $userRole != 'Client') { ?>
                            <div
                                    class=" col-md-3 costOfCapital_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfCapital', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('BORF');
                                    echo loanForm::label('costOfCapital', 'col-md-12 ');
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php
                                        if ($allowToEdit) { ?>
                                            <div class="input-group">

                                                <input
                                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfCapital', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                        type="text"
                                                        placeholder="0.0"
                                                        name="costOfCapital"
                                                        id="costOfCapital"
                                                        value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('costOfCapital', 'fileHMLONewLoanInfo')), 3) ?>"
                                                        autocomplete="off"
                                                        tabindex="<?php echo $tabIndex++; ?>"
                                                        onchange="populateYieldSpread('loanModForm', this.value);calculateHMLOInterestRate('loanModForm', 'lien1Rate');validateMinMaxLoanGuidelines();" <?php echo $editIR; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'costOfCapital', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?> <?php if (Strings::showField('lien1Rate', 'LMRInfo') > 0 && $fldEditOpt == 2) echo ' readonly '; ?> >
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        <?php } else {
                                            echo '<b>' . Strings::showField('costOfCapital', 'fileHMLONewLoanInfo') . '&nbsp;%</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($publicUser != 1 && $userRole != 'Client') { ?>
                            <div
                                    class="col-md-3 yieldSpread_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'yieldSpread', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <?php
                                    loanForm::pushSectionID('BORF');
                                    echo loanForm::label('yieldSpread', 'col-md-12 ');
                                    loanForm::popSectionID();
                                    ?>
                                    <div class="col-md-12">
                                        <?php
                                        if ($allowToEdit) { ?>
                                            <div class="input-group">

                                                <input
                                                        class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'yieldSpread', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                        type="text"
                                                        name="yieldSpread"
                                                        id="yieldSpread"
                                                        readonly="readonly"
                                                        placeholder="0.0"
                                                        value="<?php echo number_format(Strings::replaceCommaValues(Strings::showField('yieldSpread', 'fileHMLONewLoanInfo')), 3) ?>"
                                                        autocomplete="off" tabindex="<?php echo $tabIndex++; ?>"
                                                        onchange="calculateHMLOInterestRate('loanModForm', 'lien1Rate');" <?php echo $editIR; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'yieldSpread', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?> <?php if (Strings::showField('lien1Rate', 'LMRInfo') > 0 && $fldEditOpt == 2) echo ' readonly '; ?> >
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        <?php } else {
                                            echo '<b>' . Strings::showField('yieldSpread', 'fileHMLONewLoanInfo') . '&nbsp;%</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div
                                class="col-md-6 calcInrBasedOnMonthlyPayment_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                loanForm::pushSectionID('ACF');
                                echo loanForm::label('calcInrBasedOnMonthlyPayment', 'col-md-9 ');
                                loanForm::popSectionID();
                                ?>
                                <div class="col-md-3">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid "
                                                   for="calcInrBasedOnMonthlyPaymentYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                       name="calcInrBasedOnMonthlyPayment"
                                                       id="calcInrBasedOnMonthlyPaymentYes"
                                                       value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $calcInrBasedOnMonthlyPayment); ?>
                                                       onclick="involvedPurchaseHideShow(this.value, 'calcInrBasedOnMonthlyPaymentDispOpt', '');" <?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?> ><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid "
                                                   for="calcInrBasedOnMonthlyPaymentNo">
                                                <input type="radio" name="calcInrBasedOnMonthlyPayment"
                                                       id="calcInrBasedOnMonthlyPaymentNo" value="No"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $calcInrBasedOnMonthlyPayment); ?>
                                                       onclick="involvedPurchaseHideShow(this.value, 'calcInrBasedOnMonthlyPaymentDispOpt', '');" <?php echo BaseHTML::fieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?> ><span></span>No
                                            </label></div>
                                    <?php } else { ?>
                                        <h5><?php echo $calcInrBasedOnMonthlyPayment; ?></h5>
                                    <?php } ?>
                                </div>


                                <div
                                        class="calcInrBasedOnMonthlyPaymentDispOpt col-md-12 <?php echo BaseHTML::parentFieldAccess(['fNm' => 'calcInrBasedOnMonthlyPayment', 'sArr' => $ACFsecArr, 'pv' => $calcInrBasedOnMonthlyPayment, 'av' => 'Yes']); ?>">
                                    <div class="row">
                                        <?php
                                        loanForm::pushSectionID('ACF');
                                        echo loanForm::label('InrBasedOnMonthlyPayment', 'col-md-8 ');
                                        loanForm::popSectionID();
                                        ?>
                                        <div class="col-md-4">
                                            <?php if ($allowToEdit) { ?>
                                                <input type="text" name="InrBasedOnMonthlyPayment"
                                                       id="InrBasedOnMonthlyPayment"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                       value="<?php echo $InrBasedOnMonthlyPayment; ?>"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'InrBasedOnMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'InrBasedOnMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?>
                                                       readonly>
                                            <?php } else { ?>
                                                <h5><?php echo $InrBasedOnMonthlyPayment; ?></h5>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div
                                class="col-md-3 lien1Terms_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="lien1Terms"><?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $BORFsecArr, 'opt' => 'L']); ?>
                                    <?php echo loanForm::changeLog(
                                        LMRequest::$LMRId,
                                        'lien1Terms',
                                        \models\lendingwise\tblFile::class,
                                        'Amortization'
                                    ); ?>
                                </label>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                name="lien1Terms" id="lien1Terms"
                                                tabindex="<?php echo $tabIndex++; ?>" <?php if ($isLoanPaymentAmt == 'SMP' ||
                                            in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                                            echo 'disabled';
                                        } ?>
                                                onchange="updateLoanDetail();" <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Terms', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php

                                            foreach ($glHMLOAmortization as $i => $amort) {
                                                $sOpt = Arrays::isSelected($amort, $lien1Terms);
                                                echo '<option value="' . $amort . '"' . $sOpt . '>' . $amort . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        echo "<span class=\"H5\">" . $lien1Terms . ' &nbsp;&nbsp;&nbsp; ' . $amortizationType . '</span>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <?php if ($allowToEdit) {
                            if ($amortizationType == '') {
                                $amortizationType = 'Fixed';
                            }
                            ?>
                            <div
                                    class="  col-md-3 amortizationType_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <label class="col-md-12 font-weight-bold">Amortization Type</label>
                                    <div class="col-md-12">
                                        <div class="radio-inline">
                                            <label class="radio radio-solid "
                                                   for="amortizationTypeFixed"><input
                                                        type="radio"
                                                        name="amortizationType"
                                                        id="amortizationTypeFixed"
                                                        class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                        value="Fixed" <?php echo Strings::isChecked('Fixed', $amortizationType); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>><span></span>Fixed
                                                Rate</label>
                                            <label class="radio radio-solid " for="amortizationTypeAdjust">
                                                <input type="radio"
                                                       name="amortizationType"
                                                       id="amortizationTypeAdjust"
                                                       class="form-control <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                       value="Adjustable" <?php echo Strings::isChecked('Adjustable', $amortizationType); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'amortizationType', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>><span></span>Adjustable
                                                Rate</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                        ?>

                        <div
                                class=" col-md-3 principalRepayment_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'principalRepayment', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                loanForm::pushSectionID('BORF');
                                echo loanForm::label('principalRepayment', 'col-md-12 ');
                                loanForm::popSectionID();
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'principalRepayment', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                name="principalRepayment" id="principalRepayment"
                                                TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'principalRepayment', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            for ($i = 0; $i < count($glprincipalRepayment); $i++) {
                                                $sOpt = '';
                                                $sOpt = Arrays::isSelected($glprincipalRepayment[$i], Strings::showField('principalRepayment', 'fileHMLONewLoanInfo'));
                                                echo "<option value=\"" . $glprincipalRepayment[$i] . "\" " . $sOpt . '>' . $glprincipalRepayment[$i] . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        echo '<b>' . Strings::showField('principalRepayment', 'fileHMLONewLoanInfo') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>


                        <div
                                class=" col-md-3 paymentFrequency_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'paymentFrequency', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                loanForm::pushSectionID('ACF');
                                echo loanForm::label('paymentFrequency', 'col-md-12 ');
                                loanForm::popSectionID();
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select name="paymentFrequency"
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'paymentFrequency', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                id="paymentFrequency"
                                                tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'paymentFrequency', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php if ($paymentFrequency == '') $paymentFrequency = '12';
                                            if (count($HMLOPCBasicPaymentFrequencyInfoArray ?? []) > 0) {
                                                foreach ($HMLOPCBasicPaymentFrequencyInfoArray as $payFrequencyVal) {
                                                    $selectOpt = '';
                                                    if ($paymentFrequency == $payFrequencyVal) $selectOpt = 'selected';
                                                    echo "<option value=\"" . $payFrequencyVal . "\" " . $selectOpt . '>' . $glpaymentFrequency[$payFrequencyVal] . '</option>';
                                                }
                                            } else {
                                                foreach ($glpaymentFrequency as $payFrequencyKey => $payFrequencyVal) {
                                                    $selectOpt = '';
                                                    if ($paymentFrequency == $payFrequencyKey) $selectOpt = 'selected';
                                                    echo "<option value=\"" . $payFrequencyKey . "\" " . $selectOpt . '>' . $payFrequencyVal . '</option>';
                                                }
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?>
                                        <b><?php echo Strings::showField('paymentFrequency', 'fileHMLOPropertyInfo') ?></b><?php } ?>
                                </div>
                            </div>
                        </div>

                        <div class="clearfix"></div>
                        <div
                                class=" col-md-3 isLoanPaymentAmt_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="isLoanPaymentAmtILA"><?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?></label>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-list">
                                            <label id="isIntialLoanAmountDisp"
                                                   class="radio radio-solid disableClick" for="isLoanPaymentAmtILA">
                                                <div class="radio enableClick">
                                                    <input type="radio"
                                                           name="isLoanPaymentAmt"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                           id="isLoanPaymentAmtILA"
                                                           value="ILA"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           onclick="updatePaymentTooltip('ILA');updateLoanDetail();"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'isLoanPaymentAmt', 'sArr' => $ACFsecArr, 'opt' => 'i']); ?>
                                                        <?php echo Strings::isChecked('ILA', $isLoanPaymentAmt); ?>>
                                                    <span></span>
                                                    <div id="isLoTxt"><?php echo $isLoTxt; ?></div>
                                                </div>
                                            </label>
                                            <label class="radio radio-solid disableClick" for="isLoanPaymentAmtTLA">
                                                <div class="radio enableClick">
                                                    <input type="radio"
                                                           name="isLoanPaymentAmt"
                                                           id="isLoanPaymentAmtTLA"
                                                           value="TLA"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           onclick="updatePaymentTooltip('TLA');updateLoanDetail(); " <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $ACFsecArr, 'opt' => 'i']); ?> <?php echo Strings::isChecked('TLA', $isLoanPaymentAmt); ?>>
                                                    <span></span>
                                                    <div>Total Loan Amount</div>
                                                </div>
                                            </label>
                                            <?php
                                            if (!in_array($PCID, glCustomJobForProcessingCompany::$glCustomHidePaymentBasedField)) { ?>
                                                <label class="radio radio-solid disableClick" for="isLoanPaymentAmtSMP">
                                                    <div class="radio enableClick">
                                                        <input type="radio"
                                                               name="isLoanPaymentAmt"
                                                               id="isLoanPaymentAmtSMP"
                                                               value="SMP"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               onclick="updatePaymentTooltip('SMP');updateLoanDetail(); "
                                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $ACFsecArr, 'opt' => 'i']); ?> <?php echo Strings::isChecked('SMP', $isLoanPaymentAmt); ?>>
                                                        <span></span>
                                                        <div>Set Manual Payment</div>
                                                    </div>
                                                </label>
                                            <?php } ?>
                                        </div>
                                    <?php } else { ?>
                                        <h5><?php echo $isLoanPaymentAmt ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class="col-md-3 lien1Payment_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="lien1Payment"><?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?></label>
                                <div class="col-md-12">
                                    <?php
                                    if ($allowToEdit && $disabledInputForClient) {
                                        ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text" <?php echo($isLoanPaymentAmt != 'SMP' ? ' readonly ' : ''); ?>
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                   name="lien1Payment"
                                                   id="lien1Payment"
                                                   onblur="currencyConverter(this, this.value);updateLoanDetail();monthlyInterestRate(0, -this.value,0)"
                                                   placeholder="0.00"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($totalMonthlyPayment) ?>"
                                                   autocomplete="off"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'lien1Payment', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?>/>
                                            <div class="input-group-append tooltipClass"
                                                 id="totalMonthlyPaymentTooltip"
                                                 data-html="true"
                                                 data-formula="<?php echo HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltip; ?>"
                                                 title="<?php echo HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltipWithValues ? (HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltip . '<hr>' . HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltipWithValues) : HMLOLoanTermsCalculation::$totalMonthlyPaymentTooltip; ?>">
                                                  <span class="input-group-text">
                                                      <i class="fa fa-info-circle text-primary "></i>
                                                  </span>
                                            </div>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . $totalMonthlyPayment . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div class="clear"></div>
                        <div
                                class="col-md-3 accrualType_disp <?php echo loanForm::showField('accrualType'); ?>">
                            <?php echo loanForm::label(
                                'accrualType',
                                'col-md-12 ',
                                '',
                                loanForm::changeLog(
                                    LMRequest::File()->getTblFileHMLOPropInfo_by_fileID()->HMLOPID,
                                    'accrualType',
                                    tblFileHMLOPropInfo::class,
                                    'Accrual Type'
                                )
                            ); ?>
                            <div class="col-md-12">
                                <?php if ($allowToEdit) { ?>
                                    <select name="accrualType"
                                            class="form-control input-sm accrualTypeClass <?php echo BaseHTML::fieldAccess(['fNm' => 'accrualType', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                            id="accrualType"
                                            onchange="loanCalculation.updateAccrualType(this);updateLoanDetail();"
                                            tabindex="<?php echo $tabIndex++; ?>">
                                        <?php foreach (accrualTypes::$accrualTypes as $k => $v) { ?>
                                            <option value="<?php echo $k; ?>" <?php if ($accrualType == $k) echo ' selected '; ?> >
                                                <?php echo $v; ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                <?php } else {
                                    echo '<h7>' . (accrualTypes::$accrualTypes[$accrualType] ?? '') . '</h7>';
                                } ?>
                            </div>
                        </div>

                        <div
                                class="col-md-3 isTaxesInsEscrowed_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                loanForm::pushSectionID('BORF');
                                echo loanForm::label('isTaxesInsEscrowed', 'col-md-12 ');
                                loanForm::popSectionID();
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid " for="isTaxesInsEscrowedYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                       name="isTaxesInsEscrowed" id="isTaxesInsEscrowedYes" value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('Yes', Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo')); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid " for="isTaxesInsEscrowedNo">
                                                <input type="radio" name="isTaxesInsEscrowed" id="isTaxesInsEscrowedNo"
                                                       value="No"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                    <?php echo Strings::isChecked('No', Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo')); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'isTaxesInsEscrowed', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>><span></span>No
                                            </label></div>
                                    <?php } else { ?>
                                        <b><?php echo Strings::showField('isTaxesInsEscrowed', 'fileHMLONewLoanInfo') ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class="col-md-3 isTaxesInsEscrowedDispOpt taxes1_disp
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'taxes1', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                loanForm::pushSectionID('BORF');
                                echo loanForm::label('taxes1', 'col-md-12 ');
                                loanForm::popSectionID();
                                ?>
                                <div class="col-md-12">
                                    <?php
                                    if ($allowToEdit) {
                                        ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'taxes1', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?>"
                                                   name="taxes1" id="taxes1"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($taxes1) ?>"
                                                   autocomplete="off"
                                                   onblur="currencyConverter(this, this.value);calculateHMLORealEstateTaxes(this.value); updateLoanDetail();"
                                                   placeholder="0.00"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php if ($typeOfHMLOLoanRequesting == 'Blanket Loan') {
                                                echo 'readonly';
                                            } ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'taxes1', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?> />
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . $taxes1 . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class="col-md-3 isTaxesInsEscrowedDispOpt insurance1_disp
                                <?php echo BaseHTML::fieldAccess(['fNm' => 'insurance1', 'sArr' => $BORFsecArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <?php
                                loanForm::pushSectionID('BORF');
                                echo loanForm::label('insurance1', 'col-md-12 ');
                                loanForm::popSectionID();
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'insurance1', 'sArr' => $BORFsecArr, 'opt' => 'M']); ?> annualPremiumClass"
                                                   name="annualPremium" id="annualPremium"
                                                   placeholder="0.00"
                                                   onblur="currencyConverter(this, this.value);updateLoanDetail();mirrorField.mirrorFieldValues('annualPremium','spcf_annualPremium');"
                                                   placeholder="0.00"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('annualPremium', 'fileHMLOPropertyInfo')) ?>"
                                                   autocomplete="off"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'insurance1', 'sArr' => $BORFsecArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal(Strings::showField('annualPremium', 'fileHMLOPropertyInfo')) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <?php if ($publicUser != 1) { ?>
                            <div class="col-md-3 isTaxesInsEscrowedDispOpt netMonthlyPayment_disp
                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'netMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                                <div class="row form-group">
                                    <label class="col-md-12 font-weight-bold"
                                           for="netMonthlyPayment"><?php echo BaseHTML::fieldAccess(['fNm' => 'netMonthlyPayment', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                        <i class="fa fa-info-circle tooltipClass text-primary ml-2"
                                           data-html="true"
                                           id="netMonthlyPaymentTooltip"
                                           data-formula="Monthly Payment (PITIA) = PITIA = Monthly Payment + (Annual Property Tax + Annual Insurance Policy Premium + HOA Fees) / 12."
                                           title="Monthly Payment (PITIA) = PITIA = Monthly Payment + (Annual Property Tax + Annual Insurance Policy Premium + HOA Fees) / 12. <hr> <?php echo HMLOLoanTermsCalculation::$netMonthlyPaymentTooltip ?>  "></i>
                                    </label>
                                    <div class="col-md-12 h4">
                                        $
                                        <span id="netMonthlyPayment"><?php echo Currency::formatDollarAmountWithDecimal($netMonthlyPayment); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <span class="hide" id="netMonthlyPayment"><?php echo $netMonthlyPayment; ?></span>
                        <?php } ?>
                        <?php
                        //}  // Hide This row for (Pivotal #*********).
                        ?>
                        <div class="clearfix"></div>
                        <div
                                class=" col-md-3 isBlanketLoanDiv isBlanketLoan_disp <?php echo loanForm::showField('isBlanketLoan'); ?>"
                                style="<?php echo $isBlanketLoanDiv ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('isBlanketLoan', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid " for="isBlanketLoanYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="isBlanketLoan" id="isBlanketLoanYes" value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $isBlanketLoan); ?>
                                                       onclick="hideAndShowBlanketLoan(this.value, 'isBlanketLoan');checktheMirrorFields(this.value,this.name);" <?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid" for="isBlanketLoanNo">
                                                <input type="radio" name="isBlanketLoan" id="isBlanketLoanNo" value="No"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $isBlanketLoan); ?>
                                                       onclick="hideAndShowBlanketLoan(this.value, 'isBlanketLoan');checktheMirrorFields(this.value,this.name);" <?php echo BaseHTML::fieldAccess(['fNm' => 'isBlanketLoan', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                            </label></div>
                                    <?php } else {
                                        echo '<b>' . $isBlanketLoan . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 isBlanketLoan isBlanketLoanDiv noOfPropertiesAcquiring_disp <?php echo loanForm::showField('noOfPropertiesAcquiring'); ?>"
                                style="<?php echo $isBlanketLoanDispOpt ?>">
                            <div class="row form-group isBlanketLoan" style="<?php echo $isBlanketLoanDispOpt ?>">
                                <?php echo loanForm::label('noOfPropertiesAcquiring', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="number"
                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfPropertiesAcquiring', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               <?php if ($activeTab != 'HMLI') { ?>onchange="mirrornoOfPropertiesAcquiring()" <?php } ?>
                                               name="noOfPropertiesAcquiring" id="noOfPropertiesAcquiring"
                                               value="<?php echo $noOfPropertiesAcquiring ?>" autocomplete="off"
                                               tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfPropertiesAcquiring', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <span class="H5"><?php echo $noOfPropertiesAcquiring ?></span>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 isBlanketLoan isBlanketLoanDiv ownedFreeAndClear_disp <?php echo loanForm::showField('ownedFreeAndClear'); ?>"
                                style="<?php echo $isBlanketLoanDispOpt ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('ownedFreeAndClear', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid " for="ownedFreeAndClearYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'ownedFreeAndClear', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="ownedFreeAndClear" id="ownedFreeAndClearYes" value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $ownedFreeAndClear); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ownedFreeAndClear', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes</label>
                                            <label class="radio radio-solid " for="ownedFreeAndClearNo">

                                                <input type="radio" name="ownedFreeAndClear" id="ownedFreeAndClearNo"
                                                       value="No"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'ownedFreeAndClear', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $ownedFreeAndClear); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ownedFreeAndClear', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                            </label>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . $ownedFreeAndClear . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 isBlanketLoan isBlanketLoanDiv ownedSameEntity_disp <?php echo loanForm::showField('ownedSameEntity'); ?>"
                                style="<?php echo $isBlanketLoanDispOpt ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('ownedSameEntity', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid " for="ownedSameEntityYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="ownedSameEntity" id="ownedSameEntityYes" value="Yes"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $ownedSameEntity); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid " for="ownedSameEntityNo">
                                                <input type="radio" name="ownedSameEntity" id="ownedSameEntityNo"
                                                       value="No"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $ownedSameEntity); ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ownedSameEntity', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                            </label>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . $ownedSameEntity . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class="col-md-3 transactionalFields resalePrice_disp <?php echo loanForm::showField('resalePrice'); ?>"
                                style="<?php echo $transactionalFieldsDispOpt; ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('resalePrice', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'resalePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="resalePrice" id="resalePrice"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($resalePrice) ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   onblur="currencyConverter(this, this.value); "
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'resalePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($resalePrice) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 transactionalFields resaleClosingDate_disp <?php echo loanForm::showField('resaleClosingDate'); ?>"
                                style="<?php echo $transactionalFieldsDispOpt; ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('resaleClosingDate', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend resaleClosingDate">
                                            <span class="input-group-text">
                                                <i class="fa fa-calendar text-primary"></i>
                                            </span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'resaleClosingDate', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                   name="resaleClosingDate" id="resaleClosingDate"
                                                   value="<?php echo $resaleClosingDate ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                   placeholder="MM/DD/YYYY" <?php echo BaseHTML::fieldAccess(['fNm' => 'resaleClosingDate', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . $resaleClosingDate . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div class="clearfix"></div>
                        <div
                                class=" col-md-3 doesPropertyNeedRehabSection propertyNeedRehab_disp <?php echo loanForm::showField('propertyNeedRehab'); ?>"
                                style="<?php echo $doesPropertyNeedRehabSection ?>">
                            <div class="row form-group">
                                <div class="showHidePropertyRehab"
                                     style="<?php echo(loanPropertySummary::showHidePropertyRehabCv3($PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType) ? 'display:none;' : ''); ?>">
                                    <?php echo loanForm::label('propertyNeedRehab',
                                        'col-md-12 ',
                                        '',
                                        loanForm::changeLog(
                                            LMRequest::myFileInfo()->fileHMLOPropertyInfo()->HMLOPID,
                                            'propertyNeedRehab',
                                            \models\lendingwise\tblFileHMLOPropInfo::class,
                                            loanForm::getFieldLabel('propertyNeedRehab')
                                        ),
                                    ); ?>
                                    <div class="col-md-12">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="radio-inline">
                                                <label class="radio radio-solid " for="propertyNeedRehabYes">
                                                    <input type="radio"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="propertyNeedRehab"
                                                           id="propertyNeedRehabYes"
                                                           value="Yes"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                        <?php echo Strings::isChecked('Yes', $propertyNeedRehab); ?>
                                                           onclick="hideAndShowPropertyNeedRehab();"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                                </label>
                                                <label class="radio radio-solid " for="propertyNeedRehabNo">
                                                    <input type="radio"
                                                           name="propertyNeedRehab"
                                                           id="propertyNeedRehabNo"
                                                           value="No"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                        <?php echo Strings::isChecked('No', $propertyNeedRehab); ?>
                                                           onclick="hideAndShowPropertyNeedRehab();"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'propertyNeedRehab', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                                </label></div>
                                        <?php } else {
                                            echo '<b>' . $propertyNeedRehab . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class=" col-md-3 applicantConfirmed_disp <?php echo loanForm::showField('applicantConfirmed'); ?>">
                            <div class="row form-group mt-6">
                                <?php echo loanForm::label('applicantConfirmed', 'col-md-9 ', '',
                                    loanForm::changeLog(
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                        'applicantConfirmed',
                                        \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                        loanForm::label('applicantConfirmed'),
                                    )); ?>
                                <div class="col-md-3">
                                    <?php if ($allowToEdit) { ?>
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($applicantConfirmed == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $applicantConfirmed ?>"
                                                       id="applicantConfirmedId"
                                                       onchange="toggleSwitch('applicantConfirmedId', 'applicantConfirmed','1','0' );"/>
                                                <input type="hidden" name="applicantConfirmed"
                                                       id="applicantConfirmed"
                                                       value="<?php echo $applicantConfirmed ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 transactionalTdFields commercialFieldsTD transactionalTdFieldsNC costBasis_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                                style="<?php echo $commercialFieldsTDNCDispOpt; ?><?php echo $transCommercialFieldsDispOpt; ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="costBasis"><?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="costBasis"
                                                   id="costBasis"
                                                   onblur="currencyConverter(this, this.value);calculateDownPaymentByPercentage();validateMinMaxLoanGuidelines(); calculateCapRate();"
                                                   onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                   placeholder="0.00"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($costBasis); ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'costBasis', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($costBasis) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 homeValue_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $secArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="homeValue"><?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text" name="homeValue"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   id="homeValue"
                                                   onchange="updateLoanDetail(); calculateCORefiLoanAmtByLTVPercentage(); currencyConverter(this, this.value);calculateCapRate();"
                                                   onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($homeValue) ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo($PCID, $activeTab); ?>
                                                   placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'homeValue', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($homeValue) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div
                                class=" col-md-3 aggregateDSCR_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $secArr, 'opt' => 'D']); ?>">
                            <div class="row form-group">
                                <label class="col-md-12 font-weight-bold"
                                       for="aggregateDSCR"><?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $secArr, 'opt' => 'L']); ?></label>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <input type="text" name="aggregateDSCR"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   id="aggregateDSCR"
                                                   onkeyup='return restrictAlphabetsLoanTermsDecimal(this,3)'
                                                   value="<?php echo $aggregateDSCR ?>"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   placeholder="0.000" <?php echo BaseHTML::fieldAccess(['fNm' => 'aggregateDSCR', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . $aggregateDSCR . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 propertyNeedRehabinitialTddisp assessedValue_disp <?php echo loanForm::showField('assessedValue'); ?>"
                                style="<?php echo $doesPropertyNeedRehabDispTDDiv ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('assessedValue', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   name="assessedValue"
                                                   id="assessedValue"
                                                   placeholder="0.00"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($assessedValue) ?>"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assessedValue', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   onblur="currencyConverter(this, this.value);updateLoanDetail();validateMinMaxLoanGuidelines();"
                                                   onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'assessedValue', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($assessedValue) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 propertyNeedRehabinitialTddisp isThisGroundUpConstruction_disp <?php echo loanForm::showField('isThisGroundUpConstruction'); ?>"
                                style="<?php echo $doesPropertyNeedRehabDispTDDiv ?>">
                            <div class="row form-group doesPropertyNeedRehabDispDiv">
                                <?php echo loanForm::label('isThisGroundUpConstruction', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="radio-inline">
                                            <label class="radio radio-solid " for="isThisGroundUpConstructionYes">
                                                <input type="radio"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="isThisGroundUpConstruction"
                                                       id="isThisGroundUpConstructionYes" value="Yes"
                                                       onclick="showAndHideGroupUpFields(this.value)"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $isThisGroundUpConstruction); ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                            </label>
                                            <label class="radio radio-solid " for="isThisGroundUpConstructionNo">
                                                <input type="radio" name="isThisGroundUpConstruction"
                                                       id="isThisGroundUpConstructionNo" value="No"
                                                       onclick="showAndHideGroupUpFields(this.value)"
                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $isThisGroundUpConstruction); ?>
                                                    <?php echo BaseHTML::fieldAccess(['fNm' => 'isThisGroundUpConstruction', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                            </label></div>
                                    <?php } else {
                                        echo '<b>' . $isThisGroundUpConstruction . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div style="<?php echo $groundUpDispTDDiv; ?>"
                             class="  col-md-3 groundUpFields propertyNeedRehabinitialTddisp lotStatus_disp <?php echo loanForm::showField('lotStatus'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('lotStatus', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <select
                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lotStatus', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="lotStatus" id="lotStatus"
                                                TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'lotStatus', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                            <option value=""> - Select -</option>
                                            <?php
                                            for ($i = 0; $i < count($glLotStatus); $i++) {
                                                $sOpt = '';
                                                $sOpt = Arrays::isSelected(trim($glLotStatus[$i]), $lotStatus);
                                                echo "<option value=\"" . trim($glLotStatus[$i]) . "\" " . $sOpt . '>' . trim($glLotStatus[$i]) . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else {
                                        echo '<b>' . $lotStatus . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div
                                class=" col-md-3 groundUpFields propertyNeedRehabinitialTddisp lotPurchasePrice_disp <?php echo loanForm::showField('lotPurchasePrice'); ?>"
                                style="<?php echo $groundUpDispTDDiv; ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('lotPurchasePrice', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'lotPurchasePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="lotPurchasePrice" id="lotPurchasePrice"
                                                   onblur="currencyConverter(this, this.value);"
                                                   onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                   placeholder="0.00"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($lotPurchasePrice); ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'lotPurchasePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($lotPurchasePrice) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <div
                                class=" col-md-3 groundUpFields propertyNeedRehabinitialTddisp currentLotMarket_disp <?php echo loanForm::showField('currentLotMarket'); ?>"
                                style="<?php echo $groundUpDispTDDiv; ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('currentLotMarket', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLotMarket', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="currentLotMarket" id="currentLotMarket"
                                                   onblur="currencyConverter(this, this.value);"
                                                   onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                   placeholder="0.00"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($currentLotMarket); ?>"
                                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLotMarket', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($currentLotMarket) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <div
                                class=" col-md-3 propertyNeedRehabinitialTddisp costSpent_disp <?php echo loanForm::showField('costSpent'); ?>"
                                style="<?php echo $doesPropertyNeedRehabDispTDDiv ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('costSpent', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text" name="costSpent" id="costSpent"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimalZeros($costSpent) ?>"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   onblur="currencyConverter(this, this.value);calculateTotalProjectCost();updateLoanDetail();"
                                                   onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                   TABINDEX="<?php echo $tabIndex++; ?>" <?php echo BaseHTML::fieldAccess(['fNm' => 'costSpent', 'sArr' => $secArr, 'opt' => 'I']); ?>
                                                   autocomplete="off"/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($costSpent) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                        <?php
                        if (isset($glRestrictCustomFields[$PCID])) { //https://www.pivotaltracker.com/story/show/161345107
                            if (in_array('approvedAcquisition', $glRestrictCustomFields[$PCID])) { ?>
                                <div
                                        class=" col-md-3 right approvedAcquisition_disp <?php echo loanForm::showField('approvedAcquisition'); ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('approvedAcquisition', 'col-md-12 '); ?>
                                        <div class="col-md-12">
                                            <?php
                                            if ($allowToEdit) {
                                                $fldAcc = '';
                                                if (in_array($PCID, $glFrlDesLoanAmt)) {
                                                    if ($userRole != 'Manager') {
                                                        $fldAcc = ' readOnly ';
                                                    }
                                                } ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input name="approvedAcquisition" id="approvedAcquisition"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'approvedAcquisition', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           onblur="currencyConverter(this, this.value);"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($approvedAcquisition); ?>"
                                                        <?php echo $fldAcc . ' ' . BaseHTML::fieldAccess(['fNm' => 'approvedAcquisition', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                </div>
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($approvedAcquisition); ?></h5><?php } ?>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                        ?>

                        <div class="clearfix"></div>
                        <?php loanForm::pushSectionID('RCM'); ?>
                        <div
                                class=" col-md-3 transactionalTdFieldsNCDate originalPurchasePrice_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPurchasePrice', 'sArr' => $secArr, 'opt' => 'D']); ?>"
                                style="<?php echo $commercialFieldsTDNDateCDispOpt ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('originalPurchasePrice', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="text"
                                                   class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPurchasePrice', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   name="originalPurchasePrice_mirror" id="originalPurchasePrice_mirror"
                                                   value="<?php echo Currency::formatDollarAmountWithDecimal($originalPurchasePrice) ?>"
                                                   onblur="currencyConverter(this, this.value);populateDualField(this.value, 'originalPurchasePrice');"
                                                   placeholder="0.00" TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'originalPurchasePrice', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                        </div>
                                    <?php } else {
                                        echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($originalPurchasePrice) . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>
                        <?php loanForm::popSectionID(); ?>

                        <div
                                class=" col-md-3 transactionalTdFieldsNCDate originalPurchaseDate_disp <?php echo loanForm::showField('originalPurchaseDate'); ?>"
                                style="<?php echo $commercialFieldsTDNDateCDispOpt ?>">
                            <div class="row form-group">
                                <?php
                                loanForm::pushSectionID('RCM');
                                echo loanForm::label('originalPurchaseDate', 'col-md-12 ');
                                loanForm::popSectionID();
                                ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <div class="input-group-prepend originalPurchaseDate_mirror">
                                        <span class="input-group-text">
                                            <i class="fa fa-calendar text-primary"></i>
                                        </span>
                                            </div>
                                            <input
                                                    class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'assessedValue', 'sArr' => $secArr, 'opt' => 'M']); ?> dateNewClass"
                                                    type="text" name="originalPurchaseDate_mirror"
                                                    id="originalPurchaseDate_mirror" placeholder="MM/DD/YYYY"
                                                    value="<?php echo $originalPurchaseDate ?>"
                                                    TABINDEX="<?php echo $tabIndex++; ?>" autocomplete="off"
                                                    onchange="populateDualField(this.value, 'originalPurchaseDate')" <?php echo BaseHTML::fieldAccess(['fNm' => 'assessedValue', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                        </div>
                                    <?php } else {
                                        echo '<b>' . $originalPurchaseDate . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>


                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-3 <?php echo loanForm::showField('constructionType'); ?>">
                            <div class="form-group ">
                                <?php echo loanForm::label(
                                    'constructionType',
                                    ' ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                        'constructionType',
                                        \models\lendingwise\tblLoanPropertySummary::class,
                                        loanForm::getFieldLabel('constructionType')
                                    ),
                                );
                                echo loanForm::select(
                                    'constructionType',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->constructionType,
                                    glGroundUpConstruction::$constructionType,
                                    ' ',
                                    'chzn-select form-control',
                                    ' ',
                                    'Please Select ' . loanForm::$permissions['constructionType']->fieldLabel
                                ); ?>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('constructionHardCost'); ?>">
                            <div class="form-group ">
                                <?php echo loanForm::label(
                                    'constructionHardCost',
                                    ' ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                        'constructionHardCost',
                                        \models\lendingwise\tblLoanPropertySummary::class,
                                        loanForm::getFieldLabel('constructionHardCost')
                                    ),
                                );
                                echo loanForm::currency(
                                    'constructionHardCost',
                                    LMRequest::$allowToEdit,
                                    '1',
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->constructionHardCost,
                                    null,
                                    'loanInfoV2Form.calculateContingencyAmount();loanInfoV2Form.cloneHardCost();'
                                ); ?>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('constructionSoftCost'); ?>">
                            <div class="form-group ">
                                <?php echo loanForm::label(
                                    'constructionSoftCost',
                                    ' ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                        'constructionSoftCost',
                                        \models\lendingwise\tblLoanPropertySummary::class,
                                        loanForm::getFieldLabel('constructionSoftCost')
                                    ),
                                );
                                echo loanForm::currency(
                                    'constructionSoftCost',
                                    LMRequest::$allowToEdit,
                                    '1',
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->constructionSoftCost,
                                    null,
                                    'loanInfoV2Form.calculateContingencyAmount();loanInfoV2Form.cloneSoftCost();'
                                ); ?>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('contingencyTypeOption'); ?>">
                            <div class="form-group ">
                                <?php echo loanForm::label(
                                    'contingencyTypeOption',
                                    ' ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                        'contingencyTypeOption',
                                        \models\lendingwise\tblLoanPropertySummary::class,
                                        loanForm::getFieldLabel('contingencyTypeOption')
                                    ),
                                );
                                echo loanForm::select(
                                    'contingencyTypeOption',
                                    LMRequest::$allowToEdit,
                                    1,
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption ? LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption : 10,
                                    glGroundUpConstruction::$contingencyType,
                                    'loanInfoV2Form.calculateContingencyAmount();',
                                    'chzn-select form-control',
                                    ' ',
                                    'Please Select ' . loanForm::$permissions['contingencyTypeOption']->fieldLabel
                                );
                                ?>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('contingencyPercentage'); ?>">
                            <div class="form-group ">
                                <?php echo loanForm::label(
                                    'contingencyPercentage',
                                    ' ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                        'contingencyPercentage',
                                        \models\lendingwise\tblLoanPropertySummary::class,
                                        loanForm::getFieldLabel('contingencyPercentage')
                                    ),
                                );
                                echo loanForm::percentage(
                                    'contingencyPercentage',
                                    LMRequest::$allowToEdit,
                                    '1',
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage ? LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage : 10,
                                    '  ',
                                    'loanInfoV2Form.calculateContingencyAmount();',
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption != 'Other',
                                    5);
                                ?>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('contingencyAmount'); ?>">
                            <div class="form-group ">
                                <?php echo loanForm::label(
                                    'contingencyAmount',
                                    ' ',
                                    '',
                                    loanForm::changeLog(
                                        LMRequest::myFileInfo()->getLoanPropertySummary()->id,
                                        'contingencyAmount',
                                        \models\lendingwise\tblLoanPropertySummary::class,
                                        loanForm::getFieldLabel('contingencyAmount')
                                    ),
                                );
                                echo loanForm::currency(
                                    'contingencyAmount',
                                    LMRequest::$allowToEdit,
                                    '1',
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyAmount,
                                    '  ',
                                    'loanInfoV2Form.calculateContingencyPercentage();',
                                    LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyTypeOption != 'Other',
                                    '');
                                ?>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('plansAndPermitsStatus'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('plansAndPermitsStatus', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::select(
                                        'plansAndPermitsStatus',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->plansAndPermitsStatus,
                                        glPlansAndPermitStatus::getGlPlansAndPermitStatus(PageVariables::$PCID),
                                        ' ',
                                        'chzn-select form-control',
                                        ' ',
                                        'Please Select ' . loanForm::$permissions['plansAndPermitsStatus']->fieldLabel
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 <?php echo loanForm::showField('isProjectRequireRezoning'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('isProjectRequireRezoning', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::select(
                                        'isProjectRequireRezoning',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->isProjectRequireRezoning,
                                        glYesNo::$glYesNo,
                                        ' ',
                                        'chzn-select form-control',
                                        ' ',
                                        'Please Select ' . loanForm::$permissions['isProjectRequireRezoning']->fieldLabel
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('anticipatedHoldTime'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('anticipatedHoldTime', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::number(
                                        'anticipatedHoldTime',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->anticipatedHoldTime,
                                        null,
                                        '',
                                        null,
                                        'Months'
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 <?php echo loanForm::showField('anticipatedPlansPermitTimeline'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('anticipatedPlansPermitTimeline', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::number(
                                        'anticipatedPlansPermitTimeline',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->anticipatedPlansPermitTimeline,
                                        null,
                                        '',
                                        null,
                                        'Months'
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 <?php echo loanForm::showField('anticipatedConstructionTimeline'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('anticipatedConstructionTimeline', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::number(
                                        'anticipatedConstructionTimeline',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->anticipatedConstructionTimeline,
                                        null,
                                        '',
                                        null,
                                        'Months'
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-3 <?php echo loanForm::showField('buildingAnalysisOutstanding'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('buildingAnalysisOutstanding', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::select(
                                        'buildingAnalysisOutstanding',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisOutstanding,
                                        tblBuildingAnalysisOutstanding::options(),
                                        '',
                                        ' chzn-select ',
                                        ' ',
                                        'Please Select '.loanForm::getFieldLabel('buildingAnalysisOutstanding')
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('buildingAnalysisNeed'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('buildingAnalysisNeed', 'col-md-12 font-weight-bold label_highlight'); ?>
                                <div class="col-md-12">
                                    <?php
                                    echo loanForm::select(
                                        'buildingAnalysisNeed',
                                        LMRequest::$allowToEdit,
                                        1,
                                        LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisNeed,
                                        tblBuildingAnalysisNeed::options(),
                                        '',
                                        ' chzn-select ',
                                        ' ',
                                        'Please Select '.loanForm::getFieldLabel('buildingAnalysisNeed')
                                    );
                                    ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 <?php echo loanForm::showField('buildingAnalysisDueDate'); ?>">
                            <div class="row form-group">
                                <?php echo loanForm::label('buildingAnalysisDueDate', 'col-md-12 '); ?>
                                <div class="col-md-12">
                                    <?php if ($allowToEdit) {
                                        echo loanForm::date(
                                            'buildingAnalysisDueDate',
                                            $allowToEdit,
                                            $tabIndex++,
                                            LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisDueDate ?? '',
                                            ' dateNewClass '
                                        );
                                    } else {
                                        echo '<b>' . Dates::formatDateWithRE(LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->buildingAnalysisDueDate, 'YMD', 'm/d/Y') . '</b>';
                                    } ?>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>


                <div class=" col-md-12 separator separator-dashed my-2 separator13"></div>

                <!-- <div class="col-md-7">Total Loan Amount Section Start -->

                <!-- Row 3 start -->
                <div class="col-md-12  justify-content-center align-self-center">

                    <table class="table  LWcustomTable table-borderless   table-vertical-center">
                        <tr>
                            <td width="50%">
                                <div class="row  py-4">
                                    <div class="col-md-6  align-self-center">
                                        <span class="d-none"> maxAmtToPutDownClass</span>
                                        <div class=" row commercialTdFields transactionalTdFields transactionalTdFieldsNC maxAmtToPutDown_disp right
                                        <?php echo loanForm::showField('maxAmtToPutDown'); ?>"
                                             style="<?php echo $commercialFieldsTDDispOpt . $commercialFieldsTDNCDispOpt ?>">
                                            <?php echo loanForm::label('maxAmtToPutDown', 'col-md-12 '); ?>
                                            <div class="col-md-12">
                                                <?php if ($allowToEdit) { ?>
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text">$</span>
                                                        </div>
                                                        <input type="text"
                                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'maxAmtToPutDown', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                               name="maxAmtToPutDown"
                                                               id="maxAmtToPutDown"
                                                               onblur="currencyConverter(this, this.value);calculateDownPaymentPercentage(); validateMinMaxLoanGuidelines();"
                                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                               placeholder="0.00"
                                                               value="<?php echo Currency::formatDollarAmountWithDecimal($maxAmtToPutDown) ?>"
                                                               autocomplete="off"
                                                            <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo($PCID, $activeTab); ?>
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               <?php echo LTC2Variables::$readOnlyForLTC2Fields; ?>
                                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'maxAmtToPutDown', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                    </div>
                                                <?php } else {
                                                    echo '<b>$ ' . $maxAmtToPutDown . '</b>';
                                                } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6  align-self-center">
                                        <span class="d-none">commercialTdFieldsClass</span>
                                        <div
                                                class=" row commercialTdFields  transactionalTdFields <?php echo loanForm::showField('downPaymentPercentage'); ?> transactionalTdFieldsNC downPaymentPercentage_disp right"
                                                style="<?php echo $commercialFieldsTDDispOpt . $commercialFieldsTDNCDispOpt ?>">
                                            <?php echo loanForm::label('downPaymentPercentage', 'col-md-12 '); ?>
                                            <div class="col-md-12">
                                                <?php if ($allowToEdit) { ?>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'downPaymentPercentage', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="downPaymentPercentage"
                                                           id="downPaymentPercentage"
                                                           placeholder="0.0"
                                                           onchange="calculateDownPaymentByPercentage(); validateMinMaxLoanGuidelines();"
                                                           TABINDEX="<?php echo $tabIndex++; ?>"
                                                           max="100"
                                                           value="<?php echo round(floatval($downPaymentPercentage), 5) ?>"
                                                           maxlength="8"
                                                        <?php echo LTC2Variables::$readOnlyForLTC2Fields; ?>
                                                        <?php echo glCustomJobForProcessingCompany::readOnlyFieldLoanInfo($PCID, $activeTab); ?>
                                                           onkeyup="validatePercentage(this)"
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'downPaymentPercentage', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                <?php } else {
                                                    echo '<b>' . $downPaymentPercentage . ' %</b>';
                                                } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td width="50%"
                                class="commercialFieldsTD commercialFieldsTDNew  transactionalTdFields transactionalTdFieldsNC  LOCTotalLoanAmt_disp " <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D'])) == 'secShow') { ?> style="background-color: #F3F2D1;" <?php } ?> >
                                <span class="d-none">transactionalTdFieldsClass</span>

                                <div
                                        class="commercialFieldsTD commercialFieldsTDNew  transactionalTdFields <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?> transactionalTdFieldsNC  LOCTotalLoanAmt_disp"
                                        style="<?php echo $commercialFieldsTDDispOpt . ' ' . $commercialFieldsTDNCDispOpt ?>/* background-color: #F3F2D1*/">
                                    <div class="hideInitialLoanAmountCV3 "
                                         style="<?php echo glCustomJobForProcessingCompany::hideInitialLoanAmountCV3($PCID, LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType, $typeOfHMLOLoanRequesting); ?>">
                                        <div class="row py-4">
                                            <?php
                                            loanForm::pushSectionID('ACF');
                                            echo loanForm::label('LOCTotalLoanAmt', 'col-md-4 font-weight-bold justify-content-center align-self-center');
                                            loanForm::popSectionID();
                                            ?>
                                            <div class="col-md-5">
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <span style="border-color: transparent;"
                                                          class="input-group-text form-control input-xl"
                                                          id="acquisitionPriceFinanced">
                                                            <?php echo Currency::formatDollarAmountWithDecimal($acquisitionPriceFinanced) ?>
                                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td width="50%">
                                <div class="hideFieldsForLTC2_RefinanceCategory" style="<?= HMLOLoanTermsCalculation::$hideFieldsForLTC2_RefinanceCategory; ?>">
                                    <span class="d-none">rehabConsClsClass</span>
                                    <div class=" rehabConsCls lineOfCreditProp LTVPercentageDisp  <?php echo loanForm::showField('ltv'); ?>"
                                            style="<?php echo $rehabConsCls ?><?php echo $lineOfCreditProp; ?><?php echo HMLOLoanTermsCalculation::$LTVPercentageDisp; ?>">
                                        <div class="row  py-4 ">
                                            <?php echo loanForm::label('ltv', 'col-md-4 font-weight-bold justify-content-center align-self-center'); ?>
                                            <div class="col-md-4 col-md-offset-4">
                                                <?php if ($allowToEdit) { ?>
                                                    <input type="text"
                                                           name="CORefiLTVPercentage"
                                                           id="CORefiLTVPercentage"
                                                           onchange="calculateCORefiLoanAmtByLTVPercentage();"
                                                           TABINDEX="<?php echo $tabIndex++; ?>"
                                                           class="form-control input-sm <?php echo BaseHTML::checkMan($fldArr, $fileTab) ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'ltv', 'sArr' => $secArr, 'opt' => 'M']); ?> "
                                                           value="<?php echo $CORefiLTVPercentage ?>"
                                                           maxlength="8"
                                                           onkeyup="validatePercentage(this)" <?php echo BaseHTML::fieldAccess(['fNm' => 'ltv', 'sArr' => $secArr, 'opt' => 'I']); ?> >
                                                <?php } else {
                                                    echo '<b> ' . $CORefiLTVPercentage . ' %</b>';
                                                } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td width="50%" <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D'])) == 'secShow') { ?> style="background-color: #F3F2D1;" <?php } ?>>
                                <span class="d-none">LOCTotalLoanAmt_dispClass</span>
                                <div
                                        class="rehabConsCls lineOfCreditProp   LOCTotalLoanAmt_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                        style="<?php echo $rehabConsCls ?><?php echo $lineOfCreditProp ?>">
                                    <div class="row py-4">
                                        <?php loanForm::pushSectionID('ACF');
                                        echo loanForm::label(
                                            'LOCTotalLoanAmt',
                                            'col-md-4 font-weight-bold justify-content-center align-self-center',
                                            'A. Input the Rehab/Construction Cost followed by the Rehab/Construction% Financed or <br>
                                                    B. Input the Rehab/Construction Cost followed by the Rehab/Construction Cost Financed',
                                            loanForm::changeLog(
                                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                'CORTotalLoanAmt',
                                                tblFileHMLONewLoanInfo::class,
                                                'Initial Loan Amount',
                                            )
                                        );
                                        loanForm::popSectionID(); ?>
                                        <div class="col-md-5">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                           name="CORTotalLoanAmt"
                                                           id="CORTotalLoanAmt"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($CORTotalLoanAmt) ?>"
                                                           onblur="currencyConverter(this, this.value);calculateCORefiLTVPercentage();"
                                                           placeholder="0.00"
                                                           TABINDEX="<?php echo $tabIndex++; ?>"
                                                           autocomplete="off"
                                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'LOCTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?>
                                                            <?= HMLOLoanTermsCalculation::$readyOnlyFieldsForLTC2_RefinanceCategory; ?>
                                                    />
                                                </div>
                                            <?php } else {
                                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($CORTotalLoanAmt) . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td width="50%">
                                <div class="row  ">
                                    <span class="d-none">doesPropertyNeedRehabDispDivCLass1</span>
                                    <?php
                                    //        echo loanForm::showField('rehabCost',Strings::showField('rehabCost', 'fileHMLOInfo') ,true);
                                    //        echo loanForm::showFieldBasedOnValue('rehabCost',Strings::showField('rehabCost', 'fileHMLOInfo'));
                                    ?>
                                    <div class="doesPropertyNeedRehabDispDiv  col-md-6
                                    <?php echo loanForm::showField('rehabCost', Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')), !$publicUser ? true : false); ?> right "
                                         style="<?php echo $doesPropertyNeedRehabDispDiv ?>">
                                        <div class="row py-4">
                                            <?php echo loanForm::label('rehabCost',
                                                'col-md-12 font-weight-bold justify-content-center align-self-center',
                                                '',
                                                loanForm::changeLog(
                                                    LMRequest::myFileInfo()->fileHMLOInfo()->HMLOID,
                                                    'rehabCost',
                                                    \models\lendingwise\tblFileHMLO::class,
                                                    loanForm::getFieldLabel('rehabCost')
                                                )
                                            ); ?>
                                            <div class="col-md-12">
                                                <?php if ($allowToEdit) { ?>
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text">$</span>
                                                        </div>
                                                        <input type="text"
                                                               class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabCost', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                               name="rehabCost" id="rehabCost"
                                                               value="<?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')) ?>"
                                                               onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               onblur="currencyConverter(this, this.value);calculatePercentageRehabCostFinanced(); validateMinMaxLoanGuidelines();"
                                                               placeholder="0.00"
                                                            <?php
                                                            if (!(floatval(Strings::showField('rehabCost', 'fileHMLOInfo')) > 0
                                                                && !loanForm::isVisible('rehabCost'))) {
                                                                echo BaseHTML::fieldAccess(['fNm' => 'rehabCost', 'sArr' => $secArr, 'opt' => 'I']);
                                                            } ?> />
                                                    </div>
                                                <?php } else { ?>
                                                    <b>$ <?php echo Currency::formatDollarAmountWithDecimal(Strings::showField('rehabCost', 'fileHMLOInfo')) ?></b>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="d-none">doesPropertyNeedRehabDispDivClass2</span>
                                    <div
                                            class="doesPropertyNeedRehabDispDiv  col-md-6 <?php echo loanForm::showField('rehabCostPercentageFinanced', $rehabCostPercentageFinanced, !$publicUser ? true : false); ?> right rehabCostPercentageFinanced_disp"
                                            style="<?php echo $doesPropertyNeedRehabDispTDDiv ?>">
                                        <div class="row py-4">
                                            <?php echo loanForm::label('rehabCostPercentageFinanced',
                                                'col-md-12 font-weight-bold justify-content-center align-self-center',
                                                '',
                                                loanForm::changeLog(
                                                    LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->HMLIID,
                                                    'rehabCostPercentageFinanced',
                                                    \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                                    loanForm::getFieldLabel('rehabCostPercentageFinanced')
                                                )
                                            ); ?>
                                            <div class="col-md-12 ">
                                                <?php if ($allowToEdit) { ?>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'rehabCostPercentageFinanced', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="rehabCostPercentageFinanced"
                                                           id="rehabCostPercentageFinanced"
                                                           onchange="calculateRehabCostFinancedByPercentage(); validateMinMaxLoanGuidelines();"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           value="<?php echo Strings::formatNumber($rehabCostPercentageFinanced,10); ?>"
                                                           onkeyup="validatePercentage(this)"
                                                        <?php
                                                        if (!(floatval(Strings::replaceCommaValues($rehabCostPercentageFinanced)) > 0
                                                            && !loanForm::isVisible('rehabCostPercentageFinanced'))) {
                                                            echo BaseHTML::fieldAccess(['fNm' => 'rehabCostPercentageFinanced', 'sArr' => $secArr, 'opt' => 'I']);
                                                        }
                                                        ?>>
                                                <?php } else { ?>
                                                    <b><?php echo $rehabCostPercentageFinanced ?> %</b>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>

                            <?php
                            loanForm::pushSectionID('ACF');
                            // echo loanForm::showField('rehabCostFinanced',$rehabCostFinanced,true);
                            ?>
                            <td width="50%" class=" doesPropertyNeedRehabDispDiv rehabCostFinanced_disp "
                                <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'rehabCostFinanced', 'sArr' => $ACFsecArr, 'opt' => 'D'])) == 'secShow') { ?> style="background-color: #F3F2D1;" <?php } ?>>
                                <span class="d-none">doesPropertyNeedRehabDispDivClass3</span>
                                <div class="doesPropertyNeedRehabDispDiv
                                        <?php echo loanForm::showField('rehabCostFinanced', $rehabCostFinanced, !$publicUser ? true : false); ?> rehabCostFinanced_disp rehabCostFinancedDiv"
                                     style="<?php echo $doesPropertyNeedRehabDispDiv; ?>">
                                    <div class="row py-4">
                                        <?php
                                        echo loanForm::label2(
                                            'rehabCostFinanced',
                                            'col-md-4 font-weight-bold justify-content-center align-self-center',
                                            'The recommended way to calculate the amount for this field is to either:
                                            <br><br>
A. Input the Rehab/Construction Cost followed by the Rehab/Construction% Financed <br>
<b>OR</b> <br>
B. Input the Rehab/Construction Cost followed by the Rehab/Construction Cost Financed',
                                            loanForm::changeLog(
                                                LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->HMLIID,
                                                'rehabCostFinanced',
                                                \models\lendingwise\tblFileHMLONewLoanInfo::class,
                                                loanForm::getFieldLabel('rehabCostFinanced')
                                            )
                                        );
                                        ?>
                                        <div class="col-md-5">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input class="form-control input-sm"
                                                           type="text"
                                                           name="rehabCostFinanced"
                                                           id="rehabCostFinanced"
                                                           placeholder="0.00"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           onkeyup="return restrictAlphabetsLoanTerms(this)"
                                                           onblur="currencyConverter(this, this.value);calculateRehabValues();"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($rehabCostFinanced); ?>"
                                                        <?php
                                                        if (!(floatval(Strings::replaceCommaValues($rehabCostFinanced)) > 0
                                                            && !loanForm::isVisible('rehabCostFinanced'))) {
                                                            echo BaseHTML::fieldAccess(['fNm' => 'rehabCostFinanced', 'sArr' => $ACFsecArr, 'opt' => 'I']);
                                                        } ?>
                                                           autocomplete="off">
                                                    <!--                                                <span class="input-group-text form-control input-sm"-->
                                                    <!--                                                      id="rehabCostFinanced"-->
                                                    <!--                                                      name="rehabCostFinanced">-->
                                                    <!--                                                --><?php //echo Currency::formatDollarAmountWithDecimal($rehabCostFinanced); ?>
                                                    <!--                                            </span>-->
                                                </div>
                                            <?php } else { ?>
                                                <h5><?php echo Currency::formatDollarAmountWithDecimal($rehabCostFinanced); ?></h5>
                                            <?php } ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <?php loanForm::popSectionID(); ?>
                        </tr>

                        <tr>
                            <td width="50%">
                                <?php if ($publicUser != 1) { ?>
                                    <span class="d-none">haveInterestreserveClass</span>
                                    <div
                                            class="row  py-4 <?php echo loanForm::showField('haveInterestreserve'); ?>">
                                        <div class="col-md-6">
                                            <?php echo loanForm::label('haveInterestreserve', ' font-weight-bold justify-content-center align-self-center'); ?>
                                            <div class=" align-self-center">
                                                <?php if ($allowToEdit) { ?>
                                                    <div class="radio-inline">
                                                        <label class="radio radio-solid "
                                                               for="haveInterestreserveYes">
                                                            <input type="radio"
                                                                   class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                   name="haveInterestreserve"
                                                                   id="haveInterestreserveYes"
                                                                   value="Yes"
                                                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('Yes', $haveInterestreserve); ?>
                                                                   onclick="showAndHideInterestReserve(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>Yes
                                                        </label>
                                                        <label class="radio radio-solid "
                                                               for="haveInterestreserveNo">
                                                            <input type="radio" name="haveInterestreserve"
                                                                   id="haveInterestreserveNo"
                                                                   value="No"
                                                                   tabindex="<?php echo $tabIndex++; ?>" <?php echo Strings::isChecked('No', $haveInterestreserve); ?>
                                                                   onclick="showAndHideInterestReserve(this.value);" <?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'I']); ?>><span></span>No
                                                        </label></div>
                                                <?php } else { ?>
                                                    <h5><?php echo $haveInterestreserve; ?></h5>
                                                <?php } ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="addToTotalProjectValueClass"
                                                 style="<?php echo(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->haveInterestreserve != 'Yes' ? 'display:none;' : ''); ?>">
                                                <label class="font-weight-bold justify-content-center align-self-center"
                                                       for="addToTotalProjectValue">
                                                    Add to Total Project Cost?
                                                </label>
                                                <div class="align-self-center">
                                                    <?php if ($allowToEdit) { ?>
                                                        <div class="radio-inline">
                                                            <label class="radio radio-solid "
                                                                   for="addToTotalProjectValueYes">
                                                                <input type="radio"
                                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="addToTotalProjectValue"
                                                                       id="addToTotalProjectValueYes"
                                                                       value="Yes"
                                                                       onclick="calculateTotalProjectCost();"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                    <?php echo Strings::isChecked('Yes', LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->addToTotalProjectValue); ?>
                                                                ><span></span>Yes
                                                            </label>
                                                            <label class="radio radio-solid "
                                                                   for="addToTotalProjectValueNo">
                                                                <input type="radio"
                                                                       class="<?php echo BaseHTML::fieldAccess(['fNm' => 'haveInterestreserve', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                       name="addToTotalProjectValue"
                                                                       id="addToTotalProjectValueNo"
                                                                       value="No"
                                                                       onclick="calculateTotalProjectCost();"
                                                                       tabindex="<?php echo $tabIndex++; ?>"
                                                                    <?php echo Strings::isChecked('No', LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->addToTotalProjectValue); ?>
                                                                ><span></span>No
                                                            </label>
                                                        </div>
                                                    <?php } else { ?>
                                                        <h5><?php echo LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->addToTotalProjectValue; ?></h5>
                                                    <?php } ?>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                <?php } ?>
                            </td>
                            <td width="50%" <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'prepaidInterestReserve', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') { ?> style="background-color: #F3F2D1;" <?php } ?>
                                class="px-0 haveInterestreserveDiv ">
                                <?php if ($publicUser != 1) { ?>
                                    <span class="d-none">2 haveInterestreserveDivClass</span>
                                    <div
                                            class=" haveInterestreserveDiv <?php echo loanForm::showField('prepaidInterestReserve'); ?>">
                                        <div class="row py-4">
                                            <label
                                                    class="col-md-4 font-weight-bold justify-content-center align-self-center"
                                                    for="prepaidInterestReserve"><?php echo BaseHTML::fieldAccess(['fNm' => 'prepaidInterestReserve', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                                <i id="prepaidInterestReserveTip" data-html="true"
                                                   class="fa fa-info-circle text-primary tooltipClass"
                                                   title="If financed this will increase the total loan amount. If not financed then this will not increase the total loan amount."></i>
                                            </label>
                                            <div class="col-md-5">
                                                <?php if ($allowToEdit) { ?>
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text">$</span>
                                                        </div>
                                                        <input class="form-control input-sm" type="text"
                                                               name="prepaidInterestReserve"
                                                               id="prepaidInterestReserve"
                                                               placeholder="0.00"
                                                               tabindex="<?php echo $tabIndex++; ?>"
                                                               onblur="currencyConverter(this, this.value);
                                                               updateLoanDetail();validateMinMaxLoanGuidelines();"
                                                               onkeyup="return restrictAlphabetsLoanTerms(this)"
                                                               value="<?php echo Currency::formatDollarAmountWithDecimal($prepaidInterestReserve); ?>"
                                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'prepaidInterestReserve', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                    </div>
                                                <?php } else { ?>
                                                    <h5><?php echo $prepaidInterestReserve; ?></h5>
                                                <?php } ?>

                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfMonthsPrepaid', 'sArr' => $secArr, 'opt' => 'L']); ?>
                                                        </span>
                                                    </div>
                                                    <?php if ($allowToEdit) { ?>
                                                        <input
                                                                class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfMonthsPrepaid', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                                type="number"
                                                                name="noOfMonthsPrepaid"
                                                                id="noOfMonthsPrepaid"
                                                                onchange="updateLoanDetail();calculateSimpleARVPercentage('simpleARV', '');"
                                                                tabindex="<?php echo $tabIndex++; ?>"
                                                                value="<?php echo $noOfMonthsPrepaid; ?>"
                                                                placeholder="# Months"
                                                                max="12"
                                                                autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'noOfMonthsPrepaid', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                                    <?php } else { ?>
                                                        <h5><?php echo $noOfMonthsPrepaid; ?></h5>
                                                    <?php } ?>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                <?php } ?>
                            </td>
                        </tr>


                        <tr>
                            <td width="50%">
                                <span class="d-none">propertyNeedRehabinitialTddispClass</span>
                                <div
                                        class="  propertyNeedRehabinitialTddisp  initialAdvance_disp <?php echo loanForm::showField('initialAdvance'); ?>"
                                        style="<?php echo $doesPropertyNeedRehabDispTDDiv ?>">
                                    <div class="row  py-4">
                                        <?php echo loanForm::label('initialAdvance', 'col-md-6 font-weight-bold justify-content-center align-self-center'); ?>
                                        <div class="col-md-6 align-self-center">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'initialAdvance', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           placeholder="0.00"
                                                           name="initialAdvance" id="initialAdvance"
                                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                                           onkeyup="return restrictAlphabetsLoanTerms(this);"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($initialAdvance); ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>"
                                                           placeholder="0.00" <?php echo BaseHTML::fieldAccess(['fNm' => 'initialAdvance', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                    <div class="input-group-append ">
                                                                <span class="input-group-text">
                                                                <i
                                                                        class="fa fa-info-circle tooltipClass text-primary"
                                                                        data-html="true"
                                                                        title="Initial Advance is the amount of money provided at closing for the Rehab or Construction. It is considered the 1st draw as well."></i></span>
                                                    </div>
                                                </div>
                                            <?php } else {
                                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($initialAdvance) . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td width="50%" <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'payOffMortgage1', 'sArr' => $secArr, 'opt' => 'D'])) == 'secShow') { ?> style="background-color: #F3F2D1;" <?php } ?>>
                                <span class="d-none">payOffMortgage1_dispClass</span>

                                <div
                                        class="  cashOutDiv payOffMortgage1_disp <?php echo loanForm::showField('payOffMortgage1'); ?>"
                                        style="<?php echo $cashOutDiv ?>">
                                    <div class="row py-4">
                                        <?php echo loanForm::label('payOffMortgage1', 'col-md-4 font-weight-bold justify-content-center align-self-center'); ?>
                                        <div class="col-md-5">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage1', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           placeholder="0.00"
                                                           name="payOffMortgage1" id="payOffMortgage1"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimalZeros($payOffMortgage1) ?>"
                                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                                           placeholder="0.00"
                                                           TABINDEX="<?php echo $tabIndex++; ?>"
                                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage1', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                </div>
                                            <?php } else {
                                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimalZeros($payOffMortgage1) . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>

                                <div
                                        class=" cashOutDiv payOffMortgage2_disp <?php echo loanForm::showField('payOffMortgage2'); ?>"
                                        style="<?php echo $cashOutDiv ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('payOffMortgage2', 'col-md-4 font-weight-bold justify-content-center align-self-center'); ?>
                                        <div class="col-md-5">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage2', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="payOffMortgage2" id="payOffMortgage2"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimalZeros($payOffMortgage2) ?>"
                                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                                           placeholder="0.00"
                                                           TABINDEX="<?php echo $tabIndex++; ?>"
                                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffMortgage2', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                                </div>
                                            <?php } else {
                                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimalZeros($payOffMortgage2) . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>

                                <div
                                        class=" cashOutDiv payOffOutstandingTaxes_disp <?php echo loanForm::showField('payOffOutstandingTaxes'); ?>"
                                        style="<?php echo $cashOutDiv ?>">
                                    <div class="row form-group">
                                        <?php echo loanForm::label('payOffOutstandingTaxes', 'col-md-4 font-weight-bold justify-content-center align-self-center'); ?>
                                        <div class="col-md-5">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffOutstandingTaxes', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                           name="payOffOutstandingTaxes"
                                                           id="payOffOutstandingTaxes"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimalZeros($payOffOutstandingTaxes) ?>"
                                                           onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                                           placeholder="0.00"
                                                           TABINDEX="<?php echo $tabIndex++; ?>"
                                                           autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'payOffOutstandingTaxes', 'sArr' => $secArr, 'opt' => 'I']); ?> />
                                                </div>
                                            <?php } else {
                                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimalZeros($payOffOutstandingTaxes) . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>

                                <?php loanForm::pushSectionID('ACF'); ?>
                                <div class="  closingCostFinanced_disp <?php echo loanForm::showField('closingCostFinanced', $closingCostFinanced, !$publicUser ? true : false); ?>">
                                    <div class="row form-group">
                                        <?php
                                        echo loanForm::label2(
                                            'closingCostFinanced',
                                            'col-md-4 font-weight-bold justify-content-center align-self-center',
                                            '',
                                            loanForm::changeLog(
                                                LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->HMLIID,
                                                'closingCostFinanced',
                                                tblFileHMLONewLoanInfo::class,
                                                'Closing Costs Financed',
                                            )
                                        );
                                        ?>
                                        <div class="col-md-5">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text"
                                                           class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinanced', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                           name="closingCostFinanced"
                                                           id="closingCostFinanced"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($closingCostFinanced) ?>"
                                                           TABINDEX="<?php echo $tabIndex++; ?>"
                                                           autocomplete="off"
                                                           onblur="currencyConverter(this, this.value);updateLoanDetail();validateMinMaxLoanGuidelines();"
                                                           onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                           placeholder="0.00"
                                                        <?php
                                                        if (!(floatval($closingCostFinanced) > 0
                                                            && !loanForm::isVisible('closingCostFinanced'))) {
                                                            echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinanced', 'sArr' => $ACFsecArr, 'opt' => 'I']);
                                                        }

                                                        //echo BaseHTML::fieldAccess(['fNm' => 'closingCostFinanced', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?>/>
                                                </div>
                                            <?php } else {
                                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($closingCostFinanced) . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <?php loanForm::popSectionID(); ?>


                        <tr>
                            <td width="50%">
                                <div
                                        class="  propertyNeedRehabinitialTddisp totalDrawsFunded align-self-center totalDrawsFunded_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'totalDrawsFunded', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                        style="<?php echo $doesPropertyNeedRehabDispTDDiv; ?>">
                                    <div class="row">
                                        <?php loanForm::pushSectionID('ACF');
                                        echo loanForm::label2(
                                            'totalDrawsFunded',
                                            'col-md-6 justify-content-center align-self-center'
                                        );
                                        loanForm::popSectionID(); ?>
                                        <div class="col-md-6 align-self-center">
                                            <h5>$
                                                <span
                                                        id="totalDrawsFunded"><?php echo Currency::formatDollarAmountWithDecimal($totalDrawsFunded) ?></span>
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                            </td>

                            <td width="50%"
                                class="propertyNeedRehabinitialTddisp  currentLoanBalance  currentLoanBalance_disp"
                                <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'currentLoanBalance', 'sArr' => $ACFsecArr, 'opt' => 'D'])) == 'secShow') { ?> style="background-color: #F3F2D1;" <?php } ?>>
                                <span class="d-none">propertyNeedRehabinitialTddispClass</span>
                                <div
                                        class="   propertyNeedRehabinitialTddisp  currentLoanBalance  currentLoanBalance_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLoanBalance', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                        style="<?php if ($doesPropertyNeedRehabDispTDDiv == 'display: flex;') echo 'display: block;'; ?>">
                                    <div class="row py-4">
                                        <label class="col-md-4 font-weight-bold justify-content-center align-self-center"
                                               for="currentLoanBalance">
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'currentLoanBalance', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                            <i id="currentLoanBalanceTooltip"
                                               data-html="true"
                                               class="fa fa-info-circle text-primary popoverClass"
                                               title="<?php echo BaseHTML::fieldAccess(['fNm' => 'currentLoanBalance', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>"
                                               data-formula="<?php echo HMLOLoanTermsCalculation::$currentLoanBalanceTooltip; ?>"
                                               data-content="<?php echo HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues ? (HMLOLoanTermsCalculation::$currentLoanBalanceTooltip . '<hr>' . HMLOLoanTermsCalculation::$currentLoanBalanceTooltipWithValues) : HMLOLoanTermsCalculation::$currentLoanBalanceTooltip; ?>"></i>
                                        </label>
                                        <div class="col-md-5">
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <span class="form-control"
                                                      id="currentLoanBalance"
                                                      name="currentLoanBalance"><?php echo Currency::formatDollarAmountWithDecimal($currentLoanBalance) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>


                        <tr>

                            <td width="50%" <?php
                            /* Customization for PCID = 4666 -  Notes Email - 33735 Story */
                            if ($PCID == 4666 && $publicUser == 1) { ?> class="hide" <?php } ?>></td>

                            <td width="50%"
                                class="LOCTotalLoanAmtHide " <?php if (trim(BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D'])) == 'secShow') { ?> style="background-color: #F3F2D1;" <?php } ?>>
                                <span class="d-none">CORTotalLoanAmt_disp</span>
                                <div
                                        class=" LOCTotalLoanAmtHide <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                        style="<?php echo $LOCTotalLoanAmtHideDispOpt ?>/*background-color: #F3F2D1*/">
                                    <div class="row py-4 ">
                                        <label
                                                class="col-md-4  font-weight-bold justify-content-center align-self-center"
                                                for="totalLoanAmount1">
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                            <i id="tLAToolTip"
                                               data-html="true"
                                               class="fa fa-info-circle text-primary ml-2 popoverClass <?php
                                               /* Customization for PCID = 4666 -  Notes Email - 33735 Story */
                                               if ($PCID == 4666) {
                                                   echo 'hide';
                                               } ?>"
                                               title="Total Loan Amount"
                                               data-formula="<?php echo HMLOLoanTermsCalculation::$tLAToolTip; ?>"
                                               data-content="<?php echo HMLOLoanTermsCalculation::$tLAToolTipWithValues ? (
                                                   HMLOLoanTermsCalculation::$tLAToolTip . '<hr>' . HMLOLoanTermsCalculation::$tLAToolTipWithValues) : HMLOLoanTermsCalculation::$tLAToolTip ?>"></i>
                                        </label>
                                        <div class="col-md-5">
                                            <?php if ($allowToEdit) { ?>
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text"
                                                           name="totalLoanAmount"
                                                           placeholder="0.00"
                                                           id="totalLoanAmount1"
                                                           class="<?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?> form-control input-sm"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal($totalLoanAmount) ?>"
                                                           onkeyup='return restrictAlphabetsLoanTerms(this)'
                                                        <?php if (($typeOfHMLOLoanRequesting != 'Purchase'
                                                                && $typeOfHMLOLoanRequesting != 'Commercial Purchase')
                                                            || ($fileTab == 'FA'
                                                                && glCustomJobForProcessingCompany::isPC_CRB($PCID))
                                                        ) { ?>
                                                            style="background-color: rgb(213, 213, 213);"
                                                            readonly
                                                        <?php } else { ?>
                                                            onchange="updateLoanDetail('totalloanamount'); "
                                                        <?php } ?>
                                                        <?php echo LTC2Variables::$readOnlyForLTC2Fields; ?>
                                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?> />
                                                </div>
                                            <?php } else {
                                                echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($totalLoanAmount) . '</b>';
                                            } ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td width="50%"></td>
                            <td width="50%">
                                <div
                                        class="  rehabConsCls totalCashOut_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'totalCashOut', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?> "
                                        style="<?php echo $rehabConsCls; ?><?php echo HMLOLoanTermsCalculation::$totalCashOutDisp; ?>">
                                    <div class="row form-group">
                                        <label
                                                class="col-md-4 font-weight-bold justify-content-center align-self-center">
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'totalCashOut', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                            <i class="fa fa-info-circle text-primary popoverClass ml-2"
                                               data-html="true"
                                               id="totalCashOutToolTip"
                                               title="Total Cash Out"
                                               data-formula="<?php echo HMLOLoanTermsCalculation::$totalCashOutToolTip; ?>"
                                               data-content="<?php echo HMLOLoanTermsCalculation::$totalCashOutToolTipWithValues ? (
                                                   HMLOLoanTermsCalculation::$totalCashOutToolTip . '<hr>' . HMLOLoanTermsCalculation::$totalCashOutToolTipWithValues) : HMLOLoanTermsCalculation::$totalCashOutToolTip ?>"></i>
                                        </label>
                                        <div class="col-md-5">
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <span class="form-control input-group-text input-sm"
                                                      id="totalCashOut">
                                                    <?php echo Currency::formatDollarAmountWithDecimal($totalCashOutAmt) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td width="50%"></td>
                            <td width="50%">
                                <div
                                        class="  rehabConsCls lineOfCreditProp <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?> "
                                        style="<?php echo $rehabConsCls; ?><?php echo $lineOfCreditProp; ?>">
                                    <div class="row py-4">
                                        <label
                                                class="col-md-4 font-weight-bold justify-content-center align-self-center">
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                            <i class="fa fa-info-circle text-primary popoverClass ml-2"
                                               data-html="true"
                                               id="totalLoanAmountToolTip"
                                               title="Total Loan Amount"
                                               data-formula="<?php echo HMLOLoanTermsCalculation::$tLAToolTip; ?>"
                                               data-content="<?php echo HMLOLoanTermsCalculation::$tLAToolTipWithValues ? (
                                                   HMLOLoanTermsCalculation::$tLAToolTip . '<hr>' . HMLOLoanTermsCalculation::$tLAToolTipWithValues) : HMLOLoanTermsCalculation::$tLAToolTip ?>"></i>
                                        </label>
                                        <div class="col-md-5">
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <span class="form-control input-sm input-group-text totalLoanAmount"
                                                      id="coTotalAmt">
                                                    <?php echo Currency::formatDollarAmountWithDecimal($totalLoanAmount) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td width="50%">
                                <?php
                                if ($publicUser != 1) { ?>
                                    <div
                                            class="  autoCalcTLAARVDisp doesPropertyNeedRehabDispDiv <?php echo BaseHTML::fieldAccess(['fNm' => 'autoCalTotalLoanAmount', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                            style="<?php echo $autoCalcTLAARVDisp . ' ' . $doesPropertyNeedRehabDispDiv ?>">
                                        <div class="row py-4">
                                            <label
                                                    class="col-md-6 font-weight-bold justify-content-center align-self-center ">
                                                <i data-html="true"
                                                   class="fa fa-info-circle text-primary tooltipClass mr-2 autoCalARVToolTip"
                                                   title="<?php echo $autoCalARVToolTip; ?>"></i> Auto-calc
                                                Total
                                                Loan Amount based
                                                on ?
                                            </label>
                                            <div class="col-md-4 px-0">
                                                <?php if ($allowToEdit) { ?>
                                                    <div class="radio-inline float-right py-2">
                                                        <label class="radio radio-solid "
                                                               for="autoCalcTLAARVYes">
                                                            <input type="radio"
                                                                   name="autoCalcTLAARV"
                                                                   value="Yes"
                                                                   id="autoCalcTLAARVYes"
                                                                   onclick="autoCalculateTotalLoanAmountARVNew(1);"
                                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                                <?php echo Strings::isChecked('Yes', $autoCalcTLAARV); ?>>
                                                            <span></span>ARV
                                                        </label>
                                                        <label class="radio radio-solid "
                                                               for="autoCalcTLAARVLTC">
                                                            <input type="radio"
                                                                   name="autoCalcTLAARV"
                                                                   value="LTC"
                                                                   id="autoCalcTLAARVLTC"
                                                                   onclick="autoCalculateTotalLoanAmountARVNew(1);"
                                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                                <?php echo Strings::isChecked('LTC', $autoCalcTLAARV); ?>>
                                                            <span></span>LTC
                                                        </label>
                                                        <label class="radio radio-solid "
                                                               for="autoCalcTLAARVNo">
                                                            <input type="radio"
                                                                   name="autoCalcTLAARV"
                                                                   value="No"
                                                                   onclick="autoCalculateTotalLoanAmountARVNew();"
                                                                   id="autoCalcTLAARVNo"
                                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                                <?php echo Strings::isChecked('No', $autoCalcTLAARV); ?>>
                                                            <span></span>NA
                                                        </label>
                                                        <label class="radio radio-solid "
                                                               for="autoCalcTLAARVLTC2">
                                                            <input type="radio"
                                                                   name="autoCalcTLAARV"
                                                                   value="LTC2"
                                                                   onclick="autoCalculateTotalLoanAmountARVNew();"
                                                                   id="autoCalcTLAARVLTC2"
                                                                   tabindex="<?php echo $tabIndex++; ?>"
                                                                <?php echo Strings::isChecked('LTC2', $autoCalcTLAARV); ?>>
                                                            <span></span>LTC-2
                                                        </label>
                                                    </div>
                                                <?php } else {
                                                    echo '<h5>' . $autoCalcTLAARV . '</h5>';
                                                } ?>
                                            </div>
                                            <div class="col-md-2 ">
                                                <?php if ($allowToEdit) { ?>
                                                    <input type="text"
                                                           name="maxArvPer"
                                                           id="maxArvPer"
                                                           value="<?php echo $maxArvPer; ?>"
                                                           placeholder="0.0"
                                                           onkeyup="validatePercentage(this)"
                                                           onchange="autoCalculateTotalLoanAmountARVNew();validateMinMaxLoanGuidelines();"
                                                           class="form-control input-sm"
                                                           autocomplete="off"
                                                           style="<?php echo $maxArvPerDisp; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>">
                                                <?php } else {
                                                    echo '<h5>' . $maxArvPer . '</h5>';
                                                } ?>
                                                <?php if ($allowToEdit) { ?>
                                                 <input type="text" name="maxLTCPer" id="maxLTCPer"
                                                           value="<?php echo $maxLTCPer; ?>"
                                                           placeholder="0.0"
                                                           onkeyup="validatePercentage(this)"
                                                           onblur="autoCalculateTotalLoanAmountARVNew();validateMinMaxLoanGuidelines();"
                                                           class="form-control input-sm"
                                                           autocomplete="off"
                                                           style="<?php echo $maxLTCPerDisp; ?>"
                                                           tabindex="<?php echo $tabIndex++; ?>">
                                                <?php } else {
                                                    echo '<h5>' . $maxLTCPer . '</h5>';
                                                } ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                            </td>
                            <td width="50%">
                                <div
                                        class="   doesPropertyNeedRehabDispDiv totalProjectCost_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'totalProjectCost', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                        style="<?php echo $doesPropertyNeedRehabDispDiv ?>">
                                    <div class="row py-4">
                                        <label
                                                class="col-md-4 font-weight-bold  justify-content-center align-self-center">
                                            <span class="totProjectCost"><?php echo $totProjectCostLbl ?></span>
                                            <i id="TPCToolTip"
                                               class="fa fa-info-circle  text-primary ml-2 tooltipClass"
                                               data-html="true"
                                               data-formula="<?php echo HMLOLoanTermsCalculation::$TPCToolTip; ?>"
                                               title="<?php echo HMLOLoanTermsCalculation::$TPCToolTipWithValues ? (HMLOLoanTermsCalculation::$TPCToolTip . '<hr>' . HMLOLoanTermsCalculation::$TPCToolTipWithValues) : (HMLOLoanTermsCalculation::$TPCToolTip); ?>"></i>
                                        </label>
                                        <div class="col-md-5 ">
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <span class="form-control input-sm input-group-text"
                                                      id="totalProjectCost">
                                                    <?php echo Currency::formatDollarAmountWithDecimal($totalProjectCost) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td style="width:50%;">
                                <div class="showDivForLTC2"
                                    style="<?php echo HMLOLoanTermsCalculation::$showDivForLTC2; ?>"
                                >
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="row">
                                                <label class="font-weight-bold col-md-12 font-weight-bold justify-content-center align-self-center"
                                                       for="LTC2_additionalReserveInterest">
                                                    Additional Reserve Interest
                                                    <i id="LTC2_additionalReserveInterestTooltip"
                                                       class="fa fa-info-circle text-primary ml-2 popoverClass"
                                                       data-html="true"
                                                       data-formula="<?php echo LTC2Variables::$LTC2_additionalReserveInterestTooltip; ?>"
                                                       data-content="<?php echo LTC2Variables::$LTC2_additionalReserveInterestTooltipWithValues ? (
                                                           LTC2Variables::$LTC2_additionalReserveInterestTooltip . '<hr>' . LTC2Variables::$LTC2_additionalReserveInterestTooltipWithValues) : LTC2Variables::$LTC2_additionalReserveInterestTooltip ?>"></i>

                                                    <i id="LTC2_baseLoanAmountTooltip"
                                                       class="fa fa-info-circle text-danger ml-2 popoverClass"
                                                       data-html="true"
                                                       data-formula="<?php echo LTC2Variables::$LTC2_baseLoanAmountTooltip; ?>"
                                                       data-content="<?php echo LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues ? (
                                                           LTC2Variables::$LTC2_baseLoanAmountTooltip . '<hr>' . LTC2Variables::$LTC2_baseLoanAmountTooltipWithValues).'<hr>'.LTC2Variables::$LTC2_baseLoanAmountFormatted : LTC2Variables::$LTC2_baseLoanAmountTooltip ?>"></i>
                                                </label>
                                                <div class="col-md-12">
                                                    <input type="text"
                                                        <?php echo HMLOLoanTermsCalculation::$enableFieldsForLTC2; ?>
                                                           class="form-control input-sm enableFieldsForLTC2"
                                                           name="LTC2_additionalReserveInterest"
                                                           id="LTC2_additionalReserveInterest"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalReserveInterest); ?>"
                                                           readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <label class="font-weight-bold col-md-12 font-weight-bold justify-content-center align-self-center"
                                                       for="LTC2_additionalOriginationInterest">
                                                    Additional Origination Interest
                                                    <i id="LTC2_additionalOriginationInterestTooltip"
                                                       class="fa fa-info-circle text-primary ml-2 popoverClass"
                                                       data-html="true"
                                                       data-formula="<?php echo LTC2Variables::$LTC2_additionalOriginationInterestTooltip; ?>"
                                                       data-content="<?php echo LTC2Variables::$LTC2_additionalOriginationInterestTooltipWithValues ? (
                                                           LTC2Variables::$LTC2_additionalOriginationInterestTooltip . '<hr>' . LTC2Variables::$LTC2_additionalOriginationInterestTooltipWithValues) : LTC2Variables::$LTC2_additionalOriginationInterestTooltip ?>"></i>
                                                </label>
                                                <div class="col-md-12">
                                                    <input type="text"
                                                        <?php echo HMLOLoanTermsCalculation::$enableFieldsForLTC2; ?>
                                                           class="form-control input-sm enableFieldsForLTC2"
                                                           name="LTC2_additionalOriginationInterest"
                                                           id="LTC2_additionalOriginationInterest"
                                                           value="<?php echo Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalOriginationInterest); ?>"
                                                           readonly>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </td>
                            <td style="width:50%;">   <!-- New TPC (Fees & Cost)-->
                                <div id="NewTPCDiv"
                                     class=" <?php echo BaseHTML::fieldAccess(['fNm' => 'totalProjectCostNew', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                     style="<?php echo HMLOLoanTermsCalculation::$NewTotalProjectCostDisp; ?>">
                                    <div class="row py-4">
                                        <label class="col-md-4 font-weight-bold align-self-center">
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'totalProjectCostNew', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                            <i id="NewTPCToolTip"
                                               class="fa fa-info-circle  text-primary ml-2 tooltipClass"
                                               data-html="true"
                                               data-formula="<?php echo HMLOLoanTermsCalculation::$NewTPCToolTip; ?>"
                                               title="<?php echo HMLOLoanTermsCalculation::$NewTPCToolTipWithValues ? (HMLOLoanTermsCalculation::$NewTPCToolTip . '<hr>' . HMLOLoanTermsCalculation::$NewTPCToolTipWithValues) : HMLOLoanTermsCalculation::$NewTPCToolTip; ?>"></i>
                                        </label>
                                        <div class="col-md-5 align-self-center ">
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <span class="input-group-text form-control" id="NewTotalProjectCost">
                                                <?php echo Currency::formatDollarAmountWithDecimal($NewTotalProjectCost) ?>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>

                </div>
                <!-- Row 3 end -->

                <div class=" col-md-12 justify-content-center align-self-center">
                    <div class="row">
                        <div class=" col-md-6 ">
                            <div
                                    class=" LOCTotalLoanAmt LOCTotalLoanAmt_disp <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                                    style="<?php echo $LOCTotalLoanAmtDispOpt ?>">
                                <div class="row form-group">
                                    <label class="col-md-6 font-weight-bold"
                                           for="LOCTotalLoanAmt"><?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?></label>
                                    <div class="col-md-6">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="text"
                                                       class="form-control input-sm totalLoanAmount <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'M']); ?>"
                                                       name="LOCTotalLoanAmt" id="LOCTotalLoanAmt"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($LOCTotalLoanAmt) ?>"
                                                       onblur="currencyConverter(this, this.value);updateLoanDetail();"
                                                       placeholder="0.00"
                                                       TABINDEX="<?php echo $tabIndex++; ?>"
                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'CORTotalLoanAmt', 'sArr' => $ACFsecArr, 'opt' => 'I']); ?>/>
                                            </div>
                                        <?php } else {
                                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($LOCTotalLoanAmt) . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6"> <!-- Second Section Start -->
                            <div class="   landValueCls landValue_disp <?php echo loanForm::showField('landValue'); ?>"
                                 style="<?php echo $landValueCls ?>">
                                <div class="row form-group">
                                    <?php echo loanForm::label('landValue', 'col-md-4 '); ?>
                                    <div class="col-md-5">
                                        <?php if ($allowToEdit) { ?>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text">$</span>
                                                </div>
                                                <input type="text"
                                                       placeholder="0.00"
                                                       class="form-control input-sm <?php echo BaseHTML::fieldAccess(['fNm' => 'landValue', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                       name="landValue" id="landValue"
                                                       value="<?php echo Currency::formatDollarAmountWithDecimal($landValue) ?>"
                                                       onblur="currencyConverter(this, this.value);"
                                                       placeholder="0.00"
                                                       TABINDEX="<?php echo $tabIndex++; ?>"
                                                       autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'landValue', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            </div>
                                        <?php } else {
                                            echo '<b>$ ' . Currency::formatDollarAmountWithDecimal($landValue) . '</b>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- Second Div End -->
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="row">
                        <div class="col-12 col-md-3 col-lg-3 mb-2 propertyNeedRehabinitialTddisp simplearvpercentage_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'simplearvpercentage', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                             style="<?php echo $doesPropertyNeedRehabDispTDDiv ?>">
                            <div class="card card-custom bg-light-green desktop">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label  m-0">
                                                <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   id="simpleARVTooltip"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$simpleARVTooltip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$simpleARVTooltipWithValues ? (HMLOLoanTermsCalculation::$simpleARVTooltip . '<hr>' . HMLOLoanTermsCalculation::$simpleARVTooltipWithValues) : HMLOLoanTermsCalculation::$simpleARVTooltip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'simplearvpercentage', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span id="simpleARV">
                                                    <?php echo Currency::formatDollarAmountWithDecimal($simpleARV) ?>
                                                </span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-3 col-lg-3 mb-2 propertyNeedRehabinitialTddisp arvpercentage_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'arvpercentage', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                             style="<?php echo $doesPropertyNeedRehabDispTDDiv ?>">
                            <div class="card card-custom bg-light-green">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label m-0">
                                                <i id="fullARVTooltip"
                                                   class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$fullARVTooltip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$fullARVTooltipWithValues ? (HMLOLoanTermsCalculation::$fullARVTooltip . '<hr>' . HMLOLoanTermsCalculation::$fullARVTooltipWithValues) : HMLOLoanTermsCalculation::$fullARVTooltip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'arvpercentage', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="ARV"><?php echo Currency::formatDollarAmountWithDecimal($ARV); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-md-6 col-lg-3 mb-2 acquisitionLTVTD acquisitionLTV_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'acquisitionLTV', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                             style="<?php echo $acquisitionLTVTD; ?>">
                            <div class="card card-custom bg-light-green desktop">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label m-0">
                                                <i id="acquisitionLTVTooltip"
                                                   class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$acquisitionLTVTooltip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$acquisitionLTVTooltipWithValues ? (HMLOLoanTermsCalculation::$acquisitionLTVTooltip . '<hr>' . HMLOLoanTermsCalculation::$acquisitionLTVTooltipWithValues) : HMLOLoanTermsCalculation::$acquisitionLTVTooltip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'acquisitionLTV', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="acquisitionLTV"><?php echo Currency::formatDollarAmountWithDecimal($acquisitionLTV); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3 mb-2 marketLTVTD marketLTV_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'marketLTV', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                             style="<?php echo $marketLTVTD; ?>">
                            <div class="card card-custom bg-light-green">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label m-0">
                                                <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   id="marketLTVToolTip"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$marketLTVToolTip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$marketLTVToolTipWithValues ? (HMLOLoanTermsCalculation::$marketLTVToolTip . '<hr>' . HMLOLoanTermsCalculation::$marketLTVToolTipWithValues) : HMLOLoanTermsCalculation::$marketLTVToolTip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'marketLTV', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="marketLTV"><?php echo Currency::formatDollarAmountWithDecimal($marketLTV); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3 mb-2  Loan-to-Cost_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="card card-custom bg-light-green">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i id="LTCToolTip"
                                                   class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$LTCToolTip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$LTCToolTipWithValues ? (HMLOLoanTermsCalculation::$LTCToolTip . '<hr>' . HMLOLoanTermsCalculation::$LTCToolTipWithValues) : HMLOLoanTermsCalculation::$LTCToolTip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="Loan-to-Cost"><?php echo Currency::formatDollarAmountWithDecimal($LTC); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3 mb-2 propertyNeedRehabinitialTddisp perRehabCostFinanced_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'perRehabCostFinanced', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>"
                             style="<?php echo $doesPropertyNeedRehabDispTDDiv; ?>">
                            <div class="card card-custom bg-light-green">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i id="rehabCostFinancedTooltip"
                                                   class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$rehabCostFinancedTooltip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$rehabCostFinancedTooltipWithValues ? (HMLOLoanTermsCalculation::$rehabCostFinancedTooltip . '<hr>' . HMLOLoanTermsCalculation::$rehabCostFinancedTooltipWithValues) : HMLOLoanTermsCalculation::$rehabCostFinancedTooltip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'perRehabCostFinanced', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="perRehabCostFinanced"><?php echo Currency::formatDollarAmountWithDecimal($perRehabCostFinanced); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 col-md-6 col-lg-3 mb-2  grossProfit_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfit', 'sArr' => $ACFsecArr, 'opt' => 'D']);
                        if ($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE
                            && $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE
                            && $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE
                            && $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::DELAYED_PURCHASE) {
                            echo ' d-none ';
                        }
                        ?> ">
                            <div class="card card-custom bg-light-green">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i id="grossProfitTooltip"
                                                   class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$grossProfitTooltip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$grossProfitTooltipWithValues ? (HMLOLoanTermsCalculation::$grossProfitTooltip . '<hr>' . HMLOLoanTermsCalculation::$grossProfitTooltipWithValues) : HMLOLoanTermsCalculation::$grossProfitTooltip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfit', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                $
                                                <span
                                                        id="grossProfit"><?php echo Currency::formatDollarAmountWithDecimal($grossProfit); ?></span>
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-3 mb-2  grossProfitMargin_disp
                        <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfitMargin', 'sArr' => $ACFsecArr, 'opt' => 'D']);
                        if ($typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::PURCHASE
                            && $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::COMMERCIAL_PURCHASE
                            && $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::DELAYED_PURCHASE
                            && $typeOfHMLOLoanRequesting != typeOfHMLOLoanRequesting::CASH_OUT_REFINANCE) {
                            echo ' d-none ';
                        }
                        ?> ">
                            <div class="card card-custom bg-light-green">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i id="grossProfitMarginTooltip"
                                                   class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$grossProfitMarginTooltip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$grossProfitMarginTooltipWithValues ? (HMLOLoanTermsCalculation::$grossProfitMarginTooltip . '<hr>' . HMLOLoanTermsCalculation::$grossProfitMarginTooltipWithValues) : HMLOLoanTermsCalculation::$grossProfitMarginTooltip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'grossProfitMargin', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="grossProfitMargin"><?php echo Currency::formatDollarAmountWithDecimal($grossProfitMargin); ?></span>
                                                %
                                            </h3>
                                        </div>
                                        `
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- New LTC (Fees & Cost)-->
                        <div id="NewLTCDiv"
                             class="col-12 col-md-6 col-lg-3 mb-2 <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-New', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="card card-custom bg-light-green desktop">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i id="NewLTCToolTip"
                                                   class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$NewLTCToolTip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$NewLTCToolTipWithValues ? (HMLOLoanTermsCalculation::$NewLTCToolTip . '<hr>' . HMLOLoanTermsCalculation::$NewLTCToolTipWithValues) : HMLOLoanTermsCalculation::$NewLTCToolTip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-New', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span id="NewLoanToCost"><?php echo Currency::formatDollarAmountWithDecimal($NewLoanToCost); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div
                                class="col-12 col-md-6 col-lg-3 mb-2 LTCInitialLoanAmountCls <?php echo $LTCInitialLoanAmountCls; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-initial-Loan-Amount', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="card card-custom bg-light-green desktop">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   id="LTCInitialLoanAmountToolTip"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTipWithValues ? (HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTip . '<hr>' . HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTipWithValues) : HMLOLoanTermsCalculation::$LTCInitialLoanAmountToolTip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-initial-Loan-Amount', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="LTCInitialLoanAmount"><?php echo Currency::formatDollarAmountWithDecimal($LTCInitialLoanAmount); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div
                                class="col-12 col-md-6 col-lg-3 LTCMarketValueCls mb-2  <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Market-Value', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="card card-custom bg-light-green desktop">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   id="LTCMarketValueToolTip"
                                                   data-formula="<?php echo HMLOLoanTermsCalculation::$LTCMarketValueToolTip; ?>"
                                                   title="<?php echo HMLOLoanTermsCalculation::$LTCMarketValueToolTipWithValues ? (HMLOLoanTermsCalculation::$LTCMarketValueToolTip . '<hr>' . HMLOLoanTermsCalculation::$LTCMarketValueToolTipWithValues) : HMLOLoanTermsCalculation::$LTCMarketValueToolTip; ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Market-Value', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="LTCMarketValue"><?php echo Currency::formatDollarAmountWithDecimal($LTCMarketValue); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div
                                class="col-12 col-md-6 col-lg-3 LTCOriginalPurchasePriceCls mb-2 <?php echo HMLOLoanTermsCalculation::$LTCOriginalPurchasePriceCls; ?>  <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Original-Purchase-Price', 'sArr' => $ACFsecArr, 'opt' => 'D']); ?>">
                            <div class="card card-custom bg-light-green desktop">
                                <div class="card-header border-0">
                                    <div class="card-title">
                                        <div class="row">
                                            <h3 class="card-label">
                                                <i class="fa fa-info-circle pt-3 tooltipClass text-primary"
                                                   data-html="true"
                                                   id="LTCOriginalPurchasePriceToolTip"
                                                   title="<?php echo htmlspecialchars(HMLOLoanTermsCalculation::$LTCOriginalPurchasePriceToolTip); ?>">
                                                </i>
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'Loan-to-Cost-Original-Purchase-Price', 'sArr' => $ACFsecArr, 'opt' => 'L']); ?>
                                                <span
                                                        id="LTCOriginalPurchasePriceValue"><?php echo Currency::formatDollarAmountWithDecimal(HMLOLoanTermsCalculation::$LTCOriginalPurchasePriceValue); ?></span>
                                                %
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <?php echo CustomField::RenderForTabSection(
                PageVariables::$PCID,
                tblFile::class,
                LMRequest::$LMRId,
                'LT',
                $fileTab,
                $activeTab,
                LMRequest::myFileInfo()->getFileTypes(),
                LMRequest::myFileInfo()->getLoanPrograms()
            ); ?>
        </div>
    </div>
    <?php require_once 'LMRequest/sections/refinanceMortgageForm.php';
}
?>
<style>
    label.disableClick {
        pointer-events: none;
    }
    label.disableClick .enableClick {
        pointer-events: auto;
        cursor: pointer;
    }
</style>

<script>
    let refinanceList = $.parseJSON('<?php echo(json_encode(typeOfHMLOLoanRequesting::REFINANCE_LIST)); ?>');
    $(document).ready(function () {
        validateMinMaxLoanGuidelines('No');
        var typeOfloanRequest = "<?php echo $typeOfHMLOLoanRequesting; ?>";
        if (typeOfloanRequest === 'Purchase') {
            $('.cashOutDiv,.LOCTotalLoanAmt_disp,.totalCashOut_disp').css("display", "none");
            $('.commercialFieldsTD').css("display", "block");
            $('.commercialFieldsTDNew').css("display", ""); //needs to override the previous style fix for sc-31816
        }

        $(document).on('blur', '#noOfPropertiesAcquiring', function () {
            var noOfPropertiesAcquiring = $(this).val();
            if ((!$.isNumeric(noOfPropertiesAcquiring) || noOfPropertiesAcquiring < 2) && noOfPropertiesAcquiring != '') { // the entered value should be a number and greater than 1
                alert('The value should be a number and greater than 1');
                $(this).val('');
                $(this).focus();
            } else if (noOfPropertiesAcquiring > 100) {
                toastrNotification('The value should not exceed 100', 'error');
                $(this).val('');
                $(this).focus();
            }
        });

        $('#rateIndex').chosen({'create_option': true, 'persistent_create_option': true, 'skip_no_results': true});

        $('.annualPremiumClass').bind('keypress keyup blur', function () {
            if ($('.annualPremiumClass').length > 1) {
                //console.log('Vall '+$(this).val());
                $('.annualPremiumClass').val($(this).val());
            }
        });


    });

    $(function () {
        //mailing address
        if ($('.clsseperator.secShow').length > 0) { //show titlessssss
            $("#loaninfoseperator").show();
        } else { //hide title
            $("#loaninfoseperator").hide();
        }

    });

    function mirrornoOfPropertiesAcquiring() {
        $('#noOfPropertiesAcquiring_mirror').val($('#noOfPropertiesAcquiring').val());
        if ($('#noOfPropertiesAcquiring').val() > 100) {
            $('#noOfPropertiesAcquiring_mirror').val('');
            return false;
        }
        LMRId = "<?php echo $LMRId;?>";

        if ($('#webFormView').val() == 'wizard') {
            $('#btnSave').click();
            location.reload();
        } else if (LMRId > 0) {
            var formIdToSubmit = $('#loanModForm');
            ajaxUrl = $(formIdToSubmit).attr('action');
            formData = $(formIdToSubmit).serialize();
            formData = new FormData($('#loanModForm')[0]);

            var ajaxRequest = $.ajax({
                url: ajaxUrl,
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function () {
                    //  BlockDiv('employeeCreateDiv');
                },
                complete: function () {
                    //UnBlockDiv('employeeCreateDiv');
                },
                success: function (response, status, xhr) {
                    location.reload();
                },
                error: function (jqXhr, textStatus, errorMessage) {
                    toastrNotification(errorMessage, 'error');
                }
            });
        } else {
            if ($('#noOfPropertiesAcquiring_mirror').val() > 1 &&
                ($('.proploc.secShow').length > 0 || $('.propchar.secShow').length > 0 || $('.propdetails.secShow').length > 0
                    || $('.propaccess.secShow').length > 0 || $('.rentRollSecFields.secShow').length > 0)) {
                if (!($('#noOfPropertiesAcquiring_mirror_disp').is(":visible"))) {
                    $('#addSubpropDiv').show();
                }
            } else {
                $('#addSubpropDiv').hide();
            }
        }
    }

    function restrictAlphabets(el, evt, afterDecimal = 3) {
        var beforeDecimal = 10;

        $('#' + el.id).on('input', function () {
            this.value = this.value
                .replace(/[^\d.]/g, '')
                .replace(new RegExp("(^[\\d]{" + beforeDecimal + "})[\\d]", "g"), '$1')
                .replace(/(\..*)\./g, '$1')
                .replace(new RegExp("(\\.[\\d]{" + afterDecimal + "}).", "g"), '$1');
        })
    }

    function showAndHideGroupUpFields(fieldValue) {
        if (fieldValue === 'Yes') {
            $('.groundUpFields').css("display", "block");
        } else {
            $('.groundUpFields').css("display", "none");
        }
        loanCalculation.calculateGrossProfit();
    }

    let FPCID = parseInt($('#FPCID').val());
    let PCID_CRB = parseInt(<?php echo glPCID::PCID_CRB; ?>);
</script>
<script src="<?php echo CONST_SITE_URL; ?>/backoffice/LMRequest/js/crbCustomLoanTermCalculation.js?<?php echo CONST_JS_VERSION; ?>"
        type="text/javascript"></script>
<script src="/backoffice/LMRequest/loanInfoV2/js/loanInfoV2Form.js?<?php echo CONST_JS_VERSION; ?>"></script>
<?php
if (in_array('Loan Terms', $lockedSections)) {
    //$BackupAllowToEdit = $allowToEdit;
    $allowToEdit = $BackupAllowToEdit;
    LMRequest::$allowToEdit = $BackupAllowToEdit;
}
?>

<!-- HMLOLoanTermsForm.php -->
