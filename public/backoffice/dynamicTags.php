<?php
global $myModulesArray, $PCID, $docWizard;

use models\composite\oCustomDocs\getMergeTagDetails;
use models\composite\oCustomDocs\getMergeTagDetailsUpdated;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\lendingwise\tblCustomField;
use models\standard\Arrays;

$showHMTags = 'display:none;';
$showLMTags = 'display:none;';
if (in_array('HMLO', $myModulesArray) || in_array('loc', $myModulesArray)) {
    $showHMTags = 'display:table-row;';
} else {
    $showLMTags = 'display:table-row;';
}

$tagsData = getMergeTagDetails::getReport(1, '');
if (isset($isAgentEmailEmailCampaign)) {
    $tagsData = getMergeTagDetailsUpdated::getReport(['filter' => 1, 'sectionIds' => ['2', '9'], 'ignoreMergetags' => ['109']]);
}

$tblCustomField = tblCustomField::GetAll([
    'PCID'           => $PCID,
    'isActive'       => 1,
    'ShowOnMergeTag' => 1,
]);
$loanInfoV2TagsDisp = glCustomJobForProcessingCompany::showLoanInfoV2($PCID);
$hideMergeTagsList = [];
if ($docWizard == 1) {
    $hideMergeTagsList = [
        '##Esign Links##',
        '##Branch Logo##',
        '##Broker Logo##',
    ];
}
$loanInfoV2SectionID = getMergeTagDetails::getMergeTagSectionId('Loan Info V2');
//show merge tags for all PCs.
$showMergeTagsForAll = [
    'constructionHardCost',
    'constructionSoftCost'
];
$sections = [];
$sectionTags = [];
foreach ($tagsData['getSectionData'] as $eachTag) {
    if ((!$loanInfoV2TagsDisp && $eachTag['sectionID'] == $loanInfoV2SectionID) && !in_array($eachTag['tagKeys'], $showMergeTagsForAll)) {
        continue;
    }
    $sectionName = $eachTag['sectionName'];
    if (!glCustomJobForProcessingCompany::isPC_CV3($PCID) && $eachTag['sectionID'] == $loanInfoV2SectionID) {
        $sectionName = 'Loan Info';
    }
    $sections[$eachTag['sectionID']] = $sectionName;
    if (trim($eachTag['subSection'])) {
        $sectionTags[$eachTag['sectionID']][$eachTag['subSection']][] = $eachTag;
    } else {
        $sectionTags[$eachTag['sectionID']][] = $eachTag;
    }
}
asort($sections);
?>
<div class="row">
    <div class="col-md-10">
        <div class="input-group ">
            <div class="input-icon">
                <input type="text" id="searchMergeTags" class="form-control" placeholder="Search tags..."/>
                <span><i class="flaticon2-search-1 icon-md"></i></span>
            </div>
            <div class="input-group-append">
                <div class="input-group-text">
            <span class="icon-red fa fa-times" title="Click to Clear"
                  onclick="$('#searchMergeTags').val('').trigger('keyup').focus();"></span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleAll"
             data-placement="top"
             title="Toggle All Cards">
            <i class="ki ki-arrow-up icon-nm"></i>
        </div>
    </div>
</div>

<div id="tagsDiv">
    <?php
    foreach ($sections as $eachSectionId => $eachSectionName) { ?>
        <div class="card card-custom mt-2 mergeTagSections sectionCard<?php echo $eachSectionId; ?>">
            <div class="card-header card-header-tabs-line bg-gray-200"
                 style="min-height: 30px !important;">
                <div class="card-title">
                    <h3 class="card-label h7"><?php echo $eachSectionName ?> </h3>
                </div>
                <div class="card-toolbar">
                    <div class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass toggleMergeTagBtn"
                         data-card-tool="toggle"
                         data-section="sectionCard<?php echo $eachSectionId; ?>"
                         data-toggle="tooltip"
                         data-placement="top"
                         title="Toggle Card">
                        <i class="ki ki-arrow-up icon-nm"></i>
                    </div>
                </div>
            </div>
            <div class="card-body mergeTagBody sectionCard<?php echo $eachSectionId; ?>_body p-4" style="display: none;">
                <table style="width:100%;"
                       class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                    <thead class="thead-light">
                    <tr>
                        <th style="width: 45%">Dynamic Fields</th>
                        <th></th>
                        <th style="width: 45%">Tags</th>
                    </tr>
                    </thead>
                    <?php
                    foreach ($sectionTags[$eachSectionId] as $subSectionName => $mergeTagInfo) {
                        if (isset($mergeTagInfo['tagLabel'])) {
                            if($mergeTagInfo['PCID']
                                && !in_array($PCID,Arrays::explodeIntVals($mergeTagInfo['PCID']))) {
                               continue;
                            }
                            ?>
                            <tr class=" mb-2 alternativeColor p-2 copyMergeTag <?php if ($mergeTagInfo['fileType'] == 'HMLO') {
                                echo 'HMLOSmartTagsDispDiv';
                            } elseif ($mergeTagInfo['fileType'] == 'LM') {
                                echo 'LMSmartTagsDispDiv';
                            } ?>"
                                data-tag="<?= $mergeTagInfo['tagName'] ?>"
                                data-label="<?= $mergeTagInfo['tagLabel'] ?>"
                                data-body="sectionCard<?= $eachSectionId; ?>_body"
                                data-card="sectionCard<?= $eachSectionId; ?>"
                            >
                                <td class="p-2"><?= $mergeTagInfo['tagLabel'] ?></td>
                                <td class="text-center">-</td>
                                <td class=""><?= $mergeTagInfo['tagName'] ?></td>
                            </tr>
                        <?php } else { ?>
                            <tr class=" mb-4 border p-2">
                                <td colspan="3"
                                    class=" bg-primary-o-50 font-size-h6 font-weight-bold text-center mb-2"><?= $subSectionName; ?></td>
                            </tr>
                            <tr class=" mb-4 border p-2">
                                <td colspan="3">
                                    <table style="width:100%;"
                                           class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                                        <?php
                                        foreach ($mergeTagInfo as $sub => $subSections) {
                                            if($subSections['PCID']
                                                && !in_array($PCID,Arrays::explodeIntVals($subSections['PCID']))) {
                                                continue;
                                            }
                                            ?>
                                            <tr class="mb-2 alternativeColor p-2 copyMergeTag  <?php if ($subSections['fileType'] == 'HMLO') {
                                                echo 'HMLOSmartTagsDispDiv';
                                            } elseif ($subSections['fileType'] == 'LM') {
                                                echo 'LMSmartTagsDispDiv';
                                            } ?>"
                                                data-tag="<?= $subSections['tagName'] ?>"
                                                data-label="<?= $subSections['tagLabel'] ?>"
                                                data-body="sectionCard<?= $eachSectionId; ?>_body"
                                                data-card="sectionCard<?= $eachSectionId; ?>"
                                            >
                                                <td class="p-2"><?= $subSections['tagLabel'] ?></td>
                                                <td class="text-center">-</td>
                                                <td class=""><?= $subSections['tagName'] ?></td>
                                            </tr>
                                        <?php } ?>
                                    </table>
                                </td>
                            </tr>
                            <?php
                        }
                    } ?>
                </table>

            </div>
        </div>
    <?php } ?>
    <div class="card card-custom mt-2 mergeTagSections sectionCardCustom">
        <div class="card-header card-header-tabs-line bg-gray-200"
             style="min-height: 30px !important;">
            <div class="card-title">
                <h3 class="card-label h7">Custom Fields</h3>
            </div>
            <div class="card-toolbar">
                <div class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass toggleMergeTagBtn"
                     data-card-tool="toggle"
                     data-section="sectionCardCustom"
                     data-toggle="tooltip"
                     data-placement="top"
                     title="Toggle Card">
                    <i class="ki ki-arrow-up icon-nm"></i>
                </div>
            </div>
        </div>
        <div class="card-body mergeTagBody sectionCardCustom_body p-4" style="display: none;">
            <table style="width:100%;"
                   class="table table-hover  table-bordered table-condensed table-sm table-vertical-center">
                <thead class="thead-light">
                <tr>
                    <th style="width: 45%">Dynamic Fields</th>
                    <th></th>
                    <th style="width: 45%">Tags</th>
                </tr>
                </thead>
                <?php
                foreach ($tblCustomField as $eachCustomField) { ?>
                    <tr class=" mb-2 alternativeColor p-2 copyMergeTag"
                        data-tag="<?= $eachCustomField->Label ?>"
                        data-label="<?= '##Custom Field_' . $eachCustomField->id . '##' ?>"
                        data-body="sectionCardCustom_body"
                        data-card="sectionCardCustom"
                    >
                        <td class="p-2"><?= $eachCustomField->Label ?></td>
                        <td class="text-center">-</td>
                        <td class="col"><?= '##Custom Field_' . $eachCustomField->id . '##' ?></td>
                    </tr>
                <?php } ?>
            </table>
        </div>
    </div>
</div>
<script>

    function sortTable(tbody, order) {
        var asc = order === 'asc';
        $(tbody).find('tr').sort(function (a, b) {
            if (asc) {
                return $('td:first', a).text().localeCompare($('td:first', b).text());
            } else {
                return $('td:first', b).text().localeCompare($('td:first', a).text());
            }
        }).appendTo(tbody);
        $(tbody).find("tr:odd").css({
            "background-color": "#ddd"
        });
    }

    function copyToClipboard(element) {
        var $temp = $("<input>");
        $("body").append($temp);
        $temp.val($(element).text()).select();
        //document.execCommand("copy");
        navigator.clipboard.writeText($(element).text());
    }

    $(document).on('click', '.btn-copy', function () {
        toastrNotification('Copied', 'success');
        copyToClipboard(this);
    });

    $(document).on('click', '.showTags', function () {
        $(this).find('i').toggleClass('fa-caret-down fa-caret-up');
        $(this).closest('tbody').next().slideToggle('slow');
    });
    $(document).on('click', '.copyMergeTag', function () {
        let tagName = $(this).data('tag');
        navigator.clipboard.writeText(tagName)
            .then(() => toastrNotification('Copied', 'success'))
            .catch(err => console.error('Copy failed', err));
    });
</script>
<script>
    $(document).ready(function () {
        $('#searchMergeTags').on('keyup', function () {
            let value = $(this).val().toLowerCase().trim();
            let _copyMergeTag = $('.copyMergeTag');
            if (value.length < 1) {
                // Reset all tags and sections
                _copyMergeTag.show().removeClass('bg-success-o-10');
                $('.mergeTagSections').show();
                $('.mergeTagBody').show();
                return;
            }
            $('.mergeTagSections, .mergeTagBody').hide();
            _copyMergeTag.hide().removeClass('bg-success-o-10');

            let sectionsToShow = new Set();

            _copyMergeTag.each(function () {
                let tagName = $(this).data('tag')?.toLowerCase() || '';
                let tagLabel = $(this).data('label')?.toLowerCase() || '';
                let sectionBody = $(this).data('body');
                let sectionCard = $(this).data('card');

                if (tagLabel.includes(value) || tagName.includes(value)) {
                    $(this).show().addClass('bg-success-o-10');
                    sectionsToShow.add(`.${sectionCard}`);
                    sectionsToShow.add(`.${sectionBody}`);
                }
            });
            $(Array.from(sectionsToShow).join(',')).show();
        });
        $('.toggleAll').on('click', function () {
            if ($(this).find('i').hasClass('ki-arrow-down')) {
                $(this).find('.ki-arrow-down').removeClass('ki-arrow-down').addClass('ki-arrow-up');
            } else {
                $(this).find('.ki-arrow-up').removeClass('ki-arrow-up').addClass('ki-arrow-down');
            }
            $('.toggleMergeTagBtn').click();
        });
    });

</script>

<style>
    .table tbody + tbody {
        border-top: none;
    }

    .btn-copy {
        cursor: pointer;
    }

    .columnHeading.my-2.showTags {
        cursor: pointer;
    }
</style>
