<?php
//global variables
global $userRole, $PCID;

use models\composite\oThirdPartyServices\getThirdPartyServicesDetails;
use models\constants\gl\glThirdPartyServicesCRA;
use models\constants\gl\glUserRole;
use models\cypher;
use models\standard\Strings;
use models\standard\Arrays;
use models\composite\oPC\PCInfo;

$stateArray = Arrays::fetchStates();
$glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();

$pcData = PCInfo::getReport(['PCID' => $PCID]);
$thirdPartyServicesDetails = getThirdPartyServicesDetails::getReport($PCID);
$thirdPartyServicesCRA = trim($pcData['thirdPartyServices']);
$thirdPartyServiceCSR = $pcData['thirdPartyServiceCSR'];
$thirdPartyServicesProducts = $pcData['thirdPartyServicesProducts'];

$thirdPartyServicesLegalDocs = trim($pcData['thirdPartyServicesLegalDocs']);
$geraciUsername = '';
$geraciPassword = '';

$thirdPartyServiceCSRArray = [];
$thirdPartyServicesProductsArray = [];
$thirdPartyServiceCSRArray = explode(',', $thirdPartyServiceCSR);
$thirdPartyServicesProductsArray = explode(',', $thirdPartyServicesProducts);

$scriptArray = [
    '/backoffice/api_v2/js/address_lookup.js',
];

Strings::includeMyScript($scriptArray);
?>
<!-- Third party services -->
<div class="row">
    <form class="col-md-12 mb-4" autocomplete="off"
          name="thirdPartyServicesForm" id="thirdPartyServicesForm"
          enctype="multipart/form-data" method="post"
          action="thirdPartyServiceSettingsSave.php" >
        <input type="hidden" name="PCID" value="<?= $PCID ?>" >
        <div class="col-md-12 mb-4">
            <div class="card card-custom">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label"> Credit Reporting Agencies </h3>
                    </div>
                    <div class="card-toolbar">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control" type="checkbox" <?php if ($thirdPartyServicesCRA == 1) { ?> checked="checked" <?php } ?>
                                       value="<?php echo $thirdPartyServicesCRA ?>" id="thirdPartyLink"
                                       onchange="toggleSwitch('thirdPartyLink','thirdPartyServices','1','0' );showEditSwitch('thirdPartySwitch','thirdPartyServices');Validation.controlChildElements('thirdPartyLink', 'thirdPartyServicesDiv')"/>
                                <input type="hidden" name="thirdPartyServices" id="thirdPartyServices" value="<?php echo $thirdPartyServicesCRA ?>" >
                                <span></span>
                            </label>
                        </span>
                        <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                              data-card-tool="toggle" data-section="thirdPartyService" data-toggle="tooltip" data-placement="top"
                              title="" data-original-title="Toggle Card">
                            <i class="ki icon-nm ki-arrow-down"></i>
                        </span>
                    </div>
                </div>

                <div class="card-body thirdPartyService" style="display: none" id="thirdPartyServicesDiv">
                    <h5 style="color:red; margin-bottom: 10px;">
                        <i class="fa fa-info-circle text-primary tooltipClass" style="text-decoration:none;"
                           title="This controls which CRA service are available in your system."></i>&nbsp;
                        Choose your Credit Agencies:
                    </h5>
                    <div class="col-md-12 mb-6">
                        <select data-placeholder="Select third party services" name="thirdPartyServiceCSR[]"
                                id="thirdPartyServiceCSR" class="chzn-select form-control form-controller-solid"
                                multiple=""
                            <?php if ($userRole != 'Super' && $userRole != 'Manager') echo ' readonly ' ?> >
                            <option value=""></option>
                            <?php
                            foreach ($glThirdPartyServicesCRA as $cra => $craValue) {
                                if (in_array($cra, $thirdPartyServiceCSRArray) || $userRole == 'Super' || $userRole == 'Manager') {
                                    ?>
                                    <option value="<?= $cra; ?>" <?= in_array($cra, $thirdPartyServiceCSRArray) ? 'Selected' : ''; ?> >
                                        <?= $craValue->Name; ?>
                                    </option>
                                <?php }
                            } ?>
                        </select>
                    </div>
                    <div class="col-md-12 mb-6">
                        <?php
                        foreach ($glThirdPartyServicesCRA as $cra => $craInfo) {
                            if (in_array($cra, $thirdPartyServiceCSRArray)) {
                                $display = ' style="display: block;" ';
                                $disabled = '';
                            } else {
                                $display = ' style="display: none;" ';
                                $disabled = ' disabled ';
                            }

                            // hide the password from users - must be 5 asterisks to prevent saving truncated password
                            $password = $thirdPartyServicesDetails['cra_' . $cra]['password'];
                            $password = $password ? '*****' . substr($password, -4) : '';

                            $creditCardType = cypher::myDecryption($thirdPartyServicesDetails['cra_' . $cra]['creditCardType']);
                            $creditCardNumber = cypher::myDecryption($thirdPartyServicesDetails['cra_' . $cra]['creditCardNumber']);
                            $cardNumberOnBack = cypher::myDecryption($thirdPartyServicesDetails['cra_' . $cra]['cardNumberOnBack']);
                            $expirationMonth = cypher::myDecryption($thirdPartyServicesDetails['cra_' . $cra]['expirationMonth']);
                            $expirationYear = cypher::myDecryption($thirdPartyServicesDetails['cra_' . $cra]['expirationYear']);
                            $cardHolderName = cypher::myDecryption($thirdPartyServicesDetails['cra_' . $cra]['cardHolderName']);

                            if ($cra !== 'xactus') {
                        ?>
                            <div class="accordion border border-secondary cra_products <?= $cra . '_products'; ?> mb-2"
                                 id="<?= $cra . '_accordion' ?>" <?= $display ?> >
                                <div class="card">
                                    <div class="card-header d-flex">
                                        <div class="col-md-10 card-title collapsed" data-toggle="collapse"
                                             data-target="#<?= $cra . '_accordionBody' ?>" aria-expanded="false">
                                            <?= $craInfo->Name; ?>
                                        </div>
                                        <div class="col-md-2 text-right" id="<?= $cra . '_reports'; ?>" >
                                            <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon m-2 toggleClass collapsed"
                                                  data-card-tool="toggle" data-target="#<?= $cra . '_accordionBody' ?>"
                                                  data-toggle="collapse" title="" aria-expanded="false" data-original-title="" >
                                                <i class="icon-1x ki icon-nm ki-arrow-down"></i>
                                            </span>
                                        </div>
                                    </div>

                                    <div id="<?= $cra . '_accordionBody' ?>" class="collapse" data-parent="#<?= $cra . '_accordion' ?>" >
                                        <div class="card-body">
                                        <div class="col-md-12 <?= $cra . '_reports'; ?> pt-2" >
                                            <label for="check_all_<?= $cra . '_reports'; ?>" >
                                                <input class="newCheck check_all_report" type="checkbox" name="check_all_<?= $cra . '_reports'; ?>"
                                                       id="check_all_<?= $cra . '_reports'; ?>" value="<?= $cra . '_report'; ?>" >
                                                <span>&nbsp;</span>
                                            </label>
                                            <span> <h7>Select All</h7> </span>
                                        </div>

                                        <div class="col-md-12 pb-4 <?= $cra . '_reports'; ?>">
                                            <div class="pad5" style="background-color: rgb(242, 244, 242);">
                                                <div class="row">
                                                    <?php
                                                    foreach ($craInfo->Services as $productKey => $productName) {
                                                        $identifier = $cra . '_' . $productKey;
                                                        ?>
                                                        <div class="col-md-3">
                                                            <label for="<?= $identifier; ?>">
                                                                <input class="newCheck <?= $cra . '_report'; ?>" type="checkbox"
                                                                       name="thirdPartyServicesProducts[]" id="<?= $identifier; ?>"
                                                                       value="<?= $identifier; ?>"
                                                                    <?= in_array($identifier, $thirdPartyServicesProductsArray) ? 'checked' : ''; ?> >
                                                                <span>&nbsp;</span>
                                                            </label>
                                                            <?= $productName['Name']; ?>
                                                        </div>
                                                    <?php } // service loop end
                                                    ?>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="clearfix"></div>
                                            </div>
                                        </div>

                                        <div class="row <?= $cra . '_reports'; ?>" >
                                            <div class="col-md-12">
                                                <label class="bg-secondary py-2  col-lg-12 mb-4"><b>Vendor
                                                        Credentials</b></label>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[username]' ?>">Username</label>
                                                            <div class="col-md-7">
                                                                <input type="text" name="<?= $cra . '[username]' ?>"
                                                                       id="<?= $cra . '_username' ?>"
                                                                       value="<?= $thirdPartyServicesDetails['cra_' . $cra]['username']; ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm"
                                                                       placeholder="Username" <?= $disabled ?> >
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6 ">
                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[password]' ?>">Password</label>
                                                            <div class="col-md-7">
                                                                <input type="text"
                                                                       name="<?= $cra . '[password]' ?>"
                                                                       id="<?= $cra . '_password' ?>"
                                                                       value="<?= $password ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm"
                                                                       placeholder="Password" <?= $disabled ?> >
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <label class="bg-secondary py-2 col-lg-12 mb-4"><b>Credit Card
                                                                info</b></label>

                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[creditCardType]' ?>">Type of
                                                                Card</label>
                                                            <div class="col-md-7">
                                                                <select name="<?= $cra . '[creditCardType]' ?>"
                                                                        id="<?= $cra . '_creditCardType' ?>"
                                                                        class="form-control input-sm" <?= $disabled ?> >
                                                                    <option value=""> - Select Credit Card Type -
                                                                    </option>
                                                                    <?php echo Strings::listCreditCardType($creditCardType); ?>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[creditCardNumber]' ?>">Card
                                                                #</label>
                                                            <div class="col-md-4">
                                                                <input type="text"
                                                                       name="<?= $cra . '[creditCardNumber]' ?>"
                                                                       id="<?= $cra . '_creditCardNumber' ?>"
                                                                       value="<?php echo $creditCardNumber; ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm mask_ccNumber"
                                                                       placeholder="Card Number" <?= $disabled ?>>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <input type="text"
                                                                       name="<?= $cra . '[cardNumberOnBack]' ?>"
                                                                       id="<?= $cra . '_cardNumberOnBack' ?>"
                                                                       value="<?php echo $cardNumberOnBack; ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm mask_CVV"
                                                                       placeholder="CVV #" <?= $disabled ?>>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="expirationDate">Exp Date</label>
                                                            <div class="col-md-4">
                                                                <select name="<?= $cra . '[expirationMonth]' ?>"
                                                                        id="<?= $cra . '_expirationMonth' ?>"
                                                                        class="form-control input-sm" <?= $disabled ?> >
                                                                    <option value=""> - Month -</option>
                                                                    <?php echo Strings::listMonths($expirationMonth); ?>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <select name="<?= $cra . '[expirationYear]' ?>"
                                                                        id="<?= $cra . '_expirationYear' ?>"
                                                                        class="form-control input-sm" <?= $disabled ?> >
                                                                    <option value=""> - Year -</option>
                                                                    <?php echo Strings::listYears($expirationYear); ?>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[cardHolderName]' ?>">Card
                                                                Holders Name on Card</label>
                                                            <div class="col-md-7">
                                                                <input type="text"
                                                                       name="<?= $cra . '[cardHolderName]' ?>"
                                                                       id="<?= $cra . '_cardHolderName' ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm"
                                                                       placeholder="Card Holder Name"
                                                                       value="<?php echo htmlentities($cardHolderName); ?>" <?= $disabled ?> >
                                                            </div>
                                                        </div>

                                                    </div> <!-- Credit card info end -->

                                                    <div class="col-md-6">
                                                        <label class="bg-secondary  py-2  col-lg-12 mb-4"><b>Billing
                                                                Address</b></label>
                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[billingAddress1]' ?>">Address
                                                                Line 1</label>
                                                            <div class="col-md-7">
                                                                <script>
                                                                    $(document).ready(function () {
                                                                        $('#<?= $cra . '_billingAddress1' ?>').on('input', function () {
                                                                            address_lookup.InitLegacy($(this));
                                                                        });
                                                                    });
                                                                </script>
                                                                <input type="text"
                                                                       name="<?= $cra . '[billingAddress1]' ?>"
                                                                       id="<?= $cra . '_billingAddress1' ?>"
                                                                       data-address="<?= $cra . '_billingAddress1' ?>"
                                                                       data-city="<?= $cra . '_billingCity' ?>"
                                                                       data-state="<?= $cra . '_billingState' ?>"
                                                                       data-zip="<?= $cra . '_billingZip' ?>"
                                                                       data-unit="<?= $cra . '_billingAddress2' ?>"
                                                                       value="<?php echo htmlentities($thirdPartyServicesDetails['cra_' . $cra]['billingAddress1']); ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm"
                                                                       placeholder="Address Line 1" <?= $disabled ?> >
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[billingAddress2]' ?>">Address
                                                                Line 2</label>
                                                            <div class="col-md-7">
                                                                <input type="text"
                                                                       name="<?= $cra . '[billingAddress2]' ?>"
                                                                       id="<?= $cra . '_billingAddress2' ?>"
                                                                       value="<?= $thirdPartyServicesDetails['cra_' . $cra]['billingAddress2']; ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm"
                                                                       placeholder="Address Line 2" <?= $disabled ?> >
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[billingCity]' ?>">City</label>
                                                            <div class="col-md-7">
                                                                <input type="text"
                                                                       name="<?= $cra . '[billingCity]' ?>"
                                                                       id="<?= $cra . '_billingCity' ?>"
                                                                       value="<?= $thirdPartyServicesDetails['cra_' . $cra]['billingCity']; ?>"
                                                                       autocomplete="off" class="form-control input-sm"
                                                                       placeholder="City" <?= $disabled ?> >
                                                            </div>
                                                        </div>

                                                        <div class="form-group row">
                                                            <label class="col-md-5 font-weight-bold"
                                                                   for="<?= $cra . '[billingState]' ?>">State</label>
                                                            <div class="col-md-4">
                                                                <select class="form-control input-sm"
                                                                        name="<?= $cra . '[billingState]' ?>"
                                                                        id="<?= $cra . '_billingState' ?>"
                                                                        class="form-control input-sm" <?= $disabled ?> >
                                                                    <option value=""> - Select -</option>
                                                                    <?php
                                                                    for ($s = 0; $s < count($stateArray); $s++) {
                                                                        $sel = '';
                                                                        if ($thirdPartyServicesDetails['cra_' . $cra]['billingState'] == trim($stateArray[$s]['stateCode'])) $sel = 'selected';
                                                                        ?>
                                                                        <option value="<?php echo trim($stateArray[$s]['stateCode']) ?>" <?php echo $sel; ?> > <?php echo $stateArray[$s]['stateName'] ?> </option>
                                                                    <?php } ?>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <input type="text"
                                                                       name="<?= $cra . '[billingZip]' ?>"
                                                                       id="<?= $cra . '_billingZip' ?>"
                                                                       value="<?= $thirdPartyServicesDetails['cra_' . $cra]['billingZip']; ?>"
                                                                       autocomplete="off"
                                                                       class="form-control input-sm zipCode"
                                                                       placeholder="Zip" <?= $disabled ?> >
                                                            </div>
                                                        </div>

                                                    </div><!-- Billing Address End -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        <?php } else { ?>
                                <div class="accordion border border-secondary cra_products <?= $cra . '_products'; ?> mb-2"
                                     id="<?= $cra . '_accordion' ?>" <?= $display ?> >
                                    <div class="card">
                                        <div class="card-header d-flex">
                                            <div class="col-md-10 card-title collapsed" data-toggle="collapse"
                                                 data-target="#<?= $cra . '_accordionBody' ?>" aria-expanded="false">
                                                <?= $craInfo->Name; ?>
                                            </div>
                                            <div class="col-md-2 text-right" id="<?= $cra . '_reports'; ?>" >
                                            <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon m-2 toggleClass collapsed"
                                                  data-card-tool="toggle" data-target="#<?= $cra . '_accordionBody' ?>"
                                                  data-toggle="collapse" title="" aria-expanded="false" data-original-title="" >
                                                <i class="icon-1x ki icon-nm ki-arrow-down"></i>
                                            </span>
                                            </div>
                                        </div>

                                        <div id="<?= $cra . '_accordionBody' ?>" class="collapse" data-parent="#<?= $cra . '_accordion' ?>" >
                                            <div class="card-body">
                                                <div class="col-md-12 <?= $cra . '_reports'; ?> pt-2" >
                                                    <label for="check_all_<?= $cra . '_reports'; ?>" >
                                                        <input class="newCheck check_all_report" type="checkbox" name="check_all_<?= $cra . '_reports'; ?>"
                                                               id="check_all_<?= $cra . '_reports'; ?>" value="<?= $cra . '_report'; ?>" >
                                                        <span>&nbsp;</span>
                                                    </label>
                                                    <span> <h7>Select All</h7> </span>
                                                </div>

                                                <div class="col-md-12 pb-4 <?= $cra . '_reports'; ?>">
                                                    <div class="pad5" style="background-color: rgb(242, 244, 242);">
                                                        <div class="row">
                                                            <?php
                                                            foreach ($craInfo->Services as $productKey => $productName) {
                                                                $identifier = $cra . '_' . $productKey;
                                                                ?>
                                                                <div class="col-md-3">
                                                                    <label for="<?= $identifier; ?>">
                                                                        <input class="newCheck <?= $cra . '_report'; ?>" type="checkbox"
                                                                               name="thirdPartyServicesProducts[]" id="<?= $identifier; ?>"
                                                                               value="<?= $identifier; ?>"
                                                                            <?= in_array($identifier, $thirdPartyServicesProductsArray) ? 'checked' : ''; ?> >
                                                                        <span>&nbsp;</span>
                                                                    </label>
                                                                    <?= $productName['Name']; ?>
                                                                </div>
                                                            <?php } // service loop end
                                                            ?>
                                                        </div>
                                                        <div class="clearfix"></div>
                                                        <div class="clearfix"></div>
                                                    </div>
                                                </div>

                                                <div class="row <?= $cra . '_reports'; ?>" >
                                                    <div class="col-md-12">
                                                        <label class="bg-secondary py-2  col-lg-12 mb-4"><b>Vendor Credentials</b></label>
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group row">
                                                                    <label class="col-md-5 font-weight-bold" for="<?= $cra . '[username]' ?>">Username</label>
                                                                    <div class="col-md-7">
                                                                        <input type="text" name="<?= $cra . '[username]' ?>"
                                                                               id="<?= $cra . '_username' ?>"
                                                                               value="<?= $thirdPartyServicesDetails['cra_' . $cra]['username']; ?>"
                                                                               autocomplete="off"
                                                                               class="form-control input-sm"
                                                                               placeholder="Username" <?= $disabled ?> >
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 ">
                                                                <div class="form-group row">
                                                                    <label class="col-md-5 font-weight-bold" for="<?= $cra . '[password]' ?>">Password</label>
                                                                    <div class="col-md-7">
                                                                        <input type="text" name="<?= $cra . '[password]' ?>" id="<?= $cra . '_password' ?>"
                                                                               value="<?= $password ?>" autocomplete="off" class="form-control input-sm"
                                                                               placeholder="Password" <?= $disabled ?> >
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="col-md-6">
                                                                <div class="form-group row">
                                                                    <label class="col-md-5 font-weight-bold" for="<?= $cra . '[APIKey]' ?>" > Client ID </label>
                                                                    <div class="col-md-7">
                                                                        <textarea type="text" name="<?= $cra . '[APIKey]' ?>" id="<?= $cra . '_APIKey' ?>"
                                                                               autocomplete="off" class="form-control input-sm" placeholder="Client ID"
                                                                            <?= $disabled ?> ><?= $thirdPartyServicesDetails['cra_' . $cra]['APIKey']; ?></textarea>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 ">
                                                                <div class="form-group row">
                                                                    <label class="col-md-5 font-weight-bold" for="<?= $cra . '[APISecret]' ?>" > Client Secret </label>
                                                                    <div class="col-md-7">
                                                                        <textarea type="text" name="<?= $cra . '[APISecret]' ?>" id="<?= $cra . '_APISecret' ?>"
                                                                               autocomplete="off" class="form-control input-sm" placeholder="Client Secret"
                                                                            <?= $disabled ?> ><?= $thirdPartyServicesDetails['cra_' . $cra]['APISecret']; ?></textarea>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }// CRA loop end
                        ?>

                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12 mb-4">
            <div class="card card-custom">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label"> Legal Docs </h3>
                    </div>
                    <?php $LegalDocsDisabled = 'disabled';
                    if ($thirdPartyServicesLegalDocs == 1) { $LegalDocsDisabled = ''; }
                    ?>
                    <div class="card-toolbar">
                        <span class="switch switch-icon">
                            <label>
                                <input class="form-control"
                                       type="checkbox" <?php if ($thirdPartyServicesLegalDocs == 1) { ?> checked="checked" <?php } ?>
                                    value="<?php echo $thirdPartyServicesLegalDocs ?>"
                                       id="thirdPartyLinkLegalDocs" onchange="toggleSwitch('thirdPartyLinkLegalDocs','thirdPartyServicesLegalDocs','1','0' );showEditSwitch('thirdPartySwitch','thirdPartyServicesLegalDocs');Validation.controlChildElements('thirdPartyLinkLegalDocs','thirdPartyServicesLegalDocsDiv');"/>
                                <input type="hidden" name="thirdPartyServicesLegalDocs"
                                       id="thirdPartyServicesLegalDocs"
                                       value="<?php echo $thirdPartyServicesLegalDocs ?>">
                                <span></span>
                            </label>
                        </span>
                        <span class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer" data-card-tool="toggle"
                              data-section="thirdPartyServiceLegal" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki icon-nm ki-arrow-down"></i>
                        </span>
                    </div>
                </div>

                <div class="card-body thirdPartyServiceLegalDocs" style="display: none" id="thirdPartyServicesLegalDocsDiv">
                    <div class="row" >
                        <div class="col-md-12">
                            <label class="bg-secondary py-2  col-lg-12 mb-4"><b>Vendor Credentials</b></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group row">
                                        <label class="col-md-5 font-weight-bold" for="geracilightningdocs[catalogName]">Catalog Name</label>
                                        <div class="col-md-7">
                                            <input type="text" name="geracilightningdocs[catalogName]" id="geracilightningdocs[catalogName]"
                                                   value="<?= $thirdPartyServicesDetails['legaldocs_geracilightningdocs']['catalogName']; ?>" autocomplete="off"
                                                   class="form-control input-sm" placeholder="Catalog Name" >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php if ($userRole == glUserRole::USER_ROLE_MANAGER || $userRole == glUserRole::USER_ROLE_SUPER || $userRole == glUserRole::USER_ROLE_SALES) { ?>
            <div class="col-md-12" style="text-align: center;">
                <input class="btn btn-primary" type="submit" name="save" id="save" value="Save">
            </div>
        <?php } ?>
    </form>
</div>
<script>
    $("#creditCardType").change(function () {
        var fldVal = $(this).val();
        if (fldVal == 'AX') {
            $('.mask_ccNumber').inputmask({"mask": "9999 999999 99999", "placeholder": "____ ______ _____"});
            $('.mask_CVV').inputmask('9999');
        } else if (fldVal != '') {
            $('.mask_ccNumber').inputmask({"mask": "9999 9999 9999 9999", "placeholder": "____ ____ ____ ____"});
            $('.mask_CVV').inputmask('999');
        } else { // empty
            $('.mask_ccNumber').val('');
            $('.mask_ccNumber').inputmask({"mask": "9999 9999 9999 9999", "placeholder": "____ ____ ____ ____"});
        }
    });
</script>
<script type="text/javascript">
    $(function () {
<?php
        if (trim(Strings::showField('CCType', 'CCInfo')) == 'AX') {    // Ticket - https://trello.com/c/crTFKGZa
        ?>
        $('.mask_CVV').inputmask('9999');
        $('.mask_ccNumber').inputmask({"mask": "9999 999999 99999", "placeholder": "____ ______ _____"});
        <?php
        } else {
        ?>
        $('.mask_CVV').inputmask('999');
        $('.mask_ccNumber').inputmask({"mask": "9999 9999 9999 9999", "placeholder": "____ ____ ____ ____"});
        <?php
        }
        ?>
    });
</script>

<script type="text/javascript">
    function showAndHideModuleServiceTypesForPC(t, val, opt) {
        console.log({
            func: 'showAndHideModuleServiceTypesForPC',
            t: t,
            val: val,
            opt: opt,
        })
        if (t === 'moduleType[]') {
            if (opt === 'show') {
                $('#div_' + val).show();
            } else {
                $('#div_' + val).hide();
            }
        }
    }

    $(document).ready(function () {
        $('#thirdPartyServiceCSR').change(function () {
            $('.cra_products').css('display', 'none').find('input, select').prop('disabled', true);
            var fldVal = $(this).val().toString();
            var fldArr = fldVal.split(",");
            for (var i = 0; i < fldArr.length; i++) {
                $('.' + fldArr[i] + '_products').css('display', 'block').find('input, select').prop('disabled', false);;
            }
            return false;
        });

        $('.check_all_report').click(function () {
            var fldVal = $(this).val().toString();
            if ($(this).prop('checked') === true) {
                $("." + fldVal).each(function () {
                    $(this).prop('checked', true);
                });
            } else {
                $("." + fldVal).each(function () {
                    $(this).prop('checked', false);
                });
            }
        });
    });

    function showHideCRA(id) {
        $('#' + id).toggle(500);
    }
</script>
        