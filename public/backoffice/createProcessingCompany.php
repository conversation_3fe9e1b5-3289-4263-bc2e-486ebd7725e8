<?php
global $userGroup, $PCID, $userRole, $isPCActive;

use models\composite\oModules\getModules;
use models\composite\oPC\getPCModules;
use models\composite\oServiceType\getServiceTypes;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glPackageCategoryArray;
use models\constants\gl\glRAMAccessPC;
use models\constants\gl\glUserRole;
use models\Database2;
use models\lendingwise\tblFieldsQuickApp;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\BaseHTML;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';
require '../includes/router.php';

include CONST_BO_PATH . 'initPageVariables.php';
include CONST_BO_PATH . 'getPageVariables.php';

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$glRAMAccessPC = glRAMAccessPC::$glRAMAccessPC;
$glPackageCategoryArray = glPackageCategoryArray::$glPackageCategoryArray;

UserAccess::CheckAdminUse();
if (isset($_REQUEST['pcId'])) {  //for super really or people who bookmarked
    $assignedPCID = cypher::myDecryption(Request::GetClean('pcId'));
} else {
    if (isset($_SESSION['PCID']) && $_SESSION['PCID'] > 0) {
        $assignedPCID = $_SESSION['PCID'];
    }
}
if ((trim($userGroup) == 'Super') || (trim($userGroup) == 'Sales') || (trim($userGroup) == 'Employee' && ($PCID == $assignedPCID) && $assignedPCID > 0)) {
} else {
    $redirectFile = CONST_SITE_URL . 'unauthorizedPage.php';
    header('Location: ' . $redirectFile);
    exit();
}


if (
    ($_SERVER['REQUEST_METHOD'] ?? null) === 'POST'
    && ($_REQUEST['tabNumb'] ?? null) == 17
) {
    $sectionID = Request::GetClean('sectionID');
    foreach (Request::GetClean('field') ?? [] as $fieldID => $settings) {
        $field = tblFieldsQuickApp::Get([
            'PCID' => $PCID,
            'fieldID' => $fieldID,
            'sectionID' => $sectionID
        ]);

        if (!$field) {
//            Debug($fieldID, $settings, [
//                'PCID' => $PCID,
//                'fieldID' => $fieldID,
//                'sectionID' => $sectionID
//            ]);
            continue;
        }

        $field->FADisplay     = (int)($settings['FADefaultShow'] ?? 0);
        $field->BODisplay     = (int)($settings['BODefaultShow'] ?? 0);
        $field->QADisplay     = (int)($settings['QADefaultShow'] ?? 0);

        $field->FAMandatory   = (int)($settings['FADefaultMandatory'] ?? 0);
        $field->mandatory     = (int)($settings['QADefaultMandatory'] ?? 0);
        $field->BOMandatory   = (int)($settings['BODefaultMandatory'] ?? 0);


        $field->fileType = implode(',', $settings['module'] ?? []);
        $field->loanProgram = implode(',', $settings['program'] ?? []);

        $field->Save();
    }
    HTTP::ReloadPage();
}

$tabNumb = 1;
$publicUser = 0;
$userId = 0;
$executiveId = 0;
$PLOId = 0;
$modulesArray = [];
$brokerNumber = 0;
$regnFor = '';
$serviceTypeArray = [];
$libModulesArray = $libModulesStatusArray = [];
$opt1 = '';
$hideBorrowerInfo = '';

if (isset($_REQUEST['tabNumb'])) $tabNumb = trim(Request::GetClean('tabNumb'));

$userEmail = $_SESSION['userEmail'] ?? null;

echo BaseHTML::openPage('Company Profile - ' . CONST_DOMAIN, 1, 1);

?>

<?php
$scriptArray = [
    '/assets/js/LMRequest.js',
    '/assets/js/processingCompany.js',
    '/assets/js/CWForm.js',
    '/assets/js/fileStatus.js',
    '/assets/js/fileSubstatus.js',
    '/assets/js/ccValidation.js',
    '/assets/js/PCPayment.js',
    '/assets/js/loan_math/per_diem.js',
    '/assets/js/PCFileDocuments.js',
    '/assets/js/3rdParty/tinymce-5.6.1/tinymce.bundle.js',
    '/assets/js/pops/addChecklistItemControls.js',
];
if ($tabNumb == 13) {
    $scriptArray[] = '/assets/js/HMLOLoanInfo.js';
    $scriptArray[] = '/assets/js/loanCalculation.js';
}

/*  Check if the Profile tab is 5 (workflow) or 13 (Loan guidelines)  */
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 5 || $tabNumb == 13)) {
} else {
}
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 13)) {
}
Strings::includeMyScript($scriptArray);

$cssArray = [
    '/assets/styles/autocomplete.css',
];
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 5 || $tabNumb == 13)) {
} else {
    $cssArray[] = '/assets/styles/multi_select.css';
}
if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && ($tabNumb == 13)) {
}
Strings::includeMyCSS($cssArray);


$glLMRClientTypeArray = getServiceTypes::getReport(['activeStatus' => '1']);
//  $libModulesArray = $oModules->getLibModules(array("activeStatus"=> '1'));
if ($userRole == 'Super') {
    $opt1 = 'moduleInfo';
    $ip['opt1'] = $opt1;
    $ip['PCID'] = $assignedPCID;
    if ($tabNumb == 1) $ip['subscribedOpt'] = 'ALL';
} else {
    $opt1 = 'PCModuleInfo';
    $ip['opt1'] = $opt1;
    $ip['PCID'] = $assignedPCID;
    if ($tabNumb == 1) $ip['subscribedOpt'] = 'PC';
}

$modulesResultArray = [];
$tempLibModulesArray = [];
$modulesResultArray = getModules::getReport($ip);
if (count($modulesResultArray) > 0) {
    if (array_key_exists($opt1, $modulesResultArray)) {
        $tempLibModulesArray = $modulesResultArray[$opt1];
        unset($modulesResultArray[$opt1]);
    }
}
for ($j = 0; $j < count($tempLibModulesArray); $j++) {
    $libModulesArray[trim($tempLibModulesArray[$j]['moduleCode'])] = trim($tempLibModulesArray[$j]['moduleName']);
    /*if ($userRole == 'Super') {
} else {
$libModulesStatusArray[trim($tempLibModulesArray[$j]['moduleCode'])] = trim($tempLibModulesArray[$j]['activeStatus']);
}*/
}

$ip = ['PCID' => $assignedPCID];
$ip['keyNeeded'] = 'n';

if ($assignedPCID > 0) {
    $ip = ['PCID' => $assignedPCID];
    $ip['keyNeeded'] = 'n';
    //  $servicesRequested      = oPC::getPCServiceType($ip);                           /** Fetch PC services requested **/
    $modulesArray = getPCModules::getReport($ip);
}

$fileModules = [];
for ($j = 0; $j < count($modulesArray); $j++) {
    //if(trim($modulesArray[$j]['moduleCode']) == 'SLM') { /** Skip SLM from selected services to show in separate DIV **/
    //} else {
    $fileModules[] = $modulesArray[$j]['moduleCode'];
    //}
}

$tempArray = [];
$tempArray = array_keys($glPackageCategoryArray);
for ($i = 0; $i < count($tempArray); $i++) {
    $fileModules[] = $tempArray[$i];
}

$tempPkgGroups = array_merge($libModulesArray, $glPackageCategoryArray);
$exInArray['TABLENAME'] = 'tblProcessingCompany';
$exResultArray = [];
$exInArray['FIELDNAMEARRAY'] = ['isPLO', 'processingCompanyName', 'hideBorrowerInfo'];
$exInArray['CONDITION'] = ' where activeStatus = 1 and dstatus = 0 and PCID = ' . $assignedPCID . ' ';

$exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);

if (count($exResultArray) > 0) {
    $isPLO = trim($exResultArray['isPLO']);
    $PCName = trim($exResultArray['processingCompanyName']);
    $hideBorrowerInfo = trim($exResultArray['hideBorrowerInfo']);
}
$video = ' <a target="_blank" class="purple-text px-6 h5"
                                               href="https://www.youtube.com/watch?v=KP_9SjroPB4"
                                               style="text-decoration:none;">Help
                            Video: Customizing your system</a>';
if ($userRole != 'Manager' && $userRole != 'Super') {
    $titleArray = [['href' => '#', 'title' => 'Processing Company Profile' . $video]];
} else if ($assignedPCID > 0 && $userRole == 'Super') {
    $titleArray = [['href' => 'processingCompanyList.php', 'title' => 'Processing Company List'], ['href' => '#', 'title' => 'Update Company Profile &amp; System Settings - ' . htmlspecialchars($PCName)]];
} else if ($assignedPCID > 0) {
    $titleArray = [['href' => '#', 'title' => 'Update Company Profile &amp; System Settings - ' . htmlspecialchars($PCName) . $video]];
} else {
    $titleArray = [['href' => '#', 'title' => 'Create Processing Company' . $video]];
}


/* Dynamic $Breadcrumb */
$Breadcrumb = [];
$Breadcrumb['icon'] = 'fas fa-users icon-md';
$Breadcrumb['breadcrumbList'] = '';//array(array('href' => '#', 'title' => 'Sample home'), array('href' => '#', 'title' => 'Sample Child'));
$Breadcrumb['toolbarHTML'] = '';
/* End of Dynamic $Breadcrumb */
$Breadcrumb['title'] = 'Back Office';/* Dynamic $Breadcrumb */
$Breadcrumb = [];
$Breadcrumb['icon'] = 'fas fa-users icon-md';
$Breadcrumb['breadcrumbList'] = $titleArray;
$Breadcrumb['toolbarHTML'] = '';
/* End of Dynamic $Breadcrumb */
//$Breadcrumb['title'] = 'Company Info';

require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');
$customCSSArray = [];
$customCSSArray = ['/assets/js/3rdParty/datatables-1.10.22/datatables.bundle.css'];
Strings::includeMyCSS($customCSSArray);

$scriptArray = [
    '/assets/js/3rdParty/datatables-1.10.24/datatables.min.js',
    '/assets/js/3rdParty/datatables-1.10.24/dataTables.bootstrap4.min.js',
];
Strings::includeMyScript($scriptArray);
?>
<script>
    <?php
    if ($userRole == 'Super') { ?>
    function showTabUrl(tabNumb, PCID) {
        window.location.href = "/backoffice/createProcessingCompany.php?pcId=" + PCID + "&tabNumb=" + tabNumb;
    }
    <?php } else { ?>
    function showTabUrl(tabNumb, PCID) {
        window.location.href = "/backoffice/createProcessingCompany.php?tabNumb=" + tabNumb;
    }
    <?php } ?>

    var assignedPCID = 0;
    assignedPCID = '<?php echo htmlspecialchars($assignedPCID)?>';

    function showSMTPInfo(opt, smtpId) {
        if (opt == 1) {
            document.getElementById(smtpId).style.display = 'block';
        } else {
            document.getElementById(smtpId).style.display = 'none';
        }
    }

    function showFaxInfo(opt1, faxId) {
        if (opt1 == 1) {
            document.getElementById(faxId).style.display = 'block';
        } else {
            document.getElementById(faxId).style.display = 'none';
        }
    }

</script>
<style>
    .acctTable td {
        padding: 5px;
    }

    .padLeft50 {
        padding-left: 50px;
    }

    .divW295 {
        width: 295px;
    }

    .overlayout {
        z-index: 20000;
        -moz-opacity: 0.5;
        opacity: .50;
        filter: alpha(opacity=50);
        background-color: #FAFCFD;
    }

    .overlay-alert {
        clear: both;
        padding: 0px;
        margin: 0px;
        font-family: arial;
        font-weight: bold;
        font-size: 20px;
        color: #00326d;
        line-height: 42px;
        border: 0px solid #d2d2d2;
        z-index: 20000;
        position: absolute;
        top: 280px;
        left: 680px;
        width: 28%;
        height: auto;
        zoom: 1;
        color: red;
    }

    #tabledivbody tr, .connectedSortable tr, .ui-sortable-helper {
        cursor: move;
    }

    .connectedSortable tr:first-child {
        cursor: default;
    }
</style>

<div class="card card-custom p-0 card-sticky " id="kt_page_sticky_card">
    <div class="card-header px-2 py-2 border pipelineNavigation" style="">
        <ul class="nav nav-pills">
            <?php
            if ($tabNumb == 1) $newHref = ''; else  $newHref = "onclick=\"javascript:showTabUrl('1', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
            ?>
            <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                <a class="nav-link px-4 py-2 <?php if ($tabNumb == 1) {
                    echo 'active';
                } else {
                    echo '';
                } ?> " href="#" <?php echo $newHref ?>>Company Info</a>
            </li>
            <?php
            if ($tabNumb == 2 && $assignedPCID > 0) {
                $newHref = '';
            } else if ($assignedPCID > 0) {
                $newHref = " onclick=\"javascript:showTabUrl('2', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
            } else {
                $newHref = '';
            } ?>
            <li class=" nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass ">
                <a class="nav-link px-4 py-2 <?php if ($tabNumb == 2) {
                    echo 'active';
                } else {
                    echo '';
                } ?> " href="#" <?php echo $newHref ?>>Doc Library</a>
            </li>
            <?php
            if ($userRole == glUserRole::USER_ROLE_SUPER || $userRole == glUserRole::USER_ROLE_MANAGER || $userRole == glUserRole::USER_ROLE_SALES) {
                if ($tabNumb == 20 && $assignedPCID > 0) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('20', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                } ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 20) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?> " href="#" <?php echo $newHref ?>>3rd Party Integrations</a>
                </li>
            <?php }
            if ($userRole == 'Super' || $userRole == 'Sales') {
                if (($tabNumb == 3) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('3', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 3) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?> " href="#" <?php echo $newHref ?>>Activate</a>
                </li>
                <?php
            }
            if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) {

                if (($tabNumb == 4) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('4', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass"><a
                            class="nav-link px-4 py-2 <?php if ($tabNumb == 4) {
                                echo 'active';
                            } else {
                                echo '';
                            } ?>" href="#" <?php echo $newHref ?>>Required Docs</a>
                </li>

                <?php
                if (($tabNumb == 19) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('19', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php echo ($tabNumb == 19 ? 'active' : ''); ?>"
                       href="#" <?php echo $newHref ?>>Document Statuses</a>
                </li>

                <?php
                if (($tabNumb == 21) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('21', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class=" nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass ">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 21) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?> " href="#" <?php echo $newHref ?>>Borrower Profile Required Docs</a>
                </li>


                <?php
                if (($tabNumb == 5) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('5', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 5) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>Workflow</a>
                </li>
                <?php
                /*
                * Allow Secondary WF feature for the PCs =  Dave PC, Enrollment Advisory, Law offices
                */
                if (in_array($assignedPCID, $accessSecondaryWFPC)) {
                    if (($tabNumb == 9) && (in_array($assignedPCID, $accessSecondaryWFPC))) {
                        $newHref = '';
                    } else if (in_array($assignedPCID, $accessSecondaryWFPC)) {
                        $newHref = " onclick=\"javascript:showTabUrl('9', '" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                    } else {
                        $newHref = '';
                    }
                    ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 9) {
                            echo 'active';
                        } else {
                            echo '';
                        } ?>" href="#" <?php echo $newHref ?>>Workflow Actions</a>
                    </li>

                    <?php
                }

                /* Check profile >>> tab = 6 (File Status) */
                if (($tabNumb == 6) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('6','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 6) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>File Status</a>
                </li>
                <?php

                /* Check profile >>> tab = 7 (File Sub-status) */
                if (($tabNumb == 7) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('7','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 7) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>File Substatus</a>
                </li>
                <?php
            }
            //        if ($isPLO == 1) {   //part commented for clubhouse11507 - remove isPLO constraint
            if (($tabNumb == 11) && ($assignedPCID > 0)) {
                $newHref = '';
            } else if ($assignedPCID > 0) {
                $newHref = " onclick=\"javascript:showTabUrl('11','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
            } else {
                $newHref = '';
            }

            ?>
            <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                <a class="nav-link px-4 py-2 <?php if ($tabNumb == 11) {
                    echo 'active';
                } else {
                    echo '';
                } ?>" href="#" <?php echo $newHref ?>>Web Forms/ Logins</a>
            </li>
            <?php
            //        }

            //if ( in_array('HMLO', $fileModules)) {
            if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && (in_array('HMLO', $fileModules) || in_array('loc', $fileModules))) {
                if (($tabNumb == 13) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('13','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 13) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>Custom Loan Guidelines</a>
                </li>
            <?php } ?>

            <?php
            /**
             * Description : New Tab for "File tabs Display order"
             * Date        : Aug 31, 2017
             * Developer   : Viji
             **/
            if (($tabNumb == 14) && ($assignedPCID > 0)) {
                $newHref = '';
            } else if ($assignedPCID > 0) {
                $newHref = " onclick=\"javascript:showTabUrl('14','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
            } else {
                $newHref = '';
            }
            ?>
            <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                <a class="nav-link px-4 py-2 <?php if ($tabNumb == 14) {
                    echo 'active';
                } else {
                    echo ' ';
                } ?>" href="#" <?php echo $newHref ?>>File Tab Display Order</a>
            </li>
            <?php

            if ($userRole == 'Super' || ($userRole == 'Manager' && $isPCActive)) {
                if (($tabNumb == 16) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('16','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 16) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>Form Fields</a>
                </li>
                <?php
            }

            if (
                stristr($userEmail, 'lendingwise.com') !== false
                || $userRole == 'Super'
            ) {
                if (($tabNumb == 17) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('17','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 17) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>Form Fields V2</a>
                </li>
                <?php
            }
            if ($userRole == 'Super' || ($userRole == 'Manager' && $isPCActive)) {
                if (($tabNumb == 18) && ($assignedPCID > 0)) {
                    $newHref = 'onclick=window.location=\'/backoffice/settings/custom_fields?pcId=' . cypher::myEncryption($assignedPCID) . '\'';
                } elseif ($assignedPCID > 0) {
                    $newHref = 'onclick=window.location=\'/backoffice/settings/custom_fields?pcId=' . cypher::myEncryption($assignedPCID) . '\'';
                } else {
                    $newHref = 'onclick=window.location=\'/backoffice/settings/custom_fields?pcId=' . cypher::myEncryption(PageVariables::$PCID) . '\'';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 18) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>Custom Form Fields</a>
                </li>
                <?php
            }
            if ($userRole == 'Super' || ($userRole == 'Manager' && $isPCActive)) {
                if (($tabNumb == 22) && ($assignedPCID > 0)) {
                    $newHref = '';
                } else if ($assignedPCID > 0) {
                    $newHref = " onclick=\"javascript:showTabUrl('22','" . htmlspecialchars(cypher::myEncryption($assignedPCID)) . "');\"";
                } else {
                    $newHref = '';
                }
                ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 22) {
                        echo 'active';
                    } else {
                        echo '';
                    } ?>" href="#" <?php echo $newHref ?>>Draw Management</a>
                </li>
                <?php
            }
            ?>
        </ul>
    </div>
    <div class="card-body">
        <?php
        if ($tabNumb == 1) {
            require 'processingCompanyForm.php';
        } else if ($tabNumb == 2 && $assignedPCID > 0) {
            require 'packageList.php';
        } else if ($tabNumb == 3) {
            $paymentURL = 'backoffice/createProcessingCompany.php';
            if ($publicUser == 1 || $userRole == 'Super' || $userRole == 'Sales') {
                require 'PCPaymentForm.php';
            }
        } else if ($tabNumb == 4) {
            if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) {
                require 'checklistForm.php';
            }
        } else if ($tabNumb == 5) {
            if (($userRole == 'Super') || $userRole == 'Sales' || (($userRole == 'Manager') && ($isPCActive))) {
                require 'PCWorkflowForm.php';
            }
        } else if ($tabNumb == 6) {
            if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) {
                require 'PCFileStatusForm.php';
            }
        } else if ($tabNumb == 7) {
            if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) {
                require 'PCFileSubStatusForm.php';
            }
        } elseif ($tabNumb == 8) {
        } elseif ($tabNumb == 9 && (in_array($assignedPCID, $accessSecondaryWFPC))) {
            /*
            * 820 = Dave PC
            * 1469 =  Enrollment Advisory
            */
            if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) {
                require 'PCSecondaryWorkflowForm.php';
            }
        } elseif ($tabNumb == 10 && in_array($assignedPCID, $glRAMAccessPC)) {
            /** Show RAM tab **/
            if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) {
                require 'PCRAMIntegrationForm.php';
            }
        } elseif ($tabNumb == 11 /*&& $isPLO == 1*/) {  //part commented for clubhouse11507
            require 'PCWebForms.php';
        } elseif ($tabNumb == 12) {
        } elseif ($tabNumb == 13) {
            if (($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) && (in_array('HMLO', $fileModules) || in_array('loc', $fileModules))
            ) require 'PCBasicLoanTermsForm.php';
        } elseif ($tabNumb == 14) {
            require 'PCFileTabsDispOrderForm.php';
        } elseif ($tabNumb == 16) {
            require 'quickAppCustomFormFields.php';
        } elseif ($tabNumb == 17) {
            require __DIR__ . '/createProcessingCompany/quickAppCustomFormFieldsV2.php';
        } elseif ($tabNumb == 19) {
            require __DIR__ . '/documentStatus.php';
        } elseif ($tabNumb == 20) {
            require 'thirdPartyServiceSettings.php';
        } elseif ($tabNumb == 21) {
            require __DIR__ . '/createProcessingCompany/borrowerProfileRequiredDocs.php';
        } elseif ($tabNumb == 22) {
            require __DIR__ . '/drawManagement/drawManagement.php';
        }
        ?>
    </div>
</div>
<?php
if ($userRole == 'Super' || $userRole == 'Sales' || ($userRole == 'Manager' && $isPCActive)) { ?>
    <script type='text/javascript'>
        $(document).ready(function () {
            <?php if($tabNumb == 2) { ?>
            $(".expander").click(function () {
                $(this).next(".subExpander").toggle('fast');
                $(this).not(".caret").children(".fa").toggleClass("fa-folder-o fa-folder-open-o");
                return false; //signal as event handled, don't bubble up the chain
            }).not(".expanded").next(".subExpander").hide();

            <?php } else if($tabNumb == 7 || $tabNumb == 12) { ?>

            $(".expander").click(function () {
                $(this).next(".subExpander").toggle('fast');
                $(this).not(".caret").find(".fa-icon").toggleClass("fa-minus-square fa-plus-square");
                return false; //signal as event handled, don't bubble up the chain
            }).not(".expanded").next(".subExpander").hide();

            <?php } ?>
        });
    </script>
    <?php
}
?>

<?php
require('../includesNew/_layoutClose.php');
require 'adminFooter.php';
echo BaseHTML::closePage();
require('../includesNew/_customPage.php');
?>
<div id="sessMsg">
    <?php
    echo Strings::DisplayMessage(Strings::GetSess('msg'));
    Strings::SetSess('msg', '');
    ?>
</div>
