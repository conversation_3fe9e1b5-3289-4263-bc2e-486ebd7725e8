class paymentInfo {

    static init() {

        let _paymentMethod = $('input[name="paymentMethod"]:checked');
        if (_paymentMethod.val() === 'check') {
            disableFormFields('cardInfoDiv');
        } else if (_paymentMethod.val() === 'credit') {
            disableFormFields('checkInfoDiv');
        } else {
            disableFormFields('cardInfoDiv');
            disableFormFields('checkInfoDiv');
        }
        this.creditCardInit();
        this.checkInit();
    }

    static creditCardInit() {

        let _markCVVEle = $('.mask_CVV');
        let _markCCNumberEle = $('.mask_ccNumber');
        let _creditCardTypeEle = $('#creditCardType');

        if (_creditCardTypeEle.val() === 'AX') {
            if (!_markCVVEle.is(':disabled')) {
                _markCVVEle.inputmask('9999');
            }
            if (!_markCCNumberEle.is(':disabled')) {
                _markCCNumberEle.inputmask({"mask": "9999 999999 99999", "placeholder": "____ ______ _____"});
            }
        } else if (_creditCardTypeEle.val() !== '') {
            if (!_markCVVEle.is(':disabled')) {
                _markCVVEle.inputmask('999');
            }
            if (!_markCCNumberEle.is(':disabled')) {
                _markCCNumberEle.inputmask({
                    "mask": "9999 9999 9999 9999",
                    "placeholder": "____ ____ ____ ____"
                });
            }

        } else {
            _markCCNumberEle.val('').inputmask({
                "mask": "9999 9999 9999 9999",
                "placeholder": "____ ____ ____ ____"
            });
        }
    }

    static checkInit() {
        $('.mask_routingNumber:not(:disabled)').inputmask('*********'); // | Account and Bank Info >> 9 Digit Bank Routing Number
        //Conditional Enable/Disable
        paymentInfo.servicerException();
    }

    static paymentMethodChange(_method) {
        if (_method === 'credit') {
            this.creditCardInit();
        } else if (_method === 'check') {
            this.checkInit();
        }
        $('.zipCode:not(:disabled)').inputmask("99999");
    }

    static checkPdfDocStatus = function (JobId) {
        if (JobId) {
            let pdfDocReport = parseInt($('#pdfDocReport').val());
            if (!pdfDocReport) {
                let folder = $('#folder').val();
                HTTP.Get('/JQFiles/checkPdfDocStatus.php', {
                    JobId: JobId,
                    folder: folder
                }, function (data) {
                    if (parseInt(data.code) === 100) {
                        if (data.finishedStatus) {
                            let _pdfDocStatus = $('#pdfDocStatus');
                            let _pdfDocStatushref = $('#pdfDocStatushref');
                            $('#pdfDocReport').val('1');
                            _pdfDocStatushref.attr('href', data.uploadUrl);
                            _pdfDocStatus.show();
                            $('#pdfDocLoading').hide();
                        }
                    }
                });
            }
        }
    }

    static servicerException() {
        let _servicerException = $('#ACTServicerException');
        let _actLoanServicer = $('#ACTLoanServicer');
        if (_servicerException.is(':checked')) {
            _actLoanServicer.attr('disabled', false);
        } else {
            _actLoanServicer.attr('disabled', true);
        }
        //set default value for loan Servicer
        let _paymentMethod = $('input[name="paymentMethod"]:checked');
        let _propertyStateBC = $('#propertyStateBC');
        if (_paymentMethod.val() === 'check' &&
            (_propertyStateBC.val() === 'NC' || _propertyStateBC.val() === 'SC')
        ) {
            _actLoanServicer.attr('disabled', false);
            _actLoanServicer.val('ServEase');
        }
        if (_paymentMethod.val() === 'check'
            && (_propertyStateBC.val() === 'AK' || _propertyStateBC.val() === 'DE')) {
            _actLoanServicer.attr('disabled', false);
            _actLoanServicer.val('FCI');
        }
    }
}
paymentInfo.init();

if ($('#jobId').length) {
    setInterval(function () {
        paymentInfo.checkPdfDocStatus($('#jobId').val());
    }, 5000);
}
if ($('.alert-msg').length) {
    setTimeout(function () {
        $('.alert-msg').toggle();
    }, 5000);
}
