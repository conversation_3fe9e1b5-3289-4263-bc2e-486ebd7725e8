class loanInfoV2Form {
    static CV3_BRANCH_ID_RETAIL = 8697;
    static CV3_BRANCH_ID_WHOLESALE = 8608;
    static FILE_BRANCH_ID = parseInt(getFieldsValue('branchId'));

    static showHideRehabCostFinanced(ele) {
        let val = $(ele).val();
        if (val === 'Yes') {
            $('.rehabCostFinancedDiv').show();
        } else {
            $('.rehabCostFinancedDiv').hide();
        }
        $('#rehabCost').val('');
        $('#rehabCostFinanced').val('');
        $('#rehabCostPercentageFinanced').val('');

        loanInfoV2Form.calculatePercentageRehabCostFinanced();
    }

    static showAndHidePrePaymentPenalty(ele) {
        let val = $(ele).val();
        if (val === 'Yes') {
            $('.prePaymentPenaltyChildDiv').show();
        } else {
            $('.prePaymentPenaltyChildDiv').hide();
        }
        $('#prePaymentSelectVal').val('').trigger("chosen:updated");
        $('#prePaymentPenalty').val('').trigger("chosen:updated");
    }

    static showAndHideLoanProgramBasedFields(ele, PCID) {
        let val = $(ele).val();

        loanInfoV2Form.updateInterestReserveType(val);
        loanInfoV2Form.updateFields();

        setTimeout(function () {
            loanInfoV2Form.calculateTotalLoanAmount();
            loanInfoV2Form.calculateInterestReserve();
            loanInfoV2Form.calculateSumOfAllocatedRehabCost();
            loanInfoV2Form.updatePointValues();
            loanInfoV2Form.calculateMonthlyPaymentReserve();
            loanInfoV2Form.updateTotalLoanInterestPayment();
        }, 100);

    }

    static updateFields() {
        let selectElement = $('#propertyConstructionLevel');
        selectElement.prop('disabled', true);
        selectElement.trigger("chosen:updated");
    }

    //[1 => '1st lien', 2 => '2nd lien', 3 => '3rd lien']
    static updateLienPosition(loanProgram) {
        const lienPositions = {
            BRLGUC: [{
                val: 1, text: '1st lien',
            }],
            default: [{
                val: 1, text: '1st lien',
            }, {
                val: 2, text: '2nd lien',
            }, {
                val: 3, text: '3rd lien',
            },]
        };

        const selectElement = $('#lienPosition');
        const selectedVal = selectElement.val();
        selectElement.empty(); // Remove existing options
        globalJS.highlightField(selectElement);

        const options = lienPositions[loanProgram] ? lienPositions[loanProgram] : lienPositions['default'];
        options.forEach(option => {
            selectElement.append(`<option value="${option.val}">${option.text}</option>`);
        });
        selectElement.val(selectedVal);
        selectElement.trigger("chosen:updated");
    }

    static calculatePercentageRehabCostFinanced() {

        let rehabCostFinancedAmt = 0;
        let rehabCost = getFieldsValue('rehabCost');
        let rehabCostPercentageFinanced = getFieldsValue('rehabCostPercentageFinanced');

        if (rehabCostPercentageFinanced > 0) {
            rehabCostFinancedAmt = rehabCostPercentageFinanced / 100.0;
            rehabCostFinancedAmt = rehabCost * rehabCostFinancedAmt;
            rehabCostFinancedAmt = autoNumericConverter(rehabCostFinancedAmt.toFixed(2));
        }
        console.log({
            func: 'loanInfoV2Form.calculatePercentageRehabCostFinanced',
            rehabCostFinancedAmt: rehabCostFinancedAmt,
            rehabCost: rehabCost,
            rehabCostPercentageFinanced: rehabCostPercentageFinanced,
        });
        let _rehabCostFinanced = $('#rehabCostFinanced');
        _rehabCostFinanced.val(rehabCostFinancedAmt);
        globalJS.highlightField(_rehabCostFinanced);

        loanInfoV2Form.updateTotalLoanInterestPayment();
        loanInfoV2Form.calculateConstructionRatio();
        loanInfoV2Form.validateRehabCostFinanced();
        loanInfoV2Form.calculateMonthlyPaymentReserve();
        loanInfoV2Form.calculateTotalLoanAmount();
        loanInfoV2Form.calculateSumOfAllocatedRehabCost();
        loanInfoV2Form.calculatePropertyTotalProjectCost();
    }

    static calculateRehabCostFinancedByPercentage() {
        let rehabCostPercentageFinanced = getFieldsValue('rehabCostPercentageFinanced');
        let rehabCostFinancedAmt = 0;
        let rehabCost = getFieldsValue('rehabCost');

        if (rehabCostPercentageFinanced > 0) {
            rehabCostFinancedAmt = rehabCostPercentageFinanced / 100.0;
            rehabCostFinancedAmt = rehabCost * rehabCostFinancedAmt;
            rehabCostFinancedAmt = autoNumericConverter(rehabCostFinancedAmt.toFixed(2));
        }
        console.log({
            func: 'loanInfoV2Form.calculateRehabCostFinancedByPercentage',
            rehabCostPercentageFinanced: rehabCostPercentageFinanced,
            rehabCostFinancedAmt: rehabCostFinancedAmt,
            rehabCost: rehabCost,
        });

        $('.rehabPercentageFinancedClass').html(rehabCostPercentageFinanced);

        let _rehabCostFinanced = $('#rehabCostFinanced');
        _rehabCostFinanced.val(rehabCostFinancedAmt);
        globalJS.highlightField(_rehabCostFinanced);

        loanInfoV2Form.updateTotalLoanInterestPayment();
        loanInfoV2Form.calculateConstructionRatio();
        loanInfoV2Form.calculateTotalLoanAmount();
        loanInfoV2Form.calculateSumOfAllocatedRehabCost();
        loanInfoV2Form.calculatePropertyTotalProjectCost();
    }

    static calculateRehabValues() {

        //rehabCostFinanced =  rehabCost * rehabCostPercentageFinanced / 100.0
        // rehabCost = rehabCostFinanced * 100 / rehabCostPercentageFinanced;
        //  rehabCostPercentageFinanced = rehabCostFinanced * 100 / rehabCost;

        let rehabCost = getFieldsValue('rehabCost');
        let rehabCostPercentageFinanced = getFieldsValue('rehabCostPercentageFinanced');
        let rehabCostFinanced = getFieldsValue('rehabCostFinanced');

        let rehabCostEle = $('#rehabCost');
        let rehabCostPercentageFinancedEle = $('#rehabCostPercentageFinanced');
        let rehabCostFinancedEle = $('#rehabCostFinanced');

        if (rehabCostFinanced > 0) {
            if (rehabCost === 0 && rehabCostPercentageFinanced === 0) {
                rehabCostEle.val(autoNumericConverter(rehabCostFinanced.toFixed(2)));
                rehabCostPercentageFinancedEle.val('100.00');

                globalJS.highlightField(rehabCostEle);
                globalJS.highlightField(rehabCostPercentageFinancedEle);
            } else if (rehabCostFinanced > rehabCost) {
                let rehabCostUpdated = rehabCostFinanced * 100.0 / rehabCostPercentageFinanced;
                rehabCostEle.val(autoNumericConverter(rehabCostUpdated.toFixed(2)));
                globalJS.highlightField(rehabCostEle);
            } else if (rehabCost > rehabCostFinanced) {
                let rehabCostPercentageFinancedUpdated = rehabCostFinanced * 100.0 / rehabCost;
                rehabCostPercentageFinancedEle.val(loanCalculation.formatNumber(rehabCostPercentageFinancedUpdated,10));
                globalJS.highlightField(rehabCostPercentageFinancedEle);
                $('.rehabPercentageFinancedClass').html(rehabCostPercentageFinancedUpdated.toFixed(3));
                if (rehabCostPercentageFinancedUpdated > 100) {
                    toastrNotification('Rehab/Construction Cost Financed should not be greater than Rehab/Construction Cost', 'error');
                    rehabCostFinancedEle.val('');
                    globalJS.highlightField(rehabCostFinancedEle);
                }
            }
            loanInfoV2Form.updateTotalLoanInterestPayment();
            loanInfoV2Form.calculateConstructionRatio();
            loanInfoV2Form.calculateTotalLoanAmount();
            loanInfoV2Form.calculateSumOfAllocatedRehabCost();
            loanInfoV2Form.calculatePropertyTotalProjectCost();
        }
    }

    static validateRehabCostFinanced() {
        console.log({
            func: 'loanInfoV2Form.validateRehabCostFinanced',
        });
        let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        let rehabCost = getFieldsValue('rehabCost');

        if (parseFloat(rehabCostFinanced) > parseFloat(rehabCost)) {
            toastrNotification('Rehab/Construction Cost Financed should not be greater than Rehab/Construction Cost', 'error');
            $('#rehabCostFinanced').val('');
            return false;
        } else {
            return true;
        }
    }

    static updateTotalLoanInterestPayment() {
        let totalAllocatedLoanAmount = getFieldsValue('totalAllocatedLoanAmount');
        let interestRate = getFieldsValue('lien1Rate');
        let financedAtClosing = getFieldsValue('financedAtClosing');
        let totalLoanInterestPayment;
        let loanProgram = $('#LMRClientType').val();
        let _totalLoanInterestPaymentTooltip = $('#totalLoanInterestPaymentTooltip');
        let totalLoanInterestPaymentTooltip = '';
        let totalLoanInterestPaymentTooltipWithValues = '';
        if (loanProgram === 'BRLGUC') {
            totalLoanInterestPayment = financedAtClosing * (interestRate / 1200);
            totalLoanInterestPaymentTooltip = 'Total Loan InterestPayment = Financed At Closing * (interest Rate / 1200);';
            totalLoanInterestPaymentTooltipWithValues = `${financedAtClosing}  * (${interestRate} / 1200) = ${totalLoanInterestPayment}`;
        } else {
            //let rehabCostFinanced = !$('#rehabCostFinanced').prop('disabled') ? getFieldsValue('rehabCostFinanced') : 0;
            totalLoanInterestPayment = (totalAllocatedLoanAmount) * (interestRate / 1200);
            totalLoanInterestPaymentTooltip = 'Total Loan InterestPayment = (Total Allocated Loan Amount) * (interest Rate / 1200);';
            totalLoanInterestPaymentTooltipWithValues = `${totalAllocatedLoanAmount}  * (${interestRate} / 1200) = ${totalLoanInterestPayment}`;

        }

        let _totalLoanInterestPayment = $('#totalLoanInterestPayment');
        globalJS.highlightField(_totalLoanInterestPayment);
        _totalLoanInterestPayment.val(autoNumericConverter(totalLoanInterestPayment.toFixed(2)));

        _totalLoanInterestPaymentTooltip.attr('data-content', totalLoanInterestPaymentTooltip + '<hr>' + totalLoanInterestPaymentTooltipWithValues);

        loanInfoV2Form.calculateMonthlyPaymentReserve();
        loanInfoV2Form.calculateMonthlyPostClosingPaymentReserves();
    }

    static calculateInterestOnlyMonthlyPayment() {
        return false;
        let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
        let interestRate = getFieldsValue('lien1Rate');

        let InterestOnlyMonthlyPayment = (totalPropertiesLoanAmount * interestRate) / 1200
        $('#interestOnlyMonthlyPayment').val(autoNumericConverter(InterestOnlyMonthlyPayment.toFixed(2)));
        loanInfoV2Form.interestPaymentHoldBack();
    }

    static interestPaymentHoldBack() {
        return false;

        let interestOnlyMonthlyPayment = getFieldsValue('interestOnlyMonthlyPayment');
        let interestPaymentHoldBackMonths = getFieldsValue('interestPaymentHoldBackMonths');

        let interestPaymentHoldBack = interestOnlyMonthlyPayment * interestPaymentHoldBackMonths;

        $('#interestPaymentHoldBack').val(autoNumericConverter(interestPaymentHoldBack.toFixed(2)));
        loanInfoV2Form.calculateTotalLoanAmount();
    }

    static showHideChildFields(ele, childClass) {
        let val = parseInt($(ele).val());

        if (val) {
            $('.' + childClass).show();
        } else {
            $('.' + childClass).hide();
        }
        loanInfoV2Form.calculateBridgeCombinedLoanToValue();
    }

    static calculateTotalLoanAmount() {
        console.log({
            func: 'calculateTotalLoanAmount',
        });
        let totalAllocatedLoanAmount = getFieldsValue('totalAllocatedLoanAmount');
        // let holdbackVal = getFieldsValue('holdbackVal');

        let rehabCostFinanced = !$('#rehabCostFinanced').prop('disabled') ? getFieldsValue('rehabCostFinanced') : 0;
        // let interestPaymentHoldBack = getFieldsValue('interestPaymentHoldBack');

        let loanProgram = $('#LMRClientType').val();
        let totalPropertiesLoanAmount = 0;
        if (loanProgram === 'BRLGUC') {
            //loanInfoV2Form.calculateGUC(); disabled
            loanInfoV2Form.initGroundUpConstruction();
            totalPropertiesLoanAmount = getFieldsValue('GUCTotalLoanAmount');
        } else {
            totalPropertiesLoanAmount = (totalAllocatedLoanAmount)
                //   + (holdbackVal)
                + (rehabCostFinanced);
            //+ (interestPaymentHoldBack);
        }


        //let _totalPropertiesLoanAmount = $('#totalPropertiesLoanAmount');
        let _totalPropertiesLoanAmount = $('.totalPropertiesLoanAmount');
        let _totalPropertiesLoanAmountClass = $('.totalPropertiesLoanAmountClass');
        _totalPropertiesLoanAmount.val(autoNumericConverter(totalPropertiesLoanAmount.toFixed(2)));
        _totalPropertiesLoanAmountClass.val(autoNumericConverter(totalPropertiesLoanAmount.toFixed(2)));

        console.log({
            'Total Allocated Loan Amount (totalAllocatedLoanAmount) : ': totalAllocatedLoanAmount,
            'HoldbackVal (holdbackVal) : ': holdbackVal,
            'Rehab/Construction Cost Financed (rehabCostFinanced) : ': rehabCostFinanced, //      'Interest Payment Holdback (interestPaymentHoldBack) : ': interestPaymentHoldBack,
            'Total Loan Amount ': totalPropertiesLoanAmount,
        });
        globalJS.highlightField(_totalPropertiesLoanAmount);
        //loanInfoV2Form.calculateInterestOnlyMonthlyPayment();
        loanInfoV2Form.calculateBridgeLoanToValue();
        loanInfoV2Form.calculateBridgeCombinedLoanToValue();
        loanInfoV2Form.calculateBridgeRentalMonthly30YrPIPayment();
        loanInfoV2Form.calculateBridgeRentalITIA();
        loanInfoV2Form.getLTCTotalLoanAmount();
    }

    static calculateBridgeLoanToValue() {
        //let totalAsIsValue = getFieldsValue('totalAsIsValue');
        let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
        let totalQualifyingLoanAmount = getFieldsValue('totalQualifyingLoanAmount');

        //  let bridgeLoanToValue = totalPropertiesLoanAmount / totalAsIsValue * 100;
        let bridgeLoanToValue = totalPropertiesLoanAmount / totalQualifyingLoanAmount * 100;
        let _bridgeLoanToValue = $('#bridgeLoanToValue');
        let _bridgeLoanToValueHtml = $('.bridgeLoanToValueHtml');

        globalJS.highlightField(_bridgeLoanToValue);
        _bridgeLoanToValue.val(autoNumericConverter(bridgeLoanToValue.toFixed(2)));

        globalJS.highlightField(_bridgeLoanToValueHtml);
        _bridgeLoanToValueHtml.html(autoNumericConverter(bridgeLoanToValue.toFixed(2)));
    }

    static calculateBridgeCombinedLoanToValue() {
        let totalAsIsValue = getFieldsValue('totalAsIsValue');
        let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
        let subordinateFinancingAmount = 0;
        if (parseInt($('input[name="isPropertyHaveSubordinateFinancing"]:checked').val()) === 1) {
            subordinateFinancingAmount = getFieldsValue('subordinateFinancingAmount');
        }
        let bridgeLoanToValue = (totalPropertiesLoanAmount + subordinateFinancingAmount) / totalAsIsValue * 100;
        let _bridgeCombinedLoanToValue = $('#bridgeCombinedLoanToValue');
        let _bridgeCombinedLoanToValueHtml = $('.bridgeCombinedLoanToValueHtml');

        globalJS.highlightField(_bridgeCombinedLoanToValue);
        _bridgeCombinedLoanToValue.val(autoNumericConverter(bridgeLoanToValue.toFixed(2)));

        globalJS.highlightField(_bridgeCombinedLoanToValueHtml);
        _bridgeCombinedLoanToValueHtml.html(autoNumericConverter(bridgeLoanToValue.toFixed(2)));
    }

    static getQualifiedTotalLoanAmount() {

        let customTotalLoanAmountLogic = parseInt(getFieldsValue('customTotalLoanAmountLogic'));
        let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
        if (customTotalLoanAmountLogic || !totalPropertiesLoanAmount) {
            return getFieldsValue('CORTotalLoanAmt');
        } else {
            return totalPropertiesLoanAmount;
        }
    }

    static setOriginationValue() {
        console.log({
            func: 'setOriginationValue',
        });

        let totalPropertiesLoanAmount = loanInfoV2Form.getQualifiedTotalLoanAmount();
        let originationPointsRate = getFieldsValue('originationPointsRate');

        let originationPointsValue = (originationPointsRate * totalPropertiesLoanAmount) / 100.0;

        let _originationPointsValue = $('#originationPointsValue')
        _originationPointsValue.val(autoNumericConverter(originationPointsValue.toFixed(2)));
        globalJS.highlightField(_originationPointsValue);

        let cv3OriginationPoint = $('#cv3OriginationPoint');
        let cv3ReferralPoint = $('#cv3ReferralPoint');
        if (cv3OriginationPoint.length && cv3ReferralPoint.length) {
            loanInfoV2Form.cv3OriginationPoints();
            loanInfoV2Form.cv3OriginationAmount();
        }
    }

    static setOriginationPoints() {
        console.log({
            func: 'setOriginationPoints',
        });

        let totalPropertiesLoanAmount = loanInfoV2Form.getQualifiedTotalLoanAmount();
        let originationPointsValue = getFieldsValue('originationPointsValue');

        let originationPointsRate = originationPointsValue / totalPropertiesLoanAmount * 100.0;
        originationPointsRate = roundNumber(originationPointsRate, 6);

        let _originationPointsRate = $('#originationPointsRate')
        _originationPointsRate.val(autoNumericConverter(originationPointsRate));
        globalJS.highlightField(_originationPointsRate);

        loanInfoV2Form.cv3OriginationPoints();
        loanInfoV2Form.cv3OriginationAmount();
    }

    static setBrokerPointsValue() {
        console.log({
            func: 'setBrokerPointsValue',
        });

        let totalPropertiesLoanAmount = loanInfoV2Form.getQualifiedTotalLoanAmount();
        let brokerPointsRate = getFieldsValue('brokerPointsRate');

        let brokerPointsValue = (brokerPointsRate * totalPropertiesLoanAmount) / 100.0;

        let _brokerPointsValue = $('#brokerPointsValue')
        _brokerPointsValue.val(autoNumericConverter(brokerPointsValue));
        globalJS.highlightField(_brokerPointsValue);
    }

    static setBrokerPointsRate() {
        console.log({
            func: 'setBrokerPointsRate',
        });
        let totalPropertiesLoanAmount = loanInfoV2Form.getQualifiedTotalLoanAmount();
        let brokerPointsValue = getFieldsValue('brokerPointsValue');

        let brokerPointsRate = (brokerPointsValue / totalPropertiesLoanAmount) * 100.0;
        brokerPointsRate = roundNumber(brokerPointsRate, 6);

        let _brokerPointsRate = $('#brokerPointsRate')
        _brokerPointsRate.val(autoNumericConverter(brokerPointsRate));
        globalJS.highlightField(_brokerPointsRate);
    }

    static cv3OriginationPoints() {
        let cv3OriginationPoint = $('#cv3OriginationPoint');
        let cv3ReferralPoint = $('#cv3ReferralPoint');
        let branchId = loanInfoV2Form.FILE_BRANCH_ID;
        if (cv3OriginationPoint.length && cv3ReferralPoint.length) {
            let originationPoints = $('#originationPointsRate');
            let originationPointValue = originationPoints.val() ? parseFloat(originationPoints.val()) : 0;
            let cv3ReferralPointValue = cv3ReferralPoint.val() ? parseFloat(cv3ReferralPoint.val()) : 0;
            let cv3OriginationPointValue = (originationPointValue) - (cv3ReferralPointValue);
            if (branchId === loanInfoV2Form.CV3_BRANCH_ID_RETAIL) {
                cv3OriginationPointValue = (originationPointValue) - (cv3ReferralPointValue);
            }
            if (branchId === loanInfoV2Form.CV3_BRANCH_ID_WHOLESALE) {
                cv3OriginationPointValue = originationPointValue;
            }
            cv3OriginationPoint.val(cv3OriginationPointValue.toFixed(3));
            cv3ReferralPoint.val(cv3ReferralPointValue.toFixed(3));
            loanInfoV2Form.cv3OriginationAmount();
        }
    }

    static cv3OriginationAmount() {

        let cv3OriginationAmountId = $('#cv3OriginationAmount');
        let cv3ReferralAmountId = $('#cv3ReferralAmount');
        let cv3OriginationPointId = $('#cv3OriginationPoint');
        let cv3ReferralPointId = $('#cv3ReferralPoint');
        let branchId = loanInfoV2Form.FILE_BRANCH_ID;
        let calculatedValue = 0;

        let originationPointValue = getFieldsValue('originationPointsRate');
        let cv3OriginationPointValue = getFieldsValue('cv3OriginationPoint');
        let cv3ReferralPointValue = getFieldsValue('cv3ReferralPoint');

        let originationAmountValue = getFieldsValue('originationPointsValue');
        let cv3OriginationAmountValue = getFieldsValue('cv3OriginationAmount');
        let cv3ReferralAmountValue = getFieldsValue('cv3ReferralAmount');
        let totalPropertiesLoanAmount = loanInfoV2Form.getQualifiedTotalLoanAmount();

        if (cv3OriginationAmountId.length && cv3ReferralAmountId.length) {
            if (cv3ReferralPointValue && cv3ReferralAmountValue) {
                cv3OriginationAmountValue = (parseFloat(totalPropertiesLoanAmount) * parseFloat(cv3OriginationPointValue) / 100).toFixed(2);
                cv3OriginationAmountId.val(autoNumericConverter(cv3OriginationAmountValue, 2));
                globalJS.highlightField(cv3OriginationAmountId);
                if (branchId === loanInfoV2Form.CV3_BRANCH_ID_RETAIL) {
                    calculatedValue = (replaceCommaValues(originationAmountValue) - replaceCommaValues(cv3OriginationAmountValue)).toFixed(2);
                }
                if (branchId === loanInfoV2Form.CV3_BRANCH_ID_WHOLESALE) {
                    cv3ReferralPointValue = (parseFloat(totalPropertiesLoanAmount) * parseFloat(cv3ReferralPointValue) / 100).toFixed(2);
                    calculatedValue = (replaceCommaValues(cv3ReferralPointValue)).toFixed(2);
                }
                cv3ReferralAmountId.val(calculatedValue);
                globalJS.highlightField(cv3ReferralAmountId);

            } else if (cv3ReferralPointValue && !cv3ReferralAmountValue) {
                let x = (cv3ReferralPointValue / originationPointValue).toFixed(2);
                calculatedValue = (x * originationAmountValue);
                cv3ReferralAmountId.val(autoNumericConverter(calculatedValue.toFixed(2)));
                globalJS.highlightField(cv3ReferralAmountId);

                cv3OriginationAmountValue = parseFloat(originationAmountValue) - calculatedValue;
                if (branchId === loanInfoV2Form.CV3_BRANCH_ID_RETAIL) {
                    cv3OriginationAmountValue = parseFloat(originationAmountValue) - calculatedValue;
                }
                if (branchId === loanInfoV2Form.CV3_BRANCH_ID_WHOLESALE) {
                    cv3OriginationAmountValue = parseFloat(originationAmountValue);
                }
                cv3OriginationAmountId.val(autoNumericConverter(cv3OriginationAmountValue.toFixed(2)));
                globalJS.highlightField(cv3OriginationAmountId);

            } else {
                cv3OriginationPointId.val(cv3OriginationPointValue.toFixed(3));//0.000
                cv3OriginationAmountId.val(autoNumericConverter(originationAmountValue, 2)); // Origination Amount Value
                globalJS.highlightField(cv3OriginationAmountId);

                cv3ReferralAmountId.val(cv3ReferralPointValue.toFixed(2));//0.00 / entered value
                globalJS.highlightField(cv3ReferralAmountId);

            }
        }
    }

    static calculateSumOfAllocatedRehabCost() {
        let renovationAllocatedLoanAmount = getFieldsValue('renovationAllocatedLoanAmount');
        let rehabCostFinanced = !$('#rehabCostFinanced').prop('disabled') ? getFieldsValue('rehabCostFinanced') : 0;

        let _renovationSumOfAllocatedRehabCost = $('#renovationSumOfAllocatedRehabCost');
        let _renovationSumOfAllocatedRehabCostHtml = $('#renovationSumOfAllocatedRehabCostHtml');

        let renovationSumOfAllocatedRehabCost = renovationAllocatedLoanAmount + rehabCostFinanced;

        globalJS.highlightField(_renovationSumOfAllocatedRehabCost);
        _renovationSumOfAllocatedRehabCost.val(autoNumericConverter(renovationSumOfAllocatedRehabCost.toFixed(2)));

        globalJS.highlightField(_renovationSumOfAllocatedRehabCostHtml);
        _renovationSumOfAllocatedRehabCostHtml.html(autoNumericConverter(renovationSumOfAllocatedRehabCost.toFixed(2)));
    }

    static calculatePropertyTotalProjectCost() {

        let rehabCost = !$('#rehabCost').prop('disabled') ? getFieldsValue('rehabCost') : 0;
        let costSpent = !$('#costSpent').prop('disabled') ? getFieldsValue('costSpent') : 0;
        let propertyPurchasePrice = getFieldsValue('propertyPurchasePrice');

        let propertyTotalProjectCost = rehabCost + costSpent + propertyPurchasePrice;

        let _propertyTotalProjectCost = $('#propertyTotalProjectCost');
        let _propertyTotalProjectCostHtml = $('#propertyTotalProjectCostHtml');

        globalJS.highlightField(_propertyTotalProjectCost);
        _propertyTotalProjectCost.val(autoNumericConverter(propertyTotalProjectCost.toFixed(2)));

        globalJS.highlightField(_propertyTotalProjectCostHtml);
        _propertyTotalProjectCostHtml.html(autoNumericConverter(propertyTotalProjectCost.toFixed(2)));

        loanInfoV2Form.getLTCTotalLoanAmount();
    }

    static updatedInterestRate() {
        loanInfoV2Form.calculateBridgeRentalMonthly30YrPIPayment();
        loanInfoV2Form.calculateBridgeRentalITIA();
        loanInfoV2Form.calculateInterestReserve();

        // loanInfoV2Form.calculateGUC();//disabled
        loanInfoV2Form.initGroundUpConstruction();
        loanInfoV2Form.updateTotalLoanInterestPayment();
    }

    static calculateBridgeRentalMonthly30YrPIPayment() {

        let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
        let interestRate = getFieldsValue('lien1Rate');
        let property30YrAmortizationPaymentVal = 0;

        let amortizationInfo = loanCalculation.calculateAmortizationPayment({
            amount: totalPropertiesLoanAmount, rate: interestRate, term: '360',
        });
        if (amortizationInfo !== 0) {
            property30YrAmortizationPaymentVal = autoNumericConverter(amortizationInfo.payment_amount.toFixed(2));
        }
        let _monthly30YrPIPayment = $('#monthly30YrPIPayment');
        _monthly30YrPIPayment.val(property30YrAmortizationPaymentVal);

    }

    static calculateBridgeRentalITIA() {

        let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
        let interestRate = getFieldsValue('lien1Rate');
        let bridgeRentalTotalExpenses = getFieldsValue('bridgeRentalTotalExpenses');
        let totalPropertiesITIA;

        totalPropertiesITIA = (((totalPropertiesLoanAmount * interestRate) / 1200) + bridgeRentalTotalExpenses);

        let _totalPropertiesITIA = $('#totalPropertiesITIA');
        _totalPropertiesITIA.val(autoNumericConverter(totalPropertiesITIA.toFixed(2)));
        loanInfoV2Form.calculateMonthlyPaymentReserve();
    }

    static getLTCTotalLoanAmount() {

        let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
        // let propertyTotalProjectCost = getFieldsValue('propertyTotalProjectCost');

        let renovationQualifyingAmount = getFieldsValue('renovationQualifyingAmount');
        let rehabCost = getFieldsValue('rehabCost');
        let costSpent = getFieldsValue('costSpent');

        let qualifyingLoanAmount = renovationQualifyingAmount + rehabCost;
        let propertyMetrics = parseInt($('#propertyMetrics').val());
        if (propertyMetrics === 1) {
            qualifyingLoanAmount += costSpent;
        }

        let LTCTotalLoanAmount = totalPropertiesLoanAmount / qualifyingLoanAmount * 100;

        let _LTCTotalLoanAmountHtml = $('.LTCTotalLoanAmountHtml');
        let _LTCTotalLoanAmount = $('#LTCTotalLoanAmount');

        globalJS.highlightField(_LTCTotalLoanAmountHtml);
        _LTCTotalLoanAmountHtml.html(autoNumericConverter(LTCTotalLoanAmount.toFixed(2)));

        globalJS.highlightField(_LTCTotalLoanAmount);
        _LTCTotalLoanAmount.val(autoNumericConverter(LTCTotalLoanAmount.toFixed(2)));

        console.log({
            func: 'loanInfoV2Form.getLTCTotalLoanAmount',
            renovationQualifyingAmount: renovationQualifyingAmount,
            rehabCostFinanced: rehabCostFinanced,
            costSpent: costSpent,
            qualifyingLoanAmount: qualifyingLoanAmount,
            LTCTotalLoanAmount: LTCTotalLoanAmount,
            final: totalPropertiesLoanAmount + '/' + qualifyingLoanAmount
        });

    }

    static mirrorSubordinateFinancingAmount(ele) {
        $('.subordinateFinancingAmountClass').val($(ele).val());
    }

    static checkPresentOccupancyIsTenantOccupied(presentOccupancyArray, text) {
        let returnVal = true;
        presentOccupancyArray.forEach(function (presentOccupancy) {
            if (!(presentOccupancy.indexOf(text) !== -1) && returnVal) {
                returnVal = false;
            }
        });
        return returnVal;
    }

    static calculateMonthlyPaymentReserve() {

        let loanProgram = $('#LMRClientType').val();
        let loanTerm = $('#loanTerm').val();
        /*        let _monthly30YrPIPayment = $('#monthly30YrPIPayment');
                let monthly30YrPIPayment = _monthly30YrPIPayment.val();
                monthly30YrPIPayment = replaceCommaValues(monthly30YrPIPayment);*/
        let presentOccupancyArray = $('.presentOccupancy').map(function () {
            return $(this).val();
        }).get();
        let monthlyPaymentReserveVal = 0;
        let totalPropertiesPITIA = getFieldsValue('totalPropertiesPITIA');
        //let totalLoanInterestPayment = getFieldsValue('totalLoanInterestPayment');
        let financedInterestReserve = getFieldsValue('financedInterestReserve');
        let totalPropertiesITIA = getFieldsValue('totalPropertiesITIA');
        let totalPropertiesDSCR = getFieldsValue('totalPropertiesDSCR');
        let typeOfHMLOLoanRequesting = $('#typeOfHMLOLoanRequesting').val();

        let isPresentOccupancyIsTenantOccupied = loanInfoV2Form.checkPresentOccupancyIsTenantOccupied(presentOccupancyArray, 'Tenant Occupied');
        let isPresentOccupancyIsVacant = $.inArray('Vacant', presentOccupancyArray) !== -1;
        let isPresentOccupancyIsSellerOccupied = $.inArray('Seller Occupied', presentOccupancyArray) !== -1;
        let midFicoScore_LIV2 = getFieldsValue('midFicoScore_LIV2');
        midFicoScore_LIV2 = parseInt(midFicoScore_LIV2)

        if ($.inArray(loanProgram, ['Ren496', 'Ren949']) !== -1) {
            const LOAN_TERMS = [
                '360 Months- 30 Year Fixed - Fully Amortizing',
                '360 Months- 5/1 ARM - Interest Only',
                '360 Months- 5/6 ARM- 10 Year Interest Only'
            ];
            // if (LOAN_TERMS.includes(loanTerm)) {
            if (loanTerm === '360 Months- 30 Year Fixed - Fully Amortizing') {
                if (loanProgram === 'Ren496' &&
                    isPresentOccupancyIsTenantOccupied &&
                    totalPropertiesDSCR <= 1) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 6;
                } else if (loanProgram === 'Ren496'
                    && (isPresentOccupancyIsVacant || isPresentOccupancyIsSellerOccupied)
                    && totalPropertiesDSCR <= 1) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 8;
                } else if ((isPresentOccupancyIsVacant || isPresentOccupancyIsSellerOccupied)
                    && (typeOfHMLOLoanRequesting !== 'Cash-Out / Refinance'
                        || (typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                            && midFicoScore_LIV2 > 679)
                    )) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 6;
                } else if (isPresentOccupancyIsTenantOccupied
                    && (typeOfHMLOLoanRequesting !== 'Cash-Out / Refinance'
                        || (typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                            && midFicoScore_LIV2 > 679)
                    )) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 4;
                } else if ((isPresentOccupancyIsVacant || isPresentOccupancyIsSellerOccupied)
                    && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                    && midFicoScore_LIV2 < 680) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 10;
                } else if (isPresentOccupancyIsTenantOccupied
                    && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                    && midFicoScore_LIV2 < 680) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 8;
                }
            } else if (loanTerm === '360 Months- 5/1 ARM - Interest Only') {
                if (isPresentOccupancyIsTenantOccupied) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 4;
                } else if ((isPresentOccupancyIsVacant || isPresentOccupancyIsSellerOccupied)) {
                    monthlyPaymentReserveVal = totalPropertiesPITIA * 6;
                }
            } else if (loanTerm === '360 Months- 5/6 ARM- 10 Year Interest Only') {
                if (isPresentOccupancyIsTenantOccupied
                    && (typeOfHMLOLoanRequesting !== 'Cash-Out / Refinance'
                        || (typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                            && midFicoScore_LIV2 > 679)
                    )) {
                    monthlyPaymentReserveVal = totalPropertiesITIA * 4;
                } else if ((isPresentOccupancyIsVacant || isPresentOccupancyIsSellerOccupied)
                    && (typeOfHMLOLoanRequesting !== 'Cash-Out / Refinance'
                        || (typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                            && midFicoScore_LIV2 > 679)
                    )) {
                    monthlyPaymentReserveVal = totalPropertiesITIA * 6;
                } else if (isPresentOccupancyIsTenantOccupied
                    && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                    && midFicoScore_LIV2 < 680) {
                    monthlyPaymentReserveVal = totalPropertiesITIA * 8;
                } else if ((isPresentOccupancyIsVacant || isPresentOccupancyIsSellerOccupied)
                    && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance'
                    && midFicoScore_LIV2 < 680) {
                    monthlyPaymentReserveVal = totalPropertiesITIA * 10;
                }
            }
        } else if ($.inArray(loanProgram, ['BRL', 'Bri879861']) !== -1) {
            //BRL =  Bridge Loan
            //Bri879861 =  Bridge Loan Portfolio

            if (typeOfHMLOLoanRequesting !== 'Cash-Out / Refinance'
                || (midFicoScore_LIV2 > 679 && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance')
            ) {
                monthlyPaymentReserveVal = totalPropertiesITIA * 4;
            } else if (midFicoScore_LIV2 < 680 && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance') {
                monthlyPaymentReserveVal = totalPropertiesITIA * 8;
            }
            //monthlyPaymentReserveVal = totalPropertiesITIA * 4;
        } else if ($.inArray(loanProgram, ['Bri996']) !== -1) {
            //Bridge Loan - Renovation
            let rehabCost = getFieldsValue('rehabCost');
            let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
            let interestPaymentHoldBack = getFieldsValue('interestPaymentHoldBack');

            if (financedInterestReserve || interestPaymentHoldBack) {
                monthlyPaymentReserveVal = Math.max(
                    0.25 * rehabCost,
                    rehabCost - rehabCostFinanced,
                    4 * totalPropertiesITIA
                );
            } else if (typeOfHMLOLoanRequesting !== 'Cash-Out / Refinance') {
                monthlyPaymentReserveVal = Math.max(
                    4 * totalPropertiesITIA,
                    0.25 * rehabCost,
                    rehabCost - rehabCostFinanced);
            } else if (midFicoScore_LIV2 > 679 && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance') {
                monthlyPaymentReserveVal = Math.max(
                    4 * totalPropertiesITIA,
                    0.25 * rehabCost,
                    rehabCost - rehabCostFinanced);
            } else if (midFicoScore_LIV2 <= 679 && typeOfHMLOLoanRequesting === 'Cash-Out / Refinance') {
                monthlyPaymentReserveVal = Math.max(
                    8 * totalPropertiesITIA,
                    0.25 * rehabCost,
                    rehabCost - rehabCostFinanced);
            }

            /*           if (financedInterestReserve === 0) {
                           monthlyPaymentReserveVal = Math.max(4 * totalPropertiesITIA,
                               0.25 * rehabCostFinanced,
                               rehabCost - rehabCostFinanced);
                       } else {
                           monthlyPaymentReserveVal = 0.25 * rehabCostFinanced;
                       }*/
        } else if (loanProgram === 'BRLGUC') {
            let constructionHardCost = getFieldsValue('constructionHardCost');
            let constructionSoftCost = getFieldsValue('constructionSoftCost');
            let contingencyAmount = getFieldsValue('contingencyAmount');
            let financedInterestReserveMonths = getFieldsValue('financedInterestReserveMonths');
            let totalLoanInterestPayment = getFieldsValue('totalLoanInterestPayment');
            let bridgeRentalTotalExpenses = getFieldsValue('bridgeRentalTotalExpenses');
            let param = totalLoanInterestPayment + bridgeRentalTotalExpenses;

            let totalConstructionCost = constructionHardCost + constructionSoftCost + contingencyAmount;
            if (totalConstructionCost > 1000000 && financedInterestReserveMonths >= 6) {
                monthlyPaymentReserveVal = 0.15 * totalConstructionCost;
            } else if (totalConstructionCost > 1000000 && financedInterestReserveMonths < 6) {
                monthlyPaymentReserveVal = 0.15 * totalConstructionCost + ((6 - financedInterestReserveMonths) * param);
            } else if (totalConstructionCost > 500000 && totalConstructionCost <= 1000000 && financedInterestReserveMonths >= 6) {
                monthlyPaymentReserveVal = 0.20 * totalConstructionCost;
            } else if (totalConstructionCost > 500000 && totalConstructionCost <= 1000000 && financedInterestReserveMonths < 6) {
                monthlyPaymentReserveVal = 0.20 * totalConstructionCost + ((6 - financedInterestReserveMonths) * param);
            } else if (totalConstructionCost <= 500000 && financedInterestReserveMonths >= 6) {
                monthlyPaymentReserveVal = 0.25 * totalConstructionCost;
            } else if (totalConstructionCost <= 500000 && financedInterestReserveMonths < 6) {
                monthlyPaymentReserveVal = 0.25 * totalConstructionCost + ((6 - financedInterestReserveMonths) * param);
            }
            //  monthlyPaymentReserveVal = 0.25 * (constructionHardCost + constructionSoftCost + contingencyAmount);
        }

        let _monthlyPaymentReserve = $('#monthlyPaymentReserve');
        globalJS.highlightField(_monthlyPaymentReserve);
        _monthlyPaymentReserve.val(monthlyPaymentReserveVal.toFixed(2));

        let _monthlyPaymentReserveHtml = $('.monthlyPaymentReserveHtml');
        globalJS.highlightField(_monthlyPaymentReserveHtml);
        _monthlyPaymentReserveHtml.html(autoNumericConverter(monthlyPaymentReserveVal.toFixed(2)));

        console.log({
            func: 'loanInfoV2Form.calculateMonthlyPaymentReserve',
            loanProgram: loanProgram,
            loanTerm: loanTerm,
            monthlyPaymentReserveVal: monthlyPaymentReserveVal,
        });
        loanInfoV2Form.calculateFundsToVerify();
    }

    static calculateFundsToVerify() {

        let loanProceeds = getFieldsValue('loanProceeds');
        let monthlyPaymentReserve = getFieldsValue('monthlyPaymentReserve');
        let fundsToVerify;
        if (loanProceeds < 0) {
            fundsToVerify = monthlyPaymentReserve;
        } else {
            fundsToVerify = monthlyPaymentReserve + loanProceeds;
        }

        let _fundsToVerify = $('#fundsToVerify');
        globalJS.highlightField(_fundsToVerify);
        _fundsToVerify.val(fundsToVerify.toFixed(2));

        let _fundsToVerifyHtml = $('.fundsToVerifyHtml');
        globalJS.highlightField(_fundsToVerifyHtml);
        _fundsToVerifyHtml.html(autoNumericConverter(fundsToVerify.toFixed(2)));

        console.log({
            func: 'loanInfoV2Form.calculateFundsToVerify',
            loanProceeds: loanProceeds,
            monthlyPaymentReserve: monthlyPaymentReserve,
            fundsToVerify: fundsToVerify,
        });
        loanInfoV2Form.calculateTotalExcessReserves();
    }

    static calculateTotalExcessReserves() {

        let totalVerifiedAssets = getFieldsValue('totalVerifiedAssets');
        let fundsToVerify = getFieldsValue('fundsToVerify');

        let totalExcessReserves = totalVerifiedAssets - fundsToVerify;

        let _totalExcessReserves = $('#totalExcessReserves');
        globalJS.highlightField(_totalExcessReserves);
        _totalExcessReserves.val(totalExcessReserves.toFixed(2));

        let _totalExcessReservesHtml = $('.totalExcessReservesHtml');
        globalJS.highlightField(_totalExcessReservesHtml);
        _totalExcessReservesHtml.html(autoNumericConverter(totalExcessReserves.toFixed(2)));

        loanInfoV2Form.calculateMonthlyPostClosingPaymentReserves();
    }

    static calculateMonthlyPostClosingPaymentReserves() {

        let totalExcessReserves = getFieldsValue('totalExcessReserves');
        let totalLoanInterestPayment = getFieldsValue('totalLoanInterestPayment');

        let monthlyPostClosingPaymentReserves = Math.round(totalExcessReserves / totalLoanInterestPayment);

        let _monthlyPostClosingPaymentReserves = $('#monthlyPostClosingPaymentReserves');
        globalJS.highlightField(_monthlyPostClosingPaymentReserves);
        _monthlyPostClosingPaymentReserves.val(monthlyPostClosingPaymentReserves);

        let _monthlyPostClosingPaymentReservesHtml = $('.monthlyPostClosingPaymentReservesHtml');
        globalJS.highlightField(_monthlyPostClosingPaymentReservesHtml);
        _monthlyPostClosingPaymentReservesHtml.html(monthlyPostClosingPaymentReserves);

    }

    static sumOfConstructionCost() {
        let constructionHardCost = getFieldsValue('constructionHardCost');
        constructionHardCost = replaceCommaValues(constructionHardCost);

        let constructionSoftCost = getFieldsValue('constructionSoftCost');
        constructionSoftCost = replaceCommaValues(constructionSoftCost);

        return constructionHardCost + constructionSoftCost;
    }

    static calculateContingencyAmount() {

        let constructionCost = loanInfoV2Form.sumOfConstructionCost();

        let contingencyTypeOption = $('#contingencyTypeOption').val().toString();
        let _contingencyPercentage = $('#contingencyPercentage');
        let _contingencyAmount = $('#contingencyAmount');

        if ($.inArray(contingencyTypeOption, ['5', '10']) !== -1) {
            _contingencyPercentage.val(parseFloat(contingencyTypeOption).toFixed(2));
            _contingencyPercentage.attr('readonly', true);
            _contingencyAmount.attr('readonly', true);
        } else if (contingencyTypeOption === 'Other') {
            _contingencyPercentage.attr('readonly', false);
            _contingencyAmount.attr('readonly', false);
        }

        let contingencyPercentage = getFieldsValue('contingencyPercentage');
        let contingencyAmount = constructionCost * (contingencyPercentage / 100);
        contingencyAmount = contingencyAmount.toFixed(2);

        globalJS.highlightField(_contingencyAmount);
        _contingencyAmount.val(autoNumericConverter(contingencyAmount));

        let _contingencyAmountHtml = $('.contingencyAmountHtml');
        globalJS.highlightField(_contingencyAmountHtml);
        _contingencyAmountHtml.html(autoNumericConverter(contingencyAmount));

        /*        loanInfoV2Form.calculateGUCConstructionFinanced_old();
                loanInfoV2Form.calculateGUCTotalProjectCost_old();
                loanInfoV2Form.calculatedInitialLoanToCost_old();
                loanInfoV2Form.calculateSoftCostPercentageOfBudget_old();
                loanInfoV2Form.calculateGUCLoanToCost_old();*/
        loanInfoV2Form.initGroundUpConstruction();
        loanInfoV2Form.calculateMonthlyPaymentReserve();
    }

    static calculateContingencyPercentage() {
        let contingencyAmount = getFieldsValue('contingencyAmount');
        let constructionCost = loanInfoV2Form.sumOfConstructionCost();
        let contingencyPercentage = (contingencyAmount * 100) / constructionCost;

        let _contingencyPercentage = $('#contingencyPercentage');
        globalJS.highlightField(_contingencyPercentage);
        _contingencyPercentage.val(contingencyPercentage.toFixed(5));

        /*        loanInfoV2Form.calculateGUCConstructionFinanced_old();
                loanInfoV2Form.calculateGUCTotalProjectCost_old();
                loanInfoV2Form.calculatedInitialLoanToCost_old();
                loanInfoV2Form.calculateSoftCostPercentageOfBudget_old();
                loanInfoV2Form.calculateGUCLoanToCost_old();*/
        loanInfoV2Form.initGroundUpConstruction();
        loanInfoV2Form.calculateMonthlyPaymentReserve();
    }

    static getMonthsFormLoanTerms() {
        let loanTermMonthsList = $('#loanTerm').val().split(" ");
        return parseInt(loanTermMonthsList[0]) > 0 ? parseInt(loanTermMonthsList[0]) : 0;
    }

    static getGUCInterestReserve(hardCost, softCost, contingency, rate, term, totalLoanAmount) {


        let costRemaining = hardCost + softCost;
        let IR = 0;
        let previousIR = -1;
        const monthlyRate = rate / 12;

        const maxIterations = 10000;
        const tolerance = 1e-12

        let iterations = 0;
        while (Math.abs(IR - previousIR) > tolerance && iterations < maxIterations) {
            iterations++;
            previousIR = IR;
            IR = Math.floor(((0.65 * (costRemaining + contingency)) + (totalLoanAmount - costRemaining - contingency - IR)) * monthlyRate * (term));
        }
        console.log({
            func: 'getGUCInterestReserve',
            'Interest Reserve': IR,
        });
        return IR;
    }

    static calculateInterestReserve(updatedGUCTotalLoanAmount = false) {
        let _financedInterestReserveMonths = $('#financedInterestReserveMonths');
        let _financedInterestReserve = $('#financedInterestReserve');
        let _financedInterestReserveHtml = $('.financedInterestReserveHtml');
        let loanProgram = $('#LMRClientType').val();
        let loanTermsMonths = loanInfoV2Form.getMonthsFormLoanTerms();
        let interestReserveMonths = loanTermsMonths - 1;
        let financedInterestReserveMonths = _financedInterestReserveMonths.val();

        let _financedInterestReserveToolTip = $('.financedInterestReserveToolTip');

        if (loanProgram === 'BRLGUC') {

            _financedInterestReserveToolTip.attr('data-original-title', '(Total Loan Amount - Construction Hard Cost - Construction Soft Cost - Contingency $ - Interest Reserve + (0.65 * (Construction Hard Cost + Construction Soft Cost + Contingency $)) * (Interest Rate ÷ 12) * (M)');

            //_financedInterestReserveMonths.val(interestReserveMonths);
            //_financedInterestReserveMonths.attr('readonly', true);


            let totalPropertiesLoanAmount = getFieldsValue('totalPropertiesLoanAmount');
            _financedInterestReserveMonths.parent().find('.invalid-feedback').remove().end();

            let hardCostRemaining = getFieldsValue('constructionHardCost');
            let softCostRemaining = getFieldsValue('constructionSoftCost');
            let costRemaining = softCostRemaining + hardCostRemaining; // Cost Remaining - Hard Cost Remaining + Soft Cost Remaining
            let contingencyPercentage = getFieldsValue('contingencyPercentage');
            let C = contingencyPercentage / 100 * costRemaining; // Contingency Amount
            let R = getFieldsValue('lien1Rate') / 100;

            //   let interestReserve = loanInfoV2Form.getGUCInterestReserve2(totalPropertiesLoanAmount, hardCostRemaining, softCostRemaining, C, R, interestReserveMonths);
            let interestReserve = loanInfoV2Form.getGUCInterestReserve(hardCostRemaining, softCostRemaining, C, R, financedInterestReserveMonths, totalPropertiesLoanAmount);

            globalJS.highlightField(_financedInterestReserve);
            _financedInterestReserve.val(autoNumericConverter(interestReserve.toFixed(2)));

            globalJS.highlightField(_financedInterestReserveHtml);
            _financedInterestReserveHtml.html(autoNumericConverter(interestReserve.toFixed(2)));


            // loanInfoV2Form.initGroundUpConstruction(null,updatedGUCTotalLoanAmount);

        } else if (loanProgram === 'Bri996') {

            _financedInterestReserveToolTip.attr('data-original-title', '(Base Loan Amount + (.5 * (Financed Construction/Renovation Holdback)) * (Interest Rate ÷ 12) * (M)');

            _financedInterestReserveMonths.attr({
                //"readonly": false,
                //"min": 3,
                "max": loanTermsMonths - 1,
            });
            let errorMessage = '<span class="error invalid-feedback">Max Months ' + financedInterestReserveMonths + '</span>';
            _financedInterestReserveMonths.parent().find('.invalid-feedback').remove().end().append(errorMessage);

            let propertyAllocatedLoanAmount = getFieldsValue('propertyAllocatedLoanAmount');
            let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
            let interestRate = getFieldsValue('lien1Rate');
            interestRate = interestRate / 100;

            let financedInterestReserve = (propertyAllocatedLoanAmount + (0.5 * rehabCostFinanced)) * (interestRate / 12) * financedInterestReserveMonths;

            globalJS.highlightField(_financedInterestReserve);
            _financedInterestReserve.val(autoNumericConverter(financedInterestReserve.toFixed(2)));

            globalJS.highlightField(_financedInterestReserveHtml);
            _financedInterestReserveHtml.html(autoNumericConverter(financedInterestReserve.toFixed(2)));

        }
        loanInfoV2Form.calculateFinancedAtClosing_old();
        loanInfoV2Form.calculateGUCLoanToCost_old();
        loanInfoV2Form.calculateMonthlyPaymentReserve();

    }

    static updateInterestReserveType(loanProgram) {
        const interestReserveTypes = {
            BRLGUC: {
                val: 1, text: 'Non-Interest Bearing Reserve',
            }, Bri996: {
                val: 2, text: 'Interest Bearing Reserve',
            }, default: {
                val: 3, text: 'N/A',
            }
        };
        const selectElement = $('#interestReserveType');
        selectElement.empty(); // Remove existing options
        globalJS.highlightField(selectElement);

        const option = interestReserveTypes[loanProgram] || interestReserveTypes.default;
        selectElement.append(`<option value="${option.val}" selected>${option.text}</option>`);
        selectElement.trigger("chosen:updated");
    }

    /*    static validateFinancedInterestReserveMonths(financedInterestReserveMonthsValidate) {
            let loanTermsMonths = loanInfoV2Form.getMonthsFormLoanTerms();
            let loanProgram = $('#LMRClientType').val();
            let newMessages = {};

            if (loanProgram === 'Bri996') {
                newMessages = {
                    number: {
                        max: "Max Months Allowed " + (loanTermsMonths - 1),
                    }
                };
                financedInterestReserveMonthsValidate.settings.messages = newMessages;
            }
        }*/

    static cloneCostSpent() {
        let costSpent = $('#costSpent').val();

        let _costSpentHtml = $('.costSpentHtml');
        globalJS.highlightField(_costSpentHtml);
        _costSpentHtml.html(costSpent);

        /* loanInfoV2Form.calculateGUCTotalProjectCost_old();*/
        loanInfoV2Form.initGroundUpConstruction();
    }

    static cloneHardCost() {
        let constructionHardCost = $('#constructionHardCost').val();

        let _constructionHardCostHtml = $('.constructionHardCostHtml');
        globalJS.highlightField(_constructionHardCostHtml);
        _constructionHardCostHtml.html(constructionHardCost);

        /*       loanInfoV2Form.calculateGUCConstructionFinanced_old();
                loanInfoV2Form.calculateGUCTotalProjectCost_old();
                loanInfoV2Form.calculatedInitialLoanToCost_old();
                loanInfoV2Form.calculateSoftCostPercentageOfBudget_old();
                loanInfoV2Form.calculateGUCLoanToCost_old();*/
        loanInfoV2Form.initGroundUpConstruction();
        loanInfoV2Form.calculateMonthlyPaymentReserve();
    }

    static cloneSoftCost() {
        let constructionSoftCost = $('#constructionSoftCost').val();

        let _constructionSoftCostHtml = $('.constructionSoftCostHtml');
        globalJS.highlightField(_constructionSoftCostHtml);
        _constructionSoftCostHtml.html(constructionSoftCost);

        /*        loanInfoV2Form.calculateGUCConstructionFinanced_old();
                loanInfoV2Form.calculateGUCTotalProjectCost_old();
                loanInfoV2Form.calculatedInitialLoanToCost_old();
                loanInfoV2Form.calculateSoftCostPercentageOfBudget_old();
                loanInfoV2Form.calculateGUCLoanToCost_old();*/
        loanInfoV2Form.initGroundUpConstruction();
        loanInfoV2Form.calculateMonthlyPaymentReserve();

    }

    static calculateGUCTotalProjectCost_old() {
        let constructionHardCost = getFieldsValue('constructionHardCost');
        let constructionSoftCost = getFieldsValue('constructionSoftCost');
        let contingencyAmount = getFieldsValue('contingencyAmount');
        let costSpent = getFieldsValue('costSpent');

        let propertyAppraisalAsIsValue = getFieldsValue('propertyAppraisalAsIsValue');
        let estimatedPropertyValue = getFieldsValue('estimatedPropertyValue');
        let propertyPurchasePrice = getFieldsValue('propertyPurchasePrice');
        let renovationQualifyingAmount = getFieldsValue('renovationQualifyingAmount');

        let totalProjectCost = costSpent + constructionHardCost + constructionSoftCost + contingencyAmount + renovationQualifyingAmount;

        /*
         let propertyMetrics = parseInt($('#propertyMetrics').val());
         let renovationQualifyingAmount = 0;
                if (propertyMetrics === 2 && propertyAppraisalAsIsValue > 0) {
                    renovationQualifyingAmount = propertyAppraisalAsIsValue;
                } else if (propertyMetrics === 2 && propertyAppraisalAsIsValue === 0) {
                    renovationQualifyingAmount = estimatedPropertyValue;
                } else if (propertyMetrics === 1) {
                    renovationQualifyingAmount = propertyPurchasePrice;
                }
         totalProjectCost += renovationQualifyingAmount;
        */
        let _constructionTotalProjectCost = $('#constructionTotalProjectCost');
        globalJS.highlightField(_constructionTotalProjectCost);
        _constructionTotalProjectCost.val(totalProjectCost.toFixed(2));

        let _constructionTotalProjectCostHtml = $('.constructionTotalProjectCostHtml');
        globalJS.highlightField(_constructionTotalProjectCostHtml);
        _constructionTotalProjectCostHtml.html(autoNumericConverter(totalProjectCost.toFixed(2)));

        loanInfoV2Form.calculateGrossProfitMargin_old();
        //loanInfoV2Form.calculateGUC(); //disabled
        loanInfoV2Form.initGroundUpConstruction();
        console.log({
            func: 'calculateGroundUpConstructionTotalProjectCost',
            'constructionHardCost': constructionHardCost,
            'constructionSoftCost': constructionSoftCost,
            'contingencyAmount': contingencyAmount,
            'costSpent': costSpent,
            'propertyAppraisalAsIsValue': propertyAppraisalAsIsValue,
            'estimatedPropertyValue': estimatedPropertyValue,
            'propertyPurchasePrice': propertyPurchasePrice,
            'renovationQualifyingAmount': renovationQualifyingAmount,
            'totalProjectCost': totalProjectCost,
        });

    }

    static calculateGUCConstructionFinanced_old() {
        let constructionHardCost = getFieldsValue('constructionHardCost');
        let constructionSoftCost = getFieldsValue('constructionSoftCost');
        let contingencyAmount = getFieldsValue('contingencyAmount');

        let GUCConstructionFinanced = constructionHardCost + constructionSoftCost + contingencyAmount;

        let _GUCConstructionFinanced = $('#GUCConstructionFinanced');
        globalJS.highlightField(_GUCConstructionFinanced);
        _GUCConstructionFinanced.val(autoNumericConverter(GUCConstructionFinanced.toFixed(2)));

        let _GUCConstructionFinancedHtml = $('.GUCConstructionFinancedHtml');
        globalJS.highlightField(_GUCConstructionFinancedHtml);
        _GUCConstructionFinancedHtml.html(autoNumericConverter(GUCConstructionFinanced.toFixed(2)));

        loanInfoV2Form.calculateFinancedAtClosing_old();
    }

    static calculateFinancedAtClosing_old() {
        let GUCTotalLoanAmount = getFieldsValue('GUCTotalLoanAmount');
        let GUCConstructionFinanced = getFieldsValue('GUCConstructionFinanced');
        let financedInterestReserve = getFieldsValue('financedInterestReserve');

        let financedAtClosing = GUCTotalLoanAmount - GUCConstructionFinanced - financedInterestReserve;

        let _financedAtClosing = $('#financedAtClosing');
        globalJS.highlightField(_financedAtClosing);
        _financedAtClosing.val(autoNumericConverter(financedAtClosing.toFixed(2)));

        let _financedAtClosingHtml = $('.financedAtClosingHtml');
        globalJS.highlightField(_financedAtClosingHtml);
        _financedAtClosingHtml.html(autoNumericConverter(financedAtClosing.toFixed(2)));

        console.log({
            func: 'calculateFinancedAtClosing',
            'GUCTotalLoanAmount': GUCTotalLoanAmount,
            'GUCConstructionFinanced': GUCConstructionFinanced,
            'financedInterestReserve': financedInterestReserve,
            'financedAtClosing': financedAtClosing,
        });

        loanInfoV2Form.calculatedInitialLoanToCost_old();
        loanInfoV2Form.calculateGUCLoanToCost_old();
        loanInfoV2Form.calculateGUCLTP_old();
        loanInfoV2Form.updateTotalLoanInterestPayment();
    }

    static calculateGrossProfitMargin_old() {
        let constructionTotalProjectCost = getFieldsValue('constructionTotalProjectCost');
        let propertyAfterRepairValue = getFieldsValue('propertyAfterRepairValue');
        let propertyAppraisalRehabbedValue = getFieldsValue('propertyAppraisalRehabbedValue');
        let grossProfitMargin = 0;
        if (!propertyAppraisalRehabbedValue) {
            grossProfitMargin = (propertyAfterRepairValue - constructionTotalProjectCost) / propertyAfterRepairValue * 100;
        } else if (propertyAppraisalRehabbedValue) {
            grossProfitMargin = (propertyAppraisalRehabbedValue - constructionTotalProjectCost) / propertyAppraisalRehabbedValue * 100;
        }

        let _grossProfitMargin = $('#grossProfitMargin');
        globalJS.highlightField(_grossProfitMargin);
        _grossProfitMargin.val(autoNumericConverter(grossProfitMargin.toFixed(2)));

        let _grossProfitMarginHtml = $('.grossProfitMarginHtml');
        globalJS.highlightField(_grossProfitMarginHtml);
        _grossProfitMarginHtml.html(autoNumericConverter(grossProfitMargin.toFixed(2)));

        console.log({
            func: 'calculateGrossProfitMargin',
            'constructionTotalProjectCost': constructionTotalProjectCost,
            'propertyAfterRepairValue': propertyAfterRepairValue,
            'propertyAppraisalRehabbedValue': propertyAppraisalRehabbedValue,
            'grossProfitMargin': grossProfitMargin,
        });
    }

    static calculatedInitialLoanToCost_old() {
        let propertyMetrics = parseInt($('#propertyMetrics').val());

        let financedAtClosing = getFieldsValue('financedAtClosing');
        let constructionHardCost = getFieldsValue('constructionHardCost');
        let constructionSoftCost = getFieldsValue('constructionSoftCost');
        let contingencyAmount = getFieldsValue('contingencyAmount');
        let renovationQualifyingAmount = getFieldsValue('renovationQualifyingAmount');
        let propertyPurchasePrice = getFieldsValue('propertyPurchasePrice');
        let propertyAppraisalAsIsValue = getFieldsValue('propertyAppraisalAsIsValue');
        let estimatedPropertyValue = getFieldsValue('estimatedPropertyValue');
        let costSpent = getFieldsValue('costSpent');

        let initialLoanToCost = 0;
        /*if (propertyMetrics === 1) {
            initialLoanToCost = financedAtClosing / (costSpent + propertyPurchasePrice);
        } else if (propertyMetrics === 2) {
            if (propertyAppraisalAsIsValue) {
                initialLoanToCost = financedAtClosing / (costSpent + propertyAppraisalAsIsValue);
            } else if (!propertyAppraisalAsIsValue) {
                initialLoanToCost = financedAtClosing / (costSpent + estimatedPropertyValue);
            }
        }
*/
        initialLoanToCost = financedAtClosing / (costSpent + renovationQualifyingAmount);
        initialLoanToCost *= 100;

        let _initialLoanToCost = $('#initialLoanToCost');
        globalJS.highlightField(_initialLoanToCost);
        _initialLoanToCost.val(autoNumericConverter(initialLoanToCost.toFixed(2)));

        let _initialLoanToCostHtml = $('.initialLoanToCostHtml');
        globalJS.highlightField(_initialLoanToCostHtml);
        _initialLoanToCostHtml.html(autoNumericConverter(initialLoanToCost.toFixed(2)));

    }

    static calculateSoftCostPercentageOfBudget_old() {

        let constructionHardCost = getFieldsValue('constructionHardCost');
        let constructionSoftCost = getFieldsValue('constructionSoftCost');
        let contingencyAmount = getFieldsValue('contingencyAmount');

        let softCostPercentageOfBudget = constructionSoftCost / (constructionSoftCost + constructionHardCost + contingencyAmount) * 100;

        let _softCostPercentageOfBudget = $('#softCostPercentageOfBudget');
        globalJS.highlightField(_softCostPercentageOfBudget);
        _softCostPercentageOfBudget.val(softCostPercentageOfBudget.toFixed(2));

        let _softCostPercentageOfBudgetHtml = $('.softCostPercentageOfBudgetHtml');
        globalJS.highlightField(_softCostPercentageOfBudgetHtml);
        _softCostPercentageOfBudgetHtml.html(softCostPercentageOfBudget.toFixed(2));

        console.log({
            func: 'calculateSoftCostPercentageOfBudget',
            'constructionHardCost': constructionHardCost,
            'constructionSoftCost': constructionSoftCost,
            'contingencyAmount': contingencyAmount,
            'softCostPercentageOfBudget': softCostPercentageOfBudget,
        });

    }

    static calculateGUCLoanToCost_old() {
        let propertyMetrics = parseInt($('#propertyMetrics').val());
        let GUCTotalLoanAmount = getFieldsValue('GUCTotalLoanAmount');// Pending
        let costSpent = getFieldsValue('costSpent');
        let constructionSoftCost = getFieldsValue('constructionSoftCost');
        let constructionHardCost = getFieldsValue('constructionHardCost');
        let contingencyAmount = getFieldsValue('contingencyAmount');
        let renovationQualifyingAmount = getFieldsValue('renovationQualifyingAmount');
        let financedInterestReserve = getFieldsValue('financedInterestReserve');
        let financedAtClosing = getFieldsValue('financedAtClosing');

        let GUCLoanToCost = 0;
        if (propertyMetrics === 1 || propertyMetrics === 2 || propertyMetrics === 3) {
            GUCLoanToCost = GUCTotalLoanAmount / (costSpent + constructionSoftCost + constructionHardCost + contingencyAmount + renovationQualifyingAmount + financedInterestReserve);
        }
        GUCLoanToCost *= 100;

        let _GUCLoanToCost = $('#GUCLoanToCost');
        globalJS.highlightField(_GUCLoanToCost);
        _GUCLoanToCost.val(GUCLoanToCost.toFixed(2));

        let _GUCLoanToCostHtml = $('.GUCLoanToCostHtml');
        globalJS.highlightField(_GUCLoanToCostHtml);
        _GUCLoanToCostHtml.html(GUCLoanToCost.toFixed(2));

        console.log({
            func: 'calculateGUCLoanToCost',
            'GUCTotalLoanAmount': GUCTotalLoanAmount,
            'renovationQualifyingAmount': renovationQualifyingAmount,
            'GUCLoanToCost': GUCLoanToCost,
        });

    }


    static showGUCMaxLTCError() {
        let _targetLTC = $('#targetLTC');
        let errorMessage = `<span class="GUCMaxLTCError text-danger">Total Loan Amount is greater than Max LTC</span>`;
        _targetLTC.parent().parent().find('.GUCMaxLTCError').remove().end().append(errorMessage);

        toastrNotification('Total Loan Amount is greater than Max LTC ', 'error');
        let targetLTC = 85;
        _targetLTC.val(targetLTC.toFixed(5));
        setTimeout(function () {
            $('.GUCMaxLTCError').remove();
        }, 5000);
    }

    static updatedGUCTotalLoanAmount(ele) {
        if (parseInt($('input[name="editableField"]:checked').val()) === 2) {
            return false;
        }
        let GUCTotalLoanAmount = $(ele).val();

        let _totalPropertiesLoanAmount = $('.totalPropertiesLoanAmount');
        globalJS.highlightField(_totalPropertiesLoanAmount);
        _totalPropertiesLoanAmount.val(GUCTotalLoanAmount);

        loanInfoV2Form.calculateInterestReserve(true)

        let propertyMetrics = parseInt($('#propertyMetrics').val());
        let costSpent = getFieldsValue('costSpent');
        let constructionSoftCost = getFieldsValue('constructionSoftCost');
        let constructionHardCost = getFieldsValue('constructionHardCost');
        let contingencyAmount = getFieldsValue('contingencyAmount');
        let renovationQualifyingAmount = getFieldsValue('renovationQualifyingAmount');
        let financedInterestReserve = getFieldsValue('financedInterestReserve');
        let financedAtClosing = getFieldsValue('financedAtClosing');

        GUCTotalLoanAmount = replaceCommaValues(GUCTotalLoanAmount);
        let targetLTC = 0;
        if (propertyMetrics === 1 || propertyMetrics === 2 || propertyMetrics === 3) {
            targetLTC = GUCTotalLoanAmount / (costSpent + constructionSoftCost + constructionHardCost + contingencyAmount + renovationQualifyingAmount + financedInterestReserve);
        }
        targetLTC *= 100;
        targetLTC = targetLTC.toFixed(5);

        let _targetLTC = $('#targetLTC');
        globalJS.highlightField(_targetLTC);
        _targetLTC.val(targetLTC);

        loanInfoV2Form.calculateInterestReserve(false);
        console.log({
            func: 'updatedGUCTotalLoanAmount',
            'GUCTotalLoanAmount': GUCTotalLoanAmount,
            'renovationQualifyingAmount': renovationQualifyingAmount,
            'targetLTC': targetLTC,
        });
        //loanInfoV2Form.updatePointValues();

        let GUCMaxLTC = getFieldsValue('GUCMaxLTC');
        if (GUCMaxLTC < targetLTC) {
            loanInfoV2Form.showGUCMaxLTCError();
            loanInfoV2Form.calculateGUC();
            return false;
        }
        //loanInfoV2Form.calculateGUC();
        loanInfoV2Form.calculateGUCLoanToCost_old();
        loanInfoV2Form.calculateFinancedAtClosing_old();
        loanInfoV2Form.calculateGUCARV_old();
        loanInfoV2Form.updatePointValues();

        _targetLTC.parent().parent().find('.GUCMaxLTCError').remove().end();
        return true;

        /*        let _GUCLoanToCostHtml = $('.GUCLoanToCostHtml');
                globalJS.highlightField(_GUCLoanToCostHtml);
                _GUCLoanToCostHtml.html(GUCLoanToCost.toFixed(2));*/


        loanInfoV2Form.calculateFinancedAtClosing_old();

        console.log({
            func: 'updatedGUCTotalLoanAmount',
        });

    }

    /*    static calculateGUCTotalLoanAmount() {

            let targetLTC = getFieldsValue('targetLTC');
            let _GUCTotalLoanAmount = $('#GUCTotalLoanAmount');

            if (!targetLTC) {
                let constructionTotalProjectCost = getFieldsValue('constructionTotalProjectCost');
                targetLTC /= 100;
                let GUCTotalLoanAmount = targetLTC * constructionTotalProjectCost;
                globalJS.highlightField(_GUCTotalLoanAmount);
                _GUCTotalLoanAmount.val(GUCTotalLoanAmount.toFixed(2));
            }
            loanInfoV2Form.updatedGUCTotalLoanAmount();
        }*/

    /*    static updatedGUCTotalLoanAmount() {
            loanInfoV2Form.calculateGUC();
            loanInfoV2Form.calculateGUCLoanToCost();
            loanInfoV2Form.calculateFinancedAtClosing();
        }*/

    static calculateGUC() {
        //const targetLTC = 0.80;
        //const costRemaining = 1000000.00;
        //const C = 0.1 * costRemaining; //Cost Remaining
        //const R = 0.0699; //Rate Of Interest
        //const Terms = 12; //Term Months
        //const costSpent = 10000.00;
        //const purchasePrice = 2000000.00;
        //const arv = 2000000.00;
        const initialIlaGuess = 0.00;
        let _GUCTotalLoanAmount = $('#GUCTotalLoanAmount');
        let _financedInterestReserve = $('#financedInterestReserve');
        let _financedInterestReserveHtml = $('.financedInterestReserveHtml');

        let targetLTC = getFieldsValue('targetLTC');
        let GUCMaxLTC = getFieldsValue('GUCMaxLTC');
        if (GUCMaxLTC < targetLTC) {
            loanInfoV2Form.showGUCMaxLTCError();
            targetLTC = GUCMaxLTC / 100;
        } else {
            $('.GUCMaxLTCError').remove();
            targetLTC /= 100;
        }
        const costRemaining = loanInfoV2Form.sumOfConstructionCost();
        const contingencyPercentage = getFieldsValue('contingencyPercentage');
        const C = (contingencyPercentage / 100) * costRemaining;

        let R = getFieldsValue('lien1Rate');
        R /= 100;
        const Terms = loanInfoV2Form.getMonthsFormLoanTerms();
        const costSpent = getFieldsValue('costSpent');
        const purchasePrice = getFieldsValue('renovationQualifyingAmount');
        const arv = getFieldsValue('propertyAppraisalRehabbedValue');


        const result = loanInfoV2Form.findIlaIr(targetLTC, costRemaining, C, R, Terms, costSpent, purchasePrice, initialIlaGuess);
        const ila = Math.floor(result.ila);
        const ir = result.ir;

        $("#ilaResult").text(ila.toFixed(2));
        $("#irResult").text(ir.toFixed(2));

        console.log('Initial Loan Amount (ILA): ' + ila.toFixed(2));
        console.log('Interest Reserve (IR): ' + ir.toFixed(2));

        const totalLoanAmount = ila + ir + costRemaining + C;
        //$("#totalLoanAmountResult").text(totalLoanAmount.toFixed(2));
        globalJS.highlightField(_GUCTotalLoanAmount);
        _GUCTotalLoanAmount.val(autoNumericConverter(totalLoanAmount.toFixed(2)));

        let _totalPropertiesLoanAmount = $('.totalPropertiesLoanAmount');
        globalJS.highlightField(_totalPropertiesLoanAmount);
        _totalPropertiesLoanAmount.val(autoNumericConverter(totalLoanAmount.toFixed(2)));


        globalJS.highlightField(_financedInterestReserve);
        _financedInterestReserve.val(autoNumericConverter(ir.toFixed(2)));

        globalJS.highlightField(_financedInterestReserveHtml);
        _financedInterestReserveHtml.html(autoNumericConverter(ir.toFixed(2)));

        const maxLTC = 0.85 * (purchasePrice + costSpent + costRemaining + C + ir);
        const maxARV = 0.75 * arv;

        $("#maxLTCResult").text(maxLTC.toFixed(2));
        $("#maxARVResult").text(maxARV.toFixed(2));

        console.log('Max LTC: ' + maxLTC.toFixed(2));
        console.log('Max ARV: ' + maxARV.toFixed(2));

        const initialCost = totalLoanAmount - ir - costRemaining - C;
        //$("#initialCostResult").text(initialCost.toFixed(2));
        console.log('Initial Cost: ' + initialCost.toFixed(2));

        const arvPct = totalLoanAmount / arv;
        //$("#arvPctResult").text((arvPct * 100).toFixed(2) + '%');
        console.log("ARV % " + (arvPct * 100).toFixed(2));

        const profitMargin = 1 - (costSpent + costRemaining + C + purchasePrice + ir) / arv;
        //$("#profitMarginResult").text((profitMargin * 100).toFixed(2) + '%');
        console.log("Profit Margin % " + (profitMargin * 100).toFixed(2));

        //$("#isInitialCostPassingResult").text(initialCost <= maxARV);
        //$("#isLTCPassingResult").text(totalLoanAmount <= maxLTC);
        //$("#isARVPassingResult").text(totalLoanAmount <= maxARV);

        console.log("Is Initial Cost Passing: " + initialCost <= maxARV);
        console.log("Is LTC Passing: " + totalLoanAmount <= maxLTC);
        console.log("Is ARV Passing: " + totalLoanAmount <= maxARV);

        loanInfoV2Form.calculateMonthlyPaymentReserve();
        loanInfoV2Form.calculateGUCLoanToCost_old();
        loanInfoV2Form.calculateFinancedAtClosing_old();
        loanInfoV2Form.calculateGUCARV_old();
        loanInfoV2Form.updatePointValues();
    }

    static updatePointValues() {
        loanInfoV2Form.setOriginationValue();
        loanInfoV2Form.setBrokerPointsValue();
        loanInfoV2Form.cv3OriginationAmount();
    }

    static findIlaIr(targetLtc, costRemaining, C, R, T, costSpent, purchasePrice, initialIlaGuess, maxIterations = 10000, tolerance = 1e-12) {
        let ILA = initialIlaGuess;
        let IR = 0;

        for (let i = 0; i < maxIterations; i++) {
            IR = Math.floor(((0.65 * (costRemaining + C)) + ILA) * (R / 12) * (T));

            const LTC = (ILA + costRemaining + C + IR) / (costSpent + costRemaining + C + purchasePrice + IR);

            const error = LTC - targetLtc;

            if (Math.abs(error) < tolerance) {
                return {ila: ILA, ir: IR};
            }

            ILA -= (error * 10000);
        }
        return {ila: ILA, ir: IR};
    }

    static calculateGUCARV_old() {

        let GUCTotalLoanAmount = getFieldsValue('GUCTotalLoanAmount');
        let propertyAppraisalRehabbedValue = getFieldsValue('propertyAppraisalRehabbedValue');
        let propertyAfterRepairValue = getFieldsValue('propertyAfterRepairValue');

        let GUCARV = 0;
        if (!propertyAppraisalRehabbedValue) {
            GUCARV = GUCTotalLoanAmount / propertyAfterRepairValue * 100
        } else if (propertyAppraisalRehabbedValue) {
            GUCARV = GUCTotalLoanAmount / propertyAppraisalRehabbedValue * 100
        }
        let _GUCARVHtml = $('.GUCARVHtml');
        globalJS.highlightField(_GUCARVHtml);
        _GUCARVHtml.html(GUCARV.toFixed(2));

        let _GUCARV = $('#GUCARV');
        globalJS.highlightField(_GUCARV);
        _GUCARV.val(GUCARV.toFixed(2));
    }

    static calculateGUCLTP_old() {

        let financedAtClosing = getFieldsValue('financedAtClosing');
        let renovationQualifyingAmount = getFieldsValue('renovationQualifyingAmount');

        let GUCLTP = financedAtClosing / renovationQualifyingAmount

        GUCLTP *= 100;
        let _GUCLTP = $('#GUCLTP');
        globalJS.highlightField(_GUCLTP);
        _GUCLTP.val((GUCLTP.toFixed(2)));

        let _GUCLTPHtml = $('.GUCLTPHtml');
        globalJS.highlightField(_GUCLTPHtml);
        _GUCLTPHtml.html(GUCLTP.toFixed(2));
    }

    static calculateConstructionRatio() {

        let propertyAppraisalAsIsValue = getFieldsValue('propertyAppraisalAsIsValue');
        let rehabCostFinanced = getFieldsValue('rehabCostFinanced');
        let estimatedPropertyValue = getFieldsValue('estimatedPropertyValue');
        let propertyPurchasePrice = getFieldsValue('propertyPurchasePrice');

        let propertyMetrics = parseInt($('#propertyMetrics').val());
        let constructionRatio = 0;
        if (!propertyAppraisalAsIsValue && propertyMetrics === 2) {
            constructionRatio = rehabCostFinanced / estimatedPropertyValue;
        } else if (propertyAppraisalAsIsValue && propertyMetrics === 2) {
            constructionRatio = rehabCostFinanced / propertyAppraisalAsIsValue;
        } else if (propertyMetrics === 1) {
            constructionRatio = rehabCostFinanced / propertyPurchasePrice;
        }
        constructionRatio *= 100;
        let _constructionRatio = $('#constructionRatio');
        _constructionRatio.val(constructionRatio.toFixed(2));

        let _constructionRatioHtml = $('.constructionRatioHtml');
        globalJS.highlightField(_constructionRatioHtml);
        _constructionRatioHtml.html(constructionRatio.toFixed(2));

        console.log({
            func: 'calculateConstructionRatio',
            'propertyAppraisalAsIsValue': propertyAppraisalAsIsValue,
            'rehabCostFinanced': rehabCostFinanced,
            'estimatedPropertyValue': estimatedPropertyValue,
            'propertyPurchasePrice': propertyPurchasePrice,
        });

    }


    static initGroundUpConstruction(ele = null, updateTotalLoanAmount = false) {
        let loanProgram = $('#LMRClientType').val();
        if (loanProgram !== 'BRLGUC' || $('#activeTab').val() !== 'LIV2' || (ele && parseInt($('input[name="editableField"]:checked').val()) === 1)) {
            return false;
        }
        //const targetLTC = 0.80;
        //const softCostRemaining = 25000;
        //const hardCostRemaining = 500000;

        //const costRemaining = softCostRemaining + hardCostRemaining; // Cost Remaining - Hard Cost Remaining + Soft Cost Remaining
        //const C = 0.1 * costRemaining; // Contingency
        //const R = 0.0699; // Rate
        //const T = 12; // Term Months
        //const costSpent = 50000.00; // Cost Spent - Hard Cost Spent + Soft Cost Spent
        // let purchasePrice = 250000.00; // Purchase Price
        //let arv = 1200000.00; // After Repair Value

        let targetLTC = getFieldsValue('targetLTC') / 100;
        let GUCMaxLTC = getFieldsValue('GUCMaxLTC') / 100;
        const GUCMaxARV = getFieldsValue('GUCMaxARV') / 100;

        if (GUCMaxLTC < targetLTC) {
            loanInfoV2Form.showGUCMaxLTCError();
            targetLTC = GUCMaxLTC;
        }

        let hardCostRemaining = getFieldsValue('constructionHardCost');
        let softCostRemaining = getFieldsValue('constructionSoftCost');
        let costRemaining = softCostRemaining + hardCostRemaining; // Cost Remaining - Hard Cost Remaining + Soft Cost Remaining
        let contingencyPercentage = getFieldsValue('contingencyPercentage');
        let C = contingencyPercentage / 100 * costRemaining; // Contingency Amount
        let R = getFieldsValue('lien1Rate') / 100;

        let _financedInterestReserveMonths = $('#financedInterestReserveMonths');
        // let financedInterestReserveMonths = _financedInterestReserveMonths.val();
        let T = parseInt(_financedInterestReserveMonths.val()); //loanInfoV2Form.getMonthsFormLoanTerms()

        let costSpent = getFieldsValue('costSpent');
        let purchasePrice = getFieldsValue('renovationQualifyingAmount');
        let arv = getFieldsValue('propertyAppraisalRehabbedValue') ? getFieldsValue('propertyAppraisalRehabbedValue') : getFieldsValue('propertyAfterRepairValue');
        if (parseInt(T) === 0) {
            toastrNotification('Please Enter Months', 'error');
            //     return false;
        }

        const initialIlaGuess = 0.00; // optional

        const result = loanInfoV2Form.findIlaIr(targetLTC, costRemaining, C, R, T, costSpent, purchasePrice, initialIlaGuess);

        const Ila = Math.floor(result.ila);

        let output = `Initial Loan Amount (ILA): $${Ila.toFixed(2)} \n`;

        /* Start Interest Reserve */
        let interestReserve = result.ir;
        output += `Interest Reserve (IR): $${interestReserve.toFixed(2)} \n`;

        let _financedInterestReserve = $('#financedInterestReserve');
        globalJS.highlightField(_financedInterestReserve);
        _financedInterestReserve.val(autoNumericConverter(interestReserve.toFixed(2)));

        let _financedInterestReserveHtml = $('.financedInterestReserveHtml');
        globalJS.highlightField(_financedInterestReserveHtml);
        _financedInterestReserveHtml.html(autoNumericConverter(interestReserve.toFixed(2)));
        /* End Of Interest Reserve */

        if (updateTotalLoanAmount) {
            return;
        }

        /* Start Total Loan Amount */
        const totalLoanAmount = Ila + interestReserve + costRemaining + C;
        output += `Total Loan Amount: $${totalLoanAmount.toFixed(2)} \n\n`;

        let _GUCTotalLoanAmount = $('#GUCTotalLoanAmount');
        globalJS.highlightField(_GUCTotalLoanAmount);
        _GUCTotalLoanAmount.val(autoNumericConverter(totalLoanAmount.toFixed(2)));

        let _totalPropertiesLoanAmount = $('.totalPropertiesLoanAmount');
        globalJS.highlightField(_totalPropertiesLoanAmount);
        _totalPropertiesLoanAmount.val(autoNumericConverter(totalLoanAmount.toFixed(2)));
        /* End Of Total Loan Amount */


        /* Start Initial Loan to Cost */
        let iLTC = Ila / (costSpent + purchasePrice);
        iLTC *= 100;

        output += `iLTC (Initial Loan to Cost): ${iLTC.toFixed(2)}%\n`;
        let _initialLoanToCost = $('#initialLoanToCost');
        globalJS.highlightField(_initialLoanToCost);
        _initialLoanToCost.val(autoNumericConverter(iLTC.toFixed(2)));

        let _initialLoanToCostHtml = $('.initialLoanToCostHtml');
        globalJS.highlightField(_initialLoanToCostHtml);
        _initialLoanToCostHtml.html(autoNumericConverter(iLTC.toFixed(2)));
        /* End Initial Loan to Cost */


        const maxLTC = GUCMaxLTC * (purchasePrice + costSpent + costRemaining + C + interestReserve);
        const maxARV = GUCMaxARV * arv;

        output += `Max LTC: $${maxLTC.toFixed(2)}\n`;
        output += `Max ARV: $${maxARV.toFixed(2)}\n\n`;

        /* Start Loan To Purchase(LTP) */
        let ltp = Ila / purchasePrice;
        ltp *= 100;

        output += `LTP %: ${ltp.toFixed(2)}%\n`;
        let _GUCLTP = $('#GUCLTP');
        globalJS.highlightField(_GUCLTP);
        _GUCLTP.val((ltp.toFixed(2)));

        let _GUCLTPHtml = $('.GUCLTPHtml');
        globalJS.highlightField(_GUCLTPHtml);
        _GUCLTPHtml.html(ltp.toFixed(2));
        /* End Loan To Purchase(LTP) */


        /* Start After Repair Value(ARV) */
        let arvPct = totalLoanAmount / arv;
        arvPct *= 100;
        output += `ARV %: ${arvPct.toFixed(2)}%\n\n`;

        let _GUCARVHtml = $('.GUCARVHtml');
        globalJS.highlightField(_GUCARVHtml);
        _GUCARVHtml.html(arvPct.toFixed(2));

        let _GUCARV = $('#GUCARV');
        globalJS.highlightField(_GUCARV);
        _GUCARV.val(arvPct.toFixed(2));
        /* End Of After Repair Value(ARV) */


        /* Start Initial Cost, Financed At Closing: */
        const initialCost = totalLoanAmount - interestReserve - costRemaining - C;

        output += `Initial Cost, Financed At Closing: $${initialCost.toFixed(2)}\n`;
        let _financedAtClosing = $('#financedAtClosing');
        globalJS.highlightField(_financedAtClosing);
        _financedAtClosing.val(autoNumericConverter(initialCost.toFixed(2)));

        let _financedAtClosingHtml = $('.financedAtClosingHtml');
        globalJS.highlightField(_financedAtClosingHtml);
        _financedAtClosingHtml.html(autoNumericConverter(initialCost.toFixed(2)));
        /* End Of Initial Cost, Financed At Closing: */


        /* Start GUC Construction Financed	: */
        let GUCConstructionFinanced = costRemaining + C;
        output += `GUC Construction Financed: $${GUCConstructionFinanced.toFixed(2)}\n`;
        let _GUCConstructionFinanced = $('#GUCConstructionFinanced');
        globalJS.highlightField(_GUCConstructionFinanced);
        _GUCConstructionFinanced.val(autoNumericConverter(GUCConstructionFinanced.toFixed(2)));

        let _GUCConstructionFinancedHtml = $('.GUCConstructionFinancedHtml');
        globalJS.highlightField(_GUCConstructionFinancedHtml);
        _GUCConstructionFinancedHtml.html(autoNumericConverter(GUCConstructionFinanced.toFixed(2)));
        /* End GUC Construction Financed	 */

        /* Start Gross Profit Margin */
        let grossProfitMargin = (arv - costSpent - costRemaining - C - purchasePrice) / arv * 100;
        output += `Gross Profit Margin %: ${(grossProfitMargin.toFixed(2))}%\n`;
        let _grossProfitMargin = $('#grossProfitMargin');
        globalJS.highlightField(_grossProfitMargin);
        _grossProfitMargin.val(autoNumericConverter(grossProfitMargin.toFixed(2)));

        let _grossProfitMarginHtml = $('.grossProfitMarginHtml');
        globalJS.highlightField(_grossProfitMarginHtml);
        _grossProfitMarginHtml.html(autoNumericConverter(grossProfitMargin.toFixed(2)));
        /* End Of Gross Profit Margin */


        /* Start Soft Cost % Of Budget */
        let softCostPercentageOfBudget = (softCostRemaining / (softCostRemaining + hardCostRemaining + C) * 100);
        output += `Soft Cost % of Budget: ${softCostPercentageOfBudget.toFixed(2)}%\n\n`;
        let _softCostPercentageOfBudget = $('#softCostPercentageOfBudget');
        globalJS.highlightField(_softCostPercentageOfBudget);
        _softCostPercentageOfBudget.val(softCostPercentageOfBudget.toFixed(2));

        let _softCostPercentageOfBudgetHtml = $('.softCostPercentageOfBudgetHtml');
        globalJS.highlightField(_softCostPercentageOfBudgetHtml);
        _softCostPercentageOfBudgetHtml.html(softCostPercentageOfBudget.toFixed(2));
        /* End of Soft Cost % Of Budget */

        /* Start  Total Project Cost */
        let totalProjectCost = (costSpent + hardCostRemaining + softCostRemaining + C + purchasePrice);
        output += `Total Project Cost: $${totalProjectCost.toFixed(2)}\n\n`;
        let _constructionTotalProjectCost = $('#constructionTotalProjectCost');
        globalJS.highlightField(_constructionTotalProjectCost);
        _constructionTotalProjectCost.val(totalProjectCost.toFixed(2));

        let _constructionTotalProjectCostHtml = $('.constructionTotalProjectCostHtml');
        globalJS.highlightField(_constructionTotalProjectCostHtml);
        _constructionTotalProjectCostHtml.html(autoNumericConverter(totalProjectCost.toFixed(2)));
        /* End Of  Total Project Cost */

        /* Start GUC Loan To Cost */
        //let GUCLoanToCost = totalLoanAmount / (costSpent + softCostRemaining + hardCostRemaining + C + purchasePrice + interestReserve) * 100;
        let propertyMetrics = parseInt($('#propertyMetrics').val());

        let GUCLoanToCost = 0;
        if (propertyMetrics === 1 || propertyMetrics === 2 || propertyMetrics === 3) {
            GUCLoanToCost = totalLoanAmount / (costSpent + softCostRemaining + hardCostRemaining + C + purchasePrice + interestReserve);
        }
        GUCLoanToCost *= 100;

        output += `Loan to Cost (LTC) % ${GUCLoanToCost.toFixed(2)}%\n\n`;

        let _GUCLoanToCost = $('#GUCLoanToCost');
        globalJS.highlightField(_GUCLoanToCost);
        _GUCLoanToCost.val(GUCLoanToCost.toFixed(2));

        let _GUCLoanToCostHtml = $('.GUCLoanToCostHtml');
        globalJS.highlightField(_GUCLoanToCostHtml);
        _GUCLoanToCostHtml.html(GUCLoanToCost.toFixed(2));
        /* End GUC Loan To Cost */

        output += `Is Initial Cost Passing: ${initialCost <= maxARV}\n`;
        output += `Is LTC Passing: ${totalLoanAmount <= maxLTC}\n`;
        output += `Is ARV Passing: ${totalLoanAmount <= maxARV}\n`;

        console.log(output);
        loanInfoV2Form.calculateMonthlyPaymentReserve();
        loanInfoV2Form.updatePointValues();
        //$('#results').html(output);
    }

    static activateField(ele) {
        let val = parseInt($(ele).val());
        if (val === 1) { //total loan amount
            $('#GUCTotalLoanAmount').attr('readonly', false);
            $('#targetLTC').attr('readonly', true);
        } else if (val === 2) { //Target LTC
            $('#GUCTotalLoanAmount').attr('readonly', true);
            $('#targetLTC').attr('readonly', false);
        }
    }

    static updateTransactionType(ele) {
        let val = $(ele).val();
        let useOfProceedsDiv = $('.useOfProceedsDiv');
        let useOfProceeds = $('#useOfProceeds');
        let showClass = ['Cash-Out / Refinance', 'Delayed Purchase'].includes(val);
        useOfProceedsDiv.toggle(showClass);
        useOfProceeds.prop('disabled', !showClass);
        loanInfoV2Form.calculateMonthlyPaymentReserve();
    }

    static updateLoanTerm() {
        //  loanInfoV2Form.validateFinancedInterestReserveMonths(financedInterestReserveMonthsValidate);
        loanInfoV2Form.calculateInterestReserve();
        // loanInfoV2Form.calculateGUC();  diabledd
        loanInfoV2Form.initGroundUpConstruction();
        loanInfoV2Form.calculateMonthlyPaymentReserve();
    }

    static showHidePropertyRehabCv3(val) {
        if (val === 'BRLGUC') {
            $('#propertyNeedRehabYes').prop('checked', true);
            $('.showHidePropertyRehab').hide();
        } else {
            $('.showHidePropertyRehab').show();
        }
    }

    static hideInitialLoanAmountCV3() {

        let loanProgram = $('#LMRClientType').val();
        let transactionType = $('#typeOfHMLOLoanRequesting').val();
        let _hideInitialLoanAmountCV3 = $('.hideInitialLoanAmountCV3')
        if (loanProgram !== 'Bri996'
            && transactionType === 'Purchase') {
            _hideInitialLoanAmountCV3.hide();
        } else {
            _hideInitialLoanAmountCV3.show();
        }
    }

}
