<?php
global $activeTab, $allowToupdateFileAndClient, $publicUser, $PCID, $executiveId, $LMRId, $subscribePCToHOME, $subscribeToHOME, $userGroup;

use models\constants\getAccountantContactTypeID;
use models\cypher;
use models\standard\Strings;

getAccountantContactTypeID::init();

?>
<input type="hidden" name="isSectionChanged" id="isSectionChanged" value="">

<script type="text/javascript">

    $(document).on('click select paste', 'input, select, textarea', function (e) {
        /**
         * If user changed any contact, user click out side the section our system trigger confirm box.
         */
        var updateType = '';
        var publicUser = 0;
        try {
            updateType = $('#isSectionChanged').val();
            publicUser = $('#publicUser').val();
        } catch (e) {
        }

        if (updateType != '' && publicUser == 0) {
            toastrConfirmation('What would you like to do?', updateType, 'info', 'isSectionChanged');
            return false;
        }
    });

    $(document).ready(function () {
        <?php
        /**
         * Check borrower prfile related info has changed.
         * Card #600 - Updating Borrower profile logic.
         * Added by Suresh Kasinathan <<EMAIL>>
         */
        if ( ($activeTab == 'CI' || $activeTab == 'LI' || $activeTab == 'QAPP') && $allowToupdateFileAndClient != '' ) {
        ?>
        $('.isClientInfo').on('change select paste keyup', 'input, select, textarea', function (e) {
            if ($('#selClientId').val() > 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $(this).addClass('changed');
                }
            }
        });
        <?php } /* Check borrower prfile related info has changed end. */ ?>

        /* Check general contractor info changed or not start. */
        $('.generalContractor').on('change select paste', 'input, select, textarea', function (e) {
            if ($('#GCContactID').val() != '' && $('#GCContactID').val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('Contractor');
                }
            }
        });
        $(".generalContractor").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Contractor') {
                e.stopPropagation();
            }
        });

        $('.investorInfo').on('change select paste', 'input, select, textarea', function (e) {
            var secId = '';
            var arr = this.id.split('_');
            if (jQuery.type(arr[1]) === "undefined") {
                secId = ' 1';
                conId = '';
            } else {
                secId = ' ' + arr[1];
                conId = '_' + secId;
            }
            if ($('#investorID' + conId).val() != '' && $('#investorID' + conId).val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    if (this.name != 'investorTitleVesting[]' && this.name != 'investedAmount[]' && this.name != 'investedAmountPercent[]' && this.name != 'investorYield[]' && this.name != 'expectedMonthlyPayment[]' && this.name != 'totalPaymentsReceived[]') {
                        $('#isSectionChanged').val('Investor Info');
                    }
                }
            }
        });

        $('.investorInfo').on('click select paste', 'input, select, textarea', function (e) {
            var secId = '';
            var arr = this.id.split('_');
            if (jQuery.type(arr[1]) === "undefined") {
                secId = ' 1';
            } else {
                secId = ' ' + arr[1];
            }

            if ($('#isSectionChanged').val() == 'Investor Info') {
                e.stopPropagation();
            }
        });
        /* Check inverstor info changed or not end.*/

        /* Check appraiser info changed or not start .*/
        <?php    for($appr = 1; $appr <= 2; $appr++) { ?>
        $('.appraiser<?php echo $appr; ?>ID').on('change select paste', 'input, select, textarea', function (e) {
            if ($('#A<?php echo $appr; ?>AppraiserID').val() != '' && $('#A<?php echo $appr; ?>AppraiserID').val() != 0) {
                if (!$(this).hasClass('ignoreForUpdatePopup')) {

                    if (e.type == 'select' && this.type == 'text') {
                    } else {
                        $('#isSectionChanged').val('Appraiser <?php echo $appr; ?>');
                    }
                }
            }
        });
        $('.appraiser<?php echo $appr; ?>ID').on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Appraiser <?php echo $appr; ?>') {
                event.stopPropagation();
            }
        });
        <?php    } ?>
        /* Check appraiser info changed or not end .*/

        /* Check realtor BPO info changed or not start .*/
        <?php    for($real = 1; $real <= 2; $real++) { ?>
        $('.Realtor<?php echo $real; ?>ID').on('change select paste', 'input, select, textarea', function (e) {
            if ($('#A<?php echo $real; ?>BPOID').val() != '' && $('#A<?php echo $real; ?>BPOID').val() != 0) {
                if (!$(this).hasClass('ignoreForUpdatePopup')) {
                    if (e.type == 'select' && this.type == 'text') {
                    } else {
                        $('#isSectionChanged').val('BPO <?php echo $real; ?>');
                    }
                }
            }
        });
        $('.Realtor<?php echo $real; ?>ID').on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'BPO <?php echo $real; ?>') {
                event.stopPropagation();
            }
        });
        <?php    } ?>
        /* Check realtor BPO info changed or not end .*/

        /* Check realtor BPO info changed or not start .*/
        <?php    for($real = 1; $real <= 2; $real++) { ?>
        $('.HOAContact<?php echo $real; ?>').on('change select paste', 'input, select, textarea', function (e) {
            if ($('#HOA<?php echo $real; ?>ContactID').val() != '' && $('#HOA<?php echo $real; ?>ContactID').val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('HOA <?php echo $real; ?>');
                }
            }
        });
        $('.HOAContact<?php echo $real; ?>').on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'HOA <?php echo $real; ?>') {
                event.stopPropagation();
            }
        });
        <?php    } ?>
        /* Check realtor BPO info changed or not end .*/

        /* Check title info changed or not start .*/
        $('.TI').on('change select paste', 'input, select, textarea', function (e) {
            var idArray = ($(this).attr('id')).split('_');
            if ($('#representativeID_' + idArray[1]).val() != '' && $('#representativeID_' + idArray[1]).val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    if (this.name != 'titleOrderNumber' && this.name != 'recordingNo' && this.name != 'titleReportDate'
                        && this.name != 'titleSeller' && this.name != 'titleName' && this.name != 'HMLOEstateHeldIn' && this.name != 'titleReport'
                        && this.name != 'titleOrderedDate' && this.name != 'borVestingInfo' && this.name != 'titleExceptions'
                        && this.name != 'titleReceivedDate') {
                        $('#isSectionChanged').val('Title');
                        $('#isTitleEdited_' + idArray[1]).val('Edited');
                    }
                }
            }
        });
        $(".TI").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Title') {
                event.stopPropagation();
            }
        });
        /* Check title info changed or not end.*/

        /* Check escrow info changed or not start .*/
        $('.EI').on('change select paste', 'input, select, textarea', function (e) {
            var idArray = ($(this).attr('id')).split('_');
            if ($('#escrowID_' + idArray[1]).val() != '' && $('#escrowID_' + idArray[1]).val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    if (this.name != 'escrowNo') {
                        $('#isSectionChanged').val('Escrow');
                        $('#isEscrowEdited_' + idArray[1]).val('Edited');
                    }
                }
            }
        });
        $(".EI").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Escrow') {
                e.stopPropagation();
            }
        });
        /* Check escrow info changed or not end.*/

        /* Check financial Advisor info changed or not start .*/
        $('.FA').on('change select paste', 'input, select, textarea', function (e) {
            var idArray = ($(this).attr('id')).split('_');
            if ($('#financialAdvisorID_' + idArray[1]).val() != '' && $('#financialAdvisorID_' + idArray[1]).val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('Financial Advisor');
                    $('#isfinancialAdvisorEdited_' + idArray[1]).val('Edited');
                }
            }
        });
        $(".FA").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Financial Advisor') {
                e.stopPropagation();
            }
        });
        /* Check financial Advisor info changed or not end.*/

        /* Check accountant info changed or not start .*/
        $('.ACC').on('change select paste', 'input, select, textarea', function (e) {
            var idArray = ($(this).attr('id')).split('_');
            if ($('#accountantID_' + idArray[1]).val() != '' && $('#accountantID_' + idArray[1]).val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('Accountant');
                    $('#isaccountantEdited_' + idArray[1]).val('Edited');
                }
            }
        });
        $(".ACC").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Accountant') {
                e.stopPropagation();
            }
        });
        /* Check accountant info changed or not end.*/

        /* Check attorney info changed or not start .*/
        $('.AI').on('change select paste', 'input, select, textarea', function (e) {
            var idArray = ($(this).attr('id')).split('_');
            if ($('#titleAttorneyID_' + idArray[1]).val() != '' && $('#titleAttorneyID_' + idArray[1]).val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('Attorney');
                    $('#isTitleAttorneyEdited_' + idArray[1]).val('Edited');
                }
            }
        });
        $(".AI").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Attorney') {
                e.stopPropagation();
            }
        });
        /* Check attorney info changed or not end.*/

        /* Check lender info changed or not start .*/
        $('.lenderContact').on('change select paste', 'input, select, textarea', function (e) {
            if ($('#serviceLenderID').val() != '' && $('#serviceLenderID').val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('Lender');
                }
            }
        });
        $(".lenderContact").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Lender') {
                e.stopPropagation();
            }
        });
        /* Check lender info changed or not end.*/

        /* Check insurance company info changed or not start .*/
        $('.BEI').on('change select paste', 'input, select, textarea', function (e) {
            var idArray = ($(this).attr('id')).split('_');
            if ($('#insuranceCompanyID_' + idArray[1]).val() != '' && $('#insuranceCompanyID_' + idArray[1]).val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    //$('#isSectionChanged').val('Insurance'); /* Insurance details are not related to borrower employment info*/
                }
            }
        });
        $(".BEI").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Insurance') {
                e.stopPropagation();
            }
        });
        /* Check insurance company info changed or not end.*/

        /* Check servicer company info changed or not start .*/
        $('.servicerInfo').on('change select paste', 'input, select, textarea', function (e) {
            if ($('#servicerID').val() != '' && $('#servicerID').val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('Servicer');
                }
            }
        });
        $(".servicerInfo").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Servicer') {
                e.stopPropagation();
            }
        });
        /* Check servicer company info changed or not end.*/

        /* Check trustee company info changed or not start .*/
        $('.trusteeInfo').on('change select paste', 'input, select, textarea', function (e) {
            if ($('#trusteeID').val() != '' && $('#trusteeID').val() != 0) {
                if (e.type == 'select' && this.type == 'text') {
                } else {
                    $('#isSectionChanged').val('Trustee');
                }
            }
        });
        $(".trusteeInfo").on('click select paste', 'input, select, textarea', function (e) {
            if ($('#isSectionChanged').val() == 'Trustee') {
                e.stopPropagation();
            }
        });
        /* Check servicer company info changed or not end.*/


        $('.btnSave, .clBtn, .button').click(function (e) {

            var updateType = $('#isSectionChanged').val();
            if (updateType != '') {
                toastrConfirmation('You are updating ' + updateType + ' info related information, which is tied to the contact list. What would you like to do?', updateType, 'info', 'isSectionChanged');
                return false;
            }

            /* For borrower info create/update. */
            if ($('.changed').length > 0) {

                if ($('#borrowerEmail').val() == '') { // Borrower email not empty.
                    toastrNotification("Please enter Borrower Email.", 'error');
                    return false;
                }
                if ($(this).val() == 'Save') $('#isSave').val(1);

                toastrConfirmation('You are updating information related to the borrower profile. Where do you want this updated information to affect?', 'isClientData', 'warning', '<?php echo $allowToupdateFileAndClient; ?>');
                return false;
            }

            if ($(this).val() == 'Save' || $(this).val() == 'Submit' || $(this).val() == 'Update' || $(this).val() == 'Upload') {
                $('#isSave').val(1);
            }
        });

        <?php
        if ($activeTab == 'CFPB') { /** Intake tab **/
        ?>
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#CFPBServicer1').val(replaceXMLProcess(value));
                changetheValueofServicer();
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#CFPBServicer1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#CFPBServicer2').val(replaceXMLProcess(value));
                changetheValueofServicer();
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#CFPBServicer2').autocomplete(options);
        <?php
        } else if ($activeTab == 'INT') { /** Intake tab **/
        ?>
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#servicer1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#servicer1').autocomplete(options);

        BAOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3',
            minChars: 0,
            onSelect: function (value, data) {
                $('#attorneyName').val(replaceXMLProcess(value));
                document.loanModForm.attorneyID.value = data;
                document.loanModForm.tempAttorneyContactName.value = value;
                showAttorneyContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#attorneyName').autocomplete(BAOptions);

        <?php

        } else  if ($activeTab == 'CI' || $activeTab == 'HR') {
        if ($publicUser == 1) {?>
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#servicer1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#servicer1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#servicer2').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#servicer2').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#originalLender1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#originalLender1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#originalLender2').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#originalLender2').autocomplete(options);

        <?php
        } else {
        ?>
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#servicer1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#servicer1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#servicer2').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#servicer2').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#originalLender1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#originalLender1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#originalLender2').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#originalLender2').autocomplete(options);
        <?php
        }
        ?>
        counselorOtions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=13',
            minChars: 0,
            onSelect: function (value, data) {
                $('#creditCounselorName').val(replaceXMLProcess(value));
                document.loanModForm.counselorID.value = data;
                document.loanModForm.tempCounselorName.value = value;
                showCounselorInfoForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#creditCounselorName').autocomplete(counselorOtions);
        PMOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=9',
            minChars: 0,
            onSelect: function (value, data) {
                $('#propMgmntContactPerson').val(replaceXMLProcess(value));
                document.loanModForm.propMgmntContactID.value = data;
                document.loanModForm.tempPropMgmntContactName.value = value;
                showPMContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#propMgmntContactPerson').autocomplete(PMOptions);
        HOAOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=8',
            minChars: 0,
            onSelect: function (value, data) {
                $('#HOContactName').val(replaceXMLProcess(value));
                document.loanModForm.HOA1ContactID.value = data;
                document.loanModForm.tempContactName.value = value;
                showContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#HOContactName').autocomplete(HOAOptions);
        HOA2Options = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=8',
            minChars: 0,
            minLength: 0,
            onSelect: function (value, data) {
                $('#HOA2ContactName').val(replaceXMLProcess(value));
                document.loanModForm.HOA2ContactID.value = data;
                document.loanModForm.tempHOA2ContactName.value = value;
                showHOA2ContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#HOA2ContactName').autocomplete(HOA2Options);
        <?php
        } else if ($activeTab == 'PI' || $activeTab == 'QA' || $activeTab == 'SS' || $activeTab == 'LE' || $activeTab == 'MI' || $activeTab == 'DASH' || $activeTab == 'LI' || $activeTab == 'SSS' || $activeTab == 'QAPP' || $activeTab == 'BD') {
        ?>

        GCOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=28',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('GCName');
                $('#GCFirstName').val(replaceXMLProcess(value));
                document.loanModForm.GCContactID.value = data;
                document.loanModForm.tempGCFirstName.value = value;
                showGCContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#GCFirstName').autocomplete(GCOptions);

        GCLOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=28&opt=contactLName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('GCLName');
                $('#GCLastName').val(replaceXMLProcess(value));
                document.loanModForm.GCContactID.value = data;
                document.loanModForm.tempGCLastName.value = value;
                showGCContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#GCLastName').autocomplete(GCLOptions);

        GCCNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=28&opt=contactCName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('GCCName');
                $('#GCCompanyName').val(replaceXMLProcess(value));
                document.loanModForm.GCContactID.value = data;
                document.loanModForm.tempGCCompanyName.value = value;
                showGCContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#GCCompanyName').autocomplete(GCCNameOptions);

        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#servicer1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#servicer1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#servicer2').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#servicer2').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#originalLender1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#originalLender1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#originalLender2').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#originalLender2').autocomplete(options);

        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#principalResServicer1').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#principalResServicer1').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#lien1ServicerPRI').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#lien1ServicerPRI').autocomplete(options);
        options = {
            serviceUrl: '/JQFiles/getLenderList.php?opt=APP',
            minChars: 2,
            onSelect: function (value, data) {
                $('#lien2ServicerPRI').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#lien2ServicerPRI').autocomplete(options);

        PMOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=9',
            minChars: 0,
            onSelect: function (value, data) {
                $('#propMgmntContactPerson').val(replaceXMLProcess(value));
                document.loanModForm.propMgmntContactID.value = data;
                document.loanModForm.tempPropMgmntContactName.value = value;
                showPMContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#propMgmntContactPerson').autocomplete(PMOptions);

        HOAOptions1 = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=8&opt=com',
            minChars: 0,
            onSelect: function (value, data) {
                $('#condominiumOrHOAFeeAmtReceiver').val(replaceXMLProcess(value));
                document.loanModForm.HOA1ContactID.value = data;
                showContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#condominiumOrHOAFeeAmtReceiver').autocomplete(HOAOptions1);

        HOAOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=8',
            minChars: 0,
            onSelect: function (value, data) {
                $('#HOContactName').val(replaceXMLProcess(value));
                document.loanModForm.HOA1ContactID.value = data;
                document.loanModForm.tempContactName.value = value;
                showContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#HOContactName').autocomplete(HOAOptions);


        HOA2Options = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=8',
            minChars: 0,
            minLength: 0,
            onSelect: function (value, data) {
                $('#HOA2ContactName').val(replaceXMLProcess(value));
                document.loanModForm.HOA2ContactID.value = data;
                document.loanModForm.tempHOA2ContactName.value = value;
                showHOA2ContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#HOA2ContactName').autocomplete(HOA2Options);
        var cAID = 0;
        try {
            cAID = document.loanModForm.counselAttorneyID.value;
        } catch (e) {
        }

        BAOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3&filterCID=' + cAID,
            minChars: 0,
            onSelect: function (value, data) {
                $('#attorneyName').val(replaceXMLProcess(value));
                document.loanModForm.attorneyID.value = data;
                document.loanModForm.tempAttorneyContactName.value = value;
                showAttorneyContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        <?php if ($publicUser != 1) { ?>
        $('#attorneyName').autocomplete(BAOptions);
        <?php } ?>
        var aID = 0;
        try {
            aID = document.loanModForm.attorneyID.value;
        } catch (e) {
        }

        CAOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3&filterCID=' + aID,
            minChars: 0,
            onSelect: function (value, data) {
                $('#counselAttorneyName').val(replaceXMLProcess(value));
                document.loanModForm.counselAttorneyID.value = data;
                document.loanModForm.tempAttorneyContactName.value = value;
                showCounselAttorneyContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#counselAttorneyName').autocomplete(CAOptions);
        <?php if ($PCID != '3572') {  //ch13656 ?>
        titleOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=1',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('titleCompanyContacts', $('#idNo').val());
                removeFileContacts('title', $('#idNo').val());
                $('#contact_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#representativeID_' + $('#idNo').val()).val(data);
                $('#tempRepresentativeCName_' + $('#idNo').val()).val(value);
                //document.loanModForm.representativeID.value = data;
                //document.loanModForm.tempRepresentativeName.value = value;
                showRepresentativeForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#contact_' + $('#idNo').val()).autocomplete(titleOptions);

        titleLNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=1&opt=contactLName',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('titleCompanyContacts', $('#idNo').val());
                removeFileContacts('title', $('#idNo').val());
                $('#titleContactLName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#representativeID_' + $('#idNo').val()).val(data);
                $('#tempRepresentativeCName_' + $('#idNo').val()).val(value);
                //document.loanModForm.representativeID.value = data;
                //document.loanModForm.tempRepresentativeLName.value = value;
                showRepresentativeForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#titleContactLName_' + $('#idNo').val()).autocomplete(titleLNameOptions);

        titleCNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=1&opt=contactCName',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('titleCompanyContacts', $('#idNo').val());
                removeFileContacts('title', $('#idNo').val());
                $('#titleCo_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#representativeID_' + $('#idNo').val()).val(data);
                $('#tempRepresentativeCName_' + $('#idNo').val()).val(value);
                //document.loanModForm.representativeID.value = data;
                // document.loanModForm.tempRepresentativeCName.value = value;
                showRepresentativeForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#titleCo_' + $('#idNo').val()).autocomplete(titleCNameOptions);

        insuranceCompanyOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=18',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('insuranceCompanyContacts');
                removeFileContacts('insuranceCompany', $('#idNo').val());
                $('#proInsFirstName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#insuranceCompanyID_' + $('#idNo').val()).val(data);
                $('#insuranceCompanyName_' + $('#idNo').val()).val(value);
                //document.loanModForm.insuranceCompanyID.value = data;
                //document.loanModForm.insuranceCompanyName.value = value;
                showInsuranceCompanyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#proInsFirstName_' + $('#idNo').val()).autocomplete(insuranceCompanyOptions);

        financialAdvisorOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=52',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('insuranceCompanyContactsContacts');
                removeFileContacts('financialAdvisor', $('#idNo').val());
                $('#financialAdvisorFirstName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#financialAdvisorID_' + $('#idNo').val()).val(data);
                $('#ifinancialAdvisorCompanyName_' + $('#idNo').val()).val(value);
                //document.loanModForm.insuranceCompanyID.value = data;
                //document.loanModForm.insuranceCompanyName.value = value;
                showFinancialAdvisorForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#financialAdvisorFirstName_' + $('#idNo').val()).autocomplete(financialAdvisorOptions);

        accountantOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=<?php echo getAccountantContactTypeID::$accountantContactTypeID;?>',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('insuranceCompanyContactsContacts');
                removeFileContacts('accountant', $('#idNo').val());
                $('#accountantFirstName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#accountantID_' + $('#idNo').val()).val(data);
                $('#accountantCompanyName_' + $('#idNo').val()).val(value);
                //document.loanModForm.insuranceCompanyID.value = data;
                //document.loanModForm.insuranceCompanyName.value = value;
                showaccountantForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#accountantFirstName_' + $('#idNo').val()).autocomplete(accountantOptions);

        insuranceCompanyLNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=18&opt=contactLName',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('insuranceCompanyContacts');
                removeFileContacts('insuranceCompany', $('#idNo').val());
                $('#proInsLastName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#insuranceCompanyID_' + $('#idNo').val()).val(data);
                $('#insuranceCompanyName_' + $('#idNo').val()).val(value);
                //document.loanModForm.insuranceCompanyID.value = data;
                //document.loanModForm.insuranceCompanyLastName.value = value;
                showInsuranceCompanyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#proInsLastName_' + $('#idNo').val()).autocomplete(insuranceCompanyLNameOptions);

        insuranceCompanyCNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=18&opt=contactCName',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('insuranceCompanyContacts');
                removeFileContacts('insuranceCompany');
                $('#proInsName').val(replaceXMLProcess(value));
                document.loanModForm.insuranceCompanyID.value = data;
                document.loanModForm.insuranceCompanyCarrierName.value = value;
                showInsuranceCompanyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#proInsName').autocomplete(insuranceCompanyCNameOptions);

        <?php } //end of ch13656 ?>

        counselorOtions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=13',
            minChars: 0,
            onSelect: function (value, data) {
                $('#creditCounselorName').val(replaceXMLProcess(value));
                document.loanModForm.counselorID.value = data;
                document.loanModForm.tempCounselorName.value = value;
                showCounselorInfoForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#creditCounselorName').autocomplete(counselorOtions);
        POAOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3',
            minChars: 0,
            onSelect: function (value, data) {
                $('#POAttorneyName').val(replaceXMLProcess(value));
                document.loanModForm.POAttorneyID.value = data;
                document.loanModForm.tempPOAttorneyName.value = value;
                showPOAttorneyContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#POAttorneyName').autocomplete(POAOptions);

        realtorOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=2',
            minChars: 0,
            onSelect: function (value, data) {
                $('#realtor').val(replaceXMLProcess(value));
                document.loanModForm.realtorID.value = data;
                showRealtorContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#realtor').autocomplete(realtorOptions);

        attorneyOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('attorney', $('#idNo').val());
                $('#titleAttorneyName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#titleAttorneyID_' + $('#idNo').val()).val(data);
                $('#tempTitleAttorneyName_' + $('#idNo').val()).val(value);
                //document.loanModForm.titleAttorneyID.value = data;
                //document.loanModForm.tempTitleAttorneyName.value = value;
                showTitleAttorneyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#titleAttorneyName_' + $('#idNo').val()).autocomplete(attorneyOptions);

        attorneyLOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3&opt=contactLName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('title');
                $('#titleAttorneyLastName').val(replaceXMLProcess(value));
                document.loanModForm.titleAttorneyID.value = data;
                document.loanModForm.tempTitleAttorneyLName.value = value;
                showTitleAttorneyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#titleAttorneyLastName').autocomplete(attorneyLOptions);

        attorneyCNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3&opt=contactCName',
            minChars: 0,
            onSelect: function (value, data) {
                clear_form_elements('attorneyContactsInfo');
                removeFileContacts('attorneyCName', $('#idNo').val());
                $('#titleAttorneyFirmName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#titleAttorneyID_' + $('#idNo').val()).val(data);
                $('#tempTitleAttorneyName_' + $('#idNo').val()).val(value);
                //document.loanModForm.titleAttorneyID.value = data;
                //document.loanModForm.tempTitleAttorneyCName.value = value;
                showTitleAttorneyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };

        $('#titleAttorneyFirmName_' + $('#idNo').val()).autocomplete(attorneyCNameOptions);
        <?php if ($PCID != '3572') {  //ch13656 ?>
        escrowOfficerOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=41',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('escrowOfficer', $('#idNo').val());
                $('#escrowOfficer_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#escrowID_' + $('#idNo').val()).val(data);
                $('#tempEscrowName_' + $('#idNo').val()).val(value);
                //document.loanModForm.escrowID.value = data;
                //document.loanModForm.tempEscrowName.value = value;
                showEscrowContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#escrowOfficer_' + $('#idNo').val()).autocomplete(escrowOfficerOptions);

        escrowOfficerLNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=41&opt=contactLName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('escrowOfficerLName', $('#idNo').val());
                $('#escrowOfficerLName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#escrowID_' + $('#idNo').val()).val(data);
                $('#tempEscrowName_' + $('#idNo').val()).val(value);
                //document.loanModForm.escrowID.value = data;
                // document.loanModForm.tempEscrowLName.value = value;
                showEscrowContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#escrowOfficerLName_' + $('#idNo').val()).autocomplete(escrowOfficerLNameOptions);

        escrowOfficerCNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=41&opt=contactCName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('escrowOfficerFirmName', $('#idNo').val());
                $('#escrowOfficerFirmName_' + $('#idNo').val()).val(replaceXMLProcess(value));
                $('#escrowID_' + $('#idNo').val()).val(data);
                $('#tempEscrowName_' + $('#idNo').val()).val(value);
                //document.loanModForm.escrowID.value = data;
                //document.loanModForm.tempEscrowOfficerCName.value = value;
                showEscrowContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', $('#idNo').val());
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#escrowOfficerFirmName_' + $('#idNo').val()).autocomplete(escrowOfficerCNameOptions);
        <?php }  //end of ch13656 ?>
        <?php
        for ($i = 1;$i <= 3;$i++) {
        ?>
        buyer<?php echo $i;?>AgentOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=2',
            minChars: 0,
            onSelect: function (value, data) {
                $('#buyer<?php echo $i;?>AgentName').val(replaceXMLProcess(value));
                document.loanModForm.B<?php echo $i;?>AgentID.value = data;
                showBuyerAgentContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#buyer<?php echo $i;?>AgentName').autocomplete(buyer<?php echo $i;?>AgentOptions);
        <?php
        }
        for ($i = 1;$i <= 3;$i++) {
        ?>
        buyer<?php echo $i;?>Options = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3',
            minChars: 0,
            onSelect: function (value, data) {
                $('#buyer<?php echo $i;?>AttorneyName').val(replaceXMLProcess(value));
                document.loanModForm.B<?php echo $i;?>AttorneyID.value = data;
                document.loanModForm.tempB<?php echo $i;?>AttorneyName.value = value;
                showBuyerAttorneyContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#buyer<?php echo $i;?>AttorneyName').autocomplete(buyer<?php echo $i;?>Options);
        <?php
        }
        /*

        Description	: Get the Appraiser Info linked with Contacts
        Developer	: Viji & Venky
        Date		: Oct 23, 2017

        */

        for ($i = 1;$i <= 2;$i++) {
        ?>
        appraiser<?php echo $i;?>Options = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=4',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('appraiser<?php echo $i;?>');
                $('#appraiser<?php echo $i;?>AppraiserName').val(replaceXMLProcess(value));
                document.loanModForm.A<?php echo $i;?>AppraiserID.value = data;
                document.loanModForm.tempA<?php echo $i;?>AppraiserName.value = value;
                showAppraiserContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#appraiser<?php echo $i;?>AppraiserName').autocomplete(appraiser<?php echo $i;?>Options);
        <?php
        }

        for ($i = 1;$i <= 2;$i++) {
        ?>
        appraiser<?php echo $i;?>LNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=4&opt=contactLName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('appraiser<?php echo $i;?>LName');
                $('#appraiser<?php echo $i;?>LName').val(replaceXMLProcess(value));
                document.loanModForm.A<?php echo $i;?>AppraiserID.value = data;
                document.loanModForm.tempA<?php echo $i;?>AppraiserLName.value = value;
                showAppraiserContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#appraiser<?php echo $i;?>LName').autocomplete(appraiser<?php echo $i;?>LNameOptions);
        <?php
        }

        for ($i = 1;$i <= 2;$i++) {
        ?>
        appraiser<?php echo $i;?>CNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=4&opt=contactCName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('appraiser<?php echo $i;?>CName');
                $('#appraiser<?php echo $i;?>Company').val(replaceXMLProcess(value));
                document.loanModForm.A<?php echo $i;?>AppraiserID.value = data;
                document.loanModForm.tempA<?php echo $i;?>AppraiserCName.value = value;
                showAppraiserContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#appraiser<?php echo $i;?>Company').autocomplete(appraiser<?php echo $i;?>CNameOptions);
        <?php
        }

        for ($i = 1;$i <= 3;$i++) {
        ?>
        BPO<?php echo $i;?>Options = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=2',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('BPO<?php echo $i;?>');
                $('#BPO<?php echo $i;?>RealtorName').val(replaceXMLProcess(value));
                document.loanModForm.A<?php echo $i;?>BPOID.value = data;
                document.loanModForm.tempA<?php echo $i;?>RealtorName.value = value;
                showRealtorBPOContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#BPO<?php echo $i;?>RealtorName').autocomplete(BPO<?php echo $i;?>Options);
        <?php
        }

        for ($i = 1;$i <= 3;$i++) {
        ?>
        BPO<?php echo $i;?>LNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=2&opt=contactLName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('BPO<?php echo $i;?>');
                $('#BPO<?php echo $i;?>LName').val(replaceXMLProcess(value));
                document.loanModForm.A<?php echo $i;?>BPOID.value = data;
                document.loanModForm.tempA<?php echo $i;?>RealtorLName.value = value;
                showRealtorBPOContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#BPO<?php echo $i;?>LName').autocomplete(BPO<?php echo $i;?>LNameOptions);
        <?php
        }

        for ($i = 1;$i <= 3;$i++) {
        ?>
        BPO<?php echo $i;?>CNameOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=2&opt=contactCName',
            minChars: 0,
            onSelect: function (value, data) {
                removeFileContacts('BPO<?php echo $i;?>');
                $('#realtor<?php echo $i;?>Company').val(replaceXMLProcess(value));
                document.loanModForm.A<?php echo $i;?>BPOID.value = data;
                document.loanModForm.tempA<?php echo $i;?>RealtorCName.value = value;
                showRealtorBPOContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', '<?php echo $i;?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#realtor<?php echo $i;?>Company').autocomplete(BPO<?php echo $i;?>CNameOptions);
        <?php
        }
        }
        /**
         * Desc : Servicing tab new Lender field Autosuggest (Pivotal - #*********)
         */
        if (
                // $activeTab == 'SER' ||
    $activeTab == 'PI'
        ) {
        ?>

        /** Servicing tab >> Lender */
        serviceLenderOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=22',
            minChars: 0,
            onSelect: function (value, data) {
                $('#serviceLender').val(replaceXMLProcess(value));
                document.loanModForm.serviceLenderID.value = data;
                showServiceLenderForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#serviceLender').autocomplete(serviceLenderOptions);

        /** Servicing tab >> servicer */
        servicerOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=27',
            minChars: 0,
            onSelect: function (value, data) {
                $('#servicerRepFirstName').val(replaceXMLProcess(value));
                document.loanModForm.servicerID.value = data;
                showServicerForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();
                } catch (e) {
                }	// | Enable Save Button on Any Auto complete.
            }
        };
        $('#servicerRepFirstName').autocomplete(servicerOptions);

        /** Servicing tab >> Trustee */
        trusteeOptions = {
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=23',
            minChars: 0,
            onSelect: function (value, data) {
                $('#trusteeName').val(replaceXMLProcess(value));
                document.loanModForm.trusteeID.value = data;
                showTrusteeForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                try {
                    enableSaveButton();
                } catch (e) {
                }	// | Enable Save Button on Any Auto complete.
            }
        };
        $('#trusteeID').autocomplete(trusteeOptions);
        <?php
        }
        if ($activeTab == 'ADMIN' || $activeTab == 'LI') {
        $leadSourceurl = '/JQFiles/getLeadSourceList.php?branchType=' . cypher::myEncryption(Strings::showField('userType', 'BranchInfo')) . '&eId=' . cypher::myEncryption($executiveId);
        if ($LMRId > 0) {
        ?>
        leadSourceurl = '<?php echo $leadSourceurl?>';
        <?php
        } else {
        ?>
        var branchId = 0;
        branchId = document.loanModForm.branchId.value;
        leadSourceurl = '/JQFiles/getLeadSourceList.php?eId=' + branchId;
        <?php
        }
        ?>
        leadSourceOptions = {
            serviceUrl: leadSourceurl,
            minChars: 0,
            onSelect: function (value, data) {
                $('#leadSource').val(replaceXMLProcess(value));
                try {
                    enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                } catch (e) {
                }
            }
        };
        $('#leadSource').autocomplete(leadSourceOptions);
        <?php
        }
        if ($activeTab == 'PI' || $activeTab == 'ER' || $activeTab == 'LI' || $activeTab == 'QAPP') {
        ?>
        $('.propertyPictures').click(function () {
            $("#propertyPictures").dialog({
                modal: true,
                resizable: false,
                draggable: true,
                width: '90%',
                title: 'Pictures of property: ' + $(this).attr('data-title'),
                autoOpen: true,
                closeOnEscape: true,
                buttons: {
                    Close: function () {
                        $(this).dialog("close");
                    }
                },
                resizable: false,
                open: function () {   // open event handler
                    $('#propertyPictures').html('Please wait');
                }
            });

            $('.ui-widget-header').css('display', 'block');
            $('.ui-widget-header a').css('display', 'block');

            var img = "<div class='pad5'><img src='" + $(this).attr('data-url') + "'></div><div class='pad10'></div>";
            $('#propertyPictures').html(img);
            // do other stuff.
        });
        <?php
        }
        ?>
    });
    document.getElementById('pageLoad').value = 'Ok'; /* Page Load completed */

    <?php if($activeTab == 'LP' || (($subscribeToHOME == 1 && $subscribePCToHOME == 1) || $userGroup == 'Super') ) { ?>
    tinymce.init({
        selector: "#HRProposalSummary",
        onchange_callback: "enableSaveButton", /* To enableSaveButton */
        content_style: "body {font-size: 12pt;}",
        plugins: 'advlist autolink link image paste lists charmap print preview code',
        menubar: false,
        toolbar: ['styleselect | fontselect | fontsizeselect | undo redo | cut copy paste | bold italic | link image | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent | blockquote subscript superscript | advlist | autolink | lists charmap | print preview | code'],
        image_title: true,
        file_picker_types: 'image',
        width: '100%',
        height: 200,
        autoresize_min_height: 200,
        autoresize_max_height: 400,
        relative_urls: false,
        remove_script_host: false,
        branding: false,
        block_unsupported_drop: true,
        images_file_types: 'jpeg,jpg,png,gif,JPEG,JPG,PNG,GIF',
        images_upload_url: '/backoffice/api_v2/tinymce_upload',
        paste_data_images: true,
    });
    // tinyMCE.init({
    //     // General options
    //     mode: "exact",
    //     elements: " HRProposalSummary",
    //     onchange_callback: "enableSaveButton", /* To enableSaveButton */
    //     theme: "advanced",
    //     skin: "o2k7",
    //     skin_variant: "silver",
    //     plugins: "lists,pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,inlinepopups,autosave",
    //     // Theme options
    //     theme_advanced_buttons1: "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
    //     theme_advanced_buttons2: "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
    //     theme_advanced_buttons3: "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
    //     theme_advanced_buttons4: "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft",
    //     theme_advanced_toolbar_location: "top",
    //     theme_advanced_toolbar_align: "left",
    //     theme_advanced_statusbar_location: "bottom",
    //     theme_advanced_resizing: true,
    //     // Drop lists for link/image/media/template dialogs
    //     template_external_list_url: "../tinymce/lists/template_list.js",
    //     external_link_list_url: "../tinymce/lists/link_list.js",
    //     external_image_list_url: "../tinymce/lists/image_list.js",
    //     media_external_list_url: "../tinymce/lists/media_list.js",
    //     relative_urls: false,
    //     remove_script_host: false,
    //     convert_urls: true,
    // });
    <?php } ?>
    function deleteMultiSelectValueInDB(name, val) {
        if (name == 'LMRProcessorStatus') {
            saveFileSubstatusChange('loanModForm', 'encryptedLId', 'encryptedRId', val, '0', 'enc');
        }
    }

    function saveMultiSelectValueInDB(name, val) {
        if (name == 'LMRProcessorStatus') {
            saveFileSubstatusChange('loanModForm', 'encryptedLId', 'encryptedRId', val, '1', 'enc');
        }
        //QA, FA page
        //BI, AI page
        if (name === 'substatusID[]' || name === 'LMRProcessorStatus') {
            if ($('#isFssUpdated').length > 0) {
                $('#isFssUpdated').val('Yes');
                if ($('#lastUpdatedFss').length > 0) $('#lastUpdatedFss').val(val);
            }
            if ($('#lastUpdatedParam').length > 0) {
                LMRequest.lastUpdatedParams('FSS');
            }
            let allowAutomation = $('#allowAutomation');
            if (allowAutomation.length && allowAutomation.val() === '1') {
                let msg = 'Please click on the "Save" button to check and trigger any related automation(s).';
                let msgType = 'info';
                toastrNotification(msg, msgType);
            }
        }
        //Pipeline page
        if ($('#isPfsFssUpdated').length > 0) {
            $('#isPfsFssUpdated').val('Yes');
        }
    }

    $(".mask_phone:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
    $(".mask_cell:enabled").inputmask("mask", {mask: "999 - 999 - 9999"});
    $(".mask_ssn:enabled").inputmask("999 - 99 - 9999", {placeholder: "___ - __ - ____", clearMaskOnLostFocus: !0});
    $(".mask_date:enabled").inputmask("mm/dd/yyyy", {autoUnmask: !0});

    var form_original_data = $("#loanModForm").serialize();

    function populateContactName(opt, val, idVal = '') {
        $('#idNo').val(idVal);
        var arr = opt.split('_');
        if (val == '') {
            if (opt == 'HOA') {
                $('#HOContactName').autocomplete(HOAOptions).onValueChange();
            } else if (opt == 'HOA2') {
                $('#HOA2ContactName').autocomplete(HOA2Options).onValueChange();
            } else if (opt == 'title') {
                $('#contact_' + idVal).autocomplete(titleOptions).onValueChange();
            } else if (opt == 'titleLName') {
                $('#titleContactLName_' + idVal).autocomplete(titleLNameOptions).onValueChange();
            } else if (opt == 'titleCName') {
                $('#titleCo_' + idVal).autocomplete(titleCNameOptions).onValueChange();
            } else if (opt == 'attorney') {
                $('#titleAttorneyName_' + idVal).autocomplete(attorneyOptions).onValueChange();
            } else if (opt == 'attorneyLName') {
                $('#titleAttorneyLastName').autocomplete(attorneyLOptions).onValueChange();
            } else if (opt == 'attorneyCName') {
                $('#titleAttorneyFirmName_' + idVal).autocomplete(attorneyCNameOptions).onValueChange();
            } else if (opt == 'GCName') {
                $('#GCFirstName').autocomplete(GCOptions).onValueChange();
            } else if (opt == 'GCLName') {
                $('#GCLastName').autocomplete(GCLOptions).onValueChange();
            } else if (opt == 'GCCName') {
                $('#GCCompanyName').autocomplete(GCCNameOptions).onValueChange();
            } else if (opt == 'insuranceCompany') {
                $('#proInsFirstName_' + idVal).autocomplete(insuranceCompanyOptions).onValueChange();
            } else if (opt == 'financialAdvisor') {
                $('#financialAdvisorFirstName_' + idVal).autocomplete(financialAdvisorOptions).onValueChange();
            } else if (opt == 'accountant') {
                $('#accountantFirstName_' + idVal).autocomplete(accountantOptions).onValueChange();
            } else if (opt == 'insuranceCompanyLName') {
                $('#proInsLastName_' + idVal).autocomplete(insuranceCompanyLNameOptions).onValueChange();
            } else if (opt == 'insuranceCompanyCName') {
                $('#proInsName').autocomplete(insuranceCompanyCNameOptions).onValueChange();
            } else if (opt == 'escrow') {
                $('#escrowOfficer_' + idVal).autocomplete(escrowOfficerOptions).onValueChange();
            } else if (opt == 'escrowLName') {
                $('#escrowOfficerLName_' + idVal).autocomplete(escrowOfficerLNameOptions).onValueChange();
            } else if (opt == 'escrowCName') {
                $('#escrowOfficerFirmName_' + idVal).autocomplete(escrowOfficerCNameOptions).onValueChange();
            } else if (opt == 'BA') {
                var cAID = document.loanModForm.attorneyID.value;

                BAOptions = {
                    serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3&filterCID=' + cAID,
                    minChars: 0,
                    onSelect: function (value, data) {
                        $('#attorneyName').val(replaceXMLProcess(value));
                        document.loanModForm.attorneyID.value = data;
                        document.loanModForm.tempAttorneyContactName.value = value;
                        showAttorneyContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                        try {
                            enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                        } catch (e) {
                        }
                    }
                };

                $('#attorneyName').autocomplete(BAOptions).onValueChange();
            } else if (opt == 'Counselor') {
                $('#creditCounselorName').autocomplete(counselorOtions).onValueChange();
            } else if (opt == 'POA') {
                $('#POAttorneyName').autocomplete(POAOptions).onValueChange();
            } else if (opt == 'realtor') {
                $('#realtor').autocomplete(realtorOptions).onValueChange();
            } else if (opt == 'buyer1') {
                $('#buyer1AttorneyName').autocomplete(buyer1Options).onValueChange();
            } else if (opt == 'buyer2') {
                $('#buyer2AttorneyName').autocomplete(buyer2Options).onValueChange();
            } else if (opt == 'buyer3') {
                $('#buyer3AttorneyName').autocomplete(buyer3Options).onValueChange();
            } else if (opt == 'buyer1Agent') {
                $('#buyer1AgentName').autocomplete(buyer1AgentOptions).onValueChange();
            } else if (opt == 'buyer2Agent') {
                $('#buyer2AgentName').autocomplete(buyer2AgentOptions).onValueChange();
            } else if (opt == 'buyer3Agent') {
                $('#buyer3AgentName').autocomplete(buyer3AgentOptions).onValueChange();
            } else if (opt == 'PM') {
                $('#propMgmntContactPerson').autocomplete(PMOptions).onValueChange();
            } else if (opt == 'CA') {
                var aID = document.loanModForm.counselAttorneyID.value;

                CAOptions = {
                    serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3&filterCID=' + aID,
                    minChars: 0,
                    onSelect: function (value, data) {
                        $('#counselAttorneyName').val(replaceXMLProcess(value));
                        document.loanModForm.counselAttorneyID.value = data;
                        document.loanModForm.tempAttorneyContactName.value = value;
                        showCounselAttorneyContactsForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                        try {
                            enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                        } catch (e) {
                        }
                    }
                };

                $('#counselAttorneyName').autocomplete(CAOptions).onValueChange();
            } else if (opt == 'appraiser1') {
                $('#appraiser1AppraiserName').autocomplete(appraiser1Options).onValueChange();
            } else if (opt == 'appraiser1LName') {
                $('#appraiser1LName').autocomplete(appraiser1LNameOptions).onValueChange();
            } else if (opt == 'appraiser1CName') {
                $('#appraiser1Company').autocomplete(appraiser1CNameOptions).onValueChange();
            } else if (opt == 'appraiser2') {
                $('#appraiser2AppraiserName').autocomplete(appraiser2Options).onValueChange();
            } else if (opt == 'appraiser2LName') {
                $('#appraiser2LName').autocomplete(appraiser2LNameOptions).onValueChange();
            } else if (opt == 'appraiser2CName') {
                $('#appraiser2Company').autocomplete(appraiser2CNameOptions).onValueChange();
            } else if (opt == 'BPO1') {
                $('#BPO1RealtorName').autocomplete(BPO1Options).onValueChange();
            } else if (opt == 'BPO1LName') {
                $('#BPO1LName').autocomplete(BPO1LNameOptions).onValueChange();
            } else if (opt == 'BPO1CName') {
                $('#realtor1Company').autocomplete(BPO1CNameOptions).onValueChange();
            } else if (opt == 'BPO2') {
                $('#BPO2RealtorName').autocomplete(BPO2Options).onValueChange();
            } else if (opt == 'BPO2LName') {
                $('#BPO2LName').autocomplete(BPO2LNameOptions).onValueChange();
            } else if (opt == 'BPO2CName') {
                $('#realtor2Company').autocomplete(BPO2CNameOptions).onValueChange();
            } else if (opt == 'BPO3') {
                $('#BPO3RealtorName').autocomplete(BPO3Options).onValueChange();
            } else if (opt == 'BPO3LName') {
                $('#BPO3LName').autocomplete(BPO3LNameOptions).onValueChange();
            } else if (opt == 'BPO3CName') {
                $('#realtor3Company').autocomplete(BPO3CNameOptions).onValueChange();
            } else if (opt == 'Lender') {
                $('#serviceLenderName').autocomplete(serviceLenderOptions).onValueChange();
            } else if (opt == 'Servicer') {
                $('#servicerRepFirstName').autocomplete(servicerOptions).onValueChange();
            } else if (opt == 'Trustee') {
                $('#trusteeName').autocomplete(trusteeOptions).onValueChange();
            } else if (arr[0] == 'investorName') {
                var conId = '';
                try {
                    conId = arr[1];
                } catch (e) {
                }

                if (jQuery.type(conId) === "undefined") {
                    conId = '';
                } else {
                    conId = '_' + conId;
                }
                investorContactsOptions = {
                    serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=5',
                    minChars: 0,
                    onSelect: function (value, data) {
                        $('#' + opt).val(replaceXMLProcess(value));
                        $('#investorID' + conId).val(data);
                        showInvestorForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>', opt);
                        try {
                            enableSaveButton();
                        } catch (e) {
                        } // | Enable Save Button on Any Auto complete.
                    }
                };
                $('#' + opt).autocomplete(investorContactsOptions).onValueChange();

            } else if (opt == 'TA') {
                var tId = document.loanModForm.trusteeAttorneyID.value;
                CAOptions = {
                    serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=23&filterCID=' + aID,
                    minChars: 0,
                    onSelect: function (value, data) {
                        $('#trusteeName').val(replaceXMLProcess(value));
                        document.loanModForm.trusteeAttorneyID.value = data;
                        document.loanModForm.trusteeAttorneyContactName.value = value;
                        try {
                            enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                        } catch (e) {
                        }
                    }
                };

                $('#trusteeName').autocomplete(CAOptions).onValueChange();
            } else if (opt == 'Entity') {
                <?php if ( Strings::showField('clientId', 'LMRInfo') > 0 ) { ?>
                let PCID = '<?php echo $PCID; ?>';
                EntityOptions = {
                    serviceUrl: siteSSLUrl + 'JQFiles/getAutoCompletedClientEntityInfo.php?PCID=' + PCID + '&autoCID=' + <?php echo Strings::showField('clientId', 'LMRInfo'); ?>,
                    minChars: 0,/* it should populate all the business entites on click yes radio button : card309 */
                    onSelect: function (value, data) {
                        $('#entityName').val(value);
                        $('#CBEID').val(data);
                        showPCClientEntityInfoForFile(data, '<?php echo $PCID; ?>');
                    }
                };
                $('#entityName').autocomplete(EntityOptions).onValueChange(); // Passing value from matching email Entity Info End..
                if ($("input[name='btnSave']").is("[disabled=disabled]")) { /* enable the save button on populating the entity : card309 */
                    $("input[name='btnSave']").attr("disabled", false);
                }
                <?php } ?>
            } else if (opt == 'TR') {
                TROptions = {
                    serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=23',
                    minChars: 0,
                    onSelect: function (value, data) {
                        $('#trusteeName').val(replaceXMLProcess(value));
                        document.loanModForm.trusteeID.value = data;
                        document.loanModForm.temptrusteeContactName.value = value;
                        showTrusteeForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
                        try {
                            enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                        } catch (e) {
                        }
                    }
                };
                $('#trusteeName').autocomplete(TROptions);
            }
        }
//			$('#HOContactName').autocomplete(HOAOptions).onValueChange();
    }

    function closeSuggestionDropdown() {
        if ($('div.autocomplete:visible').length > 0) {
            $('div.autocomplete').autocomplete('close');
        }
    }

    function populateHOACompanyName(opt, val) {
        $('#condominiumOrHOAFeeAmtReceiver').autocomplete(PMOptions).onValueChange();
    }
    <?php
    if ($activeTab == 'ADMIN' || $activeTab == 'LI' || $activeTab == 'QAPP' || $activeTab == 'SSS' || $activeTab == 'CI') {
    ?>

    function listAllLeadSource(val) {
        if (val == '') {
            <?php
            $leadSourceurl = '/JQFiles/getLeadSourceList.php?branchType=' . cypher::myEncryption(Strings::showField('userType', 'BranchInfo')) . '&eId=' . cypher::myEncryption($executiveId);
            if ($LMRId > 0) {
            ?>
            leadSourceurl = '<?php echo $leadSourceurl?>';
            <?php
            } else {
            ?>
            var branchId = 0;
            branchId = document.loanModForm.branchId.value;
            leadSourceurl = '/JQFiles/getLeadSourceList.php?eId=' + branchId;
            <?php
            }
            ?>

            leadSourceOptions = {
                serviceUrl: leadSourceurl,
                minChars: 0,
                onSelect: function (value, data) {
                    $('#leadSource').val(replaceXMLProcess(value));
                    try {
                        checkRef(value);
                        enableSaveButton();																		// | Enable Save Button on Any Auto complete.
                    } catch (e) {
                    }
                }
            };
            $('#leadSource').autocomplete(leadSourceOptions).onValueChange();
        }
    }

    function clearLeadSourceVal(formName, fldName) {
        try {
            eval("document." + formName + "." + fldName + ".value = ''");
        } catch (e) {
        }
    }
    <?php
    }
    ?>
</script>
