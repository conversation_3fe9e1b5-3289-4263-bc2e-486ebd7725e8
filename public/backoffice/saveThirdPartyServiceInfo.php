<?php

global $PCID;

use models\composite\oHMLOInfo\saveHMLOClientInfo;
use models\composite\oHMLOInfo\saveHMLOFile2Info;
use models\composite\oThirdPartyServices\saveLoanInformation;
use models\Controllers\backoffice\saveThirdPartyServiceInfo;
use models\Controllers\LMRequest\Property;
use models\CustomField;
use models\cypher;
use models\Database2;
use models\lendingwise\tblAutomatedRuleRequestV2;
use models\lendingwise\tblFile;
use models\lendingwise_log\ChangeLog;
use models\PageVariables;
use models\Request;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;
use models\Controllers\backoffice\saveThirdPartyServiceInfoLegalDocs;

session_start();
require '../includes/util.php';

require CONST_BO_PATH . 'initPageVariables.php';
require CONST_BO_PATH . 'getPageVariables.php';

UserAccess::checkReferrerPgs(['url' => 'LMRequest.php']);
UserAccess::CheckAdminUse();

$executiveId = $LMRId = $responseId = 0;
$op = $goToTab = $isSave = $activeTab = '';
/**
 * Loan file details
 */
if (isset($_REQUEST['encryptedEId'])) {
    $executiveId = cypher::myDecryption(trim($_REQUEST['encryptedEId']));
}

if (isset($_REQUEST['encryptedLId'])) {
    $LMRId = cypher::myDecryption(trim($_REQUEST['encryptedLId']));
}
if (isset($_REQUEST['encryptedRId'])) {
    $responseId = trim(cypher::myDecryption(trim($_REQUEST['encryptedRId'])));
}
if (isset($_REQUEST['goToTab'])) {
    $goToTab = trim($_REQUEST['goToTab']);
}
if (isset($_REQUEST['op'])) {
    $op = trim($_REQUEST['op']);
}
if (isset($_REQUEST['isSave'])) {
    $isSave = trim($_REQUEST['isSave']);
}
if (isset($_REQUEST['activeTab'])) {
    $activeTab = trim($_REQUEST['activeTab']);
}
if (isset($_REQUEST['ssnNumber'])) {
    $_REQUEST['ssnNumber'] = Strings::getNumberValue($_REQUEST['ssnNumber']);
}
if (isset($_REQUEST['phoneNumber'])) {
    $_REQUEST['phoneNumber'] = Strings::getNumberValue($_REQUEST['phoneNumber']);
}
if (isset($_REQUEST['cellNumber'])) {
    $_REQUEST['cellNumber'] = Strings::getNumberValue($_REQUEST['cellNumber']);
}
if (isset($_REQUEST['employer1Phone'])) {
    $_REQUEST['employer1Phone'] = Strings::getNumberValue($_REQUEST['employer1Phone']);
}
if (isset($_REQUEST['businessPhone'])) {
    $_REQUEST['businessPhone'] = Strings::getNumberValue($_REQUEST['businessPhone']);
}
if (isset($_REQUEST['coBSsnNumber'])) {
    $_REQUEST['coBSsnNumber'] = Strings::getNumberValue($_REQUEST['coBSsnNumber']);
}
if (isset($_REQUEST['coBPhoneNumber'])) {
    $_REQUEST['coBPhoneNumber'] = Strings::getNumberValue($_REQUEST['coBPhoneNumber']);
}
if (isset($_REQUEST['borrowerDOB'])) {
    $_REQUEST['borrowerDOB'] = Dates::formatDateWithRE($_REQUEST['borrowerDOB'], 'MDY', 'Y-m-d');
}
if (isset($_REQUEST['coBorrowerDOB'])) {
    $_REQUEST['coBorrowerDOB'] = Dates::formatDateWithRE($_REQUEST['coBorrowerDOB'], 'MDY', 'Y-m-d');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    ChangeLog::LogChanges(
        tblFile::class,
        $LMRId,
        basename(__FILE__, '.php'),
        $_REQUEST,
        PageVariables::$userNumber
    );
}

/** Save file information */
$inputArray = [
    'LMRId' => $_REQUEST['LMRId'],
    'PCID' => $_REQUEST['FPCID'],
    'UID' => $_REQUEST['UID'],
    'URole' => $_REQUEST['URole'],
    'CID' => $_REQUEST['selClientId'],
];
saveHMLOClientInfo::getReport($inputArray);
saveLoanInformation::getReport($inputArray);
saveHMLOFile2Info::getReport($inputArray);
Property::updatePrimaryPropertyInfo($LMRId);

/* Generate redirection URL */
$redirect = strtok($_SERVER['HTTP_REFERER'], '?') . '?eId=' . cypher::myEncryption($executiveId)
    . '&lId=' . cypher::myEncryption($LMRId)
    . '&rId=' . cypher::myEncryption($responseId)
    . '&op=' . trim($op)
    . '&tabOpt=' . $activeTab;

Strings::SetSess('msg', 'Updated Successfully.');

$requestFor = Request::GetClean('request_for');
$requestType = Request::GetClean('request_type');
if (empty($requestFor) && $requestType == 'Submit') {
    $_REQUEST['files'] = $_FILES;
    Strings::SetSess('msg', 'Request submitted successfully.');
    $res = saveThirdPartyServiceInfo::getReport(
        $LMRId,
        $_REQUEST,
        $_SESSION['firstName'],
        $_SESSION['lastName'],
        $_SESSION['userGroup'],
        $_SESSION['userRole'],
        intval($_SESSION['userNumber']),
    );
}

// Legal Docs save and submit
if ($requestFor == 'legalDocs') {
    $redirect .= '&ser=ld';
    $res = saveThirdPartyServiceInfoLegalDocs::getReport(
        $LMRId,
        $_REQUEST,
        $_SESSION['firstName'],
        $_SESSION['lastName'],
        $_SESSION['userGroup'],
        $_SESSION['userRole'],
        intval($_SESSION['userNumber'])
    );
    if ($res['status']) {
        if ($requestType == 'Submit') {
            Strings::SetSess('msg', 'Request submitted successfully. Please head to the Lightning Docs App to generate Documents.');
        }
    } else {
        Strings::SetSess('msg', $res['message']);
    }
}

tblAutomatedRuleRequestV2::Trigger($LMRId, $PCID);
CustomField::Save(
    tblFile::class,
    $LMRId,
    $PCID,
    PageVariables::$userNumber,
    PageVariables::$userRole,
    PageVariables::$userGroup
);
Database2::saveLogQuery();

HTTP::Redirect($redirect);
