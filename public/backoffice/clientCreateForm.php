<?php
global $isClientProfile, $userRole, $userGroup, $selClientId, $selExecutiveId, $tabNumb, $PCModulesArray;

use models\composite\oAffiliate\getBranchAffiliateSubmissionCode;
use models\composite\oBranch\getBranches;
use models\composite\oBranch\getMyDetails;
use models\composite\oBranch\getPromoCodeForBranch;
use models\composite\oClient\getPCClientExperienceInfo;
use models\composite\oClient\getPCClientRealEstateInfo;
use models\composite\oClient\getPLOClients;
use models\composite\oPC\getAppFormFields;
use models\composite\oPC\getPCModules;
use models\constants\gl\glUserGroup;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\loanForm;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$clientFName = '';
$clientLName = '';
$clientPhone = '';
$clientPhone1 = '';
$clientPhone2 = '';
$clientPhone3 = '';
$clientExt = '';
$clientCell = '';
$clientCell1 = '';
$clientCell2 = '';
$clientCell3 = '';
$clientEmail = '';
$clientSecondaryEmail = '';
$agreedTC = 0;
$clientAddress = '';
$clientCity = '';
$clientState = '';
$clientZip = '';
$clientMembership = '';
$clientPaymentStatus = '';
$publishBInfo = 0;
$race = [];
$ethnicity = [];
$gender = 0;
$veteran = '';
$tabIndex = 1;
$agentInfoArray = [];
$agentInputArray = [];
$executiveId = 0;
$PLOSearchArray = [];
$mySearchArray = [];
$PLOKeyArray = [];
$LMRInfoArray = [];
$PLOPaymentGateway = '';
$clientAllowFreeLMR = 0;
$LMRPaypalMemberEmail = '';
$eCheckUrl = '';
$isECheck = 0;
$executiveEmail = '';
$LMRPromoCodeArray = [];
$newLMRPromoCodeArray = [];
$referralSiteCode = 1;

$clientInfoArray = [];
$clientPhoneNumberArray = [];
$clientLMRAffiliateCode = 0;
$clientCellNumberArray = [];
$clientAgentInfoArray = [];

$agentKeyArray = [];
$newLMRPromoCodeArray = [];
$brokerInfoArray = [];
$agentNo = 0;
$brokerEmail = '';
$promoCodeArray = [];
$promoCode = '';
$agentCode = 0;
$brokEmail = '';
$processorAllow = false;
$adminUserRole = '';
$processorNumb = 0;
$assignedProcCompId = 0;
$processorLMRAEIDInfoArray = [];
$LMRAEId = 0;
$LMRAEIDs = 0;
$processorLMRAEIDKeyArray = [];
$resultAgentInfoArray = [];
$agentPromoCodeArray = [];
$agentKeyArray = [];
$agentEmails = '';
$loggedInUser = '';
$TMPClientAllow = false;
$serviceProvider = $methodOfContact = '';
$customMessage = '';
$myTimeZone = '';
$AEUserType = '';
$sendMarketingEmail = 1;
$DIYDescription = '';
$clientFullName = '';
$allowToViewMarketPlace = 1;
$clientCreditScoreRange = $midFicoScore = '';
$borrowerUnderEntity = '';
$sameAsEntityAddr = '';
$driverLicenseState = '';
$driverLicenseNumber = '';

$clientInfo = [];
$branchList = [];
$agentList = [];
$agentListInfo = [];
$methodContactArray = [];
$borPriInvesStrategyArray = $coBorPriInvesStrategyArray = [];
$loadTabsArray = [];
$allowToLockLoanFileClient = 1;

/* Added by Berin */
$allowToEdit = $isClientProfile;
$PCquickAppFieldsInfo = $fileHMLOEntityInfo = [];
$showBorrowerEntityDispOpt = 'display:contents';
for ($j = 1; $j <= 10; $j++) {
    ${'showMember' . $j . 'DispOpt'} = 'display:none';
}
$memberName = $memberTitle = $memberOwnership = $memberAddress = $memberPhone = $memberCell = $memberSSN = $memberDOB = $memberEmail = '';
$isLO = $isHMLO = '';
$assetCash = $assetCheckingAccounts = $networthOfBusinessOwned = $assetStocks = $assetIRAAccounts = $assetESPOAccounts = '';
$assetLifeInsurance = $assetHome = $assetHomeOwed = $assetORE = $assetSR = $assetOREOwed = $assetSROwed = $assetCars = $assetCarsOwed = $assetOther = '';
$otherAmtOwed = $assetOther = $assetSavingMoneyMarket = $otherDesc = '';
$assetAccount = $assetAccountOwd = $assetStocksOwed = $assetNonMarketableSecurities = $assetNonMarketableSecuritiesOwd = $assetIRAAccountsOwed = $assetESPOAccountsOwed = '';
$assetLifeInsuranceOwed = $assetAvailabilityLinesCreditOwed = $notesPayableToBanksOthersOwed = $installmentAccountOwed = $revolvingDebtOwed = $unpaidPayableTaxesOwed = '';
$assetSecNotesOwd = $assetUnsecNotesOwd = $assetAcctPayableOwd = $assetMarginOwd = '';
$otherLiabilitiesOwed = $unpaidPayableTaxesDesc = $otherLiabilityDetails = '';
$totalAssets = $totalAssetsOwed = $totalAssetsNetValue = 0;
$otherDescription = '';
$registerDate = '';
$fullTimeRealEstateInvestor = '';
$assetTotalCashBankAcc = $assetTotalRetirementValue = $assetAvailabilityLinesCredit = '';
$borPrimaryInvestmentStrategy = $borPrimaryInvestmentStrategyExplain = '';
$internalInfoStatus = '';
$internalInfoCreditLine = '';
$internalInfoCreditLine = 0;
$vestedInterest = $automobilesOwned3x = $automobilesOwned3x1 = $otherAssets = 0;
$internalInfoStatusArr = [];
$fieldsInfo = [];
$CID = 0;
$internalInfoTagsArr = [];
$internalInfoTags = '';
$faceAmount = '';
$automobilesOwned1 = '';
$flipPropCompletedLifetime = $groundPropCompletedLifetime = $sellPropCompletedLifetime = '';
$overallRealEstateInvesExp = '';
$borrowerDOB = $borrowerPOB = $ssnNumber = '';
$clientMaxAcqusitionLTV = $clientMaxRehabBudget = $clientMaxAllowedARV = 0;
$clientInternalInfoLoanTerms = $clientInternalInfoAddTerms = '';

/* Added by Berin */

$allowToSubmitOffer = 0;

/*HMDA*/
$FIEthnicity = '';
$FIEthnicitySub = [];
$FIEthnicitySubOther = '';
$FISex = '';
$FIRace = '';
$FIRaceSub = [];
$FIRaceAsianOther = '';
$FIRacePacificOther = '';
$DemoInfo = '';
/*HMDA*/
$isEF = 0;
if ($userRole == 'Super') {
    $loggedInUser = 'Admin';
} else if ($userGroup == 'Employee') {
    $loggedInUser = 'Staff';
} else if ($userRole == 'Branch') {
    $loggedInUser = 'PLO';
} else if ($userRole == 'Agent') {
    $loggedInUser = $userRole;
}


if ($selClientId > 0) {
    $myArray = ['clientID' => $selClientId];
    $clientInfo = getPLOClients::getReport($myArray);
    if (count($clientInfo) > 0) {
        $clientInfoArray = $clientInfo['clientInfo'];
        $clientNotesBO = $clientInfo['clientNotesBO'];
        $clientNotesAll = $clientInfo['clientNotesAll'];
    }
}
for ($c = 0; $c < count($clientInfoArray); $c++) {
    $CID = trim($clientInfoArray[$c]['CID']);
    $clientFName = trim($clientInfoArray[$c]['clientFName']);
    $clientLName = trim($clientInfoArray[$c]['clientLName']);
    $clientPhone = trim($clientInfoArray[$c]['clientPhone']);
    $clientCell = trim($clientInfoArray[$c]['clientCell']);
    $clientEmail = trim($clientInfoArray[$c]['clientEmail']);
    $clientSecondaryEmail = trim($clientInfoArray[$c]['clientSecondaryEmail']);
    $clientAddress = trim($clientInfoArray[$c]['clientAddress']);
    $clientCity = trim($clientInfoArray[$c]['clientCity']);
    $clientState = trim($clientInfoArray[$c]['clientState']);
    $clientZip = trim($clientInfoArray[$c]['clientZip']);
    $clientLMRAffiliateCode = trim($clientInfoArray[$c]['LMRAffiliateCode']);
    $serviceProvider = trim($clientInfoArray[$c]['serviceProvider']);
    $myTimeZone = trim($clientInfoArray[$c]['timeZone']);
    $sendMarketingEmail = trim($clientInfoArray[$c]['sendMarketingEmail']);
    $allowToViewMarketPlace = trim($clientInfoArray[$c]['allowToViewMarketPlace']);
    $allowToSubmitOffer = trim($clientInfoArray[$c]['allowToSubmitOffer']);
    $registerDate = trim($clientInfoArray[$c]['registerDate']);
    $internalInfoCreditLine = trim($clientInfoArray[$c]['internalInfoCreditLine']);
    $borrowerDOB = trim($clientInfoArray[$c]['borrowerDOB']);
    $borrowerPOB = trim($clientInfoArray[$c]['borrowerPOB']);
    $ssnNumber = trim($clientInfoArray[$c]['ssnNumber']);
    $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'YMD', 'm/d/Y');
    $clientMaxAcqusitionLTV = trim($clientInfoArray[$c]['clientMaxAcqusitionLTV']);
    $clientMaxRehabBudget = trim($clientInfoArray[$c]['clientMaxRehabBudget']);
    $clientMaxAllowedARV = trim($clientInfoArray[$c]['clientMaxAllowedARV']);
    $clientInternalInfoLoanTerms = trim($clientInfoArray[$c]['clientInternalInfoLoanTerms']);
    $clientInternalInfoAddTerms = trim($clientInfoArray[$c]['clientInternalInfoAddTerms']);
    $driverLicenseState = trim($clientInfoArray[$c]['driverLicenseState']);
    $driverLicenseNumber = trim($clientInfoArray[$c]['driverLicenseNumber']);

    if ($clientInfoArray[$c]['PCID'] > 0) $PCID = trim($clientInfoArray[$c]['PCID']);
    if (array_key_exists('clientCreditScoreRange', $clientInfoArray[$c])) $clientCreditScoreRange = trim($clientInfoArray[$c]['clientCreditScoreRange']);
    if (array_key_exists('midFicoScore', $clientInfoArray[$c])) $midFicoScore = trim($clientInfoArray[$c]['midFicoScore']);

    if (array_key_exists('publishBInfo', $clientInfoArray[$c])) $publishBInfo = trim($clientInfoArray[$c]['publishBInfo']);
    if (array_key_exists('race', $clientInfoArray[$c])) $race = $clientInfoArray[$c]['race'] != '' ? explode(',', $clientInfoArray[$c]['race']) : [];
    if (array_key_exists('ethnicity', $clientInfoArray[$c])) $ethnicity = $clientInfoArray[$c]['ethnicity'] != '' ? explode(',', $clientInfoArray[$c]['ethnicity']) : [];
    if (array_key_exists('gender', $clientInfoArray[$c])) $gender = trim($clientInfoArray[$c]['gender']);
    if (array_key_exists('veteran', $clientInfoArray[$c])) $veteran = trim($clientInfoArray[$c]['veteran']);
    if (array_key_exists('methodOfContact', $clientInfoArray[$c])) $methodOfContact = trim($clientInfoArray[$c]['methodOfContact']);

    if (array_key_exists('FIEthnicity', $clientInfoArray[$c])) $FIEthnicity = trim($clientInfoArray[$c]['FIEthnicity']);
    if (array_key_exists('FIEthnicitySub', $clientInfoArray[$c])) $FIEthnicitySub = $clientInfoArray[$c]['FIEthnicitySub'] != '' ? explode(',', $clientInfoArray[$c]['FIEthnicitySub']) : [];
    if (array_key_exists('FIEthnicitySubOther', $clientInfoArray[$c])) $FIEthnicitySubOther = trim($clientInfoArray[$c]['FIEthnicitySubOther']);
    if (array_key_exists('FISex', $clientInfoArray[$c])) $FISex = trim($clientInfoArray[$c]['FISex']);
    if (array_key_exists('FIRace', $clientInfoArray[$c])) $FIRace = trim($clientInfoArray[$c]['FIRace']);
    if (array_key_exists('FIRaceSub', $clientInfoArray[$c])) $FIRaceSub = $clientInfoArray[$c]['FIRaceSub'] != '' ? explode(',', $clientInfoArray[$c]['FIRaceSub']) : [];
    if (array_key_exists('FIRaceAsianOther', $clientInfoArray[$c])) $FIRaceAsianOther = trim($clientInfoArray[$c]['FIRaceAsianOther']);
    if (array_key_exists('FIRacePacificOther', $clientInfoArray[$c])) $FIRacePacificOther = trim($clientInfoArray[$c]['FIRacePacificOther']);
    if (array_key_exists('DemoInfo', $clientInfoArray[$c])) $DemoInfo = trim($clientInfoArray[$c]['DemoInfo']);

    if (array_key_exists('internalInfoStatus', $clientInfoArray[$c])) $internalInfoStatus = trim($clientInfoArray[$c]['internalInfoStatus']);
    if (array_key_exists('internalInfoTags', $clientInfoArray[$c])) $internalInfoTags = trim($clientInfoArray[$c]['internalInfoTags']);
    if (array_key_exists('allowToLockLoanFileClient', $clientInfoArray[$c])) $allowToLockLoanFileClient = trim($clientInfoArray[$c]['allowToLockLoanFileClient']);

    $creferralCode = trim($clientInfoArray[$c]['referralCode']);
}

if(PageVariables::$userGroup == glUserGroup::USER_GROUP_SUPER) {
    $PCID = Request::GetClean('PCID') ? cypher::myDecryption(Request::GetClean('PCID')) : PageVariables::$PCID;

}
if ($methodOfContact != '') $methodContactArray = explode(',', $methodOfContact);
if ($internalInfoStatus != '') $internalInfoStatusArr = explode(',', $internalInfoStatus);
if ($internalInfoTags != '') $internalInfoTagsArr = explode(',', $internalInfoTags);


$clientFullName = $clientFName . ' ' . $clientLName;
if ($creferralCode > 0) {
    $clientAssignedBranch = 0;
    $branchData = getBranchAffiliateSubmissionCode::getReport([
        'affiliateCode' => $creferralCode,
    ]);
    $clientAssignedBranch = $branchData['executiveId'];
    if ($clientAssignedBranch > 0) {
        $cresult = getMyDetails::getReport(['executiveId' => $clientAssignedBranch]);
        $clientSelectBranch = $cresult[$clientAssignedBranch]['LMRExecutive'];
    }
}
if ($clientLMRAffiliateCode > 0) {
//        $objLMRAffiliate = new ObjLMRAffiliate();
//        $clientAgentInfoArray = $objLMRAffiliate->getAgentAffiliateSubmissionCode($clientLMRAffiliateCode);
    if (count($clientAgentInfoArray) > 0) {
        $clientAllowFreeLMR = $clientAgentInfoArray['clientAllowFreeLMR'];
        $clientAgentNumber = $clientAgentInfoArray['brokerNumber'];
    }
}

$myOptArray = ['execID' => explode(',', $selExecutiveId)];
if ($selExecutiveId > 0) {
    $branchList = getBranches::getReport($myOptArray);
    if (count($branchList) > 0) {
        $LMRInfoArray = $branchList['branchList'];
    }
}

for ($lr = 0; $lr < count($LMRInfoArray); $lr++) {
    $tempArray = [];
    $tempArray = $LMRInfoArray[$lr];
    $PLOPaymentGateway = trim($tempArray['paymentGateway']);
    $LMRPaypalMemberEmail = trim($tempArray['paypalEmailId']);
    $eCheckUrl = trim($tempArray['eCheckUrl']);
    $isECheck = trim($tempArray['isECheck']);
    $executiveEmail = trim($tempArray['executiveEmail']);
    $AEUserType = trim($tempArray['userType']);
    $customMessage = trim($tempArray['customMessage']);
}
if ($executiveEmail != '') {
    $exEmail = "'" . $executiveEmail . "'";
    $ip = ['executiveEmails' => $exEmail];
    $LMRPromoCodeArray = getPromoCodeForBranch::getReport($ip);
    if (count($LMRPromoCodeArray) > 0) {
        $newLMRPromoCodeArray = array_change_key_case($LMRPromoCodeArray, CASE_LOWER);
        if (array_key_exists(strtolower($executiveEmail), $newLMRPromoCodeArray)) {
            $referralSiteCode = $newLMRPromoCodeArray[strtolower($executiveEmail)];
            $promoCode = $referralSiteCode;
        }
    }
}

$ip = ['states' => $clientState];
$stateArray = Arrays::fetchStates();
/** Fetch all States **/
$fileTab = 'BO';
loanForm::$fileTab = $fileTab;
if (($tabNumb >= 3 && $tabNumb <= 6) || $tabNumb == 1) {
    $pcTypeArray = getPCModules::getReport(['PCID' => $PCID, 'keyNeeded' => 'n']);
    $fieldsInfo = getAppFormFields::getReport(['assignedPCID' => $PCID, 'fTArray' => $pcTypeArray, 'myOpt' => $fileTab]);
}
// Checks access to the 'Update Borrower Profile > Assets' section.
$assetArray = BaseHTML::sectionAccess([
    'sId' => 'Assets',
    'opt' => $fileTab,
]);


$loadTabsArray = [];
if ($tabNumb == 1) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','1');\"";
?>
<div class="col-md-12 card card-custom p-0 card-sticky " id="kt_page_sticky_card">
    <div class="card-header px-2 py-2 border" style="">
        <div class="pipelineNavigation ">
            <ul class="nav nav-pills">
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2  <?php if ($tabNumb == 1) {
                        echo 'active';
                    } ?>" href="#" data-toggle="tab" <?php echo $newHref; ?>>
                        <?php
                        if ($selClientId > 0) {
                            if (($userRole != 'Client') && ($AEUserType == 'PLO')) { ?>1.<?php } ?> Contact info
                        <?php } else { ?>
                            Contact Info
                        <?php } ?>
                    </a>
                </li>
                <?php
                if ($userRole == 'Client') {
                    $loadTabsArray[] = '3';
                } else if ($AEUserType == 'PLO') {
                    if ($selClientId > 0) {
                        if ($tabNumb == 2) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','2');\"";
                        ?>
                        <?php $loadTabsArray[] = '2'; ?>
                        <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                            <a class="nav-link px-4 py-2 <?php if ($tabNumb == 2) {
                                echo 'active';
                            } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                                <span class="nav-text font-weight-bold">2. Activate account</span>
                            </a>
                        </li>
                        <?php
                    }
                }

                /**
                 * Description : Background Info, Entity Info, Experience Info tabs only shown HMLO module.
                 * Date        : August 22, 2017
                 * Author      : Viji, Venkatesh and Suresh
                 * Updated by  : Suresh
                 * Pivotal No  : #*********
                 **/

                if (in_array('HMLO', $PCModulesArray) || in_array('EF', $PCModulesArray)) {
                    if ($tabNumb == 3) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','3');\"";
                    ?>
                    <?php $loadTabsArray[] = '3'; ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 3) {
                            echo 'active';
                        } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                            <span class="nav-text font-weight-bold">Background Info</span>
                        </a>
                    </li>

                    <?php
                    if ($tabNumb == 4) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','4');\"";
                    ?>

                    <?php $loadTabsArray[] = '4'; ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 4) {
                            echo 'active';
                        } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                            <span class="nav-text font-weight-bold">Entity Info</span>
                        </a>
                    </li>
                    <?php
                    if ($tabNumb == 5) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','5');\"";
                    ?>
                    <?php $loadTabsArray[] = '5'; ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 5) {
                            echo 'active';
                        } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                            <span class="nav-text font-weight-bold">Experience Info</span>
                        </a>
                    </li>
                    <?php
                    if ($tabNumb == 6) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','6');\"";
                    ?>
                    <?php $loadTabsArray[] = '6'; ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 6) {
                            echo 'active';
                        } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                            <span class="nav-text font-weight-bold">Assets</span>
                        </a>
                    </li>
                    <?php
                    if ($userGroup == 'Super' || $userGroup == 'Employee' || $userGroup == 'Branch') {
                        if ($tabNumb == 7) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','7');\"";
                        ?><?php $loadTabsArray[] = '7'; ?>
                        <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                            <a class="nav-link px-4 py-2 <?php if ($tabNumb == 7) {
                                echo 'active';
                            } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                                <span class="nav-text font-weight-bold">Internal Info</span>
                            </a>
                        </li>
                    <?php }
                } ?>

                <?php
                if ($tabNumb == 8) $newHref = ''; else  $newHref = "onclick=\"javascript:submitAndNavigateTab('" . cypher::myEncryption($selClientId) . "','" . cypher::myEncryption($selExecutiveId) . "','8');\"";
                ?>
                <?php $loadTabsArray[] = '8'; ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2 <?php if ($tabNumb == 8) {
                        echo 'active';
                    } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                        <span class="nav-text font-weight-bold">Docs</span>
                    </a>
                </li>



                <?php if(SHOW_DEBUG_TOOLS) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Clear Loan Form"
                        id="clear_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.clearForm('clientRegForm');debugTools.clearForm('loanModForm');">
                        Clear Form
                        <i class="ml-2 fa fa-eraser"></i>
                    </span>
                    </li>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Fill Loan Form"
                        id="fill_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.fillForm('clientRegForm');debugTools.fillForm('loanModForm');">
                        Fill Form
                        <i class="ml-2 fa fa-edit"></i>
                    </span>
                    </li>
                <?php } ?>

            </ul>
        </div>
    </div>
</div>


<?php
if ($tabNumb == 1) {
    ?>
    <form name="clientRegForm" id="clientRegForm" method="POST" class="clientRegForm"
          action="../backoffice/saveClientInfo.php" onsubmit="return validateClientForm();">
        <!-- Collects user's current selected tab number -->
        <input type="hidden" name="tabOpt" id="tabOpt"
               value="<?php echo(htmlspecialchars(Request::GetClean('tabNumb')) ?? ''); ?>"/>
        <input type="hidden" name="loadedTabOpts" id="loadedTabOpts"
               value="<?php echo implode(',', $loadTabsArray); ?>"/>
        <?php //print_r($loadTabsArray);?>
        <?php require 'clientRegForm.php'; ?>
    </form>
    <?php
}
if ($userRole == 'Client') {
} else  if ($AEUserType == 'PLO'){
if ($tabNumb == 2) {
?>

<?php
if (($PLOPaymentGateway == 'Free') || ($clientAllowFreeLMR == 1)) {
?>
<form name="paymentInfoForm" id="paymentInfoForm" class="clientRegForm" method="POST"
      action="<?php echo CONST_URL_BOSSL; ?>submitFreeMembership.php">
    <?php //print_r($loadTabsArray);?>
    <?php
    } else if ($PLOPaymentGateway == 'Paypal') {
    ?>
    <form name="paymentInfoForm" id="paymentInfoForm" class="clientRegForm" target="_parent" method="POST"
          action="https://www.paypal.com/cgi-bin/webscr">
        <?php //print_r($loadTabsArray);?>
        <?php
        } else {
        ?>
        <form name="paymentInfoForm" id="paymentInfoForm" method="POST"
              action="<?php echo CONST_URL_BOSSL; ?>DIYCreditInfoSave.php">
            <?php //print_r($loadTabsArray);?>
            <?php
            }
            ?>
            <input type="hidden" name="executiveId" id="executiveId"
                   value="<?php echo cypher::myEncryption($selExecutiveId) ?>"/>
            <input type="hidden" name="encClientId" id="encClientId"
                   value="<?php echo cypher::myEncryption($selClientId) ?>"/>

            <?php
            if ($PLOPaymentGateway == 'Paypal') {
                if ($referralSiteCode == '394502') {
                    ?>
                    <input type="hidden" name="cmd" value="_s-xclick">
                    <input type="hidden" name="hosted_button_id" value="6328285">
                    <?php
                } else if ($referralSiteCode == '401709') {
                    ?>
                    <input type="hidden" name="cmd" value="_s-xclick">
                    <input type="hidden" name="hosted_button_id" value="8944181">
                    <?php
                } else {
                    if (isset(${'DIYDescription_' . $referralSiteCode})) {
                    } else {
                        ${'DIYDescription_' . $referralSiteCode} = $DIYDescription;
                    }
                    ?>

                    <input type="hidden" name="membership" id="membership" value="">
                    <input type="hidden" name="trackingId" id="trackingId" value=""/>
                    <input type="hidden" name="cmd" value="_xclick">
                    <input type="hidden" name="business" value="<?php echo $LMRPaypalMemberEmail ?>">
                    <input type="hidden" name="item_name"
                           value="<?php echo ${'DIYDescription_' . $referralSiteCode} ?>">
                    <input type="hidden" name="memberId" value="<?php echo cypher::myEncryption($selClientId) ?>">
                    <input type="hidden" name="currency_code" value="USD">
                    <input type="hidden" name="amount" id="amount" value="$0"/>
                    <input type="hidden" name="tabOpt" id="tabOpt" value=""/>
                    <input type="hidden" name="loadedTabOpts" id="loadedTabOpts"
                           value="<?php echo implode(',', $loadTabsArray); ?>"/>
                    <?php
                }
                ?>
                <input type="hidden" name="rm" value="2">
                <input type="hidden" name="return"
                       value="<?php echo CONST_URL_BOSSL; ?>PaypalProcess.php?cId=<?php echo cypher::myEncryption($selClientId) ?>&enrc=<?php echo cypher::myEncryption($referralSiteCode) ?>&enEId=<?php echo cypher::myEncryption($selExecutiveId) ?>&lIUser=<?php echo cypher::myEncryption($loggedInUser) ?>">
                <input type="hidden" name="cancel_return"
                       value="<?php echo CONST_URL_BOSSL; ?>PaypalProcess.php?cId=<?php echo cypher::myEncryption($selClientId) ?>&enrc=<?php echo cypher::myEncryption($referralSiteCode) ?>&enEId=<?php echo cypher::myEncryption($selExecutiveId) ?>&lIUser=<?php echo cypher::myEncryption($loggedInUser) ?>">
                <?php
            }
            ?>
            <?php require 'clientPaymentForm.php'; ?>
        </form>
        <?php
        }
        }
        if ($tabNumb == 3) {
            $includedFormUrl = 'PCClientBackgroundInfoForm.php';
            $saveUrl = '../backoffice/PCClientBackgroundInfoSave.php';
        } else if ($tabNumb == 4) {
            $includedFormUrl = 'PCClientEntityInfoForm.php';
            $saveUrl = '../pops/PCClientEntityInfoSave.php';
        } else if ($tabNumb == 5) {
            /* Borrower Experience Information */
            $includedFormUrl = 'borrowerExperience.php';
            $saveUrl = '../backoffice/PCClientExperienceInfoSave.php';
            $allowToEdit = 1;
            $clientDocsInfo = [];
            $clientSellExpInfo = [];
            $clientExperienceInfo = [];
            $propertyTypeKeyArray = [];
            $clientExpProInfo = [];
            $clientGUExpInfo = [];
            $clientDocsArray = $geographicAreas = [];

            $haveBorREInvestmentExperience = $borNoOfREPropertiesCompleted = $haveBorRehabConstructionExperience = $borNoOfYearRehabExperience = $borRehabPropCompleted = '';
            $haveBorProjectCurrentlyInProgress = $borNoOfProjectCurrently = $haveBorOwnInvestmentProperties = $borNoOfOwnProp = $areBorMemberOfInvestmentClub = '';
            $borClubName = '';
            $borNoOfFlippingExperience = '';
            $haveBorProfLicences = '';
            $borProfLicence = '';
            $HMLOLoanInfoSectionsDisp = '';

            $amountOfFinancing = $amountOfFinancingTo = $typicalPurchasePrice = $typicalPurchasePriceTo = $typicalConstructionCosts = $typicalConstructionCostsTo = '';
            $typicalSalePrice = $typicalSalePriceTo = $constructionDrawsPerProject = $constructionDrawsPerProjectTo = $monthsPurchaseDateToFirstConst = '';
            $monthsPurchaseDateToFirstConstTo = $monthsPurchaseDateUntilConst = $monthsPurchaseDateUntilConstTo = $monthsPurchaseDateToSaleDate = $monthsPurchaseDateToSaleDateTo = $NoOfSuchProjects = $NoOfSuchProjectsTo = '';

            $showREInvestmentDispOpt = 'display : none';
            $showOwnInvestmentDispOpt = 'display : none';
            $showProjectInProgressDispOpt = 'display : none';
            $showRCDispOpt = 'display : none';
            $showMemberDispOpt = 'display : none';
            $showProjectInProgressDispOpt = 'display : none';
            $liquidAssets = $areBuilderDeveloper = '';
            $doYouHireGC = '';
            $showProfLicencesOpt = $haveBorSellPropertieDisp = 'display : none';
            $borPriInvesStrategyDispOpt = 'display : none';

            $haveBorSellPropertie = '';
            $borNoOfProSellExperience = '';
            $borNoOfProSellCompleted = '';
            $haveCoBorSellPropertie = '';
            $coBorNoOfProSellExperience = '';
            $coBorNoOfProSellCompleted = '';
            $coBorSellAddress1 = '';
            $coBorSellOutcome1 = '';
            $proofOfSale13 = '';
            $coBorSellAddress2 = '';
            $coBorSellOutcome2 = '';
            $proofOfSale14 = '';
            $coBorSellAddress3 = '';
            $coBorSellOutcome3 = '';
            $proofOfSale15 = '';

            if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);

            if ($selClientId > 0) {
                $resultArray = getPCClientExperienceInfo::getReport(['PCID' => $PCID, 'CID' => $selClientId]);

                if (array_key_exists('clientFlipExpInfo', $resultArray)) $clientExpProInfo = $resultArray['clientFlipExpInfo'];
                if (array_key_exists('clientGUExpInfo', $resultArray)) $clientGUExpInfo = $resultArray['clientGUExpInfo'];
                if (array_key_exists('clientSellExpInfo', $resultArray)) $clientSellExpInfo = $resultArray['clientSellExpInfo'];
                if (array_key_exists('clientBackExpInfo', $resultArray)) $clientExperienceInfo = $resultArray['clientBackExpInfo'][0];
                if (array_key_exists('ClientDocs', $resultArray)) $clientDocsInfo = $resultArray['ClientDocs'];

                for ($cd = 0; $cd < count($clientDocsInfo); $cd++) {
                    $dc = '';
                    $dc = $clientDocsInfo[$cd]['docCategory'];
                    $clientDocsArray[$dc] = $clientDocsInfo[$cd];
                }

                if (count($clientExperienceInfo) > 0) {
                    $haveBorREInvestmentExperience = trim($clientExperienceInfo['haveBorREInvestmentExperience']);
                    $borNoOfREPropertiesCompleted = trim($clientExperienceInfo['borNoOfREPropertiesCompleted']);
                    $haveBorRehabConstructionExperience = trim($clientExperienceInfo['haveBorRehabConstructionExperience']);
                    $borNoOfYearRehabExperience = trim($clientExperienceInfo['borNoOfYearRehabExperience']);
                    $borRehabPropCompleted = trim($clientExperienceInfo['borRehabPropCompleted']);
                    $haveBorProjectCurrentlyInProgress = trim($clientExperienceInfo['haveBorProjectCurrentlyInProgress']);
                    $borNoOfProjectCurrently = trim($clientExperienceInfo['borNoOfProjectCurrently']);
                    $haveBorOwnInvestmentProperties = trim($clientExperienceInfo['haveBorOwnInvestmentProperties']);
                    $borNoOfOwnProp = trim($clientExperienceInfo['borNoOfOwnProp']);
                    $areBorMemberOfInvestmentClub = trim($clientExperienceInfo['areBorMemberOfInvestmentClub']);
                    $borClubName = trim($clientExperienceInfo['borClubName']);
                    $borNoOfFlippingExperience = trim($clientExperienceInfo['borNoOfFlippingExperience']);
                    $liquidAssets = trim($clientExperienceInfo['liquidAssets']);
                    $haveBorProfLicences = trim($clientExperienceInfo['haveBorProfLicences']);
                    $borProfLicence = trim($clientExperienceInfo['borProfLicence']);
                    $fullTimeRealEstateInvestor = trim($clientExperienceInfo['fullTimeRealEstateInvestor']);
                    $areBuilderDeveloper = trim($clientExperienceInfo['areBuilderDeveloper']);
                    $doYouHireGC = trim($clientExperienceInfo['doYouHireGC']);
                    $borLicenseNo = trim($clientExperienceInfo['borLicenseNo']);

                    $borPrimaryInvestmentStrategy = trim($clientExperienceInfo['borPrimaryInvestmentStrategy']);
                    $borPrimaryInvestmentStrategyExplain = trim($clientExperienceInfo['borPrimaryInvestmentStrategyExplain']);
                    $amountOfFinancing = trim($clientExperienceInfo['amountOfFinancing']);
                    $amountOfFinancingTo = trim($clientExperienceInfo['amountOfFinancingTo']);
                    $typicalPurchasePrice = trim($clientExperienceInfo['typicalPurchasePrice']);
                    $typicalPurchasePriceTo = trim($clientExperienceInfo['typicalPurchasePriceTo']);
                    $typicalConstructionCosts = trim($clientExperienceInfo['typicalConstructionCosts']);
                    $typicalConstructionCostsTo = trim($clientExperienceInfo['typicalConstructionCostsTo']);
                    $typicalSalePrice = trim($clientExperienceInfo['typicalSalePrice']);
                    $typicalSalePriceTo = trim($clientExperienceInfo['typicalSalePriceTo']);

                    $constructionDrawsPerProject = trim($clientExperienceInfo['constructionDrawsPerProject']);
                    $constructionDrawsPerProjectTo = trim($clientExperienceInfo['constructionDrawsPerProjectTo']);
                    $monthsPurchaseDateToFirstConst = trim($clientExperienceInfo['monthsPurchaseDateToFirstConst']);
                    $monthsPurchaseDateToFirstConstTo = trim($clientExperienceInfo['monthsPurchaseDateToFirstConstTo']);
                    $monthsPurchaseDateUntilConst = trim($clientExperienceInfo['monthsPurchaseDateUntilConst']);
                    $monthsPurchaseDateUntilConstTo = trim($clientExperienceInfo['monthsPurchaseDateUntilConstTo']);
                    $monthsPurchaseDateToSaleDate = trim($clientExperienceInfo['monthsPurchaseDateToSaleDate']);
                    $monthsPurchaseDateToSaleDateTo = trim($clientExperienceInfo['monthsPurchaseDateToSaleDateTo']);
                    $NoOfSuchProjects = trim($clientExperienceInfo['NoOfSuchProjects']);
                    $NoOfSuchProjectsTo = trim($clientExperienceInfo['NoOfSuchProjectsTo']);

                    $haveBorSellPropertie = trim($clientExperienceInfo['haveBorSellPropertie']);
                    $borNoOfProSellExperience = trim($clientExperienceInfo['borNoOfProSellExperience']);
                    $borNoOfProSellCompleted = trim($clientExperienceInfo['borNoOfProSellCompleted']);

                    $flipPropCompletedLifetime = trim($clientExperienceInfo['flipPropCompletedLifetime']);
                    $groundPropCompletedLifetime = trim($clientExperienceInfo['groundPropCompletedLifetime']);
                    $sellPropCompletedLifetime = trim($clientExperienceInfo['sellPropCompletedLifetime']);
                    $overallRealEstateInvesExp = trim($clientExperienceInfo['overallRealEstateInvesExp']);
                    $trackRecord = $clientExperienceInfo['trackRecord'];


                    if ($borPrimaryInvestmentStrategy != '') $borPriInvesStrategyArray = explode(',', $borPrimaryInvestmentStrategy);
                    if (trim($clientExperienceInfo['geographicAreas']) != '') $geographicAreas = explode(',', trim($clientExperienceInfo['geographicAreas']));

                }

                if ($haveBorREInvestmentExperience == 'Yes') $showREInvestmentDispOpt = 'display: block;';
                if ($haveBorSellPropertie == 'Yes') $haveBorSellPropertieDisp = 'display: block;';
                if ($haveBorRehabConstructionExperience == 'Yes') $showRCDispOpt = 'display:block;';
                if ($haveBorProjectCurrentlyInProgress == 'Yes') $showProjectInProgressDispOpt = 'display: block;';
                if ($haveBorOwnInvestmentProperties == 'Yes') $showOwnInvestmentDispOpt = 'display:block;';
                if ($areBorMemberOfInvestmentClub == 'Yes') $showMemberDispOpt = 'display:block;';
                if ($haveBorProfLicences == 'Yes') $showProfLicencesOpt = 'display:block;';
                if (in_array('Other', $borPriInvesStrategyArray)) $borPriInvesStrategyDispOpt = 'display:block;';
            }

        } else if ($tabNumb == 6) {
            $includedFormUrl = '../backoffice/assetsForm.php';
            $saveUrl = '../backoffice/PCClientRealEstateInfoSave.php';
            /* Assets Information */
            $borrowerAssetsSRE = [];
            $fileLOScheduleRealInfo = [];
            $clientAssetsInfo = [];
            $CLOSRID = '';
            $encCID = cypher::myEncryption($selClientId);
            $networthOfBusinessOwned = 0;

            $activeTab = '';
            $LMRId = 0;
            $propertyDescDivDisplay = 'display : none';

            if ($selClientId > 0 && $PCID > 0) {
                $borrowerAssetsSRE = getPCClientRealEstateInfo::getReport(['CLOSRID' => $CLOSRID, 'CID' => $selClientId]);
                if (array_key_exists('realEstateInfo', $borrowerAssetsSRE)) $fileLOScheduleRealInfo = $borrowerAssetsSRE['realEstateInfo'];
                if (array_key_exists('clientAssetsInfo', $borrowerAssetsSRE)) {
                    if (array_key_exists(0, $borrowerAssetsSRE['clientAssetsInfo'])) $clientAssetsInfo = $borrowerAssetsSRE['clientAssetsInfo'][0];
                }
                if (count($clientAssetsInfo) > 0) {
                    $assetCheckingAccounts = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetCheckingAccounts');
                    $assetSavingMoneyMarket = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetSavingMoneyMarket');
                    $assetStocks = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetStocks');
                    $assetIRAAccounts = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetIRAAccounts');
                    $assetESPOAccounts = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetESPOAccounts');
                    $assetHome = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetHome');
                    $assetORE = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetORE');
                    $assetCars = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetCars');
                    $assetLifeInsurance = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetLifeInsurance');
                    $assetTotalCashBankAcc = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetTotalCashBankAcc');
                    $assetTotalRetirementValue = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetTotalRetirementValue');
                    $assetAvailabilityLinesCredit = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetAvailabilityLinesCredit');
                    $assetSR = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetSR');
                    $assetSROwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetSROwed');
                    $assetOther = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetOther');
                    $assetCash = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetCash');
                    $assetHomeOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetHomeOwed');
                    $assetOREOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetOREOwed');
                    $assetCarsOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetCarsOwed');
                    $otherAmtOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'otherAmtOwed');
                    $networthOfBusinessOwned = checkFieldAccess($assetArray, $clientAssetsInfo, 'networthOfBusinessOwned');
                    $networthOfBusinessOwned = !empty($networthOfBusinessOwned) ? Currency::formatDollarAmountWithDecimal($networthOfBusinessOwned) : '';
                    $otherDescription = !empty($clientAssetsInfo['otherDesc']) ? $clientAssetsInfo['otherDesc'] : '';
                    $vestedInterest = checkFieldAccess($assetArray, $clientAssetsInfo, 'vestedInterest');
                    $automobilesOwned3x = checkFieldAccess($assetArray, $clientAssetsInfo, 'automobilesOwned3x');
                    $automobilesOwned3x1 = checkFieldAccess($assetArray, $clientAssetsInfo, 'automobilesOwned3x1');
                    $otherAssets = checkFieldAccess($assetArray, $clientAssetsInfo, 'otherAssets');

                    $assetAccount = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetAccount');
                    $assetAccountOwd = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetAccountOwd');
                    $assetStocksOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetStocksOwed');
                    $assetNonMarketableSecurities = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetNonMarketableSecurities');
                    $assetNonMarketableSecuritiesOwd = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetNonMarketableSecuritiesOwd');
                    $assetIRAAccountsOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetIRAAccountsOwed');
                    $assetESPOAccountsOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetESPOAccountsOwed');
                    $assetLifeInsuranceOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetLifeInsuranceOwed');
                    $assetAvailabilityLinesCreditOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetAvailabilityLinesCreditOwed');
                    $notesPayableToBanksOthersOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'notesPayableToBanksOthersOwed');
                    $installmentAccountOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'installmentAccountOwed');
                    $revolvingDebtOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'revolvingDebtOwed');
                    $unpaidPayableTaxesOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'unpaidPayableTaxesOwed');
                    $otherLiabilitiesOwed = checkFieldAccess($assetArray, $clientAssetsInfo, 'otherLiabilitiesOwed');
                    $unpaidPayableTaxesDesc = checkFieldAccess($assetArray, $clientAssetsInfo, 'unpaidPayableTaxesDesc', 'string');
                    $otherLiabilityDetails = checkFieldAccess($assetArray, $clientAssetsInfo, 'otherLiabilityDetails', 'string');

                    $assetSecNotesOwd = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetSecNotesOwd');
                    $assetUnsecNotesOwd = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetUnsecNotesOwd');
                    $assetAcctPayableOwd = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetAcctPayableOwd');
                    $assetMarginOwd = checkFieldAccess($assetArray, $clientAssetsInfo, 'assetMarginOwd');
                    $totalVerifiedAssets = $clientAssetsInfo['totalVerifiedAssets'];
                }

                $totalAssets = Strings::replaceCommaValues($assetNonMarketableSecurities) + Strings::replaceCommaValues($assetAccount) + Strings::replaceCommaValues($assetCheckingAccounts) + Strings::replaceCommaValues($assetSavingMoneyMarket);
                $totalAssets += Strings::replaceCommaValues($assetStocks) + Strings::replaceCommaValues($assetIRAAccounts);
                $totalAssets += Strings::replaceCommaValues($assetESPOAccounts) + Strings::replaceCommaValues($assetHome);
                $totalAssets += Strings::replaceCommaValues($assetORE) + Strings::replaceCommaValues($assetSR) + Strings::replaceCommaValues($assetCars);
                $totalAssets += Strings::replaceCommaValues($assetLifeInsurance) + Strings::replaceCommaValues($assetOther);
                $totalAssets += Strings::replaceCommaValues($assetTotalCashBankAcc) + Strings::replaceCommaValues($assetTotalRetirementValue) + Strings::replaceCommaValues($assetAvailabilityLinesCredit) + Strings::replaceCommaValues($otherAssets);
                $totalAssets += Strings::replaceCommaValues($assetCash) + Strings::replaceCommaValues($networthOfBusinessOwned) + Strings::replaceCommaValues($vestedInterest);

                $totalAssets = round($totalAssets, 2);
                $totalAssetsOwed += Strings::replaceCommaValues($assetIRAAccountsOwed)
                    + Strings::replaceCommaValues($assetNonMarketableSecuritiesOwd)
                    + Strings::replaceCommaValues($assetStocksOwed)
                    + Strings::replaceCommaValues($assetAccountOwd)
                    + Strings::replaceCommaValues($assetHomeOwed);
                $totalAssetsOwed += Strings::replaceCommaValues($assetLifeInsuranceOwed) + Strings::replaceCommaValues($assetESPOAccountsOwed) + Strings::replaceCommaValues($assetOREOwed) + Strings::replaceCommaValues($assetSROwed) + Strings::replaceCommaValues($assetCarsOwed) + Strings::replaceCommaValues($otherAmtOwed);
                $totalAssetsOwed += Strings::replaceCommaValues($otherLiabilitiesOwed) + Strings::replaceCommaValues($unpaidPayableTaxesOwed) + Strings::replaceCommaValues($revolvingDebtOwed) + Strings::replaceCommaValues($installmentAccountOwed) + Strings::replaceCommaValues($notesPayableToBanksOthersOwed) + Strings::replaceCommaValues($assetAvailabilityLinesCreditOwed);
                $totalAssetsOwed += Strings::replaceCommaValues($assetSecNotesOwd) + Strings::replaceCommaValues($assetUnsecNotesOwd) + Strings::replaceCommaValues($assetAcctPayableOwd) + Strings::replaceCommaValues($assetMarginOwd);
                $totalAssetsOwed = round($totalAssetsOwed, 2);
                $totalAssetsNetValue = Strings::replaceCommaValues($totalAssets) - Strings::replaceCommaValues($totalAssetsOwed);
                $totalAssetsNetValue = round($totalAssetsNetValue, 2);
                $totalAutoMobiles = Strings::replaceCommaValues($automobilesOwned3x) + Strings::replaceCommaValues($automobilesOwned3x1);

                $totalSumOfAll = Strings::replaceCommaValues($assetCash)
                    + Strings::replaceCommaValues($vestedInterest)
                    + Strings::replaceCommaValues($networthOfBusinessOwned)
                    + Strings::replaceCommaValues($assetStocks)
                    + Strings::replaceCommaValues($assetCarsOwed)
                    + Strings::replaceCommaValues($automobilesOwned1)
                    + Strings::replaceCommaValues($assetLifeInsurance)
                    + Strings::replaceCommaValues($assetTotalRetirementValue)
                    + Strings::replaceCommaValues($assetAvailabilityLinesCredit)
                    + Strings::replaceCommaValues($faceAmount)
                    + Strings::replaceCommaValues($assetOther)
                    + Strings::replaceCommaValues($assetSR)
                    + Strings::replaceCommaValues($assetORE)
                    + Strings::replaceCommaValues($assetCars)
                    + Strings::replaceCommaValues($otherAmtOwed)
                    + Strings::replaceCommaValues($assetTotalCashBankAcc)
                    + Strings::replaceCommaValues($otherAssets)
                    + Strings::replaceCommaValues($assetSecNotesOwd)
                    + Strings::replaceCommaValues($assetUnsecNotesOwd)
                    + Strings::replaceCommaValues($assetAcctPayableOwd)
                    + Strings::replaceCommaValues($assetMarginOwd);
            }
        }

        if ($tabNumb == 3 || $tabNumb == 5 || $tabNumb == 6) {
        ?>
        <?php if ($tabNumb == 6) {
        ?>
        <form name="loanModForm" id="loanModForm" class="clientRegForm" method="POST"
              action="<?php echo $saveUrl; ?>" enctype="multipart/form-data">
            <?php //print_r($loadTabsArray);?>
            <?php
            } else {
            ?>
            <form name="clientRegForm" id="clientRegForm" class="clientRegForm" method="POST"
                  action="<?php echo $saveUrl; ?>" enctype="multipart/form-data">
                <?php //print_r($loadTabsArray);?>

                <?php
                }
                ?>
                <input type="hidden" name="CID" id="CID" value="<?php echo cypher::myEncryption($selClientId) ?>"/>
                <input type="hidden" name="PCID" id="PCID" value="<?php echo cypher::myEncryption($PCID) ?>"/>
                <!-- Collects user's current selected tab number -->
                <input type="hidden" name="loadedTabOpts" id="loadedTabOpts"
                       value="<?php echo implode(',', $loadTabsArray); ?>"/>
                <input type="hidden" name="tabOpt" id="tabOpt"
                       value="<?php echo(htmlspecialchars(Request::GetClean('tabNumb')) ?? ''); ?>"/>
                <input type="hidden" name="executiveId" id="executiveId"
                       value="<?php echo cypher::myEncryption($selExecutiveId) ?>"/>
                <input type="hidden" name="encClientId" id="encClientId"
                       value="<?php echo cypher::myEncryption($selClientId) ?>"/>
                <input type="hidden" name="userRole" id="userRole"
                       value="<?php echo cypher::myEncryption($userRole) ?>"/>
                <input type="hidden" name="isClient" id="isClient" value="1"/>
                <?php
                include $includedFormUrl;

                if ($tabNumb == 6) {

                    $encrypCID = cypher::myEncryption($selClientId);
                    $secArr = Arrays::getValueFromArray('SORE', $fieldsInfo); // Section Array
                    ?>
                    <div class="card card-custom SORESection SORE <?php if (count($secArr) <= 0) {
                        echo 'secHide';
                    } ?>" style="<?php echo $showSOREDispOpt; ?>">
                        <?php
                        if ($allowToEdit) { ?>
                            <div class="card-header card-header-tabs-line bg-gray-100  ">
                                <div class="card-title">
                                    <h3 class="card-label">
                                        <?php echo BaseHTML::getSectionHeading('SORE'); ?>
                                    </h3>
                                    <?php if (trim(BaseHTML::getSectionTooltip('SORE')) != '') { ?>&nbsp;
                                        <i class="popoverClass fas fa-info-circle text-primary "
                                           data-html="true"
                                           data-content="<?php echo BaseHTML::getSectionTooltip('SORE'); ?>"></i>
                                    <?php } ?>
                                </div>
                                <div class="card-toolbar ">
                                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                                       data-card-tool="toggle"
                                       data-toggle="tooltip" data-placement="top" title=""
                                       data-original-title="Toggle Card">
                                        <i class="ki ki-arrow-down icon-nm"></i>
                                    </a>
                                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                                       data-card-tool="reload"
                                       data-toggle="tooltip" data-placement="top" title=""
                                       data-original-title="Reload Card">
                                        <i class="ki ki-reload icon-nm"></i>
                                    </a>
                                    <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                                       data-card-tool="remove"
                                       data-toggle="tooltip" data-placement="top" title=""
                                       data-original-title="Remove Card">
                                        <i class="ki ki-close icon-nm"></i>
                                    </a>
                                    <a href="javascript:realEstateInfoPopup('','');" class="btn btn-primary btn-sm"> Add
                                        New
                                    </a>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="card-body">
                            <div id="showLORealEstateInfo">

                                <?php
                                $k = 1;
                                if (!$isClientProfile) {
                                    $encLMRId = cypher::myEncryption($LMRId);
                                }
                                for ($d = 0; $d < count($fileLOScheduleRealInfo); $d++) {
                                    $encSID = '';
                                    $encLMRId = '';
                                    if ($isClientProfile) {
                                        $LMRId = '';
                                    }
                                    $cls = $realEstateSectionID = $realEstateSectionIDFile = '';
                                    $schedulePropAddr = $schedulePropCity = $schedulePropState = $schedulePropZip = '';
                                    $scheduleStatus = '';
                                    $propType = $propertyDesc = '';
                                    $presentMarketValue = '';
                                    $amountOfMortgages = '';
                                    $netRentalIncome = '';
                                    $CLOSRID = $LOSRID = 0;
                                    $grossRentalIncome = '';
                                    $mortgagePayments = '';
                                    $insMaintTaxMisc = '';
                                    $encCLOSRID = $encLOSRID = '';
                                    if ((($d + 1) % 2) == 0) $cls = 'even';
                                    $propTypeAns = '';

                                    $propertyTypeNumbBarrnetArray = [1 => 'Single Family Home', 6 => 'Duplex', 7 => '3 Units', 8 => '4 Units', 11 => 'Condo', 48 => 'Town Home', 37 => 'Multi Family+5', 34 => 'Mixed use', 39 => 'Retail', 40 => 'Office', 42 => 'Industrial', 10 => 'land', 47 => 'Other'];

                                    $schedulePropAddr = $fileLOScheduleRealInfo[$d]['schedulePropAddr'];
                                    $schedulePropCity = $fileLOScheduleRealInfo[$d]['schedulePropCity'];
                                    $schedulePropState = $fileLOScheduleRealInfo[$d]['schedulePropState'];
                                    $schedulePropZip = $fileLOScheduleRealInfo[$d]['schedulePropZip'];
                                    $scheduleStatus = $fileLOScheduleRealInfo[$d]['scheduleStatus'];
                                    $propType = $fileLOScheduleRealInfo[$d]['propType'];
                                    $propertyDesc = $fileLOScheduleRealInfo[$d]['propertyDesc'];
                                    $presentMarketValue = $fileLOScheduleRealInfo[$d]['presentMarketValue'];
                                    $amountOfMortgages = $fileLOScheduleRealInfo[$d]['amountOfMortgages'];
                                    $grossRentalIncome = $fileLOScheduleRealInfo[$d]['grossRentalIncome'];
                                    $mortgagePayments = $fileLOScheduleRealInfo[$d]['mortgagePayments'];
                                    $insMaintTaxMisc = $fileLOScheduleRealInfo[$d]['insMaintTaxMisc'];
                                    $netRentalIncome = $fileLOScheduleRealInfo[$d]['netRentalIncome'];
                                    $titledUnder = $fileLOScheduleRealInfo[$d]['titledUnder'];
                                    $datePurchased = $fileLOScheduleRealInfo[$d]['datePurchased'];
                                    $salesDate = $fileLOScheduleRealInfo[$d]['salesDate'];
                                    $salesPrice = $fileLOScheduleRealInfo[$d]['salesPrice'];
                                    $purchasePrice = $fileLOScheduleRealInfo[$d]['purchasePrice'];
                                    $valueofImprovementsMade = $fileLOScheduleRealInfo[$d]['valueofImprovementsMade'];
                                    $intendedOccupancy = $fileLOScheduleRealInfo[$d]['intendedOccupancy'];
                                    $anyMortgagesLiens = $fileLOScheduleRealInfo[$d]['anyMortgagesLiens'];
                                    $creditorName = $fileLOScheduleRealInfo[$d]['creditorName'];
                                    $accountNumber = $fileLOScheduleRealInfo[$d]['accountNumber'];
                                    $loanStatus = $fileLOScheduleRealInfo[$d]['loanStatus'];
                                    $unpaidBalance = $fileLOScheduleRealInfo[$d]['unpaidBalance'];
                                    $monthlyPayment = $fileLOScheduleRealInfo[$d]['monthlyPayment'];
                                    $mortgageType = $fileLOScheduleRealInfo[$d]['mortgageType'];
                                    $creditLimit = $fileLOScheduleRealInfo[$d]['creditLimit'];
                                    $anyOtherMortgagesLiens = $fileLOScheduleRealInfo[$d]['anyOtherMortgagesLiens'];
                                    $creditorNameAnother = $fileLOScheduleRealInfo[$d]['creditorNameAnother'];
                                    $accountNumberAnother = $fileLOScheduleRealInfo[$d]['accountNumberAnother'];
                                    $loanStatusAnother = $fileLOScheduleRealInfo[$d]['loanStatusAnother'];
                                    $unpaidBalanceAnother = $fileLOScheduleRealInfo[$d]['unpaidBalanceAnother'];
                                    $monthlyPaymentAnother = $fileLOScheduleRealInfo[$d]['monthlyPaymentAnother'];
                                    $mortgageTypeAnother = $fileLOScheduleRealInfo[$d]['typeAnother'];
                                    $creditLimitAnother = $fileLOScheduleRealInfo[$d]['creditLimitAnother'];
                                    $maturityDateSchedule = $fileLOScheduleRealInfo[$d]['maturityDateSchedule'];
                                    $maturityDateAnother = $fileLOScheduleRealInfo[$d]['maturityDateAnother'];
                                    $ownership = $fileLOScheduleRealInfo[$d]['ownership'];
                                    $scheduleInvestType = $fileLOScheduleRealInfo[$d]['scheduleInvestType'];
                                    if (array_key_exists($propType, GpropertyTypeNumbArray::$GpropertyTypeNumbArray)) {
                                        $propTypeAns = GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propType];
                                    }
                                    if (array_key_exists('CLOSRID', $fileLOScheduleRealInfo[$d])) {
                                        $CLOSRID = $fileLOScheduleRealInfo[$d]['CLOSRID'];
                                    }
                                    if (array_key_exists('CLOSRID', $fileLOScheduleRealInfo[$d])) {
                                        $CID = $fileLOScheduleRealInfo[$d]['CID'];
                                    }

                                    if (array_key_exists('LOSRID', $fileLOScheduleRealInfo[$d])) {
                                        $LOSRID = $fileLOScheduleRealInfo[$d]['LOSRID'];
                                    }
                                    if (array_key_exists('fileID', $fileLOScheduleRealInfo[$d])) {
                                        $LMRId = $fileLOScheduleRealInfo[$d]['fileID'];
                                    }
                                    $encSID = cypher::myEncryption($LOSRID);
                                    $encLMRId = cypher::myEncryption($LMRId);
                                    $encLOSRID = cypher::myEncryption($LOSRID);

                                    $encCLOSRID = cypher::myEncryption($CLOSRID);
                                    if ($LMRId > 0) {
                                        $realEstateSectionID = 'realEstateInfoDivId_' . $LOSRID;
                                    } else {
                                        $realEstateSectionID = 'realEstateInfoDivId_' . $CLOSRID;
                                    }
                                    ?>
                                    <div class="card card-custom mb-2" id="<?php echo $realEstateSectionID ?>">
                                        <div class="card-header bg-primary-o-40">
                                            <div class="card-title">
                                                <h3 class="card-label realInfoCnt">    <?php
                                                    $sreHeading = '';
                                                    if ($schedulePropAddr != '') {
                                                        $sreHeading .= $schedulePropAddr;
                                                    }
                                                    if ($schedulePropCity != '') {
                                                        $sreHeading .= ', ' . $schedulePropCity;
                                                    }
                                                    if ($schedulePropState != '') {
                                                        $sreHeading .= ', ' . Strings::convertState($schedulePropState);
                                                    }
                                                    echo $sreHeading; ?> </h3>
                                            </div>
                                            <div class="card-toolbar">
                                                <a href="#"
                                                   class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 divOpenClose"
                                                   data-toggle="tooltip" data-placement="top"
                                                   data-body-id="<?php echo 'body_' . $realEstateSectionID ?>"
                                                   title="Toggle Card">
                                                    <i class="ki ki-arrow-down icon-nm"></i>
                                                </a>
                                                <?php
                                                if ($isClientProfile) { ?>
                                                    <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 cursor-pointer"
                                                       data-toggle="tooltip" data-placement="top" onclick="deleteClientLOScheduleRealInfo('<?php echo $encCID ?>','<?php echo $encCLOSRID ?>','<?php echo $realEstateSectionID ?>', 'Client');"
                                                       title="Click To Delete">
                                                        <i class="fas fa-trash-alt  "></i>
                                                    </a>
                                                    <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 cursor-pointer"
                                                       data-placement="top" onclick="realEstateInfoPopup('<?php echo $encCID; ?>', '<?php echo $encCLOSRID; ?>', 'Client');"
                                                       title="Click to edit client entity Info">
                                                        <i class="fas fa-pencil-alt"></i>
                                                    </a>
                                                <?php } else { ?>
                                                    <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 cursor-pointer"
                                                       data-toggle="tooltip" data-placement="top" onclick="deleteClientLOScheduleRealInfo('<?php echo $encLMRId ?>','<?php echo $encLOSRID ?>','<?php echo $realEstateSectionID ?>', 'File');"
                                                       title="Click To Delete">
                                                        <i class="fas fa-trash-alt "></i>
                                                    </a>
                                                    <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 cursor-pointer"
                                                       data-card-tool="reload" data-toggle="tooltip"
                                                       data-placement="top" onclick="realEstateInfoPopup('<?php echo $encLMRId; ?>', '<?php echo $encLOSRID; ?>', 'File');"
                                                       title="Click to edit client entity Info">
                                                        <i class="fas fa-pencil-alt"></i>
                                                    </a>
                                                <?php } ?>
                                            </div>
                                        </div>
                                        <div class="card-body" style="display: none;"
                                             id="<?php echo 'body_' . $realEstateSectionID ?>">
                                            <div class="row">
                                                <?php
                                                /*$fldArr = Arrays::getValueFromArray('schedulePropAddr', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('schedulePropAddr'); ?>">
                                    <span>Property Address : <span
                                                class="H5"><?php echo $schedulePropAddr; ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('schedulePropCity', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('schedulePropCity'); ?>">
                                    <span>Property City : <span
                                                class="H5"><?php echo $schedulePropCity; ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('schedulePropState', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('schedulePropState'); ?>">
                                    <span>Property State : <span
                                                class="H5"><?php echo $schedulePropState; ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('schedulePropZip', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('schedulePropZip'); ?>">
                                    <span>Property Zip : <span
                                                class="H5"><?php echo $schedulePropZip; ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('scheduleStatus', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('scheduleStatus'); ?>">
                                                    <span>Status : <span
                                                                class="H5"><?php echo $scheduleStatus; ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('propType', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('ownership'); ?>">
                                                    <span>Ownership : <span class="H5"><?php echo $ownership; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('propType'); ?>">
                                                    <span>Property Type : <span
                                                                class="H5"><?php echo $propTypeAns; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('scheduleInvestType'); ?>">
                                    <span>Investment Type : <span
                                                class="H5"><?php echo $scheduleInvestType; ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('presentMarketValue', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('presentMarketValue'); ?>">
                                    <span>Present Market Value : <span
                                                class="H5"><?php echo $presentMarketValue; ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('amountOfMortgages', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('amountOfMortgages'); ?>">
                                    <span>Balance of Mortgages &amp; Liens : <span
                                                class="H5"><?php echo Currency::formatDollarAmountWithDecimal($amountOfMortgages); ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('grossRentalIncome', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('grossRentalIncome'); ?>">
                                    <span>Gross Rental Income : <span
                                                class="H5"><?php echo Currency::formatDollarAmountWithDecimal($grossRentalIncome); ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('mortgagePayments', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('mortgagePayments'); ?>">
                                    <span>Mortgage Payments : <span
                                                class="H5"><?php echo Currency::formatDollarAmountWithDecimal($mortgagePayments); ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('insMaintTaxMisc', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('insMaintTaxMisc'); ?>">
                                    <span>Insurance, Taxes, HOA (If Not Included in Payment) : <span
                                                class="H5"><?php echo Currency::formatDollarAmountWithDecimal($insMaintTaxMisc); ?></span></span>
                                                </div>
                                                <?php
                                                /*}
                                                $fldArr = Arrays::getValueFromArray('netRentalIncome', $secArr); // Field Array
                                                if ( checkDisplay($fldArr, $fileTab) ) {*/
                                                ?>
                                                <div class="col-md-4  <?php echo loanForm::showField('netRentalIncome'); ?>">
                                    <span>Net Rental Income : <span
                                                class="H5"><?php echo Currency::formatDollarAmountWithDecimal($netRentalIncome); ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('titledUnder'); ?>">
                                    <span>Titled Under : <span
                                                class="H5"><?php echo $titledUnder; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('datePurchased'); ?>">
                                    <span>Date Purchased : <span
                                                class="H5"><?php echo $datePurchased; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('purchasePrice'); ?>">
                                    <span>Purchase Price : <span
                                                class="H5"><?php echo Currency::formatDollarAmountWithDecimal($purchasePrice); ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('valueofImprovementsMade'); ?>">
                                    <span>Value of Improvements Made : <span
                                                class="H5"><?php echo Currency::formatDollarAmountWithDecimal($valueofImprovementsMade); ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('intendedOccupancy'); ?>">
                                    <span>Intended Occupancy : <span
                                                class="H5"><?php echo $intendedOccupancy; ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-md-4  <?php echo loanForm::showField('salesDate'); ?>">
                                    <span>Sale Date : <span
                                                class="H5"><?php echo $salesDate; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('salesPrice'); ?>">
                                    <span>Sale Price : <span
                                                class="H5"><?php echo $salesPrice; ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-md-4  <?php echo loanForm::showField('anyMortgagesLiens'); ?>">
                                    <span>Are there any mortgages or liens? : <span
                                                class="H5"><?php echo $anyMortgagesLiens; ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-md-4  <?php echo loanForm::showField('creditorName'); ?>">
                                    <span>Creditor Name : <span
                                                class="H5"><?php echo $creditorName; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('accountNumber'); ?>">
                                    <span>Account Number : <span
                                                class="H5"><?php echo $accountNumber; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('loanStatus'); ?>">
                                    <span>Loan Status : <span
                                                class="H5"><?php echo $loanStatus; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('unpaidBalance'); ?>">
                                    <span>Unpaid Balance : <span
                                                class="H5"><?php echo $unpaidBalance; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('monthlyPayment'); ?>">
                                    <span>Monthly Payment : <span
                                                class="H5"><?php echo $monthlyPayment; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('type'); ?>">
                                    <span>Type : <span
                                                class="H5"><?php echo $mortgageType; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('creditLimit'); ?>">
                                    <span>Credit Limit : <span
                                                class="H5"><?php echo $creditLimit; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('maturityDateSchedule'); ?>">
                                    <span>Maturity Date : <span
                                                class="H5"><?php echo $maturityDateSchedule; ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-md-4  <?php echo loanForm::showField('anyOtherMortgagesLiens'); ?>">
                                    <span>Is their another mortgage or lien? : <span
                                                class="H5"><?php echo $anyOtherMortgagesLiens; ?></span></span>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-md-4  <?php echo loanForm::showField('creditorNameAnother'); ?>">
                                    <span>Creditor Name : <span
                                                class="H5"><?php echo $creditorNameAnother; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('accountNumberAnother'); ?>">
                                    <span>Account Number : <span
                                                class="H5"><?php echo $accountNumberAnother; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('loanStatusAnother'); ?>">
                                    <span>Loan Status : <span
                                                class="H5"><?php echo $loanStatusAnother; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('unpaidBalanceAnother'); ?>">
                                    <span>Unpaid Balance : <span
                                                class="H5"><?php echo $unpaidBalanceAnother; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('monthlyPaymentAnother'); ?>">
                                    <span>Monthly Payment : <span
                                                class="H5"><?php echo $monthlyPaymentAnother; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('typeAnother'); ?>">
                                    <span>Type : <span
                                                class="H5"><?php echo $mortgageTypeAnother; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('creditLimitAnother'); ?>">
                                    <span>Credit Limit : <span
                                                class="H5"><?php echo $creditLimitAnother; ?></span></span>
                                                </div>
                                                <div class="col-md-4  <?php echo loanForm::showField('maturityDateAnother'); ?>">
                                    <span>Maturity Date : <span
                                                class="H5"><?php echo $maturityDateAnother; ?></span></span>
                                                </div>
                                                <?php //} ?>
                                            </div>
                                        </div>
                                    </div>
                                    <script>
                                        //    var card = new KTCard('<?php //echo $realEstateSectionID ?>');
                                    </script>
                                    <?php
                                    $k++;
                                }
                                ?>
                            </div>
                        </div>
                    </div>

                    <script type="text/javascript">
                        function realEstateInfoPopup(encCID, encCLOSRID, Opt) {
                            clear_form_elements('realEstateInfoContent');
                            $('.anyMortgagesLiensDispOpt').css("display", "none");
                            $('.anyOtherMortgagesLiensDispOpt').css("display", "none");
                            $('.salesDispOpt').css("display", "none");
                            $('.mortgagepopInfo').css("display", "block");
                            $('.incomeValuespopInfo').css("display", "flex");
                            $('#encCLOSRID').val(encCLOSRID);
                            $('#LOSRID').val(encCLOSRID);
                            let fileLMRID = "<?php echo $encLMRId; ?>";
                            let fileCID = "<?php echo $encrypCID; ?>";
                            $("#anyMortgagesLiensNo").attr('checked', 'checked');
                            $("#anyOtherMortgagesLiensNo").attr('checked', 'checked');
                            $('#fileLMRId').val(fileLMRID);
                            $('#schCID').val(fileCID);
                            if (encCID != '') {
                                $.ajax({
                                    type: 'POST',
                                    url: '../backoffice/getRealEstateInfoPopup.php',
                                    data: jQuery.param({'encCID': encCID, 'encCLOSRID': encCLOSRID, 'Opt': Opt}),
                                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                                    success: function (myData) {
                                        let obj = $.parseJSON(myData);
                                        assignFieldValue(obj[0].schedulePropAddr, 'schedulePropAddr');
                                        assignFieldValue(obj[0].schedulePropCity, 'schedulePropCity');
                                        assignFieldValue(obj[0].schedulePropState, 'schedulePropState');
                                        assignFieldValue(obj[0].schedulePropZip, 'schedulePropZip');
                                        assignFieldValue(obj[0].scheduleStatus, 'scheduleStatus');
                                        assignFieldValue(obj[0].propType, 'propType');
                                        checkPropInfo(obj[0].propType);
                                        assignFieldValue(obj[0].propertyDesc, 'propertyDesc');
                                        assignFieldValue(obj[0].presentMarketValue, 'presentMarketValue');
                                        assignFieldValue(obj[0].amountOfMortgages, 'amountOfMortgages');
                                        assignFieldValue(obj[0].grossRentalIncome, 'grossRentalIncome');
                                        assignFieldValue(obj[0].mortgagePayments, 'mortgagePayments');
                                        assignFieldValue(obj[0].insMaintTaxMisc, 'insMaintTaxMisc');
                                        assignFieldValue(obj[0].netRentalIncome, 'netRentalIncome');
                                        assignFieldValue(obj[0].titledUnder, 'titledUnder');
                                        assignFieldValue(obj[0].scheduleInvestType, 'scheduleInvestType');
                                        assignFieldValue(autoNumericConverter(obj[0].purchasePrice), 'purchasePrice');
                                        assignFieldValue(autoNumericConverter(obj[0].valueofImprovementsMade), 'valueofImprovementsMade');
                                        assignFieldValue(obj[0].intendedOccupancy, 'intendedOccupancy');
                                        $("#anyMortgagesLiens" + obj[0].anyMortgagesLiens).prop('checked', true);
                                        if (obj[0].anyMortgagesLiens == 'Yes') {
                                            $('.anyMortgagesLiensDispOpt').css("display", "");
                                        } else {
                                            $('.anyMortgagesLiensDispOpt').css("display", "none");
                                        }
                                        assignFieldValue(obj[0].creditorName, 'creditorName');
                                        assignFieldValue(obj[0].accountNumber, 'accountNumber');
                                        assignFieldValue(obj[0].loanStatus, 'loanStatus');
                                        assignFieldValue(autoNumericConverter(obj[0].unpaidBalance), 'unpaidBalance');
                                        assignFieldValue(autoNumericConverter(obj[0].monthlyPayment), 'monthlyPayment');
                                        assignFieldValue(obj[0].mortgageType, 'type');
                                        assignFieldValue(autoNumericConverter(obj[0].creditLimit), 'creditLimit');
                                        $("#anyOtherMortgagesLiens" + obj[0].anyOtherMortgagesLiens).prop('checked', true);
                                        if (obj[0].anyOtherMortgagesLiens == 'Yes') {
                                            $('.anyOtherMortgagesLiensDispOpt').css("display", "");
                                        } else {
                                            $('.anyOtherMortgagesLiensDispOpt').css("display", "none");
                                        }
                                        assignFieldValue(obj[0].datePurchased, 'datePurchasedSchedule');
                                        assignFieldValue(obj[0].salesDate, 'salesDate');
                                        assignFieldValue(obj[0].salesPrice, 'salesPrice');
                                        assignFieldValue(obj[0].maturityDateSchedule, 'maturityDateSchedule');
                                        assignFieldValue(obj[0].maturityDateAnother, 'maturityDateAnother');
                                        assignFieldValue(obj[0].creditorNameAnother, 'creditorNameAnother');
                                        assignFieldValue(obj[0].accountNumberAnother, 'accountNumberAnother');
                                        assignFieldValue(obj[0].loanStatusAnother, 'loanStatusAnother');
                                        assignFieldValue(autoNumericConverter(obj[0].unpaidBalanceAnother), 'unpaidBalanceAnother');
                                        assignFieldValue(autoNumericConverter(obj[0].monthlyPaymentAnother), 'monthlyPaymentAnother');
                                        assignFieldValue(obj[0].typeAnother, 'typeAnother');
                                        assignFieldValue(obj[0].ownership, 'ownership');
                                        assignFieldValue(autoNumericConverter(obj[0].creditLimitAnother), 'creditLimitAnother');

                                        assignFieldValue(obj[0].schedulePropUnit, 'schedulePropUnit');
                                        assignFieldValue(obj[0].schedulePropCountry, 'schedulePropCountry');
                                        assignFieldValue(obj[0].scheduleInterestRate, 'scheduleInterestRate');
                                        assignFieldValue(obj[0].scheduleInterestRateAnother, 'scheduleInterestRateAnother');
                                        if(obj[0].paidAtOrBeforeClose) {
                                            let div = $('#paidAtOrBeforeCloseDiv');
                                            $('#paidAtOrBeforeClose').prop('checked', true);
                                            div.addClass('switch-on');
                                            div.removeClass('switch-off');
                                        } else {
                                            $('#paidAtOrBeforeClose').prop('checked', false);
                                            div.addClass('switch-off');
                                            div.removeClass('switch-on');
                                        }

                                        if(obj[0].paidAtOrBeforeCloseAnother) {
                                            let div = $('#paidAtOrBeforeCloseAnotherDiv');
                                            $('#paidAtOrBeforeCloseAnother').prop('checked', true);
                                            div.addClass('switch-on');
                                            div.removeClass('switch-off');
                                        } else {
                                            $('#paidAtOrBeforeClose').prop('checked', false);
                                            div.addClass('switch-off');
                                            div.removeClass('switch-on');
                                        }

                                        if (obj[0].scheduleStatus == 'Sold') {
                                            $('.salesDispOpt').css("display", "block");
                                            $('.mortgagepopInfo').css("display", "none");
                                            $('.incomeValuespopInfo').css("display", "none");
                                        } else {
                                            $('.salesDispOpt').css("display", "none");
                                            $('.mortgagepopInfo').css("display", "block");
                                            $('.incomeValuespopInfo').css("display", "block");
                                        }
                                    }
                                });
                            }
                            $('#realEstateInfoContent').modal('toggle');
                        }
                    </script>
                    <?php
                    require_once 'FinancialAccountsAndSecurities.php';
                }

                ?>
                <div class="row d-flex justify-content-center my-2">
                    <?php
                    //if ($tabNumb == 6) { ?>
                    <!--button type="submit" name="but_search" id="but_sumbit" value="Save"
                            class="btn btn-primary font-weight-bold">Save
                    </button-->
                    <?php //} else { ?>
                    <button type="submit" name="but_search" id="butSubmit" value="Save"
                            class="btn btn-primary font-weight-bold">Save
                    </button>
                    &nbsp;&nbsp;&nbsp;
                    <button type="submit" name="but_search" id="but_sumbit" value="Save & Next"
                            class="btn btn-primary font-weight-bold">Save & Next
                    </button>
                    <?php //} ?>
                </div>
            </form>
            <?php
            }
            if ($tabNumb == 4) {
                include $includedFormUrl;
            }
            ?>
            <!-- Schedule of Real Estate Modal -->
            <?php if ($tabNumb == 6) { ?>
                <div class="modal fade realEstateInfoContent" id="realEstateInfoContent" role="dialog">
                    <div class="modal-dialog modal-xl">
                        <!-- Modal content-->
                        <form name="realEstateForm" id="realEstateForm" method="POST">
                            <input type="hidden" name="butSubmit" id="butSubmit" value="Save"/>
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">Schedule of Real Estate</h4>
                                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                                </div>
                                <div class="modal-body">
                                    <?php require 'addLORealEstateInfoPopup.php'; ?>
                                </div>
                                <div class="modal-footer">
                                    <input type="button" name="butSubmit" id="butSubmit" value="Save"
                                           class="btn btn-primary"
                                           onclick="save_and_update_client_real_estate_info()"/>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <script type="text/javascript">
                    function save_and_update_client_real_estate_info() {
                        $.ajax({
                            type: 'POST',
                            url: '<?php echo $saveUrl; ?>',
                            data: $('#realEstateForm').serialize(),
                            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                            success: function (myData) {
                                var edLOSRID = $('#LOSRID').val();
                                var obj = $.parseJSON(myData);
                                var posteddata = $("#realEstateForm").serialize();
                                var postedvalues = posteddata.split("&");
                                var finalArray = [];
                                for (var i = 0; i < postedvalues.length; i++) {
                                    var finalVal = postedvalues[i].substr(0, postedvalues[i].indexOf('='));
                                    if (finalVal != 'fileLMRId' && finalVal != 'CID' && finalVal != 'encCLOSRID' && finalVal != 'LOSRID' && finalVal != 'popOpt') {
                                        finalArray.push(finalVal);
                                    }
                                }
                                console.log(finalArray);
                                var innerText = '';
                                var index = 1;
                                for (let sre = 0; sre < obj.length; sre++) {
                                    titledUnder = obj[sre].titledUnder;
                                    datePurchased = obj[sre].datePurchased;
                                    maturityDateAnother = obj[sre].maturityDateAnother;
                                    maturityDateSchedule = obj[sre].maturityDateSchedule;
                                    purchasePrice = autoNumericConverter(obj[sre].purchasePrice);
                                    valueofImprovementsMade = autoNumericConverter(obj[sre].valueofImprovementsMade);
                                    intendedOccupancy = obj[sre].intendedOccupancy;
                                    anyMortgagesLiens = obj[sre].anyMortgagesLiens;
                                    creditorName = obj[sre].creditorName;
                                    accountNumber = obj[sre].accountNumber;
                                    loanStatus = obj[sre].loanStatus;
                                    unpaidBalance = autoNumericConverter(obj[sre].unpaidBalance);
                                    monthlyPayment = autoNumericConverter(obj[sre].monthlyPayment);
                                    mortgageType = obj[sre].mortgageType;
                                    creditLimit = autoNumericConverter(obj[sre].creditLimit);
                                    anyOtherMortgagesLiens = obj[sre].anyOtherMortgagesLiens;
                                    creditorNameAnother = obj[sre].creditorNameAnother;
                                    accountNumberAnother = obj[sre].accountNumberAnother;
                                    loanStatusAnother = obj[sre].loanStatusAnother;
                                    unpaidBalanceAnother = autoNumericConverter(obj[sre].unpaidBalanceAnother);
                                    monthlyPaymentAnother = autoNumericConverter(obj[sre].monthlyPaymentAnother);
                                    typeAnother = obj[sre].typeAnother;
                                    ownership = obj[sre].ownership;
                                    creditLimitAnother = autoNumericConverter(obj[sre].creditLimitAnother);
                                    var schTitle = '';

                                    if (obj[sre].schedulePropAddr != '') {
                                        schTitle += obj[sre].schedulePropAddr;
                                    }
                                    if (obj[sre].schedulePropCity != '') {
                                        schTitle += ', ' + obj[sre].schedulePropCity;
                                    }
                                    if (obj[sre].schedulePropState != '') {
                                        schTitle += ', ' + obj[sre].schedulePropState;
                                    }
                                    var CLOSRID = obj[sre].CLOSRID;
                                    var innerText = '<div class="card card-custom card-collapsed" id="realEstateInfoDivId_' + obj[sre].CLOSRID + '"><div class="card-header bg-primary-o-40"><div class="card-title"><h3 class="card-label realInfoCnt"> ' + schTitle + '</h3></div><div class="card-toolbar"><a href="javascript:void(0);" class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 divOpenClose" id="body_realEstateInfoDivId_' + obj[sre].CLOSRID + '" data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="Toggle Card"><i class="ki ki-arrow-down icon-nm"></i></a>';


                                    innerText += "<a class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 \" href=\"javascript:deleteClientLOScheduleRealInfo('" + obj[sre].encCID + "','" + obj[sre].enCLOSRID + "','realEstateInfoDivId_" + obj[sre].CLOSRID + "', 'Client');\" data-toggle=\"tooltip\" data-placement=\"top\" title=\"Click To Delete\"><i class=\"fas fa-trash-alt  \"></i></a><a class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2\" data-toggle=\"tooltip\" data-placement=\"top\" title=\"Click to edit client entity Info\" href=\"javascript:realEstateInfoPopup('" + obj[sre].encCID + "','" + obj[sre].enCLOSRID + "', 'Client');\"><i class=\"fas fa-pencil-alt\"></i></a>";
                                    innerText += '</div></div><div class="card-body" style="display: none;" id="body_realEstateInfoDivId_' + obj[sre].CLOSRID + '"><div class="row">';


                                    if ($.inArray('schedulePropAddr', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Property Address : <span class="H5">' + obj[sre].schedulePropAddr + '</span></span></div>';
                                    }
                                    if ($.inArray('schedulePropCity', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Property City : <span class="H5">' + obj[sre].schedulePropCity + '</span></span></div>';
                                    }
                                    if ($.inArray('schedulePropState', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Property State : <span class="H5">' + obj[sre].schedulePropState + '</span></span></div>'
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('schedulePropZip', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Property Zip : <span class="H5">' + obj[sre].schedulePropZip + '</span></span></div>';
                                    }
                                    if ($.inArray('scheduleStatus', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Status : <span class="H5">' + obj[sre].scheduleStatus + '</span></span></div>';
                                    }
                                    if ($.inArray('ownership', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Ownership % : <span class="H5">' + ownership + '</span></span></div>';
                                    }
                                    if ($.inArray('propType', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Property Type : <span class="H5">' + obj[sre].propType + '</span></span></div>';
                                    }
                                    if ($.inArray('scheduleInvestType', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Investment Type : <span class="H5">' + obj[sre].scheduleInvestType + '</span></span></div>';
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('presentMarketValue', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Present Market Value : <span class="H5">' + obj[sre].presentMarketValue + '</span></span></div>';
                                    }
                                    if ($.inArray('amountOfMortgages', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Balance of Mortgages &amp; Liens : <span class="H5">' + obj[sre].amountOfMortgages + '</span></span></div>';
                                    }
                                    if ($.inArray('grossRentalIncome', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Gross Rental Income : <span class="H5">' + obj[sre].grossRentalIncome + '</span></span></div>';
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('mortgagePayments', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Mortgage Payments : <span class="H5">' + obj[sre].mortgagePayments + '</span></span></div>';
                                    }
                                    if ($.inArray('insMaintTaxMisc', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Insurance, Taxes, HOA (If Not Included in Payment) : <span class="H5">' + obj[sre].insMaintTaxMisc + '</span></span></div>';
                                    }
                                    if ($.inArray('netRentalIncome', finalArray) != -1) {
                                        innerText += '<div class=" col-md-4"><span>Net Rental Income : <span class="H5">' + obj[sre].netRentalIncome + '</span></span></div>';
                                    }
                                    if ($.inArray('titledUnder', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Titled Under : <span class="H5">' + titledUnder + '</span></span></div>';
                                    }
                                    if ($.inArray('datePurchased', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Date Purchased : <span class="H5">' + datePurchased + '</span></span></div>';
                                    }
                                    if ($.inArray('purchasePrice', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Purchase Price : <span class="H5">' + purchasePrice + '</span></span></div>';
                                    }
                                    if ($.inArray('valueofImprovementsMade', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Value of Improvements Made : <span class="H5">' + valueofImprovementsMade + '</span></span></div>';
                                    }
                                    if ($.inArray('intendedOccupancy', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Intended Occupancy : <span class="H5">' + intendedOccupancy + '</span></span></div>';
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('salesDate', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Sale Date : <span class="H5">' + obj[sre].salesDate + '</span></span></div>';
                                    }
                                    if ($.inArray('salesPrice', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Sale Price : <span class="H5">' + autoNumericConverter(obj[sre].salesPrice) + '</span></span></div>';
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('anyMortgagesLiens', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Are there any mortgages or liens? : <span class="H5">' + anyMortgagesLiens + '</span></span></div>';
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('creditorName', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Creditor Name : <span class="H5">' + creditorName + '</span></span></div>';
                                    }
                                    if ($.inArray('accountNumber', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Account Number : <span class="H5">' + accountNumber + '</span></span></div>';
                                    }
                                    if ($.inArray('loanStatus', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Loan Status : <span class="H5">' + loanStatus + '</span></span></div>';
                                    }
                                    if ($.inArray('unpaidBalance', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Unpaid Balance : <span class="H5">' + unpaidBalance + '</span></span></div>';
                                    }
                                    if ($.inArray('monthlyPayment', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Monthly Payment : <span class="H5">' + monthlyPayment + '</span></span></div>';
                                    }
                                    if ($.inArray('type', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Type : <span class="H5">' + mortgageType + '</span></span></div>';
                                    }
                                    if ($.inArray('creditLimit', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Credit Limit : <span class="H5">' + creditLimit + '</span></span></div>';
                                    }
                                    if ($.inArray('maturityDateSchedule', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Maturity Date : <span class="H5">' + maturityDateSchedule + '</span></span></div>';
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('anyOtherMortgagesLiens', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Is their another mortgage or lien? : <span class="H5">' + anyOtherMortgagesLiens + '</span></span></div>';
                                    }
                                    innerText += '<div class="clearfix"></div>';
                                    if ($.inArray('creditorNameAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Creditor Name : <span class="H5">' + creditorNameAnother + '</span></span></div>';
                                    }
                                    if ($.inArray('accountNumberAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Account Number : <span class="H5">' + accountNumberAnother + '</span></span></div>';
                                    }
                                    if ($.inArray('loanStatusAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Loan Status : <span class="H5">' + loanStatusAnother + '</span></span></div>';
                                    }
                                    if ($.inArray('unpaidBalanceAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Unpaid Balance : <span class="H5">' + unpaidBalanceAnother + '</span></span></div>';
                                    }
                                    if ($.inArray('monthlyPaymentAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Monthly Payment : <span class="H5">' + monthlyPaymentAnother + '</span></span></div>';
                                    }
                                    if ($.inArray('typeAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Type : <span class="H5">' + typeAnother + '</span></span></div>';
                                    }
                                    if ($.inArray('creditLimitAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Credit Limit : <span class="H5">' + creditLimitAnother + '</span></span></div>';
                                    }
                                    if ($.inArray('maturityDateAnother', finalArray) != -1) {
                                        innerText += '<div class="col-md-4 "><span>Maturity Date : <span class="H5">' + maturityDateAnother + '</span></span></div>';
                                    }

                                    innerText += '</div></div></div>';
                                    index++
                                }
                                if (edLOSRID == '') {
                                    $('#showLORealEstateInfo').append(innerText);
                                } else {
                                    $('#realEstateInfoDivId_' + CLOSRID).html(innerText);
                                }
                            }
                        });
                        $('#realEstateInfoContent').modal('toggle');
                    }
                </script>
                <!-- Modal content End-->
            <?php } ?>
            <?php
            if ($tabNumb == 7) {
                ?>
                <form name="internalInfoForm" id="internalInfoForm" class="clientRegForm" method="POST"
                      action="../backoffice/saveInternalInfo.php"
                      onsubmit="return validateClientForm();">
                    <?php //print_r($loadTabsArray);?>
                    <input type="hidden" name="CID" id="CID" value="<?php echo cypher::myEncryption($selClientId) ?>"/>
                    <input type="hidden" name="encClientId" value="<?php echo cypher::myEncryption($selClientId) ?>">
                    <input type="hidden" name="PCID" id="PCID" value="<?php echo cypher::myEncryption($PCID); ?>"/>
                    <input type="hidden" name="executiveId" id="executiveId"
                           value="<?php echo cypher::myEncryption($selExecutiveId) ?>"/>
                    <!-- Collects user's current selected tab number -->
                    <input type="hidden" name="tabOpt" id="tabOpt"
                           value="<?php echo(htmlspecialchars(Request::GetClean('tabNumb')) ?? '') ?>"/>
                    <input type="hidden" name="loadedTabOpts" id="loadedTabOpts"
                           value="<?php echo implode(',', $loadTabsArray); ?>"/>
                    <div class="clear pad10"></div>
                    <?php require 'internalInfo.php'; ?>
                </form>
                <?php
            }
            if ($tabNumb == 8) {
                require_once 'borrower/Docs/profile.php';
            }

            /**
             * This function checks if the given field was allowed to include in the calculation of assets total or not in Assets tab in 'Update Borrower Profile'.
             * page.
             *
             * It checks access to the given field using fieldAccess() and applies Strings::replaceCommaValues() to the field values.
             * If the field was enabled in the 'Update Company Profile & System Settings > Form Fields' page It returns the field's value else returns null.
             *
             *
             * @param array $assetArray An array of fields allowed for the 'Update Borrower Profile > Assets' section.
             * @param array $clientAssetsInfo An array of client's assets information.
             * @param string $fieldName A string of field name for which access to be checked.
             * in it.
             * <AUTHOR> A.
             */
            function checkFieldAccess(array $assetArray, array $clientAssetsInfo, string $fieldName, $inType = 'int')
            {
                $assetInfo = [
                    'fNm' => $fieldName,
                    'sArr' => $assetArray,
                    'opt' => 'D',
                    'tf' => 1,
                ];
                if (BaseHTML::fieldAccess($assetInfo)) {
                    $fieldValue = trim($clientAssetsInfo[$fieldName]);
                    if ($inType == 'int') {
                        return Strings::replaceCommaValues($fieldValue);
                    } else {
                        return ($fieldValue);
                    }
                } else {
                    return null;
                }
            }

            ?>


<!-- clientCreateForm.php -->
