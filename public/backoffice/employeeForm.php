<?php
global $isPLO, $userRole, $processorId, $PCID, $employeeInfo, $userGroup, $oEmployee, $PCTimeZone,
       $PCAllowToCreate;
global $isPCActiveAloware, $allowPCUsersToMarketPlace,
       $pcAcqualifyStatus, $pcAcqualifyId, $pcPriceEngineStatus;

use models\composite\oEmployee\getEmployeeList;
use models\composite\oEmployee\getUserAssignedWorkflow;
use models\composite\oPC\getMyDetails;
use models\composite\oPC\getPCModules;
use models\composite\oServiceType\getUserTypes;
use models\composite\oThirdPartyServices\getThirdPartyServicesUserDetails;
use models\composite\oWorkflow\getPCWorkflow;
use models\constants\accessRestrictionPC;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glPCID;
use models\constants\gl\glRAMAccessPC;
use models\constants\gl\glThirdPartyServicesCRA;
use models\constants\gl\glUserGroup;
use models\constants\SMSServiceProviderArray;
use models\constants\timeZoneArray;
use models\cypher;
use models\FileStorage;
use models\lendingwise\tblAdminUsers;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$glRAMAccessPC = glRAMAccessPC::$glRAMAccessPC;
$accessRestrictionPC = accessRestrictionPC::$accessRestrictionPC;
$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$timeZoneArray = timeZoneArray::$timeZoneArray;

$email = '';
$processorName = '';
$processorInfoArray = [];
$tollFree1 = '';
$tollFree2 = '';
$tollFree3 = '';
$ext = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$tollFree = '';
$fax = '';
$cellNo1 = '';
$cellNo2 = '';
$cellNo3 = '';
$cellNumber = '';
$tollFreeArray = [];
$faxArray = [];
$role = '';
$LMRInfoArray = [];
$LMRAEId = 0;
$executiveInfoArray = [];
$LMRInfoArray = [];
$assignedExecutiveName = '';
$viewOpt = 0;
$assignedCompanyName = '';
$assignedToCompany = '';
$allowToViewAllFiles = 1;
$adminUserRole = '';
$assignedProcCompId = 0;
$processorLMRAEIDInfoArray = [];
$processorLMRAEIDKeyArray = [];
$LMRAEIDs = '';
$pll = 0;
$LMRAEIDsArray = [];
$serviceProvider = '';
$myTimeZone = '';
$allowExcelDownload = 0;
$sendMarketingEmail = 1;
$allowToListIn3PartyForm = 1;
$allowEmpToLogin = 0;
$phone1 = '';
$phone2 = '';
$phone3 = '';
$phoneExt = '';
$allowEmpToCreateFiles = 1;
$allowEmpToCreateTasks = 1;
$allowEmpToSeeDashboard = 1;
$seePrivate = 0;
$accessRestriction = 0;
$allowedToEditOwnNotes = 1;
$allowUserToDeleteUploadedDocs = 1;
$processorNumb = 0;
$seeBilling = 1;
$alternatePC = '';
$permissionToREST = 1;
$allowEmailCampaign = 1;
$allowBOToEditLMRFile = 1;
$employeeArray = [];
$LMRAEArray = [];
$ipArray = [];
$changeDIYPlan = 1;
$allowToUpdateFileAdminSection = 0;
$oldRole = '';
$allowToLASubmit = 0;
$subscribeToHOME = 0;
$allowToSendFax = 0;
$allowToCFPBSubmit = 0;

$allowEmployeeToEditCommission = 1;
$allowEmployeeToSeeCommission = 1;
$allowToChangeOrAssignBranchForFile = 1;
$allowToLockLoanFileEmpl = 1;
$allowToGetBorrowerUploadDocsNotification = 1;
$allowToViewMarketPlace = 1;
$allowToViewContactsList = 1;
$allowToViewCreditScreening = 1;

$allowToSeeBillingSectionForFile = 0;
$allowToSendFileDesignation = 1;
$barNo = '';
$allowEmpToSeePublicNotes = 1;
$attorneyRate = '';
$allowToViewCFPBPipeline = 0;
$serviceFeeRate = '';
$paralegalRate = '';
$empStates = '';
$empCity = '';
$empAddress = '';
$zipCode = '';
$empBarState1 = '';
$empBarNo1 = '';
$maxBarNo = 1;
$allowEmpToAccessRAM = 0;
$allowEmpToCreateBranch = 0;
$allowEmpToCreateAloware = 0;
$convertNewBRIntoEmpOwnBR = 0;
$allowEmpToCreateAgent = 0;
$allowEmpToSeeAgent = 0;
$processorLName = '';
$thirdPartyServices = '';
$thirdPartyServicesLegalDocs = '';
$enable2FAAuthentication = 0;
$TwoFAType = 'email';
$allowEmpToCreateAloware = 0;

$notifyBODocUpload = 0;
$notifyBranchDocUpload = 0;
$notifyLODocUpload = 0;
$notifyBrokerDocUpload = 0;
$notifyDocUploadRequest = 0;
$notifyNewFileCreated = 0;
$allowUserToSendMsgToBorrower = 1;

$allowToViewAutomationPopup = 0;
$allowToControlAutomationPopup = 0;
$allowEmpToSeeAllEmails = 0;
$userPriceEngineStatus = 0;
$loanpassLogin = '';
$loanpassPassword = '';
$allowToSelectFromEmail = 0;
$allowToSeeFeesInDashboard = 0;

if ($isPLO == 1) $allowToSendHomeownerLink = 1; else $allowToSendHomeownerLink = 0;
$PCInfoArray = [];
$allowToCFPBSubmitForPC = 0;
$allowToupdateFileAndClient = '';
$allowToupdateFileAndClientArr = [];
$additionalStateBarInfo = [];
$countyArray = [];
$empCounty = '';
$additionalCountyInfo = [];


$stateArray = Arrays::fetchStates();
/** Fetch all States **/
$shareThisFile = 1;
$allowToSubmitOffer = 1;
if (($userRole == 'Super' || $userRole == 'Manager') && $processorId == 0) {
    //$allowBOToEditLMRFile = 2;
    $allowEmpToLogin = 1;
    $allowExcelDownload = 1;
    $seePrivate = 1;
    $allowToUpdateFileAdminSection = 1;
    $allowToSendFax = 1;
    $subscribeToHOME = 1;
    $allowToLASubmit = 1;
    $allowToCFPBSubmit = 1;
    $allowToViewCFPBPipeline = 1;
}
$allowAutomation = 0;
$PCInfoArray = getMyDetails::getReport(['PCID' => $PCID]);
if (count($PCInfoArray) > 0) {
    if (array_key_exists($PCID, $PCInfoArray)) {
        $allowToCFPBSubmitForPC = trim($PCInfoArray[$PCID]['allowToCFPBSubmitForPC']);
    }
    if (array_key_exists($PCID, $PCInfoArray)) {
        $allowAutomation = trim($PCInfoArray[$PCID]['allowAutomation']);
    }
}
$branchUserTypeArray = [];
$isDIYBranch = 0;
$LMRInfoArray = $employeeInfo['branchArray'];
if (array_search('PLO', array_column($LMRInfoArray, 'userType')) >= 0) {
    $isDIYBranch = 1;
}

$PCArray = $employeeInfo['PCArray'];
$employeeArray = $employeeInfo['employeeArray'] ?? [];
$LMRAEIDsArray = $employeeInfo['LMRAEIDInfoArray'];

$emp = $employeeArray[0] ?? null;

$employeeObj = new tblAdminUsers();
if($emp) {
    unset($emp['employeeName']);
    $employeeObj->fromData($emp);
}


if ($processorId > 0) {
    if (count($LMRAEIDsArray) > 0) {
        $tempArray = [];
        $LMRAEIDs = '';
        $tempArray = $LMRAEIDsArray[$processorId];
        for ($pl = 0; $pl < count($tempArray); $pl++) {
            $LMRAEId = 0;
            $LMRAEId = $tempArray[$pl]['LMRAEID'];
            if ($pl > 0) {
                $LMRAEIDs .= ',';
            }
            $LMRAEIDs .= $LMRAEId;
        }
    }
    $LMRAEArray = explode(',', $LMRAEIDs);
}
$branchInfoArray = [];
$pll = 0;
if ($userRole != 'Manager' && $userGroup == 'Employee') {
    $branchInfoArray = \models\composite\oBranch\getMyDetails::getReport(['executiveId' => $LMRAEIDs]);
    for ($i = 0; $i < count($LMRAEArray); $i++) {
        $branchID = 0;
        $branchID = trim($LMRAEArray[$i]);
        if (array_key_exists($branchID, $branchInfoArray)) {
            if ($pll > 0) {
                $assignedToCompany .= ', ';
            }
            $assignedToCompany .= trim($branchInfoArray[$branchID]['LMRExecutive']) . ' - ' . trim($branchInfoArray[$branchID]['company']);
            $pll++;
        }
    }
}

/**
 *
 * Description    : hide the specific fields in Employee Profile
 * Date        : Mar 14, 2017
 * Author        : Venkatesh
 * Modified    : Viji on Mar 17, 2017
 **/
$tempModulesArray = [];
$moduleCodeArray = [];

if ($PCID > 0) {
    $tempModulesArray = getPCModules::getReport(['PCID' => $PCID, 'keyNeeded' => 'n']);
}

for ($b = 0; $b < count($tempModulesArray); $b++) {
    $moduleCodeArray[] = $tempModulesArray[$b]['moduleCode'];
}

$glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
$thirdPartyServiceCSRArray = explode(',', PageVariables::$thirdPartyServiceCSR);
$thirdPartyServicesUserDetails = getThirdPartyServicesUserDetails::getReport('Employee', $processorId);

foreach ($employeeArray as $employee) {
    $processorName = trim($employee['processorName']);
    $processorLName = trim($employee['processorLName']);
    $AID = trim($employee['AID']);
    $email = trim($employee['email']);
    $tollFree = trim($employee['tollFree']);
    $cellNumber = trim($employee['cellNumber']);
    $fax = trim($employee['fax']);
    $role = trim($employee['role']);
    $processingCompanyId = trim($employee['processingCompanyId']);
    $allowToViewAllFiles = trim($employee['allowToViewAllFiles']);
    $serviceProvider = trim($employee['serviceProvider']);
    $myTimeZone = trim($employee['timeZone']);
    $allowExcelDownload = trim($employee['allowExcelDownload']);
    $sendMarketingEmail = trim($employee['sendMarketingEmail']);
    $forensicAuditService = trim($employee['forensicAuditService']);
    $forensicAuditInterest = trim($employee['forensicAuditInterest']);
    $feedbackOfFAS = trim($employee['feedbackOfFAS']);
    $allowToListIn3PartyForm = trim($employee['allowToListIn3PartyForm']);
    $allowEmpToLogin = trim($employee['allowEmpToLogin']);
    $phone = trim($employee['directPhone']);
    $allowEmpToCreateFiles = trim($employee['allowEmpToCreateFiles']);
    $allowEmpToCreateTasks = trim($employee['allowEmpToCreateTasks']);
    $allowEmpToSeeDashboard = trim($employee['allowEmpToSeeDashboard']);
    $seePrivate = trim($employee['seePrivate']);
    $allowUserToDeleteUploadedDocs = trim($employee['allowedToDeleteUplodedDocs']);
    $allowedToEditOwnNotes = trim($employee['allowedToEditOwnNotes']);
    $seeBilling = trim($employee['seeBilling']);
    $alternatePC = trim($employee['alternatePC']);
    $permissionToREST = trim($employee['permissionToREST']);
    $allowEmailCampaign = trim($employee['allowEmailCampaign']);
    $allowBOToEditLMRFile = trim($employee['allowBOToEditLMRFile']);
    $changeDIYPlan = trim($employee['changeDIYPlan']);
    $allowToSendHomeownerLink = trim($employee['allowToSendHomeownerLink']);
    $allowToUpdateFileAdminSection = trim($employee['allowToUpdateFileAdminSection']);
    $allowToLASubmit = trim($employee['allowToLASubmit']);
    $allowToCFPBSubmit = trim($employee['allowToCFPBSubmit']);
    $subscribeToHOME = trim($employee['subscribeToHOME']);
    $allowToSendFax = trim($employee['allowToSendFax']);
    $allowEmployeeToEditCommission = trim($employee['allowEmployeeToEditCommission']);
    $allowEmployeeToSeeCommission = trim($employee['allowEmployeeToSeeCommission']);
    $allowToSendFileDesignation = trim($employee['allowToSendFileDesignation']);
    $barNo = trim($employee['barNo']);
    $attorneyRate = trim($employee['attorneyRate']);
    $paralegalRate = trim($employee['paralegalRate']);

    $serviceFeeRate = trim($employee['serviceFeeRate']);


    $empAddress = trim($employee['empAddress']);
    $empCity = trim($employee['empCity']);
    $empStates = trim($employee['empState']);
    $zipCode = trim($employee['empZip']);
    $empCounty = trim($employee['empCounty']);

    $allowToChangeOrAssignBranchForFile = trim($employee['allowToChangeOrAssignBranchForFile']);
    $allowToLockLoanFileEmpl = trim($employee['allowToLockLoanFileEmpl']);
    $allowToGetBorrowerUploadDocsNotification = trim($employee['allowToGetBorrowerUploadDocsNotification']);
    $allowToViewCFPBPipeline = trim($employee['allowToViewCFPBPipeline']);
    $allowToSeeBillingSectionForFile = trim($employee['allowToSeeBillingSectionForFile']);

    $showEditSwitch = 'display:none';
    if ($seeBilling == 1) {
        $showEditSwitch = 'display:table-row';
    }


    $allowEmpToSeePublicNotes = trim($employee['allowEmpToSeePublicNotes']);
    $allowEmpToAccessRAM = trim($employee['allowToAccessRAM']);
    $allowEmpToCreateBranch = trim($employee['allowEmpToCreateBranch']);
    $allowEmpToCreateAloware = trim($employee['allowEmpToCreateAloware']);
    $allowEmpToCreateAgent = trim($employee['allowEmpToCreateAgent']);
    $allowEmpToSeeAgent = trim($employee['allowEmpToSeeAgent']);
    $convertNewBRIntoEmpOwnBR = trim($employee['convertNewBRIntoEmpOwnBR']);
    $thirdPartyServices = trim($employee['thirdPartyServices']);
    $thirdPartyServicesLegalDocs = trim($employee['thirdPartyServicesLegalDocs']);
    $shareThisFile = trim($employee['shareThisFile']);
    $allowToSubmitOffer = trim($employee['allowToSubmitOffer']);

    $allowToViewMarketPlace = trim($employee['allowToViewMarketPlace']);
    $allowToupdateFileAndClient = trim($employee['allowToupdateFileAndClient']);
    $allowToViewContactsList = trim($employee['allowToViewContactsList']);
    $allowToViewCreditScreening = trim($employee['allowToViewCreditScreening']);
    $enable2FAAuthentication = trim($employee['enable2FAAuthentication']);
    $TwoFAType = trim($employee['TwoFAType']);
    $userPriceEngineStatus = trim($employee['userPriceEngineStatus']);

    $loanpassLogin = trim($employee['loanpassLogin']);
    $loanpassPassword = trim($employee['loanpassPassword']);

    $notifyBODocUpload = trim($employee['notifyBODocUpload']);
    $notifyBranchDocUpload = trim($employee['notifyBranchDocUpload']);
    $notifyLODocUpload = trim($employee['notifyLODocUpload']);
    $notifyBrokerDocUpload = trim($employee['notifyBrokerDocUpload']);
    $notifyDocUploadRequest = trim($employee['notifyDocUploadRequest']);
    $notifyNewFileCreated = trim($employee['notifyNewFileCreated']);
    $allowUserToSendMsgToBorrower = trim($employee['allowUserToSendMsgToBorrower']);
    $allowEmpToCopyFile = trim($employee['allowEmpToCopyFile']) ?? 0;

    $allowToSelectFromEmail = $employee['allowToSelectFromEmail'] ?? 0;
    $allowEmpToSeeAllEmails = trim($employee['allowEmpToSeeAllEmails']) ?? 0;
    $allowToMassUpdate = trim($employee['allowToMassUpdate']) ?? 0;
    $allowToSeeFeesInDashboard = trim($employee['allowToSeeFeesInDashboard']) ?? 0;

    $allowToViewAutomationPopup = trim($employee['allowToViewAutomationPopup']);
    $allowToControlAutomationPopup = trim($employee['allowToControlAutomationPopup']);

    if (($tollFree == '') || ($tollFree == NULL) || ($tollFree == 'NULL')) {
    } else {
        $tollFreeArray = Strings::splitPhoneNumber($tollFree);
        if (count($tollFreeArray) > 0) {
            $tollFree1 = $tollFreeArray['No1'];
            $tollFree2 = $tollFreeArray['No2'];
            $tollFree3 = $tollFreeArray['No3'];
        }
        $ext = $tollFreeArray['Ext'];
    }
    if (($phone == '') || ($phone == NULL) || ($phone == 'NULL')) {
    } else {
        $phoneArray = Strings::splitPhoneNumber($phone);
        if (count($phoneArray) > 0) {
            $phone1 = $phoneArray['No1'];
            $phone2 = $phoneArray['No2'];
            $phone3 = $phoneArray['No3'];
        }
        $phoneExt = $phoneArray['Ext'];
    }
    if (($cellNumber == '') || ($cellNumber == NULL) || ($cellNumber == 'NULL')) {
    } else {
        $cellNumberArray = Strings::splitPhoneNumber($cellNumber);
        if (count($cellNumberArray) > 0) {
            $cellNo1 = $cellNumberArray['No1'];
            $cellNo2 = $cellNumberArray['No2'];
            $cellNo3 = $cellNumberArray['No3'];
        }
    }
    if (($fax == '') || ($fax == NULL) || ($fax == 'NULL')) {
    } else {
        $faxArray = Strings::splitPhoneNumber($fax);
        if (count($faxArray) > 0) {
            $fax1 = $faxArray['No1'];
            $fax2 = $faxArray['No2'];
            $fax3 = $faxArray['No3'];
        }
    }
}
if ($allowToupdateFileAndClient != '') {
    $allowToupdateFileAndClientArr = explode(',', $allowToupdateFileAndClient);
}
if ($alternatePC == '') {

    $processingCompanyName = '';
    for ($pc = 0; $pc < count($PCArray); $pc++) {

        if (trim($PCArray[$pc]['PCID']) == $PCID) $processingCompanyName = trim($PCArray[$pc]['processingCompanyName']);
    }
    $alternatePC = $processingCompanyName;
}


$ipArray = ['activeStatus' => '1', 'isPublic' => '1', 'PCID' => $PCID];
$adminUserRoleArray = getUserTypes::getReport($ipArray);
if (count($adminUserRoleArray) > 0) $adminUserRoleArray = Arrays::sortDbResult($adminUserRoleArray, 'userType', SORT_ASC);


$PCWorkflowArray = [];
if ($processingCompanyId > 0) {
    $PCWorkflowArray = getPCWorkflow::getReport(['PCID' => $processingCompanyId]);
}
$assignedWorkflow = [];
$assignedWFIDs = [];
if ($processorId > 0) {
    $assignedWorkflow = getUserAssignedWorkflow::getReport(['UID' => $processorId, 'UType' => 'Employee']);
}
if (trim($myTimeZone) == '') { /* Show Assigned PC timezone while creating employee - on Jan 30, 2018 Ticket ID: *********  */
    $myTimeZone = $PCTimeZone;
}
if (trim($myTimeZone) == '') { /* If TZ is blank, assign default server time zone. */
    $myTimeZone = CONST_SERVER_TIME_ZONE;
}
for ($pc = 0; $pc < count($assignedWorkflow); $pc++) {
    $assignedWFIDs[] = trim($assignedWorkflow[$pc]['WFID']);
}

if ($role == 'Attorney') {
    $additionalStateBarInfo = $employeeInfo['additionalStateBarInfo'];
    $additionalCountyInfo = $employeeInfo['additionalCountyInfo'];
    if (count($additionalStateBarInfo) > 1) $maxBarNo = count($additionalStateBarInfo);
}

$showThirdPartyEditSwitch = 'display:none';
if ($thirdPartyServices == 1) {
    $showThirdPartyEditSwitch = 'display:table-row';
}
$showThirdPartyEditSwitchLegalDocs = 'display:none';
if ($thirdPartyServicesLegalDocs == 1) {
    $showThirdPartyEditSwitchLegalDocs = 'display:table-row';
}
$showCreateAgentSwitch = 'display:none';
if ($allowEmpToSeeAgent == 1) {
    $showCreateAgentSwitch = 'display:table-row';
}
//Pricing Engine
$showPriceEngineSwitch = 'display:none';
if ($userPriceEngineStatus == 1) {
    $showPriceEngineSwitch = 'display:table-row';
}

if($processorId) {
    $tblAdminUsers = tblAdminUsers::Get([
        'AID' => $processorId,
    ]);
} else {
    $tblAdminUsers = new tblAdminUsers();
}


if (($userRole == 'Super') || ($processorId > 0) || ($PCAllowToCreate == 1) || ($userRole == 'Manager')) {
?>
<form class="form" name="processorForm" id="processorForm" method="post" action="employeeSave.php" enctype="multipart/form-data">
    <?php } else { ?>
    <form class="form" name="processorForm" id="processorForm" method="post" action="javascript:void(0);" enctype="multipart/form-data">
        <?php } ?>
        <input type="hidden" name="pId" id="pId" value="<?php echo $processorId; ?>">
        <input type="hidden" name="userRole" id="userRole" value="<?php echo $userRole; ?>">
        <input type="hidden" name="isPLO" id="isPLO" value="<?php echo $isPLO; ?>">
        <input type="hidden" name="PCAllowToCreate" id="PCAllowToCreate" value="<?php echo $PCAllowToCreate; ?>">
        <input type="hidden" name="currentPage" id="currentPage"
               value="<?php echo htmlspecialchars(basename($_SERVER['PHP_SELF'], '.php')); ?>">
        <input type="hidden" name="maxBarNo" id="maxBarNo" value="<?php echo $maxBarNo; ?>">

        <div class="form-group row">
            <div class="col-lg-6">
                <label class="font-weight-bold" for="email">Email</label>
                <?php if ($processorId == 0) { ?>
                    <input class="form-control mandatory" type="text" placeholder="Enter Email" name="email"
                           id="email"
                           value="<?php echo $email; ?>" size="40"
                           maxlength="75" onblur="createEmployee.checkEmployeeEmailExist();" autocomplete="off">
                <?php } else {
                    echo " <div class=\"input-group\"><input  class=\"form-control form-controller-solid  datatable-input\" type=\"text\"  value=\"$email\" disabled><input type=\"hidden\" name=\"oldEmail\" id=\"oldEmail\" value=\"" . $email . "\">
                        <div class=\"input-group-append\"><span class=\"input-group-text\"><a data-id=\"userId=" . cypher::myEncryption($processorId) . '&userType=' . cypher::myEncryption('Employee') . '&email=' . $email . "\" href='' data-href=\"" . CONST_URL_POPS . "changeEmailPopup.php\" data-name=\"" . $processorName . " > Change Email \"  data-toggle='modal' data-target='#exampleModal1'><i class='fas fa-pencil-alt' title=\"Click here to change email address\"></i></a></span>
                        </div>
                        </div>
                        ";
                }
                ?>
            </div>
            <?php
            if ($processorId == 0) {
                ?>
                <div class="col-lg-6">
                    <label class="font-weight-bold" for="confirmEmail">Confirm Email</label>
                    <input type="text" class="form-control mandatory" placeholder="Enter Confirm Email"
                           name="confirmEmail"
                           id="confirmEmail" value="<?php echo $email ?>" size="40" maxlength="75"
                           autocomplete="off">
                </div>
            <?php } ?>
        </div>

        <div class="form-group row">
            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="processorName">Employee First Name</label>
                <input class="form-control mandatory" placeholder="Enter Employee First Name" type="text"
                       name="processorName"
                       id="processorName" value="<?php echo $processorName ?>" size="25" maxlength="50"
                       autocomplete="off">
            </div>
            <div class="col-lg-6 mb-7">
                <label class="font-weight-bold" for="processorLName">Employee Last Name</label>
                <input class="form-control mandatory" type="text" placeholder="Enter Employee Last Name"
                       name="processorLName"
                       id="processorLName" value="<?php echo $processorLName ?>" size="25" maxlength="50"
                       autocomplete="off">
            </div>

            <?php
            if ($userRole == 'Super') {
                ?>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="assignedToProcessingCompany">Assigned To Processing
                        Company</label>
                    <select class="form-control mandatory" name="assignedToProcessingCompany"
                            id="assignedToProcessingCompany"
                            onchange="createEmployee.checkAllowedToLogin(this.value); createEmployee.getPCUserRole('processorForm','assignedToProcessingCompany','role');">
                        <option value=""> - Select -</option>
                        <?php
                        for ($pc = 0; $pc < count($PCArray); $pc++) {
                            $processingStatus = '';
                            $processingStatus = trim($PCArray[$pc]['activeStatus']);
                            ?>
                            <option value="<?php echo trim($PCArray[$pc]['PCID']) ?>" <?php echo Arrays::isSelected(trim($PCArray[$pc]['PCID']), $processingCompanyId); ?> <?php if ($processingStatus == 0) { ?> class="clsRed" <?php } ?>><?php echo trim($PCArray[$pc]['processingCompanyName']) ?></option>
                            <?php
                        }
                        ?>
                    </select>
                </div>
            <?php } else { ?>
                <input type="hidden" name="assignedToProcessingCompany" id="assignedToProcessingCompany"
                       value="<?php echo $PCID ?>">
            <?php } ?>

            <div class="col-lg-6 mb-7 showOnTop statusOpt">
                <label class="font-weight-bold" for="assignedToCompany">
                    Exclusively Assign to a Branch <i class="tooltipAjax fas fa-info-circle text-primary"
                                                      data-html="true"
                                                      data-toggle="tooltip"
                                                      title="Employees can be assigned to a specific branch, so the employee user only sees those respective loan files. For example, an Account Executive can manage the loan files of  specific branches."></i></label>
                <?php
                $allBranchList = [];
                if ($userRole == 'Super' || $userRole == 'Manager') { ?>
                    <select data-placeholder=" - Select Branches - " name="assignedToCompany[]" id="assignedToCompany"
                            class="chzn-select form-control form-controller-solid " multiple="" onchange="checkIsDIY()">
                        <?php

                        for ($l = 0; $l < count($LMRInfoArray); $l++) {
                            $tempArray = [];
                            $executiveId = 0;
                            $LMRExecutive = '';
                            $LMRExecutiveCompany = '';
                            $tempArray = $LMRInfoArray[$l];
                            $executiveId = $tempArray['executiveId'];
                            $LMRExecutive = $tempArray['LMRExecutive'];
                            $LMRExecutiveCompany = $tempArray['company'];
                            $branchUserTypeArray[$tempArray['executiveId']] = $tempArray['userType'];

                            $sOpt = '';
                            $sOpt = Arrays::isSelectedArray($LMRAEArray, $executiveId);
                            echo "<option value=\"" . $executiveId . "\" " . $sOpt . '>' . $LMRExecutive . ' - ' . $LMRExecutiveCompany . '</option>';
                            $allBranchList[] = $executiveId;
                        }
                        ?>
                    </select>
                <?php } else {
                    echo $assignedToCompany;
                } ?>
                <?php if ($userRole == 'Super' || $userRole == 'Manager') { ?>
                    <input type="hidden" name="allBranchList" value="<?php echo implode(',', $allBranchList); ?>"/>
                    <span class="form-text text-muted">Note: If you do not select any branch, employee user will
                                have access to all loan files for all branches.</span>
                <?php } ?>
            </div>

            <div class="col-lg-6 mb-7">

                <label class="font-weight-bold"
                       for="alternateToProcessingCompany"><?php if ($PCID == '1520') { ?>Firm Name <?php } else { ?>Company Name<?php } ?>
                    <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true" data-toggle="tooltip"
                       title="If this employee does not work for the parent company, you can enter their company name here. For example, an Investor, auditor, attorney, or any other 3rd party"></i></label>
                <input class="form-control" type="text" id="alternateToProcessingCompany"
                       name="alternateToProcessingCompany"
                       value="<?php echo $alternatePC ?>" size="35" maxlength="70" autocomplete="off">
            </div>
            <div class="col-lg-6 mb-7" id="serviceFeeRateDiv" <?php if ($role == 'Attorney') {
                echo 'style="display:block;"';
            } else {
                echo 'style="display:none;"';
            } ?>>
                <label class="font-weight-bold" for="serviceFeeRate"><?php if ($PCID == '1520') { ?>RAM <?php } ?>
                    Service Fee Rate</label>
                <input class="form-control" type="text" name="serviceFeeRate" id="serviceFeeRate" size="10"
                       maxLength="25" value="<?php echo $serviceFeeRate ?>">
            </div>

            <div class="col-lg-6 mb-7" id="customUserRoleDiv">
                <label class="font-weight-bold">Role</label>
                <?php
                if ((PageVariables::$userNumber == $processorId) && ($userRole != 'Super')) {
                    $oldRole = $role;
                    if ($role == 'Manager') {
                        $role = 'Manager (Admin Rights)';
                    }
                    ?>
                    <input class="form-control" type="text" value="<?php echo $role ?>" disabled>
                    <input type="hidden" name="role" id="role" value="<?php echo $oldRole ?>">
                    <?php
                } else {
                    ?>
                    <div class="input-group">
                        <select name="role" class="form-control custom-select"
                                id="role" <?php if (($userRole == 'Super' || ($userRole == 'Manager' && $PCAllowToCreate == 1)) && $processorId == 0) { ?> onchange="createEmployee.changeEmployeeAccess(); createEmployee.checkEmpRole(this.value);" <?php } else { ?> onchange="createEmployee.checkEmpRole(this.value); " <?php } ?> >
                            <option value=""> - Select -</option>
                            <?php
                            for ($r = 0; $r < count($adminUserRoleArray); $r++) {
                                $emp = '';
                                $emp = $adminUserRoleArray[$r]['userType'];
                                if ($emp == 'Manager') {
                                    $emp = 'Manager (Admin Rights)';
                                }
                                ?>
                                <option value="<?php echo trim($adminUserRoleArray[$r]['userType']) ?>" <?php echo Arrays::isSelected(trim($adminUserRoleArray[$r]['userType']), $role); ?> ><?php echo $emp ?></option>
                                <?php
                            }
                            if ($userGroup == 'Super') {
                                ?>
                                <option value="Sales" <?php echo Arrays::isSelected('Sales', $role); ?> >Sales Rep
                                </option>
                                <option value="CFPB Auditor" <?php echo Arrays::isSelected('CFPB Auditor', $role); ?> >
                                    CFPB Auditor
                                </option>
                                <option value="Auditor Manager" <?php echo Arrays::isSelected('Auditor Manager', $role); ?> >
                                    Auditor Manager
                                </option>
                                <?php
                            }
                            ?>
                        </select>
                        <div class="input-group-append tooltipAjax" data-toggle="tooltip"
                             title="Click to Add User Role"><span class="input-group-text ">
                           <i data-href="<?php echo CONST_URL_POPS; ?>addCustomUserRole.php" data-wsize='modal-default'
                              data-name='Add User Role'
                              data-id="userRole=<?php echo $userRole ?>&PCID=<?php echo cypher::myEncryption($PCID) ?>&employeeProfile=employeeProfile"
                              data-toggle='modal' data-target='#exampleModal1'
                              class=' fas fa-plus-circle'></i>
                            </span></div>
                    </div>
                <?php } ?>
            </div>

            <div class="col-lg-6 mb-7" id="paralegalRateDiv" <?php if ($role == 'Attorney') {
                echo 'style="display:block;"';
            } else {
                echo 'style="display:none;"';
            } ?>>
                <label class="font-weight-bold"><?php if ($PCID == '1520') { ?>RAM <?php } ?>Service Fee Rate</label>
                <input class="form-control" type="text" name="serviceFeeRate" id="serviceFeeRate" size="10"
                       maxLength="25" value="<?php echo $serviceFeeRate ?>">
            </div>


            <?php
            if ($processorId == 0) {
                ?>
                <div class="col-lg-6 mb-7">
                    <label class="font-weight-bold" for="pwd">Password</label>
                    <input type="password" name="pwd" id="pwd" value="" size="25" maxlength="15"
                           class="form-control mandatory" autocomplete="off">
                </div>
                <div class="col-lg-6">
                    <label class="font-weight-bold" for="confirmPwd">Confirm Password</label>
                    <input type="password" name="confirmPwd" id="confirmPwd" value="" size="25" maxlength="15"
                           class="form-control mandatory" autocomplete="off"></div>
                <?php
            }
            ?>
            <div class="col-lg-6 mb-7" id="receiveNoticeSubDiv_2">
                <div class="align-items-center">
                    <div class="row mx-auto">
                        <label class="font-weight-bold">Allow login to Back Office?
                            <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                               data-toggle="tooltip"
                               title="This will enable or disable ability to login. Also, your billing is tied to # of users with login rights enabled."></i>
                        </label>
                        <?php
                        if ($PCAllowToCreate == 1 || ($allowEmpToLogin == 1 && $processorId > 0)) {
                            ?>
                            <div class="col-lg-4">
                            <span class="switch switch-icon">
                                <label class="font-weight-bold">
                                    <input class="form-control" <?php if ($allowEmpToLogin == '1') { ?> checked="checked" <?php } ?>
                                           type="checkbox"
                                           value="<?php echo $allowEmpToLogin ?>"
                                           id="empLogin"
                                           onchange="toggleSwitch('empLogin', 'allowEmpToLogin', '1', '0' ); createEmployee.showAndHideAllEmpPermission('ShowAllEmpPermission');">
                                    <input type="hidden" name="allowEmpToLogin" id="allowEmpToLogin"
                                           value="<?php echo $allowEmpToLogin ?>">
                                    <span></span>
                                </label>
                            </span>
                            </div>
                        <?php } else {
                            $allowEmpToLogin = 0; /* Force the user login rights OFF */
                            ?>
                            <div class="col-lg-4 h5">
                                <?php if ($allowEmpToLogin == 1 && $processorId > 0) {
                                    echo 'Yes';
                                } else {
                                    echo 'No';
                                } ?>
                                <span class="form-text text-muted">( Note: You cannot edit this field since you have exceeded the # of allowed users )
                            <a href="mailto:<EMAIL>?subject=Add more users&body=I would like to add the following number of users to our account:%0D%0A%0D%0A%0D%0AThank you"
                               alt="Click to add more users" title="Click to add more users"><b>Add Users</b></a></span>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-7">
                <label class="font-weight-bold">Avatar</label>
                <?php if($tblAdminUsers->avatar) { ?>
                    <i class="fa fa-copy copy-link tooltipClass text-primary"
                       title="Click To Copy Avatar Link"
                       id="<?php echo $tblAdminUsers->getURL(); ?>"></i>
                <?php } ?>
                <input type="FILE"
                       name="employeeAvatar"
                       id="employeeAvatar"
                       size="30"
                       autocomplete="off"
                       class="employeeAvatar form-control">
                <?php if($tblAdminUsers->avatar) { ?>
                    <div class="symbol symbol-100 mr-5 mt-2 ">
                        <div class="symbol-label" style="background-image:url('<?php echo $tblAdminUsers->getURL(); ?>')"></div>
                       Merge Tag: <?php echo $tblAdminUsers->getMergeTag(); ?>
                        <i class="fa fa-copy copy-link tooltipClass text-primary"
                           title="Click To Copy Merge tag Path"
                           id="<?php echo $tblAdminUsers->getMergeTag(); ?>"></i>
                    </div>
                <?php
                    FileStorage::Logging(true);
                } ?>
                <span class="form-text text-muted">(The file types allowed are JPEG,PNG,GIF.)</span>
            </div>

        </div>
        <!--begin::Accordion::1-->
        <div class="card card-custom mb-2">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">General Information</h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0)"
                       class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                       data-card-tool="toggle">
                        <i class="ki icon-nm ki-arrow-down"></i>
                    </a>
                </div>
            </div>
            <div class="card-body accordion" style="display: none">
                <div class="form-group row">
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="timeZone">Time Zone</label>
                        <select name="timeZone" id="timeZone" class="form-control">
                            <?php
                            $timeZoneKeyArray = [];
                            $timeZoneKeyArray = array_keys($timeZoneArray);
                            for ($tz = 0; $tz < count($timeZoneKeyArray); $tz++) {
                                $timeZone = trim($timeZoneKeyArray[$tz]);
                                ?>
                                <option value="<?php echo $timeZone ?>" <?php echo Arrays::isSelected($timeZone, $myTimeZone); ?>><?php echo $timeZoneArray[$timeZone] ?></option>
                                <?php
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="tollFree">Toll Free/Main</label>
                        <input type="text" class="form-control mask_phone" name="tollFree" id="tollFree"
                               value="<?php echo $tollFree ?>" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7" id="attorneyRateDiv" <?php if ($role == 'Attorney') {
                        echo 'style="display:block;"';
                    } else {
                        echo 'style="display:none;"';
                    } ?>>
                        <label class="font-weight-bold" for="attorneyRate">Attorney Rate</label>
                        <input type="text" name="attorneyRate" id="attorneyRate" size="10" class="form-control"
                               maxLength="25" value="<?php echo $attorneyRate ?>">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="empAddress">Street</label>
                        <input type="text" name="empAddress" id="empAddress" value="<?php echo $empAddress ?>"
                               class="form-control" size="40" maxlength="75" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="phone">Local/Direct Phone</label>
                        <input type="text" class="form-control mask_phone" name="phone" id="phone"
                               value="<?php echo $phone ?>" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="empCity">City</label>
                        <input type="text" name="empCity" id="empCity" value="<?php echo $empCity ?>" size="20"
                               class="form-control" maxlength="30" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="cellNumber">Cell Number</label>
                        <input type="text" class="form-control mask_cellnew <?php if ($allowEmpToCreateAloware == 0) {
                            echo 'ignoreValidation';
                        } ?>" name="cellNumber" id="cellNumber"
                               value="<?php echo $cellNumber ?>" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="empState">State</label>
                        <select name="empState" id="empState" class="form-control">
                            <option value=''> - Select -</option>
                            <?php
                            for ($j = 0; $j < count($stateArray); $j++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $empStates);
                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="serviceProvider">
                            Please select service provider if you would like to receive task reminders via SMS <i
                                    class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                    data-toggle="tooltip"
                                    title="Please select service provider if you would like to receive task reminders via SMS"></i>
                        </label>
                        <select class="form-control" name="serviceProvider" id="serviceProvider">
                            <option value="">- Select -</option>
                            <?php
                            $spKeyArray = array_keys($SMSServiceProviderArray);
                            for ($sp = 0; $sp < count($spKeyArray); $sp++) {
                                $spKey = $spKeyArray[$sp];
                                $myServiceProvider = $SMSServiceProviderArray[$spKey];
                                ?>
                                <option value="<?php echo $spKey ?>" <?php echo Arrays::isSelected($spKey, $serviceProvider); ?>><?php echo $myServiceProvider ?></option>
                                <?php
                            }
                            ?>
                        </select>
                    </div>
                    <?php if ((PageVariables::$userNumber == $processorId) && ($userRole != 'Super')) {
                    } else { ?>
                        <div class="col-lg-6 mb-7" id="barNumberDiv" <?php if ($role == 'Attorney') {
                            echo 'style="display:block;"';
                        } else {
                            echo 'style="display:none;"';
                        } ?>>
                            <label class="font-weight-bold" for="barNo">Bar #</label>
                            <input type="text" name="barNo" id="barNo" size="10"
                                   maxLength="200" value="<?php echo $barNo ?>">
                        </div>
                    <?php } ?>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="zipCode">Zip Code</label>
                        <input type="text" class="form-control zipCode" name="zipCode" id="zipCode"
                               value="<?php echo $zipCode ?>" size="10" maxlength="10" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="fax">Fax</label>
                        <input type="text" class="form-control mask_cellnew" name="fax" id="fax"
                               value="<?php echo $fax ?>" autocomplete="off">
                    </div>
                    <?php
                    if ((PageVariables::$userNumber == $processorId) && ($userRole != 'Super')) {
                    } else {

                        for ($k = 1; $k <= $maxBarNo; $k++) {
                            ${'empBarState' . $k} = '';
                            ${'empBarCounty' . $k} = '';
                            $countyArray = [];
                            if (count($additionalStateBarInfo) > 0) {
                                if (array_key_exists(($k - 1), $additionalStateBarInfo)) {
                                    ${'empBarState' . $k} = $additionalStateBarInfo[($k - 1)]['state'];
                                    ${'empBarCounty' . $k} = $additionalStateBarInfo[($k - 1)]['county'];
                                    ${'empBarNo' . $k} = $additionalStateBarInfo[($k - 1)]['barNo'];
                                }
                            }
                            if (count($additionalCountyInfo) > 0) {
                                if (array_key_exists(${'empBarState' . $k}, $additionalCountyInfo)) $countyArray = $additionalCountyInfo[${'empBarState' . $k}];
                            }
                        }
                    }
                    ?>
                    <?php
                    /*
                    * Allow Secondary WF feature for the PCs =
                    * Dave PC, Enrollment Advisory, Law offices
                    */
                    if (($userRole == 'Manager' || $userRole == 'Administrator' || $userRole == 'Super') && (in_array($processingCompanyId, $accessSecondaryWFPC))) {
                        ?>
                        <div class="col-lg-6 mb-7">
                            <label class="font-weight-bold" for="WFID">Assigned Workflows</label>
                            <select class="form-control" data-placeholder=" - Select Workflow - " name="WFID[]"
                                    id="WFID"
                                    class="chzn-select odd" multiple="" style="width:250px;">
                                <?php
                                for ($w = 0; $w < count($PCWorkflowArray); $w++) {
                                    ?>
                                    <option value="<?php echo trim($PCWorkflowArray[$w]['WFID']) ?>" <?php echo Arrays::isSelectedArray($assignedWFIDs, trim($PCWorkflowArray[$w]['WFID'])) ?>><?php echo trim($PCWorkflowArray[$w]['WFName']) ?></option>
                                    <?php
                                }
                                ?>
                            </select>
                        </div>
                        <?php
                    }
                    ?>
                </div>
            </div>
        </div>
        <div id="ShowAllEmpPermission">
            <div class="card card-custom mb-2">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label">Editing and Visibility Permissions</h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="javascript:void(0)"
                           class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                           data-card-tool="toggle" data-section="">
                            <i class="ki icon-nm ki-arrow-down"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body accordion" style="display: none">
                    <div style="<?php if ($allowEmpToLogin == 1 || $PCAllowToCreate == 1) { ?>display:block; <?php } else { ?>display:none; <?php } ?>">
                        <div class="form-group row">
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to view "ALL" Files & See Borrower List?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="By selecting NO, the employee will not have access to the main pipeline, and will only see files that are assigned to them."></i></label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control" type="checkbox"
                                                               value="<?php echo $allowToViewAllFiles ?>" id="viewfiles"
                                                               <?php if ($allowToViewAllFiles == 1) { ?> checked="checked" <?php } ?>
                                                                    onchange="toggleSwitch('viewfiles','allowToViewAllFiles','1','0' );"/>
                                                        <input type="hidden" name="allowToViewAllFiles"
                                                               id="allowToViewAllFiles"
                                                               value="<?php echo $allowToViewAllFiles ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Would you like this employee to be able to change status and
                                        substatus if the file is editable for them?</label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToUpdateFileAdminSection == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToUpdateFileAdminSection ?>"
                                                               id="allowToUpFileAdminSec"
                                                               onchange="toggleSwitch('allowToUpFileAdminSec', 'allowToUpdateFileAdminSection','1','0' );"/>
                                                        <input type="hidden" name="allowToUpdateFileAdminSection"
                                                               id="allowToUpdateFileAdminSection"
                                                               value="<?php echo $allowToUpdateFileAdminSection ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to create files?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If NO, the user will not be able to create loan files in the system."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowEmpToCreateFiles == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowEmpToCreateFiles ?>"
                                                               id="empToCreateFile"
                                                               onchange="toggleSwitch('empToCreateFile','allowEmpToCreateFiles','1','0' );"/>
                                                        <input type="hidden" name="allowEmpToCreateFiles"
                                                               id="allowEmpToCreateFiles"
                                                               value="<?php echo $allowEmpToCreateFiles ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to edit files?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If Yes, the user will be able to edit all files, regardless of the file status-related permission. If NO, the user will NOT be able to edit any loan files in the system. If Yes for 'Follow Company Settings' the user will only be able to edit files based on the platform settings-> File Status-> User Type Permissions."></i></label>
                                    <div class="col-lg-4">
                                        <div class="radio-inline">
                                            <label for="allowBOToEditLMRFile"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="allowBOToEditLMRFile"
                                                       id="allowBOToEditLMRFile"
                                                       value="1" <?php echo Strings::isChecked($allowBOToEditLMRFile, '1') ?> >
                                                <span></span>
                                                Yes
                                            </label>
                                            <label for="allowBOToEditLMRFile1"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="allowBOToEditLMRFile"
                                                       id="allowBOToEditLMRFile1"
                                                       value="0" <?php echo Strings::isChecked($allowBOToEditLMRFile, '0') ?>>
                                                <span></span>
                                                No
                                            </label>
                                            <label for="allowBOToEditLMRFile2"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="allowBOToEditLMRFile"
                                                       id="allowBOToEditLMRFile2"
                                                       value="2" <?php echo Strings::isChecked($allowBOToEditLMRFile, '2') ?>>
                                                <span></span>
                                                <a target="_blank"
                                                   href="<?php echo CONST_BO_URL; ?>createProcessingCompany.php?pcId=<?php echo cypher::myEncryption($PCID) ?>&tabNumb=6">
                                                    Follow Company Settings</a>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to create tasks?</label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowEmpToCreateTasks == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowEmpToCreateTasks ?>"
                                                               id="empToCreateTask"
                                                               onchange="toggleSwitch('empToCreateTask','allowEmpToCreateTasks','1','0' );"/>
                                                        <input type="hidden" name="allowEmpToCreateTasks"
                                                               id="allowEmpToCreateTasks"
                                                               value="<?php echo $allowEmpToCreateTasks ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to Edit or Delete their Own Notes?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If NO, user can NOT delete their own note history entered in a client file."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowedToEditOwnNotes == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowedToEditOwnNotes ?>"
                                                               id="allowToEditNotes"
                                                               onchange="toggleSwitch('allowToEditNotes','allowedToEditOwnNotes','1','0' );"/>
                                                        <input type="hidden" name="allowedToEditOwnNotes"
                                                               id="allowedToEditOwnNotes"
                                                               value="<?php echo $allowedToEditOwnNotes ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to create branch?</label>
                                    <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowEmpToCreateBranch == 1) { ?> checked="checked" <?php } ?> id="empToCreateBranch"
                                                                   value="<?php echo $allowEmpToCreateBranch ?>"
                                                                   onchange="toggleSwitch('empToCreateBranch','allowEmpToCreateBranch','1','0' );showAndHideSwitch('allowEmpToCreateBranch', 'createBranch');"/>
                                                            <input type="hidden" name="allowEmpToCreateBranch"
                                                                   id="allowEmpToCreateBranch"
                                                                   value="<?php echo $allowEmpToCreateBranch ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to see private notes?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, the user will be able to see notes marked as private."></i></label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($seePrivate == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $seePrivate ?>" id="seePrivateid"
                                                               onchange="toggleSwitch('seePrivateid','seePrivate','1','0' );"/>
                                                        <input type="hidden" name="seePrivate" id="seePrivate"
                                                               value="<?php echo $seePrivate ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <?php if (!in_array($PCID, $accessRestrictionPC)) { ?>
                                <div class="col-6">
                                    <div class="form-group row align-items-center">
                                        <label class="col-lg-8">Allow to Excel Export File Data?
                                            <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                               data-toggle="tooltip"
                                               title="If yes, the user can export excel reports from the pipeline."></i>
                                        </label>
                                        <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowExcelDownload == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowExcelDownload ?>"
                                                                   id="excelDownload"
                                                                   onchange="toggleSwitch('excelDownload', 'allowExcelDownload','1','0' );"/>
                                                            <span></span>
                                                        </label>
                                                    </span>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <input type="hidden" name="allowExcelDownload" id="allowExcelDownload"
                                   value="<?php echo $allowExcelDownload ?>">
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to see public notes?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, the user will be able to see notes marked as public."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowEmpToSeePublicNotes == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowEmpToSeePublicNotes ?>"
                                                               id="seePublicId"
                                                               onchange="toggleSwitch('seePublicId','allowEmpToSeePublicNotes','1','0' );"/>
                                                        <input type="hidden" name="allowEmpToSeePublicNotes"
                                                               id="allowEmpToSeePublicNotes"
                                                               value="<?php echo $allowEmpToSeePublicNotes ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to Lock/unlock loan information?</label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToLockLoanFileEmpl == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToLockLoanFileEmpl ?>"
                                                               id="allowToLockLoanFileId"
                                                               onchange="toggleSwitch('allowToLockLoanFileId', 'allowToLockLoanFileEmpl','1','0' );"/>
                                                        <input type="hidden" name="allowToLockLoanFileEmpl"
                                                               id="allowToLockLoanFileEmpl"
                                                               value="<?php echo $allowToLockLoanFileEmpl ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to view Contact List?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, user will be able to create/View Contacts."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToViewContactsList == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToViewContactsList ?>"
                                                               id="allowToContactsList"
                                                               onchange="toggleSwitch('allowToContactsList', 'allowToViewContactsList','1','0' );"/>
                                                        <input type="hidden" name="allowToViewContactsList"
                                                               id="allowToViewContactsList"
                                                               value="<?php echo $allowToViewContactsList ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to view commission?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If Yes, the user can update the admin level info like status update, sub status, loan program, etc.. even if the Platform settings--> file status based permission is set to NOT Editable."></i>
                                    </label>
                                    <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowEmployeeToSeeCommission == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $allowEmployeeToSeeCommission ?>"
                                                           id="allowToSeeCommission"
                                                           onchange="toggleSwitch('allowToSeeCommission', 'allowEmployeeToSeeCommission','1','0' );"/>
                                                    <input type="hidden" name="allowEmployeeToSeeCommission"
                                                           id="allowEmployeeToSeeCommission"
                                                           value="<?php echo $allowEmployeeToSeeCommission ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to change/assign branch or broker/agent for a
                                        file?</label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToChangeOrAssignBranchForFile == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToChangeOrAssignBranchForFile ?>"
                                                               id="allowToChangeOrAssignBranch"
                                                               onchange="toggleSwitch('allowToChangeOrAssignBranch', 'allowToChangeOrAssignBranchForFile','1','0' );"/>
                                                        <input type="hidden" name="allowToChangeOrAssignBranchForFile"
                                                               id="allowToChangeOrAssignBranchForFile"
                                                               value="<?php echo $allowToChangeOrAssignBranchForFile ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to edit commission?</label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowEmployeeToEditCommission == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowEmployeeToEditCommission ?>"
                                                       id="allowToEditCommission"
                                                       onchange="toggleSwitch('allowToEditCommission', 'allowEmployeeToEditCommission','1','0' );"/>
                                                <input type="hidden" name="allowEmployeeToEditCommission"
                                                       id="allowEmployeeToEditCommission"
                                                       value="<?php echo $allowEmployeeToEditCommission ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to see billing section
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If no, they cannot see any billing related information."></i>
                                    </label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($seeBilling == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $seeBilling ?>"
                                                       id="allowToSeeBilling"
                                                       onchange="toggleSwitch('allowToSeeBilling','seeBilling','1','0' );showEditSwitch('editSwitch','seeBilling');"/>
                                                <input type="hidden" name="seeBilling" id="seeBilling"
                                                       value="<?php echo $seeBilling ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 editSwitch" style="<?php echo $showEditSwitch; ?>">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to Edit Bank or Card Information?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If Yes, the user can edit the data in the bank and card info fields under the billing tab."></i>
                                    </label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToSeeBillingSectionForFile == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToSeeBillingSectionForFile ?>"
                                                       id="allowToSeeBillingSection"
                                                       onchange="toggleSwitch('allowToSeeBillingSection', 'allowToSeeBillingSectionForFile','1','0' );"/>
                                                <input type="hidden" name="allowToSeeBillingSectionForFile"
                                                       id="allowToSeeBillingSectionForFile"
                                                       value="<?php echo $allowToSeeBillingSectionForFile ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to see Loan Officer/Broker List </i></label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowEmpToSeeAgent == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowEmpToSeeAgent ?>"
                                                       id="empToSeeAgent"
                                                       onchange="toggleSwitch('empToSeeAgent','allowEmpToSeeAgent','1','0' );showEditSwitch('createAgentSwitch','allowEmpToSeeAgent');"/>
                                                <input type="hidden" name="allowEmpToSeeAgent" id="allowEmpToSeeAgent"
                                                       value="<?php echo $allowEmpToSeeAgent ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 createAgentSwitch" style="<?php echo $showCreateAgentSwitch; ?>">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to create Loan Officer/Broker</label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowEmpToCreateAgent == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowEmpToCreateAgent ?>"
                                                       id="empToCreateAgent"
                                                       onchange="toggleSwitch('empToCreateAgent','allowEmpToCreateAgent','1','0' );"/>
                                                <input type="hidden" name="allowEmpToCreateAgent"
                                                       id="allowEmpToCreateAgent"
                                                       value="<?php echo $allowEmpToCreateAgent ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to Copy file?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title=" If turned on, users will be able to utilize the copy file feature from the pipeline."></i></label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowEmpToCopyFile == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowEmpToCopyFile ?>"
                                                       id="empToCopyFile"
                                                       onchange="toggleSwitch('empToCopyFile','allowEmpToCopyFile','1','0' );"/>
                                                <input type="text" name="allowEmpToCopyFile"
                                                       id="allowEmpToCopyFile"
                                                       value="<?php echo $allowEmpToCopyFile ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to see all emails
                                        <i class="tooltipAjax fas fa-info-circle text-primary"
                                           data-html="true"
                                           data-toggle="tooltip"
                                           title="This functionality will grant the back-office user access to all emails within any loan application,
                                             based on previously established permissions, irrespective of whether the user is assigned to the file or not."></i>

                                    </label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowEmpToSeeAllEmails == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowEmpToSeeAllEmails; ?>"
                                                       id="empToSeeAllEmails"
                                                       onchange="toggleSwitch('empToSeeAllEmails','allowEmpToSeeAllEmails','1','0' );"/>
                                                <input type="hidden" name="allowEmpToSeeAllEmails"
                                                       id="allowEmpToSeeAllEmails"
                                                       value="<?php echo $allowEmpToSeeAllEmails; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <?php if ($PCID != glPCID::PCID_PROD_CV3) { ?>
                                <div class="col-6">
                                    <div class="form-group row align-items-center">
                                        <label class="col-lg-8">Allowed to select from E-mail address ?
                                            <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                               data-toggle="tooltip"
                                               title="If enabled, the user will be able to select a user from the back office user directory to be used as the from email address in the Compose email tool."></i></label>
                                        <div class="col-lg-4">
                                            <div class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowToSelectFromEmail == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $allowToSelectFromEmail ?>"
                                                           id="allowToSelectFromEmailCheckbox"
                                                           onchange="toggleSwitch('allowToSelectFromEmailCheckbox','allowToSelectFromEmail','1','0' );showEditSwitch('backOfficersForFromEmailOptions','allowToSelectFromEmail');"/>
                                                    <input type="text"
                                                           name="allowToSelectFromEmail"
                                                           id="allowToSelectFromEmail"
                                                           value="<?php echo $allowToSelectFromEmail; ?>">
                                                    <span></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class=" p-4 backOfficersForFromEmailOptions"
                                         style="<?php echo $allowToSelectFromEmail ? '' : 'display:none;'; ?>">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Select All BackOffice Users As From Email
                                                    <i class="tooltipClass fas fa-info-circle text-primary "
                                                       data-html="true"
                                                       data-toggle="tooltip"
                                                       title="If enabled, the backoffice user type will be able to select and send from emails from any users of the list. If disabled, you will need to select the backoffice users the current backoffice user can send the email from."></i>
                                                </label>
                                                <div class="col-lg-4">
                                                    <div class="switch switch-sm switch-icon">
                                                        <label>
                                                            <input type="checkbox"
                                                                   value="1"
                                                                <?php echo $employeeObj->allowAllBackofficeUsersForFromEmail ? 'checked' : ''; ?>
                                                                   id="allowAllBackofficeUsersForFromEmailCheckbox"
                                                                   onchange="toggleSwitch('allowAllBackofficeUsersForFromEmailCheckbox','allowAllBackofficeUsersForFromEmail','1','0' );createEmployee.allowBackofficeUsersFromEmail(this);"/>
                                                            <span></span>
                                                        </label>
                                                    </div>
                                                    <input type="hidden"
                                                           name="allowAllBackofficeUsersForFromEmail"
                                                           id="allowAllBackofficeUsersForFromEmail"
                                                           value="<?php echo $employeeObj->allowAllBackofficeUsersForFromEmail; ?>"
                                                    />
                                                </div>
                                                <div class="col-lg-12 mt-2">
                                                        <?php
                                                        $employeeList = getEmployeeList::getReport([
                                                             'PCID' => $PCID
                                                            ]);
                                                        ?>
                                                    <select data-placeholder="Select Limited BackOffice Users"
                                                            class="chzn-select form-control form-controller-solid tooltipClass"
                                                            multiple=""
                                                            title="<?php echo $employeeObj->allowAllBackofficeUsersForFromEmail ?
                                                                'Uncheck Above Switch To Enable' :
                                                                'Limited BackOffice Users'; ?>"
                                                        <?php echo $employeeObj->allowAllBackofficeUsersForFromEmail ? 'disabled' : ''; ?>
                                                            name="backOfficersForFromEmail[]"
                                                            id="backOfficersForFromEmail">
                                                    <?php foreach ($employeeList['processorInfoArray'] as  $eachEmployee) { ?>
                                                        <option value="<?php echo $eachEmployee['AID']; ?>"
                                                        <?php echo in_array($eachEmployee['AID'],$employeeObj->getFromEmailUsers()) ? 'selected': ''; ?>
                                                        ><?php echo $eachEmployee['processorName'].'('.$eachEmployee['role'].')'; ?></option>
                                                <?php  } ?>
                                                    </select>
                                                </div>
                                                <div class="col-lg-12 mt-2">
                                                    Custom From Name and Email Address:
                                                    <textarea class="form-control"
                                                              placeholder="Please Enter Custom Email Address"
                                                              name="customEmailForEmail"
                                                              id="customEmailForEmail"><?php echo $employeeObj->customEmailForEmail; ?></textarea>
                                                    <span class="form-text text-muted">Example: Name(<EMAIL>);Name2(<EMAIL>)</span>
                                                </div>
                                        </div>
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Branch Email as From Email
                                                <i class="tooltipClass fas fa-info-circle text-primary "
                                                 data-html="true"
                                                 data-toggle="tooltip"
                                                 title="If enabled, the branch user type will be able to select and send from emails from any users of the list. if disabled, the branch user type will be only able to send emails from the logged in user email"></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <div class="switch switch-sm switch-icon">
                                                    <label>
                                                        <input type="checkbox"
                                                               value="1"
                                                            <?php echo $employeeObj->allowBranchEmailAsFromEmail ? 'checked' : ''; ?>
                                                               id="allowBranchEmailAsFromEmailCheckbox"
                                                               onchange="toggleSwitch('allowBranchEmailAsFromEmailCheckbox','allowBranchEmailAsFromEmail','1','0' );"
                                                               />
                                                        <span></span>
                                                    </label>
                                                </div>
                                                <input type="hidden"
                                                       name="allowBranchEmailAsFromEmail"
                                                       id="allowBranchEmailAsFromEmail"
                                                       value="<?php echo $employeeObj->allowBranchEmailAsFromEmail; ?>"
                                                       />
                                            </div>
                                        </div>
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Broker Email as From Email
                                                <i class="tooltipClass fas fa-info-circle text-primary "
                                                   data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If enabled, broker user type can select and send emails from any user in the list. If disabled, they can only send emails from the logged-in user's email."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <div class="switch switch-sm switch-icon">
                                                    <label>
                                                        <input type="checkbox"
                                                               value="1"
                                                               id="allowBrokerEmailAsFromEmailCheckbox"
                                                            <?php echo $employeeObj->allowBrokerEmailAsFromEmail ? 'checked' : ''; ?>
                                                               onchange="toggleSwitch('allowBrokerEmailAsFromEmailCheckbox','allowBrokerEmailAsFromEmail','1','0' );"
                                                        />
                                                        <span></span>
                                                    </label>
                                                </div>
                                                <input type="hidden"
                                                       name="allowBrokerEmailAsFromEmail"
                                                       id="allowBrokerEmailAsFromEmail"
                                                       value="<?php echo $employeeObj->allowBrokerEmailAsFromEmail; ?>"
                                                />
                                            </div>
                                        </div>
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Loan Officer Email as From Email
                                                <i class="tooltipClass fas fa-info-circle text-primary "
                                                   data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If enabled, loan officer user type can select and send emails from any user in the list. If disabled, they can only send emails from the logged-in user's email."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <div class="switch switch-sm switch-icon">
                                                    <label>
                                                         <input type="checkbox"
                                                                value="1"
                                                                <?php echo $employeeObj->allowLoanofficerEmailAsFromEmail ? 'checked' : ''; ?>
                                                                id="allowLoanofficerEmailAsFromEmailCheckbox"
                                                                onchange="toggleSwitch('allowLoanofficerEmailAsFromEmailCheckbox','allowLoanofficerEmailAsFromEmail','1','0' );"
                                                                />
                                                         <span></span>
                                                    </label>
                                                </div>
                                                <input type="hidden"
                                                       name="allowLoanofficerEmailAsFromEmail"
                                                       id="allowLoanofficerEmailAsFromEmail"
                                                       value="<?php echo $employeeObj->allowLoanofficerEmailAsFromEmail; ?>"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            <?php } ?>

                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to see fees in Dashboard File Status Cards ?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If enabled, the user will be able to see the sum of Origination Fees and sum of Broker Fees in the Dashboard's Files Status cards."></i></label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToSeeFeesInDashboard == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToSeeFeesInDashboard ?>"
                                                       id="allowToSeeFeesInDashboardCheckbox"
                                                       onchange="toggleSwitch('allowToSeeFeesInDashboardCheckbox','allowToSeeFeesInDashboard','1','0' );"/>
                                                <input type="text"
                                                       name="allowToSeeFeesInDashboard"
                                                       id="allowToSeeFeesInDashboard"
                                                       value="<?php echo $allowToSeeFeesInDashboard; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to set MIN ID ?
                                        <i class="tooltipAjax fas fa-info-circle text-primary "
                                           data-html="true"
                                           data-toggle="tooltip"
                                           title="If enabled, the user will be able to set the MERS ID of a file."></i></label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($employeeObj->allowMERS) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $employeeObj->allowMERS ?>"
                                                       id="allowMERSCheckbox"
                                                       onchange="toggleSwitch('allowMERSCheckbox','allowMERS','1','0' );"/>
                                                <input type="text"
                                                       name="allowMERS"
                                                       id="allowMERS"
                                                       value="<?php echo $employeeObj->allowMERS ?: 0; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-6"></div>

                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to manually set MIN ID ?
                                        <i class="tooltipAjax fas fa-info-circle text-primary "
                                           data-html="true"
                                           data-toggle="tooltip"
                                           title="If enabled, the user will be able to set the MERS ID of a file manually."></i></label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($employeeObj->manualMERS) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $employeeObj->manualMERS ?>"
                                                       id="manualMERSCheckbox"
                                                       onchange="toggleSwitch('manualMERSCheckbox','manualMERS','1','0' );"/>
                                                <input type="text"
                                                       name="manualMERS"
                                                       id="manualMERS"
                                                       value="<?php echo $employeeObj->manualMERS ?: 0; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="card card-custom mb-2">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label">Documents and Notifications</h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="javascript:void(0)"
                           class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                           data-card-tool="toggle" data-section="">
                            <i class="ki icon-nm ki-arrow-down"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body accordion" style="display: none">
                    <div class="form-group row">
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8">Notify if Borrowers/Clients upload a document?
                                    <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                       data-toggle="tooltip"
                                       title="If Yes, this user will receive notifications from doc uploads (only if they are assigned to the file)."></i>
                                </label>
                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowToGetBorrowerUploadDocsNotification == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $allowToGetBorrowerUploadDocsNotification ?>"
                                                           id="allowToGetBorrowerUploadDocs"
                                                           onchange="toggleSwitch('allowToGetBorrowerUploadDocs', 'allowToGetBorrowerUploadDocsNotification','1','0' );"/>
                                                    <input type="hidden" name="allowToGetBorrowerUploadDocsNotification"
                                                           id="allowToGetBorrowerUploadDocsNotification"
                                                           value="<?php echo $allowToGetBorrowerUploadDocsNotification ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8">Allowed to delete/replace uploaded docs/e-signed docs/binder
                                    docs?
                                    <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                       data-toggle="tooltip"
                                       title="If yes, User is allowed to delete/replace uploaded docs/e-signed docs/binder docs uploaded by everyone. If set to no, user can only delete their own uploaded docs"></i>
                                </label>
                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowUserToDeleteUploadedDocs == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $allowUserToDeleteUploadedDocs ?>"
                                                           id="allowToUploadDeleteDocs"
                                                           onchange="toggleSwitch('allowToUploadDeleteDocs','allowUserToDeleteUplodedDocs','1','0' );"/>
                                                    <input type="hidden" name="allowUserToDeleteUplodedDocs"
                                                           id="allowUserToDeleteUplodedDocs"
                                                           value="<?php echo $allowUserToDeleteUploadedDocs ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                </div>
                            </div>
                        </div>
                        <?php if (($userRole == 'Manager' || $userRole == 'Super')) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to share file?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="if Yes, Share this file is enabled on the file."></i>
                                    </label>
                                    <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($shareThisFile == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $shareThisFile ?>"
                                                           id="sharethisfile"
                                                           onchange="toggleSwitch('sharethisfile','shareThisFile','1','0' );"/>
                                                    <input type="hidden" name="shareThisFile" id="shareThisFile"
                                                           value="<?php echo $shareThisFile; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8">Allowed to send Client Portal, Login Access Information
                                    <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                       data-toggle="tooltip"
                                       title="<?php if ($isPLO == 1 || $userGroup == 'Super') { ?>If you purchased the Private Label option, your client will have access to login and view their file in real time.<?php } else { ?>This is a private label option<?php } ?>"></i>
                                </label>
                                <div class="col-lg-4">
                                    <?php if ($isPLO == 1 || $userGroup == 'Super') { ?>
                                        <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToSendHomeownerLink == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToSendHomeownerLink ?>"
                                                               id="allowToSendHomeowner"
                                                               onchange="toggleSwitch('allowToSendHomeowner', 'allowToSendHomeownerLink','1','0' );"/>
                                                        <input type="hidden" name="allowToSendHomeownerLink"
                                                               id="allowToSendHomeownerLink"
                                                               value="<?php echo $allowToSendHomeownerLink ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    <?php } else { ?>
                                        <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control" disabled
                                                               type="checkbox" <?php if ($allowToSendHomeownerLink == 1) { ?> checked="checked" <?php } ?> />
                                                        <input type="hidden" name="allowToSendHomeownerLink"
                                                               id="allowToSendHomeownerLink"
                                                               value="<?php echo $allowToSendHomeownerLink ?>">
                                                        <span></span>
                                                    </label>
                                                    <i class="tooltipAjax fas fa-lock text-danger"
                                                       title="This feature is available with Private Label package."></i>
                                                </span>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8">Send File Designation Information to Clients with Public Notes?
                                    <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                       data-toggle="tooltip"
                                       title="If you choose no, when you send emails to your clients using the public notes feature they will not receive assigned employee or assigned agent contact details I.E. Name, Phone # , and Email Address."></i>
                                </label>
                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowToSendFileDesignation == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $allowToSendFileDesignation ?>"
                                                           id="sendFileDesignation"
                                                           onchange="toggleSwitch('sendFileDesignation', 'allowToSendFileDesignation','1','0' );"/>
                                                    <input type="hidden" name="allowToSendFileDesignation"
                                                           id="allowToSendFileDesignation"
                                                           value="<?php echo $allowToSendFileDesignation ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                </div>
                            </div>
                        </div>
                        <?php if (in_array('LM', $moduleCodeArray) || in_array('SS', $moduleCodeArray)) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">List in 3rd Party Auth Form
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, employee name will show in the 3rd party authorization form."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToListIn3PartyForm == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToListIn3PartyForm ?>"
                                                               id="listIn3Party"
                                                               onchange="toggleSwitch('listIn3Party', 'allowToListIn3PartyForm','1','0' );"/>
                                                        <input type="hidden" name="allowToListIn3PartyForm"
                                                               id="allowToListIn3PartyForm"
                                                               value="<?php echo $allowToListIn3PartyForm ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8"> Allow to send Fax</label>
                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowToSendFax == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $allowToSendFax ?>"
                                                           id="allowToUserSendFax"
                                                           onchange="toggleSwitch('allowToUserSendFax', 'allowToSendFax','1','0' );"/>
                                                    <input type="hidden" name="allowToSendFax" id="allowToSendFax"
                                                           value="<?php echo $allowToSendFax ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                </div>
                            </div>
                        </div>
                        <?php if (($userRole == 'Manager' || $userRole == 'Super')) { ?>
                            <!-- New permissions added -->
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Notify if Back Office users upload a document?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control" type="checkbox"
                                                   value="<?php echo $notifyBODocUpload; ?>"
                                                   id="notifyBODocUploadCheckbox"
                                               <?php if ($notifyBODocUpload == 1) { ?> checked="checked" <?php } ?>
                                               onchange="toggleSwitch('notifyBODocUploadCheckbox', 'notifyBODocUpload','1','0' );"/>
                                            <input type="hidden" name="notifyBODocUpload" id="notifyBODocUpload"
                                                   value="<?php echo $notifyBODocUpload; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Notify if Branch users upload a document?
                                        <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                           data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control" type="checkbox"
                                                   value="<?php echo $notifyBranchDocUpload; ?>"
                                                   id="notifyBranchDocUploadCheckbox"
                                               <?php if ($notifyBranchDocUpload == 1) { ?> checked="checked" <?php } ?>
                                               onchange="toggleSwitch('notifyBranchDocUploadCheckbox', 'notifyBranchDocUpload','1','0' );"/>
                                            <input type="hidden" name="notifyBranchDocUpload" id="notifyBranchDocUpload"
                                                   value="<?php echo $notifyBranchDocUpload; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Notify if Loan Officer users upload a document?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control" type="checkbox"
                                                   value="<?php echo $notifyLODocUpload; ?>"
                                                   id="notifyLODocUploadCheckbox"
                                               <?php if ($notifyLODocUpload == 1) { ?> checked="checked" <?php } ?>
                                               onchange="toggleSwitch('notifyLODocUploadCheckbox', 'notifyLODocUpload','1','0' );"/>
                                            <input type="hidden" name="notifyLODocUpload" id="notifyLODocUpload"
                                                   value="<?php echo $notifyLODocUpload; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Notify if Broker users upload a document?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control" type="checkbox"
                                                   value="<?php echo $notifyBrokerDocUpload; ?>"
                                                   id="notifyBrokerDocUploadCheckbox"
                                               <?php if ($notifyBrokerDocUpload == 1) { ?> checked="checked" <?php } ?>
                                               onchange="toggleSwitch('notifyBrokerDocUploadCheckbox', 'notifyBrokerDocUpload','1','0' );"/>
                                            <input type="hidden" name="notifyBrokerDocUpload" id="notifyBrokerDocUpload"
                                                   value="<?php echo $notifyBrokerDocUpload; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Notify if document upload request is sent to the
                                        borrower/client?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control" type="checkbox"
                                                   value="<?php echo $notifyDocUploadRequest; ?>"
                                                   id="notifyDocUploadRequestCheckbox"
                                               <?php if ($notifyDocUploadRequest == 1) { ?> checked="checked" <?php } ?>
                                               onchange="toggleSwitch('notifyDocUploadRequestCheckbox', 'notifyDocUploadRequest','1','0' );"/>
                                            <input type="hidden" name="notifyDocUploadRequest"
                                                   id="notifyDocUploadRequest"
                                                   value="<?php echo $notifyDocUploadRequest; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Notify if new file is created?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="Will notify this user if a new file is created in their associated branches"></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control" type="checkbox"
                                                   value="<?php echo $notifyNewFileCreated; ?>"
                                                   id="notifyNewFileCreatedCheckbox"
                                                <?php if ($notifyNewFileCreated == 1) { ?> checked="checked" <?php } ?>
                                               onchange="toggleSwitch('notifyNewFileCreatedCheckbox', 'notifyNewFileCreated','1','0' );"/>
                                            <input type="hidden" name="notifyNewFileCreated" id="notifyNewFileCreated"
                                                   value="<?php echo $notifyNewFileCreated; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>


                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to message borrower?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="This restricts Back office user from sending mail or note updates to borrower, co-borrower or members of the entity."></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control" type="checkbox"
                                                   value="<?php echo $allowUserToSendMsgToBorrower;; ?>"
                                                   id="allowUserToSendMsgToBorrowerCheckbox"
                                                <?php if ($allowUserToSendMsgToBorrower == 1) { ?> checked="checked" <?php } ?>
                                               onchange="toggleSwitch('allowUserToSendMsgToBorrowerCheckbox', 'allowUserToSendMsgToBorrower','1','0' );"/>
                                            <input type="hidden" name="allowUserToSendMsgToBorrower"
                                                   id="allowUserToSendMsgToBorrower"
                                                   value="<?php echo $allowUserToSendMsgToBorrower; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>


                            <!-- //New permissions added// -->
                        <?php } ?>
                    </div>
                </div>
            </div>
            <div class="card card-custom mb-2">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label">Special Reports or Features</h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="javascript:void(0)"
                           class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                           data-card-tool="toggle" data-section="">
                            <i class="ki icon-nm ki-arrow-down"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body accordion" style="display: none">
                    <div class="form-group row">
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8">Enable Two-Factor Authentication (2FA)?
                                    <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                       data-toggle="tooltip"
                                       title="An additional layer of security for your account. A 2nd form of authentication can be sent by email or SMS message to provide you with a pin to enter on login. If you choose sms please be sure you have set your cell phone number on your profile"></i>
                                </label>
                                <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($enable2FAAuthentication == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $enable2FAAuthentication ?>"
                                                           id="allowAuthentication"
                                                           onchange="toggleSwitch('allowAuthentication', 'enable2FAAuthentication','1','0' );showEditSwitch('2FASelection','enable2FAAuthentication');"/>
                                                    <input type="hidden" name="enable2FAAuthentication"
                                                           id="enable2FAAuthentication"
                                                           value="<?php echo $enable2FAAuthentication ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                </div>
                            </div>
                            <div class="form-group row align-items-center 2FASelection" <?php if ($enable2FAAuthentication == 0) {
                                echo "style='display:none;'";
                            } ?> >
                                <div class="col-lg-12">
                                    <div class="radio-inline">
                                        <label for="TwoFATypeEmail" class="radio radio-solid font-weight-bolder">
                                            <input type="radio" name="TwoFAType" id="TwoFATypeEmail"
                                                   value="email" <?php if ($TwoFAType == 'email') {
                                                echo 'checked';
                                            } ?> >
                                            <span></span>
                                            Email
                                        </label>
                                        <label for="TwoFATypeSms" class="radio radio-solid font-weight-bolder">
                                            <input type="radio" name="TwoFAType" id="TwoFATypeSms"
                                                   value="sms" <?php if ($TwoFAType == 'sms') {
                                                echo 'checked';
                                            } ?>>
                                            <span></span>
                                            SMS
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8">Allowed to see dashboard?
                                    <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                       data-toggle="tooltip"
                                       title="If No, The entire dashboard page will be disabled. The user will go straight to their pipeline."></i>
                                </label>
                                <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowEmpToSeeDashboard == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowEmpToSeeDashboard ?>"
                                                               id="allowToSeeDashboard"
                                                               onchange="toggleSwitch('allowToSeeDashboard','allowEmpToSeeDashboard','1','0' );"/>
                                                        <input type="hidden" name="allowEmpToSeeDashboard"
                                                               id="allowEmpToSeeDashboard"
                                                               value="<?php echo $allowEmpToSeeDashboard ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                </div>
                            </div>
                        </div>
                        <?php if (PageVariables::$isPCAllowEmailCampaign && ($userRole == 'Manager' || $userGroup == glUserGroup::USER_GROUP_SUPER)) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to mass email</label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowEmailCampaign == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowEmailCampaign ?>"
                                                               id="emailCampaign"
                                                               onchange="toggleSwitch('emailCampaign','allowEmailCampaign','1','0' );"/>
                                                        <input type="hidden" name="allowEmailCampaign"
                                                               id="allowEmailCampaign"
                                                               value="<?php echo $allowEmailCampaign ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if (($userRole == 'Manager' || $userRole == 'Super')) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to mass update
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="This will enable an additional column in the main pipeline. For Managers, they will be able to do functions which include, deactivating files, update status, Assign Employees and many others. For Non-Managers, Branch(s), and Agents (Loan Officer and Brokers), they will only be allowed to Deactivate files.."></i></label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToMassUpdate == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToMassUpdate ?>"
                                                       id="allowToMassUpdateCheckbox"
                                                       onchange="toggleSwitch('allowToMassUpdateCheckbox','allowToMassUpdate','1','0' );"/>
                                                <input type="text"
                                                       name="allowToMassUpdate"
                                                       id="allowToMassUpdate"
                                                       value="<?php echo $allowToMassUpdate; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to see offers tab?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="if Yes, offers tab is enabled."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToSubmitOffer == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToSubmitOffer ?>"
                                                               id="allowToSubmitOffer"
                                                               onchange="toggleSwitch('allowToSubmitOffer','allowtosubmitoffer','1','0' );"/>
                                                        <input type="hidden" name="allowToSubmitOffer"
                                                               id="allowtosubmitoffer"
                                                               value="<?php echo $allowToSubmitOffer; ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if (($userRole == 'Super' || $userRole == 'Manager') && $isPCActiveAloware == 1) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow to access CallWise
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="CallWise is a 3rd party service that allows your team to make calls, and see & send text messages inside the loan file--> CallWise tab. Contact us to activate this service."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowEmpToCreateAloware == 1) { ?> checked="checked" <?php } ?>
                                                               id="empToCreateAloware"
                                                               value="<?php echo $allowEmpToCreateAloware ?>"
                                                               onchange="toggleSwitch('empToCreateAloware','allowEmpToCreateAloware','1','0' );"/>
                                                        <input type="hidden" name="allowEmpToCreateAloware"
                                                               id="allowEmpToCreateAloware"
                                                               value="<?php echo $allowEmpToCreateAloware ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="PHP_SELF"
                                   value="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                            <input type="hidden" name="AID" value="<?php echo $AID; ?>">
                            <input type="hidden" name="emailaloware" value="<?php echo $email; ?>">
                        <?php } ?>
                        <?php if ($allowPCUsersToMarketPlace) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Enable Marketplace Tab?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, user will be able to use the marketplace feature."></i>
                                    </label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToViewMarketPlace == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToViewMarketPlace ?>"
                                                               id="allowToViewMarketPlaceForm"
                                                               onchange="toggleSwitch('allowToViewMarketPlaceForm', 'allowToViewMarketPlace','1','0' );"/>
                                                        <input type="hidden" name="allowToViewMarketPlace"
                                                               id="allowToViewMarketPlace"
                                                               value="<?php echo $allowToViewMarketPlace ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if (($userRole == 'Super' || $userRole == 'Manager') && (in_array('LM', $moduleCodeArray) || in_array('SS', $moduleCodeArray))) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Subscribe to HOME Report</label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($subscribeToHOME == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $subscribeToHOME ?>"
                                                               id="subscribeToHOMEForPC"
                                                               onchange="toggleSwitch('subscribeToHOMEForPC', 'subscribeToHOME','1','0' );"/>
                                                        <input type="hidden" name="subscribeToHOME" id="subscribeToHOME"
                                                               value="<?php echo $subscribeToHOME ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ((in_array('LM', $moduleCodeArray) || in_array('SS', $moduleCodeArray) || in_array('MF', $moduleCodeArray)) && $isDIYBranch == 1) { ?>
                            <div class="col-6 changeDIYPlanDiv">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to upgrade/downgrade DIY clients sometimes?</label>
                                    <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($changeDIYPlan == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $changeDIYPlan ?>"
                                                               id="allowToChangeDIY"
                                                               onchange="toggleSwitch('allowToChangeDIY','changeDIYPlan','1','0' );"/>
                                                        <input type="hidden" name="changeDIYPlan" id="changeDIYPlan"
                                                               value="<?php echo $changeDIYPlan ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if (in_array($PCID, $glRAMAccessPC) && $userRole == 'Super') { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allowed to access RAM?</label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control"
                                                   type="checkbox" <?php if ($allowEmpToAccessRAM == 1) { ?> checked="checked" <?php } ?>
                                                   value="<?php echo $allowEmpToAccessRAM ?>"
                                                   id="allowToAccessRAM"
                                                   onchange="toggleSwitch('allowToAccessRAM','allowEmpToAccessRAM','1','0' );"/>
                                            <input type="hidden" name="allowEmpToAccessRAM" id="allowEmpToAccessRAM"
                                                   value="<?php echo $allowEmpToAccessRAM ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($pcAcqualifyStatus > 0 && $pcAcqualifyId > 0) { ?>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow To View Credit Screen?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, user will be able to View Credit Screen"></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control"
                                                   type="checkbox" <?php if ($allowToViewCreditScreening == 1) { ?> checked="checked" <?php } ?>
                                                   value="<?php echo $allowToViewCreditScreening ?>"
                                                   id="allowToViewCreditMenu"
                                                   onchange="toggleSwitch('allowToViewCreditMenu', 'allowToViewCreditScreening','1','0' );"/>
                                            <input type="hidden" name="allowToViewCreditScreening"
                                                   id="allowToViewCreditScreening"
                                                   value="<?php echo $allowToViewCreditScreening ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if (strpos($role, 'Manager') === 0 && $userRole != 'Super') {
                            //don't show permission for add branch or agent because manager and manager (admin rights) always can add them.  Since this is hidden from these managers i allow super to reach the questions to fix the child question of exclusively assign manager to new branch for those managers with the setting turned on. This strpos catches manager, and manager (admin rights).  Other managers will be higher than 0, and other employees will be null so i needed to use === to differenciate
                        } else { ?>
                            <div class="col-6" id="createBranch"
                                 style="display: <?php if ($allowEmpToCreateBranch == 1) { ?>block<?php } else { ?>none<?php } ?>">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Convert the new branch created by this employee as their
                                        assigned branch
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="When YES, the employee can create branches that will automatically be linked to their profile, and all unlinked branches will be removed from their view"></i>
                                    </label>
                                    <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($convertNewBRIntoEmpOwnBR == 1) { ?> checked="checked" <?php } ?> id="empToconvertBranch"
                                                       value="<?php echo $convertNewBRIntoEmpOwnBR ?>"
                                                       onchange="toggleSwitch('empToconvertBranch','convertNewBRIntoEmpOwnBR','1','0' );"/>
                                                <input type="hidden" name="convertNewBRIntoEmpOwnBR"
                                                       id="convertNewBRIntoEmpOwnBR"
                                                       value="<?php echo $convertNewBRIntoEmpOwnBR ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if ($allowAutomation) { ?>
                            <div class="col-12 mb-2">
                                <div class="separator separator-solid"></div>
                            </div>
                            <div class="col-6">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Display any automations that are triggered by this user?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, user will be able to see the automations that are triggered."></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control"
                                                   type="checkbox" <?php if ($allowToViewAutomationPopup == 1) echo 'checked'; ?>
                                                   value="<?php echo $allowToViewAutomationPopup; ?>"
                                                   id="allowToViewAutomationPopup"
                                                   onchange="toggleSwitch('allowToViewAutomationPopup', 'allowToViewAutomationPopupVal','1','0' );
                                                   showEditSwitch('allowToControlAutomationPopupDiv','allowToViewAutomationPopupVal');"/>
                                            <input type="hidden" name="allowToViewAutomationPopup"
                                                   id="allowToViewAutomationPopupVal"
                                                   value="<?php echo $allowToViewAutomationPopup; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 allowToControlAutomationPopupDiv <?php if ($allowToViewAutomationPopup == 0) echo 'hidden'; ?>">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-8">Allow user to control triggered automations?
                                        <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                           data-toggle="tooltip"
                                           title="If yes, user will be able to control automations that will be triggered."></i>
                                    </label>
                                    <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control"
                                                   type="checkbox" <?php if ($allowToControlAutomationPopup == 1) echo 'checked'; ?>
                                                   value="<?php echo $allowToControlAutomationPopup; ?>"
                                                   id="allowToControlAutomationPopup"
                                                   onchange="toggleSwitch('allowToControlAutomationPopup', 'allowToControlAutomationPopupVal','1','0' );"/>
                                            <input type="hidden" name="allowToControlAutomationPopup"
                                                   id="allowToControlAutomationPopupVal"
                                                   value="<?php echo $allowToControlAutomationPopup; ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="col-12 mb-2">
                            <div class="separator separator-solid"></div>
                        </div>
                        <div class="col-6">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-8">Enable Pricing Engine?</label>
                                <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" <?php if ($pcPriceEngineStatus == 0) {
                                                    echo 'disabled';
                                                } ?>
                                                       type="checkbox" <?php if ($userPriceEngineStatus == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $userPriceEngineStatus ?>"
                                                       id="userPriceEngineStatusCheckbox"
                                                       onchange="toggleSwitch('userPriceEngineStatusCheckbox', 'userPriceEngineStatus','1','0' );
                                                       showEditSwitch('priceEngineSwitch','userPriceEngineStatus');"/>
                                                <input type="hidden" name="userPriceEngineStatus"
                                                       id="userPriceEngineStatus"
                                                       value="<?php echo $userPriceEngineStatus ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-6"></div>
                        <div class="col-6 priceEngineSwitch" style="<?php echo $showPriceEngineSwitch; ?>">
                            <div class="form-group row">
                                <label class="col-3">Login</label>
                                <div class="col-6">
                                    <input type="text" name="loanpassLogin" id="loanpassLogin" class="form-control"
                                           autocomplete="off" value="<?php echo $loanpassLogin; ?>"
                                        <?php if ($pcPriceEngineStatus == 0) {
                                            echo 'disabled';
                                        } ?>>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 priceEngineSwitch" style="<?php echo $showPriceEngineSwitch; ?>">
                            <div class="form-group row">
                                <label class="col-3">Password</label>
                                <div class="col-6">
                                    <input type="password" name="loanpassPassword" id="loanpassPassword"
                                           class="form-control" autocomplete="off"
                                           value="<?php echo $loanpassPassword; ?>"
                                        <?php if ($pcPriceEngineStatus == 0) {
                                            echo 'disabled';
                                        } ?>>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if (($userRole == 'Manager' || $userRole == 'Super') && (PageVariables::$glThirdPartyServices > 0 || PageVariables::$glThirdPartyServicesLegalDocs > 0)) { ?>
            <div class="card card-custom mb-2">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label">Integrations</h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="javascript:void(0)"
                           class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                           data-card-tool="toggle" data-section="">
                            <i class="ki icon-nm ki-arrow-down"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body accordion" style="display: none">
                    <div class="form-group row">
                                <?php if (PageVariables::$glThirdPartyServices > 0) { ?>
                                    <div class="card card-custom col-md-12 mb-3" >
                                        <div class="card-header card-header-tabs-line bg-gray-100">
                                            <div class="card-title">
                                                <h3 class="card-label">Credit Reporting Agencies:</h3>
                                            </div>
                                            <div class="card-toolbar" id="craHeader" onclick="Validation.controlChildElements('craHeader','craProducts','showHide')">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($thirdPartyServices == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $thirdPartyServices ?>"
                                                               id="thirdPartyLink" onchange="toggleSwitch('thirdPartyLink','thirdPartyServices','1','0' );showEditSwitch('thirdPartySwitch','thirdPartyServices');"/>
                                                        <input type="hidden" name="thirdPartyServices"
                                                               id="thirdPartyServices"
                                                               value="<?php echo $thirdPartyServices ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                <div class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass" data-card-tool="toggle" data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                                                    <i class="ki icon-nm ki-arrow-down"></i>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="row pt-4" id="craProducts" style="display: none;">
                                            <?php foreach ($glThirdPartyServicesCRA as $cra => $craValue) {
                                                if (in_array($cra, $thirdPartyServiceCSRArray)) {
                                                    $thirdPartyPassword = $thirdPartyServicesUserDetails['cra_'.$cra]['password'];
                                            ?>
                                                <div class="col-6 thirdPartySwitch pr-6" style="<?php echo $showThirdPartyEditSwitch; ?>" >
                                                    <div class="form-group row align-items-center">
                                                        <h6 class="col-lg-12"> <?= $craValue->Name ?> </h6>
                                                        <label class="col-lg-2">User Name</label>
                                                        <div class="col-lg-10">
                                                            <input type="text" name="<?= 'thirdPartyServicesData['.$cra.'][username]' ?>" id="thirdPartyUsername" class="form-control" value="<?= $thirdPartyServicesUserDetails['cra_'.$cra]['username']; ?>">
                                                        </div>
                                                        <label class="col-lg-2">Password</label>
                                                        <div class="col-lg-10">
                                                            <input type="text" autocomplete="off" name="<?= 'thirdPartyServicesData['.$cra.'][password]' ?>" id="thirdPartyPassword" class="form-control" value="<?= $thirdPartyPassword ? '*****' . substr($thirdPartyPassword, -4) : ''; ?>">
                                                        </div>
                                                    </div>
                                                    <hr>
                                                </div>

                                            <?php } } ?>
                                        </div>
                                    </div>
                                <?php } if (PageVariables::$glThirdPartyServicesLegalDocs > 0) { ?>
                                    <div class="card card-custom col-md-12 mb-3" >
                                        <div class="card-header card-header-tabs-line bg-gray-100">
                                            <div class="card-title">
                                                <h3 class="card-label">Legal Docs</h3>
                                            </div>
                                            <div class="card-toolbar" id="legalDocsHeader" onclick="Validation.controlChildElements('legalDocsHeader','legalDocsProducts','showHide')">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($thirdPartyServicesLegalDocs == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $thirdPartyServicesLegalDocs ?>"
                                                               id="thirdPartyLinkLegalDocs"
                                                               onchange="toggleSwitch('thirdPartyLinkLegalDocs', 'thirdPartyServicesLegalDocs','1','0' );showEditSwitch('thirdPartySwitchLegalDocs','thirdPartyServicesLegalDocs');"/>
                                                        <input type="hidden" name="thirdPartyServicesLegalDocs"
                                                               id="thirdPartyServicesLegalDocs"
                                                               value="<?php echo $thirdPartyServicesLegalDocs ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                                <div class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass" data-card-tool="toggle"
                                                     data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card" style="visibility: hidden;">
                                                    <i class="ki icon-nm ki-arrow-down"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                    </div>
                </div>
            </div>
            <?php } ?>
        </div>
        <!--end::Accordion::1-->
        <!-- maindiv close-->
        <?php
        if ($userRole == 'Super') {
            $viewOpt = 1;
        } else {
            $viewOpt = 0;
        }

        if (($userRole == 'Super') || ($processorId > 0) || ($PCAllowToCreate == 1) || ($userRole == 'Manager')) { ?>
            <div class="row d-flex justify-content-center">
                <button type="submit" name="saveProcessor" id="saveProcessor"
                        class="btn btn-primary font-weight-bold">Save
                </button>
                <button type="reset" name="reset" id="reset" class="btn btn-light font-weight-bold ml-2">Reset
                </button>
            </div>
            <?php
        }
        ?>

    </form>
    <script>
        $("input[name='allowBOToEditLMRFile']").click(function () {
            var val = $("input[name=allowBOToEditLMRFile]:checked").val();
            if (val == 1 || val == 2) {
                $('#allowToupdateFileAndClientRow').css('display', 'table-row')
            } else {
                $('#allowToupdateFileAndClientRow').hide();
            }
        });
        cnt = 1;

        function populateStateCounty(formName, srcName, targetName) {
            var propertyState = '', xmlDoc = '', countyDataArray = [];
            var propertyCountyArray = [];

            var propertyCountyArray = getNewObject(targetName);
            propertyCountyArray.options[0] = new Option("- Select -", "", false);

            try {
                eval("propertyState = document." + formName + "." + srcName + ".value");
            } catch (e) {
            }
            if (propertyState != "") {
                var url = "../backoffice/getCountyData.php";
                var qstr = "sc=" + propertyState;

                try {
                    xmlDoc = getXMLDoc(url, qstr);
                } catch (e) {
                }
                try {
                    countyDataArray = xmlDoc.getElementsByTagName("county");
                } catch (e) {
                }

                for (var c = 0; c < countyDataArray.length; c++) {
                    var countyName = "";
                    try {
                        countyName = countyDataArray[c].getElementsByTagName("countyName")[0].firstChild.nodeValue;
                    } catch (e) {
                    }
                    propertyCountyArray.options[c + 1] = new Option(countyName, countyName, false, false);
                }
            }
        }

        function checkCountyData(formName, fldName) {
            eval("var obj = document." + formName + "." + fldName);
            if (obj.options.length == 1) {
                alert('Please select any state.');
            }
        }

        function showAndHideSwitch(src, target) {
            var createBranch = 0;
            eval("createBranch = document.processorForm." + src + ".value");
            if (createBranch == 1) {
                $("#" + target).css("display", "table-row");
            } else {
                $("#" + target).css("display", "none");
            }
        }

        function showEditSwitch(targetName, parent) {
            var pVal = $('#' + parent).val();
            if (pVal == 1) {
                $("." + targetName).css("display", "block");
            } else {
                if (parent == 'allowToViewAutomationPopupVal' && pVal == 0) {
                    $('#allowToControlAutomationPopupVal').val(0);
                    $('#allowToControlAutomationPopup').prop('checked', false);
                }
                $("." + targetName).css("display", "none");
            }
        }
    </script>

    <script>
        var LWFormControls = function () {

            var _formSubmitValidation = function () {
                var formIdToSubmit = $('#processorForm');
                formIdToSubmit.validate({
                    ignore: ".ignoreValidation",
                    rules: {
                        email: {
                            required: true,
                            email: true
                        },
                        confirmEmail: {
                            equalTo: "#email"
                        },
                        processorName: "required",
                        role: "required",
                        pwd: {
                            required: true,
                            minlength: 6
                        },
                        confirmPwd: {
                            equalTo: "#pwd"
                        },
                        assignedToProcessingCompany: "required",
                        cellNumber: "required",
                    },
                    messages: {
                        email: {
                            required: "Please Enter E-mail address.",
                            email: "Please Enter Valid E-mail address."
                        },
                        confirmEmail: "Please Confirm Your E-mail address.",
                        processorName: "Please Enter Employee Name",
                        role: "Please Select Employee Role",
                        pwd: {
                            required: "Enter Your Password.",
                            minlength: "Your Password Should be at least 6 Characters."
                        },
                        confirmPwd: "Password does not match.",
                        assignedToProcessingCompany: "Please assigned to processing company.",
                        cellNumber: "Please enter cell number",
                    },
                    errorElement: "em",
                    errorPlacement: function (error, element) {
                        // Add the `invalid-feedback` class to the error element
                        error.addClass("invalid-feedback");

                        if (element.prop("type") === "checkbox") {
                            error.insertAfter(element.next("label"));
                        } else {
                            if (element.prop("name") == 'role') {
                                element.next('.input-group-append').after(error);
                            } else {
                                error.insertAfter(element);
                            }
                        }
                    },
                    highlight: function (element, errorClass, validClass) {
                        $(element).addClass("is-invalid").removeClass("is-valid");
                        $(element).closest('.card-body').show();
                    },
                    unhighlight: function (element, errorClass, validClass) {
                        $(element).addClass("is-valid").removeClass("is-invalid");
                    },
                    submitHandler: function (form) {

                        let formData = new FormData(form);
                        let url = $(form).attr('action');
                        let method = $(form).attr('method');

                        $.ajax({
                            url: url,
                            type: method,
                            data: formData,
                            contentType: false,
                            processData: false,
                            beforeSend: function () {
                                BlockDiv('employeeCreateDiv');
                            },
                            complete: function () {
                                UnBlockDiv('employeeCreateDiv');
                            },
                            success: function (response) {
                                let res = JSON.parse(response);
                                let notificationType = parseInt(res.code) === 100 ? 'success' : 'error';
                                toastrNotification(res.msg, notificationType);
                                if (parseInt(res.code) === 100) {
                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 100);
                                }
                            },
                            error: function (jqXhr, textStatus, errorMessage) {
                                $.alert({
                                    icon: 'fa fa-warning',
                                    type: 'red',
                                    title: 'Alert!',
                                    content: errorMessage,
                                });
                            }
                        });
                    }
                });
            }
            return {
                // public functions
                init: function () {
                    _formSubmitValidation();
                }
            };
        }();

        $(document).ready(function () {
            LWFormControls.init();
        });

        function checkIsDIY() {
            var branchids = $('#assignedToCompany').val();
            var val = String(branchids).split(',');
            var PLOCount = 0;
            var branchUserTypeArray = <?php echo json_encode($branchUserTypeArray); ?>;
            console.log(branchUserTypeArray);
            for (var i = 0; i < val.length; i++) {
                if (val[i] in branchUserTypeArray) {
                    if (String(branchUserTypeArray[val[i]]) == "PLO") {
                        PLOCount++;
                    }
                }
            }
            if (branchids == '') {
                $.each(branchUserTypeArray, function (key, userTypeVal) {
                    if (userTypeVal == "PLO") {
                        PLOCount++;
                    }
                });
            }
            if (PLOCount > 0) {
                $('.changeDIYPlanDiv').show();
            } else {
                $('.changeDIYPlanDiv').hide();
            }
        }

        //Expand All / Collapse All
        $('#toggle-all').click(function () {
            var status = $(this).text();
            if (status == 'Close All') {
                $(this).text('Open All');
                $('.accordion').hide();
                $('.icon-nm').addClass('ki-arrow-down').removeClass('ki-arrow-up');
            } else if (status == 'Open All') {
                $(this).text('Close All');
                $('.accordion').show();
                $('.icon-nm').addClass('ki-arrow-up').removeClass('ki-arrow-down');
            }
        });
    </script>

    <!-- employeeForm.php -->
