<?php
use models\portals\PublicPage;
use models\JSCompiler;
session_start();
require './includes/util.php';
$config = 'borrower';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <link href="/assets/images/favicon-whitelabel.png" rel="SHORTCUT ICON"/>
    <title> Submit/Revise Scope of Work</title>
    <?php
    PublicPage::Init();
    echo JSCompiler::scripts();
    echo JSCompiler::stylesheets();
    ?>
    <script src="/assets/js/submitReviseSow.js"></script>
</head>

<body translate="no">

<style>
    html {
        font-size: 12px;
    }
    body {
        background-color: #f8f9fa; /* Light gray background for the page */
        font-size: 1.2rem; /* Increased base font size */
    }
    .progress-bar-container {
        overflow: hidden;
    }
    .progress-bar-container .progress-step {
        position: relative;
        z-index: 1; /* Badges above connectors */
        min-width: 120px; /* Increased min-width for progress step text */
    }
    .progress-connector {
        height: 2px;
        background-color: #dee2e6; /* Bootstrap's default border color */
        position: relative;
        top: 16px; /* Pushes the connector down to align with the badge center */
        flex-grow: 1;                 /* Allow this item to grow */
        flex-shrink: 1;               /* Allow shrinking if necessary, though 0 might also work if space is guaranteed */
        flex-basis: 0%;
        margin-left: -10px;
        margin-right: -10px;
        z-index: 0; /* Optional: ensure it's behind badges if stacking issues occur */
    }
    .progress-step .badge-pill {
        width: 35px; /* Increased badge size */
        height: 35px; /* Increased badge size */
        line-height: 1;
        font-size: 1rem; /* Increased font size inside badge */
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;
        margin-right: auto;
    }
    .progress-step .mt-2 {
        font-size: 1.2rem;
    }

    .table th {
        font-size: 1rem;
    }
    .table th, .table td {
        vertical-align: middle;
    }
</style>

<div class="container mt-4 mb-5">
        <!-- Progress Steps -->
        <div class="d-flex justify-content-around align-items-start mb-5 pb-3 progress-bar-container">
        <div class="progress-connector"></div>
        <div class="progress-step text-center">
                <span class="badge badge-secondary badge-pill p-2">2</span>
                <div class="mt-2 small">Categories</div>
            </div>
            <div class="progress-connector"></div>
            <div class="progress-step text-center">
                <span class="badge badge-primary badge-pill p-2">3</span>
                <div class="mt-2 small font-weight-bold">Line Items</div>
            </div>
            <div class="progress-connector"></div>
        </div>

        <!-- Main Content Card -->
        <div class="card p-4 p-md-5 shadow-sm">
            <h2 class="mb-2 text-dark">Enter Line Items for Each Category</h2>
            <p class="text-muted mb-4" style="font-size: 1.05rem;">Add detailed line items for each project category. Enter cost, completion percentage, and notes for each item.</p>

            <input type="hidden" id="pcid" value="<?php echo htmlspecialchars($_REQUEST['pcid'] ?? ''); ?>">
            <div id="lineItemCategoriesContainer">
                <!-- Line items will be loaded here dynamically -->
            </div>

            <!-- Next Button -->
            <div class="mt-5">
                <button type="button" id="save-sow" class="btn btn-primary btn-lg btn-block">Save</button>
            </div>

        </div>
    </div>

<?php
$lineItemConfig = [
    'showDescription' => false,
    'showCost' => true,
    'showCompleted' => true,
    'showNotes' => true,
    'showActions' => false,
];
include './backoffice/drawManagement/partials/_line-item-category-card.php';
include './backoffice/drawManagement/partials/_line-item-row-borrower.php';
?>

    <div class="modal fade" id="noteModal" tabindex="-1" role="dialog" aria-labelledby="noteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="noteModalLabel">Edit Notes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <textarea id="noteTextarea" class="form-control" rows="4" placeholder="Enter notes…"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Cancel</button>
                <button type="button" id="saveNoteBtn" class="btn btn-primary btn-sm">Save</button>
            </div>
            </div>
        </div>
    </div>

    <script>
    $(function(){
    let currentBtn;

    // when the icon button opens the modal
    $('.note-btn').on('click', function(){
        currentBtn = $(this);
        const noteText = currentBtn.data('note') || '';
        $('#noteTextarea').val(noteText);
    });

    // when Save is clicked
    $('#saveNoteBtn').on('click', function(){
        const updated = $('#noteTextarea').val();
        // store it back on the button (or send via AJAX to your server)
        currentBtn.data('note', updated);

        // optionally change the icon to reflect “has notes”
        if (updated.trim()) {
        currentBtn.find('i')
            .removeClass('fa-sticky-note')
            .addClass('fa-sticky-note');
            // you could swap to fa-sticky-note-solid if you use Pro…
        }

        $('#noteModal').modal('hide');
    });
    });
    </script>
</body>
</html>
