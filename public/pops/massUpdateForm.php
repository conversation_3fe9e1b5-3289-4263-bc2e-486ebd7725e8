<?php

use models\cypher;
use models\pops\massUpdateForm;
use models\Request;
use models\standard\Strings;

global $userRole, $userNumber, $userGroup, $allowEmailCampaign, $isSysNotesPrivate;

require('popsConfig.php');
require '../backoffice/initPageVariables.php';
require '../backoffice/getPageVariables.php';
require 'checkSessionValue.php';
/** Redirect to login page when the user sessions end **/
global $allowAutomation;
$LMRId = 0;
$LMRResponseId = 0;
$PCID = 0;
if (isset($_REQUEST['LMRId'])) $LMRId = intval(cypher::myDecryption(Request::GetClean('LMRId')));
if (isset($_REQUEST['PCID'])) $PCID = intval(Request::GetClean('PCID'));
if (isset($_REQUEST['LMRResponseId'])) $LMRResponseId = intval(cypher::myDecryption(Request::GetClean('LMRResponseId')));
$activeFile = isset($_REQUEST['activeFile']) ? intval(Request::GetClean('activeFile')) : 1;
$inArray = [
    'LMRId' => $LMRId,
    'LMRResponseId' => $LMRResponseId,
    'userRole' => $userRole,
    'userNumber' => $userNumber,
    'userGroup' => $userGroup,
    'allowEmailCampaign' => $allowEmailCampaign,
    'fileType' => htmlspecialchars(Request::GetClean('fileType')),
    'isSysNotesPrivate' => $isSysNotesPrivate,
    'PCID' => $PCID,
    'activeFile' => $activeFile
];

$massUpdate = new massUpdateForm();
echo $massUpdate->getFormHtml(1, $inArray);
?>
<script>
    //console.log('form');
    var LWFormControls = function () {
        let _formSubmitValidation = function () {
            let formIdToSubmit = $('#massUpdateForm');
            jQuery.validator.addMethod(
                "validatedMassUpdateForm",
                function () {
                    return validateMassEmailForm1();
                },
                jQuery.validator.messages.validatedMassUpdateForm
            );
            formIdToSubmit.validate({
                ignore: ".ignoreValidation",
                rules: {
                    'validationField': {
                        validatedMassUpdateForm: true,
                    }
                },
                messages: {
                    'validationField': {
                        validatedMassUpdateForm: "",
                    },
                },
                errorElement: "em",
                errorPlacement: function (error, element) {
                    // Add the `invalid-feedback` class to the error element
                    error.addClass("invalid-feedback");

                    if (element.prop("type") === "checkbox") {
                        error.insertAfter(element.next("label"));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function (element) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function (element) {
                    $(element).addClass("is-valid").removeClass("is-invalid");
                },
                submitHandler: function () {
                    //check if PC has automation feature enabled
                    var allowAutomation = '<?php echo $allowAutomation; ?>';
                    var lastUpdatedParam = $('#lastUpdatedParam').val();
                    var lastUpdatedFss = $('#lastUpdatedFss').val();
                    if(allowAutomation == 1 && lastUpdatedParam != '') {
                        let formIdToSubmit = $('#massUpdateForm');
                        let ajaxUrl = $(formIdToSubmit).attr('action');
                        var PCID = $("input[name=selectedPCID]").val();
                        var LMRResponseIds = $("input[name=LMRResponseIds]").val();
                        var triggerRule = $('#triggerRule').val();
                        var opn = $('input[name="opn"]:checked').val();
                        var priStatus = $('select[name="primaryStatus"]').find(":selected").val();
                        //allow automation rule repeat
                        //code to check for user conformation
                        if ($('#selMassOpt').val() === 'All') {
                            $.confirm({
                                icon: 'fa fa-warning',
                                closeIcon: true,
                                title: 'Confirm',
                                content: "You are performing a mass update on " + $('#allCount').val() + " file(s). Are you sure you want to proceed?",
                                type: 'red',
                                backgroundDismiss: true,
                                buttons: {
                                    Yes: function () {
                                        sendMassUpdateRequest();
                                    },
                                    Cancel: function () {
                                        // Optional cancel logic
                                    }
                                }
                            });
                        } else {
                            sendMassUpdateRequest();
                        }

                    }
                    else {
                        if($('#selMassOpt').val() === 'All'){
                            $.confirm({
                                icon: 'fa fa-warning',
                                closeIcon: true,
                                title: 'Confirm',
                                content: "You are performing a mass update on "+$('#allCount').val()+" file(s). Are you sure you want to proceed?",
                                type: 'red',
                                backgroundDismiss: true,
                                buttons: {
                                    Yes: function () {
                                        postData();
                                    },
                                    Cancel: function () {

                                    }
                                },
                            });
                        } else {
                            postData();
                        }
                    }
                }
            });
        }
        return {
            // public functions
            init: function () {
                _formSubmitValidation();
            }
        };
    }();

    function postData()
    {
        let formIdToSubmit = $('#massUpdateForm');
        let ajaxUrl = $(formIdToSubmit).attr('action');
        let formData = new FormData($('#massUpdateForm')[0]);

        $.ajax({
            url: ajaxUrl,
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function () {
                BlockDiv('modal-content-id');
            },
            complete: function () {
                UnBlockDiv('modal-content-id');
            },
            success: function (response) {
                closeModal();
                let res = JSON.parse(response);
                if (parseInt(res.code) === 100) {
                    toastrNotification(res.msg, 'success');
                    setTimeout(function () {
                        location.reload();
                    }, 1000);
                } else {
                    toastrNotification(res.msg, 'error');
                }
            },
            error: function (jqXhr, textStatus, errorMessage) {
                toastrNotification(errorMessage, 'error');
            }
        });
    }

    function sendMassUpdateRequest() {
        let lastUpdatedParam = $('#lastUpdatedParam').val();
        let lastUpdatedFss = $('#lastUpdatedFss').val();
        let formIdToSubmit = $('#massUpdateForm');
        let ajaxUrl = $(formIdToSubmit).attr('action');
        let PCID = $("input[name=selectedPCID]").val();
        let LMRResponseIds = $("input[name=LMRResponseIds]").val();
        let triggerRule = $('#triggerRule').val();
        let opn = $('input[name="opn"]:checked').val();
        let priStatus = $('select[name="primaryStatus"]').find(":selected").val();
        let eventType = '';
        if(lastUpdatedParam == 'PFS') {
            eventType = 'File Status';
        }
        else if (lastUpdatedParam == 'FSS') {
            eventType = 'File Sub Status';
        }
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            datatype: 'json',
            data: {
                PCID: PCID,
                LMRResponseIds: LMRResponseIds,
                opn: opn,
                lastUpdatedParam: lastUpdatedParam,
                lastUpdatedFss: lastUpdatedFss,
                primaryStatus: priStatus,
                allowRepeat: 'check',
                triggerRule: triggerRule,
            },
            beforeSend: function () {
                // Optional: console.log(this.data);
            },
            success: function (resp) {
                if (resp == '-1') {
                    var msgTxt = 'You have previously triggered your automation rules by updating the ' + eventType +
                        '. Would you like to trigger your automation rules again?';
                    $.confirm({
                        icon: 'fas fa-exclamation-triangle text-danger',
                        closeIcon: false,
                        title: 'Confirm',
                        content: msgTxt,
                        type: 'red',
                        backgroundDismiss: false,
                        buttons: {
                            yes: {
                                text: 'Yes',
                                btnClass: 'btn-green',
                                action: function () {
                                    $('#triggerRule').val('Yes');
                                    postData();
                                }
                            },
                            cancel: {
                                text: 'No',
                                action: function () {
                                    postData();
                                }
                            }
                        }
                    });
                } else {
                    $('#triggerRule').val('Yes');
                    postData();
                }
            },
            error: function () {
                // Optional error handler
            }
        });
    }

    $(document).ready(function () {
        LWFormControls.init();
        $('#exampleModal1').on('hide.bs.modal', function () {
            $("input[name='bulk']").prop('checked', false);
            $(".singleCheckFile").prop('checked', false);
        });
        /* mypipeline - Mass Update popup */
        /* This is used in automation */
        $(document).on('change', '.primaryStatus', function () {
            let psVal = $(this).val();
            if ($('#lastUpdatedParam').length > 0 && psVal != '') {
                $('#lastUpdatedParam').val('PFS');
                if ($('#lastUpdatedFss').length > 0) $('#lastUpdatedFss').val('');
            }
        });
        //FSS
        $(document).on('change', '.uss', function () {
            var id = $(this).attr('for');
            if ($("#" + id).is(':checked')) {
                var val = $("#" + id).val();
                if ($('#lastUpdatedParam').length > 0) {
                    LMRequest.lastUpdatedParams('FSS');
                    if ($('#lastUpdatedFss').length > 0) $('#lastUpdatedFss').val(val);
                }
            }
        });
    });
</script>
