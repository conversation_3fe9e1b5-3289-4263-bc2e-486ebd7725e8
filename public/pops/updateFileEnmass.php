<?php

use models\composite\oAffiliate\checkAffiliateAEExist;
use models\composite\oAffiliate\checkPromoCodeExist;
use models\composite\oAffiliate\generatePromoCode;
use models\composite\oAffiliate\insertAffiliateInfo;
use models\composite\oBranch\getBranches;
use models\composite\oBroker\getBrokerInfo;
use models\composite\oBroker\saveAgentInfo;
use models\composite\oCustomDocs\getAllDynamicDatas;
use models\composite\oCustomDocs\sendPCCustomDocsContent;
use models\composite\oCustomDocs\SubstituteDynamicTagsForEmail;
use models\composite\oEmail\saveMailsToQueue;
use models\composite\oFileUpdate\saveFileNotes;
use models\composite\oHMLOInfo\recordFileTabUpdate;
use models\composite\oMassUpdate\allowMassPrintingForPC;
use models\composite\oMassUpdate\getLMRIdForResponse;
use models\composite\oMassUpdate\getMassFileDetails;
use models\composite\oMassUpdate\saveMassLeadMailScheduled;
use models\composite\oMassUpdate\saveMassLeadMailScheduledPre;
use models\composite\oMassUpdate\doAction;
use models\composite\oPackage\getLibPackage;
use models\composite\oPrimaryStatus\getPCPrimaryStatus;
use models\composite\oTrust\trustDocWrapper;
use models\constants\gl\glUserRole;
use models\Database2;
use models\cypher;
use models\packages\PackageSynergyLawInvoiceMassPrint;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\standard\UserAccess;


global $userRole, $userGroup, $userNumber, $UID, $userName, $userEmail, $BCCEMAIL, $allowAutomation;
global $PCID;

require 'popsConfig.php';


/* Check referrer page. */
UserAccess::checkReferrerPgs(['url' => 'myPipeline.php, agentList.php, branchList.php, /backoffice/brokers']);


UserAccess::CheckAdminUse();

//Allow automated rules repeat code here
//This is only for use confirmation if required
$allowRepeat = htmlspecialchars(Request::GetClean('allowRepeat')) ?? '';
if ($allowAutomation && $allowRepeat == 'check') {

    $PCID = intval(Request::GetClean('PCID')) ?? 0;
    $allowRepeat = htmlspecialchars(Request::GetClean('allowRepeat')) ?? '';
    $triggerRule = htmlspecialchars(Request::GetClean('triggerRule')) ?? '';
    $LMRResponseIds = array_map("htmlspecialchars", Request::GetClean('LMRResponseIds')) ?? null;
    $lastUpdatedParam = htmlspecialchars(Request::GetClean('lastUpdatedParam')) ?? '';
    $lastUpdatedFss = htmlspecialchars(Request::GetClean('lastUpdatedFss')) ?? 0;
    $primaryStatus = htmlspecialchars(Request::GetClean('primaryStatus')) ?? 0;
    $opn = htmlspecialchars(Request::GetClean('opn'));
    $fileIDArray = [];
    $impLMRIDs = [];
    $LMRIDs = '';
    if (sizeof($LMRResponseIds)) {
        $fileIDArray = getLMRIdForResponse::getReport(['LMRResponseIds' => $LMRResponseIds, 'actionType' => $opn]);
        if (sizeof($fileIDArray)) {
            foreach ($fileIDArray as $fileID) {
                $impLMRIDs[] = $fileID['LMRId'];
            }
            $LMRIDs = implode(',', $impLMRIDs);
        }
    }
    $newArray = [];
    $newArray['PCID'] = $PCID;
    $newArray['allowRepeat'] = $allowRepeat;
    $newArray['triggerRule'] = $triggerRule;
    $newArray['LMRIDs'] = $LMRIDs;
    $newArray['opn'] = $opn;
    $newArray['userRole'] = $userRole;
    $newArray['lastUpdatedParam'] = $lastUpdatedParam;
    $newArray['lastUpdatedFss'] = $lastUpdatedFss;
    $newArray['primaryStatus'] = $primaryStatus;
    $newArray['allowAutomation'] = $allowAutomation;
    $result = doAction::getReport($newArray);
    echo $result;
    exit();
}

$msg = null;
$LMRResponseIds = 0;
$opn = '';
$noOfFiles = 0;
$allowToAssignOtherFiles = 0;
$primaryStatus = '';
$subStatusIdArray = [];
$branchID = 0;
$brokerNumber = 0;
$secondaryBrokerNumber = 0;
$emailCampaignID = 0;
$stdTemplates = '';
$emailDate = '';
$employeeId = 0;
$emailArray = [];
$stdEmailArray = [];
$LMRIDArray = [];
$fileIDArray = [];
$clientIDArray = [];
$LMRIDs = '';
$CIDs = '';
$resultKeysArray = [];

$mailScheduledArray = [];
$agentId = 0;
$selMassOpt = '';
$massFileStatusId = 0;
$selectedBranchID = 0;
$selectedAgentID = 0;
$selectedSecondaryBrokerID = 0;
$selectedBrokerID = 0;
$selectedEmpID = 0;
$lenderName = '';
$endDate = '';
$selectedPCID = 0;
$startDate = '';
$selectedPriLevel = '';
$NewBranch = '';
$newbranchName = '';
$selectedStaleDay = '';
$selectedPropState = '';
$selectedLeadSource = '';
$selectedLMRCTypes = '';
$selectedStatusOpt = '';
$searchTerm = '';
$searchField = [];
$tempSearchField = '';
$mailSubject = '';
$mailContent = '';
$templateName = Arrays::getArrayValue('templateName', $_REQUEST);
$fileType = 0;
$notesType = '';
$paymentStatus = '';
$selectedPaymentStatus = '';
$subStDate = '';
$subEndDate = '';
$useMyNameAndEmail = 0;
$qry = '';
$billingDueDate = '';
$billingDueDateQry = '';
$assignedBranchArray = [];
$FBRIDs = '';
$PCInfo = [];
$massTableQuery = '';
$modifiedStartDate = '';
$modifiedEndDate = '';
$multipleStatus = '';
$assignedCFPBFileID = '';
$lenderNameArray = [];
$WFStepIDs = '';
$agentResponseIds = '';
$tabOpt = '';
$branchResponseIds = '';
$isSysNotesPrivate = 0;
$fromPCID = 0;
$toPCID = 0;
$toBranchID = 0;
$toAgentID = 0;
$newStatusId = 0;
$multipleModuleCode = 0;
$CPType = '';
$CPBRAGType = '';
$primeStatusInfo = [];
$getMassPrintFilesArray = [];
$billQry = '';
$withDocs = '';
$cusDoc = $cusEDoc = $docWizard = $attachmentArray = $pkgAllSelectedArray = $CustomDocInfoArray = [];
$removeDuplicateEmail = 0;
//automation
//get the latest updated parameter to check the specific condition
//PFS / FSS
$lastUpdatedParam = $lastUpdatedFss = '';
if (isset($_POST['lastUpdatedParam'])) $lastUpdatedParam = $_POST['lastUpdatedParam'];
if (isset($_POST['lastUpdatedFss'])) $lastUpdatedFss = $_POST['lastUpdatedFss'];

if (isset($_POST['removeDuplicateEmail'])) $removeDuplicateEmail = trim($_POST['removeDuplicateEmail']);
if (isset($_POST['LMRResponseIds'])) $LMRResponseIds = trim($_POST['LMRResponseIds']);
if (isset($_POST['opn'])) $opn = trim($_POST['opn']);
if (isset($_POST['noOfFiles'])) $noOfFiles = trim($_POST['noOfFiles']);
if (isset($_POST['primaryStatus'])) $primaryStatus = trim($_POST['primaryStatus']);
if (isset($_POST['subStatusId'])) $subStatusIdArray = $_POST['subStatusId'];
if (isset($_POST['branchID'])) $branchID = trim($_POST['branchID']);
if (isset($_POST['brokerNumber'])) $brokerNumber = trim($_POST['brokerNumber']);
if (isset($_POST['secondaryBrokerNumber'])) $secondaryBrokerNumber = trim($_POST['secondaryBrokerNumber']);
if (isset($_POST['massEmailCampaign'])) $emailCampaignID = trim($_POST['massEmailCampaign']);
if (isset($_POST['stdTemplates'])) $stdTemplates = trim($_POST['stdTemplates']);
if (isset($_POST['massEmailDate'])) $emailDate = trim($_POST['massEmailDate']);
if (isset($_POST['selMassOpt'])) $selMassOpt = trim($_POST['selMassOpt']);
if (isset($_POST['massStatusId'])) $massFileStatusId = trim($_POST['massStatusId']);
if (isset($_POST['selectedBranchID'])) $selectedBranchID = trim($_POST['selectedBranchID']);
if (isset($_POST['selectedAgentID'])) $selectedAgentID = trim($_POST['selectedAgentID']);
if (isset($_POST['selectedSecondaryBrokerID'])) $selectedSecondaryBrokerID = trim($_POST['selectedSecondaryBrokerID']);
if (isset($_POST['selectedBrokerID'])) $selectedBrokerID = trim($_POST['selectedBrokerID']);
if (isset($_POST['selectedEmpID'])) $selectedEmpID = trim($_POST['selectedEmpID']);
if (isset($_POST['selectedPCID'])) $selectedPCID = trim($_POST['selectedPCID']);
if (isset($_POST['lenName'])) $lenderName = trim($_POST['lenName']);
if (isset($_POST['eDate'])) $endDate = trim($_POST['eDate']);
if (isset($_POST['startDate'])) $startDate = trim($_POST['startDate']);
if (isset($_POST['bDD'])) $billingDueDate = trim($_POST['bDD']);
if (isset($_REQUEST['newbranchName'])) $newbranchName = trim($_REQUEST['newbranchName']);

if (isset($_POST['selectedPriLevel'])) $selectedPriLevel = trim($_POST['selectedPriLevel']);
if (isset($_POST['selectedStaleDay'])) $selectedStaleDay = trim($_POST['selectedStaleDay']);
if (isset($_POST['selectedPropState'])) $selectedPropState = trim($_POST['selectedPropState']);
if (isset($_POST['selectedLeadSource'])) $selectedLeadSource = trim($_POST['selectedLeadSource']);
if (isset($_POST['selectedLMRCTypes'])) $selectedLMRCTypes = stripslashes(trim($_POST['selectedLMRCTypes']));
if (isset($_POST['selectedStatusOpt'])) $selectedStatusOpt = trim($_POST['selectedStatusOpt']);
if (isset($_POST['searchTerm'])) $searchTerm = trim($_POST['searchTerm']);
if (isset($_POST['searchFields'])) $tempSearchField = trim($_POST['searchFields']);
if (isset($_POST['notesType'])) $notesType = trim($_POST['notesType']);
if (isset($_POST['branchName'])) $branchName = trim($_POST['branchName']);
if (isset($_POST['fileType'])) $fileType = trim($_POST['fileType']);
if (isset($_POST['paymentStatus'])) $paymentStatus = trim($_POST['paymentStatus']);
if (isset($_POST['selectedPaymentStatus'])) $selectedPaymentStatus = trim($_POST['selectedPaymentStatus']);
if (isset($_POST['subStDate'])) $subStDate = trim($_POST['subStDate']);
if (isset($_POST['subEndDate'])) $subEndDate = trim($_POST['subEndDate']);
if (isset($_POST['updatedStartDate'])) $modifiedStartDate = trim($_POST['updatedStartDate']);
if (isset($_POST['updatedEndDate'])) $modifiedEndDate = trim($_POST['updatedEndDate']);
if (isset($_POST['multipleFileStatus'])) $multipleStatus = trim($_POST['multipleFileStatus']);
if (isset($_POST['multipleModuleCode'])) $multipleModuleCode = trim($_POST['multipleModuleCode']);
if (isset($_POST['WFStepIDs'])) $WFStepIDs = trim($_POST['WFStepIDs']);
if (trim($tempSearchField)) $searchField = explode(',', $tempSearchField);
if (isset($_POST['isSysNotesPrivate'])) $isSysNotesPrivate = trim($_POST['isSysNotesPrivate']);
if (isset($_POST['fromPCID'])) $fromPCID = trim($_POST['fromPCID']);
if (isset($_POST['toPCID'])) $toPCID = trim($_POST['toPCID']);
if (isset($_POST['toBranchID'])) $toBranchID = trim($_POST['toBranchID']);
if (isset($_POST['toAgentID'])) $toAgentID = trim($_POST['toAgentID']);
if (isset($_POST['newStatusId'])) $newStatusId = trim($_POST['newStatusId']);
if (isset($_POST['agentResponseIds'])) $agentResponseIds = trim($_POST['agentResponseIds']);
if (isset($_POST['branchResponseIds'])) $branchResponseIds = trim($_POST['branchResponseIds']);
if (isset($_POST['tabOpt'])) $tabOpt = trim($_POST['tabOpt']);

if (isset($_POST['cusDoc'])) $cusDoc = $_POST['cusDoc'];
if (isset($_POST['cusEDoc'])) $cusEDoc = $_POST['cusEDoc'];
if (isset($_POST['docWizard'])) $docWizard = $_POST['docWizard'];

if (isset($_POST['emailmessage'])) $mailContent = Strings::stripQuote($_POST['emailmessage']);
if (isset($_POST['mailSubject'])) $mailSubject = Strings::stripQuote($_POST['mailSubject']);
if (isset($_POST['branchID'])) $branchID = trim($_POST['branchID']);
$loanProgram = $_POST['loanProgram'] ?? '';
$lpModuleCode = $_POST['lpModuleCode'] ?? '';
$closingStartDate = Request::GetClean('closingStartDate') ?? '';
$closingEndDate = Request::GetClean('closingEndDate') ?? '';
if ($closingStartDate) $closingStartDate = Dates::formatDateWithRE($closingStartDate, 'MDY', 'Y-m-d');
if ($closingEndDate) $closingEndDate = Dates::formatDateWithRE($closingEndDate, 'MDY', 'Y-m-d');

$maturityStartDate = Request::GetClean('maturityStartDate') ?? '';
$maturityEndDate = Request::GetClean('maturityEndDate') ?? '';
if ($maturityStartDate) $maturityStartDate = Dates::formatDateWithRE($maturityStartDate, 'MDY', 'Y-m-d');
if ($maturityEndDate) $maturityEndDate = Dates::formatDateWithRE($maturityEndDate, 'MDY', 'Y-m-d');

$appraisalStartDate = Request::GetClean('appraisalStartDate') ?? '';
$appraisalEndDate = Request::GetClean('appraisalEndDate') ?? '';
if ($appraisalStartDate) $appraisalStartDate = Dates::formatDateWithRE($appraisalStartDate, 'MDY', 'Y-m-d');
if ($appraisalEndDate) $appraisalEndDate = Dates::formatDateWithRE($appraisalEndDate, 'MDY', 'Y-m-d');

$receivedStartDate = Request::GetClean('receivedStartDate') ?? '';
$receivedEndDate = Request::GetClean('receivedEndDate') ?? '';
if ($receivedStartDate) $receivedStartDate = Dates::formatDateWithRE($receivedStartDate, 'MDY', 'Y-m-d');
if ($receivedEndDate) $receivedEndDate = Dates::formatDateWithRE($receivedEndDate, 'MDY', 'Y-m-d');
$selectedLoanOfficerID = Request::GetClean('selectedLoanOfficerID') ?? null;
$selectedWFID = Request::GetClean('selectedWFID') ?? null;
$selectedWFSID = Request::GetClean('selectedWFSID') ?? null;
$selectedWFSNotCompletedId = Request::GetClean('selectedWFSNotCompletedId') ?? null;
$selectedInternalLoanPrograms = Request::GetClean('selectedInternalLoanPrograms') ?? null;
$selectedServicingStatusCodes = Request::GetClean('selectedServicingStatusCodes') ?? null;
$referringPartySearchValue = Request::GetClean('referringPartySearchValue') ?? null;
$paymentBasedAmt = Request::GetClean('paymentBasedAmt') ?? null;
$selectedContactsIds = Request::GetClean('selectedContactsIds') ?? null;

$disclosureSentDateStart = Request::GetClean('disclosureSentDateStart') ?? '';
$disclosureSentDateEnd = Request::GetClean('disclosureSentDateEnd') ?? '';
if ($disclosureSentDateStart) $disclosureSentDateStart = Dates::formatDateWithRE($disclosureSentDateStart, 'MDY', 'Y-m-d');
if ($disclosureSentDateEnd) $disclosureSentDateEnd = Dates::formatDateWithRE($disclosureSentDateEnd, 'MDY', 'Y-m-d');

if ($userRole == glUserRole::USER_ROLE_SUPER) {
    $PCID = $selectedPCID;
}
if (in_array($opn, ['UA', 'UBA'])) {

    if ($brokerNumber == '' || $brokerNumber == '0') {

        $dummyBrokerInfo = getBrokerInfo::getReport(['mail' => $PCID . '@dummyAgentemail.com']);
        if (count($dummyBrokerInfo)) {
            if (isset($dummyBrokerInfo['userNumber'])) {
                $brokerNumber = ($dummyBrokerInfo['userNumber']);
            }
        } else {
            $dummyBrokerArray = [];
            $dummyEmailAgent = $PCID . '@dummyAgentemail.com';
            $AEPartnerCode = 0;
            $regDate = date('Y-m-d');
            $dummyBrokerArray = [
                'firstName'                                     => 'null',
                'lastName'                                      => '',
                'company'                                       => '',
                'email'                                         => $dummyEmailAgent,
                'phoneNumber'                                   => '',
                'registerDate'                                  => '',
                'promoCode'                                     => '',
                'pwd'                                           => $PCID,
                'city'                                          => '',
                'cellNumber'                                    => '',
                'fax'                                           => '',
                'agreedTC'                                      => 0,
                'allowAgentToAccessLMRDocs'                     => 0,
                'listAllAgents'                                 => 0,
                'allowAgentToCreateTasks'                       => 0,
                'allowAgentToSeeDashboard'                      => 0,
                'allowedToDeleteUplodedDocs'                    => 0,
                'allowedToEditOwnNotes'                         => 0,
                'seeBilling'                                    => 0,
                'allowToSendMarketingEmailForBRBO'              => 0,
                'allowToSeeWebForms'                            => 0,
                'allowToViewMarketPlace'                        => 0,
                'allowToLockLoanFileAgent'                      => 0,
                'allowToupdateFileAndClient'                    => 0,
                'allowToSendMassEmail'                          => 0,
                'sendNewDealAlert'                              => 0,
                'allowWorkflowEdit'                             => 0,
                'allowAgentToGetBorrowerUploadDocsNotification' => 0,
                'allowEmailCampaign'                            => 0,

            ];

            $ip = ['email' => $dummyEmailAgent];
            $AEPartnerCode = checkAffiliateAEExist::getReport($ip);

            if ($AEPartnerCode > 1) {
                $promoCode = $AEPartnerCode;
            } else {
                do {
                    $promoCode = generatePromoCode::getReport();
                    $ip = ['promoCode' => $promoCode];
                    $opt = checkPromoCodeExist::getReport($ip);
                } while ($opt);

                $affiliateInfoArray = [
                    'firstName'  => 'null',
                    'lastName'   => '',
                    'company'    => '',
                    'email'      => $dummyEmailAgent,
                    'pwd'        => '',
                    'webAddress' => '',
                    'promoCode'  => $promoCode,
                    'siteName'   => 'TLP',

                ];
                insertAffiliateInfo::getReport($affiliateInfoArray);
            }
            $brokerArray = saveAgentInfo::getReport($dummyBrokerArray);

            if (count($brokerArray)) {
                if (array_key_exists('brokerNumber', $brokerArray)) $brokerNumber = $brokerArray['brokerNumber'];
                if ($brokerNumber) {
                    $agentPCArray = [
                        'PCID'         => $PCID,
                        'brokerNumber' => $brokerNumber,

                    ];
                }
            }
        }
    }
}
if ($opn == 'MCF' && $userRole == 'Super') {
    if (isset($_POST['CPType'])) $CPType = trim($_POST['CPType']);
    if (isset($_POST['CPBRAGType'])) $CPBRAGType = trim($_POST['CPBRAGType']);
    if (isset($_POST['withDocs'])) $withDocs = trim($_POST['withDocs']);

    if ($CPType == 'samePC') {
        $fromPCID = $PCID;
        $toPCID = $PCID;
    }
    if ($CPType == 'diffPC') {
        $toBranchID = $branchID;
        $toAgentID = $brokerNumber;
    }
    if ($PCID) {


        $primeStatusInfo = getPCPrimaryStatus::getReport(['PCID' => $PCID, 'userGroup' => $userGroup, 'primeStatus' => ['New'], 'opt1' => 'list', 'keyNeeded' => 'n']);

        if (array_key_exists('primaryStatusInfo', $primeStatusInfo)) {
            $primeStatusInfo = $primeStatusInfo['primaryStatusInfo'];
        }

        for ($j = 0; $j < count($primeStatusInfo); $j++) {
            $newStatusId = trim($primeStatusInfo[$j]['PSID']);
        }
    }
}

if ($PCID) {
    $PCInfo = \models\composite\oPC\getMyDetails::getReport(['PCID' => $PCID]);
}
if (count($PCInfo)) {
    if (array_key_exists($PCID, $PCInfo)) {
        $useMyNameAndEmail = trim($PCInfo[$PCID]['useMyNameAndEmail']);
    }
}
if ($emailDate) $emailDate = trim(Dates::formatDateWithRE($emailDate, 'MDY_HMA', 'Y-m-d H:i:s'));

$branchEmail = null;

if ($branchID) {
    $branchList = [];
    $BranchInfoArray = [];
    $agentListInfo = [];
    $branchList = getBranches::getReport(['execID' => $branchID]);
    if (count($branchList)) $BranchInfoArray = $branchList['branchList'];
    $bCnt = count($BranchInfoArray);
    for ($i = 0; $i < $bCnt; $i++) {
        $tempArray = $BranchInfoArray[$i];
        $branchEmail = trim($tempArray['executiveEmail']);

    }
    $agentListInfo = getBrokerInfo::getReport(['mail' => $branchEmail, 'PCID' => $PCID]);
    if (count($agentListInfo)) {
        $agentId = $agentListInfo['userNumber'];
    }

}
if (Dates::IsEmpty($emailDate)) {
    $emailDate = Dates::Timestamp();
}

if (trim($lenderName)) $lenderNameArray = explode(',', $lenderName);
$lenderNames = [];
for ($i = 0; $i < count($lenderNameArray); $i++) {
    $lenderNames[] = addslashes(trim($lenderNameArray[$i]));
}
$lender = '';
if (count($lenderNameArray)) {
    $lender = "'" . implode("','", $lenderNameArray) . "'";
}

$propStateArray = [];
if (trim($selectedPropState)) $propStateArray = explode(',', $selectedPropState);
$propState = '';
if (count($propStateArray)) {
    $propState = "'" . implode("','", $propStateArray) . "'";
}


$borrowerNameArray = [];
$FBRIDArray = $LMRIdDetails = [];
$fileIDArray = getLMRIdForResponse::getReport(['LMRResponseIds' => $LMRResponseIds, 'removeDuplicateEmail' => $removeDuplicateEmail, 'actionType' => $opn]);

if (count($fileIDArray) > 10000) {
    mail('<EMAIL>', 'User exceeded 10000 queued', 'Email message about ' . $_SESSION['userEmail'] . ' sending over 10k marketing emails' . '; PCID = ' . $_SESSION['PCID']);
}

for ($i = 0; $i < count($fileIDArray); $i++) {
    $LMRIDArray[] = $fileIDArray[$i]['LMRId'];
    $clientIDArray[] = $fileIDArray[$i]['clientId'];
    $borrowerNameArray[trim($fileIDArray[$i]['LMRId'])] = trim($fileIDArray[$i]['borrowerName']);
    $FBRIDArray[trim($fileIDArray[$i]['LMRId'])] = trim($fileIDArray[$i]['FBRID']);

    $LMRIdDetails[trim($fileIDArray[$i]['LMRId'])] = $fileIDArray[$i];
}

if (count($LMRIDArray)) $LMRIDs = implode(',', $LMRIDArray);

if ($startDate) $startDate = Dates::formatDateWithRE($startDate, 'MDY', 'Y-m-d');
if ($endDate) $endDate = Dates::formatDateWithRE($endDate, 'MDY', 'Y-m-d');
if ($modifiedStartDate) $modifiedStartDate = Dates::formatDateWithRE($modifiedStartDate, 'MDY', 'Y-m-d');
if ($modifiedEndDate) $modifiedEndDate = Dates::formatDateWithRE($modifiedEndDate, 'MDY', 'Y-m-d');


/* Mass Advanced search */
if ($selMassOpt == 'All') {


    $assignedBranchId = '';
    if ($userGroup == 'Employee') {
        $qry = ' SELECT tl.LMRAEID
					   FROM tblBranchEmployees tl, tblAdminUsers ta  WHERE
					   tl.EID=ta.AID and ta.processingCompanyId in (' . $PCID . ') AND EID = ' . $userNumber . '
					 ';
        $rs = Database2::getInstance()->queryData($qry);
        foreach ($rs as $row) {
            $assignedBranchArray[] = $row['LMRAEID'];
        }
        if (count($assignedBranchArray)) {
            $assignedBranchId = implode(', ', array_unique($assignedBranchArray));
        }
    }


    if ($userGroup == 'CFPB Auditor') {
        $assignCFPBFileIDArray = [];
        $assignedCFPBFileIDArray = [];

        $qry = ' SELECT fileID
                       FROM tblFileAssignedCFPBAuditors WHERE UID = ' . $UID . " AND URole = 'Employee'
                       AND COALESCE(dateRemoved, '')
                     ";
        $assignCFPBFileIDArray = Database2::getInstance()->fetchRecords(['qry' => $qry]);
        for ($i = 0; $i < count($assignCFPBFileIDArray); $i++) {
            $assignedCFPBFileIDArray[] = $assignCFPBFileIDArray[$i]['fileID'];
        }
        if (count($assignedCFPBFileIDArray)) $assignedCFPBFileID = implode(', ', array_unique($assignedCFPBFileIDArray));

    }
    if ($userGroup == 'Super' && $PCID == 0 && trim($selectedLMRCTypes)) {
        $tempClientType = '';
        $allPCServiceInfoArray = [];
        $tempClientType = str_replace("'", '', $selectedLMRCTypes);
        $selectedLMRCTypes = '';

        $qry = " CALL SP_GetServiceTypesForAllPC('" . $tempClientType . "', 'SBYT');";
        $allPCServiceInfoArray = Database2::getInstance()->fetchRecords(['qry' => $qry, 'keyParam' => 'myFld']);
        if (count($allPCServiceInfoArray)) $selectedLMRCTypes = "'" . implode("','", array_keys($allPCServiceInfoArray)) . "'";

    }
    $massTableQuery = ' FROM tblFile tl
						JOIN tblFileResponse tlr ON tlr.LMRId = tl.LMRId
                        JOIN tblFileUpdatedDate tfu ON tl.LMRId = tfu.fileID
						AND tlr.activeStatus = tl.activeStatus
					';

    if (in_array('ent.entityName', $searchField)) {
        $massTableQuery .= '
        JOIN tblFileHMLOBusinessEntity ent ON ent.fileID = tl.LMRId
        ';
    }

    $propertyFields = [
        'tp.propertyAddress',
        'tp.propertyCity',
        'tp.propertyState',
    ];
    if (count(array_intersect($propertyFields, $searchField)) || $propState) {
        $massTableQuery .= '
        LEFT JOIN tblProperties tp ON tp.LMRId = tl.LMRId
        ';
    }


    if ($selectedEmpID) $massTableQuery .= ' JOIN tblFileUsers te ON tl.LMRId = te.fileID';
    if ($selectedLMRCTypes || $selectedWFID) $massTableQuery .= ' JOIN tblLMRClientType tct ON tl.LMRId = tct.LMRId';
    if ($selectedStatusOpt && $selectedStatusOpt != 'stale') $massTableQuery .= ' JOIN tblFileSubstatus ts ON tl.LMRId = ts.fileID';
    if ($notesType) $massTableQuery .= ' JOIN tblLMRProcessorComments tpc ON tl.LMRId = tpc.fileID';
    if ($billingDueDate) $massTableQuery .= ' JOIN tblLMRBilling bill ON tl.LMRId = bill.LMRId AND bill.rStatus = 1
		                                                                  JOIN tblLMRBillingPayment pay ON tl.LMRId = pay.LMRId AND pay.rStatus = 1
																		  AND bill.LMRId = pay.LMRId AND bill.phase=pay.phase
																		';
    if ($fileType == 4) {
        $massTableQuery .= ' JOIN tblHomeReport thr ON tl.LMRId = thr.fileID JOIN (SELECT MIN(th1.SubmissionDate) AS SubmissionDate, th1.fileID FROM tblHomeReportHistory th1 GROUP BY th1.fileID) AS thrh ON thr.fileID = thrh.fileID ';
    }
//echo "fileType ".$fileType;
    if (trim($fileType) == 'LA') {
        $massTableQuery .= ' JOIN tblFileLoanAudit tfl ON tl.LMRId = tfl.fileID JOIN tblPCAuditor t5 ON t5.PCID = tl.FPCID AND t5.auditorID>0 JOIN tblAdminUsers tu ON tu.AID = t5.auditorID AND tu.activeStatus = 1 ';
    }
//echo "fgfgf ".$massTableQuery;
    if ($WFStepIDs) {
        $massTableQuery .= ' JOIN tblFileSecondaryWFStatus tfsw ON tl.LMRId = tfsw.fileID ';
    }
    if ($billingDueDate) {
        $billingDueDateQry = ', ((SUM(functionToRemoveSpecialChar(bill.amount))-  (SUM(functionToRemoveSpecialChar(pay.totalPaid)) / COUNT(DISTINCT bill.feeCode) ))) AS balanceDue ';
    }

    if ($multipleStatus || $multipleModuleCode) {
        $massTableQuery .= ' JOIN tblPCPrimeStatus tps ON tlr.primeStatusId = tps.PSID and tps.activeStatus = 1 ';
    }

    if ($multipleModuleCode) {
        $massTableQuery .= ' JOIN tblPCPrimeStatusModules tpsm ON tps.PSID = tpsm.primeStatusId AND tlr.primeStatusId = tpsm.primeStatusId 
			JOIN tblFileModules tfm ON tl.LMRId = tfm.fileID AND tpsm.moduleCode = tfm.moduleCode ';
    }
    if ($closingEndDate || $closingStartDate) {
        $massTableQuery .= " JOIN tblQAInfo tqa ON tl.LMRId = tqa.LMRId";
    }
    if ($maturityStartDate || $maturityEndDate || $selectedServicingStatusCodes || $referringPartySearchValue) {
        $massTableQuery .= " LEFT JOIN tblFileHMLOPropInfo tpi ON tl.LMRId = tpi.fileID AND tlr.LMRId = tpi.fileID";
    }
    if ($appraisalStartDate || $appraisalEndDate) {
        $massTableQuery .= ' LEFT JOIN tblShortSale tss ON tl.LMRId = tss.LMRId AND tlr.LMRId = tss.LMRId ';
    }
    if ($selectedWFID) {
        $massTableQuery .= ' JOIN tblLMRWorkflow tfw ON tl.LMRId = tfw.LMRID AND tfw.dStatus = 1
                                  JOIN tblPCWFServiceType tPCWS ON tfw.WFID = tPCWS.WFID AND tct.ClientType = tPCWS.WFServiceType ';
    }
    if ($selectedWFSID) {
        $massTableQuery .= ' LEFT  JOIN tblLMRWorkflowSteps tfs ON tl.LMRId = tfs.LMRID AND tfw.dStatus = 1 ';
    }
    if ($selectedInternalLoanPrograms) {
        $massTableQuery .= ' JOIN tblFileInternalLoanPrograms tFIL ON tl.LMRId = tFIL.LMRID';
    }
    if ($disclosureSentDateStart || $disclosureSentDateEnd) {
        $massTableQuery .= ' LEFT JOIN tblFileAdminInfo tfa ON tl.LMRId = tfa.LMRId ';
    }
    if ($paymentBasedAmt) {
        $massTableQuery .= ' LEFT JOIN tblFileHMLONewLoanInfo tnl ON tl.LMRId = tnl.fileID';
    }

    if($selectedContactsIds){
        $massTableQuery .= ' JOIN tblFileContacts tfc ON tl.LMRId = tfc.fileID';
    }



    $qry = '';
    if ($opn == 'Activate') {
        $qry .= ' AND tl.activeStatus = 0 ';
    } else {
        $qry .= ' AND tl.activeStatus = 1 ';
    }
    if ($PCID) $qry .= ' AND tl.FPCID IN (' . $PCID . ') ';
    if ($assignedBranchId) $qry .= ' AND tl.FBRID IN (' . $assignedBranchId . ') ';
    if ($userGroup == 'CFPB Auditor') $qry .= ' AND tl.LMRId IN (' . $assignedCFPBFileID . ') ';

    $billingArray = ['tcc.CCNumber', 'tca.accountNo'];
    foreach ($searchField as $i => $field) {
        if (in_array(trim($field), $billingArray) && ($field) == 'tcc.CCNumber') {
            $massTableQuery .= '  JOIN tblLMRCCInfo tcc ON tl.LMRId = tcc.LMRID  ';
        }
        if (in_array(trim($field), $billingArray) && trim($field) == 'tca.accountNo') {
            $massTableQuery .= ' JOIN tblACHInfo tca ON tl.LMRId = tca.LMRID ';
        }
    }

    if ($opn == 'US') {
        if ($massFileStatusId != 'All') $qry .= " AND tlr.primeStatusId = '" . $massFileStatusId . "' ";
    } else {
        if ($massFileStatusId && trim($fileType) == 'LA') {
            if ($massFileStatusId != 'All') {
                $qry .= " AND tfl.loanAuditStatus = '" . $massFileStatusId . "' ";
            }
        } else {
            if ($massFileStatusId && $massFileStatusId != 'All') {
                $qry .= " AND tlr.primeStatusId = '" . $massFileStatusId . "' ";
            }
        }
//		  if ($primaryStatus )		    $qry .= " AND tlr.primeStatusId = '".$primaryStatus."' ";
    }
    //if ($multipleStatus!='')		    $qry .= " AND tlr.primeStatusId in(".$multipleStatus.") ";


    if ($multipleStatus) {
        if ($PCID) {
            if (preg_match('/^[a-zA-Z]+$/', $multipleStatus)) { /* Multi select and check the string has aplahbets - Dec 8, 2015 */
                $qry .= " AND tps.primaryStatus in('" . $multipleStatus . "') ";
            } else {
                $qry .= ' AND tlr.primeStatusId in(' . $multipleStatus . ') ';
            }
        } else {
            $multipleStatusArray = [];
            $multipleStatusArray = explode(', ', $multipleStatus);
            if (count($multipleStatusArray)) $multipleStatus = "'" . implode("','", $multipleStatusArray) . "'";
            $qry .= ' AND tps.primaryStatus in(' . $multipleStatus . ') ';
        }
    }

    if ($multipleModuleCode) {
        $multipleModuleCodeArray = [];
        $multipleModuleCodeArray = preg_split('/[, ]+/', $multipleModuleCode);
        if (count($multipleModuleCodeArray)) $multipleModuleCode = "'" . implode("','", $multipleModuleCodeArray) . "'";
        $qry .= ' AND tpsm.moduleCode in(' . $multipleModuleCode . ') ';
    }

    if ($selectedBranchID) $qry .= ' AND tl.FBRID in(' . $selectedBranchID . ') ';
    if ($selectedAgentID) $qry .= ' AND tl.brokerNumber in(' . $selectedAgentID . ') ';
    if ($selectedBrokerID) $qry .= ' AND tl.brokerNumber in(' . $selectedBrokerID . ') ';
    if ($selectedLoanOfficerID) {
        $qry .= ' AND tl.secondaryBrokerNumber in(' . $selectedLoanOfficerID . ') ';
    }
    if ($selectedSecondaryBrokerID) $qry .= ' AND tl.secondaryBrokerNumber in(' . $selectedSecondaryBrokerID . ') ';
    if ($selectedStaleDay) $qry .= ' AND tfu.lastUpdatedDate < DATE(DATE_SUB(NOW(), INTERVAL ' . $selectedStaleDay . ' DAY)) ';
    if ($propState) $qry .= ' AND tp.propertyState IN (' . $propState . ') ';
    if ($selectedPriLevel) $qry .= " AND tlr.priorityLevel = '" . $selectedPriLevel . "' ";
    if ($startDate == '' || $endDate == '') {
        if ($startDate) $qry .= " AND tl.recordDate >= '" . $startDate . "'";
        else if ($endDate) $qry .= " AND tl.recordDate <= '" . $endDate . "' ";
    } else                        $qry .= " AND tl.recordDate BETWEEN '" . $startDate . "' AND '" . $endDate . "' ";

    if ($modifiedStartDate == '' || $modifiedEndDate == '') {
        if ($modifiedStartDate) $qry .= " AND date(tfu.lastUpdatedDate) >= '" . $modifiedStartDate . "'";
        else if ($modifiedEndDate) $qry .= " AND date(tfu.lastUpdatedDate) <= '" . $modifiedEndDate . "' ";
    } else                        $qry .= " AND date(tfu.lastUpdatedDate) BETWEEN '" . $modifiedStartDate . "' AND '" . $modifiedEndDate . "' ";

    if ($closingStartDate == '' || $closingEndDate == '') {
        if ($closingStartDate) {
            $qry .= " AND date(tqa.closingDate) >= '" . $closingStartDate . "'";
        } elseif ($closingEndDate) {
            $qry .= " AND date(tqa.closingDate) <= '" . $closingEndDate . "'";
        }
    } else {
        $qry .= " AND date(tqa.closingDate) BETWEEN '" . $closingStartDate . "' AND '" . $closingEndDate . "' ";
    }
    if ($appraisalStartDate == '' || $appraisalEndDate == '') {
        if ($appraisalStartDate) {
            $qry .= " AND date(tss.appraisal1OrderDate) >= '" . $appraisalStartDate . "'";
        } elseif ($appraisalEndDate) {
            $qry .= " AND date(tss.appraisal1OrderDate) <= '" . $appraisalEndDate . "'";
        }
    } else {
        $qry .= " AND date(tss.appraisal1OrderDate) BETWEEN '" . $appraisalStartDate . "' AND '" . $appraisalEndDate . "' ";
    }

    if ($receivedStartDate == '' || $receivedEndDate == '') {
        if ($receivedStartDate) {
            $qry .= " AND date(tl.receivedDate) >= '" . $receivedStartDate . "'";
        } elseif ($receivedEndDate) {
            $qry .= " AND date(tl.receivedDate) <= '" . $receivedEndDate . "'";
        }
    } else {
        $qry .= " AND date(tl.receivedDate) BETWEEN '" . $receivedStartDate . "' AND '" . $receivedEndDate . "' ";
    }

    if ($disclosureSentDateStart == '' || $disclosureSentDateEnd == '') {
        if ($disclosureSentDateStart) {
            $qry .= " AND date(tfa.disclosureSentDate) >= '" . $disclosureSentDateStart . "'";
        } elseif ($disclosureSentDateEnd) {
            $qry .= " AND date(tfa.disclosureSentDate) <= '" . $disclosureSentDateEnd . "'";
        }
    } else {
        $qry .= " AND date(tfa.disclosureSentDate) BETWEEN '" . $disclosureSentDateStart . "' AND '" . $disclosureSentDateEnd . "' ";
    }

    if ($maturityStartDate == '' || $maturityEndDate == '') {
        if ($maturityStartDate) {
            $qry .= " AND date(tpi.maturityDate) >= '" . $maturityStartDate . "'";
        } elseif ($maturityEndDate) {
            $qry .= " AND date(tpi.maturityDate) <= '" . $maturityEndDate . "'";
        }
    } else {
        $qry .= " AND date(tpi.maturityDate) BETWEEN '" . $maturityStartDate . "' AND '" . $maturityEndDate . "' ";
    }

    if ($selectedWFID) {
        $qry .= ' AND tfw.WFID in(' . $selectedWFID . ') ';
    }
    if ($selectedWFSID) {
        $qry .= ' AND tfs.WFSID in( ' . $selectedWFSID . ' ) ';
    }
    if ($selectedWFSNotCompletedId) {
        $qry .= ' AND   tl.LMRId  NOT IN
                        (SELECT LMRID FROM tblLMRWorkflowSteps WHERE WFSID IN (' . $selectedWFSNotCompletedId . ')  AND dstatus=1
                        )   ';
    }
    if ($selectedInternalLoanPrograms) {
        $qry .= ' AND tFIL.internalLoanProgram IN (' . $selectedInternalLoanPrograms . ') ';
    }
    if ($selectedServicingStatusCodes) {
        $qry .= ' AND tpi.servicingSubStatus IN (' . $selectedServicingStatusCodes . ')';
    }
    if ($referringPartySearchValue) {
        $qry .= " AND ( tpi.referringParty LIKE '%" . $referringPartySearchValue . "%')";
    }
    if ($paymentBasedAmt) {
        $qry .= " AND ( tnl.isLoanPaymentAmt = '" . $paymentBasedAmt . "' ) ";
    }
    if($selectedContactsIds){
        $qry .= " AND tfc.CID in (".$selectedContactsIds.") ";
    }

    if ($lenderName) $qry .= ' AND ( tl.servicer1 IN(' . $lender . ') OR tl.servicer2 IN(' . $lender . ')) ';
    if ($selectedLeadSource) $qry .= " AND tlr.leadSource like '%" . $selectedLeadSource . "%' ";
    if ($selectedLMRCTypes) $qry .= ' AND tct.ClientType IN (' . $selectedLMRCTypes . ') ';
    if ($selectedStatusOpt) {
        if ($selectedStatusOpt != 'stale') {
            $qry .= " AND tl.LMRId = ts.fileID AND COALESCE(dateUnchecked, '') = '' AND ts.substatusId in (" . $selectedStatusOpt . ')';
        }
    }
    $phoneArray = ['tl.cellNumber', 'tl.phoneNumber', 'tl.coBPhoneNumber', 'tl.coBCellNumber', 'tl.workNumber', 'tl.coBorrowerWorkNumber', 'tl.fax', 'tl.coBFax'];
    $PhoneSearchTermCnt = 0;
    $PhoneSearchTerm = '';
    $PhoneSearchTerm = Strings::cleanPhoneNo($searchTerm);
    $PhoneSearchTermCnt = strlen($PhoneSearchTerm);

    if ($PhoneSearchTermCnt == 10) {
        for ($i = 0; $i < count($searchField); $i++) {
            if (in_array($searchField[$i], $phoneArray)) {
                unset($searchField[$i]);
            }
        }
        if (count($searchField)) {
            $searchField = array_values($searchField);
        }
    }

    if (count($searchField)) {
        $qry .= ' and (';
        for ($i = 0; $i < count($searchField); $i++) {
            if ($i) $qry .= 'OR ';

            if (in_array($searchField[$i], $phoneArray)) {

                $qry .= $searchField[$i] . " like ('%" . Strings::cleanPhoneNo($searchTerm) . "%') ";
            } else {
                if (in_array(trim($searchField[$i]), $billingArray)) {
                    $qry .= $searchField[$i] . " like ('%" . cypher::myEncryption($searchTerm) . "%') ";
                } else {
                    $qry .= $searchField[$i] . " like ('%" . $searchTerm . "%') ";
                }
            }
        }
        if ($PhoneSearchTermCnt == 10) {
            for ($j = 0; $j < count($phoneArray); $j++) {
                if ($i || $j) $qry .= 'OR ';
                $qry .= $phoneArray[$j] . " like ('%" . $PhoneSearchTerm . "%') ";
            }
        }
        $qry .= ')';
    }
    if ($fileType == 4 || trim($fileType) == 'LA') {
        if ($selectedPaymentStatus) {
            if (trim($fileType) == 'LA') {
                $qry .= " AND tfl.paymentStatus = '" . $selectedPaymentStatus . "' ";
            } else {
                $qry .= " AND thr.paymentStatus = '" . $selectedPaymentStatus . "' ";
            }
        }

        if ($subStDate == '' || $subEndDate == '') {
            if ($subStDate) {
                $qry .= " AND date(thrh.SubmissionDate) >= '" . $subStDate . "'";
            } else if ($subEndDate) {
                $qry .= " AND date(thrh.SubmissionDate) <= '" . $subEndDate . "' ";
            }
        } else {
            $qry .= " AND date(thrh.SubmissionDate) BETWEEN '" . $subStDate . "' AND '" . $subEndDate . "' ";
        }
    }
    if ($selectedEmpID) $qry .= ' AND te.UID = ' . $selectedEmpID . " AND te.URole = 'Employee' AND COALESCE(te.dateRemoved, '') = ''";
    if ($WFStepIDs) {
        $qry .= ' AND tfsw.WFSID in(' . $WFStepIDs . ') ';
    }
    if ($notesType) $qry .= "  AND tpc.deleted = 0 AND tpc.notesType = '" . $notesType . "'";
    if ($billingDueDate) {
        $curDate = date('Y-m-d');
        $wkStDate = '';
        $wkEndDate = '';
        $wkStDateArray = [];
        $wkStDay = '';
        $wkStMn = '';
        $wkStYr = '';

        $firstDay = getdate(mktime(0, 0, 0, date('m'), date('d'), date('Y')));
        $curDay = $firstDay['wday'];
        $wkStDate = date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - $curDay, date('Y')));
        $wkStDateArray = explode('-', $wkStDate);
        $wkStDay = $wkStDateArray[2];
        $wkStMn = $wkStDateArray[1];
        $wkStYr = $wkStDateArray[0];

        if ($billingDueDate == 'pastDue') {
            $qry .= " and bill.dateOwed < '" . $curDate . "' ";
        } else if ($billingDueDate == 'thisWeek') {
            $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 6, $wkStYr));
            $qry .= " and (bill.dateOwed between '" . $wkStDate . "' and '" . $wkEndDate . "')";
        } else if ($billingDueDate == 'twoWeek') {
            $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 13, $wkStYr));
            $qry .= " and (bill.dateOwed between '" . $wkStDate . "' and '" . $wkEndDate . "')";
        } else if ($billingDueDate == 'threeWeek') {
            $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 20, $wkStYr));
            $qry .= " and (bill.dateOwed between '" . $wkStDate . "' and '" . $wkEndDate . "')";
        }

    }
    $billQry = $qry;
    if (trim($fileType) == 'LA') {
        $qry .= ' GROUP BY tfl.fileID HAVING MAX(tfl.activeStatus) = 1 ';
    } else {
        if (($selectedLMRCTypes) || ($selectedStatusOpt && $selectedStatusOpt != 'stale') || ($notesType)) {
            $qry .= ' GROUP BY tlr.LMRResponseId ';
        } else {
            $qry .= ' GROUP BY tl.LMRId ';
        }
    }
}

/* Assigned Employees to mass update */


$aDComma = null;

if (isset($_POST['employeeId'])) $employeeId = trim($_POST['employeeId']);

if (($opn == 'MED' || $opn == 'UME') && ($userGroup == 'Employee' || $userRole == 'Agent' || $userRole == 'Branch')) {

    if ($opn == 'UME') {
        $stdArr = explode('-', $stdTemplates);
        $resultArray = [];
        $LMRIDArray = explode(',', $LMRIDs);

        if (count($LMRIDArray)) {
            $resultArray = [];
        } else {
            $resultArray = getAllDynamicDatas::getReport(['LMRID' => $LMRIDs, 'userName' => $userName, 'multiOpt' => '']);

        }

        $fileIDs = [];
        $notesArray = [];
        for ($j = 0; $j < count($fileIDArray); $j++) {
            $tempLMRID = 0;
            $clientId = 0;
            $tempArray = [];
            $FBRID = '';
            $borrowerName = '';
            $tempLMRID = trim($fileIDArray[$j]['LMRId']);
            $clientId = trim($fileIDArray[$j]['clientId']);
            if (array_key_exists($tempLMRID, $borrowerNameArray)) $borrowerName = trim($borrowerNameArray[$tempLMRID]);
            if (array_key_exists($tempLMRID, $FBRIDArray)) $FBRID = $FBRIDArray[$tempLMRID];


            if (count($resultArray)) {
                if (array_key_exists($tempLMRID, $resultArray)) {
                    $tempArray = $resultArray[$tempLMRID];
                }
                $tempArray['DOCCONTENT'] = $mailSubject;
                $tempArray['dynamicMergeTags'] = $resultArray['dynamicMergeTags'];
                $eMailSubject = SubstituteDynamicTagsForEmail::getReport($tempArray);
                $tempArray['DOCCONTENT'] = $mailContent;
                $eMailContent = SubstituteDynamicTagsForEmail::getReport($tempArray);
            } else {
                $eMailSubject = $mailSubject;
                $eMailContent = $mailContent;
            }

            $inputArray = [
                'LMRID'          => $tempLMRID,
                'fromUID'        => $userNumber,
                'fromUType'      => $userGroup,
                'toUID'          => $clientId,
                'toUType'        => 'Client',
                'mailSubject'    => $eMailSubject,
                'mailContent'    => $eMailContent,
                'templateName'   => $templateName,
                'scheduledOn'    => $emailDate,
                'scheduledBy'    => $userNumber,
                'scheduledUType' => $userGroup,
                'PCID'           => $PCID,
            ];

            if ($useMyNameAndEmail == 'branch') { /* Send email to client from branch name - june 12, 2015 */
                $inputArray['fromUID'] = $FBRID;
                $inputArray['fromUType'] = 'Branch';
            }
            $mailScheduledArray[] = $inputArray;
            $fileIDs[] = $tempLMRID;
            $notesArray[$tempLMRID] = $templateName . ' was emailed to ' . $borrowerName;
        }

        $ip = [
            'fileID'         => $fileIDs,
            'processorNotes' => $notesArray,
            'privateNotes'   => $isSysNotesPrivate, 'isSysNotes' => 1, 'processorName' => $userName, 'execName' => $userName,
        ];
        $exeId = 0;
        $empId = 0;
        $brId = 0;
        $clientId = 0;
        if ($userGroup == 'Employee') {
            $empId = $userNumber;
        } else if ($userGroup == 'Branch') {
            $exeId = $userNumber;
        } else if ($userGroup == 'Agent') {
            $brId = $userNumber;
        } else if ($userGroup == 'Client') {
            $clientId = $userNumber;
        }
        $ip['employeeId'] = $empId;
        $ip['executiveId'] = $exeId;
        $ip['brokerNumber'] = $brId;
        $ip['clientId'] = $clientId;
        $ip['PCID'] = $PCID;

        if (count($mailScheduledArray)) {
            $ip = ['mailScheduled' => $mailScheduledArray, 'LMRID' => $LMRIDs, 'ip' => $ip];
            if (count($resultArray)) {
                $msg = saveMassLeadMailScheduled::getReport($ip);
            } else {
                $msg = saveMassLeadMailScheduledPre::getReport($ip);
            }
        }
    }

    /* Mass Email Documents Start.. */
    if ($opn == 'MED') {
        $getMassFileDetails = [];

        $nIp['opn'] = $opn;
        $nIp['userRole'] = $userRole;
        $nIp['UID'] = $userNumber;
        $nIp['UType'] = $userGroup;
        $nIp['LMRIDs'] = $LMRIDs;
        $nIp['PCID'] = $PCID;
        $nIp['massSearchQry'] = $qry;
        $nIp['massTableQuery'] = $massTableQuery;
        $getMassFileDetails = getMassFileDetails::getReport($nIp);

        if (count($getMassFileDetails)) {
            $getMassFileDetails = $getMassFileDetails['borrowerInfo'];
        }

        $PKGID = $eMsg = '';
        $pkgIdArr = [];
        $pkgIdArray = [];


        $pkgIdArray = array_merge($cusDoc, $cusEDoc);
        $pkgIdArr = array_unique($pkgIdArray);
        $pkgIdArr = array_values($pkgIdArr);

        if (count($pkgIdArr)) {
            $PKGID = implode(',', $pkgIdArr);
            $pkgAllSelectedArray = getLibPackage::getReport(['PKGID' => $PKGID]);
        }

        if (count($docWizard)) {
            $DOCID = implode(',', $docWizard);
            $CustomDocInfoArray = sendPCCustomDocsContent::getReport(['PCID' => $PCID, 'DOCID' => $DOCID]);
        }

        $emMsg = '';
        for ($j = 0; $j < count($getMassFileDetails); $j++) { // Main Loop Start..

            $tempLMRID = 0;
            $clientId = 0;
            $tempArray = $attachmentArray = [];
            $eMailSubject = '';
            $eMailContent = '';
            $FBRID = '';
            $borrowerName = '';
            $responseId = 0;
            $borrowerLName = '';
            $borrowerEmail = '';
            $brokerNumber = '';
            $notesDocNames = '';

            $tempLMRID = trim($getMassFileDetails[$j]['LMRId']);

            $borrowerName = trim($getMassFileDetails[$j]['borrowerName']);
            $borrowerLName = trim($getMassFileDetails[$j]['borrowerLName']);
            $borrowerEmail = trim($getMassFileDetails[$j]['borrowerEmail']);
            $responseId = trim($getMassFileDetails[$j]['LMRResponseId']);
            $fileCreatedDate = trim($getMassFileDetails[$j]['recordDate']);
            $clientId = trim($getMassFileDetails[$j]['clientId']);
            $brokerNumber = trim($getMassFileDetails[$j]['brokerNumber']);

            $eMsg = "
<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\"><html xmlns=\"http://www.w3.org/1999/xhtml\" lang=''>
<head>
<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />
<title>Untitled Document</title>
<style media=\"all\" type=\"text/css\">table td { border-collapse: collapse; }</style>
</head><body><div>
<table width=\"585\">
<tr><td colspan=\"2\"><img src=\"/assets/images/email_header.gif\" alt=''>
</td>
</tr>";

            $eMsg .= "<tr><td colspan=\"2\" style=\"vertical-align: top; padding-top:30px;\"><p>1) Please find below the documents for your review / e-sign as appropriate.</p><p>2) Below you will see link(s) to important documents that you can click to open, review, and sign. Please click the link(s) and follow the instructions below:</p>RE:";

            $tempFileCreatedDate = '';
            $tempFileCreatedDate = str_replace('-', '', $fileCreatedDate);
            $dest = $PCID . '/' . date('Y', strtotime($tempFileCreatedDate)) . '/' . date('m', strtotime($tempFileCreatedDate)) . '/' . date('d', strtotime($tempFileCreatedDate));

            /* Esign And Custom Documents Start */
            if (count($pkgAllSelectedArray)) {

                for ($spk = 0; $spk < count($cusEDoc); $spk++) {
                    $PKGID = 0;
                    $pkgArray = [];
                    $docId = 0;
                    $docName = '';
                    $esign = '';
                    $actions = '';
                    $aDComma = '';
                    $res = [];
                    $esignDoc = 0;
                    $rsCnt = 0;
                    $eSignedResultArray = [];

                    $PKGID = trim($cusEDoc[$spk]);

                    /* E-sign Start */
                    if (array_key_exists($PKGID, $pkgAllSelectedArray)) {
                        $pkgArray = [];
                        $docId = 0;
                        $docName = '';
                        $esign = '';
                        $actions = '';
                        $res = [];
                        $esignDoc = 0;
                        $rsCnt = 0;
                        $packageType = '';
                        $selectedPkgUrl = '';
                        $eSignedTxnID = 0;
                        $eSignedActionID = 0;

                        $pkgArray = $pkgAllSelectedArray[$PKGID];
                        $docName = $pkgArray['pkgName'];
                        $docId = $pkgArray['PKGID'];
                        $esign = $pkgArray['esign'];
                        $packageType = $pkgArray['packageType'];
                        $selectedPkgUrl = trim($pkgArray['filePath']);

                        $signedDocInArray['responseId'] = $responseId;
                        $signedDocInArray['LMRId'] = $tempLMRID;
                        $signedDocInArray['packageId'] = $docId;
                        $signedDocInArray['UID'] = $userNumber;
                        $signedDocInArray['UType'] = $userGroup;
                        $signedDocInArray['userName'] = $userName;
                        $signedDocInArray['savePdf'] = 'yes';
                        $signedDocInArray['pkgType'] = 'LibPack';
                        $eSignedResultArray = trustDocWrapper::getReport($signedDocInArray);

                        $notesDocNames .= $aDComma . $docName;
                        $aDComma = ', ';

                        if (count($eSignedResultArray)) {
                            $eSignedTxnID = trim($eSignedResultArray['txnID']);
                            $eSignedActionID = trim($eSignedResultArray['actionID']);

                            $eMsg .= "<p><a target=\"_blank\" href=\"" . CONST_SITE_URL . 'sign/trustDocument.php?txnID=' . $eSignedTxnID . '&uId=' . cypher::myEncryption($clientId) . '&uType=' . cypher::myEncryption('Client') . "\">Click here to sign the " . ucwords($docName) . '</a></p>';
                        }
                    }
                }
                /* E-sign End */

                /* Non E-signable Documents Start */
                for ($spk = 0; $spk < count($cusDoc); $spk++) {
                    $PKGID = trim($cusDoc[$spk]);

                    if (array_key_exists($PKGID, $pkgAllSelectedArray)) {
                        $pkgArray = [];
                        $docId = 0;
                        $docName = '';
                        $esign = '';
                        $actions = '';
                        $esignDoc = 0;
                        $rsCnt = 0;
                        $packageType = '';
                        $selectedPkgUrl = '';

                        $pkgArray = $pkgAllSelectedArray[$PKGID];
                        $docName = $pkgArray['pkgName'];
                        $docId = $pkgArray['PKGID'];
                        $esign = $pkgArray['esign'];
                        $packageType = $pkgArray['packageType'];
                        $selectedPkgUrl = trim($pkgArray['filePath']);

                        $notesDocNames .= $aDComma . $docName;
                        $aDComma = ', ';

                        if ($packageType == 'static') {
                            $path_info = pathinfo(CONST_PATH_LIBS_DOC . $selectedPkgUrl);
                            $fileExtension = null;
                            if (count($path_info)) {
                                if (array_key_exists('extension', $path_info)) $fileExtension = trim($path_info['extension']);
                            }
                            $attachmentArray[] = CONST_FOLDER_LIB_DOC . $selectedPkgUrl . '{' . Strings::fileExtensionFilter($docName, $fileExtension);
                        } else {

                            $fname = Strings::removeDisAllowedChars($borrowerName) . '-' . Strings::removeDisAllowedChars($docName) . '.pdf';

                            $url = CONST_SITE_URL . 'package/pkgController.php?file=' . $fname . '&amp;rId=' . cypher::myEncryption($responseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($tempLMRID) . '&amp;pkgID=' . cypher::myEncryption($docId) . '&opt=sample';

                            $eMsg .= "<p><a rel=\"nofollow\" target=\"_blank\" href=\"" . $url . "\">Click to view " . ucwords($docName) . '</a></p>';
                        }
                    }
                }

                /* Non E-signable Documents End */
            }

            /* Esign And Custom Documents End */
            foreach ($CustomDocInfoArray as $wizDKey => $wizDKvalue) {
                $tWDArray = $infoArray = $customTempInfoArray = [];
                $tWDArray = $wizDKvalue[0];
                /* Get Dynamic Tag Values.. */
                $infoArray['LMRID'] = $tempLMRID;
                $infoArray['typeOpt'] = 'EmailContent';

                $customTempInfoArray = getAllDynamicDatas::getReport($infoArray);

                $customTempInfoArray['LMRID'] = $tempLMRID;
                $customTempInfoArray['DOCCONTENT'] = $tWDArray['docTitle'];
                $packageName = SubstituteDynamicTagsForEmail::getReport($customTempInfoArray);
                $packageName = Strings::processEmailString($packageName);

                $docTitle = $tWDArray['docTitle'];
                $CCID = $tWDArray['CCID'];

                $notesDocNames .= $aDComma . $packageName;
                $aDComma = ', ';

                $fname = Strings::removeDisAllowedChars($borrowerName) . '-' . Strings::removeDisAllowedChars($packageName);
                $url = CONST_SITE_URL . 'backoffice/loan/create_custom_doc?file=' . $fname . '&amp;rId=' . cypher::myEncryption($responseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($tempLMRID) . '&amp;pkgID=' . cypher::myEncryption($CCID) . '&amp;opt=1lien';

                $eMsg .= "<p><a rel=\"nofollow\" target=\"_blank\" href=\"" . $url . "\">Click to view " . ucwords($packageName) . '</a></p>';
            }
            /* Esign And Custom Documents Start */

            $eMsg .= '<p>Instructions: On opening the PDF, you will be asked to enter a 4 character password.</p><p>Your password consists of the last 4 numbers of your social security number. For example, if the SSN in your file is ***********, then the password is 2356. If no social security number was provided, then the password is 0000.</p></td></tr></table></body></html>';

            $eInArray['fromName'] = $userName;
            $eInArray['fromEmailActual'] = $userEmail;
            $eInArray['subject'] = 'Documents for your review';
            $eInArray['mailType'] = 'Mass Documents Review';
            $eInArray['LMRId'] = $tempLMRID;
            $eInArray['PCID'] = $PCID;
            $eInArray['attachmentArray'] = $attachmentArray;
            $eInArray['UID'] = $userNumber;
            $eInArray['URole'] = $userRole;
            $eInArray['fromEmail'] = CONST_EMAIL_FROM;
            $eInArray['toName'] = $borrowerName;
            $eInArray['toEmail'] = $borrowerEmail;
            $eInArray['msg'] = $eMsg;

            //$emMsg .= $j."--<b>".$borrowerName."</b><br>".$eMsg."<hr>";
            saveMailsToQueue::getReport(['info' => [$eInArray]]);

            $notes = 'Sent the following docs: ' . addslashes($notesDocNames);
            $inDArray = [
                'processorNotes'    => $notes,
                'fileID'            => $tempLMRID,
                'employeeId'        => $userNumber,
                'notesInfo'         => $borrowerName . ' - ' . $borrowerEmail,
                'privateNotes'      => $isSysNotesPrivate,
                'signExecutiveName' => ' ',
                'isSysNotes'        => 1,
                'displayIn'         => 'BO',
            ];

            $resultArray = saveFileNotes::getReport($inDArray);
            recordFileTabUpdate::getReport(['fileID' => $tempLMRID, 'UID' => $userNumber, 'UGroup' => $userGroup, 'opt' => 'Mass Email Documents', 'PCID' => $PCID]);

        } // Main Loop End

        $msg = 'Package has been emailed.';
    }
    /* Mass Email Documents End.. */

} elseif (PageVariables::$allowToMassUpdate || $userRole == 'Super' || ($userRole == 'Agent' && $allowToAssignOtherFiles == 1)) {

    if ($LMRResponseIds || $LMRIDs || $selMassOpt == 'All') {


        $ip = [
            'opn'                     => $opn,
            'LMRResponseIds'          => $LMRResponseIds,
            'LMRIDs'                  => $LMRIDs,
            'UType'                   => $userGroup,
            'userRole'                => $userRole,
            'UID'                     => $userNumber,
            'allowToAssignOtherFiles' => $allowToAssignOtherFiles,

            'PCID'                  => $PCID,
            'primaryStatus'         => $primaryStatus,
            'subStatusId'           => $subStatusIdArray,
            'newBranchID'           => $branchID,
            'brokerNumber'          => $brokerNumber,
            'secondaryBrokerNumber' => $secondaryBrokerNumber,
            'employeeId'            => $employeeId,
            'userEmail'             => $userEmail,
            'ccEmail'               => CONST_EMAIL_CC,
            'bccEmail'              => $BCCEMAIL,
            'fromEmail'             => CONST_EMAIL_FROM,
            'clientIDArray'         => $clientIDArray,

            'agentId'                   => $agentId,
            'opt'                       => $selMassOpt,
            'selectedStatusID'          => $massFileStatusId,
            'selectedBranchID'          => $selectedBranchID,
            'selectedAgentID'           => $selectedAgentID,
            'selectedBrokerID'          => $selectedBrokerID,
            'selectedSecondaryBrokerID' => $selectedSecondaryBrokerID,
            'selectedEmpID'             => $selectedEmpID,
            'lenderName'                => $lenderName,
            'startDate'                 => $startDate,
            'endDate'                   => $endDate,
            'userName'                  => $userName,
            'massSearchQry'             => $qry,
            'billingDueDate'            => $billingDueDate,
            'selectedLMRCTypes'         => $selectedLMRCTypes,
            'notesType'                 => $notesType,
            'selectedStatusOpt'         => $selectedStatusOpt,
            'billingDueDateQry'         => $billingDueDateQry,
            'massTableQuery'            => $massTableQuery,
            'selectedPCID'              => $selectedPCID,
            'fileType'                  => $fileType,
            'paymentStatus'             => $paymentStatus,
            'isSysNotesPrivate'         => $isSysNotesPrivate,
            'newLoanProgram'            => $loanProgram,
            'newlpModuleCode'           => $lpModuleCode,
        ];
        if ($opn == 'MCF') {
            $ip['isSysNotesPrivate'] = $isSysNotesPrivate;
            $ip['fromPCID'] = $PCID;
            $ip['toPCID'] = $toPCID;
            $ip['toBranchID'] = $toBranchID;
            $ip['toAgentID'] = $toAgentID;
            $ip['newStatusId'] = $newStatusId;
            $ip['CPBRAGType'] = $CPBRAGType;
            $ip['withDocs'] = $withDocs;
        }
        if ($opn == 'MPF') {
            $ip['billQry'] = $billQry;
            $getMassPrintFilesArray = allowMassPrintingForPC::getReport($ip);
            $getMassPrintFilesArray['opn'] = $opn;
            PackageSynergyLawInvoiceMassPrint::GeneratePDF($getMassPrintFilesArray);
            exit();
        } else {
            //Automation
            //check if the PC is enabled for Automation
            $ip['allowAutomation'] = $allowAutomation; //$allowAutomation set in getPageVariables.php
            $ip['lastUpdatedParam'] = $lastUpdatedParam;
            $ip['lastUpdatedFss'] = $lastUpdatedFss;

            $msg = doAction::getReport($ip);
        }
    }
}


if ($msg) {
    //  SetSess("msg", $msg);
    if ($msg == 'Update operation not specified!') {
        $responseAjax = ['code' => '101', 'msg' => $msg];
    } else {
        $responseAjax = ['code' => '100', 'msg' => $msg];
    }
    echo json_encode($responseAjax);
}
exit;
