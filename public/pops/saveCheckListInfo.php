<?php

use models\composite\oChecklist\emailFlatNotes;
use models\composite\oChecklist\saveChecklistForFile;
use models\composite\oChecklist\saveChecklistForPC;
use models\composite\oChecklist\updateChecklistForPC;
use models\composite\oFileUpdate\saveFileNotes;
use models\constants\gl\glUserGroup;
use models\constants\mimeTypeArray;
use models\cypher;
use models\FileStorage;
use models\Request;
use models\standard\Arrays;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;
use models\UploadServer;


global $docName;

require 'popsConfig.php';

UserAccess::checkReferrerPgs([
    'url' => 'createProcessingCompany.php, LMRequest.php, createBranch.php,defaultPCSettings.php,myPipeline.php,adminWelcome.php,adminHome.php, dashboard'
]);


UserAccess::CheckAdminUse();

$PCID = 0;
$checklistId = 0;
$checklistItem = '';
$rsCnt = 0;
$checklistInfoArray = [];
$checklistCnt = 0;
$serviceType = '';
$checklistServiceTypeArray = [];
$exChecklistItem = '';
$selServiceType = '';
$moduleName = '';
$dispOrder = '';
$moduleNameArray = [];
$tempChecklistServiceTypeArray = [];
$searchModuleType = '';
$requiredByArray = [];
$tabOpt = 'CW';
$notifyUsers = '';
$borrowerEmail = '';
$coBorrowerEmail = '';
$brokerEmail = '';
$SecondarybrokerEmail = '';
$branchEmail = '';
$empCnt = '';
$employeeId = '';
$sendNotifyBorEmail = '';
$sendNotifyCoBorEmail = '';
$sendNotifyAEEmail = '';
$sendNotifyEmail = '';
$sendNotifySecondarybrokerEmail = '';
$sendNotifyEmployee = '';
$contact_ids = '';
$customRecipientEmail = '';
$employeeIds = '';
$clientName = '';
$coBorrowerName = '';
$LMRExecutive = '';
$execName = '';
$userEmail = '';
$brokerName = '';
$SecondarybrokerName = '';
$userNumber = '';
$sendNotifyEmployee = $checklistDesc = '';
$customRecipientEmailArray = [];

$transactionType = [];
$propertyType = [];
$borrowerOccupancy = [];
$propertyState = [];
$entityState = [];
$entityType = [];
$borrowerCreditScoreRange = [];
$coBorrowerRelatedReqdoc = 0;
$rehabRelatedReqdoc = 0;
$noCrossCollRelatedReqdoc = 0;
$usCitizenRelatedReqdoc = 0;
$refDocName = '';
$refDocUrl = '';
$uploadedRefDocName = '';
$exRdesc = '';
$fileID = null;
$userGroup = null;
$UID = null;

if (isset($_POST['PCID'])) $PCID = trim($_POST['PCID']);
if (isset($_POST['checklistId'])) $checklistId = trim($_POST['checklistId']);
if (isset($_POST['checklistItem'])) $checklistItem = trim($_POST['checklistItem']);
if (isset($_POST['checklistServiceType'])) $checklistServiceTypeArray = $_POST['checklistServiceType'];
if (isset($_POST['exC'])) $exChecklistItem = trim($_POST['exC']);
if (isset($_POST['exRdesc'])) $exRdesc = trim($_POST['exRdesc']);
if (isset($_POST['serviceType'])) $selServiceType = trim($_POST['serviceType']);
if (isset($_POST['dispOrder'])) $dispOrder = trim($_POST['dispOrder']);
if (isset($_POST['moduleName'])) $moduleNameArray = $_POST['moduleName'];
if (isset($_POST['searchModuleType'])) $searchModuleType = trim($_POST['searchModuleType']);
if (isset($_POST['requiredBy'])) $requiredByArray = $_POST['requiredBy'];
if (isset($_POST['UID'])) $UID = trim($_POST['UID']);
if (isset($_POST['userGroup'])) $userGroup = trim($_POST['userGroup']);
if (isset($_POST['fileID'])) $fileID = trim($_POST['fileID']);
if (isset($_POST['tabOpt'])) $tabOpt = trim($_POST['tabOpt']);
if (isset($_POST['employeeIds'])) $employeeIds = $_POST['employeeIds'];
if (isset($_POST['contact_ids'])) $contact_ids = $_POST['contact_ids']; //File Contacts added (#154749877).
if (isset($_POST['checklistDesc'])) $checklistDesc = trim($_POST['checklistDesc']);

$categoryId = Request::isset('categoryId') ? cypher::myDecryption(Request::GetClean('categoryId') ? Request::GetClean('categoryId') : null) : null;

$employeeIdArray = Arrays::explodeIntVals($employeeIds);

if (isset($_REQUEST['customRecipientEmail'])) {
    $customRecipientEmail = trim($_REQUEST['customRecipientEmail']);
    $customRecipientEmailArray = preg_split('/[; ]+/', trim($customRecipientEmail), -1, PREG_SPLIT_NO_EMPTY);
}

if (isset($_POST['borrowerEmail'])) $borrowerEmail = trim($_POST['borrowerEmail']);
if (isset($_POST['coBorrowerEmail'])) $coBorrowerEmail = trim($_POST['coBorrowerEmail']);
if (isset($_POST['brokerEmail'])) $brokerEmail = trim($_POST['brokerEmail']);
if (isset($_POST['SecondarybrokerEmail'])) $SecondarybrokerEmail = trim($_POST['SecondarybrokerEmail']);
if (isset($_POST['branchEmail'])) $branchEmail = trim($_POST['branchEmail']);
if (isset($_POST['clientName'])) $clientName = trim($_POST['clientName']);
if (isset($_POST['coBorrowerName'])) $coBorrowerName = trim($_POST['coBorrowerName']);
if (isset($_POST['LMRExecutive'])) $LMRExecutive = trim($_POST['LMRExecutive']);
if (isset($_POST['execName'])) $execName = trim($_POST['execName']);
if (isset($_POST['userEmail'])) $userEmail = trim($_POST['userEmail']);
if (isset($_POST['brokerName'])) $brokerName = trim($_POST['brokerName']);
if (isset($_POST['SecondarybrokerName'])) $SecondarybrokerName = trim($_POST['SecondarybrokerName']);
if (isset($_POST['userNumber'])) $userNumber = trim($_POST['userNumber']);


if (isset($_POST['transactionType'])) $transactionType = ($_POST['transactionType']);
if (isset($_POST['propertyType'])) $propertyType = ($_POST['propertyType']);
if (isset($_POST['borrowerOccupancy'])) $borrowerOccupancy = ($_POST['borrowerOccupancy']);
if (isset($_POST['entityState'])) $entityState = ($_POST['entityState']);
if (isset($_POST['propertyState'])) $propertyState = ($_POST['propertyState']);
if (isset($_POST['entityType'])) $entityType = ($_POST['entityType']);
if (isset($_POST['borrowerCreditScoreRange'])) $borrowerCreditScoreRange = $_POST['borrowerCreditScoreRange'];
if (isset($_POST['coBorrowerRelatedReqdoc'])) $coBorrowerRelatedReqdoc = trim($_POST['coBorrowerRelatedReqdoc']);
if (isset($_POST['rehabRelatedReqdoc'])) $rehabRelatedReqdoc = trim($_POST['rehabRelatedReqdoc']);
if (isset($_POST['noCrossCollRelatedReqdoc'])) $noCrossCollRelatedReqdoc = trim($_POST['noCrossCollRelatedReqdoc']);
if (isset($_POST['usCitizenRelatedReqdoc'])) $usCitizenRelatedReqdoc = trim($_POST['usCitizenRelatedReqdoc']);

if (isset($_POST['refDocUrl'])) $refDocUrl = ($_POST['refDocUrl']);
if (isset($_POST['uploadedRefDocName'])) $uploadedRefDocName = ($_POST['uploadedRefDocName']);

$branchList = ($_REQUEST['branchList'] ?? Request::GetClean('branchList'));
$borrowerType = Request::GetClean('borrowerType') ?? [];


$givenChecklistId = $checklistId;

if (isset($_FILES)) {
    if ($_FILES['refDocName']['name']) {
        //$refDocUrl = '';
        $refDocName = $_FILES['refDocName']['name'];
        $file_type = $_FILES['refDocName']['type'];
        $file_size = $_FILES['refDocName']['size'];
        //$file_size = $_FILES['refDocName']['tmp_name'];

        $mimeTypeArray = mimeTypeArray::$mimeTypeArray;
        // $mimeTypeArray = array();

        if (in_array($file_type, $mimeTypeArray)) {
            if ($file_size > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
                Strings::SetSess('updateMsg', 'Unsupported File Format/File Size is too large.');
            } else {
                if ($refDocName) {
                    $specialCharacters = ['%', '+', '&', '@', '?', '#', '*', '$', '^', '!'];
                    $spaces = [' ', '  ', '   ', '    ', '     '];
                    $file_name = str_replace($spaces, '_', $refDocName);
                    $file_name = Strings::stripQuote(str_replace($specialCharacters, '', $file_name));
                    if ($file_size) {
                        $docInfoArray = ['PCID' => $PCID,
                            'file_name' => $file_name,
                            'docName' => $docName,
                        ];
                        $res = '';

                        $infoArray['PCDocs'] = 1;
                        $infoArray['oldFPCID'] = $PCID;
                        $infoArray['tmpFileContent'] = base64_encode(FileStorage::getFile($_FILES['refDocName']['tmp_name']));
                        $infoArray['fileDocName'] = 'req_' . Strings::removeDisAllowedChars((session_id() . $PCID) . '_' . $file_name);

                        $res = UploadServer::upload($infoArray);
                        if ($res == 'Success') {
                            $refDocName = $infoArray['fileDocName'];
                        }
                    }
                }
            }
        }
    } else if ($uploadedRefDocName) {
        $refDocName = $uploadedRefDocName;
    }

}

$notifyEmployeeIds = '';
$LMRNotesId = '';
$LMRId = '';
$empEmailCnt = count($employeeIdArray);
for ($ur = 0; $ur < $empEmailCnt; $ur++) {
    $empIds = $employeeIdArray[$ur];
    if ($empIds) {
        if ($ur > 0) $notifyEmployeeIds .= ',';
        $notifyEmployeeIds .= $empIds;
    }
}

$notesInfo = '';

if ($borrowerEmail) $sendNotifyBorEmail = 'yes';
if ($coBorrowerEmail) $sendNotifyCoBorEmail = 'yes';
if ($branchEmail) $sendNotifyAEEmail = 'yes';
if ($brokerEmail) $sendNotifyEmail = 'yes';
if ($SecondarybrokerEmail) $sendNotifySecondarybrokerEmail = 'yes';
if ($notifyEmployeeIds) $sendNotifyEmployee = 'yes';

$notesComma = ', ';

if ($sendNotifyBorEmail == 'yes') $notesInfo = $clientName . ' - ' . $borrowerEmail;
if ($sendNotifyCoBorEmail == 'yes') $notesInfo .= $notesComma . $coBorrowerName . ' - ' . $coBorrowerEmail;
if ($sendNotifyAEEmail == 'yes') $notesInfo .= $notesComma . $LMRExecutive . ' - ' . $branchEmail;
if ($sendNotifyEmail == 'yes') $notesInfo .= $notesComma . $brokerName . ' - ' . $brokerEmail;
if ($sendNotifySecondarybrokerEmail == 'yes') $notesInfo .= $notesComma . $SecondarybrokerName . ' - ' . $SecondarybrokerEmail;
if ($customRecipientEmail) $notesInfo .= $notesComma . str_replace(';', ', ', $customRecipientEmail);

$notifyInput = [
    'fileID' => $fileID,
    'notifyEmployeeIds' => $notifyEmployeeIds,
    'sendNotifyBorEmail' => $sendNotifyBorEmail,
    'sendNotifyCoBorEmail' => $sendNotifyCoBorEmail,
    'sendNotifyAEEmail' => $sendNotifyAEEmail,
    'sendNotifyEmail' => $sendNotifyEmail,
    'sendNotifySecondarybrokerEmail' => $sendNotifySecondarybrokerEmail,
    'sendNotifyEmployee' => $sendNotifyEmployee,
    'userNumber' => $userNumber,
    'userGroup' => $userGroup,
    'customRecipientEmailArray' => $customRecipientEmailArray,
    'execName' => $execName,
    'PCID' => $PCID,
    'userEmail' => $userEmail,
    'UID' => $userNumber,
    'UGroup' => $userGroup,
    'contact_ids' => $contact_ids,
    'opt' => 'nReqDoc',
    'listName' => $checklistItem,
];


if (!in_array('Lender', $requiredByArray)) {
    $requiredByArray[] = 'Lender';
}

foreach ($moduleNameArray as $i => $module) {
    $moduleName = '';
    $moduleName = trim($module);

    if (isset($_POST['services_' . $moduleName])) {
        $tempChecklistServiceTypeArray = $_POST['services_' . $moduleName];
    }
    $checklistServiceTypeArray[$i]['serviceType'] = $tempChecklistServiceTypeArray;

    $checklistServiceTypeArray[$i]['moduleType'] = $moduleName;
}

if ($fileID) {

    $notes = '';
    if ($checklistId) {
        if ((trim($exChecklistItem) != $checklistItem)) {
            $notes = 'Required doc name: ' . $exChecklistItem . '  Updated To: ' . $checklistItem;
        }
        if (trim($exRdesc) != trim($checklistDesc)) {
            $notes .= '<br>' . $exChecklistItem . ' Description Updated.<br> From: ' . $exRdesc . '<br> To:' . $checklistDesc;
        }
    } else {
        $notes = 'Required docs: ' . $checklistItem . ' Created ';

    }
    $checklistId = saveChecklistForFile::getReport([
        'fileID' => $fileID,
        'categoryId' => $categoryId,
        'checklistItem' => $checklistItem,
        'checklistServiceType' => $checklistServiceTypeArray,
        'dispOrder' => $dispOrder,
        'moduleName' => $moduleNameArray,
        'requiredBy' => $requiredByArray,
        'UID' => $UID,
        'userGroup' => $userGroup,
        'checklistDesc' => $checklistDesc,
        'refDocName' => $refDocName,
        'refDocUrl' => $refDocUrl,
        'checklistId' => $checklistId,
        'PCID' => $PCID,
    ]);

    emailFlatNotes::getReport($notifyInput);                                                // | Notify email method.

    $fileIDArray = explode(',', $fileID);
    $employeeId = '';
    $executiveId = '';
    $brokerNumber = '';
    $clientId = '';
    if ($userGroup == glUserGroup::USER_GROUP_EMPLOYEE) {
        $employeeId = $UID;
    } else if ($userGroup == glUserGroup::USER_GROUP_BRANCH) {
        $executiveId = $UID;
    } else if ($userGroup == glUserGroup::USER_GROUP_AGENT) {
        $brokerNumber = $UID;
    } else if ($userGroup == glUserGroup::USER_GROUP_CLIENT) {
        $clientId = $UID;
    }

    saveFileNotes::getReport([
        'processorNotes' => $notes,
        'fileID' => $fileIDArray,
        'privateNotes' => 0,
        'employeeId' => $employeeId,
        'executiveId' => $executiveId,
        'brokerNumber' => $brokerNumber,
        'clientId' => $clientId,
        'isSysNotes' => 1,
        'notesType' => 'CHKN',
    ]);

} else {
    $inputArray = [
        'PCID' => $PCID,
        'checklistItem' => $checklistItem,
        'categoryId' => $categoryId,
        'checklistServiceType' => $checklistServiceTypeArray,
        'dispOrder' => $dispOrder,
        'moduleName' => $moduleNameArray,
        'requiredBy' => $requiredByArray,
        'UID' => $UID,
        'userGroup' => $userGroup,
        'checklistDesc' => $checklistDesc,
        'transactionType' => $transactionType,
        'propertyType' => $propertyType,
        'borrowerOccupancy' => $borrowerOccupancy,
        'propertyState' => $propertyState,
        'entityState' => $entityState,
        'entityType' => $entityType,
        'coBorrowerRelatedReqdoc' => $coBorrowerRelatedReqdoc,
        'rehabRelatedReqdoc' => $rehabRelatedReqdoc,
        'noCrossCollRelatedReqdoc' => $noCrossCollRelatedReqdoc,
        'usCitizenRelatedReqdoc' => $usCitizenRelatedReqdoc,
        'borrowerCreditScoreRange' => $borrowerCreditScoreRange,
        'refDocName' => $refDocName,
        'refDocUrl' => $refDocUrl,
        'branchList' => $branchList,
        'borrowerType' => $borrowerType,
    ];

    if ($checklistId > 0) {
        $inputArray['checklistId'] = $checklistId;
        $inputArray['exChecklistItem'] = $exChecklistItem;
        updateChecklistForPC::getReport($inputArray);
    } else {
        $checklistId = saveChecklistForPC::getReport($inputArray);
    }
}
//}
if ($checklistId) {
    HTTP::ExitJSON(['code' => '100', 'msg' => 'Required Docs ' . ($givenChecklistId ? 'Updated' : 'Added')]);
}

$responseAjax = [
    'code' => '101',
    'msg' => 'Error in creation',
    'notificationList' => '',
    'fileID' => $fileID,
    'checklistId' => $checklistId,
];
$notificationList = Strings::getNotifications();
if ($notificationList) {
    $responseAjax['notificationList'] = $notificationList;
}
HTTP::ExitJSON($responseAjax);


