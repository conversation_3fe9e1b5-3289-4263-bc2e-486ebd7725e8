<?xml version="1.0" encoding="UTF-8"?>
<MESSAGE xmlns="http://www.mismo.org/residential/2009/schemas"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns:ULAD="http://www.datamodelextension.org/Schema/ULAD"
         xmlns:DU="http://www.datamodelextension.org/Schema/DU"
         MISMOReferenceModelIdentifier="3.4.032420160128" xmlns:xlink="http://www.w3.org/1999/xlink"
         xsi:schemaLocation="http://www.mismo.org/residential/2009/schemas DU_Wrapper_3.4.0_B324.xsd">
    <ABOUT_VERSIONS>
        <ABOUT_VERSION>
            <CreatedDatetime>[-]currentDateTime[-]</CreatedDatetime>
            <AboutVersionIdentifier>DU Spec 1.9</AboutVersionIdentifier>
        </ABOUT_VERSION>
    </ABOUT_VERSIONS>
    <DEAL_SETS>
        <DEAL_SET>
            <DEALS>
                <DEAL>
                    <ASSETS>
                        |-|BREAKPOINT|-|ASSETS|-|
                        <ASSET SequenceNumber="[-]assetSequenceNumber[-]" xlink:label="[-]assetLabel[-]">
                            <ASSET_DETAIL>
                                <AssetAccountIdentifier>[-]accountNumber[-]</AssetAccountIdentifier>
                                <AssetCashOrMarketValueAmount>[-]balance[-]</AssetCashOrMarketValueAmount>
                                <AssetType>[-]accType[-]</AssetType>
                            </ASSET_DETAIL>
                            <ASSET_HOLDER>
                                <NAME>
                                    <FullName>[-]nameAddrOfBank[-]</FullName>
                                </NAME>
                            </ASSET_HOLDER>
                        </ASSET>
                        |-|BREAKPOINT|-|ASSETS_END|-|
                    </ASSETS>
                    <COLLATERALS>
                        <COLLATERAL>
                            <SUBJECT_PROPERTY>
                                <ADDRESS>
                                    <AddressLineText>[-]propertyAddress[-]</AddressLineText>
                                    <CityName>[-]propertyCity[-]</CityName>
                                    <PostalCode>[-]propertyZip[-]</PostalCode>
                                    <StateCode>[-]propertyState[-]</StateCode>
                                </ADDRESS>
                                <PROPERTY_DETAIL>
                                    <AttachmentType></AttachmentType>
                                    <CommunityPropertyStateIndicator>true</CommunityPropertyStateIndicator>
                                    <ConstructionMethodType>[-]propConstructionMethod[-]</ConstructionMethodType>
                                    <FinancedUnitCount></FinancedUnitCount>
                                    <PropertyEstateType></PropertyEstateType>
                                    <PropertyExistingCleanEnergyLienIndicator>false</PropertyExistingCleanEnergyLienIndicator>
                                    <PropertyInProjectIndicator>false</PropertyInProjectIndicator>
                                    <PropertyMixedUsageIndicator>false</PropertyMixedUsageIndicator>
                                    <PropertyStructureBuiltYear>[-]yearBuilt[-]</PropertyStructureBuiltYear>
                                    <PropertyUsageType>[-]propertyUsageType[-]</PropertyUsageType>
                                    <PUDIndicator>false</PUDIndicator>
                                </PROPERTY_DETAIL>
                                <PROPERTY_VALUATIONS>
                                    <PROPERTY_VALUATION>
                                        <PROPERTY_VALUATION_DETAIL>
                                            <PropertyValuationAmount>[-]homeValue[-]</PropertyValuationAmount>
                                        </PROPERTY_VALUATION_DETAIL>
                                    </PROPERTY_VALUATION>
                                </PROPERTY_VALUATIONS>
                                <SALES_CONTRACTS>
                                    <SALES_CONTRACT>
                                        <SALES_CONCESSIONS>
                                            <SALES_CONCESSION>
                                                <SalesConcessionAmount></SalesConcessionAmount>
                                            </SALES_CONCESSION>
                                        </SALES_CONCESSIONS>
                                        <SALES_CONTRACT_DETAIL>
                                            <SalesContractAmount>[-]costBasis[-]</SalesContractAmount>
                                        </SALES_CONTRACT_DETAIL>
                                    </SALES_CONTRACT>
                                </SALES_CONTRACTS>
                            </SUBJECT_PROPERTY>
                        </COLLATERAL>
                    </COLLATERALS>
                    <LIABILITIES>
                        |-|BREAKPOINT|-|LIABILITIES|-|
                        <LIABILITY SequenceNumber="[-]liabilitiesSequenceNumber[-]" xlink:label="[-]liabilitiesLabel[-]">
                            <LIABILITY_DETAIL>
                                <LiabilityAccountIdentifier>[-]accountNo[-]</LiabilityAccountIdentifier>
                                <LiabilityExclusionIndicator>false</LiabilityExclusionIndicator>
                                <LiabilityMonthlyPaymentAmount>[-]monthlyPayment[-]</LiabilityMonthlyPaymentAmount>
                                <LiabilityPayoffStatusIndicator>[-]liabilityAtorBeforeClose[-]</LiabilityPayoffStatusIndicator>
                                <LiabilityRemainingTermMonthsCount>[-]monthsLeftToPay[-]</LiabilityRemainingTermMonthsCount>
                                <LiabilityType>[-]liabilityAccType[-]</LiabilityType>
                                <LiabilityUnpaidBalanceAmount>[-]unpaidBalance[-]</LiabilityUnpaidBalanceAmount>
                            </LIABILITY_DETAIL>
                            <LIABILITY_HOLDER>
                                <NAME>
                                    <FullName>[-]nameAddrOfCompany[-]</FullName>
                                </NAME>
                            </LIABILITY_HOLDER>
                        </LIABILITY>
                        |-|BREAKPOINT|-|LIABILITIES_END|-|
                    </LIABILITIES>
                    <LOANS>
                        <LOAN LoanRoleType="SubjectLoan" xlink:label="LOAN_1">
                            <AMORTIZATION>
                                <AMORTIZATION_RULE>
                                    <AmortizationType>[-]amortizationType[-]</AmortizationType>
                                    <LoanAmortizationPeriodCount>[-]loanTerm[-]</LoanAmortizationPeriodCount>
                                    <LoanAmortizationPeriodType>Month</LoanAmortizationPeriodType>
                                </AMORTIZATION_RULE>
                            </AMORTIZATION>
                            <DOCUMENT_SPECIFIC_DATA_SETS>
                                <DOCUMENT_SPECIFIC_DATA_SET>
                                    <URLA>
                                        <URLA_DETAIL>
                                            <ApplicationSignedByLoanOriginatorDate></ApplicationSignedByLoanOriginatorDate>
                                            <EstimatedClosingCostsAmount>[-]totalFeesAndCost[-]</EstimatedClosingCostsAmount>
                                            <PrepaidItemsEstimatedAmount>0.00</PrepaidItemsEstimatedAmount>
                                        </URLA_DETAIL>
                                    </URLA>
                                </DOCUMENT_SPECIFIC_DATA_SET>
                            </DOCUMENT_SPECIFIC_DATA_SETS>
                            <HMDA_LOAN>
                                <HMDA_LOAN_DETAIL>
                                    <HMDA_HOEPALoanStatusIndicator>[-]HOEPAStatus[-]</HMDA_HOEPALoanStatusIndicator>
                                </HMDA_LOAN_DETAIL>
                            </HMDA_LOAN>
                            <HOUSING_EXPENSES>
                                <HOUSING_EXPENSE>
                                    <HousingExpensePaymentAmount>[-]lien1Payment[-]</HousingExpensePaymentAmount>
                                    <HousingExpenseTimingType>Proposed</HousingExpenseTimingType>
                                    <HousingExpenseType>FirstMortgagePrincipalAndInterest</HousingExpenseType>
                                </HOUSING_EXPENSE>
                            </HOUSING_EXPENSES>
                            <LOAN_DETAIL>
                                <ApplicationReceivedDate>[-]applicationReceivedDate[-]</ApplicationReceivedDate>
                                <BalloonIndicator>[-]balloonPayment[-]</BalloonIndicator>
                                <BelowMarketSubordinateFinancingIndicator>false</BelowMarketSubordinateFinancingIndicator>
                                <BorrowerCount>[-]borrowerCount[-]</BorrowerCount>
                                <BuydownTemporarySubsidyFundingIndicator>false</BuydownTemporarySubsidyFundingIndicator>
                                <ConstructionLoanIndicator>[-]constructionLoanIndicator[-]</ConstructionLoanIndicator>
                                <InterestOnlyIndicator>[-]interestOnlyIndicator[-]</InterestOnlyIndicator>
                                <NegativeAmortizationIndicator>false</NegativeAmortizationIndicator>
                                <PrepaymentPenaltyIndicator>false</PrepaymentPenaltyIndicator>
                            </LOAN_DETAIL>
                            <LOAN_IDENTIFIERS>
                                <LOAN_IDENTIFIER>
                                    <LoanIdentifier>[-]loanNumber[-]</LoanIdentifier>
                                    <LoanIdentifierType>LenderLoan</LoanIdentifierType>
                                </LOAN_IDENTIFIER>
                            </LOAN_IDENTIFIERS>
                            <ORIGINATION_SYSTEMS>
                                <ORIGINATION_SYSTEM>
                                    <LoanOriginationSystemName>LendingWise</LoanOriginationSystemName>
                                    <LoanOriginationSystemVendorIdentifier>Software Provider ID</LoanOriginationSystemVendorIdentifier>
                                    <LoanOriginationSystemVersionIdentifier>Version of LOS</LoanOriginationSystemVersionIdentifier>
                                </ORIGINATION_SYSTEM>
                            </ORIGINATION_SYSTEMS>
                            <TERMS_OF_LOAN>
                                <BaseLoanAmount>[-]totalLoanAmount[-]</BaseLoanAmount>
                                <LienPriorityType>[-]lienPosition[-]</LienPriorityType>
                                <LoanPurposeType>[-]typeOfHMLOLoanRequesting[-]</LoanPurposeType>
                                <MortgageType>Conventional</MortgageType>
                                <NoteRatePercent>[-]lien1Rate[-]</NoteRatePercent>
                            </TERMS_OF_LOAN>
                        </LOAN>
                    </LOANS>
                    <PARTIES>
                        <PARTY>
                            <INDIVIDUAL>
                                <CONTACT_POINTS>
                                    <CONTACT_POINT>
                                        <CONTACT_POINT_EMAIL>
                                            <ContactPointEmailValue>[-]borrowerEmail[-]</ContactPointEmailValue>
                                        </CONTACT_POINT_EMAIL>
                                    </CONTACT_POINT>
                                    <CONTACT_POINT>
                                        <CONTACT_POINT_TELEPHONE>
                                            <ContactPointTelephoneValue>[-]methodOfContactValue[-]</ContactPointTelephoneValue>
                                        </CONTACT_POINT_TELEPHONE>
                                        <CONTACT_POINT_DETAIL>
                                            <ContactPointRoleType>[-]methodOfContactRole[-]</ContactPointRoleType>
                                        </CONTACT_POINT_DETAIL>
                                    </CONTACT_POINT>
                                </CONTACT_POINTS>
                                <NAME>
                                    <FirstName>[-]borrowerFName[-]</FirstName>
                                    <LastName>[-]borrowerLName[-]</LastName>
                                    <MiddleName>[-]borrowerMName[-]</MiddleName>
                                </NAME>
                            </INDIVIDUAL>
                            <ADDRESSES>
                                <ADDRESS>
                                    <AddressLineText>[-]presentAddress[-]</AddressLineText>
                                    <AddressType>Mailing</AddressType>
                                    <CityName>[-]presentCity[-]</CityName>
                                    <CountryCode>US</CountryCode>
                                    <PostalCode>[-]presentZip[-]</PostalCode>
                                    <StateCode>[-]presentState[-]</StateCode>
                                </ADDRESS>
                            </ADDRESSES>
                            <LANGUAGES>
                                <LANGUAGE>
                                    <LanguageCode>eng</LanguageCode>
                                    <EXTENSION>
                                        <OTHER>
                                            <ULAD:LANGUAGE_EXTENSION>
                                                <ULAD:LanguageRefusalIndicator>false</ULAD:LanguageRefusalIndicator>
                                            </ULAD:LANGUAGE_EXTENSION>
                                        </OTHER>
                                    </EXTENSION>
                                </LANGUAGE>
                            </LANGUAGES>
                            <ROLES>
                                <ROLE SequenceNumber="1" xlink:label="BORROWER_1">
                                    <BORROWER>
                                        <BORROWER_DETAIL>
                                            <BorrowerBirthDate>[-]borrowerDOB[-]</BorrowerBirthDate>
                                            <CommunityPropertyStateResidentIndicator>false</CommunityPropertyStateResidentIndicator>
                                            <DependentCount>[-]numberOfDependents[-]</DependentCount>
                                            <DomesticRelationshipIndicator>true</DomesticRelationshipIndicator>
                                            <DomesticRelationshipType>DomesticPartnership</DomesticRelationshipType>
                                            <MaritalStatusType>[-]maritalStatus[-]</MaritalStatusType>
                                        </BORROWER_DETAIL>
                                        <CURRENT_INCOME>
                                            <CURRENT_INCOME_ITEMS>
                                                <CURRENT_INCOME_ITEM SequenceNumber="1" xlink:label="CURRENT_INCOME_ITEM_1">
                                                    <CURRENT_INCOME_ITEM_DETAIL>
                                                        <CurrentIncomeMonthlyTotalAmount>[-]totalHouseHoldIncome[-]</CurrentIncomeMonthlyTotalAmount>
                                                        <EmploymentIncomeIndicator>false</EmploymentIncomeIndicator>
                                                    </CURRENT_INCOME_ITEM_DETAIL>
                                                </CURRENT_INCOME_ITEM>
                                            </CURRENT_INCOME_ITEMS>
                                        </CURRENT_INCOME>
                                        <DECLARATION>
                                            <DECLARATION_DETAIL>
                                                <BankruptcyIndicator>[-]bankruptcyIndicator[-]</BankruptcyIndicator>
                                                <CitizenshipResidencyType>[-]citizenshipResidencyType[-]</CitizenshipResidencyType>
                                                <IntentToOccupyType>[-]intentToOccupyType[-]</IntentToOccupyType>
                                                <OutstandingJudgmentsIndicator>[-]outstandingJudgmentsIndicator[-]</OutstandingJudgmentsIndicator>
                                                <PartyToLawsuitIndicator>[-]partyToLawsuitIndicator[-]</PartyToLawsuitIndicator>
                                                <PresentlyDelinquentIndicator>[-]presentlyDelinquentIndicator[-]</PresentlyDelinquentIndicator>
                                                <PriorPropertyForeclosureCompletedIndicator>[-]priorPropertyForeclosureCompletedIndicator[-]</PriorPropertyForeclosureCompletedIndicator>
                                                <PriorPropertyShortSaleCompletedIndicator>[-]priorPropertyShortSaleCompletedIndicator[-]</PriorPropertyShortSaleCompletedIndicator>
                                                <PropertyProposedCleanEnergyLienIndicator>[-]propertyProposedCleanEnergyLienIndicator[-]</PropertyProposedCleanEnergyLienIndicator>
                                                <UndisclosedBorrowedFundsIndicator>[-]undisclosedBorrowedFundsIndicator[-]</UndisclosedBorrowedFundsIndicator>
                                                <UndisclosedCreditApplicationIndicator>[-]undisclosedCreditApplicationIndicator[-]</UndisclosedCreditApplicationIndicator>
                                                <UndisclosedMortgageApplicationIndicator>[-]undisclosedMortgageApplicationIndicator[-]</UndisclosedMortgageApplicationIndicator>
                                                <EXTENSION>
                                                    <OTHER>
                                                        <ULAD:DECLARATION_DETAIL_EXTENSION>
                                                            <ULAD:SpecialBorrowerSellerRelationshipIndicator>[-]specialBorrowerSellerRelationshipIndicator[-]</ULAD:SpecialBorrowerSellerRelationshipIndicator>
                                                        </ULAD:DECLARATION_DETAIL_EXTENSION>
                                                    </OTHER>
                                                </EXTENSION>
                                            </DECLARATION_DETAIL>
                                        </DECLARATION>
                                        <EMPLOYERS>
                                            <EMPLOYER SequenceNumber="1" xlink:label="EMPLOYER_1">
                                                <LEGAL_ENTITY>
                                                    <CONTACTS>
                                                        <CONTACT>
                                                            <CONTACT_POINTS>
                                                                <CONTACT_POINT>
                                                                    <CONTACT_POINT_TELEPHONE>
                                                                        <ContactPointTelephoneValue>[-]employer1Phone[-]</ContactPointTelephoneValue>
                                                                    </CONTACT_POINT_TELEPHONE>
                                                                </CONTACT_POINT>
                                                            </CONTACT_POINTS>
                                                        </CONTACT>
                                                    </CONTACTS>
                                                    <LEGAL_ENTITY_DETAIL>
                                                        <FullName>[-]employer1[-]</FullName>
                                                    </LEGAL_ENTITY_DETAIL>
                                                </LEGAL_ENTITY>
                                                <ADDRESS>
                                                    <AddressLineText>[-]employer1Add[-]</AddressLineText>
                                                    <CityName>[-]employer1City[-]</CityName>
                                                    <PostalCode>[-]employer1Zip[-]</PostalCode>
                                                    <StateCode>[-]employer1State[-]</StateCode>
                                                </ADDRESS>
                                                <EMPLOYMENT>
                                                    <EmploymentBorrowerSelfEmployedIndicator>[-]employmentBorrowerSelfEmployedIndicator[-]</EmploymentBorrowerSelfEmployedIndicator>
                                                    <EmploymentClassificationType>Primary</EmploymentClassificationType>
                                                    <EmploymentPositionDescription>[-]occupation1[-]</EmploymentPositionDescription>
                                                    <EmploymentStartDate>[-]borrowerHireDate[-]</EmploymentStartDate>
                                                    <EmploymentStatusType>Current</EmploymentStatusType>
                                                    <EmploymentTimeInLineOfWorkMonthsCount>[-]employmentTimeInLineOfWorkMonthsCount[-]</EmploymentTimeInLineOfWorkMonthsCount>
                                                    <SpecialBorrowerEmployerRelationshipIndicator>[-]specialBorrowerEmployerRelationshipIndicator[-]</SpecialBorrowerEmployerRelationshipIndicator>
                                                    <EXTENSION>
                                                        <OTHER>
                                                            <DU:EMPLOYMENT_EXTENSION>
                                                                <DU:ForeignIncomeIndicator>false</DU:ForeignIncomeIndicator>
                                                                <DU:SeasonalIncomeIndicator>false</DU:SeasonalIncomeIndicator>
                                                            </DU:EMPLOYMENT_EXTENSION>
                                                        </OTHER>
                                                    </EXTENSION>
                                                </EMPLOYMENT>
                                            </EMPLOYER>
                                        </EMPLOYERS>
                                        <GOVERNMENT_MONITORING>
                                            <GOVERNMENT_MONITORING_DETAIL>
                                                <HMDAEthnicityRefusalIndicator>[-]HMDAEthnicityRefusalIndicator[-]</HMDAEthnicityRefusalIndicator>
                                                <HMDAGenderRefusalIndicator>[-]HMDAGenderRefusalIndicator[-]</HMDAGenderRefusalIndicator>
                                                <HMDARaceRefusalIndicator>[-]HMDARaceRefusalIndicator[-]</HMDARaceRefusalIndicator>
                                                <EXTENSION>
                                                    <OTHER>
                                                        <ULAD:GOVERNMENT_MONITORING_DETAIL_EXTENSION>
                                                            <ULAD:ApplicationTakenMethodType>[-]applicationTakenMethodType[-]</ULAD:ApplicationTakenMethodType>
                                                            <ULAD:HMDAGenderType>[-]HMDAGenderType[-]</ULAD:HMDAGenderType>
                                                        </ULAD:GOVERNMENT_MONITORING_DETAIL_EXTENSION>
                                                    </OTHER>
                                                </EXTENSION>
                                            </GOVERNMENT_MONITORING_DETAIL>
                                            <HMDA_RACES>
                                                <HMDA_RACE>
                                                    <HMDA_RACE_DETAIL>
                                                        <HMDARaceType>[-]HMDARaceType[-]</HMDARaceType>
                                                    </HMDA_RACE_DETAIL>
                                                </HMDA_RACE>
                                            </HMDA_RACES>
                                            <EXTENSION>
                                                <OTHER>
                                                    <ULAD:GOVERNMENT_MONITORING_EXTENSION>
                                                        <ULAD:HMDA_ETHNICITIES>
                                                            <ULAD:HMDA_ETHNICITY>
                                                                <ULAD:HMDAEthnicityType>[-]HMDAEthnicityType[-]</ULAD:HMDAEthnicityType>
                                                            </ULAD:HMDA_ETHNICITY>
                                                        </ULAD:HMDA_ETHNICITIES>
                                                    </ULAD:GOVERNMENT_MONITORING_EXTENSION>
                                                </OTHER>
                                            </EXTENSION>
                                        </GOVERNMENT_MONITORING>
                                        <RESIDENCES>
                                            <RESIDENCE>
                                                <ADDRESS>
                                                    <AddressLineText>[-]presentAddress[-]</AddressLineText>
                                                    <CityName>[-]presentCity[-]</CityName>
                                                    <PostalCode>[-]presentZip[-]</PostalCode>
                                                    <StateCode>[-]presentState[-]</StateCode>
                                                </ADDRESS>
                                                <LANDLORD>
                                                    <LANDLORD_DETAIL>
                                                        <MonthlyRentAmount>[-]currentRPM[-]</MonthlyRentAmount>
                                                    </LANDLORD_DETAIL>
                                                </LANDLORD>
                                                <RESIDENCE_DETAIL>
                                                    <BorrowerResidencyBasisType>[-]borPresentPropType[-]</BorrowerResidencyBasisType>
                                                    <BorrowerResidencyDurationMonthsCount>[-]presentPropLengthMonths[-]</BorrowerResidencyDurationMonthsCount>
                                                    <BorrowerResidencyType>Current</BorrowerResidencyType>
                                                </RESIDENCE_DETAIL>
                                            </RESIDENCE>
                                        </RESIDENCES>
                                    </BORROWER>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>Borrower</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                            <TAXPAYER_IDENTIFIERS>
                                <TAXPAYER_IDENTIFIER>
                                    <TaxpayerIdentifierType>SocialSecurityNumber</TaxpayerIdentifierType>
                                    <TaxpayerIdentifierValue>[-]ssnNumber[-]</TaxpayerIdentifierValue>
                                </TAXPAYER_IDENTIFIER>
                            </TAXPAYER_IDENTIFIERS>
                        </PARTY>
                        <PARTY>
                            <INDIVIDUAL>
                                <NAME>
                                    <FullName>[-]firstName[-] [-]lastName[-]</FullName>
                                </NAME>
                            </INDIVIDUAL>
                            <ROLES>
                                <ROLE SequenceNumber="1" xlink:label="PROPERTY_OWNER_1">
                                    <PROPERTY_OWNER>
                                        <PropertyOwnerStatusType>[-]propertyOwnerStatusType[-]</PropertyOwnerStatusType>
                                    </PROPERTY_OWNER>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>PropertyOwner</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                        </PARTY>
                        <PARTY>
                            <LEGAL_ENTITY>
                                <LEGAL_ENTITY_DETAIL>
                                    <FullName>[-]processingCompanyName[-]</FullName>
                                </LEGAL_ENTITY_DETAIL>
                            </LEGAL_ENTITY>
                            <ADDRESSES>
                                <ADDRESS>
                                    <AddressLineText>[-]attorneyAddress[-]</AddressLineText>
                                    <CityName>[-]attorneyCity[-]</CityName>
                                    <PostalCode>[-]attorneyZipCode[-]</PostalCode>
                                    <StateCode>[-]attorneyState[-]</StateCode>
                                </ADDRESS>
                            </ADDRESSES>
                            <ROLES>
                                <ROLE SequenceNumber="1" xlink:label="LOAN_ORIGINATION_COMPANY_1">
                                    <LICENSES>
                                        <LICENSE>
                                            <LICENSE_DETAIL>
                                                <LicenseIdentifier>[-]NMLSID[-]</LicenseIdentifier>
                                            </LICENSE_DETAIL>
                                        </LICENSE>
                                    </LICENSES>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>LoanOriginationCompany</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                        </PARTY>
                        |-|BREAKPOINT|-|SECONDARY_BROKER_INFO|-|
                        <PARTY>
                            <INDIVIDUAL>
                                <CONTACT_POINTS>
                                    <CONTACT_POINT>
                                        <CONTACT_POINT_TELEPHONE>
                                            <ContactPointTelephoneValue>[-]phoneNumber[-]</ContactPointTelephoneValue>\
                                        </CONTACT_POINT_TELEPHONE>
                                    </CONTACT_POINT>
                                </CONTACT_POINTS>
                                <NAME>
                                    <FirstName>[-]firstName[-]</FirstName>
                                    <LastName>[-]lastName[-]</LastName>
                                </NAME>
                            </INDIVIDUAL>
                            <ROLES>
                                <ROLE SequenceNumber="1" xlink:label="LOAN_ORIGINATOR_1">
                                    <LICENSES>
                                        <LICENSE>
                                            <LICENSE_DETAIL>
                                                <LicenseIdentifier>[-]loOrginatorID[-]</LicenseIdentifier>
                                            </LICENSE_DETAIL>
                                        </LICENSE>
                                    </LICENSES>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>LoanOriginator</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                        </PARTY>
                        |-|BREAKPOINT|-|SECONDARY_BROKER_INFO_END|-|
                    </PARTIES>
                    <RELATIONSHIPS xsi:type="RELATIONSHIPS">
                        [-]relationshipAssetBorrower[-]
                        <RELATIONSHIP SequenceNumber="1" xlink:from="CURRENT_INCOME_ITEM_1"
                                      xlink:to="EMPLOYER_1" xlink:arcrole="urn:fdc:mismo.org:2009:residential/CURRENT_INCOME_ITEM_IsAssociatedWith_EMPLOYER" />
                        [-]relationshipLiabilityBorrower[-]
                    </RELATIONSHIPS>
                </DEAL>
            </DEALS>
        </DEAL_SET>
    </DEAL_SETS>
</MESSAGE>