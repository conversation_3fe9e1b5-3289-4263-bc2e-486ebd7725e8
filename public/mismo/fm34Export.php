<?php

use models\cypher;
use models\composite\oFile\getFileInfo;
use models\constants\gl\glMismo34Constants;
use models\composite\oThirdPartyServices\generateXML;

require '../includes/util.php';

$error1 = "Error: ";
$error2 = " Please contact support.";
$LMRId = cypher::myDecryption($_REQUEST['lId']);
$rawXML = 'mismo34.xml';

if (!file_exists($rawXML)) {
    die("$error1 Unable to open XML File! $error2");
}
$rawXML = file_get_contents($rawXML);
if ($rawXML === false) {
    die("$error1 Unable to read XML Structure! $error2");
}
if (simplexml_load_string($rawXML) === false) {
    die("$error1 Unable to validate XML Structure! $error2");
}

$xmlArr = generateXML::fetch($rawXML, [], false);
$xmlContent = $xmlArr['BEGIN'];

$fileData = getFileInfo::getReport(['LMRId' => $LMRId])[$LMRId];

$fileLOChekingSavingInfo = $fileData['fileLOChekingSavingInfo'] ?? ['empty'];
$counter = 1;
$relationshipAssetBorrower = '';
foreach ($fileLOChekingSavingInfo as $key => $value) {
    if ($value !== 'empty') {
        $data = [
            "assetSequenceNumber" => $counter,
            "assetLabel" => "ASSET_$counter",
        ];
        $data = array_merge($value, $data);
        $xmlContent .= generateXML::replace($xmlArr['ASSETS'], $data);
        $relationshipAssetBorrower .= '<RELATIONSHIP SequenceNumber="' . $counter . '" xlink:from="ASSET_' . $counter . '" xlink:to="BORROWER_1" xlink:arcrole="urn:fdc:mismo.org:2009:residential/ASSET_IsAssociatedWith_ROLE" />';
        $counter++;
    }
}
$xmlContent .= $xmlArr['ASSETS_END'];

$liabilitiesInfo = $fileData['liabilitiesInfo'] ?? ['empty'];
$counter = 1;
$relationshipLiabilityBorrower = '';
foreach ($liabilitiesInfo as $key => $value) {
    if ($value !== 'empty') {
        $data = [
            "liabilitiesSequenceNumber" => $counter,
            "liabilitiesLabel" => "LIABILITY_$counter",
            "liabilityAtorBeforeClose" => $value['liabilityAtorBeforeClose'] ? 'true' : 'false',
        ];
        $data = array_merge($value, $data);
        $xmlContent .= generateXML::replace($xmlArr['LIABILITIES'], $data);
        $relationshipLiabilityBorrower .= '<RELATIONSHIP SequenceNumber="' . $counter . '" xlink:from="LIABILITY_' . $counter . '" xlink:to="BORROWER_1" xlink:arcrole="urn:fdc:mismo.org:2009:residential/LIABILITY_IsAssociatedWith_ROLE" />';
        $counter++;
    }
}
$xmlContent .= $xmlArr['LIABILITIES_END'];

$secondaryBrokerInfo = $fileData['SecondaryBrokerInfo'] ?? [];
if (!empty($secondaryBrokerInfo)) {
    $xmlContent .= generateXML::replace($xmlArr['SECONDARY_BROKER_INFO'], $secondaryBrokerInfo);
}
$xmlContent .= $xmlArr['SECONDARY_BROKER_INFO_END'];

$LMRInfo = $fileData['LMRInfo'] ?? [];
$PCInfo = $fileData['PCInfo'] ?? [];
$brokerInfo = $fileData['BrokerInfo'] ?? [];
$incomeInfo = $fileData['incomeInfo'] ?? [];
$ResponseInfo = $fileData['ResponseInfo'] ?? [];
$listingRealtorInfo = $fileData['listingRealtorInfo'] ?? [];
$fileHMLONewLoanInfo = $fileData['fileHMLONewLoanInfo'] ?? [];
$fileCalculatedValues = $fileData['fileCalculatedValues'] ?? [];
$fileHMLOBackGroundInfo = $fileData['fileHMLOBackGroundInfo'] ?? [];
unset($brokerInfo['ssnNumber']);

$QAInfo = $fileData['QAInfo'] ?? [];
$bDemoInfo = glMismo34Constants::$borrowerDemoInfo[$QAInfo['bDemoInfo']] ?? '';
$BGender = glMismo34Constants::$borrowerGender[$QAInfo['BGender']] ?? '';
$BRace = glMismo34Constants::$borrowerRace[$QAInfo['BRace']] ?? '';
$BEthnicity = glMismo34Constants::$borrowerEthnicity[$QAInfo['BEthnicity']] ?? '';

$fileHMLOInfo = $fileData['fileHMLOInfo'] ?? [];
$borrowerCitizenship = glMismo34Constants::$borrowerCitizenship[$fileHMLOInfo['borrowerCitizenship']] ?? '';

$fileHMLOPropertyInfo = $fileData['fileHMLOPropertyInfo'] ?? [];
$lienPosition = glMismo34Constants::$lienPosition[$fileHMLOPropertyInfo['lienPosition']] ?? 'Other';

$filePropInfo = $fileData['FilePropInfo'] ?? [];
$borVestingInfo = glMismo34Constants::$borVestingInfo[$filePropInfo['borVestingInfo']] ?? '';
$isHouseProperty = $filePropInfo['isHouseProperty'];
$propertyUsageType = glMismo34Constants::$propertyUsageType[$isHouseProperty] ?? '';
$intentToOccupyType = $isHouseProperty === 'Owner Occupied' ? 'Yes' : 'No';

$file2Info = $fileData['file2Info'] ?? [];
$methodOfContactRole = explode(',', $file2Info['methodOfContact'])[0] ?? '';
switch ($methodOfContactRole) {
    case 'phone':
    case 'text':
        $methodOfContactValue = $LMRInfo['cellNumber'] ?? '';
        break;
    case 'email':
        $methodOfContactValue = $LMRInfo['borrowerEmail'] ?? '';
        break;
}

unset($fileData);
$datetime = new DateTime('now', new DateTimeZone('UTC'));
$applicationReceivedDate = new DateTime($LMRInfo['recordDate']);
$data = [
    'currentDateTime' => $datetime->format('Y-m-d\TH:i:s\Z'),
    'applicationReceivedDate' => $applicationReceivedDate->format('Y-m-d'),
    'HMDARaceType' => $BRace,
    'HMDAGenderType' => $BGender,
    'HMDAEthnicityType' => $BEthnicity,
    'applicationTakenMethodType' => $bDemoInfo,
    'propertyUsageType' => $propertyUsageType,
    'intentToOccupyType' => $intentToOccupyType,
    'citizenshipResidencyType' => $borrowerCitizenship,
    'propertyOwnerStatusType' => $borVestingInfo,
    'methodOfContactRole' => ucfirst($methodOfContactRole),
    'methodOfContactValue' => $methodOfContactValue ?? '',
    'relationshipAssetBorrower' => $relationshipAssetBorrower,
    'relationshipLiabilityBorrower' => $relationshipLiabilityBorrower,
    'HOEPAStatus' => $QAInfo['HOEPAStatus'] === 1 ? 'true' : 'false',
    'HMDARaceRefusalIndicator' => $QAInfo['BRace'] === 1 ? 'true' : 'false',
    'HMDAGenderRefusalIndicator' => $QAInfo['BGender'] === 3 ? 'true' : 'false',
    'HMDAEthnicityRefusalIndicator' => in_array($QAInfo['PublishBInfo'], [1, 3]) ? 'true' : 'false',
    'lienPosition' => $fileHMLOPropertyInfo['lienPosition'] === 1 ? 'FirstLien' : 'Second',
    'balloonPayment' => $fileHMLOPropertyInfo['balloonPayment'] === 'Yes' ? 'true' : 'false',
    'undisclosedBorrowedFundsIndicator' => $fileHMLOPropertyInfo['borrowingMoney'] === 'Yes' ? 'true' : 'false',
    'undisclosedCreditApplicationIndicator' => $fileHMLOPropertyInfo['applyNewCredit'] === 'Yes' ? 'true' : 'false',
    'undisclosedMortgageApplicationIndicator' => $fileHMLOPropertyInfo['applyOtherLoan'] === 'Yes' ? 'true' : 'false',
    'specialBorrowerSellerRelationshipIndicator' => $fileHMLOPropertyInfo['famBizAffil'] === 'Yes' ? 'true' : 'false',
    'borrowerCount' => $LMRInfo['isCoBorrower'] ? '2' : '1',
    'interestOnlyIndicator' => $LMRInfo['lien1Terms'] === 'Interest Only' ? 'true' : 'false',
    'constructionLoanIndicator' => empty($filePropInfo['propConstructionType']) ? 'false' : 'true',
    'partyToLawsuitIndicator' => $fileHMLOBackGroundInfo['hasBorAnyActiveLawsuits'] === 'Yes' ? 'true' : 'false',
    'bankruptcyIndicator' => $fileHMLOBackGroundInfo['isBorDecalredBankruptPastYears'] === 'Yes' ? 'true' : 'false',
    'presentlyDelinquentIndicator' => $fileHMLOBackGroundInfo['isBorPresenltyDelinquent'] === 'Yes' ? 'true' : 'false',
    'outstandingJudgmentsIndicator' => $fileHMLOBackGroundInfo['isAnyBorOutstandingJudgements'] === 'Yes' ? 'true' : 'false',
    'priorPropertyShortSaleCompletedIndicator' => $fileHMLOBackGroundInfo['previouslyHadShortSale'] === 'Yes' ? 'true' : 'false',
    'priorPropertyForeclosureCompletedIndicator' => $fileHMLOBackGroundInfo['hasBorBeenForeclosed'] === 'Yes' ? 'true' : 'false',
    'amortizationType' => $fileHMLONewLoanInfo['amortizationType'] === 'Adjustable' ? 'AdjustableRate' : 'Fixed',
    'propertyProposedCleanEnergyLienIndicator' => $fileHMLONewLoanInfo['additionalPropertyRestrictions'] === 'Yes' ? 'true' : 'false',
    'employmentTimeInLineOfWorkMonthsCount' => (int)$incomeInfo['borLineOfWorkProfession'] * 12,
    'specialBorrowerEmployerRelationshipIndicator' => $incomeInfo['employedByOtherParty'] ? 'true' : 'false',
    'employmentBorrowerSelfEmployedIndicator' => $incomeInfo['employedInfo1'] === 'Self-Employed' ? 'true' : 'false',
    'totalHouseHoldIncome' => round($incomeInfo['primTotalHouseHoldIncome'] + $incomeInfo['coTotalHouseHoldIncome'], 2),
];


$data = array_merge($fileCalculatedValues, $listingRealtorInfo, $LMRInfo, $filePropInfo, $fileHMLOPropertyInfo, $QAInfo, $ResponseInfo, $file2Info, $fileHMLOInfo, $incomeInfo, $brokerInfo, $PCInfo, $data);
$xmlContent = generateXML::replace($xmlContent, $data);

// Headers for file download
$newFileName = "FM34Export_" . $LMRInfo['loanNumber'] . "_" . date('Y-m-d-H-i-s') . ".xml";
header('Content-Type: application/xml');
header('Content-Disposition: attachment; filename="' . $newFileName . '"');
header('Content-Length: ' . strlen($xmlContent));
echo $xmlContent;
exit();
