<?php

namespace models;

use models\composite\oBranch\getBranchesForAgent;
use models\constants\gl\glUserGroup;
use models\lendingwise\db\tblAdminUsers_db;
use models\lendingwise\db\tblAgent_db;
use models\lendingwise\db\tblBranch_db;
use models\lendingwise\db\tblBranchModules_db;
use models\lendingwise\db\tblClient_db;
use models\lendingwise\db\tblPCModules_db;
use models\lendingwise\db\tblProcessingCompany_db;
use models\lendingwise\tblAdminUsers;
use models\lendingwise\tblAgent;
use models\lendingwise\tblBranch;
use models\lendingwise\tblBranchModules;
use models\lendingwise\tblClient;
use models\lendingwise\tblPCModules;
use models\lendingwise\tblProcessingCompany;
use models\lendingwise\db\tblProcessingCompanyLMRClientType_db;
use models\lendingwise\tblProcessingCompanyLMRClientType;
use models\PageVariables\User;
use models\types\strongType;

// http://dev.theloanpost.com/HMLOWebForm.php?bRc=b2a85f6feca79ecb&fOpt=8e614f58c0d670e4&lid=59ca4a9a3f907c49&opt=e637bd2de4735343&ft=HMLO&op=69ae9aa7bfc04392&UType=00eedfe87c52b964
class PageVariables extends strongType
{
    public static ?string $subscriberID = null;
    public static ?string $AEUserType = null;
    public static ?string $BranchUserType = null;
    public static ?array $CFPBAuditingPC = null;
    public static ?string $DIYUser = null;
    public static ?string $DOMAIN = null;
    public static ?array $HMLOListOfRepairsAmtArray = null;
    public static ?array $HMLOListOfRepairsDescFieldsArray = null;
    public static ?string $NumberOfLinksToShow = null;
    public static ?string $PCAllowToCreateBranch = null;
    public static ?string $PCExpiryDate = null;
    public static ?int $PCID = null;
    public static ?string $PCLMRClientType = null;
    public static ?string $PCOwed = null;
    public static ?string $PCTimeZone = null;
    public static ?array $SMTPAPIInfoArray = null;
    public static ?array $SMTPInfoArray = null;
    public static ?string $accessRestricted = null;
    public static ?array $accessRestrictionPC = null;
    public static ?string $acqualifyOptStatus = null;
    public static ?string $activeTab = null;
    public static ?string $allow = null;
    public static ?array $allowAgentToSeeFile = null;
    public static ?string $allowAgentWorkflowEdit = null;
    public static ?string $allowAutomation = null;
    public static ?string $allowBranchToEditAgentProfile = null;
    public static ?string $allowBranchWorkflowEdit = null;
    public static ?string $allowCFPBAuditing = null;
    public static ?string $allowClientToCreateHMLOFile = null;
    public static ?string $allowDashboard = null;
    public static ?string $allowESignService = null;
    public static ?string $allowEmailCampaign = null;
    public static ?string $allowEmpToCopyFile = null;
    public static ?string $allowEmpToCreateAgent = null;
    public static ?string $allowEmpToCreateBranch = null;
    public static ?string $allowEmpToCreateFiles = null;
    public static ?string $allowEmpToCreateTasks = null;
    public static ?string $allowEmpToLogin = null;
    public static ?string $allowEmpToSeeAgent = null;
    public static ?string $allowEmpToSeeDashboard = null;
    public static ?string $allowEmpToViewAllFiles = null;
    public static ?string $allowExcelDownload = null;
    public static ?string $allowLMRAEToEditCommission = null;
    public static ?string $allowPCToCreateBranch = null;
    public static ?string $allowPCToCreateEmployee = null;
    public static ?string $allowPCToOnOffAgentLogin = null;
    public static ?string $allowPCToViewAddOn = null;
    public static ?string $allowPCUserToSubmitCFPB = null;
    public static ?string $allowPCUsersToMarketPlace = null;
    public static ?string $allowPCUsersToMarketPlacePublic = null;
    public static ?string $allowPeerstreet = null;
    public static ?string $allowServicing = null;
    public static ?string $allowToAccessAdminReqDocs = null;
    public static ?string $allowToAccessDocs = null;
    public static ?string $allowToAccessInternalLoanProgram = null;
    public static ?string $allowToAccessRAM = null;
    public static ?string $allowToCFPBSubmission = null;
    public static ?string $allowToCFPBSubmitForPC = null;
    public static ?string $allowToChangeOrAssignBranchForFile = null;
    public static ?string $allowToCreateBranch = null;
    public static ?string $allowToCreateFiles = null;
    public static ?string $allowToCreateTasks = null;
    public static ?string $allowToDeleteUploadedDocs = null;
    public static ?string $allowToEditCommission = null;
    public static ?string $allowToEditContact = null;
    public static ?string $allowToEditLenderList = null;
    public static ?string $allowToEditMyFile = null;
    public static ?string $allowToEditOwnNotes = null;
    public static ?string $allowToLASubmission = null;
    public static ?string $allowToLASubmitForPC = null;
    public static ?string $allowToLockLoanFile = null;
    public static ?string $allowToOnOffAgentLogin = null;
    public static ?string $allowToSeeAlowareTab = null;
    public static ?string $allowToSeeBillingSectionForFile = null;
    public static ?string $allowToSeeCommission = null;
    public static ?string $allowToSendFax = null;
    public static ?string $allowToUpdateFileAdminSection = null;
    public static ?string $allowToViewAllFiles = null;
    public static ?string $allowToViewCFPBPipeline = null;
    public static ?string $allowToViewContactsList = null;
    public static ?string $allowToViewCreditScreening = null;
    public static ?string $allowToViewMarketPlace = null;
    public static ?string $allowToupdateFileAndClient = null;
    public static ?string $allowUserToAccessRAM = null;
    public static ?string $allowUserToDeleteUploadedDocs = null;
    public static ?int $allowUserToSendMsgToBorrower = null;
    public static ?string $allowed = null;
    public static ?int $allowedToSendFileDesignation = null;
    public static ?int $allowedToSendHomeownerLink = null;
    public static ?array $apiAcessPC = null;
    public static ?string $asgEmpId = null;
    public static ?string $assignedPCID = null;
    public static ?array $borPreviousAddress = null;
    public static ?array $borrowerInfo = null;
    public static ?string $branchId = null;
    public static ?string $branchesName = null;
    public static ?string $changeDIYPlan = null;
    public static ?string $chkHttps = null;
    public static ?string $cliType = null;
    public static ?string $clientSelectReferralCode = null;
    public static ?array $coBorrowerInfo = null;
    public static ?array $contacts = null;
    public static ?string $convertNewBRIntoEmpOwnBR = null;
    public static ?string $cssVr = null;
    public static ?array $domainArray = null;
    public static ?string $eOpt = null;
    public static ?string $eid = null;
    public static ?string $env = null;
    public static ?string $errorMsg = null;
    public static ?array $exInArray = null;
    public static ?array $exResultArray = null;
    public static ?array $executiveInfoArray = null;
    public static ?int $externalBroker = null;
    public static ?string $feedbackOfFAS = null;
    public static ?array $fileInfo = null;
    public static ?array $fileModuleInfo = null;
    public static ?array $fileTabsKey = null;
    public static ?string $forensicAuditInterest = null;
    public static ?string $forensicAuditService = null;
    public static ?string $ftModuleCode = null;
    public static ?string $glHMLOAttachmentPKGID = null;
    public static ?array $glPreApprovalPkgID = null;
    public static ?array $glRAMAccessPC = null;
    public static ?string $glThirdPartyServices = null;
    public static ?string $glThirdPartyServicesLegalDocs = null;
    public static ?string $glUseMyNameAndEmail = null;
    public static ?array $glborrowerMissingDocPkgID = null;
    public static ?array $govtInfoArray = null;
    public static ?string $gpSecurityInstrumentArray = null;
    public static ?array $inFileArray = null;
    public static ?string $ip = null;
    public static ?string $isHMLO = null;
    public static ?string $isPCActive = null;
    public static ?string $isPCActiveAloware = null;
    public static ?string $isPL = null;
    public static ?string $isPLO = null;
    public static ?string $isSysNotesPrivate = null;
    public static ?string $isUserActive = null;
    public static ?string $jsVr = null;
    public static ?string $loadMenu = null;
    public static ?string $loginError = null;
    public static ?string $logoExit = null;
    public static ?string $maxN4Export = null;
    public static ?string $mgrAllowToCreateBranch = null;
    public static ?string $mgrAllowToCreateEmployee = null;
    public static ?string $moduleCode = null;
    public static ?array $moduleFileTabInfo = null;
    public static ?string $moduleName = null;
    public static ?array $myFileInfo = null;
    public static ?string $myForm = null;
    public static ?array $myModulesArray = null;
    public static ?array $myPCServicesArray = null;
    public static ?array $myServicesArray = null;
    public static ?array $moduleServiceArray = null;
    public static ?string $noOfRecords = null;
    public static ?string $noOfRecordsPerPage = null;
    public static ?string $noOfSessions = null;
    public static ?string $owedFlag = null;
    public static ?string $pageNumber = null;
    public static ?string $pcAcqualifyStatus = null;
    public static ?string $pcClient_id = null;
    public static ?string $pcPriceEngineStatus = null;
    public static ?string $permissionToREST = null;
    public static ?string $ploAccess = null;
    public static ?string $private = null;
    public static ?string $privateSite = null;
    public static ?string $processorAssignedCompany = null;
    public static ?string $propertyNeedRehab = null;
    public static ?string $publicUser = null;
    public static ?array $repairs = null;
    public static ?string $restrict = null;
    public static ?string $secondaryBrokerNumber = null;
    public static ?string $seeBilling = null;
    public static ?string $shareThisFile = null;
    public static ?string $showFSCntInDash = null;
    public static ?string $showStartLoanNumber = null;
    public static ?string $showStateMapInDash = null;
    public static ?string $simLogin = null;
    public static ?string $siteUrl = null;
    public static ?array $stateSummaryArray = null;
    public static ?string $subscribePCToHOME = null;
    public static ?string $subscribeToHOME = null;
    public static ?string $subscribeToREST = null;
    public static ?string $theme = null;
    public static ?string $thirdPartyServiceCSR = null;
    public static ?string $thirdPartyServices = null;
    public static ?string $thirdPartyServicesProducts = null;
    public static ?string $titleBorName = null;
    public static ?string $userCell = null;
    public static ?string $userEmail = null;
    public static ?string $userFName = null;
    public static ?string $userGroup = null;
    public static ?string $userId = null;
    public static ?array $userInfoArray = null;
    public static ?string $userLName = null;
    public static ?string $userLogo = null;
    public static ?string $userLogoHeight = null;
    public static ?string $userLogoWidth = null;
    public static ?string $userName = null;
    public static ?int $userNumber = null;
    public static ?string $userPhone = null;
    public static ?string $userPriceEngineStatus = null;
    public static ?string $userRole = null;
    public static ?string $userSeeBilling = null;
    public static ?string $userTimeZone = null;
    public static ?string $userType = null;
    public static ?string $ut = null;
    public static ?string $viewPrivateNotes = null;
    public static ?string $viewPublicNotes = null;
    public static ?string $viewSubmitOfferTab = null;
    public static ?string $lastActivity = null;
    public static ?int $allowToSeeAllBrokers = null;
    public static ?int $isPCAllowEmailCampaign = null;
    public static ?int $allowToSelectFromEmail = null;

    public static ?int $allowToMassUpdate = null;
    public static ?int $allowPropertyAddressAutoLookUp = null;
    public static ?int $nonInclusivePerDiem = null;
    public static ?int $allowCustomFields = null;
    public static ?string $avatar = null;
    public static ?string $createdDate = null;
    public static ?int $docWizard = null;
    public static ?int $showSysGenNote = null;

    public static ?array $agentBranchArray = [];

    public static ?int $allowToEdit = null;
    public static ?string $allowToCopyFile = null;
    public static ?string $allowBranchManagerToLogin = null;
    public static ?string $allowBranchToAddAgent = null;
    public static ?string $loggedInEmail = null;

    public static ?tblAgent $CurrentAgent = null;
    public static ?tblAdminUsers $CurrentEmployee = null;
    public static ?tblProcessingCompany $ProcessingCompany = null;
    public static ?tblProcessingCompany $AssignedProcessingCompany = null;
    public static ?tblClient $CurrentClient = null;
    public static ?tblBranch $CurrentBranch = null;
    public static ?int $allowAllBackofficeUsersForFromEmail = null;
    public static ?int $allowBranchEmailAsFromEmail = null;
    public static ?string $customEmailForEmail = null;
    public static ?int $allowBrokerEmailAsFromEmail = null;
    public static ?int $allowLoanofficerEmailAsFromEmail = null;
    public static array $fromEmailUsers = [];

    public static ?bool $isGhosted = null;
    public static ?array $GhostDetails = null;
    public static ?bool $hideNotices = false;
    public static ?int $identifierForMinAutoGeneration = null;
    public static ?string $procCompLogo = null;
    public static ?string $branchLogo = null;

    public static function AgentBranches(): ?array
    {
        if (is_null(self::$agentBranchArray)) {
            $rs = getBranchesForAgent::getReport(['brokerNumber' => self::$userNumber]);
            foreach ($rs as $item) {
                self::$agentBranchArray[$item['executiveId']] = $item;
            }
        }
        return self::$agentBranchArray;
    }

    public static function initProcessingCompany(?int $PCID)
    {
        if (!$PCID
            || (self::$ProcessingCompany && self::$ProcessingCompany->PCID = $PCID)) {
            return;
        }

        self::$ProcessingCompany = tblProcessingCompany::Get([
            tblProcessingCompany_db::COLUMN_PCID         => self::$PCID,
            tblProcessingCompany_db::COLUMN_ACTIVESTATUS => 1,
            tblProcessingCompany_db::COLUMN_DSTATUS      => 0,
        ]);

        self::$isPLO = trim(self::$ProcessingCompany->isPLO);
        self::$subscribeToREST = trim(self::$ProcessingCompany->subscribeToREST);
        self::$isPCActive = trim(self::$ProcessingCompany->activeStatus);
        self::$PCExpiryDate = trim(self::$ProcessingCompany->PCExpiryDate);
        self::$PCOwed = trim(self::$ProcessingCompany->owedFlag);
        self::$allowToEditLenderList = trim(self::$ProcessingCompany->allowToEditLenderList);
        self::$processorAssignedCompany = trim(self::$ProcessingCompany->processingCompanyName);
        self::$PCTimeZone = trim(self::$ProcessingCompany->timeZone);
        self::$allowToLASubmitForPC = trim(self::$ProcessingCompany->allowToLASubmitForPC);
        self::$allowToCFPBSubmitForPC = trim(self::$ProcessingCompany->allowToCFPBSubmitForPC);
        self::$allowPCUserToSubmitCFPB = trim(self::$ProcessingCompany->allowPCUserToSubmitCFPB);
        self::$subscribePCToHOME = trim(self::$ProcessingCompany->subscribeToHOME);
        self::$theme = trim(self::$ProcessingCompany->theme);
        self::$isSysNotesPrivate = trim(self::$ProcessingCompany->isSysNotesPrivate);
        self::$showFSCntInDash = trim(self::$ProcessingCompany->showFSCntInDash);
        self::$showStateMapInDash = trim(self::$ProcessingCompany->showStateMapInDash);
        self::$showStartLoanNumber = trim(self::$ProcessingCompany->showStartLoanNumber);

        self::$pcClient_id = trim(self::$ProcessingCompany->client_id);
        self::$allowPeerstreet = self::$ProcessingCompany->allowPeerstreet;
        self::$isPCActiveAloware = trim(self::$ProcessingCompany->allowToCreateAloware);
        self::$allowPCUsersToMarketPlace = trim(self::$ProcessingCompany->allowPCToMarketPlace);
        self::$allowPCUsersToMarketPlacePublic = trim(self::$ProcessingCompany->allowPCToMarketPlacePublic);
        self::$glThirdPartyServices = trim(self::$ProcessingCompany->thirdPartyServices);
        self::$thirdPartyServiceCSR = self::$ProcessingCompany->thirdPartyServiceCSR;
        self::$thirdPartyServicesProducts = self::$ProcessingCompany->thirdPartyServicesProducts;
        self::$shareThisFile = 0; // trim(self::$ProcessingCompany->shareThisFile ?? ''); - this is not in the processing company table
        self::$loadMenu = trim(self::$ProcessingCompany->loadMenu);
        self::$pcAcqualifyStatus = trim(self::$ProcessingCompany->pcAcqualifyStatus);
        self::$allowAutomation = trim(self::$ProcessingCompany->allowAutomation);
        self::$allowServicing = trim(self::$ProcessingCompany->allowServicing);
        self::$pcPriceEngineStatus = trim(self::$ProcessingCompany->pcPriceEngineStatus);
        self::$allowESignService = trim(self::$ProcessingCompany->allowESignService);
        self::$isPCAllowEmailCampaign = intval(self::$ProcessingCompany->allowEmailCampaign);
        self::$docWizard = self::$ProcessingCompany->docWizard;
        self::$identifierForMinAutoGeneration = self::$ProcessingCompany->identifierForMinAutoGeneration;
        self::$procCompLogo = self::$ProcessingCompany->procCompLogo;

//        $res = tblProcessingCompanyLMRClientType::GetAll([
//            tblProcessingCompanyLMRClientType_db::COLUMN_PCID => self::$PCID,
//        ]);
//
//        self::$myServicesArray = [];
//        foreach ($res as $item) {
//            self::$myServicesArray[] = trim($item->LMRClientType);
//        }

        $res = tblPCModules::GetAll([
            tblPCModules_db::COLUMN_PCID             => self::$PCID,
            tblPCModules_db::COLUMN_SUBSCRIBEDSTATUS => 1,
            tblPCModules_db::COLUMN_ACTIVESTATUS     => 1,
        ]);

        self::$myModulesArray = [];
        foreach ($res as $item) {
            self::$myModulesArray[] = trim($item->moduleCode);
        }

    }

    public static function initAssignedProcessingCompany(?int $assignedPCID)
    {
        if (self::$AssignedProcessingCompany && self::$AssignedProcessingCompany->PCID == $assignedPCID) {
            return;

        }

        self::$assignedPCID = $assignedPCID;
        self::$AssignedProcessingCompany = tblProcessingCompany::Get([
            tblProcessingCompany_db::COLUMN_PCID => self::$assignedPCID,
        ]);
    }

    public static function initAgent()
    {
        if (self::$CurrentAgent && self::$CurrentAgent->userNumber == self::$userNumber) {
            return;
        }

        self::$CurrentAgent = tblAgent::Get([
            tblAgent_db::COLUMN_USERNUMBER => self::$userNumber,
            tblAgent_db::COLUMN_STATUS     => 1,
        ]);

        if (!self::$CurrentAgent) {
            return;
        }

        self::$loggedInEmail = self::$CurrentAgent->email;

        self::$allowToSeeAlowareTab = trim(self::$CurrentAgent->allowToCreateAloware);
        self::$allowToEditMyFile = trim(self::$CurrentAgent->allowAgentToEditLMRFile);
        self::$allowToUpdateFileAdminSection = trim(self::$CurrentAgent->allowedToUpdateFiles);
        self::$userSeeBilling = trim(self::$CurrentAgent->seeBilling);
        self::$allowToDeleteUploadedDocs = trim(self::$CurrentAgent->allowedToDeleteUplodedDocs);
        self::$allowDashboard = trim(self::$CurrentAgent->allowAgentToSeeDashboard);
        self::$allowToCreateFiles = trim(self::$CurrentAgent->allowAgentToCreateFiles);
        self::$allowToCreateTasks = trim(self::$CurrentAgent->allowAgentToCreateTasks);
        self::$changeDIYPlan = trim(self::$CurrentAgent->changeDIYPlan);
        self::$allowToAccessDocs = trim(self::$CurrentAgent->allowAgentToAccessLMRDocs);
        self::$allowedToSendHomeownerLink = trim(self::$CurrentAgent->allowAgentToSendHomeownerLink);
        self::$viewPrivateNotes = trim(self::$CurrentAgent->allowToAccessPrivateNotes);
        self::$allowToEditOwnNotes = trim(self::$CurrentAgent->allowedToEditOwnNotes);
        self::$permissionToREST = trim(self::$CurrentAgent->permissionToREST);
        self::$allowExcelDownload = trim(self::$CurrentAgent->allowedToExcelReport);
        self::$userTimeZone = trim(self::$CurrentAgent->timeZone);
        self::$allowToLASubmission = trim(self::$CurrentAgent->allowToLASubmit);
        self::$subscribeToHOME = trim(self::$CurrentAgent->subscribeToHOME);
        self::$allowEmailCampaign = trim(self::$CurrentAgent->allowEmailCampaign);
        self::$allowToSendFax = trim(self::$CurrentAgent->allowToSendFax);

        self::$allowToEditCommission = trim(self::$CurrentAgent->allowAgentToEditCommission);
        self::$allowToSeeCommission = trim(self::$CurrentAgent->allowAgentToSeeCommission);
        self::$allowedToSendFileDesignation = trim(self::$CurrentAgent->allowToSendFileDesignation);
        self::$viewPublicNotes = trim(self::$CurrentAgent->allowAgentToSeePublicNotes);

        self::$allowToCFPBSubmission = trim(self::$CurrentAgent->allowToCFPBSubmit);
        self::$allowToViewCFPBPipeline = trim(self::$CurrentAgent->allowToViewCFPBPipeline);
        self::$allowUserToAccessRAM = trim(self::$CurrentAgent->allowToAccessRAM);
        self::$allowAgentWorkflowEdit = trim(self::$CurrentAgent->allowWorkflowEdit);
        self::$allowToLockLoanFile = trim(self::$CurrentAgent->allowToLockLoanFileAgent);
        self::$allowToViewMarketPlace = trim(self::$CurrentAgent->allowToViewMarketPlace);
        self::$allowToupdateFileAndClient = trim(self::$CurrentAgent->allowToupdateFileAndClient);
        self::$thirdPartyServices = trim(self::$CurrentAgent->thirdPartyServices);
        self::$thirdPartyServicesLegalDocs = trim(self::$CurrentAgent->thirdPartyServicesLegalDocs);
        self::$shareThisFile = trim(self::$CurrentAgent->shareThisFile);
        self::$allowToViewCreditScreening = trim(self::$CurrentAgent->allowToViewCreditScreening);
        self::$acqualifyOptStatus = trim(self::$CurrentAgent->acqualifyOptStatus);
        self::$avatar = trim(self::$CurrentAgent->avatar);
        self::$createdDate = trim(self::$CurrentAgent->registerDate);

        self::$externalBroker = trim(self::$CurrentAgent->externalBroker); //Loan officer Flag
        if (self::$externalBroker == 1) {
            self::$secondaryBrokerNumber = self::$userNumber;// assigning Loan officer value as $secondary Broker number
        }

        self::$viewSubmitOfferTab = trim(self::$CurrentAgent->allowToSubmitOffer);
        self::$userPriceEngineStatus = trim(self::$CurrentAgent->userPriceEngineStatus);
        self::$allowToAccessInternalLoanProgram = trim(self::$CurrentAgent->allowToAccessInternalLoanProgram);
        self::$allowToCopyFile = trim(self::$CurrentAgent->allowToCopyFile);
        self::$allowToSeeAllBrokers = trim(self::$CurrentAgent->allowToSeeAllBrokers);
        self::$allowToMassUpdate = (int)trim(self::$CurrentAgent->allowToMassUpdate);
        self::$allowToViewContactsList = (int)trim(self::$CurrentAgent->allowToViewContactsList);
    }

    public static function initBranch()
    {
        if (self::$CurrentBranch && self::$CurrentBranch->executiveId == self::$userNumber) {
            return;
        }

        self::$CurrentBranch = tblBranch::Get([
            tblBranch_db::COLUMN_EXECUTIVEID  => self::$userNumber,
            tblBranch_db::COLUMN_ACTIVESTATUS => 1,
        ]);

        if (!self::$CurrentBranch) {
            return;
        }

        self::$loggedInEmail = self::$CurrentBranch->executiveEmail;

        self::$allowToSeeAlowareTab = trim(self::$CurrentBranch->AllowToCreateAloware);
        self::$allowToEditMyFile = trim(self::$CurrentBranch->allowLMRAEToEditFile);
        self::$allowToUpdateFileAdminSection = trim(self::$CurrentBranch->allowToUpdateFileAdminSection);
        self::$allowExcelDownload = trim(self::$CurrentBranch->allowedToExcelReport);
        self::$permissionToREST = trim(self::$CurrentBranch->permissionToREST);
        self::$viewPrivateNotes = trim(self::$CurrentBranch->seePrivate);
        self::$allowToEditOwnNotes = trim(self::$CurrentBranch->allowedToEditOwnNotes);
        self::$allowToEditCommission = trim(self::$CurrentBranch->allowLMRAEToEditCommission);
        self::$allowToSeeCommission = trim(self::$CurrentBranch->allowBranchToSeeCommission);
        self::$allowToAccessDocs = trim(self::$CurrentBranch->allowLMRAEToAccessDocs);
        self::$allowToDeleteUploadedDocs = trim(self::$CurrentBranch->allowedToDeleteUplodedDocs);
        self::$allowDashboard = trim(self::$CurrentBranch->allowBranchToSeeDashboard);
        self::$allowToCreateFiles = trim(self::$CurrentBranch->allowBranchToCreateFiles);
        self::$allowToCreateTasks = trim(self::$CurrentBranch->allowBranchToCreateTasks);
        self::$changeDIYPlan = trim(self::$CurrentBranch->changeDIYPlan);
        self::$allowToOnOffAgentLogin = trim(self::$CurrentBranch->allowLMRToOnOffAgentLogin);
        self::$allowBranchToEditAgentProfile = trim(self::$CurrentBranch->allowLMRToEditAgentProfile);
        self::$allowedToSendHomeownerLink = trim(self::$CurrentBranch->allowToSendHomeownerLink);
        self::$AEUserType = trim(self::$CurrentBranch->userType);
        self::$userTimeZone = trim(self::$CurrentBranch->timeZone);
        self::$allowToLASubmission = trim(self::$CurrentBranch->allowToLASubmit);
        self::$subscribeToHOME = trim(self::$CurrentBranch->subscribeToHOME);
        self::$allowEmailCampaign = trim(self::$CurrentBranch->allowEmailCampaign);
        self::$allowToSendFax = trim(self::$CurrentBranch->allowToSendFax);
        self::$allowedToSendFileDesignation = trim(self::$CurrentBranch->allowToSendFileDesignation);
        self::$viewPublicNotes = trim(self::$CurrentBranch->allowBranchToSeePublicNotes);
        self::$allowToCFPBSubmission = trim(self::$CurrentBranch->allowToCFPBSubmit);

        self::$allowToViewCFPBPipeline = trim(self::$CurrentBranch->allowToViewCFPBPipeline);
        self::$allowUserToAccessRAM = trim(self::$CurrentBranch->allowToAccessRAM);
        self::$allowBranchManagerToLogin = trim(self::$CurrentBranch->allowBranchManagerToLogin);
        self::$allowBranchWorkflowEdit = trim(self::$CurrentBranch->allowWorkflowEdit);
        self::$allowToLockLoanFile = trim(self::$CurrentBranch->allowToLockLoanFileBranch);
        self::$allowBranchToAddAgent = trim(self::$CurrentBranch->allowToAddAgent);
        self::$allowToViewMarketPlace = trim(self::$CurrentBranch->allowToViewMarketPlace);
        self::$allowToupdateFileAndClient = trim(self::$CurrentBranch->allowToupdateFileAndClient);
        self::$thirdPartyServices = trim(self::$CurrentBranch->thirdPartyServices);
        self::$thirdPartyServicesLegalDocs = trim(self::$CurrentBranch->thirdPartyServicesLegalDocs);
        self::$shareThisFile = trim(self::$CurrentBranch->shareThisFile);
        self::$viewSubmitOfferTab = trim(self::$CurrentBranch->allowToSubmitOffer);
        self::$allowToViewCreditScreening = trim(self::$CurrentBranch->allowToViewCreditScreening);
        self::$acqualifyOptStatus = trim(self::$CurrentBranch->acqualifyOptStatus);
        self::$userPriceEngineStatus = trim(self::$CurrentBranch->userPriceEngineStatus);
        self::$allowToAccessInternalLoanProgram = trim(self::$CurrentBranch->allowToAccessInternalLoanProgram);
        self::$allowToCopyFile = trim(self::$CurrentBranch->allowToCopyFile);
        self::$allowToMassUpdate = (int)trim(self::$CurrentBranch->allowToMassUpdate);
        self::$avatar = trim(self::$CurrentBranch->avatar);
        self::$createdDate = trim(self::$CurrentBranch->executiveCreatedDate);
        self::$branchLogo = trim(self::$CurrentBranch->logo);

        $res = tblBranchModules::GetAll([
            tblBranchModules_db::COLUMN_BRANCHID => self::$userNumber,
        ]);

        self::$myModulesArray = [];

        foreach ($res as $item) {
            self::$myModulesArray[] = trim($item->moduleCode);
        }
    }

    public static function initClient()
    {
        if (self::$CurrentClient && self::$CurrentClient->CID == self::$userNumber) {
            return;
        }

        self::$CurrentClient = tblClient::Get([
            tblClient_db::COLUMN_CID => self::$userNumber,
        ]);

        self::$allowToCreateTasks = 1;
        self::$viewPrivateNotes = 0;

        self::$allowToAccessDocs = 0;
        self::$shareThisFile = 0; //NEVER show Share This File

        if (!PageVariables::$CurrentClient) {
            return;
        }

        self::$loggedInEmail = self::$CurrentClient->clientEmail;


        self::$userTimeZone = trim(PageVariables::$CurrentClient->timeZone);
        self::$allowToLockLoanFile = trim(PageVariables::$CurrentClient->allowToLockLoanFileClient);
        self::$allowToViewMarketPlace = trim(PageVariables::$CurrentClient->allowToViewMarketPlace);
        self::$viewSubmitOfferTab = trim(PageVariables::$CurrentClient->allowToSubmitOffer);
        self::$clientSelectReferralCode = trim(PageVariables::$CurrentClient->referralCode);
    }

    public static function initAdminUser()
    {
        if (self::$CurrentEmployee && self::$CurrentEmployee->AID == self::$userNumber) {
            return;
        }

        self::$CurrentEmployee = tblAdminUsers::Get([
            tblAdminUsers_db::COLUMN_AID          => self::$userNumber,
            tblAdminUsers_db::COLUMN_ACTIVESTATUS => 1,
        ]);

        if (!self::$CurrentEmployee) {
            return;
        }

        self::$loggedInEmail = self::$CurrentEmployee->email;


        self::$allowToSeeAlowareTab = trim(self::$CurrentEmployee->allowEmpToCreateAloware);
        self::$allowToEditMyFile = trim(self::$CurrentEmployee->allowBOToEditLMRFile);
        self::$allowExcelDownload = trim(self::$CurrentEmployee->allowExcelDownload);
        self::$allowToViewAllFiles = trim(self::$CurrentEmployee->allowToViewAllFiles);
        self::$allowToEditOwnNotes = trim(self::$CurrentEmployee->allowedToEditOwnNotes);
        self::$userSeeBilling = trim(self::$CurrentEmployee->seeBilling);
        self::$allowEmailCampaign = trim(self::$CurrentEmployee->allowEmailCampaign);
        self::$allowToDeleteUploadedDocs = trim(self::$CurrentEmployee->allowedToDeleteUplodedDocs);
        self::$allowDashboard = trim(self::$CurrentEmployee->allowEmpToSeeDashboard);
        self::$allowToCreateFiles = trim(self::$CurrentEmployee->allowEmpToCreateFiles);
        self::$allowToCreateTasks = trim(self::$CurrentEmployee->allowEmpToCreateTasks);
        self::$permissionToREST = trim(self::$CurrentEmployee->permissionToREST);
        self::$viewPrivateNotes = trim(self::$CurrentEmployee->seePrivate);
        self::$changeDIYPlan = trim(self::$CurrentEmployee->changeDIYPlan);
        self::$allowedToSendHomeownerLink = trim(self::$CurrentEmployee->allowToSendHomeownerLink);
        self::$allowToUpdateFileAdminSection = trim(self::$CurrentEmployee->allowToUpdateFileAdminSection);
        self::$userTimeZone = trim(self::$CurrentEmployee->timeZone);
        self::$allowToLASubmission = trim(self::$CurrentEmployee->allowToLASubmit);
        self::$subscribeToHOME = trim(self::$CurrentEmployee->subscribeToHOME);
        self::$allowToSendFax = trim(self::$CurrentEmployee->allowToSendFax);

        self::$allowToEditCommission = trim(self::$CurrentEmployee->allowEmployeeToEditCommission);
        self::$allowToSeeCommission = trim(self::$CurrentEmployee->allowEmployeeToSeeCommission);
        self::$allowedToSendFileDesignation = trim(self::$CurrentEmployee->allowToSendFileDesignation);
        self::$viewPublicNotes = trim(self::$CurrentEmployee->allowEmpToSeePublicNotes);

        self::$allowToChangeOrAssignBranchForFile = trim(self::$CurrentEmployee->allowToChangeOrAssignBranchForFile);
        self::$allowToCFPBSubmission = trim(self::$CurrentEmployee->allowToCFPBSubmit);
        self::$allowToViewCFPBPipeline = trim(self::$CurrentEmployee->allowToViewCFPBPipeline);
        self::$allowUserToAccessRAM = trim(self::$CurrentEmployee->allowToAccessRAM);
        self::$allowEmpToCreateBranch = trim(self::$CurrentEmployee->allowEmpToCreateBranch);
        self::$convertNewBRIntoEmpOwnBR = trim(self::$CurrentEmployee->convertNewBRIntoEmpOwnBR);
        self::$allowToSeeBillingSectionForFile = trim(self::$CurrentEmployee->allowToSeeBillingSectionForFile);
        self::$allowEmpToCreateAgent = trim(self::$CurrentEmployee->allowEmpToCreateAgent);
        self::$allowEmpToSeeAgent = trim(self::$CurrentEmployee->allowEmpToSeeAgent);
        self::$allowToLockLoanFile = trim(self::$CurrentEmployee->allowToLockLoanFileEmpl);
        self::$allowToViewMarketPlace = trim(self::$CurrentEmployee->allowToViewMarketPlace);
        self::$allowToupdateFileAndClient = trim(self::$CurrentEmployee->allowToupdateFileAndClient ?? '');
        self::$thirdPartyServices = trim(self::$CurrentEmployee->thirdPartyServices);
        self::$thirdPartyServicesLegalDocs = trim(self::$CurrentEmployee->thirdPartyServicesLegalDocs);
        self::$shareThisFile = trim(self::$CurrentEmployee->shareThisFile);
        self::$allowToViewContactsList = trim(self::$CurrentEmployee->allowToViewContactsList);
        self::$viewSubmitOfferTab = trim(self::$CurrentEmployee->allowToSubmitOffer);
        self::$allowToViewCreditScreening = trim(self::$CurrentEmployee->allowToViewCreditScreening);
        self::$acqualifyOptStatus = trim(self::$CurrentEmployee->acqualifyOptStatus);
        self::$userPriceEngineStatus = trim(self::$CurrentEmployee->userPriceEngineStatus);
        self::$allowUserToSendMsgToBorrower = trim(self::$CurrentEmployee->allowUserToSendMsgToBorrower);
        self::$allowEmpToCopyFile = trim(self::$CurrentEmployee->allowEmpToCopyFile);
        self::$allowToSelectFromEmail = self::$CurrentEmployee->allowToSelectFromEmail;
        self::$allowAllBackofficeUsersForFromEmail = self::$CurrentEmployee->allowAllBackofficeUsersForFromEmail;
        self::$customEmailForEmail = self::$CurrentEmployee->customEmailForEmail;
        self::$allowBranchEmailAsFromEmail = self::$CurrentEmployee->allowBranchEmailAsFromEmail;
        self::$allowBrokerEmailAsFromEmail = self::$CurrentEmployee->allowBrokerEmailAsFromEmail;
        self::$allowLoanofficerEmailAsFromEmail = self::$CurrentEmployee->allowLoanofficerEmailAsFromEmail;
        self::$fromEmailUsers = self::$CurrentEmployee->getFromEmailUsers();

        self::$allowToMassUpdate = (int)self::$CurrentEmployee->allowToMassUpdate;
        self::$avatar = trim(self::$CurrentEmployee->avatar);
        self::$createdDate = trim(self::$CurrentEmployee->empCreatedDate);
    }

    public static function initUser(?int $userNumber, ?string $userGroup)
    {
        if (self::$CurrentAgent && self::$CurrentAgent->userNumber == $userNumber) {
            return;
        }

        if (self::$CurrentClient && self::$CurrentClient->CID == $userNumber) {
            return;
        }

        if (self::$CurrentEmployee && self::$CurrentEmployee->AID == $userNumber) {
            return;
        }

        if (self::$CurrentBranch && self::$CurrentBranch->executiveId == $userNumber) {
            return;
        }

        self::$userNumber = $userNumber;
        self::$userGroup = $userGroup;

        if (self::$userNumber) {
            switch (self::$userGroup) {
                case glUserGroup::USER_GROUP_AGENT:
                    self::initAgent();
                    break;

                case 'Client':
                    self::initClient();
                    break;

                case 'Auditor':
                case 'CFPB Auditor':
                case 'Auditor Manager':
                case glUserGroup::USER_GROUP_EMPLOYEE:
                    self::initAdminUser();
                    break;

                case glUserGroup::USER_GROUP_BRANCH:
                    self::initBranch();
                    break;
            }
        }
    }

    public static ?int $allowToEditProfile = null;
    public static ?int $noOfUsersAllowed = null;
    public static ?int $noOfUsersCreated = null;
    public static ?int $PCAllowToCreate = null;
    public static ?int $allowToSeeFeesInDashboard = null;

    public static ?int $allowNestedEntityMembers = null;
    public static ?int $thirdPartyServicesLegalDocs = null;

    // for timing page loads
    public static ?float $globalTimer = null;

    public static function Init(array $data)
    {
        self::$globalTimer = microtime(true);

        ksort($data);

        if (array_key_exists('GLOBALS', $data)) {
            unset($data['GLOBALS']);
        }

        if (array_key_exists('i', $data)) {
            unset($data['i']);
        }

        if (array_key_exists('LMRId', $data)) {
            unset($data['LMRId']);
        }

        if (array_key_exists('dotenv', $data)) {
            unset($data['dotenv']);
        }

        foreach ($data as $k => $v) {
            if ($k[0] == '_') {
                unset($data[$k]);
            }
        }
        self::fromGetDefinedVars($data, false);

        self::initProcessingCompany(self::$PCID);
        self::initAssignedProcessingCompany(self::$assignedPCID);
        self::initUser(self::$userNumber, self::$userGroup);
    }

    public static function setPCID(?int $PCID)
    {
        if ($PCID !== self::$PCID) {
            self::$_PC = null;
            self::$_PCModules = null;
            self::$_PCServices = null;
        }

        self::$PCID = $PCID;
    }

    private static ?tblProcessingCompany $_PC = null;

    public static function PC(): ?tblProcessingCompany
    {
        if (is_null(self::$_PC) && self::$PCID) {
            self::$_PC = tblProcessingCompany::Get([
                tblProcessingCompany_db::COLUMN_PCID         => self::$PCID,
                tblProcessingCompany_db::COLUMN_ACTIVESTATUS => 1,
                tblProcessingCompany_db::COLUMN_DSTATUS      => 0,
            ]);
        }

        return self::$_PC;
    }

    /* @var tblProcessingCompanyLMRClientType[] $_PCServices */
    private static ?array $_PCServices = null;

    /**
     * @return tblProcessingCompanyLMRClientType[]|null
     */
    public static function PCServices(): ?array
    {
        if (is_null(self::$_PCServices) && self::$PCID) {
            self::$_PCServices = tblProcessingCompanyLMRClientType::GetAll([
                tblProcessingCompanyLMRClientType_db::COLUMN_PCID => self::$PCID,
            ]);
        }
        return self::$_PCServices;
    }

    /* @var tblPCModules[] $_PCModules */
    private static ?array $_PCModules = null;

    /**
     * @return tblPCModules[]|null
     */
    public static function PCModules(): ?array
    {
        if (is_null(self::$_PCModules) && self::$PCID) {
            self::$_PCModules = tblPCModules::GetAll([
                tblPCModules_db::COLUMN_PCID             => self::$PCID,
                tblPCModules_db::COLUMN_SUBSCRIBEDSTATUS => 1,
                tblPCModules_db::COLUMN_ACTIVESTATUS     => 1,
            ]);
        }
        return self::$_PCModules;
    }

    private static ?User $_User = null;

    public static function setUser(
        string $userGroup,
        string $userRole,
        int    $userNumber,
        ?int   $publicUser
    )
    {
        self::$userGroup = $userGroup;
        self::$userRole = $userRole;
        self::$userNumber = $userNumber;
        self::$publicUser = $publicUser;

        self::$_User = null;
    }

    public static function User(): ?User
    {
        if (is_null(self::$_User)) {
            if (self::$userGroup && self::$userRole && self::$userNumber) {
                self::$PCID = self::$PCID ?: 0;

                self::$_User = User::Init(
                    self::$PCID,
                    self::$userGroup,
                    self::$userRole,
                    self::$userNumber,
                    self::$publicUser
                );
            }
        }
        return self::$_User;
    }

    public static function isSuper(): bool
    {
        return self::$userGroup == glUserGroup::USER_GROUP_SUPER;
    }
}
