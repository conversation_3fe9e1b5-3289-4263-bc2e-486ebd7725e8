<?php

namespace models\composite\oCustomDocs;

use models\composite\oBranch\getPromoCodeForBranch;
use models\composite\oBroker\getBrokerInfo;
use models\composite\oBroker\getWebFormsForBroker;
use models\composite\oChecklist\getDocStatusFileName;
use models\composite\oChecklist\getRequiredDocsAdditionalLogic;
use models\composite\oChecklist\requiredDocsForAdditionalLoanProgram;
use models\composite\oChecklist\requiredDocsForInternalLoanProgram;
use models\composite\oFile\getFileInfo;
use models\composite\oPC\getAppFormFields;
use models\composite\oPC\getPCInternalLoanGuidelines;
use models\composite\oPC\getPCMinMaxLoanGuidelines;
use models\composite\oServiceType\getServiceTypeList;
use models\composite\proposalFormula;
use models\constants\accrualTypes;
use models\constants\creditDecision;
use models\constants\creditMemoCategories;
use models\constants\docStatusArray;
use models\constants\gl\glCountryArray;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFUModulesNotesTypeArray;
use models\constants\gl\glGroundUpConstruction;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOLienPosition;
use models\constants\getEntityTypeLongName;
use models\constants\gl\glpaymentFrequency;
use models\constants\gl\glPlansAndPermitStatus;
use models\constants\gl\glYesNo;
use models\constants\GpropertyTypeNumbArray;
use models\constants\HMDAActionTaken;
use models\constants\loanProgram;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\Base\generateWebformLinks;
use models\Controllers\LMRequest\feesAndCost;
use models\Controllers\LMRequest\fileAdminInfo;
use models\Controllers\LMRequest\HUDCalculation;
use models\Controllers\LMRequest\HUDFundingClosingInfo;
use models\Controllers\LMRequest\Property;
use models\Controllers\Pops\exportClientFiles;
use models\HMLOLoanTermsCalculation;
use models\lendingwise\tblAdminUsers;
use models\lendingwise\tblAgent;
use models\lendingwise\tblBranch;
use models\lendingwise\tblFileHUDTransaction;
use models\lendingwise\tblLMRHUDItemsPayableLoan;
use models\lendingwise\tblProperties;
use models\cypher;
use models\myFileInfo;
use models\myFileInfo\budgetAndDrawsInfo;
use models\PageVariables;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Integers;
use models\standard\Strings;
use models\standard\UserAccess;
use models\types\strongType;
use NumberFormatter;
use models\Controllers\LMRequest\HUD as HUDController;
use models\Controllers\LMRequest\creditDecision as creditDecisionController;

/**
 *
 */
class getAllDynamicDatas extends strongType
{
    /**
     * @param array $ip
     * @return array
     */
    public static function getReport(array $ip = []): array
    {

        global $fieldsInfo, $fileCT, $fileMC;
        global $LMRId;
        global $proposalInfoArray, $insurance1, $HOAFees1;
        global $lien1BalanceDue, $lien2BalanceDue, $lien1Amount, $lien2Amount, $lien1Rate, $lien1Payment, $mortgageInsurance1, $floodInsurance;
        global $lien2Payment, $foodStampWelfare, $homeValue, $unemployment;
        global $isCoBorrower;
        global $lien2Rate;
        global $LMRID, $HMLOPropInfoArray, $HMLONewLoanInfoArray, $HMLOInfoArray, $shortSaleInfo, $LMRInfo, $incomeInfoArray;
        global $fileHMLONewLoanInfo, $taxImpoundsFee, $fileHMLOPropertyInfo,
               $exitStrategy,
               $haveInterestreserve, $closingCostFinanced,
               $filepaydownInfo, $lien1Terms, $taxes1, $interestChargedFromDate,
               $interestChargedEndDate, $originationPointsValue, $brokerPointsValue,
               $insImpoundsFee, $fv, $insImpoundsMonthAmt,
               $contingencyReserve, $guess, $CORTotalLoanAmt;
        global $totalMonthlyPayment, $paymentReservesAmt, $requiredConstructionAmt, $contingencyReserveAmt, $escrowFees,
               $attorneyFee, $prePaidInterestFee, $hideTr, $docUploadedDate, $ft,
               $totalCashOutAmt, $extensionOptionsAmt, $paydownamount, $tempTotalLoanAmount,
               $typeOfHMLOLoanRequesting, $loanTerm, $purchaseCloseDate, $accrualType, $totalDrawsFunded, $costSpent, $myFileInfo,
               $totalHouseHoldIncome, $totalHouseHoldExpenses, $lien1DTI, $tempLien1PaymentPITIA, $totalGrossMonthlyHouseHoldIncome,
               $payOffMortgage1, $payOffMortgage2, $payOffOtherOutstandingAmounts, $payOffOutstandingTaxes, $cashOutAmt, $totalEstPerDiem,
               $PerDeimTotalAmt, $travelNotaryFee, $constructionHoldbackFee,
               $valuationAVEFee, $valuationCMAFee;
        global $userTimeZone;

        $glHMLOLienPosition = glHMLOLienPosition::$glHMLOLienPosition;
        $glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;
        $glpaymentFrequency = glpaymentFrequency::$glpaymentFrequency;

        $LMRIDArray = [];
        $fileInfoArray = [];
        $LMRInfo = $paydownInfoArray = [];
        $BrokerInfo = [];
        $LoanOfficerInfo = [];
        $BranchInfo = [];
        $PCInfo = [];
        $shortSaleInfo = [];
        $incomeInfoArray = [];
        $propertyInfoArray = [];
        $clientServiceTypeInfo = [];
        $fileModuleInfoArray = [];
        $newArray = [];
        $LMRServiceType = '';
        $responseInfoArray = $BorrowerExpInfo = $PCStatusInfo = $PCSubStatusInfo = $fileSubstatusInfo = $fileLOAssetsInfo = [];
        $QAInfoArray = [];
        $LMRACHInfoArray = [];
        $LMRCCInfoArray = [];
        $PCCheckList = [];
        $missingDocInfoArray = [];
        $proposalInfo = [];
        $listingHistoryInfoArray = [];
        $userName = '';
        $fileCheckList = [];
        $bothPCFileChecklistArray = [];
        $appendComma = '';
        $propAddrInfo = '';
        $propertyAddress = '';
        $propertyCity = '';
        $ssnNumberArray = [];
        $propertyState = '';
        $propertyZip = '';
        $propertyCounty = '';
        $borrowerDOB = '';
        $ssnNumber = '';
        $replyName = '';
        $borrowerName = '';
        $coBorrowerName = '';
        $coBorrowerDOB = '';
        $coBssnNumberArray = [];
        $coBSsnNumber = '';
        $brokerNumber = 0;
        $executiveID = 0;
        $propertyTypeNumb = 0;
        $clientTypeInfoArray = [];
        $assignedEmpInfo = [];
        $lien1DTI = '';
        $lien1ProposalDTI = '';
        $lien1ProposalBalance = '';
        $lien2ProposalBalance = '';
        $lien1ProposalTerms = '';
        $lien2ProposalTerms = '';
        $lien1ProposalRate = '';
        $lien2ProposalRate = '';
        $lien1ProposalPrincipalReductionAmt = '';
        $lien2ProposalPrincipalReductionAmt = '';
        $backEndDTI = '';
        $proposalBackEndDTI = '';
        $lien1ProposalPaymentPITIA = '';
        $lien2ProposalPaymentPITIA = '';
        $isCoBorrower = '';
        $lien1Payment = '';
        $lien2Payment = '';
        $lien1BalanceDue = '';
        $lien2BalanceDue = '';
        $lien1Amount = '';
        $lien2Amount = '';
        $lien1Terms = '';
        $lien1Rate = '';
        $coExpenseForBackEndDTI = '';
        $homeValue = '';
        $lien2Rate = '';
        $lenderInfo1Array = [];
        $lenderInfo2Array = [];
        $insurancePolicies = '';
        $taxes1 = 0;
        $mortgageInsurance1 = '';
        $floodInsurance1 = '';
        $insurance1 = 0;
        $fileChecklistNotesInfo = [];
        $fileChecklistNotesInfoNew = [];
        $file2Info = [];
        $BOStaff = [];
        $assignedEmployeeArray = [];
        $foodStampWelfare = '';
        $unemployment = '';
        $clientEmail = '';
        $clientPwd = '';
        $borrowerMissingDoc = $branchMissingDoc = $brokerMissingDoc = $loanOfficerMissingDoc = $allMissingDocs = [];
        // // $myFileInfo = [];
        $lenderNOEArray = [];
        $LID1 = 0;
        $LID2 = 0;
        $RESTInfoArray = [];
        $tempBOStaff = [];
        $CLType = 'PCL';
        $LMRChecklistInfo = '';
        $LMRChecklistInfoPCL = $LMRChecklistInfoFCL = '';
        $cklistNotRequiredInfo = [];
        $cklistNotRequiredInfoName = [];
        $HMLONewLoanInfoArray = [];
        $LOExplanationArray = [];
        $HMLOInfoArray = [];
        $typeOpt = '';
        $fileHMLOListOfRepairsInfoArray = [];
        $listingRealtorInfo2 = $PCquickAppFieldsInfo = [];
        $budgetAndDrawsInfoArray = [];
        $AssetInfoArray = $assetsInfo = [];
        $fileHMLOBackGroundInfoArray = [];
        $fileHMLOExperienceInfo = [];
        $fileHMLOPropertyInfo = [];
        $escrowInfoArray = [];
        $googleDocs = 0;
        $dynamicTagValue = [];
        $investorInfoArray = [];
        $getSellerInfo = [];
        $getCreditMemo = [];
        $fileLoanOriginationInfoArray = [];
        $contractorInfoArray = [];
        $loggedInUserInfo = $ip['loggedInUserInfo'] ?? [];

        $allMergeTagsInContent = [];
        if (isset($ip['allMergeTagsInContent'])) {
            if (count($ip['allMergeTagsInContent']) > 0) {
                $allMergeTagsInContent = $ip['allMergeTagsInContent'];
            }
        } else if (isset($ip['contentMail'])) {
            if ($ip['contentMail'] != '') {
                preg_match_all('/\##(.*?)\##/', $ip['contentMail'], $mergeTagsIncludedInArray);
                $allMergeTagsInContent = ($mergeTagsIncludedInArray[0]);
            }
        }
        $printExecutionTime = 0;
        if (isset($ip['printExecutionTime'])) {
            if (($ip['printExecutionTime']) > 0) {
                $printExecutionTime = $ip['printExecutionTime'];
            }
        }
        if ($printExecutionTime == 1) {
            echo __LINE__ . ' Execution Started : ' . Dates::Timestamp() . "\n";
        }
        $LMRIDArray = array_unique(explode(',', $ip['LMRID']));

        if (array_key_exists('typeOpt', $ip)) $typeOpt = $ip['typeOpt'];
        /**
         * Google docs api call..
         */
        if (isset($ip['googleDocs'])) {
            $googleDocs = $ip['googleDocs'];
            $dynamicMergeTags = getAllDynamicFieldTags::getReport(['googleDocs' => 1]);
            $dynamicTagValue = array_flip($dynamicMergeTags);
        } else {
            $dynamicMergeTags = getAllDynamicFieldTags::getReport();
        }
        //  $userName   = $ip['userName'];
        if ($printExecutionTime == 1) {
            echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
        }
        $LMRClientTypeArray = getServiceTypeList::getReport([]);

        $fileTab = 'BO';

        $ip['empRole'] = 'Processor';
        $fileInfoArray = getMultipleFileInfo::getReport($ip) ?? [];
        if (count($fileInfoArray) > 0) {
            $LMRInfo = $fileInfoArray['LMRInfo'] ?? [];
            $BrokerInfo = $fileInfoArray['BrokerInfo'] ?? [];
            $LoanOfficerInfo = $fileInfoArray['LoanOfficerInfo'] ?? [];
            $BranchInfo = $fileInfoArray['BranchInfo'] ?? [];
            $PCInfo = $fileInfoArray['PCInfo'] ?? [];
            $responseInfoArray = $fileInfoArray['responseInfo'] ?? [];
            $incomeInfoArray = $fileInfoArray['incomeInfo'] ?? [];
            $propertyInfoArray = $fileInfoArray['propertyInfo'] ?? [];
            $clientServiceTypeInfo = $fileInfoArray['clientTypeInfo'] ?? [];
            $QAInfoArray = $fileInfoArray['QAInfo'] ?? [];
            $shortSaleInfo = $fileInfoArray['shortSaleInfo'] ?? [];
            $LMRACHInfoArray = $fileInfoArray['LMRACHInfo'] ?? [];
            $LMRCCInfoArray = $fileInfoArray['LMRCCInfo'] ?? [];
            $PCCheckList = $fileInfoArray['PCCheckList'] ?? [];
            $missingDocInfoArray = $fileInfoArray['missingDocInfo'] ?? [];
            $proposalInfo = $fileInfoArray['proposalInfo'] ?? [];
            $lenderInfo1Array = $fileInfoArray['LenderInfo1'] ?? [];
            $lenderInfo2Array = $fileInfoArray['LenderInfo2'] ?? [];
            $listingPageArray = $fileInfoArray['listingPageArray'] ?? [];
            $lenderNOEArray = $fileInfoArray['lenderNOE'] ?? [];
            $fileChecklistNotesInfo = $fileInfoArray['fileChecklistNotesInfo'] ?? [];
            $fileChecklistNotesInfoNew = $fileInfoArray['fileChecklistNotesInfoNew'] ?? [];
            $file2Info = $fileInfoArray['file2Info'] ?? [];
            $RESTInfoArray = $fileInfoArray['RESTInfo'] ?? [];
            $HMLOBusinessEntity = $fileInfoArray['HMLOBusinessEntity'] ?? [];
            $HMLOPropInfoArray = $fileInfoArray['HMLOPropInfoArray'] ?? [];
            $fileModuleInfoArray = $fileInfoArray['fileModuleInfo'] ?? [];
            $fileCheckList = $fileInfoArray['fileCheckList'] ?? [];
            $borrowerMissingDoc = $fileInfoArray['borrowerMissingDoc'] ?? [];
            $branchMissingDoc = $fileInfoArray['branchMissingDoc'] ?? [];
            $brokerMissingDoc = $fileInfoArray['brokerMissingDoc'] ?? [];
            $loanOfficerMissingDoc = $fileInfoArray['loanOfficerMissingDoc'] ?? [];
            $allMissingDocs = $fileInfoArray['allMissingDocs'] ?? [];
            $LMRChecklistInfo = $fileInfoArray['LMRChecklistInfo'] ?? [];
            $LMRChecklistInfoPCL = $fileInfoArray['LMRChecklistInfoPCL'] ?? [];
            $LMRChecklistInfoFCL = $fileInfoArray['LMRChecklistInfoFCL'] ?? [];
            $cklistNotRequiredInfo = $fileInfoArray['cklistNotRequiredInfo'] ?? [];
            $cklistNotRequiredInfoName = $fileInfoArray['cklistNotRequiredInfoName'] ?? [];
            $HMLONewLoanInfoArray = $fileInfoArray['HMLONewLoanInfo'] ?? [];
            $LOExplanationArray = $fileInfoArray['LOExplanation'] ?? [];
            $HMLOInfoArray = $fileInfoArray['HMLOInfo'] ?? [];
            $fileHMLOListOfRepairsInfoArray = $fileInfoArray['fileHMLOListOfRepairsInfo'] ?? [];
            $listingRealtorInfo2 = $fileInfoArray['listingRealtorInfo2'] ?? [];
            $budgetAndDrawsInfoArray = $fileInfoArray['budgetAndDrawsInfo'] ?? [];
            $AssetInfoArray = $fileInfoArray['assetInfo'] ?? [];
            $fileHMLOBackGroundInfoArray = $fileInfoArray['fileHMLOBackGroundInfo'] ?? [];
            $escrowInfoArray = $fileInfoArray['EscrowInfo'] ?? [];
            $HOA1InfoArray = $fileInfoArray['HOA1Info'] ?? [];
            $HOA2InfoArray = $fileInfoArray['HOA2Info'] ?? [];
            $BorrowerExpInfo = $fileInfoArray['BorrowerExpInfo'] ?? [];
            $PCStatusInfo = $fileInfoArray['PCStatusInfo'] ?? [];
            $PCSubStatusInfo = $fileInfoArray['PCSubStatusInfo'] ?? [];
            $fileSubstatusInfo = $fileInfoArray['fileSubstatusInfo'] ?? [];
            $fileLOAssetsInfo = $fileInfoArray['fileLOAssetsInfo'] ?? [];
            $getInsuranceDtls = $fileInfoArray['getInsuranceDtls'] ?? [];
            $paydownInfoArray = $fileInfoArray['paydownInfo'] ?? [];
            $additionalGuarantorsInfo = $fileInfoArray['additionalGuarantorsInfo'] ?? [];
            $alternateNamesInfo = $fileInfoArray['alternateNamesInfo'] ?? [];
            $investorInfoArray = $fileInfoArray['investorInfo'] ?? [];
            $getSellerInfo = $fileInfoArray['getSellerInfo'] ?? [];
            $LMRInternalLoanprogramsFiles = $fileInfoArray['LMRInternalLoanprogramsFiles'] ?? [];
            $LMRInternalLoanProgramNames = $fileInfoArray['LMRInternalLoanProgramNames'] ?? [];
            $LMRadditionalLoanprogramsFiles = $fileInfoArray['LMRadditionalLoanprogramsFiles'] ?? [];
            $sreoInfo = $fileInfoArray['sreoInfo'] ?? [];

            if (array_key_exists('listingHistoryInfo', $fileInfoArray)) {
                $listingHistoryInfoArray = $fileInfoArray['listingHistoryInfo'] ?? [];
            }
            $assignedEmpInfo = $fileInfoArray['assignedEmpInfo'] ?? [];


            if (array_key_exists('creditMemo', $fileInfoArray)) {
                $getCreditMemo = $fileInfoArray['creditMemo'] ?? [];
            }
            if (array_key_exists('fileLoanOriginationInfo', $fileInfoArray)) {
                $fileLoanOriginationInfoArray = $fileInfoArray['fileLoanOriginationInfo'] ?? [];
            }
            $getCreditMemoDetails = $fileInfoArray['getCreditMemoDetails'] ?? [];
            $memberOfficerInfoArray = $fileInfoArray['memberOfficerInfo'] ?? [];
            $loanSettingsInfoArray = $fileInfoArray['loanSettingsInfo'] ?? [];
            $noOfDaysInCurrentStatusInfoArray = $fileInfoArray['noOfDaysInCurrentStatusInfo'] ?? [];
            $equipmentInfoArray = $fileInfoArray['equipmentInfo'] ?? [];
            $HUDLenderToPayInfoArray = $fileInfoArray['HUDLenderToPayInfo'] ?? [];
            $HUDReservesDepositInfoArray = $fileInfoArray['HUDReservesDepositInfo'] ?? [];
            $creditDecisionInfoArray = $fileInfoArray['creditDecisionInfo'] ?? [];
            $HUDItemsPayableLoanInfoArray = $fileInfoArray['HUDItemsPayableLoanInfo'] ?? [];
            $extensionInfoArray = $fileInfoArray['extensionInfo'] ?? [];
            $refinanceMortgageInfoArray = $fileInfoArray['refinanceMortgageInfo'] ?? [];
            $fileExtensionOptionsInfoArray = $fileInfoArray['fileExtensionOptionsInfo'] ?? [];
            $contractorInfoArray = $fileInfoArray['contractorInfo'] ?? [];
            $fileAuditInfoArray = $fileInfoArray['fileAuditInfo'] ?? [];
        }

        if ($printExecutionTime == 1) {
            echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
        }
        $CONST_SITE_URL = CONST_SITE_URL;
        foreach ($LMRIDArray as $i => $LMRID) {
            $LMRId = $LMRID;

            $tblFileInfo = $fieldsInfo = $fileMC = [];
            $shortSale = [];
            $incomeInfo = [];
            $propertyInfo = [];
            $QAInfo = [];
            $LMRACHInfo = [];
            $clientServiceType = [];
            $LMRCCInfo = [];
            $billingAddrInfo = '';
            $proposalInfoArray = [];
            $PCID = 0;
            $listingHistoryInfo = [];
            $inArray = [];
            $LMRResponseId = 0;
            $clientLoginCredentials = $fileCT = '';
            $myFileInfo = [];
            $LMRClientTypeInfo = [];
            $fileHMLOPropertyInfo = $fileHMLOInfo = [];
            $LMRInternalLoanprogramsArray = [];
            $contractorInfo = [];

            $fileInfo = getFileInfo::getReport([
                'LMRId'    => $LMRId,
                'fetchTab' => 'ALL',
            ]);
            if (array_key_exists($LMRId, $fileInfo)) {
                $myFileInfo = $fileInfo[$LMRId];
            }
            if (array_key_exists('fileHMLOExperienceInfo', $myFileInfo)) $fileHMLOExperienceInfo = $myFileInfo['fileHMLOExperienceInfo'];
            if (array_key_exists('fileHMLOPropertyInfo', $myFileInfo)) $fileHMLOPropertyInfo = $myFileInfo['fileHMLOPropertyInfo'];
            if (array_key_exists('FilePropInfo', $myFileInfo)) $FilePropInfo = $myFileInfo['FilePropInfo'];
            if (array_key_exists('fileHMLOInfo', $myFileInfo)) $fileHMLOInfo = $myFileInfo['fileHMLOInfo'];
            if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
                if (array_key_exists($LMRId, $myFileInfo['LMRClientTypeInfo'])) $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
            }
            if (array_key_exists('LMRInternalLoanprograms', $myFileInfo)) $LMRInternalLoanprogramsArray = $myFileInfo['LMRInternalLoanprograms'];
            if (array_key_exists('LMRInternalLoanprograms', $myFileInfo)) $LMRInternalLoanprogramsArray = $myFileInfo['LMRInternalLoanprograms'];
            $fileModuleInfo = $myFileInfo['fileModuleInfo'][$LMRId];
            if (trim($fileModuleInfo[$i]['moduleCode']) == 'HMLO' || trim($fileModuleInfo[$i]['moduleCode']) == 'loc') {
                $isHMLO = 1;
            }
            $fileVelocityInfo = $myFileInfo['fileVelocityInfo'] ?? [];
            $fileVelocity = $fileVelocityInfo['noOfDays'] ?? 0;
            $inArray['daysInPrimaryStatus'] = $fileVelocity;

            $HUDFundingClosingInfoData = [];
            if (array_key_exists('HUDFundingClosingInfo', $myFileInfo)) $HUDFundingClosingInfoData = $myFileInfo['HUDFundingClosingInfo'];
            // echo "_____________________________________";
            //  pr($myFileInfo);
            $inArray['fileID'] = $LMRID;
            if (array_key_exists($LMRID, $LMRInfo)) {
                LMRequest::setLMRId($LMRID);
                $tblFileInfo = $LMRInfo[$LMRID];
//pr($tblFileInfo);die();
                if (count($tblFileInfo) > 0) {
                    $appendComma = '';
                    $propAddrInfo = '';
                    $ssnNumberArray = [];
                    $borrowerDOB = '';
                    $ssnNumber = '';
                    $replyName = '';
                    $borrowerName = '';
                    $coBorrowerName = '';
                    $coBorrowerDOB = '';
                    $coBssnNumberArray = [];
                    $coBSsnNumber = '';
                    $brokerNumber = 0;
                    $loanOfficerNumber = 0;
                    $executiveID = 0;
                    $propertyTypeNumb = 0;
                    $propertyType = '';
                    $isCoBorrower = 0;
                    $clientPwd = '';
                    $clientEmail = '';
                    $ssnLast4Number = '';
                    $coBSsnLast4Number = '';
                    $lien1Amount = '';
                    $lien2Amount = '';
                    $lien1Payment = '';
                    $taxes1 = 0;
                    $HOAFees1 = '';
                    $mortgageInsurance1 = '';
                    $lien2Payment = '';
                    $unemployment = '';
                    $floodInsurance1 = '';
                    $totalExpenseForBackEndDTI = '';
                    $primExpenseForBackEndDTI = '';
                    $coExpenseForBackEndDTI = '';
                    $insurance1 = 0;
                    $foodStampWelfare = '';
                    $homeValue = '';
                    $lien1Rate = '';
                    $lien2Rate = $maritalStatus = $borrowerCitizenship = '';
                    $lien1BalanceDue = '';
                    $lien2BalanceDue = '';
                    $salesDate = '';
                    $recordDate = '';
                    $receivedDate = '';
                    $lien1Terms = '';
                    $originalLender = '';
                    $mortgageOwner1 = '';
                    $noteDate = '';
                    $lien1ProposalTerms = '';
                    $lien1ProposalRate = '';
                    $dtArray = [];
                    $dtArray1 = [];
                    $PCID = 0;
                    $floodInsurance = '';
                    $creditLine = '';
                    $spouseName = '';
                    $driverLicenseState = '';
                    $driverLicenseNumber = '';
                    $coBorDriverLicenseState = '';
                    $coBorDriverLicenseNumber = '';
                    $propertyStateLong = '';


                    $brokerNumber = trim($tblFileInfo['brokerNumber']);
                    $loanOfficerNumber = trim($tblFileInfo['secondaryBrokerNumber']);
                    $executiveID = trim($tblFileInfo['FBRID']);
                    $PCID = trim($tblFileInfo['FPCID']);

                    $borrowerName = ucwords(trim($tblFileInfo['borrowerName'])) . ' ' . ucwords(trim($tblFileInfo['borrowerLName']));
                    $coBorrowerName = ucwords(trim($tblFileInfo['coBorrowerFName'])) . ' ' . ucwords(trim($tblFileInfo['coBorrowerLName']));
                    $replyName = $borrowerName . ' ' . $coBorrowerName;
                    $creditLine = Currency::formatDollarAmountWithDecimal(trim($tblFileInfo['internalInfoCreditLine']));
                    $clientEmail = trim($tblFileInfo['clientEmail']);
                    $clientPwd = trim($tblFileInfo['clientPwd']);

                    $borrowerDOB = trim($tblFileInfo['borrowerDOB']);
                    $coBorrowerDOB = trim($tblFileInfo['coBorrowerDOB']);
                    $ssnNumberArray = Strings::splitSSNNumber(trim($tblFileInfo['ssnNumber']));
                    $isCoBorrower = trim($tblFileInfo['isCoBorrower']);
                    $lien1BalanceDue = trim($tblFileInfo['lien1BalanceDue']);
                    $lien2BalanceDue = trim($tblFileInfo['lien2BalanceDue']);
                    $lien1ProposalTerms = trim($tblFileInfo['lien1Terms']);
                    $lien1ProposalRate = trim($tblFileInfo['lien1Rate']);
                    $lien1Amount = trim($tblFileInfo['lien1Amount']);
                    $lien2Amount = trim($tblFileInfo['lien2Amount']);
                    $lien1Payment = trim($tblFileInfo['lien1Payment']);
                    $lien2Payment = trim($tblFileInfo['lien2Payment']);
                    $homeValue = trim($tblFileInfo['homeValue']);
                    $lien1Rate = trim($tblFileInfo['lien1Rate']);
                    $lien2Rate = trim($tblFileInfo['lien2Rate']);
                    $salesDate = trim($tblFileInfo['salesDate']);
                    $originalLender = trim($tblFileInfo['originalLender1']);
                    $mortgageOwner1 = trim($tblFileInfo['mortgageOwner1']);
                    $recordDate = trim($tblFileInfo['recordDate']);
                    $receivedDate = trim($tblFileInfo['receivedDate']);
                    $maritalStatus = trim($tblFileInfo['maritalStatus']);
                    $driverLicenseState = trim($tblFileInfo['driverLicenseState']);
                    $driverLicenseNumber = trim($tblFileInfo['driverLicenseNumber']);
                    $coBorDriverLicenseState = trim($tblFileInfo['coBorDriverLicenseState']);
                    $coBorDriverLicenseNumber = trim($tblFileInfo['coBorDriverLicenseNumber']);
                    $spouseName = trim($tblFileInfo['spouseName']);

                    if (count($ssnNumberArray) > 0) {
                        $ssnNumber = trim($ssnNumberArray['No1']) . '-' . trim($ssnNumberArray['No2']) . '-' . trim($ssnNumberArray['No3']);
                        $ssnLast4Number = 'XXX' . '-' . 'XX' . '-' . trim($ssnNumberArray['No3']);
                    }
                    $coBssnNumberArray = Strings::splitSSNNumber(trim($tblFileInfo['coBSsnNumber']));
                    if (count($coBssnNumberArray) > 0) {
                        $coBSsnNumber = trim($coBssnNumberArray['No1']) . '-' . trim($coBssnNumberArray['No2']) . '-' . trim($coBssnNumberArray['No3']);
                        $coBSsnLast4Number = 'XXX' . '-' . 'XX' . '-' . trim($coBssnNumberArray['No3']);
                    }


                    if (Dates::IsEmpty($borrowerDOB)) {
                        $borrowerDOB = '';
                    } else {
                        $dtArray = explode('-', $borrowerDOB);
                        $borrowerDOB = date('m/d/Y', mktime(0, 0, 0, $dtArray[1], $dtArray[2], $dtArray[0]));
                    }
                    if (Dates::IsEmpty($coBorrowerDOB)) {
                        $coBorrowerDOB = '';
                    } else {
                        $dtArray1 = explode('-', $coBorrowerDOB);
                        $coBorrowerDOB = date('m/d/Y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }

                    //update links for the private label clients
                    $CONST_SITE_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
                        ? LMRequest::myFileInfo()->PCInfo()->vhost
                        : CONST_SITE_URL;
                    $CONST_SITE_URL = HTTP::formatUrl($CONST_SITE_URL);

                    /* Borrower */
                    $inArray['clientPwd'] = $clientPwd;
                    $inArray['PCID'] = $PCID;
                    $inArray['LMRID'] = $LMRID;
                    $inArray['clientName'] = $replyName;
                    $inArray['creditLine'] = $creditLine;
                    $inArray['borrowerFName'] = ucwords(trim($tblFileInfo['borrowerName']));
                    $inArray['borrowerLName'] = ucwords(trim($tblFileInfo['borrowerLName']));
                    $inArray['borrowerMName'] = ucwords(trim($tblFileInfo['borrowerMName']));
                    $inArray['borrowerName'] = $borrowerName;
                    $inArray['borrowerDOB'] = $borrowerDOB;
                    $inArray['formatSsnNumber'] = $ssnNumber;
                    $inArray['FormatssnLast4Number'] = $ssnLast4Number;
                    $inArray['maritalStatus'] = $maritalStatus;
                    $inArray['borrowerCitizenship'] = $borrowerCitizenship;
                    $inArray['borrowerNotes'] = urldecode(stripslashes(str_replace('$', '&#36;', $tblFileInfo['mortgageNotes'])));

                    $inArray['borrowerNotesFormatted'] = preg_replace("/\r\n|\r|\n/", '<br>', $tblFileInfo['mortgageNotes']);
                    $inArray['borrowerNotesFormatted'] = stripslashes(str_replace('$', '&#36;', $inArray['borrowerNotesFormatted']));
                    $inArray['propertyStateLong'] = $propertyStateLong;

                    $inArray['borPhone'] = Strings::formatPhoneNumber(trim($tblFileInfo['phoneNumber']));
                    $inArray['borCell'] = Strings::formatPhoneNumber(trim($tblFileInfo['cellNumber']));
                    $inArray['borFax'] = Strings::formatPhoneNumber(trim($tblFileInfo['fax']));
                    $inArray['boAltPhoneNumber'] = Strings::formatPhoneNumber(trim($tblFileInfo['altPhoneNumber']));
                    $inArray['workNumber'] = Strings::formatPhoneNumber(trim($tblFileInfo['workNumber']));
                    $inArray['borrowerEmail'] = trim($tblFileInfo['borrowerEmail']);
                    $inArray['borrowerSecondaryEmail'] = trim($tblFileInfo['borrowerSecondaryEmail']);
                    $inArray['bormailingAddress'] = trim($tblFileInfo['mailingAddress']);
                    $inArray['mailingCity'] = trim($tblFileInfo['mailingCity']);
                    $inArray['mailingState'] = trim($tblFileInfo['mailingState']);
                    $inArray['mailingZip'] = trim($tblFileInfo['mailingZip']);
                    $inArray['mailingUnit'] = trim($tblFileInfo['mailingUnit']);
                    $inArray['mailingCountry'] = glCountryArray::convertCountry(trim($tblFileInfo['mailingCountry']));

                    $inArray['clientMaxAcqusitionLTV'] = trim($tblFileInfo['clientMaxAcqusitionLTV']);
                    $inArray['clientMaxRehabBudget'] = trim($tblFileInfo['clientMaxRehabBudget']);
                    $inArray['clientMaxAllowedARV'] = trim($tblFileInfo['clientMaxAllowedARV']);
                    $inArray['driverLicenseState'] = $driverLicenseState;
                    $inArray['driverLicenseNumber'] = $driverLicenseNumber;
                    $inArray['spouseName'] = $spouseName;
                    /* Borrower */

                    /* Co-Borrower */
                    $inArray['todaysdate'] = date('m/d/Y');
                    $inArray['twoweeks'] = date('m/d/Y', mktime(0, 0, 0, date('m'), date('d') + 14, date('Y')));
                    $inArray['coBorrowerFName'] = ucwords(trim($tblFileInfo['coBorrowerFName']));

                    $inArray['coBorrowerLName'] = ucwords(trim($tblFileInfo['coBorrowerLName']));
                    $inArray['coBorrowerName'] = $coBorrowerName;
                    $inArray['coBorrowerEmail'] = trim($tblFileInfo['coBorrowerEmail']);
                    $inArray['coBorrowerDOB'] = $coBorrowerDOB;
                    $inArray['formatCoBSsnNumber'] = $coBSsnNumber;
                    $inArray['formatCoBSsnLast4Number'] = $coBSsnLast4Number;
                    $inArray['coBorDriverLicenseState'] = $coBorDriverLicenseState;
                    $inArray['coBorDriverLicenseNumber'] = $coBorDriverLicenseNumber;
                    $inArray['coBorMailingStateLong'] = trim($tblFileInfo['coBorMailingStateLong']);

                    /* General New  Tags  04-02-2019*/
                    if (Dates::IsEmpty($recordDate)) {
                        $recordDate = '';
                    } else {
                        $dtArray1 = explode('-', $recordDate);
                        $recordDate = date('m/d/Y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }
                    $inArray['createdDate'] = $recordDate;


                    if (Dates::IsEmpty($receivedDate)) {
                        $receivedDate = '';
                    } else {
                        $dtArray1 = explode('-', $receivedDate);
                        $receivedDate = date('m/d/Y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }
                    $inArray['receivedDate'] = $receivedDate;
                    /* */


                    $inArray['coBCellNumber'] = Strings::formatPhoneNumber(trim($tblFileInfo['coBCellNumber']));
                    $inArray['coBorrowerWorkNumber'] = Strings::formatPhoneNumber(trim($tblFileInfo['coBorrowerWorkNumber']));
                    $inArray['coBPhoneNumber'] = Strings::formatPhoneNumber(trim($tblFileInfo['coBPhoneNumber']));
                    $inArray['coBAltPhoneNumber'] = Strings::formatPhoneNumber(trim($tblFileInfo['coBAltPhoneNumber']));
                    $inArray['coBFax'] = Strings::formatPhoneNumber(trim($tblFileInfo['coBFax']));
                    $inArray['coBorrowerMailingCity'] = trim($tblFileInfo['coBorrowerMailingCity']);
                    $inArray['coBorrowerMailingState'] = trim($tblFileInfo['coBorrowerMailingState']);
                    $inArray['coBorrowerMailingZip'] = trim($tblFileInfo['coBorrowerMailingZip']);
                    $inArray['coBorrowerMailingAddress'] = trim($tblFileInfo['coBorrowerMailingAddress']);
                    /* Co-Borrower */

                    /* Mortgage Info */
                    $inArray['lenderName1'] = trim($tblFileInfo['servicer1']);
                    $inArray['lenderName2'] = trim($tblFileInfo['servicer2']);
                    $inArray['loanNumber1'] = trim($tblFileInfo['loanNumber']);
                    $inArray['loanNumber2'] = trim($tblFileInfo['loanNumber2']);
                    $inArray['currentBalance1'] = trim($tblFileInfo['lien1Amount']);
                    $inArray['currentBalance2'] = trim($tblFileInfo['lien2Amount']);
                    $inArray['lien1BalanceDue'] = trim($tblFileInfo['lien1BalanceDue']);
                    $inArray['lien2BalanceDue'] = trim($tblFileInfo['lien2BalanceDue']);
                    $inArray['noOfMonthsBehind1'] = trim($tblFileInfo['noOfMonthsBehind1']);
                    $inArray['noOfMonthsBehind2'] = trim($tblFileInfo['noOfMonthsBehind2']);
                    $inArray['lien1Rate'] = trim($tblFileInfo['lien1Rate']);
                    $inArray['lien2Rate'] = trim($tblFileInfo['lien2Rate']);
                    $inArray['PIPayment'] = Currency::formatDollarAmountWithDecimal($tblFileInfo['lien1Payment']);
                    $inArray['payment'] = trim($tblFileInfo['lien2Payment']);
                    $inArray['lien1Terms'] = trim($tblFileInfo['lien1Terms']);
                    $lien1Terms = trim($tblFileInfo['lien1Terms']);
                    $inArray['lien2Terms'] = trim($tblFileInfo['lien2Terms']);
                    $inArray['mortgageOwner1'] = trim($tblFileInfo['mortgageOwner1']);
                    $inArray['mortgageOwner2'] = trim($tblFileInfo['mortgageOwner2']);
                    $inArray['originalLender'] = trim($tblFileInfo['originalLender1']);
                    $inArray['lien1RateText'] = Strings::convertNumberToWord(trim($tblFileInfo['lien1Rate'])) . ' percent';

                    $inArray['lien1OriginalBalance'] = Currency::formatDollarAmountWithDecimal($tblFileInfo['lien1OriginalBalance']);
                    $inArray['borrowerPreviousAddress'] = $tblFileInfo['previousAddress'];
                    $inArray['borrowerPreviousCity'] = $tblFileInfo['previousCity'];
                    $inArray['borrowerPreviousState'] = $tblFileInfo['previousState'];
                    $inArray['borrowerPreviousZip'] = $tblFileInfo['previousZip'];


                    /* Mortgage Info */
                    $inArray['homeValue'] = Currency::formatDollarAmountWithDecimal($tblFileInfo['homeValue']);
                    $inArray['occupancy'] = trim($tblFileInfo['occupancy']);

                    if (Dates::IsEmpty($salesDate)) {
                        $salesDate = '';
                    } else {
                        $dtArray1 = explode('-', $salesDate);
                        $salesDate = date('m/d/Y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }
                    $inArray['salesDate'] = $salesDate;


                    $lien1LPMade = '';
                    $lien2LPMade = '';
                    $lien1LPMade = trim($tblFileInfo['lien1LPMade']);
                    $lien2LPMade = trim($tblFileInfo['lien2LPMade']);
                    if (Dates::IsEmpty($lien1LPMade)) {
                        $lien1LPMade = '';
                    } else {
                        $dtArray1 = [];
                        $dtArray1 = explode('-', $lien1LPMade);
                        $lien1LPMade = date('m/d/Y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }
                    if (Dates::IsEmpty($lien2LPMade)) {
                        $lien2LPMade = '';
                    } else {
                        $dtArray1 = [];
                        $dtArray1 = explode('-', $lien2LPMade);
                        $lien2LPMade = date('m/d/Y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }
                    $inArray['lien1LPMade'] = trim($lien1LPMade);
                    $inArray['lien2LPMade'] = trim($lien2LPMade);
                }
            }


            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }

            if (array_key_exists($LMRID, $fileModuleInfoArray)) {
                $fileTypeArray = [];
                $fileTypeArray = $fileModuleInfoArray[$LMRID];
                /* Get Branch Modules Keys */
                $aCm = '';
                for ($ct = 0; $ct < count($fileTypeArray); $ct++) {
                    $fileCT .= $aCm . trim($fileTypeArray[$ct]['moduleCode']);
                    $aCm = ',';
                }
                /* Get File Modules Keys */
                $aCm = '';
                for ($ct = 0; $ct < count($fileTypeArray); $ct++) {
                    $fileMC[] = trim($fileTypeArray[$ct]['moduleCode']);
                }
                $fieldsInfo = getAppFormFields::getReport(['assignedPCID' => $PCID, 'fTArray' => $fileTypeArray, 'myOpt' => 'BO']);
            }
            $fileLoanOriginationInfo = [];
            if (array_key_exists($LMRID, $fileLoanOriginationInfoArray)) {
                $fileLoanOriginationInfo = $fileLoanOriginationInfoArray[$LMRID];
                if (count($fileLoanOriginationInfo) > 0) {
                    $inArray['borrowerResideTwoYears'] = $fileLoanOriginationInfo['borResidedPresentAddr'];
                }
            }

            if (array_key_exists($LMRID, $fileAuditInfoArray ?? [])) {
                $fileAuditInfo = $fileAuditInfoArray[$LMRID];
                if (count($fileAuditInfo)) {
                    $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                    $ipArray['outputZone'] = $userTimeZone;
                    $ipArray['inputTime'] = $fileAuditInfo['recordDate'];
                    $fileCreateDateTimeStamp = Dates::timeZoneConversion($ipArray);
                    $fileCreateDateTimeStamp = Dates::formatDateWithRE($fileCreateDateTimeStamp, 'YMD_HMS', 'M j, Y h:i A');
                    $fileCreateDateTimeStamp .= ' - ' . $userTimeZone;

                    $inArray['createdDateTimestamp'] = $fileCreateDateTimeStamp;
                }
            }

            /* File2 Info */
            $file2InfoArray = [];
            if (array_key_exists($LMRID, $file2Info)) {
                $file2InfoArray = $file2Info[$LMRID];
                if (count($file2InfoArray) > 0) {
                    $transferOfServicingDate = '';
                    $presentAddress = $presentCity = $presentState = $presentZip = $presentPropLengthTime = $presentPropLengthTimeCoBor = '';
                    $transferOfServicingDate = trim($file2InfoArray['transferOfServicingDate']);
                    $presentAddress = trim($file2InfoArray['presentAddress']);
                    $presentCity = trim($file2InfoArray['presentCity']);
                    $presentState = trim($file2InfoArray['presentState']);
                    $presentZip = trim($file2InfoArray['presentZip']);
                    $presentCounty = trim($file2InfoArray['presentCounty']);
                    $presentPropLengthTime = trim($file2InfoArray['presentPropLengthTime']);
                    $presentPropLengthTimeCoBor = trim($file2InfoArray['presentPropLengthTimeCoBor']);
//pr($file2InfoArray);
                    $coBPresentAddress = trim($file2InfoArray['coBPresentAddress']);
                    $coBPresentCity = trim($file2InfoArray['coBPresentCity']);
                    $coBPresentState = trim($file2InfoArray['coBPresentState']);
                    $coBPresentZip = trim($file2InfoArray['coBPresentZip']);
                    $borrowerStateLong = trim($file2InfoArray['borrowerStateLong']);
                    $coBorrStateLong = trim($file2InfoArray['coBorrStateLong']);

                    if (Dates::IsEmpty($transferOfServicingDate)) {
                        $transferOfServicingDate = '';
                    } else {
                        $transferOfServicingDate = Dates::formatDateWithRE($transferOfServicingDate, 'YMD', 'm/d/Y');
                    }
                    $inArray['transferOfServicingDate'] = $transferOfServicingDate;
                    $inArray['presentAddress'] = $presentAddress;
                    $inArray['presentCity'] = $presentCity;
                    $inArray['presentState'] = $presentState;
                    $inArray['presentZip'] = $presentZip;
                    $inArray['presentCounty'] = $presentCounty;
                    $inArray['presentPropLengthTime'] = $presentPropLengthTime;
                    $inArray['coBPresentAddress'] = $coBPresentAddress;
                    $inArray['coBPresentCity'] = $coBPresentCity;
                    $inArray['coBPresentState'] = $coBPresentState;
                    $inArray['coBPresentZip'] = $coBPresentZip;
                    $inArray['presentPropLengthTimeCoBor'] = $presentPropLengthTimeCoBor;
                    $inArray['borrowerStateLong'] = $borrowerStateLong;
                    $inArray['coBorrStateLong'] = $coBorrStateLong;
                    $inArray['guarantorNotes'] = $file2InfoArray['guarantorNotes'];
                    $inArray['borrowerUnit'] = $file2InfoArray['presentUnit'];
                    $inArray['borrowerCountry'] = glCountryArray::convertCountry($file2InfoArray['presentCountry']);
                    $inArray['presentPropLengthMonths'] = $file2InfoArray['presentPropLengthMonths'];


                    $inArray['borrowerPreviousUnit'] = $file2InfoArray['previousUnit'];
                    $inArray['borrowerPreviousCountry'] = glCountryArray::convertCountry($file2InfoArray['previousCountry']);
                    $inArray['previousPropLength'] = $file2InfoArray['previousPropLengthTime'];
                    $inArray['previousPropLengthMonths'] = $file2InfoArray['previousPropLengthMonths'];
                    $inArray['previousRentOrOwn'] = $file2InfoArray['borFormerPropType'];
                    $inArray['previousRent'] = Currency::formatDollarAmountWithDecimal($file2InfoArray['previousRPM']);
                    $inArray['borrowerRentOrOwn'] = $file2InfoArray['borPresentPropType'];
                    $inArray['borrowerPresentRent'] = Currency::formatDollarAmountWithDecimal($file2InfoArray['currentRPM']);
                    $inArray['mailingAddrAsPresent'] = $file2InfoArray['mailingAddrAsPresent'] = 0 ? 'No' : 'Yes';
                    $inArray['coBorrowerMName'] = ucwords(trim($file2InfoArray['coBorrowerMName']));
                }
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /* Asset Info */
            $assetInfoArrayNew = [];
            if (count($AssetInfoArray) > 0) {
                if (array_key_exists($LMRID, $AssetInfoArray)) {
                    $assetInfoArrayNew = $assetsInfo = $AssetInfoArray[$LMRID];

                    if (count($assetInfoArrayNew) > 0) {

                        $inArray['assetTotalCashBankAcc'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetTotalCashBankAcc']));
                        $inArray['assetTotalRetirementValue'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetTotalRetirementValue']));
                        $inArray['assetHome'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetHome']));
                        $inArray['assetHomeOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetHomeOwed']));
                        $inArray['assetSR'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetSR']));
                        $inArray['assetSROwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetSROwed']));
                        $inArray['assetORE'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetORE']));
                        $inArray['assetOREOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetOREOwed']));
                        $inArray['assetCheckingAccounts'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetCheckingAccounts']));

                        $inArray['assetTotalEstValue'] += floatval(trim($assetInfoArrayNew['assetTotalCashBankAcc']));
                        $inArray['assetTotalEstValue'] += floatval(trim($assetInfoArrayNew['assetTotalRetirementValue']));
                        $inArray['assetTotalEstValue'] += floatval(trim($assetInfoArrayNew['assetHome']));
                        $inArray['assetTotalEstValue'] += floatval(trim($assetInfoArrayNew['assetSR']));
                        $inArray['assetTotalEstValue'] += floatval(trim($assetInfoArrayNew['assetORE']));

                        $inArray['assetTotalEstValue'] = '$ ' . Integers::formatInputToInt(trim($inArray['assetTotalEstValue']));

                        $inArray['assetTotalOwedValue'] += floatval(trim($assetInfoArrayNew['assetHomeOwed']));
                        $inArray['assetTotalOwedValue'] += floatval(trim($assetInfoArrayNew['assetSROwed']));
                        $inArray['assetTotalOwedValue'] += floatval(trim($assetInfoArrayNew['assetOREOwed']));
                        $inArray['assetTotalOwedValue'] = '$ ' . Integers::formatInputToInt(trim($inArray['assetTotalOwedValue']));
                        $inArray['otherDescription'] = $assetInfoArrayNew['otherDesc'];
                        $inArray['assetLifeInsurance'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetLifeInsurance']));
                        $inArray['assetLifeInsuranceOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetLifeInsuranceOwed']));

                        $inArray['assetSavingMoneyMarket'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetSavingMoneyMarket']));
                        $inArray['networthOfBusinessOwned'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['networthOfBusinessOwned']));
                        $inArray['assetAccount'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetAccount']));
                        $inArray['assetAccountOwd'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetAccountOwd']));
                        $inArray['assetStocks'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetStocks']));
                        $inArray['assetStocksOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetStocksOwed']));
                        $inArray['assetNonMarketableSecurities'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetNonMarketableSecurities']));
                        $inArray['assetNonMarketableSecuritiesOwd'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetNonMarketableSecuritiesOwd']));
                        $inArray['assetIRAAccounts'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetIRAAccounts']));
                        $inArray['assetIRAAccountsOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetIRAAccountsOwed']));
                        $inArray['assetESPOAccounts'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetESPOAccounts']));
                        $inArray['assetESPOAccountsOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetESPOAccountsOwed']));
                        $inArray['assetCars'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetCars']));
                        $inArray['assetCarsOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetCarsOwed']));
                        $inArray['assetOther'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['assetOther']));
                        $inArray['otherLiabilitiesOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['otherLiabilitiesOwed']));
                        $inArray['otherAssets'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['otherAssets']));
                        $inArray['notesPayableToBanksOthersOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['notesPayableToBanksOthersOwed']));
                        $inArray['installmentAccountOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['installmentAccountOwed']));
                        $inArray['revolvingDebtOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['revolvingDebtOwed']));
                        $inArray['unpaidPayableTaxesOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['unpaidPayableTaxesOwed']));
                        $inArray['otherLiabilitiesOwed'] = '$ ' . Integers::formatInputToInt(trim($assetInfoArrayNew['otherLiabilitiesOwed']));
                        $inArray['otherLiabilityDetails'] = trim($assetInfoArrayNew['otherLiabilityDetails']);
                        $inArray['unpaidPayableTaxesDesc'] = trim($assetInfoArrayNew['unpaidPayableTaxesDesc']);
                    }
                }
            }
            /* close of Asset Info */

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            $creditMemoDetails = [];
            $creditMemoValueArr = [];
            $creditMemoValue = '';
            $creditMemoCategories = creditMemoCategories::$creditMemoCategories;

            if (count($getCreditMemoDetails ?? []) > 0) {
                if (array_key_exists($LMRID, $getCreditMemoDetails)) {
                    $creditMemoDetails = $getCreditMemoDetails[$LMRID];
                }
                $creditMemoValue = '<table border=\"0\" width=\"100%\">
					<tr>
						<td><b>Category</b></td>
						<td><b>Description</b></td>
					</tr>';
            }
            if ($googleDocs == 1) {
                $creditMemoValueArr[] = [
                    ['fieldColor' => '', 'fieldText' => 'Category'],
                    ['fieldColor' => '', 'fieldText' => 'Description'],
                ];
            }
            foreach ($creditMemoDetails as $k => $v) {
                $memoCategory = $v['memoCategory'] ?? '';
                $memoCategoryvalue = array_key_exists($memoCategory, $creditMemoCategories) ? $creditMemoCategories[$memoCategory] : '';
                $memoDescription = $v['memoDescription'] ?? '';
                $creditMemoValue .= "<tr><td>$memoCategoryvalue</td><td>$memoDescription</td></tr>";
                if ($googleDocs == 1) {
                    $creditMemoValueArr[] = [
                        ['fieldColor' => '', 'fieldText' => "$memoCategoryvalue"],
                        ['fieldColor' => '', 'fieldText' => "$memoDescription"],
                    ];
                }
            }
            $creditMemoValue .= "</table>";
            if ($googleDocs == 1) {
                $inArray['tables'][] = [
                    'mergeTableTag'  => '##Credit Memo##',
                    'mergeTableData' => $creditMemoValueArr,
                ];
            } else {
                $inArray['creditMemoDetails'] = $creditMemoValue;
            }


            /******************************* Properties *************************/

            $allPropertiesAddress = '';
            $blanketLoanPropertiesAddress = '';
            $googlePropertiesUrlLinks = [];
            $googlePropertyHeader = [];
            $googlePrimaryProperty = [];
            $googleALLProperties = [];
            $blanketLoanPropertyTableRows = '';
            $blanketLoanPropertyTableRowsAll = '';

            $primaryPropertyInfo = Property::$primaryPropertyInfo;

            $propertyAddress = $primaryPropertyInfo ? $primaryPropertyInfo->propertyAddress : null;
            $propertyCity = $primaryPropertyInfo ? $primaryPropertyInfo->propertyCity : null;
            $propertyState = $primaryPropertyInfo ? $primaryPropertyInfo->propertyState : null;
            $propertyStateLong = $primaryPropertyInfo ? Strings::convertState($primaryPropertyInfo->propertyState) : null;
            $propertyZip = $primaryPropertyInfo ? $primaryPropertyInfo->propertyZipCode : null;
            $propertyCounty = $primaryPropertyInfo ? $primaryPropertyInfo->propertyCounty : null;

            $inArray['legalDescription'] = $primaryPropertyInfo ? urldecode($primaryPropertyInfo->propertyLegalDescription) : null;
            $inArray['parcelNo'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertyParcelNumber : null;
            $inArray['block'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertyBlock : null;
            $inArray['lot'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertyLot : null;
            $inArray['district'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertyDistrict : null;
            $inArray['section'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertySection : null;
            $inArray['propertyUnit'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertyUnit : null;
            $inArray['propertyCountry'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertyCountry : null;
            $inArray['propertyLocation'] = $primaryPropertyInfo ? $primaryPropertyInfo->propertyLocation : null;


            $propertyDetails = $primaryPropertyInfo ? $primaryPropertyInfo->getTblPropertiesDetails_by_propertyId() : null;

            $propertyestimatedValue = $propertyDetails ? $propertyDetails->propertyEstimatedValue : null;
            $subjectPropURLLink1 = $propertyDetails ? $propertyDetails->propertyURLLink1 : null;
            $subjectPropURLLink2 = $propertyDetails ? $propertyDetails->propertyURLLink2 : null;
            $inArray['presentOccupancy'] = $propertyDetails ? $propertyDetails->propertyPresentOccupancy : null;
            $inArray['propertyCondition'] = $propertyDetails ? $propertyDetails->propertyCondition : null;
            $inArray['conditionNotes'] = $propertyDetails ? $propertyDetails->propertyConditionNotes : null;
            $inArray['occupancyNotes'] = $propertyDetails ? $propertyDetails->propertyOccupancyNotes : null;
            $inArray['taxYear'] = $propertyDetails ? $propertyDetails->propertyTaxYear : null;
            $inArray['propertyURLLink1'] = $propertyDetails ? trim($propertyDetails->propertyURLLink1) : null;
            $inArray['propertyURLLink2'] = $propertyDetails ? trim($propertyDetails->propertyURLLink2) : null;
            $inArray['zillowValue'] = $propertyDetails ? trim($propertyDetails->propertyZillowValue) : null;
            $inArray['pricePerDoor'] = $propertyDetails ? Currency::formatDollarAmountWithDecimal($propertyDetails->propertyPricePerDoor) : null;
            $inArray['yearRenovated'] = $propertyDetails ? $propertyDetails->propertyRenovatedYear : null;
            $inArray['pastDuePropertyTaxes'] = $propertyDetails ? Currency::formatDollarAmountWithDecimal($propertyDetails->propertyPastDuePropertyTaxes) : null;

            $propertyCharacteristics = $primaryPropertyInfo ? $primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId() : null;

            $propertyTypeNumb = $propertyCharacteristics ? $propertyCharacteristics->propertyType : null;
            $propertyFeatures = $propertyCharacteristics ? $propertyCharacteristics->propertyFeatures : null;
            $inArray['howManyBedRoom'] = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfBedRooms : null;
            $inArray['howManyBathRoom'] = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfBathRooms : null;
            $inArray['propConstructionType'] = $propertyCharacteristics ? $propertyCharacteristics->propertyConstructionType : null;
            $inArray['yearBuilt'] = $propertyCharacteristics ? $propertyCharacteristics->propertyYearBuilt : null;
            $inArray['acres'] = $propertyCharacteristics ? $propertyCharacteristics->propertyAcres : null;
            $inArray['propertySqFt'] = $propertyCharacteristics ? $propertyCharacteristics->propertySqFt : null;
            $inArray['howManyHalfBathRoom'] = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfHalfBathRooms : null;
            $inArray['basementHome'] = $propertyCharacteristics ? Strings::booleanTextVal($propertyCharacteristics->propertyIsHomeHaveBasement) : null;
            $inArray['garageHome'] = $propertyCharacteristics ? Strings::booleanTextVal($propertyCharacteristics->propertyIsHomeHaveGarage) : null;
            $inArray['addRentableSqFt'] = $propertyCharacteristics ? $propertyCharacteristics->propertyRentableSqFt : null;
            $inArray['noOfBuildings'] = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfBuildings : null;
            $inArray['noOfUnits'] = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfUnits : null;
            $inArray['propertyValue'] = $propertyCharacteristics ? trim(Currency::formatDollarAmountWithDecimal($propertyCharacteristics->propertyValue)) : null;
            $inArray['noOfParcels'] = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfParcels : null;
            $inArray['ownerOccupancyPercentage'] = $propertyCharacteristics ? $propertyCharacteristics->propertyOwnerOccupancy : null;
            $inArray['adjustedSqFt'] = $propertyCharacteristics ? $propertyCharacteristics->propertyAdjustedSqFt : null;
            $inArray['propertyNumberOfStories'] = $propertyCharacteristics ? $propertyCharacteristics->propertyNumberOfStories : null;
            $inArray['currentADU'] = $propertyCharacteristics ? $propertyCharacteristics->currentADU : null;
            $inArray['futureADU'] = $propertyCharacteristics ? $propertyCharacteristics->futureADU : null;

            //Property Characteristics
            $inArray['futurePropertyType'] = $propertyCharacteristics && Property::$propertyFuturePropertyType[$propertyCharacteristics->futurePropertyType]
                ? Property::$propertyFuturePropertyType[$propertyCharacteristics->futurePropertyType]
                : null;
            $inArray['futureNoofUnits'] = $propertyCharacteristics ? $propertyCharacteristics->futureNoOfUnits : null;

            $inArray['propertyFutureNumberOfBedRooms'] = $propertyCharacteristics ? $propertyCharacteristics->propertyFutureNumberOfBedRooms : null;
            $inArray['propertyFutureNumberOfBathRooms'] = $propertyCharacteristics ? $propertyCharacteristics->propertyFutureNumberOfBathRooms : null;

            $propertyAccess = $primaryPropertyInfo ? $primaryPropertyInfo->getTblPropertiesAccess_by_propertyId() : null;

            $inArray['LBContactName'] = $propertyAccess ? $propertyAccess->propertyAccessName : null;
            $inArray['LBContactPhone'] = $propertyAccess ? trim(Strings::formatPhoneNumber($propertyAccess->propertyAccessPhone)) : null;
            $inArray['LBContactEmail'] = $propertyAccess ? trim($propertyAccess->propertyAccessEmail) : null;
            $inArray['LBInfo'] = $propertyAccess ? trim($propertyAccess->propertyAccessLockBoxInfo) : null;
            $inArray['propertyAccessRelationship'] = $propertyAccess ? Property::$propertyAccessRelationshipList[$propertyAccess->propertyAccessRelationship] : null;

            //Supplemental Product Fields
            $propertyAppraisalDetails = $primaryPropertyInfo && $primaryPropertyInfo->getTblPropertiesAppraiserDetails_by_propertyId() ? $primaryPropertyInfo->getTblPropertiesAppraiserDetails_by_propertyId()[0] : null;
            $inArray['primaryAppraisalECOADeliveryDate'] = $propertyAppraisalDetails ? Dates::formatDateWithRE($propertyAppraisalDetails->primaryAppraisalEcoaDeliveryDate, 'YMD', 'm/d/Y') : null;
            $inArray['supplementalProductFormType1'] = $propertyAppraisalDetails ? $propertyAppraisalDetails->propertyAppraisalSupplementalProductFormType1 : null;
            $inArray['supplementalProductFormType2'] = $propertyAppraisalDetails ? $propertyAppraisalDetails->propertyAppraisalSupplementalProductFormType2 : null;
            $inArray['supplementalProductFormType3'] = $propertyAppraisalDetails ? $propertyAppraisalDetails->propertyAppraisalSupplementalProductFormType3 : null;
            $inArray['supplementalProductEffectiveDate1'] = $propertyAppraisalDetails ? Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEffectiveDate1, 'YMD', 'm/d/Y') : null;
            $inArray['supplementalProductEffectiveDate2'] = $propertyAppraisalDetails ? Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEffectiveDate2, 'YMD', 'm/d/Y') : null;
            $inArray['supplementalProductEffectiveDate3'] = $propertyAppraisalDetails ? Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEffectiveDate3, 'YMD', 'm/d/Y') : null;
            $inArray['supplementalProductEcoaDeliveryDate1'] = $propertyAppraisalDetails ? Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEcoaADeliveryDate1, 'YMD', 'm/d/Y') : null;
            $inArray['supplementalProductEcoaDeliveryDate2'] = $propertyAppraisalDetails ? Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEcoaADeliveryDate2, 'YMD', 'm/d/Y') : null;
            $inArray['supplementalProductEcoaDeliveryDate3'] = $propertyAppraisalDetails ? Dates::formatDateWithRE($propertyAppraisalDetails->propertyAppraisalSupplementalProductEcoaADeliveryDate3, 'YMD', 'm/d/Y') : null;


            $propertyFullAddress = Property::$primaryPropertyFullAddress;
            $propertyType = $propertyTypeNumb ? Property::getPropertyTypeText(Property::$primaryPropertyInfo) : '';

            $inArray['propertyCity'] = $propertyCity;
            $inArray['propertyState'] = $propertyState;
            $inArray['propertyZip'] = $propertyZip;
            $inArray['propertyCounty'] = $propertyCounty;
            $inArray['propAddrInfo'] = $propertyFullAddress;  //correct
            $inArray['borPropAddress'] = $propertyAddress;  //this should ONLY be addr line

            $propertyFullAddress = ($propertyCounty) ? Strings::arrayToString([$propertyFullAddress, $propertyCounty]) : $propertyFullAddress;

            $inArray['propertyType'] = $propertyType;

            $inArray['propertyFeatures'] = $propertyFeatures;


            $propertyDetailsTableHeader = '<table border=\"0\" width=\"100%\">
					<tr>
						<td><b>SN</b></td>
						<td><b>Address</b></td>
						<td><b>City</b></td>
						<td><b>State</b></td>
						<td><b>Zip</b></td>
						<td><b>County</b></td>
						<td><b>Estimated Property Value</b></td>
						<td><b>Property type</b></td>
						<td><b>Features</b></td>
					</tr>';

            $propertyDetailsTableFooter = '</table>';


            $allPropertiesUrlLinks = '<table border=\"0\" width=\"100%\">
					<tr>
						<th><b>SN</b></th>
						<th><b>Property Address</b></th>
						<th><b>URL 1</b></th>
						<th><b>URL 2</b></th>
					</tr>';


            if ($googleDocs == 1) {
                $googlePropertyHeader = [
                    ['fieldColor' => '', 'fieldText' => 'SN'],
                    ['fieldColor' => '', 'fieldText' => 'Address'],
                    ['fieldColor' => '', 'fieldText' => 'City'],
                    ['fieldColor' => '', 'fieldText' => 'State'],
                    ['fieldColor' => '', 'fieldText' => 'Zip'],
                    ['fieldColor' => '', 'fieldText' => 'County'],
                    ['fieldColor' => '', 'fieldText' => 'Estim. Property Value'],
                    ['fieldColor' => '', 'fieldText' => 'Property Types'],
                    ['fieldColor' => '', 'fieldText' => 'Features'],
                ];

                $googlePrimaryProperty = [
                    ['fieldColor' => '', 'fieldText' => '1'],
                    ['fieldColor' => '', 'fieldText' => "$propertyAddress"],
                    ['fieldColor' => '', 'fieldText' => "$propertyCity"],
                    ['fieldColor' => '', 'fieldText' => "$propertyState"],
                    ['fieldColor' => '', 'fieldText' => "$propertyZip"],
                    ['fieldColor' => '', 'fieldText' => "$propertyCounty"],
                    ['fieldColor' => '', 'fieldText' => "$propertyestimatedValue"],
                    ['fieldColor' => '', 'fieldText' => "$propertyType"],
                    ['fieldColor' => '', 'fieldText' => "$propertyFeatures"],
                ];

                $googlePropertiesUrlLinks[] = [
                    ['fieldColor' => '', 'fieldText' => 'Property'],
                    ['fieldColor' => '', 'fieldText' => 'Property Address'],
                    ['fieldColor' => '', 'fieldText' => 'URL 1'],
                    ['fieldColor' => '', 'fieldText' => 'URL 2'],
                ];
                $googlePropertiesUrlLinks[] = [
                    ['fieldColor' => '', 'fieldText' => '1'],
                    ['fieldColor' => '', 'fieldText' => "$propertyFullAddress"],
                    ['fieldColor' => '', 'fieldText' => "$subjectPropURLLink1"],
                    ['fieldColor' => '', 'fieldText' => "$subjectPropURLLink2"],
                ];
            } else {
                $primaryPropertyTableRow = "<tr>
						                        <td>1</td>
						                        <td>$propertyAddress</td>
						                        <td>$propertyCity</td>
						                        <td>$propertyState</td>
						                        <td>$propertyZip</td>
						                        <td>$propertyCounty</td>
						                        <td>$propertyestimatedValue</td>
						                        <td>$propertyType</td>
						                        <td>$propertyFeatures</td>
					                        </tr>";

                $allPropertiesUrlLinks .= "
					<tr>
						<td>1</td>
						<td>$propertyFullAddress</td>
						<td>$subjectPropURLLink1</td>
						<td>$subjectPropURLLink2</td>
					</tr>";
            }

            $allPropertiesAddress .= "<p>$propertyFullAddress</p>" . "\n";

            $propertySerialNumber = 2;
            $blanketLoanPropertySerialNumber = 1;

            foreach (Property::$blanketLoanPropertiesInfo as $blanketProperty) {

                $addressInfo = Property::getPropertyFullAddress($blanketProperty);
                $propertyFullAddress = Property::getPropertyFullAddress($blanketProperty);
                $allPropertiesAddress .= "<p>$addressInfo</p>" . "\n";
                $blanketLoanPropertiesAddress .= "<p>$addressInfo</p>" . "\n";

                $inArray['propertyAddress' . $propertySerialNumber] = $blanketProperty->propertyAddress;
                $inArray['propertyType' . $propertySerialNumber] = Property::getPropertyTypeText($blanketProperty);
                $inArray['propertyCity' . $propertySerialNumber] = $blanketProperty->propertyCity;
                $inArray['propertyState' . $propertySerialNumber] = $blanketProperty->propertyState;
                $inArray['propertyZip' . $propertySerialNumber] = $blanketProperty->propertyZipCode;
                $inArray['propertyFullAddress' . $propertySerialNumber] = $addressInfo;


                $propertyFeatures = $blanketProperty->getTblPropertiesCharacteristics_by_propertyId()->propertyFeatures;
                $propertyTypeText = Property::getPropertyTypeText($blanketProperty);
                $propertyurlLink1 = $blanketProperty->getTblPropertiesDetails_by_propertyId()->propertyURLLink1;
                $propertyurlLink2 = $blanketProperty->getTblPropertiesDetails_by_propertyId()->propertyURLLink2;
                $propertyestimatedValue = $blanketProperty->getTblPropertiesDetails_by_propertyId()->propertyEstimatedValue;

                $googleBlanketLoanPropertiesArray[] = [
                    ['fieldColor' => '', 'fieldText' => "$propertySerialNumber"],
                    ['fieldColor' => '', 'fieldText' => "$blanketProperty->propertyAddress"],
                    ['fieldColor' => '', 'fieldText' => "$blanketProperty->propertyCity"],
                    ['fieldColor' => '', 'fieldText' => "$blanketProperty->propertyState"],
                    ['fieldColor' => '', 'fieldText' => "$blanketProperty->propertyZipCode"],
                    ['fieldColor' => '', 'fieldText' => "$blanketProperty->propertyCounty"],
                    ['fieldColor' => '', 'fieldText' => "$propertyestimatedValue"],
                    ['fieldColor' => '', 'fieldText' => "$propertyTypeText"],
                    ['fieldColor' => '', 'fieldText' => "$propertyFeatures"],
                ];


                $blanketLoanPropertyTableRowsAll .= "<tr>
						<td>$propertySerialNumber</td>
						<td>$blanketProperty->propertyAddress</td>
						<td>$blanketProperty->propertyCity</td>
						<td>$blanketProperty->propertyState</td>
						<td>$blanketProperty->propertyZipCode</td>
						<td>$blanketProperty->propertyCounty</td>
						<td>$propertyestimatedValue</td>
						<td>$propertyTypeText</td>
						<td>$propertyFeatures</td>
					</tr>";

                $blanketLoanPropertyTableRows .= "<tr>
						<td>$blanketLoanPropertySerialNumber</td>
						<td>$blanketProperty->propertyAddress</td>
						<td>$blanketProperty->propertyCity</td>
						<td>$blanketProperty->propertyState</td>
						<td>$blanketProperty->propertyZipCode</td>
						<td>$blanketProperty->propertyCounty</td>
						<td>$propertyestimatedValue</td>
						<td>$propertyTypeText</td>
						<td>$propertyFeatures</td>
					</tr>";

                $googlePropertiesUrlLinks[] = [
                    ['fieldColor' => '', 'fieldText' => "$propertySerialNumber"],
                    ['fieldColor' => '', 'fieldText' => "$propertyFullAddress"],
                    ['fieldColor' => '', 'fieldText' => "$propertyurlLink1"],
                    ['fieldColor' => '', 'fieldText' => "$propertyurlLink2"],
                ];

                $allPropertiesUrlLinks .= "<tr>
                                                <td>$propertySerialNumber</td>
                                                <td>$propertyFullAddress</td>
                                                <td>$propertyurlLink1</td>
                                                <td>$propertyurlLink2</td>
                                           </tr>";
                $propertySerialNumber++;
                $blanketLoanPropertySerialNumber++;
            }

            if ($googleDocs == 1) {
                $allPropertiesAddress = str_replace('</p>', '', str_replace('<p>', '', $allPropertiesAddress));
                $blanketLoanPropertiesAddress = str_replace('</p>', '', str_replace('<p>', '', $blanketLoanPropertiesAddress));
            }
            $inArray['allProperties'] = $allPropertiesAddress;
            $inArray['additionalProperties'] = $blanketLoanPropertiesAddress;

            $allPropertiesUrlLinks .= '</table>';

            if ($googleDocs == 1) {
                $googleALLProperties[] = $googlePropertyHeader;
                $googleALLProperties[] = $googlePrimaryProperty;
                $googleBlanketLoanProperties[] = $googlePropertyHeader;

                foreach ($googleBlanketLoanPropertiesArray as $eachBlanketProperty) {
                    $googleALLProperties[] = $eachBlanketProperty;
                    $googleBlanketLoanProperties[] = $eachBlanketProperty;
                }

                $inArray['tables'][] = [
                    'mergeTableTag'  => '##All Properties Detailed##',
                    'mergeTableData' => $googleALLProperties,
                ];
                $inArray['tables'][] = [
                    'mergeTableTag'  => '##Additional Properties Detailed##',
                    'mergeTableData' => $googleBlanketLoanProperties,
                ];

                $inArray['tables'][] = [
                    'mergeTableTag'  => '##All Properties - URL links##',
                    'mergeTableData' => $googlePropertiesUrlLinks,
                ];
            } else {
                $inArray['allPropertiesdetailed'] = $propertyDetailsTableHeader . $primaryPropertyTableRow . $blanketLoanPropertyTableRowsAll . $propertyDetailsTableFooter;
                $inArray['additionalPropertiesdetailed'] = $propertyDetailsTableHeader . $blanketLoanPropertyTableRows . $propertyDetailsTableFooter;
                $inArray['allPropertiesUrlLinks'] = $allPropertiesUrlLinks;
            }
            $propertyCount = 0;
            if ($primaryPropertyInfo && $primaryPropertyInfo->propertyId) {
                $propertyCount = 1;
            }
            $propertyCount += count(Property::$blanketLoanPropertiesInfo ?? []);

            $inArray['noOfPropertiesAcquiring'] = $propertyCount;


            /******************************* End Of Properties *************************/

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }

            // Safely access flood certificate info
            $propertyFloodCertificatesInfo = [];
            if (Property::$primaryPropertyInfo !== null) {
                $propertyFloodCertificatesInfo = Property::$primaryPropertyInfo->getTblPropertiesFloodCertificates_by_propertyId();
            }
            $floodCertificateSerialNumber = 1;
            foreach ($propertyFloodCertificatesInfo as $eachCertificate ){
                $inArray['propertyFloodZone'.$floodCertificateSerialNumber] = Property::$propertyFloodZoneList[$eachCertificate->propertyFloodZone];
                $floodCertificateSerialNumber++;
            }


            /* RESTInfo Info */

            $noteDate = '';
            $RESTInfo1Array = [];
            if (count($RESTInfoArray) > 0) {

                if (array_key_exists($LMRID, $RESTInfoArray)) {
                    $RESTInfo1Array = $RESTInfoArray[$LMRID];
                    if (count($RESTInfo1Array) > 0) {
                        $noteDate = trim($RESTInfo1Array['noteDate']);

                        if (Dates::IsEmpty($noteDate)) {
                            $noteDate = '';
                        } else {
                            $noteDate = Dates::formatDateWithRE($noteDate, 'YMD', 'm/d/Y');
                        }
                        $inArray['noteDate'] = $noteDate;
                    }
                }
            }

            /* RESTInfo Info */
            /**
             * Get all assigned staff info.
             */
            $assignedAttorneyName = '';
            $assignedEmployeeName = '';
            $AssignedprocessorName = '';
            $assignedLoanOfficerAssistantName = '';
            $assignedCloser = '';
            $assignedTransactionCoordinator = '';
            if (isset($LMRID, $assignedEmpInfo)) {
                for ($g = 0; $g < count($assignedEmpInfo[$LMRID] ?? []); $g++) {
                    $processorName = $assignedEmpInfo[$LMRID][$g]['processorName'] . ' ' . $assignedEmpInfo[$LMRID][$g]['processorLName'];

                    $assignedEmployeeName .= $processorName;

                    if ($assignedEmpInfo[$LMRID][$g]['email'] != '') $assignedEmployeeName .= ' / ' . $assignedEmpInfo[$LMRID][$g]['email'];
                    if ($assignedEmpInfo[$LMRID][$g]['fax'] != '') $assignedEmployeeName .= ' / ' . Strings::formatPhoneNumber($assignedEmpInfo[$LMRID][$g]['fax']);
                    if ($assignedEmpInfo[$LMRID][$g]['directPhone'] != '') $assignedEmployeeName .= ' / ' . Strings::formatPhoneNumber($assignedEmpInfo[$LMRID][$g]['directPhone']);
                    $assignedEmployeeName .= '<br>';
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Attorney') $assignedAttorneyName = $processorName; // Attorney Name.
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Processor') $AssignedprocessorName = $processorName; // Processor Name.
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Loan Officer Assistant') $assignedLoanOfficerAssistantName = $processorName; // Loan Officer Assistant Name.
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Closer') {
                        $assignedCloser .= $assignedEmpInfo[$LMRID][$g]['processorName'] . ' ' .
                            $assignedEmpInfo[$LMRID][$g]['processorLName'] . '<br> ' . $assignedEmpInfo[$LMRID][$g]['email'] .
                            '<br> ' . Strings::formatPhoneNumber($assignedEmpInfo[$LMRID][$g]['cellNumber']); // Processor Name.
                        $assignedCloser .= '<br> ';

                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Transaction Coordinator') {
                        $assignedTransactionCoordinator .= $assignedEmpInfo[$LMRID][$g]['processorName'] . ' ' .
                            $assignedEmpInfo[$LMRID][$g]['processorLName'] . '<br>' . Strings::formatPhoneNumber($assignedEmpInfo[$LMRID][$g]['cellNumber']) .
                            '<br>' . $assignedEmpInfo[$LMRID][$g]['email']; // Processor Name.
                        $assignedTransactionCoordinator .= '<br> ';

                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Manager') {
                        $assignedManager = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'QC') {
                        $assignedQC = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Negotiator') {
                        $assignedNegotiator = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Mediator') {
                        $assignedMediator = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Plaintiff attorney') {
                        $assignedPlaintiffattorney = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Defense attorney') {
                        $assignedDefenseattorney = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Servicer rep') {
                        $assignedServicerrep = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Investor') {
                        $assignedInvestor = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Title Rep') {
                        $assignedTitleRep = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Case Manager') {
                        $assignedCaseManager = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Legal Assistant') {
                        $assignedLegalAssistant = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Processing Manager') {
                        $assignedProcessingManager = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Administrator') {
                        $assignedAdministrator = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Senior Underwriter') {
                        $assignedSeniorUnderwriter = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Paralegal') {
                        $assignedParalegal = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Internal Sales') {
                        $assignedInternalSales = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Network Coordinator') {
                        $assignedNetworkCoordinator = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Sales Manager') {
                        $assignedSalesManager = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Super') {
                        $assignedSuper = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Sales') {
                        $assignedSales = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Appraiser') {
                        $assignedAppraiser = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Asset Manager') {
                        $assignedAssetManager = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Processor Assistant') {
                        $assignedProcessorAssistant = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Underwriter') {
                        $assignedUnderwriter = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Funding Coordinator') {
                        $assignedFundingCoordinator = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Account Manager') {
                        $assignedAccountManager = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Accounting') {
                        $assignedAccounting = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Head of Origination') {
                        $assignedHeadofOrigination = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Asset Management') {
                        $assignedAssetManagement = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'VP of Sales') {
                        $assignedVPofSales = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Managing Director') {
                        $assignedManagingDirector = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Assistant') {
                        $assignedAssistant = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Branch Manager') {
                        $assignedBranchManager = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Assistant/ Transaction Coordinator') {
                        $assignedAssistantTransactionCoordinator = $processorName;
                    }
                    if ($assignedEmpInfo[$LMRID][$g]['role'] == 'Director of Operations') {
                        $assignedDirectorofOperations = $processorName;
                    }

                }
            }
            $inArray['assignedEmployeeName'] = $assignedEmployeeName;
            $inArray['assignedAttorneyName'] = $assignedAttorneyName;
            $inArray['assignedProcessor'] = $AssignedprocessorName;
            $inArray['assignedLoanOfficerAssistant'] = $assignedLoanOfficerAssistantName;
            $inArray['assignedCloser'] = $assignedCloser;
            $inArray['assignedTransactionCoordinator'] = $assignedTransactionCoordinator;
            $inArray['assignedManager'] = $assignedManager;
            $inArray['assignedQC'] = $assignedQC;
            $inArray['assignedNegotiator'] = $assignedNegotiator;
            $inArray['assignedMediator'] = $assignedMediator;
            $inArray['assignedPlaintiffattorney'] = $assignedPlaintiffattorney;
            $inArray['assignedDefenseattorney'] = $assignedDefenseattorney;
            $inArray['assignedServicerrep'] = $assignedServicerrep;
            $inArray['assignedInvestor'] = $assignedInvestor;
            $inArray['assignedTitleRep'] = $assignedTitleRep;
            $inArray['assignedCaseManager'] = $assignedCaseManager;
            $inArray['assignedLegalAssistant'] = $assignedLegalAssistant;
            $inArray['assignedProcessingManager'] = $assignedProcessingManager;
            $inArray['assignedAdministrator'] = $assignedAdministrator;
            $inArray['assignedSeniorUnderwriter'] = $assignedSeniorUnderwriter;
            $inArray['assignedParalegal'] = $assignedParalegal;
            $inArray['assignedInternalSales'] = $assignedInternalSales;
            $inArray['assignedNetworkCoordinator'] = $assignedNetworkCoordinator;
            $inArray['assignedSalesManager'] = $assignedSalesManager;
            $inArray['assignedSuper'] = $assignedSuper;
            $inArray['assignedSales'] = $assignedSales;
            $inArray['assignedAppraiser'] = $assignedAppraiser;
            $inArray['assignedAssetManager'] = $assignedAssetManager;
            $inArray['assignedProcessorAssistant'] = $assignedProcessorAssistant;
            $inArray['assignedUnderwriter'] = $assignedUnderwriter;
            $inArray['assignedFundingCoordinator'] = $assignedFundingCoordinator;
            $inArray['assignedAccountManager'] = $assignedAccountManager;
            $inArray['assignedAccounting'] = $assignedAccounting;
            $inArray['assignedHeadofOrigination'] = $assignedHeadofOrigination;
            $inArray['assignedAssetManagement'] = $assignedAssetManagement;
            $inArray['assignedVPofSales'] = $assignedVPofSales;
            $inArray['assignedManagingDirector'] = $assignedManagingDirector;
            $inArray['assignedAssistant'] = $assignedAssistant;
            $inArray['assignedBranchManager'] = $assignedBranchManager;
            $inArray['assignedAssistantTransactionCoordinator'] = $assignedAssistantTransactionCoordinator;
            $inArray['assignedDirectorofOperations'] = $assignedDirectorofOperations;


            /* Add estimated total budget to merge tags */


            $totalListOfRepairs = '';
            $fileHMLOListOfRepairs = [];
            if (count($fileHMLOListOfRepairsInfoArray) > 0) {
                if (array_key_exists($LMRID, $fileHMLOListOfRepairsInfoArray)) {
                    $fileHMLOListOfRepairs = $fileHMLOListOfRepairsInfoArray[$LMRID];
                    $totalListOfRepairs = Strings::replaceCommaValues($fileHMLOListOfRepairs['architectFees'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['permitsFees'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['demolitionTrashDumpsters'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['exteriorRepairs'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['termiteInspectionTreatment'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['foundationStructuralReport'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['roofing'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['windows'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['doors'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['siding'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['carpentry'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['deckPorch'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['drivewayWalkwayPatio'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['landscaping'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['exteriorRepairsOther'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['HVACRough'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['HVACFinish'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['plumbingRough'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['plumbingFixtures'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['plumbingFinish'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['electricalRough'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['electricalFixtures'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['electricalFinish'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['sheetRock'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['interiorRepairsDoors'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['interiorRepairsCarpentry'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['interiorRepairsOther1'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['interiorRepairsOther2'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['interiorRepairsOther3'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['kitchenCabinets'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['kitchenCountertops'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['kitchenAppliances'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['bath1'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['bath2'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['bath3'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['interiorPainting'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['exteriorPainting'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['flooringCarpetVinyl'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['flooringTile'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['flooringHardwood'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['finalCleanupOther1'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['finalCleanupOther2'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['finalCleanupOther3'])
                        + Strings::replaceCommaValues($fileHMLOListOfRepairs['finalCleanupOther4']);

                    $totalListOfRepairs = round(Strings::replaceCommaValues($totalListOfRepairs), 2);

                    $inArray['totalListOfRepairs'] = $totalListOfRepairs;
                }
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /* File - File Number Admin section Info */
            if (array_key_exists($LMRID, $responseInfoArray)) {
                $responseInfo = $responseInfoArray[$LMRID];
                $LMRResponseId = trim($responseInfo['LMRResponseId']);
                if (count($responseInfo) > 0) {
                    $trialModReceivedDate = '';
                    $trialPaymentDate1 = '';
                    $trialPaymentDate2 = '';
                    $trialPaymentDate3 = '';
                    $lenderSubmissionDate = '';
                    $denialDate = '';
                    $appealDate = '';
                    $lenderInternalNotes = '';
                    $escalationDate = '';
                    $trialPaymentDateLong1 = '';
                    $trialPaymentDate1LongLong = '';
                    $welcomeCallDate = $filestatus = '';
                    $borrowerCallBack = '';
                    $primaryStatusId = 0;

                    $trialModReceivedDate = trim($responseInfo['trialModReceivedDate']);
                    $trialPaymentDate1 = trim($responseInfo['trialPaymentDate1']);
                    $trialPaymentDate2 = trim($responseInfo['trialPaymentDate2']);
                    $trialPaymentDate3 = trim($responseInfo['trialPaymentDate3']);
                    $lenderSubmissionDate = trim($responseInfo['lenderSubmissionDate']);
                    $denialDate = trim($responseInfo['denialDate']);
                    $appealDate = trim($responseInfo['appealDate']);
                    $escalationDate = trim($responseInfo['escalationDate']);
                    $welcomeCallDate = trim($responseInfo['welcomeCallDate']);
                    $borrowerCallBack = trim($responseInfo['borrowerCallBack']);

                    if (Dates::IsEmpty($trialModReceivedDate)) {
                        $trialModReceivedDate = '';
                    } else {
                        $trialModReceivedDate = Dates::formatDateWithRE($trialModReceivedDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($trialPaymentDate1)) {
                        $trialPaymentDate1 = '';
                    } else {
                        $trialPaymentDate1 = Dates::formatDateWithRE($trialPaymentDate1, 'YMD', 'm/d/Y');
                        $trialPaymentDateLong1 = date('M d, Y', strtotime($trialPaymentDate1));
                        $trialPaymentDate1LongLong = date('F d, Y', strtotime($trialPaymentDate1));
                    }
                    if (Dates::IsEmpty($trialPaymentDate2)) {
                        $trialPaymentDate2 = '';
                    } else {
                        $trialPaymentDate2 = Dates::formatDateWithRE($trialPaymentDate2, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($trialPaymentDate3)) {
                        $trialPaymentDate3 = '';
                    } else {
                        $trialPaymentDate3 = Dates::formatDateWithRE($trialPaymentDate3, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($lenderSubmissionDate)) {
                        $lenderSubmissionDate = '';
                    } else {
                        $lenderSubmissionDate = Dates::formatDateWithRE($lenderSubmissionDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($denialDate)) {
                        $denialDate = '';
                    } else {
                        $denialDate = Dates::formatDateWithRE($denialDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($appealDate)) {
                        $appealDate = '';
                    } else {
                        $appealDate = Dates::formatDateWithRE($appealDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($escalationDate)) {
                        $escalationDate = '';
                    } else {
                        $escalationDate = Dates::formatDateWithRE($escalationDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($welcomeCallDate)) {
                        $welcomeCallDate = '';
                    } else {
                        $welcomeCallDate = Dates::formatDateWithRE($welcomeCallDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($borrowerCallBack)) {
                        $borrowerCallBack = '';
                    } else {
                        $borrowerCallBack = Dates::formatDateWithRE($borrowerCallBack, 'YMD', 'm/d/Y');
                    }

                    $inArray['lenderSubmissionDate'] = $lenderSubmissionDate;
                    $inArray['denialDate'] = $denialDate;
                    $inArray['fileNumber'] = trim($responseInfo['fileNumber']);
                    $inArray['firstModPaymentAmt'] = Integers::formatInputToInt(trim($responseInfo['firstModPaymentAmt']));
                    $inArray['trialModReceivedDate'] = $trialModReceivedDate;
                    $inArray['trialPaymentDate1'] = $trialPaymentDate1;
                    $inArray['trialPaymentDateLong1'] = $trialPaymentDateLong1;
                    $inArray['trialPaymentDate1LongLong'] = $trialPaymentDate1LongLong;
                    $inArray['trialPaymentDate2'] = $trialPaymentDate2;
                    $inArray['trialPaymentDate3'] = $trialPaymentDate3;
                    $inArray['appealDate'] = $appealDate;
                    $inArray['escalationDate'] = $escalationDate;
                    $inArray['lenderInternalNotes'] = trim($responseInfo['lenderInternalNotes']);
                    $inArray['projectName'] = trim($responseInfo['projectName']);
                    $inArray['welcomeCallDate'] = $welcomeCallDate; /* General New  Tags  04-02-2019*/
                    $inArray['borrowerCallBack'] = $borrowerCallBack; /* General New  Tags  04-02-2019*/
                    $inArray['leadSource'] = $responseInfo['leadSource'];
                    $inArray['3rdPartyFileId'] = $responseInfo['3rdPartyFileId'];
                    $primaryStatusId = $responseInfo['primeStatusId'];
                    $inArray['primaryStatusId'] = $primaryStatusId;
                    if (count($PCStatusInfo) > 0) {
                        for ($j = 0; $j < count($PCStatusInfo); $j++) {
                            if ($PCStatusInfo[$j]['PSID'] == $primaryStatusId) {
                                $filestatus = $PCStatusInfo[$j]['primaryStatus'];
                                break;
                            }
                        }
                    }
                    $inArray['filestatus'] = $filestatus;
                    $inArray['ecoaWaiverStatus'] = $responseInfo['eCoaWaiverStatus'];
                }
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            $filessubtatus = '';
            for ($sub = 0; $sub < count($fileSubstatusInfo); $sub++) {
                if ($filessubtatus == '')
                    $filessubtatus = $fileSubstatusInfo[$sub]['substatus'];
                else
                    $filessubtatus = $filessubtatus . ' , ' . $fileSubstatusInfo[$sub]['substatus'];
            }
            $inArray['filessubtatus'] = $filessubtatus;
            $insuranceDetailsHTML = '';


            if (count($getInsuranceDtls ?? []) > 0) {
                $insuranceDetailsHTML = '<table width="100%" border="0" cellspacing="5" cellpadding="5">
                                        <tr>
                                            <td>Policy Infornation</td>
                                            <td>Name of Carrier</td>
                                            <td>Name of Policy</td>
                                            <td>Policy Number</td>
                                            <td>Annual Premium</td>
                                            <td>Effec. Date<hr>Expir. Date</td>
                                            <td>Ins. Notes</td>
                                            <td>Insurance Date Received</td>
                                            <td>Insurance Date Ordered</td>
                                            </tr>';
                foreach ($getInsuranceDtls as $InsuranceDtl) {
                    $insuranceDetailsHTML .= '<tr><td>' . $InsuranceDtl['insuranceTypes'] . '</td>';
                    $insuranceDetailsHTML .= '<td>' . $InsuranceDtl['policyCarrier'] . '</td><td>' . $InsuranceDtl['policyName'] . '</td>';
                    $insuranceDetailsHTML .= '<td>' . $InsuranceDtl['policyNumber'] . '</td>';
                    $insuranceDetailsHTML .= '<td>$ ' . number_format(Strings::replaceCommaValues(trim($InsuranceDtl['policyAnnualPremium'])), 2) . '</td>';
                    $insuranceDetailsHTML .= '<td>' . $InsuranceDtl['policyEffDate'] . '<hr>' . $InsuranceDtl['policyExpDate'] . '</td>';
                    $insuranceDetailsHTML .= '<td>' . $InsuranceDtl['policyNote'] . '</td>';
                    $insuranceDetailsHTML .= '<td>' . $InsuranceDtl['insuranceDateReceived'] . '</td>';
                    $insuranceDetailsHTML .= '<td>' . $InsuranceDtl['insuranceDateOrdered'] . '</td></tr>';
                }
                $insuranceDetailsHTML .= '</table>';
            }

            $inArray['getInsuranceDtls'] = $insuranceDetailsHTML;

            $insuranceDetailsHTMLS = [];
            $insuranceDetailsHTMLS[] = [
                ['fieldColor' => '', 'fieldText' => 'Policy Information'],
                ['fieldColor' => '', 'fieldText' => 'Name Of Carrier'],
                ['fieldColor' => '', 'fieldText' => 'Name of Policy:'],
                ['fieldColor' => '', 'fieldText' => 'Policy Number:'],
                ['fieldColor' => '', 'fieldText' => 'Annual Premium:'],
                ['fieldColor' => '', 'fieldText' => "Effec. Date\n---\nExpir. Date"],
                ['fieldColor' => '', 'fieldText' => 'Insurance Date Received'],
                ['fieldColor' => '', 'fieldText' => 'Insurance Date Ordered'],
                ['fieldColor' => '', 'fieldText' => 'Ins. Notes'],
            ];
            if (count($getInsuranceDtls ?? []) > 0) {
                $policyCount = 1;
                foreach ($getInsuranceDtls as $InsuranceDtl) {
                    $insuranceDetailsHTMLS[] = [
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['insuranceTypes']],
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['policyCarrier']],
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['policyName']],
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['policyNumber']],
                        ['fieldColor' => '', 'fieldText' => '$ ' . number_format(Strings::replaceCommaValues(trim($InsuranceDtl['policyAnnualPremium'])), 2)],
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['policyEffDate'] . "\n---\n" . $InsuranceDtl['policyExpDate']],
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['insuranceDateReceived']],
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['insuranceDateOrdered']],
                        ['fieldColor' => '', 'fieldText' => $InsuranceDtl['policyNote']],
                    ];
                }
            }
            if ($googleDocs == 1) {
                $inArray['tables'][] = [
                    'mergeTableTag'  => '##Insurance Policies##',
                    'mergeTableData' => $insuranceDetailsHTMLS,
                ];
            } else {
                $inArray['insurancePolicies'] = $insuranceDetailsHTML;
            }
            /* File - File Number Admin section Info */

            if (count($clientTypeInfoArray) > 0) {
                if (array_key_exists($LMRID, $clientTypeInfoArray)) {
                    $selectedClientTypeArray = $clientTypeInfoArray[$LMRID];
                }
            }
            /* SREO Info Start */
            $sreo = [];
            if (count($sreoInfo ?? []) > 0) {
                if (array_key_exists($LMRID, $sreoInfo)) {
                    $sreo = $sreoInfo[$LMRID];
                    if (count($sreo) > 0) {
                        for ($sre = 0; $sre < 2; $sre++) {
                            $inArray['presentMarketValue' . ($sre + 1)] = trim($sreo[$sre]['presentMarketValue']);
                            $inArray['valueofImprovementsMade' . ($sre + 1)] = trim($sreo[$sre]['valueofImprovementsMade']);
                            $inArray['amountOfMortgages' . ($sre + 1)] = trim($sreo[$sre]['amountOfMortgages']);
                        }
                    }

                }
            }
            /* SREO Info END */

            /* Income Info */
            if (array_key_exists($LMRID, $incomeInfoArray)) {
                $incomeInfo = $incomeInfoArray[$LMRID];
                if (count($incomeInfo) > 0) {
                    $taxes1 = trim($incomeInfo['taxes1']);
                    $insurance1 = trim($incomeInfo['insurance1']);
                    $floodInsurance1 = trim($incomeInfo['floodInsurance1']);
                    $HOAFees1 = trim($incomeInfo['HOAFees1']);
                    $mortgageInsurance1 = trim($incomeInfo['mortgageInsurance1']);
                    $unemployment = floatval(trim($incomeInfo['unemployment1'])) + floatval(trim($incomeInfo['unemployment2']));
                    $foodStampWelfare = floatval(trim($incomeInfo['foodStampWelfare1'])) + floatval(trim($incomeInfo['foodStampWelfare2']));
                    $floodInsurance = $floodInsurance1;
                    $ownershipLessOrMore = '';
                    if ($incomeInfo['emptypeshare1'] == 'lessthan25') {
                        $ownershipLessOrMore = 'I have ownership share of less than 25%';
                    } elseif ($incomeInfo['emptypeshare1'] == 'eqmorethan25') {
                        $ownershipLessOrMore = 'I have ownership share of 25% or more';
                    }

                    $inArray['taxes1'] = $taxes1;
                    $inArray['insurance1'] = $insurance1;
                    $inArray['mortgageInsurance1'] = $mortgageInsurance1;
                    $inArray['HOAFees1'] = $HOAFees1;

                    $inArray['employerName'] = $incomeInfo['employer1'];
                    $inArray['employerPhone'] = trim(Strings::formatPhoneNumber($incomeInfo['employer1Phone']));
                    $inArray['employerStreetAddr'] = $incomeInfo['employer1Add'];
                    $inArray['employerCity'] = $incomeInfo['employer1City'];
                    $inArray['employerState'] = $incomeInfo['employer1State'];
                    $inArray['employerZip'] = $incomeInfo['employer1Zip'];
                    $inArray['employerCountry'] = $incomeInfo['employer1Country'];

                    $inArray['occupation1'] = $incomeInfo['occupation1'];
                    $inArray['empStartDate'] = $incomeInfo['borrowerHireDate'];
                    $inArray['yearsAtJob1'] = $incomeInfo['yearsAtJob1'];
                    $inArray['employedByOtherParty'] = $incomeInfo['employedByOtherParty'] = 1 ? 'Yes' : 'No';
                    $inArray['ownerOrSelfEmp'] = $incomeInfo['ownerOrSelfEmpoyed'] = 1 ? 'Yes' : 'No';
                    $inArray['ownershipLessOrMore'] = $ownershipLessOrMore;
                    $inArray['empmonthlyincome1'] = $incomeInfo['empmonthlyincome1'];
                    $inArray['grossIncome1'] = $incomeInfo['grossIncome1'];
                    $inArray['coborrowerBaseIncome'] = $incomeInfo['grossIncome2'];
                    $inArray['overtime1'] = $incomeInfo['overtime1'];
                    $inArray['commissionOrBonus1'] = $incomeInfo['commissionOrBonus1'];
                    $inArray['militaryIncome1'] = $incomeInfo['militaryIncome1'];
                    $inArray['otherHouseHold1'] = $incomeInfo['otherHouseHold1'];
                    $inArray['otherMortgage1'] = $incomeInfo['otherMortgage1'];
                    $inArray['otherMortgageBalance1'] = $incomeInfo['otherMortgageBalance1'];
                }
            }
            /* Income Info */

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /* Property Info */
            if (count($listingHistoryInfoArray) > 0) {
                if (array_key_exists($LMRID, $listingHistoryInfoArray)) {
                    $listingHistoryInfo = $listingHistoryInfoArray[$LMRID];
                    if (count($listingHistoryInfo) > 0) $inArray['mlsNo'] = trim($listingHistoryInfo['mlsNo']);
                }
            }
            if (count($propertyInfoArray) > 0) {
                if (array_key_exists($LMRID, $propertyInfoArray)) {
                    $propertyInfo = $propertyInfoArray[$LMRID];
                    if (count($propertyInfo) > 0) {
                        $titleReportDate = '';
                        $titleReportDate = $propertyInfo['titleReportDate'];
                        if (Dates::IsEmpty($titleReportDate)) {
                            $titleReportDate = '';
                        } else {
                            $titleReportDate = Dates::formatDateWithRE($titleReportDate, 'YMD', 'm/d/Y');
                        }
                        $titleOrderedDate = '';
                        $titleOrderedDate = $propertyInfo['titleOrderedDate'];
                        if (Dates::IsEmpty($titleOrderedDate)) {
                            $titleOrderedDate = '';
                        } else {
                            $titleOrderedDate = Dates::formatDateWithRE($titleOrderedDate, 'YMD', 'm/d/Y');
                        }
                        $inArray['titleReportDate'] = $titleReportDate;
                        $inArray['titleOrderedDate'] = $titleOrderedDate;
                        $inArray['titleName'] = trim($propertyInfo['titleName']);
                        $inArray['titleSeller'] = trim($propertyInfo['titleSeller']);
                        $inArray['titleEscrowNo'] = trim($propertyInfo['titleEscrowNo']);
                        $inArray['isHouseProperty'] = trim($propertyInfo['isHouseProperty']);
                        $inArray['titleOrderNumber'] = trim($propertyInfo['titleOrderNumber']);
                        $inArray['titleRecordingNumber'] = trim($propertyInfo['recordingNo']);
                    }
                }
            }
            /* Property Info */

            /* File - Client service type Info */
            $LMRServiceType = '';
            $clientServiceType = [];
            if (array_key_exists($LMRID, $clientServiceTypeInfo)) {
                $clientServiceType = $clientServiceTypeInfo[$LMRID];
                if (count($clientServiceType) > 0) {
                    for ($j = 0; $j < count($clientServiceType); $j++) {
                        $clientTypeCode = '';
                        $clientTypeCode = trim($clientServiceType[$j]['ClientType']);
                        if (array_key_exists($clientTypeCode, $LMRClientTypeArray)) {
                            if ($j > 0) $LMRServiceType .= ', ';
                            $LMRServiceType .= trim($LMRClientTypeArray[$clientTypeCode]['serviceType']);
                        }
                    }
                    $inArray['LMRServiceType'] = $LMRServiceType;
                }
            }

            /* File - Client service type Info */

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /* File - Attorney  Info */
            if (array_key_exists($LMRID, $QAInfoArray)) {
                $QAInfo = $QAInfoArray[$LMRID];
                if (count($QAInfo) > 0) {
                    $inArray['fileAttorneyFirmName'] = ucwords(trim($QAInfo['attorneyFirmName']));
                    $inArray['fileAttorneyName'] = ucwords(trim($QAInfo['attorneyName']));
                    $inArray['fileAttorneyPhone'] = Strings::formatPhoneNumber(trim($QAInfo['attorneyPhone']));
                    $inArray['fileAttorneyFax'] = Strings::formatPhoneNumber(trim($QAInfo['attorneyFax']));
                    $inArray['fileAttorneyEmail'] = trim($QAInfo['attorneyEmail']);
                    $inArray['fileAttorneyAddress'] = ucwords(trim($QAInfo['attorneyAddress']));
                    $inArray['fileAttorneyCity'] = ucwords(trim($QAInfo['attorneyCity']));
                    $inArray['fileAttorneyState'] = trim($QAInfo['attorneyState']);
                    $inArray['fileAttorneyZip'] = trim($QAInfo['attorneyZip']);
                    $inArray['fileAttorneySaleNumb'] = trim($QAInfo['attorneyNumber']);
                    $inArray['jurisDiction'] = trim($QAInfo['jurisDiction']);
                    $inArray['HOAOrCOAFeeAmt'] = Currency::formatDollarAmountWithDecimal(trim($QAInfo['HOAOrCOAFeeAmt']));
                    $reasonForDenialArray = explode('~', $QAInfo['reasonForDenial']) ?? [];
                    $reasonForDenial = '';
                    foreach ($reasonForDenialArray as $data) {
                        $reasonForDenial .= ',' . HMDAActionTaken::$reasonForDenial[$data];
                    }

                    $inArray['legalEntityIdentifier'] = $QAInfo['legalEntityIdentifier'];
                    $inArray['universalLoanIdentifier'] = $QAInfo['universalLoanIdentifier'];
                    $inArray['actionTaken'] = HMDAActionTaken::$getHMDAActionTaken[$QAInfo['actionTaken']];
                    $inArray['typeOfPurchaser'] = HMDAActionTaken::$typeOfPurchaser[$QAInfo['typeOfPurchaser']];
                    $inArray['reasonForDenial'] = ltrim($reasonForDenial, ',');
                    $inArray['censusTract'] = Currency::formatDollarAmountWithDecimal($QAInfo['censusTract']);

                    $summonDate = '';
                    $noticeReceivedDate = '';
                    $closingDate = $desiredClosingDate = '';
                    $closingDateLong = '';
                    $closingDateLongLong = '';
                    $summonDate = trim($QAInfo['summonDate']);
                    $noticeReceivedDate = trim($QAInfo['noticeReceivedDate']);
                    $closingDate = trim($QAInfo['closingDate']);
                    $desiredClosingDate = trim($QAInfo['desiredClosingDate']);

                    if (Dates::IsEmpty($summonDate)) {
                        $summonDate = '';
                    } else {
                        $summonDate = Dates::formatDateWithRE($summonDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($noticeReceivedDate)) {
                        $noticeReceivedDate = '';
                    } else {
                        $noticeReceivedDate = Dates::formatDateWithRE($noticeReceivedDate, 'YMD', 'm/d/Y');
                    }

                    if (Dates::IsEmpty($closingDate)) {
                        $closingDate = $purchaseCloseDate = '';
                    } else {
                        $closingDate = $purchaseCloseDate = Dates::formatDateWithRE($closingDate, 'YMD', 'm/d/Y');
                        $closingDateLongLong = date('F d, Y', strtotime($closingDate));
                        $closingDateLong = date('M. d, Y', strtotime($closingDate));
                    }

                    if (Dates::IsEmpty($desiredClosingDate)) {
                        $desiredClosingDate = '';
                    } else {
                        $desiredClosingDate = Dates::formatDateWithRE($desiredClosingDate, 'YMD', 'm/d/Y');
                    }

                    $inArray['closingDate'] = $closingDate;
                    $inArray['targetClosingDate'] = Dates::formatDateWithRE(LMRequest::myFileInfo()->fileHMLOInfo()->targetClosingDate, 'YMD', 'm/d/Y');;
                    $inArray['desiredClosingDate'] = $desiredClosingDate;
                    $inArray['summonDate'] = $summonDate;
                    $inArray['noticeReceivedDate'] = $noticeReceivedDate;
                    $inArray['closingDateLong'] = $closingDateLong;
                    $inArray['closingDateLongLong'] = $closingDateLongLong;

                    $dateDiff = [];
                    $dateDiff['lastPaymentMade'] = date('Y-m-d');
                    $dateDiff['futureDate'] = $closingDate;
                    $dayuntilclose = Integers::calculateNoOfDaysBehind($dateDiff);
                    if ($dayuntilclose < 0) {
                        $dayuntilclose = '';
                    }
                    $inArray['daysUntilClose'] = $dayuntilclose;


                    $hearingDate = '';
                    $hearingDateLong = '';
                    $hearingDate = trim($QAInfo['hearingDate']);
                    if (Dates::IsEmpty($hearingDate)) {
                        $hearingDate = '';
                        $hearingDateLong = '';
                    } else {
                        $hearingDate = Dates::formatDateWithRE($hearingDate, 'YMD', 'm/d/Y');
                        $hearingDateLong = date('M d, Y', strtotime($hearingDate));
                    }
                    $inArray['hearingDate'] = $hearingDate;
                    $inArray['hearingDateLong'] = $hearingDateLong;

                    $inArray['PublishBInfo'] = $QAInfo['PublishBInfo'];
                    $inArray['BEthnicity'] = $QAInfo['BEthnicity'];
                    $inArray['BRace'] = $QAInfo['BRace'];
                    $inArray['BGender'] = $QAInfo['BGender'];
                    $inArray['BVeteran'] = $QAInfo['BVeteran'];
                    $inArray['bFiEthnicity'] = $QAInfo['bFiEthnicity'];
                    $inArray['bFiSex'] = $QAInfo['bFiSex'];
                    $inArray['bFiRace'] = $QAInfo['bFiRace'];
                    $inArray['bDemoInfo'] = $QAInfo['bDemoInfo'];


                    $inArray['PublishCBInfo'] = $QAInfo['PublishCBInfo'];
                    $inArray['CBEthnicity'] = $QAInfo['CBEthnicity'];
                    $inArray['CBRace'] = $QAInfo['CBRace'];
                    $inArray['CBGender'] = $QAInfo['CBGender'];
                    $inArray['CBVeteran'] = $QAInfo['CBVeteran'];
                    $inArray['CBFiEthnicity'] = $QAInfo['CBFiEthnicity'];
                    $inArray['CBFiGender'] = $QAInfo['CBFiGender'];
                    $inArray['CBFiRace'] = $QAInfo['CBFiRace'];
                    $inArray['CBDDemoInfo'] = $QAInfo['CBDDemoInfo'];


                }
            }
            /* File - Attorney  Info  */

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /* File - SS/Buyer Info */
            if (array_key_exists($LMRID, $shortSaleInfo)) {
                $shortSale = $shortSaleInfo[$LMRID];
                if (count($shortSale) > 0) {
                    $listingDate = '';
                    $contractDate1 = '';
                    $closingDate1 = '';
                    $listingDate = trim($shortSale['listingDate']);
                    $contractDate1 = trim($shortSale['contractDate1']);
                    $closingDate1 = trim($shortSale['closingDate1']);

                    if (Dates::IsEmpty($listingDate)) {
                        $listingDate = '';
                    } else {
                        $dtArray = explode('-', $listingDate);
                        $listingDate = date('m/d/y', mktime(0, 0, 0, $dtArray[1], $dtArray[2], $dtArray[0]));
                    }
                    if (Dates::IsEmpty($contractDate1)) {
                        $contractDate1 = '';
                    } else {
                        $dtArray1 = explode('-', $contractDate1);
                        $contractDate1 = date('m/d/y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }
                    if (Dates::IsEmpty($closingDate1)) {
                        $closingDate1 = '';
                    } else {
                        $dtArray1 = explode('-', $closingDate1);
                        $closingDate1 = date('m/d/y', mktime(0, 0, 0, $dtArray1[1], $dtArray1[2], $dtArray1[0]));
                    }


                    $inArray['listingAgent'] = trim($shortSale['realtor']);
                    $inArray['sellerName'] = trim($shortSale['buyer1RelToSeller']);
                    $inArray['listingPrice'] = trim($shortSale['listingPrice']);
                    $inArray['listingDate'] = $listingDate;
                    $inArray['realtorAddress'] = trim($shortSale['realtorAddress']);
                    $inArray['realtorPhone'] = Strings::formatPhoneNumber(trim($shortSale['realtorPhoneNumber']));
                    $inArray['realtorCell'] = Strings::formatPhoneNumber(trim($shortSale['sales1CellNo']));
                    $inArray['realtorFax'] = Strings::formatPhoneNumber(trim($shortSale['sales1Fax']));
                    $inArray['realtorEmail'] = trim($shortSale['realtorEmail']);
                    $inArray['titleCompany'] = trim($shortSale['titleCompany']);
                    $inArray['titleRepName'] = trim($shortSale['contact']);
                    $inArray['titleAddress'] = trim($shortSale['titleAddress']);
                    $inArray['titleCity'] = trim($shortSale['titleCity']);
                    $inArray['titleState'] = Strings::getStateFullName(Arrays::fetchStates(), trim($shortSale['titleState']));
                    $inArray['titleZip'] = trim($shortSale['titleZip']);

                    $inArray['sellerAttorneyFirstName'] = trim($shortSale['sellerAttorneyFirstName']);
                    $inArray['sellerAttorneyLastName'] = trim($shortSale['sellerAttorneyLastName']);
                    $inArray['sellerAttorneyFullName'] = $shortSale['sellerAttorneyFirstName'] . ' ' . $shortSale['sellerAttorneyLastName'];
                    $inArray['sellerAttorneyFirmName'] = trim($shortSale['sellerAttorneyFirmName']);
                    $inArray['sellerAttorneyPhone'] = Strings::formatPhoneNumber($shortSale['sellerAttorneyPhone']);
                    $inArray['sellerAttorneyEmail'] = trim($shortSale['sellerAttorneyEmail']);


                    if (array_key_exists('contactLastName', $shortSale)) {
                        $inArray['titleRepLastName'] = trim($shortSale['contactLastName']);
                    }

                    $inArray['titleCompanyPhoneNumber'] = Strings::formatPhoneNumber(trim($shortSale['titleCompanyPhoneNumber']));
                    $inArray['titleFaxNumber'] = Strings::formatPhoneNumber(trim($shortSale['sales2Fax']));
                    $inArray['titleCompanyEmail'] = trim($shortSale['titleCompanyEmail']);

                    if (array_key_exists('titleNotes', $shortSale)) {
                        $inArray['titleNotes'] = trim($shortSale['titleNotes']);
                    }

                    if (array_key_exists('titleAttorneyName', $shortSale)) {
                        $inArray['fileAttorneyName'] = trim($shortSale['titleAttorneyName']);
                    }
                    if (array_key_exists('titleAttorneyFirmName', $shortSale)) {
                        $inArray['fileAttorneyCompany'] = trim($shortSale['titleAttorneyFirmName']);
                    }
                    if (array_key_exists('titleAttorneyPhoneNumber', $shortSale)) {
                        $inArray['fileAttorneyPhone'] = Strings::formatPhoneNumber(trim($shortSale['titleAttorneyPhoneNumber']));
                    }
                    if (array_key_exists('titleAttorneyEmail', $shortSale)) {
                        $inArray['fileAttorneyEmail'] = trim($shortSale['titleAttorneyEmail']);
                    }
                    if (array_key_exists('titleAttorneyAddress', $shortSale)) {
                        $inArray['fileAttorneyAddress'] = trim($shortSale['titleAttorneyAddress']);
                    }
                    if (array_key_exists('titleAttorneyCity', $shortSale)) {
                        $inArray['fileAttorneyCity'] = trim($shortSale['titleAttorneyCity']);
                    }
                    if (array_key_exists('titleAttorneyState', $shortSale)) {
                        $inArray['fileAttorneyState'] = trim($shortSale['titleAttorneyState']);
                    }
                    if (array_key_exists('titleAttorneyStateLong', $shortSale)) {
                        $inArray['fileAttorneyStateLong'] = trim($shortSale['titleAttorneyStateLong']);
                    }
                    if (array_key_exists('titleAttorneyLName', $shortSale)) {
                        $inArray['fileAttorneyLName'] = trim($shortSale['titleAttorneyLName']);
                    }
                    if (array_key_exists('titleAttorneyCellNumber', $shortSale)) {
                        $inArray['fileAttorneyCell'] = trim($shortSale['titleAttorneyCellNumber']);
                    }
                    if (array_key_exists('titleAttorneyFaxNumber', $shortSale)) {
                        $inArray['fileAttorneyFax'] = trim($shortSale['titleAttorneyFaxNumber']);
                    }
                    if (array_key_exists('titleAttorneyTollFreeNo', $shortSale)) {
                        $inArray['fileAttorneyTollFree'] = trim($shortSale['titleAttorneyTollFreeNo']);
                    }
                    if (array_key_exists('titleAttorneyZip', $shortSale)) {
                        $inArray['fileAttorneyZip'] = trim($shortSale['titleAttorneyZip']);
                    }
                    if (array_key_exists('titleAttorneyLicenseNo', $shortSale)) {
                        $inArray['fileAttorneyLicenseNo'] = trim($shortSale['titleAttorneyLicenseNo']);
                    }
                    if (array_key_exists('titleAttorneyWebsite', $shortSale)) {
                        $inArray['fileAttorneyWebsite'] = trim($shortSale['titleAttorneyWebsite']);
                    }
                    if (array_key_exists('titleAttorneyBarNo', $shortSale)) {
                        $inArray['fileAttorneyBarNo'] = trim($shortSale['titleAttorneyBarNo']);
                    }
                    if (array_key_exists('titleAttorneyStateOfFormation', $shortSale)) {
                        $inArray['fileAttorneyStateOfFormation'] = trim($shortSale['titleAttorneyStateOfFormation']);
                    }
                    if (array_key_exists('titleAttorneyStateOfFormationLg', $shortSale)) {
                        $inArray['fileAttorneyStateOfFormationLg'] = trim($shortSale['titleAttorneyStateOfFormationLg']);
                    }
                    if (array_key_exists('titleAttorneyEntityType', $shortSale)) {
                        $inArray['fileAttorneyEntityType'] = trim($shortSale['titleAttorneyEntityType']);
                    }
                    if (array_key_exists('titleAttorneyDescription', $shortSale)) {
                        $inArray['fileAttorneyDescription'] = trim($shortSale['titleAttorneyDescription']);
                    }


                    if (isset($shortSale['serviceLenderName'])) $inArray['serviceLenderName'] = trim($shortSale['serviceLenderName']);
                    if (isset($shortSale['lenderLastName'])) $inArray['lenderLastName'] = trim($shortSale['lenderLastName']);
                    $inArray['lenderFullName'] = $inArray['serviceLenderName'] . ' ' . $inArray['lenderLastName'];
                    if (isset($shortSale['lenderFirmName'])) $inArray['lenderFirmName'] = trim($shortSale['lenderFirmName']);
                    if (isset($shortSale['lenderPhone'])) $inArray['lenderPhone'] = Strings::formatPhoneNumber(trim($shortSale['lenderPhone']));
                    if (isset($shortSale['lenderEmail'])) $inArray['lenderEmail'] = trim($shortSale['lenderEmail']);
                    if (isset($shortSale['lenderCell'])) $inArray['lenderCell'] = Strings::formatPhoneNumber(trim($shortSale['lenderCell']));
                    if (isset($shortSale['lenderFax'])) $inArray['lenderFax'] = Strings::formatPhoneNumber(trim($shortSale['lenderFax']));
                    if (isset($shortSale['serviceLenderAddress'])) $inArray['serviceLenderAddress'] = trim($shortSale['serviceLenderAddress']);
                    if (isset($shortSale['serviceLenderCity'])) $inArray['serviceLenderCity'] = trim($shortSale['serviceLenderCity']);
                    if (isset($shortSale['serviceLenderState'])) $inArray['serviceLenderState'] = trim($shortSale['serviceLenderState']);
                    if (isset($shortSale['serviceLenderState'])) $inArray['serviceLenderStateName'] = Strings::getStateFullName(Arrays::fetchStates(), trim($shortSale['serviceLenderState']));
                    if (isset($shortSale['serviceLenderZip'])) $inArray['serviceLenderZip'] = trim($shortSale['serviceLenderZip']);
                    if (isset($shortSale['lendertollFree'])) $inArray['lendertollFree'] = Strings::formatPhoneNumber(trim($shortSale['lendertollFree']));
                    if (isset($shortSale['serviceLenderEIN'])) $inArray['serviceLenderEIN'] = trim($shortSale['serviceLenderEIN']);
                    if (isset($shortSale['serviceLenderRepTitle'])) $inArray['serviceLenderRepTitle'] = trim($shortSale['serviceLenderRepTitle']);
                    if (isset($shortSale['lenderstateOfFormation'])) $inArray['lenderstateOfFormation'] = trim($shortSale['lenderstateOfFormation']);
                    if (isset($shortSale['lenderentityType'])) {
                        $inArray['lenderentityType'] = trim($shortSale['lenderentityType']);
                        $inArray['lenderEntityTypeLong'] = getEntityTypeLongName::getEntityName(trim($shortSale['lenderentityType']));
                    }
                    if (isset($shortSale['serviceLenderStateLong'])) $inArray['serviceLenderStateLong'] = trim($shortSale['serviceLenderStateLong']);
                    if (isset($shortSale['lenderstateOfFormationLong'])) $inArray['lenderstateOfFormationLong'] = trim($shortSale['lenderstateOfFormationLong']);


                    if (isset($shortSale['servicerRepFirstName'])) $inArray['servicerRepFirstName'] = trim($shortSale['servicerRepFirstName']);
                    if (isset($shortSale['servicerRepLastName'])) $inArray['servicerRepLastName'] = trim($shortSale['servicerRepLastName']);
                    if (isset($shortSale['servicerCompanyName'])) $inArray['servicerCompanyName'] = trim($shortSale['servicerCompanyName']);
                    if (isset($shortSale['servicerPhone'])) $inArray['servicerPhone'] = Strings::formatPhoneNumber(trim($shortSale['servicerPhone']));
                    if (isset($shortSale['servicerEmail'])) $inArray['servicerEmail'] = trim($shortSale['servicerEmail']);
                    if (isset($shortSale['servicerAddress'])) $inArray['servicerAddress'] = trim($shortSale['servicerAddress']);
                    if (isset($shortSale['servicerCity'])) $inArray['servicerCity'] = trim($shortSale['servicerCity']);
                    if (isset($shortSale['servicerState'])) $inArray['servicerState'] = trim($shortSale['servicerState']);
                    if (isset($shortSale['servicerStateLong'])) $inArray['servicerStateLong'] = trim($shortSale['servicerStateLong']);
                    if (isset($shortSale['servicerZip'])) $inArray['servicerZip'] = trim($shortSale['servicerZip']);

                    if (isset($shortSale['servicingServicerRepFirstName'])) $inArray['servicingServicerFirstName'] = trim($shortSale['servicingServicerRepFirstName']);
                    if (isset($shortSale['servicingServicerRepLastName'])) $inArray['servicingServicerLastName'] = trim($shortSale['servicingServicerRepLastName']);
                    if (isset($shortSale['servicingServicerCompanyName'])) $inArray['servicingServicerCompanyName'] = trim($shortSale['servicingServicerCompanyName']);
                    if (isset($shortSale['servicingServicerPhone'])) $inArray['servicingServicerPhone'] = Strings::formatPhoneNumber(trim($shortSale['servicingServicerPhone']));
                    if (isset($shortSale['servicingServicerEmail'])) $inArray['servicingServicerEmail'] = trim($shortSale['servicingServicerEmail']);
                    if (isset($shortSale['servicingServicerAddress'])) $inArray['servicingServicerAddress'] = trim($shortSale['servicingServicerAddress']);
                    if (isset($shortSale['servicingServicerCity'])) $inArray['servicingServicerCity'] = trim($shortSale['servicingServicerCity']);
                    if (isset($shortSale['servicingServicerState'])) $inArray['servicingServicerState'] = trim($shortSale['servicingServicerState']);
                    if (isset($shortSale['servicingServicerZip'])) $inArray['servicingServicerZip'] = trim($shortSale['servicingServicerZip']);

                    if (isset($shortSale['trusteeName'])) $inArray['trusteeName'] = trim($shortSale['trusteeName']);
                    if (isset($shortSale['trusteeLName'])) $inArray['trusteeLName'] = trim($shortSale['trusteeLName']);
                    if (isset($shortSale['trusteeFirmName'])) $inArray['trusteeFirmName'] = trim($shortSale['trusteeFirmName']);
                    if (isset($shortSale['trusteePhone'])) $inArray['trusteePhone'] = Strings::formatPhoneNumber(trim($shortSale['trusteePhone']));
                    if (isset($shortSale['trusteetollFree'])) $inArray['trusteetollFree'] = Strings::formatPhoneNumber(trim($shortSale['trusteetollFree']));
                    if (isset($shortSale['trusteeFax'])) $inArray['trusteeFax'] = Strings::formatPhoneNumber(trim($shortSale['trusteeFax']));
                    if (isset($shortSale['trusteeCellNo'])) $inArray['trusteeCellNo'] = Strings::formatPhoneNumber(trim($shortSale['trusteeCellNo']));
                    if (isset($shortSale['trusteeEmail'])) $inArray['trusteeEmail'] = trim($shortSale['trusteeEmail']);
                    if (isset($shortSale['trusteeAddress'])) $inArray['trusteeAddress'] = trim($shortSale['trusteeAddress']);
                    if (isset($shortSale['trusteeCity'])) $inArray['trusteeCity'] = trim($shortSale['trusteeCity']);
                    if (isset($shortSale['trusteeState'])) $inArray['trusteeState'] = trim($shortSale['trusteeState']);
                    if (isset($shortSale['trusteeZip'])) $inArray['trusteeZip'] = trim($shortSale['trusteeZip']);
                    if (isset($shortSale['trusteeStateOfFormation'])) $inArray['trusteeStateOfFormation'] = trim($shortSale['trusteeStateOfFormation']);
                    if (isset($shortSale['trusteeEntityType'])) $inArray['trusteeEntityType'] = trim($shortSale['trusteeEntityType']);

                    if (isset($shortSale['trusteeStateLong'])) $inArray['trusteeStateLong'] = trim($shortSale['trusteeStateLong']);
                    if (isset($shortSale['trusteeStateOfFormationLong'])) $inArray['trusteeStateOfFormationLong'] = trim($shortSale['trusteeStateOfFormationLong']);


                    $inArray['buyerName'] = trim($shortSale['buyerName1']);
                    $inArray['coBuyerName1'] = trim($shortSale['coBuyerName1']);
                    $inArray['firstBuyerPhone'] = Strings::formatPhoneNumber(trim($shortSale['firstBuyerPhone']));
                    $inArray['firstBuyerEmail'] = trim($shortSale['firstBuyerEmail']);
                    $inArray['offer1'] = trim($shortSale['offer1']);
                    $inArray['sqft1'] = trim($shortSale['sqft1']);
                    $inArray['contractDate1'] = $contractDate1;
                    $inArray['closingDate1'] = $closingDate1;
                    $inArray['buyer1AgentName'] = trim($shortSale['buyer1AgentName']);
                    $inArray['buyer1AgencyName'] = trim($shortSale['buyer1AgencyName']);
                    $inArray['buyer1Cell'] = Strings::formatPhoneNumber(trim($shortSale['buyer1Cell']));
                    $inArray['buyer1Fax'] = Strings::formatPhoneNumber(trim($shortSale['buyer1Fax']));
                    $inArray['buyer1Email'] = trim($shortSale['buyer1Email']);
                    $inArray['buyer1LOName'] = trim($shortSale['buyer1LOName']);
                    $inArray['buyer1LOCompany'] = trim($shortSale['buyer1LOCompany']);
                    $inArray['buyer1LOPhone'] = Strings::formatPhoneNumber(trim($shortSale['buyer1LOPhone']));
                    $inArray['buyer1LOFax'] = Strings::formatPhoneNumber(trim($shortSale['buyer1LOFax']));
                    $inArray['buyer1LOCell'] = Strings::formatPhoneNumber(trim($shortSale['buyer1LOCell']));
                    $inArray['buyer1LOEmail'] = trim($shortSale['buyer1LOEmail']);
                    $inArray['financingType1'] = trim($shortSale['buyer1Deal']);


                    $inArray['updateEmailStatus'] = trim($shortSale['updateEmailStatus']);
                    $inArray['offerAmount'] = trim($shortSale['offerAmount']);
                    $inArray['currentLenderValue'] = trim($shortSale['currentLenderValue']);
                    $inArray['valueExpires'] = trim($shortSale['valueExpires']);
                    $inArray['nextStage'] = trim($shortSale['nextStage']);
                    $inArray['updateEmailComments'] = trim($shortSale['updateEmailComments']);

                    $inArray['updateEmailBuyerName'] = trim($shortSale['updateEmailBuyerName']);
                    $inArray['costBasis'] = Currency::formatDollarAmountWithDecimal(trim($shortSale['costBasis']));
                    $inArray['assessedValue'] = Currency::formatDollarAmountWithDecimal(trim($shortSale['assessedValue']));
                    $inArray['costSpent'] = Currency::formatDollarAmountWithDecimal(trim($shortSale['costSpent']));
                    $inArray['appraisalNotes'] = trim($shortSale['appraisalNotes']);

                    $costSpent = 0;
                    $costSpent = trim($shortSale['costSpent']);

                    $contractDate = '';
                    $foreclosureDate = '';
                    $contractDate = trim($shortSale['contractDate']);
                    $foreclosureDate = trim($shortSale['foreclosureDate']);

                    if (Dates::IsEmpty($contractDate)) {
                        $contractDate = '';
                    } else {
                        $contractDate = Dates::formatDateWithRE($contractDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($foreclosureDate)) {
                        $foreclosureDate = '';
                    } else {
                        $foreclosureDate = Dates::formatDateWithRE($foreclosureDate, 'YMD', 'm/d/Y');
                    }

                    $inArray['contractDate'] = $contractDate;
                    $inArray['foreclosureDate'] = $foreclosureDate;

                    /**
                     * Added Appraiser Info Pivotal # ********* by Suresh K
                     */
                    for ($appr = 1; $appr <= 2; $appr++) {
                        if (array_key_exists('appraiser' . $appr . 'Company', $shortSale)) $inArray['appraiser' . $appr . 'Company'] = trim($shortSale['appraiser' . $appr . 'Company']);
                        if (array_key_exists('appraiser' . $appr . 'FirstName', $shortSale)) $inArray['appraiser' . $appr . 'FirstName'] = trim($shortSale['appraiser' . $appr . 'FirstName']);
                        if (array_key_exists('appraiser' . $appr . 'LastName', $shortSale)) $inArray['appraiser' . $appr . 'LastName'] = trim($shortSale['appraiser' . $appr . 'LastName']);
                        if (array_key_exists('appraiser' . $appr . 'Phone', $shortSale)) $inArray['appraiser' . $appr . 'Phone'] = Strings::formatPhoneNumber(trim($shortSale['appraiser' . $appr . 'Phone']));
                        if (array_key_exists('appraiser' . $appr . 'Email', $shortSale)) $inArray['appraiser' . $appr . 'Email'] = trim($shortSale['appraiser' . $appr . 'Email']);
                        if (array_key_exists('appraiser' . $appr . 'Value', $shortSale)) $inArray['appraiser' . $appr . 'AsIsValue'] = Currency::formatDollarAmountWithDecimal($shortSale['appraiser' . $appr . 'Value']);
                    }
                    $inArray['appraiser1RehabbedValue'] = Currency::formatDollarAmountWithDecimal($shortSale['rehabValue']);
                    $inArray['appraiser2RehabbedValue'] = Currency::formatDollarAmountWithDecimal($shortSale['rehabValue2']);
                    $inArray['appraiser1DateObtained'] = Dates::formatDateWithRE(trim($shortSale['dateObtained']), 'YMD', 'm/d/Y');
                    $inArray['appraiser2DateObtained'] = Dates::formatDateWithRE(trim($shortSale['dateObtained2']), 'YMD', 'm/d/Y');
                    /**
                     * Added BPO Info Pivotal # ********* by Suresh K
                     */
                    for ($bpo = 1; $bpo <= 3; $bpo++) {
                        if (array_key_exists('realtor' . $bpo . 'Company', $shortSale)) $inArray['BPORealtor' . $bpo . 'Company'] = trim($shortSale['realtor' . $bpo . 'Company']);
                        if (array_key_exists('realtor' . $bpo . 'FirstName', $shortSale)) $inArray['BPORealtor' . $bpo . 'FirstName'] = trim($shortSale['realtor' . $bpo . 'FirstName']);
                        if (array_key_exists('realtor' . $bpo . 'LastName', $shortSale)) $inArray['BPORealtor' . $bpo . 'LastName'] = trim($shortSale['realtor' . $bpo . 'LastName']);
                        if (array_key_exists('realtor' . $bpo . 'Phone', $shortSale)) $inArray['BPORealtor' . $bpo . 'Phone'] = Strings::formatPhoneNumber(trim($shortSale['realtor' . $bpo . 'Phone']));
                        if (array_key_exists('realtor' . $bpo . 'Email', $shortSale)) $inArray['BPORealtor' . $bpo . 'Email'] = trim($shortSale['realtor' . $bpo . 'Email']);
                        if (array_key_exists('BPO' . $bpo . 'Value', $shortSale)) $inArray['BPORealtor' . $bpo . 'AsIsValue'] = Currency::formatDollarAmountWithDecimal($shortSale['BPO' . $bpo . 'Value']);
                    }
                    $inArray['BPORealtor1RehabbedValue'] = Currency::formatDollarAmountWithDecimal($shortSale['rehabValue3']);
                    $inArray['BPORealtor2RehabbedValue'] = Currency::formatDollarAmountWithDecimal($shortSale['rehabValue4']);
                    $inArray['BPORealtor3RehabbedValue'] = Currency::formatDollarAmountWithDecimal($shortSale['rehabValue5']);
                    $inArray['BPORealtor1DateObtained'] = Dates::formatDateWithRE(trim($shortSale['dateObtained3']), 'YMD', 'm/d/Y');
                    $inArray['BPORealtor2DateObtained'] = Dates::formatDateWithRE(trim($shortSale['dateObtained4']), 'YMD', 'm/d/Y');
                    $inArray['BPORealtor3DateObtained'] = Dates::formatDateWithRE(trim($shortSale['dateObtained5']), 'YMD', 'm/d/Y');
                    $inArray['BPORealtor1RentValue'] = Currency::formatDollarAmountWithDecimal($shortSale['twelveMonthRent']);
                    $inArray['BPORealtor2RentValue'] = Currency::formatDollarAmountWithDecimal($shortSale['twelveMonthRent1']);
                    $inArray['BPORealtor3RentValue'] = Currency::formatDollarAmountWithDecimal($shortSale['twelveMonthRent2']);
                    /**
                     * Added AVM Info Pivotal # ********* by Suresh K
                     */
                    for ($avm = 1; $avm <= 3; $avm++) {
                        $inArray['AVM' . $avm . 'Company'] = trim($shortSale['AVM' . $avm . 'Company']);
                        $inArray['AVM' . $avm . 'AsIsValue'] = Currency::formatDollarAmountWithDecimal($shortSale['AVM' . $avm . 'Value']);
                    }

                    if (array_key_exists('insuranceRepFirstName', $shortSale)) $inArray['insuranceRepFirstName'] = trim($shortSale['insuranceRepFirstName']);
                    if (array_key_exists('insuranceRepLastName', $shortSale)) $inArray['insuranceRepLastName'] = trim($shortSale['insuranceRepLastName']);
                    if (array_key_exists('nameOfCarrier', $shortSale)) $inArray['nameOfCarrier'] = trim($shortSale['nameOfCarrier']);
                    if (array_key_exists('insuranceRepOfficePhone', $shortSale)) $inArray['insuranceRepOfficePhone'] = Strings::formatPhoneNumber(trim($shortSale['insuranceRepOfficePhone']));
                    if (array_key_exists('insuranceRepEmail', $shortSale)) $inArray['insuranceRepEmail'] = trim($shortSale['insuranceRepEmail']);
                    if (array_key_exists('insuranceRepCell', $shortSale)) $inArray['insuranceRepCell'] = Strings::formatPhoneNumber(trim($shortSale['insuranceRepCell']));
                    if (array_key_exists('insuranceRepFax', $shortSale)) $inArray['insuranceRepFax'] = Strings::formatPhoneNumber(trim($shortSale['insuranceRepFax']));
                    if (array_key_exists('insuranceRepTollFree', $shortSale)) $inArray['insuranceRepTollFree'] = Strings::formatPhoneNumber(trim($shortSale['insuranceRepTollFree']));
                    if (array_key_exists('propertyInsuranceNotes', $shortSale)) $inArray['propertyInsuranceNotes'] = trim($shortSale['propertyInsuranceNotes']);
                    if (array_key_exists('insuranceRepWebsite', $shortSale)) $inArray['insuranceRepWebsite'] = trim($shortSale['insuranceRepWebsite']);
                    if (array_key_exists('insuranceRepAddress', $shortSale)) $inArray['insuranceRepAddress'] = trim($shortSale['insuranceRepAddress']);
                    if (array_key_exists('insuranceRepCity', $shortSale)) $inArray['insuranceRepCity'] = trim($shortSale['insuranceRepCity']);
                    if (array_key_exists('insuranceRepState', $shortSale)) $inArray['insuranceRepState'] = trim($shortSale['insuranceRepState']);
                    if (array_key_exists('insuranceRepZip', $shortSale)) $inArray['insuranceRepZip'] = trim($shortSale['insuranceRepZip']);
                }
            }
            /* File - SS/Buyer Info */

            //Contractor Info
            if (array_key_exists($LMRId, $contractorInfoArray)) {
                $contractorInfo = $contractorInfoArray[$LMRID];
                if (count($contractorInfo)) {
                    $inArray['contractorCompanyName'] = $contractorInfo['companyName'];
                    $inArray['contractorMailingStreet'] = $contractorInfo['address'];
                    $inArray['contractorMailingCity'] = $contractorInfo['city'];
                    $inArray['contractorMailingState'] = $contractorInfo['state'] ? Strings::convertState($contractorInfo['state']) : '';
                    $inArray['contractorMailingZip'] = $contractorInfo['zip'];
                    $inArray['contractorPhone'] = Strings::formatPhoneNumber(trim($contractorInfo['phone']));
                }
            }

            /* LM Proposal Info */
            if (array_key_exists($LMRID, $proposalInfo)) {
                $proposalInfoArray = $proposalInfo[$LMRID];
            }

            $taxImpoundsFee = '';
            $isEF = 0;

            $haveInterestreserve = 0;
            $insImpoundsMonthAmt = $totalHousingTaxesInsurance = $proposalTotalHousingTaxesInsurance = $newPaymentInterest = $annualPremium = $acquisitionPriceFinanced = 0;
            $totalProjectCost = $ARV = $acquisitionLTV = $marketLTV = $LTC = $perRehabCostFinanced = 0;
            $totalCashToClose = 0;
            $totalRequiredReserves = 0;
            $totalMonthlyPaymentAmt = 0;
            $totalLoanAmount = 0;
            $originationPointsRate = 0;
            $originationPointsValue = 0;
            $brokerPointsRate = 0;
            $brokerPointsValue = 0;
            $applicationFee = 0;
            $appraisalFee = 0;
            $estdTitleClosingFee = 0;
            $processingFee = 0;
            $underwritingFees = 0;
            $drawsSetUpFee = 0;
            $drawsFee = 0;
            $valuationBPOFee = 0;
            $valuationAVMFee = 0;
            $creditReportFee = 0;
            $backgroundCheckFee = 0;
            $taxServiceFee = 0;
            $documentPreparationFee = 0;
            $wireFee = 0;
            $servicingSetUpFee = 0;
            $floodCertificateFee = 0;
            $floodServiceFee = 0;
            $inspectionFees = 0;
            $projectFeasibility = 0;
            $dueDiligence = $totalAssetsOwed = 0;
            $UccLienSearch = 0;
            $thirdPartyFees = 0;
            $otherFee = 0;
            $totalFeesAndCost = 0;
            $insImpoundsFee = 0;
            $extensionOptionPercentage = '';
            $rehabCostFinanced = 0;
            $isLoanPaymentAmt = $netMonthlyPayment = 0;
            $Payment = 0;
            $survey = 0;
            $wholeSaleAdminFee = 0;
            $earnestDeposit = 0;
            $currentLoanBalance = 0;
            $initialLoanAmount = 0;
            $prepaidInterestReserve = 0;
            $closingCostFinanced = 0;
            $availableBudget = 0;
            $brokerProcessingFee = 0;
            $totalLoanAmountWithOutComma = '0';

            $activeTab = 'BO';
            $totalAssetsNetValue = 0;
            $totalAssets = 0;
            $primTotalNetHouseHoldIncome = 0;
            $coTotalNetHouseHoldIncome = 0;
            $primTotalHouseHoldExpenses = 0;
            $coTotalHouseHoldExpenses = 0;

            $filepaydownInfo = [];
            if (array_key_exists($LMRID, $paydownInfoArray)) {
                $filepaydownInfo = $paydownInfoArray[$LMRID];
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }

            /*  HMLO Property Info */

            if (array_key_exists($LMRID, $HMLOPropInfoArray ?? [])) {
                $HMLOPropInfo = $HMLOPropInfoArray[$LMRID];
                if (count($HMLOPropInfo) > 0) {
                    $proInsType = '';
                    $proInsIDsArray = '';
                    $maturityDate = '';
                    $fundingDate = '';
                    $dateNoteSigned = '';
                    $recordingDate = '';
                    $payOffDate = '';
                    $paymentDue = '';
                    $lenderLossPayableEndorsementInfo = '';
                    $servicingNumber = '';
                    $servicingSubStatus = '';
                    $defaultInterestRate = '';
                    $defaultInterestRateText = '';
                    $f = '';
                    $g = '';

                    //	$triggeredByDays= ''; $latePayemntAppliedOn= ''; $lateChargeAmt= ''; $minLateFeeAmt= ''; $lenderNotes = '';  $loanSaleDate = ''; $masterLoanSaleDate = ''; $expectForDueDiligence = '';
                    //$exitStrategy='';
                    $triggeredByDays = '';

                    $dateNoteSignedLong = '';
                    $loanSaleDateLong = '';
                    $latePayemntAppliedDateLong = '';
                    $recordingDateLong = '';
                    $latePayemntAppliedOn = '';
                    $lateChargeAmt = '';
                    $minLateFeeAmt = '';
                    $lateDue = '';
//pr($HMLOPropInfo); die;
                    $lenderNotes = '';
                    $loanSaleDate = '';
                    $masterLoanSaleDate = '';
                    $expectForDueDiligence = '';
                    $referringParty = '';
                    $docTypeLoanTerm = '';

                    $maturityDateLong = $maturityDateLongLong = $payOffDateLong = $payOffDateLongLong = '';

                    $maturityDate = trim($HMLOPropInfo['maturityDate']);
                    $fundingDate = trim($HMLOPropInfo['fundingDate']);
                    $dateNoteSigned = trim($HMLOPropInfo['dateNoteSigned']);
                    $recordingDate = trim($HMLOPropInfo['recordingDate']);
                    $payOffDate = trim($HMLOPropInfo['payOffDate']);
                    $loanSaleDate = trim($HMLOPropInfo['loanSaleDate']);
                    $masterLoanSaleDate = trim($HMLOPropInfo['masterLoanSaleDate']);
                    $docTypeLoanTerms = trim($HMLOPropInfo['docType']);
                    $proInsType = trim($HMLOPropInfo['proInsType']);  //comma separated
                    $proInsTypeArray = explode(',', $proInsType);
                    $proInsDecoder = [1 => 'General Liability', 2 => 'Fire/Hazard Structural', 3 => 'Flood', 4 => 'Windstorm', 5 => "Builder's Risk", 6 => 'Earthquake', 7 => 'Lava', 8 => 'Environmental', 9 => 'Other (See Notes)', 10 => 'None (Waiver Required)'];
                    for ($x = 0; $x <= count($proInsTypeArray); $x += 1) {
                        $proInsTypeArrayOutcome[$x] = $proInsDecoder[$proInsTypeArray[$x]];
                    }
                    $proInsTypeArrayOutcome = implode(', ', $proInsTypeArrayOutcome);
                    $requiredInsurance = rtrim($proInsTypeArrayOutcome, ', ');

                    $insuranceDetailsHTMLS = [];
                    $insuranceDetailsHTMLS[] = [
                        ['fieldColor' => '', 'fieldText' => 'Required Insurance Types'],
                        ['fieldColor' => '', 'fieldText' => 'Annual Premium'],
                    ];
                    $insuranceDetailsHTMLS[] = [
                        ['fieldColor' => '', 'fieldText' => $proInsTypeArrayOutcome],
                        ['fieldColor' => '', 'fieldText' => number_format(Strings::replaceCommaValues(trim($HMLOPropInfo['annualPremium'])), 2)],
                    ];
                    if ($googleDocs == 1) {
                        $inArray['tables'][] = [
                            'mergeTableTag'  => '##Required Insurance##',
                            'mergeTableData' => $insuranceDetailsHTMLS,
                        ];
                    } else {
                        $inArray['requiredInsurance'] = "<table><tr><td width='400px'>" . rtrim($proInsTypeArrayOutcome, ', ') . '</td><td>$ ' . number_format(Strings::replaceCommaValues(trim($HMLOPropInfo['annualPremium'])), 2) . '</td></table>';
                    }

                    $exitStrategy = trim($HMLOPropInfo['exitStrategy']);

                    $referringParty = trim($HMLOPropInfo['referringParty']);

                    if (Dates::IsEmpty($maturityDate)) {
                        $maturityDate = '';
                    } else {
                        $maturityDate = Dates::formatDateWithRE($maturityDate, 'YMD', 'm/d/Y');
                        $maturityDateLong = date('M d, Y', strtotime($maturityDate));
                        $maturityDateLongLong = date('F d, Y', strtotime($maturityDate));
                    }
                    if (Dates::IsEmpty($fundingDate)) {
                        $fundingDate = '';
                    } else {
                        $fundingDate = Dates::formatDateWithRE($fundingDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($dateNoteSigned)) {
                        $dateNoteSigned = '';
                    } else {
                        $dateNoteSigned = Dates::formatDateWithRE($dateNoteSigned, 'YMD', 'm/d/Y');
                        $dateNoteSignedLong = date('M d, Y', strtotime($dateNoteSigned));
                    }
                    if (Dates::IsEmpty($recordingDate)) {
                        $recordingDate = '';
                    } else {
                        $recordingDate = Dates::formatDateWithRE($recordingDate, 'YMD', 'm/d/Y');
                        $recordingDateLong = date('M d, Y', strtotime($recordingDate));
                    }
                    if (Dates::IsEmpty($payOffDate)) {
                        $payOffDate = '';
                    } else {
                        $payOffDate = Dates::formatDateWithRE($payOffDate, 'YMD', 'm/d/Y');
                        $payOffDateLong = date('M. d, Y', strtotime($payOffDate));
                        $payOffDateLongLong = date('F d, Y', strtotime($payOffDate));
                    }
                    if (Dates::IsEmpty($loanSaleDate)) {
                        $loanSaleDate = '';
                    } else {
                        $loanSaleDate = Dates::formatDateWithRE($loanSaleDate, 'YMD', 'm/d/Y');
                        $loanSaleDateLong = date('M d, Y', strtotime($loanSaleDate));
                    }
                    if (Dates::IsEmpty($masterLoanSaleDate)) {
                        $masterLoanSaleDate = '';
                    } else {
                        $masterLoanSaleDate = Dates::formatDateWithRE($masterLoanSaleDate, 'YMD', 'm/d/Y');
                    }


                    $inArray['annualPremiumProp'] = round(Strings::replaceCommaValues(trim($HMLOPropInfo['annualPremium'])), 2);
                    $inArray['typeOfHMLOLoanRequesting'] = trim($HMLOPropInfo['typeOfHMLOLoanRequesting']);
                    $inArray['maxAmtToPutDown'] = Currency::formatDollarAmountWithDecimal(trim($HMLOPropInfo['maxAmtToPutDown']));
                    $inArray['exitStrategy'] = trim($HMLOPropInfo['exitStrategy']);
                    $inArray['HMLOEstateHeldIn'] = trim($HMLOPropInfo['HMLOEstateHeldIn']);
                    $inArray['propertyNeedRehab'] = trim($HMLOPropInfo['propertyNeedRehab']);
                    $inArray['loanTerm'] = trim($HMLOPropInfo['loanTerm']);
                    $inArray['paymentDue'] = trim($HMLOPropInfo['paymentDue']);
                    $inArray['lenderLossPayableEndorsementIn'] = trim($HMLOPropInfo['lenderLossPayableEndorsementInfo']);
                    $inArray['servicingLenderLossPayableInfo'] = trim($HMLOPropInfo['lenderLossPayableEndorsementInfo']);
                    $inArray['servicingNumber'] = trim($HMLOPropInfo['servicingNumber']);
                    $inArray['servicingSubStatus'] = trim($HMLOPropInfo['servicingSubStatus']);
                    $inArray['defaultInterestRate'] = trim($HMLOPropInfo['defaultInterestRate']);
                    $f = new NumberFormatter('en', NumberFormatter::SPELLOUT);
                    $newval = $f->format(Strings::replaceCommaValues($HMLOPropInfo['defaultInterestRate']));
                    $inArray['defaultInterestRateText'] = $newval . ' percent';
                    //used numberformatter because it handles decimals not as cents!, other function in util.php does not.
                    $g = new NumberFormatter('en', NumberFormatter::SPELLOUT);
                    $newvalg = $g->format(floatval($inArray['lien1Rate']));
                    $inArray['lien1InterestRateText'] = $newvalg . ' percent';
                    $inArray['lien1InterestRateTextPerAnnum'] = Currency::percentage_to_text(floatval($inArray['lien1Rate']));
                    $inArray['triggeredByDays'] = trim($HMLOPropInfo['triggeredByDays']);
                    $inArray['lateChargeAmt'] = trim($HMLOPropInfo['lateChargeAmt']);
                    $inArray['minLateFeeAmt'] = Currency::formatDollarAmountWithDecimal(trim($HMLOPropInfo['minLateFeeAmt']));
                    $inArray['lenderNotes'] = trim($HMLOPropInfo['lenderNotes']);
                    $inArray['expectForDueDiligence'] = urldecode(trim($HMLOPropInfo['expectForDueDiligence']));
                    $inArray['maturityDate'] = $maturityDate;
                    $inArray['fundingDate'] = $fundingDate;
                    $inArray['maturityDateLong'] = $maturityDateLong;
                    $inArray['maturityDateLongLong'] = $maturityDateLongLong;
                    $inArray['dateNoteSigned'] = $dateNoteSigned;
                    $inArray['recordingDate'] = $recordingDate;
                    $inArray['payOffDate'] = $payOffDate;
                    $inArray['payOffDateLong'] = $payOffDateLong;
                    $inArray['payOffDateLongLong'] = $payOffDateLongLong;
                    $inArray['loanSaleDate'] = $loanSaleDate;
                    $inArray['masterLoanSaleDate'] = $masterLoanSaleDate;
                    $inArray['referringParty'] = $referringParty; //2019-02-04
                    if (array_key_exists($HMLOPropInfo['lienPosition'], $glHMLOLienPosition)) {
                        $inArray['lienPosition'] = trim($glHMLOLienPosition[$HMLOPropInfo['lienPosition']]);
                    }
                    $inArray['exitStrategy'] = trim($exitStrategy);
                    $inArray['typeOfPurchase'] = $HMLOPropInfo['typeOfSale'];
                    $inArray['desiredFundingAmount'] = Currency::formatDollarAmountWithDecimal($HMLOPropInfo['desiredFundingAmount']);
                    $inArray['useOfFunds'] = $HMLOPropInfo['useOfFunds'];
                    $inArray['haveCurrentLoanBal'] = $HMLOPropInfo['haveCurrentLoanBal'];
                    $inArray['balance'] = Currency::formatDollarAmountWithDecimal($HMLOPropInfo['balance']);
                    $inArray['heldWith'] = $HMLOPropInfo['heldWith'];
                    $inArray['haveInvoiceToFactor'] = $HMLOPropInfo['haveInvoiceToFactor'];
                    $inArray['amount'] = Currency::formatDollarAmountWithDecimal($HMLOPropInfo['amount']);
                    $inArray['securityInstrument'] = $HMLOPropInfo['securityInstrument'];
                    $inArray['isThisGroundUpConstruction'] = $HMLOPropInfo['isThisGroundUpConstruction'];

                    $inArray['dateNoteSignedLong'] = $dateNoteSignedLong;
                    $inArray['loanSaleDateLong'] = $loanSaleDateLong;
                    $inArray['latePayemntAppliedDateLong'] = $latePayemntAppliedDateLong;
                    $inArray['recordingDateLong'] = $recordingDateLong;
                    $inArray['docTypeLoanTerms'] = $docTypeLoanTerms;
                    $inArray['spread'] = $HMLOPropInfo['spread'];
                    $inArray['rateIndex'] = trim($HMLOPropInfo['rateIndex'], ',');
                    $inArray['involvedPurchase'] = $HMLOPropInfo['involvedPurchase'];
                    $inArray['wholesaleFee'] = $HMLOPropInfo['wholesaleFee'];
                }
            }
            $accrualType = accrualTypes::ACCRUAL_TYPE_30_360;
            $accrualType = $fileHMLOPropertyInfo['accrualType'];
            if ($accrualType == '') $accrualType = accrualTypes::ACCRUAL_TYPE_30_360;

            require CONST_BO_PATH . 'incExpCalculation.php';
            /** Income & expenses calculation for a file **/
            require CONST_BO_PATH . 'proposalCalculation.php';
            /** Income & expenses calculation for a file **/
            require CONST_BO_PATH . 'HMLOLoanInfoCalcForSmartTag.php';
            /** HMLO Loan Info Calculation for a file **/

            $fileHMLOPropertyInfo = $fileHMLOPropertyInfo[$LMRID];
            $inArray['assetTotalNetValue'] = '$ ' . Integers::formatInputToInt(trim($totalAssetsNetValue));
            $inArray['TotalEstValue'] = '$ ' . Integers::formatInputToInt(trim($totalAssets));
            $inArray['totalRealEstateValue'] = '$ ' . Integers::formatInputToInt(trim($totalRealEstateValue));
            $inArray['primTotalNetHouseHoldIncome'] = $primTotalNetHouseHoldIncome;
            $inArray['coTotalNetHouseHoldIncome'] = $coTotalNetHouseHoldIncome;
            $inArray['subTotalNetHouseHoldIncome'] = $totalHouseHoldIncome;
            $inArray['primTotalHouseHoldExpenses'] = $primTotalHouseHoldExpenses;
            $inArray['coTotalHouseHoldExpenses'] = $coTotalHouseHoldExpenses;
            $inArray['subTotalHouseHoldExpenses'] = $totalHouseHoldExpenses;
            $inArray['totalHouseHoldIncome'] = $totalHouseHoldIncome;
            $inArray['totalHouseHoldExpenses'] = $totalHouseHoldExpenses;
            if ($isHMLO == 1) { /* Calculate separate DTI for HMLO */
                $lien1DTI = proposalFormula::calculateDTIForHMLO($totalHouseHoldIncome, $totalHouseHoldExpenses);
            } else {
                $lien1DTI = proposalFormula::calculateDTI($tempLien1PaymentPITIA, 0, $totalGrossMonthlyHouseHoldIncome);
            }

            $inArray['lien1DTI'] = $lien1DTI;
            $inArray['lien1ProposalDTI'] = $lien1ProposalDTI;
            $inArray['lien1ProposalBalance'] = $lien1ProposalBalance;
            $inArray['lien2ProposalBalance'] = $lien2ProposalBalance;
            $inArray['lien1ProposalTerms'] = $lien1ProposalTerms;
            $inArray['lien2ProposalTerms'] = $lien2ProposalTerms;
            $inArray['lien1ProposalRate'] = $lien1ProposalRate;
            $inArray['lien2ProposalRate'] = $lien2ProposalRate;
            $inArray['lien1ProposalPrincipalReductionAmt'] = $lien1ProposalPrincipalReductionAmt;
            $inArray['lien2ProposalPrincipalReductionAmt'] = $lien2ProposalPrincipalReductionAmt;
            $inArray['totalHousingTaxesInsurance'] = $totalHousingTaxesInsurance;
            $inArray['proposalTotalHousingTaxesInsurance'] = $proposalTotalHousingTaxesInsurance;

            $inArray['backEndDTI'] = $backEndDTI;
            $inArray['proposalBackEndDTI'] = $proposalBackEndDTI;
            $inArray['lien1ProposalPaymentPITIA'] = Currency::formatDollarAmountWithDecimal($newPaymentInterest, 4);
            $inArray['lien2ProposalPaymentPITIA'] = Currency::formatDollarAmountWithDecimal($lien2ProposalPaymentPITIA, 4);
            $inArray['insuranceRepAnnualPremium'] = Currency::formatDollarAmountWithDecimal($annualPremium);
            $inArray['insuranceRepAnnualFlood'] = $floodInsurance1;

            /* LM Proposal Info */


            if ($typeOfHMLOLoanRequesting == 'Transactional' || $typeOfHMLOLoanRequesting == 'Commercial Purchase' || $typeOfHMLOLoanRequesting == 'Purchase') {
                $inArray['acquisitionPriceFinanced'] = Currency::formatDollarAmountWithDecimal($acquisitionPriceFinanced);
                $inArray['acquisitionPriceFinancedInText'] = Strings::makewords(Strings::replaceCommaValues(trim($acquisitionPriceFinanced)));
            } else {
                $inArray['acquisitionPriceFinanced'] = Currency::formatDollarAmountWithDecimal($CORTotalLoanAmt);
                $inArray['acquisitionPriceFinancedInText'] = Strings::makewords(Strings::replaceCommaValues(trim($CORTotalLoanAmt)));
            }
            /*          pr($inArray['acquisitionPriceFinanced']);
                      pr($initialLoanAmount);
          exit;*/
            $inArray['totalProjectCost'] = Currency::formatDollarAmountWithDecimal($totalProjectCost);
            $inArray['ARV'] = $ARV;
            $inArray['acquisitionLTV'] = $acquisitionLTV;
            $inArray['marketLTV'] = $marketLTV;
            $inArray['LTC'] = $LTC;
            $inArray['perRehabCostFinanced'] = Currency::formatDollarAmountWithDecimal($perRehabCostFinanced);
            $inArray['totalCashToClose'] = Currency::formatDollarAmountWithDecimal($totalCashToClose);
            $inArray['totalMonthlyPaymentAmt'] = $totalMonthlyPayment;
            $inArray['totalLoanAmount'] = Currency::formatDollarAmountWithDecimal($totalLoanAmount);  //add commas, 2 decimals
            $inArray['totalLoanAmountNoCents'] = Currency::formatDollarAmountWithDecimal($totalLoanAmount, 0);  //add commas, 0 decimals
            $totalLoanAmountWithOutComma = str_replace(',', '', $totalLoanAmount);
            $totalLoanAmountWithOutCommaNoCents = round(floatval(str_replace(',', '', $totalLoanAmount)), 0);
            $inArray['totalLoanAmountInText'] = Strings::makewords(floatval($totalLoanAmountWithOutComma));
            $inArray['totalLoanAmountInTextNoCents'] = Strings::makewords($totalLoanAmountWithOutCommaNoCents, 0);

            $inArray['paymentReservesAmt'] = Currency::formatDollarAmountWithDecimal($paymentReservesAmt);

            $inArray['requiredConstructionAmt'] = Currency::formatDollarAmountWithDecimal($requiredConstructionAmt);
            $inArray['contingencyReserveAmt'] = Currency::formatDollarAmountWithDecimal($contingencyReserveAmt);
            $inArray['contingencyReserve'] = $contingencyReserve;
            $inArray['totalRequiredReserves'] = Currency::formatDollarAmountWithDecimal($totalRequiredReserves);
            if ($haveInterestreserve == 'No' || $haveInterestreserve == '') {
                $prepaidInterestReserve = 0;
            }
            $inArray['prepaidInterestReserve'] = Currency::formatDollarAmountWithDecimal(trim($prepaidInterestReserve));
            $inArray['initialLoanAmount'] = $initialLoanAmount;
            $inArray['earnestDeposit'] = Currency::formatDollarAmountWithDecimal(round(Strings::replaceCommaValues(trim($earnestDeposit)), 2));
            //get the required data for calculation and validation as per Guidelines
            $maxLTV = '';
            $loanPgm = '';
            $resultArray = $HMLOPCMinMaxLoanGuidelines = [];
            for ($ct = 0; $ct < count($LMRClientTypeInfo); $ct++) {
                $loanPgm = $LMRClientTypeInfo[$ct]['ClientType'];
            }

            if ($loanPgm != '') {
                $HMLOPCMinMaxLoanGuidelines = getPCMinMaxLoanGuidelines::getReport(['loanPgm' => $loanPgm, 'PCID' => $PCID, 'loanPurpose' => '']);
                $LMRInternalLoanGuidelines = getPCInternalLoanGuidelines::getReport(['loanPgm' => $LMRInternalLoanprogramsArray, 'PCID' => $PCID, 'loanPurpose' => $typeOfHMLOLoanRequesting]);
            }
            //Get the PC GuideLines Data
            if (count($HMLOPCMinMaxLoanGuidelines) > 0) {
                $maxLTV = $HMLOPCMinMaxLoanGuidelines['maxLTV'];
                $minLoanAmount = $HMLOPCMinMaxLoanGuidelines['minLoanAmount'];
                $maxLoanAmount = $HMLOPCMinMaxLoanGuidelines['maxLoanAmount'];
                $maxARV = $HMLOPCMinMaxLoanGuidelines['maxLTVAfterRehab'];
                $minRate = $HMLOPCMinMaxLoanGuidelines['minRate'];
                $maxRate = $HMLOPCMinMaxLoanGuidelines['maxRate'];
                $LGMaxLTC = $HMLOPCMinMaxLoanGuidelines['totalLTC'];
                $minMidFico = $HMLOPCMinMaxLoanGuidelines['minMidFico'];
                $maxMidFico = $HMLOPCMinMaxLoanGuidelines['maxMidFico'];
                $maxPropertyForGrndConst = $HMLOPCMinMaxLoanGuidelines['maxPropertyForGrndConst'];
                $minPropertyForGrndConst = $HMLOPCMinMaxLoanGuidelines['minPropertyForGrndConst'];
                $maxPropertyForFixFlop = $HMLOPCMinMaxLoanGuidelines['maxPropertyForFixFlop'];
                $minPropertyForFixFlop = $HMLOPCMinMaxLoanGuidelines['minPropertyForFixFlop'];
                $maxPoints = $HMLOPCMinMaxLoanGuidelines['maxPoints'];
                $minPoints = $HMLOPCMinMaxLoanGuidelines['minPoints'];
                $downPaymentPercent = $HMLOPCMinMaxLoanGuidelines['downPaymentPercentage'];
                $loanPgmDetails = urldecode($HMLOPCMinMaxLoanGuidelines['loanPgmDetails']);
                $reqForLoanProUnderwriting = urldecode($HMLOPCMinMaxLoanGuidelines['reqForLoanProUnderwriting']);
            }
            //Get the File Level data
            //HMLOLoanInfoCalcForSmartTag.php
            //HMLOLoanTermsCalculation.php
            $chkMidFico = $fileHMLOInfo['midFicoScore'];
            if ($chkMidFico == '') $chkMidFico = 0;
            $chkFixflipProp = $fileHMLOExperienceInfo['borNoOfREPropertiesCompleted'];
            if ($chkFixflipProp == '') $chkFixflipProp = 0;
            $chkGround = $fileHMLOExperienceInfo['borRehabPropCompleted'];
            if ($chkGround == '') $chkGround = 0;
            $chkOrgPoints = $originationPointsRate;
            if ($chkOrgPoints == '') $chkOrgPoints = 0;
            $chkDownPaymentPerc = $downPaymentPercentage;
            if ($chkDownPaymentPerc == '') $chkDownPaymentPerc = 0;
            $chkAcquisitionLTV = $acquisitionLTV;
            if ($chkAcquisitionLTV == '') $chkAcquisitionLTV = 0;

            $ExtGLWText = $IntGLWText = '';
            //Loan Amount
            if ($totalLoanAmount > 0) {
                //External Loan Amount LG
                if (floatval($minLoanAmount) > 0 && floatval($totalLoanAmount) < floatval($minLoanAmount)) {
                    $ExtGLWText .= "The Loan Amount Range Set In This Program's Guidelines Is Between $ " . Currency::formatDollarAmountWithDecimalZeros($minLoanAmount) . ' and $ ' . Currency::formatDollarAmountWithDecimalZeros($maxLoanAmount) . '. The Current Loan Amount is $ ' . Currency::formatDollarAmountWithDecimalZeros($totalLoanAmount) . '.';
                } elseif (floatval($maxLoanAmount) > 0 && floatval($totalLoanAmount) > floatval($maxLoanAmount)) {
                    $ExtGLWText .= "The Loan Amount Range Set In This Program's Guidelines Is Between $ " . Currency::formatDollarAmountWithDecimalZeros($minLoanAmount) . ' and $ ' . Currency::formatDollarAmountWithDecimalZeros($maxLoanAmount) . '. The Current Loan Amount is $ ' . Currency::formatDollarAmountWithDecimalZeros($totalLoanAmount) . '.';
                }
                //Internal Loan Amount LG
                $laNo = 0;
                foreach ($LMRInternalLoanGuidelines as $la) {
                    $minLoanAmount = $la['minLoanAmount'];
                    $maxLoanAmount = $la['maxLoanAmount'];
                    $internalService = ' [' . $la['serviceType'] . ']';
                    if (floatval($minLoanAmount) > 0 && floatval($totalLoanAmount) < floatval($minLoanAmount)) {
                        if ($laNo == 0) {
                            $IntGLWText .= "The Loan Amount Range Set In This Program's Guidelines Is Between $ " . Currency::formatDollarAmountWithDecimalZeros($minLoanAmount) . ' and $ ' . Currency::formatDollarAmountWithDecimalZeros($maxLoanAmount) . '. The Current Loan Amount is $ ' . Currency::formatDollarAmountWithDecimalZeros($totalLoanAmount) . ', ';
                        } else {
                            $IntGLWText .= $internalService . ',';
                        }
                    } elseif (floatval($maxLoanAmount) > 0 && floatval($totalLoanAmount) > floatval($maxLoanAmount)) {
                        if ($laNo == 0) {
                            $IntGLWText .= "The Loan Amount Range Set In This Program's Guidelines Is Between $ " . Currency::formatDollarAmountWithDecimalZeros($minLoanAmount) . ' and $ ' . Currency::formatDollarAmountWithDecimalZeros($maxLoanAmount) . '. The Current Loan Amount is $ ' . Currency::formatDollarAmountWithDecimalZeros($totalLoanAmount) . ', ';
                        } else {
                            $IntGLWText .= $internalService . ',';
                        }
                    }
                    $laNo++;
                }
            }
            //Interest Rate
            if ($lien1Rate > 0) {
                //External Interest Rate LG
                if ((floatval($minRate) > 0 && floatval($lien1Rate) < floatval($minRate)) || (floatval($maxRate) > 0 && floatval($lien1Rate) > floatval($maxRate))) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= "The Interest Rate Range Set In This Program's Guidelines is between " . $minRate . ' and ' . $maxRate . '. The Current Interest Rate Is ' . $lien1Rate . '.';
                }
                //Internal Interest Rate LG
                $irNo = 0;
                foreach ($LMRInternalLoanGuidelines as $ir) {
                    $minRate = $ir['minRate'];
                    $maxRate = $ir['maxRate'];
                    $internalService = ' [' . $ir['serviceType'] . ']';
                    if ((floatval($minRate) > 0 && floatval($lien1Rate) < floatval($minRate)) || (floatval($maxRate) > 0 && floatval($lien1Rate) > floatval($maxRate))) {
                        if ($irNo == 0) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= "The Interest Rate Range Set In This Program's Guidelines is between " . $minRate . ' and ' . $maxRate . '. The Current Interest Rate Is ' . $lien1Rate . $internalService . ' ,';
                        } else {
                            $IntGLWText .= $internalService . ',';
                        }
                    }
                    $irNo++;
                }
            }
            //Origination points
            if ($chkOrgPoints > 0) {
                //External Origination points LG
                if ((floatval($minPoints) > 0 && floatval($chkOrgPoints) < floatval($minPoints)) || (floatval($maxPoints) > 0 && floatval($chkOrgPoints) > floatval($maxPoints))) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= "The Origination Points Range Set In This Program's Guidelines is between " . floatval($minPoints) . ' and  ' . floatval($maxPoints) . '. The Current Origination Points is ' . floatval($chkOrgPoints) . '.';
                }
                //Internal Origination points LG
                $orNo = 0;
                foreach ($LMRInternalLoanGuidelines as $or) {
                    $minPoints = $or['minPoints'];
                    $maxPoints = $or['maxPoints'];
                    $internalService = ' [' . $or['serviceType'] . ']';

                    if ((floatval($minPoints) > 0 && floatval($chkOrgPoints) < floatval($minPoints)) || (floatval($maxPoints) > 0 && floatval($chkOrgPoints) > floatval($maxPoints))) {
                        if ($orNo == 1) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= "The Origination Points Range Set In This Program's Guidelines is between " . floatval($minPoints) . ' and ' . floatval($maxPoints) . '. The Current Origination Points is ' . floatval($chkOrgPoints) . $internalService . ', ';
                        } else {
                            $IntGLWText .= $internalService . ',';
                        }
                    }
                    $orNo++;
                }
            }
            //Max Loan-to-Cost(LTC)
            if ($LTC > 0) {
                //External Max Loan-to-Cost(LTC) LG
                if (floatval($LGMaxLTC) > 0 && floatval($LTC) > floatval($LGMaxLTC) && floatval($LTC) != floatval($LGMaxLTC)) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= "The Maximum Loan-to-Cost (LTC) Set In This Program's Guidelines Is " . floatval($LGMaxLTC) . '%.  The Current Loan-to-Cost(LTC) Is ' . floatval($LTC) . '%.';
                }
                //Internal Max Loan-to-Cost(LTC) LG
                $ltcNo = 0;
                foreach ($LMRInternalLoanGuidelines as $ltc) {
                    $LGMaxLTC = $ltc['totalLTC'];
                    $internalService = ' [' . $ltc['serviceType'] . ']';
                    if (floatval($LGMaxLTC) > 0 && floatval($LTC) > floatval($LGMaxLTC) && floatval($LTC) != floatval($LGMaxLTC)) {
                        if ($ltcNo == 1) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= "The Maximum Loan-to-Cost (LTC) Set In This Program's Guidelines Is " . floatval($LGMaxLTC) . '%.  The Current Loan-to-Cost(LTC) Is ' . floatval($LTC) . '%.' . $internalService . ', ';
                        } else {
                            $IntGLWText .= $internalService . ', ';
                        }
                    }
                    $ltcNo++;
                }
            }
            //Down Payment
            if (floatval($chkDownPaymentPerc) > 0) {
                //External Down Payment LG
                if (floatval($downPaymentPercent) > 0 && floatval($chkDownPaymentPerc) < floatval($downPaymentPercent)) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= " The Minimum Down Payment Set In This Program's Guidelines is " . floatval($downPaymentPercent) . '. The Current Down Payment Is ' . floatval($chkDownPaymentPerc) . '.';
                }
                //Internal Down Payment LG
                $dpNo = 0;
                foreach ($LMRInternalLoanGuidelines as $dp) {
                    $downPaymentPercent = $dp['downPaymentPercentage'];
                    $internalService = ' [' . $dp['serviceType'] . ']';
                    if (floatval($downPaymentPercent) > 0 && floatval($chkDownPaymentPerc) < floatval($downPaymentPercent)) {
                        if ($dpNo == 0) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= " The Minimum Down Payment Set In This Program's Guidelines is " . floatval($downPaymentPercent) . '. The Current Down Payment Is ' . floatval($chkDownPaymentPerc) . $internalService . ', ';
                        } else {
                            $IntGLWText .= $internalService . ', ';
                        }
                    }
                    $dpNo++;
                }


            }
            if ($typeOfHMLOLoanRequesting == 'Purchase' || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land' || $typeOfHMLOLoanRequesting == 'Commercial Purchase') {
                //LTV
                if (floatval($maxLTV) > 0 && floatval($chkAcquisitionLTV) > floatval($maxLTV) && floatval($chkAcquisitionLTV) != floatval($maxLTV)) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= "The Maximum LTV Set In This Program's Guidelines Is " . $maxLTV . '. The Current LTV Is ' . $chkAcquisitionLTV . '.';
                }
                //ARV
                if (floatval($maxARV) > 0 && floatval($ARV) > floatval($maxARV) && floatval($ARV) != floatval($maxARV)) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= " The Maximum ARV Set In This Program's Guidelines Is " . $maxARV . '. The Current ARV is ' . $ARV . '.';
                }
                //Internal LTV ARV LG
                $ltvArvNo = 0;
                foreach ($LMRInternalLoanGuidelines as $ltvArv) {
                    $maxLTV = $ltvArv['maxLTV'];
                    $maxARV = $ltvArv['maxLTVAfterRehab'];
                    $internalService = ' [' . $ltvArv['serviceType'] . ']';
                    //LTV
                    if (floatval($maxLTV) > 0 && floatval($chkAcquisitionLTV) > floatval($maxLTV) && floatval($chkAcquisitionLTV) != floatval($maxLTV)) {
                        if ($ltvArvNo == 1) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= "The Maximum LTV Set In This Program's Guidelines Is " . $maxLTV . '. The Current LTV Is ' . $chkAcquisitionLTV . $internalService . ', ';
                        } else {
                            $IntGLWText .= $internalService . ', ';
                        }
                    }
                    //ARV
                    if (floatval($maxARV) > 0 && floatval($ARV) > floatval($maxARV) && floatval($ARV) != floatval($maxARV)) {
                        if ($ltvArvNo == 1) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= "The Maximum ARV Set In This Program's Guidelines Is " . $maxARV . '. The Current ARV is ' . $ARV . $internalService . ', ';
                        } else {
                            $IntGLWText .= $internalService . ', ';
                        }
                    }
                    $ltvArvNo++;
                }
            }
            //Fix and Flip
            if (floatval($chkFixflipProp) > 0) {
                //External Fix and Flip LG
                //|| (floatval($maxPropertyForFixFlop) > 0 && floatval($chkFixflipProp) > floatval($maxPropertyForFixFlop))
                if ((floatval($minPropertyForFixFlop) > 0 && floatval($chkFixflipProp) < floatval($minPropertyForFixFlop))) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= " The Minimum Fix and Flip Experience Set In This Program's Guidelines Is A Minimum Of " . $minPropertyForFixFlop . ' Properties. The Current Fix And Flip Construction Experience Is ' . $chkFixflipProp . '.';
                }
                //Internal Fix and Flip LG
                $ffNo = 0;
                foreach ($LMRInternalLoanGuidelines as $ff) {
                    $LGMinFixFlop = $ff['minPropertyForFixFlop'];
                    $LGMaxFixFlop = $ff['maxPropertyForFixFlop'];
                    $internalService = ' [' . $ff['serviceType'] . ']';
                    // || (floatval($LGMaxFixFlop) > 0 && floatval($chkFixflipProp) > floatval($LGMaxFixFlop))
                    if ((floatval($LGMinFixFlop) > 0 && floatval($chkFixflipProp) < floatval($LGMinFixFlop))) {
                        if ($ffNo == 0) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= " The Minimum Fix and Flip Experience Set In This Program's Guidelines Is A Minimum Of " . $LGMinFixFlop . ' Properties. The Current Fix And Flip Construction Experience Is ' . $chkFixflipProp . $internalService . ', ';
                        } else {
                            $IntGLWText .= $internalService . ', ';
                        }
                    }
                    $ffNo++;
                }
            }
            //Ground Construction
            if (floatval($chkGround) > 0) {
                //External Ground Construction LG
                if ((floatval($minPropertyForGrndConst) > 0 && floatval($chkGround) < floatval($minPropertyForGrndConst)) || (floatval($maxPropertyForGrndConst) > 0 && floatval($chkGround) > floatval($maxPropertyForGrndConst))) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= " The Minimum Ground Up Construction Experience Set In This Program's Guidelines Is " . $minPropertyForGrndConst . ' Properties. The Current Ground Up Construction Experience Is ' . $chkGround . ' Properties.';
                }
                //Internal Ground Construction LG
                $gcNo = 0;
                foreach ($LMRInternalLoanGuidelines as $gc) {
                    $LGMinGround = $gc['minPropertyForGrndConst'];
                    $LGMaxGround = $gc['maxPropertyForGrndConst'];
                    $internalService = ' [' . $gc['serviceType'] . ']';
                    if ((floatval($LGMinGround) > 0 && floatval($chkGround) < floatval($LGMinGround)) || (floatval($LGMaxGround) > 0 && floatval($chkGround) > floatval($LGMaxGround))) {
                        if ($gcNo == 0) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= " The Minimum Ground Up Construction Experience Set In This Program's Guidelines Is " . $LGMinGround . ' Properties. The Current Ground Up Construction Experience Is ' . $chkGround . $internalService . ' Properties, ';
                        } else {
                            $IntGLWText .= $internalService . ', ';
                        }
                    }
                    $gcNo++;
                }

            }
            //Mid FICO
            if (floatval($chkMidFico) > 0) {
                //External Mid FICO LG
                if ((floatval($minMidFico) > 0 && floatval($chkMidFico) < floatval($minMidFico))) {
                    if (trim($ExtGLWText) != '') $ExtGLWText .= '<br>';
                    $ExtGLWText .= " The Minimum Mid FICO Score Set In This Program's Guidelines is " . $minMidFico . ' . The Current Mid FICO score is ' . $chkMidFico . '.';
                }
                //Internal Mid FICO LG
                $mfNo = 0;
                foreach ($LMRInternalLoanGuidelines as $mf) {
                    $minMidFico = $mf['minMidFico'];
                    $maxMidFico = $mf['maxMidFico'];
                    $internalService = ' [' . $mf['serviceType'] . ']';
                    if ((floatval($minMidFico) > 0 && floatval($chkMidFico) < floatval($minMidFico))) {
                        if ($mfNo == 0) {
                            if (trim($IntGLWText) != '') $IntGLWText .= '<br>';
                            $IntGLWText .= " The Minimum Mid FICO Score Set In This Program's Guidelines is " . $minMidFico . ' . The Current Mid FICO score is ' . $chkMidFico . $internalService . ', ';
                        } else {
                            $IntGLWText .= $internalService . ', ';
                        }
                    }
                    $mfNo++;
                }
            }


            if (trim($ExtGLWText) == '') {
                $ExtGLWText = 'None';
            }
            if (trim($IntGLWText) == '') {
                $IntGLWText = 'None';
            }

            //Handle for Google Docs
            if ($googleDocs == 1 && ($LMRID > 0)) { // Google Docs
                $ExternalGuidelineWarnings = str_replace('<br>', "\n", $ExtGLWText);
                $InternalGuidelineWarnings = str_replace('<br>', "\n", $IntGLWText);
            } else { // Email Template
                $ExternalGuidelineWarnings = $ExtGLWText;
                $InternalGuidelineWarnings = $IntGLWText;
            }


            $inArray['ExternalGuidelineWarnings'] = $ExternalGuidelineWarnings;
            $inArray['InternalGuidelineWarnings'] = $InternalGuidelineWarnings;


            /* Fees & cost Calculation - values from /backoffice/HMLOLoanInfoCalcForSmartTag.php */

            $taxImpoundsMonth = $fileHMLONewLoanInfo['taxImpoundsMonth'] ?? 0;
            $taxImpoundsMonthAmt = $fileHMLONewLoanInfo['taxImpoundsMonthAmt'] ?? 0;
            $taxImpoundsFee = $fileHMLONewLoanInfo['taxImpoundsFee'] ?? 0;
            $MERSID = $fileHMLONewLoanInfo['MERSID'] ?? null;

            $insImpoundsMonth = $fileHMLONewLoanInfo['insImpoundsMonth'] ?? 0;
            $insImpoundsMonthAmt = $fileHMLONewLoanInfo['insImpoundsMonthAmt'] ?? 0;
            $insImpoundsFee = $fileHMLONewLoanInfo['insImpoundsFee'] ?? 0;

            $floodImpoundsMonth = $fileHMLONewLoanInfo['floodImpoundsMonth'] ?? 0;
            $floodImpoundsMonthAmt = $fileHMLONewLoanInfo['floodImpoundsMonthAmt'] ?? 0;
            $floodImpoundsFee = $fileHMLONewLoanInfo['floodImpoundsFee'] ?? 0;

            $applicantConfirmed = $fileHMLONewLoanInfo['applicantConfirmed'] ? 'Checked' : 'Not Checked';
            $inArray['applicantConfirmed'] = $applicantConfirmed;

            $inArray['originationPointsRate'] = strval(floatval($originationPointsRate));  //use strval to cast it to string for google sheets
            $inArray['originationPointsValue'] = Currency::formatDollarAmountWithDecimalZeros($originationPointsValue);
            if ($inArray['originationPointsValue'] == 0.00) $inArray['originationPointsValue'] = 0;
            $inArray['brokerPointsRate'] = strval(floatval($brokerPointsRate));   //use strval to cast it to string for google sheets
            $inArray['brokerPointsValue'] = Currency::formatDollarAmountWithDecimalZeros($brokerPointsValue);
            if ($inArray['brokerPointsValue'] == 0.00) $inArray['brokerPointsValue'] = 0;

            // die($brokerPointsValue);
            $inArray['applicationFee'] = $applicationFee;
            $inArray['appraisalFee'] = $appraisalFee;
            $inArray['estdTitleClosingFee'] = $estdTitleClosingFee;
            $inArray['processingFee'] = $processingFee;
            $inArray['underwritingFees'] = $underwritingFees;
            $inArray['procAndUnderwritingFee'] = floatval($processingFee) + floatval($underwritingFees);
            $inArray['drawsSetUpFee'] = $drawsSetUpFee;
            $inArray['drawsFee'] = $drawsFee;
            $inArray['valuationBPOFee'] = $valuationBPOFee;
            $inArray['valuationAVMFee'] = $valuationAVMFee;
            $inArray['valuationAVEFee'] = $valuationAVEFee;
            $inArray['valuationCMAFee'] = $valuationCMAFee;
            $inArray['creditReportFee'] = $creditReportFee;
            $inArray['backgroundCheckFee'] = $backgroundCheckFee;
            $inArray['taxServiceFee'] = $taxServiceFee;
            $inArray['documentPreparationFee'] = $documentPreparationFee;
            $inArray['wireFee'] = $wireFee;
            $inArray['servicingSetUpFee'] = $servicingSetUpFee;
            $inArray['floodCertificateFee'] = $floodCertificateFee;
            $inArray['floodServiceFee'] = $floodServiceFee;
            $inArray['inspectionFees'] = $inspectionFees;
            $inArray['projectFeasibility'] = $projectFeasibility;
            $inArray['dueDiligence'] = $dueDiligence;
            $inArray['UccLienSearch'] = $UccLienSearch;
            $inArray['thirdPartyFees'] = $thirdPartyFees;
            $inArray['otherFee'] = $otherFee;
            $inArray['brokerProcessingFee'] = $brokerProcessingFee;

            $inArray['taxNoOfMonthsCollectedAtClosing'] = $taxImpoundsMonth;
            $inArray['taxMonthlyEscrow'] = Currency::formatDollarAmountWithDecimal($taxImpoundsMonthAmt);
            $inArray['taxImpoundsFee'] = $taxImpoundsFee;
            $inArray['MERSID'] = $MERSID;

            $inArray['hazardNoOfMonthsCollectedAtClosing'] = $insImpoundsMonth;
            $inArray['hazardMonthlyEscrow'] = Currency::formatDollarAmountWithDecimal($insImpoundsMonthAmt);
            $inArray['insImpoundsFee'] = $insImpoundsFee;

            $inArray['floodNoOfMonthsCollectedAtClosing'] = $floodImpoundsMonth;
            $inArray['floodMonthlyEscrow'] = $floodImpoundsMonthAmt;
            $inArray['floodTotalCollectedAtClosing'] = $floodImpoundsFee;


            $specialHolidayWeekendInterest = $HUDFundingClosingInfoData->specialHolidayWeekendInterest ?? 0;
            $totalEscrowInterestCollectedAtClosing = HUDFundingClosingInfo::TotalEscrowInterest(
                $taxImpoundsFee,
                $insImpoundsFee,
                $floodImpoundsFee,
                $specialHolidayWeekendInterest,
                $totalEstPerDiem
            );

            $inArray['totalEscrowInterestCollectedAtClosing'] = Currency::formatDollarAmountWithDecimal($totalEscrowInterestCollectedAtClosing);

            $inArray['rehabCostFinanced'] = Currency::formatDollarAmountWithDecimal($rehabCostFinanced);
            $inArray['escrowFees'] = $escrowFees;
            $inArray['settlementOrClosingEscrowFee'] = $escrowFees;
            $inArray['survey'] = $survey;
            $inArray['wholeSaleAdminFee'] = $wholeSaleAdminFee;
            $inArray['attorneyFee'] = $attorneyFee;
            $inArray['travelNotaryFee'] = $travelNotaryFee;
            $inArray['constructionHoldbackFee'] = $constructionHoldbackFee;
            $originatingBrokerFees = 0;
            $originatingBrokerFees = Currency::formatDollarAmountWithDecimal(
                floatval($originationPointsRate)
                + floatval($processingFee)
                + floatval($documentPreparationFee)
                + floatval($underwritingFees)
                + floatval($applicationFee)
                + floatval($dueDiligence)
            );
            $inArray['originatingBrokerFees'] = $originatingBrokerFees;
            $inArray['prePaidInterestFee'] = $prePaidInterestFee;

            /*  Budget And Draws Info */
            $budgetAndDrawsInfo = [];
            if (array_key_exists($LMRID, $budgetAndDrawsInfoArray)) {
                $budgetAndDrawsInfo = $budgetAndDrawsInfoArray[$LMRID];
            }

            $totalDrawsFunded = 0;
            if (count($budgetAndDrawsInfo) > 0) {
                for ($k = 0; $k < count($budgetAndDrawsInfo); $k++) {
                    $totalDrawsFunded += Strings::replaceCommaValues($budgetAndDrawsInfo[$k]['amountAddedToTotalDrawsFunded']);
                }
                $inArray['totalDrawsFunded'] = Currency::formatDollarAmountWithDecimal($totalDrawsFunded);
            }

            $availableBudget = Strings::replaceCommaValues($rehabCostFinanced) - Strings::replaceCommaValues($totalDrawsFunded);
            if ($availableBudget == '') {
                $inArray['availableBudget'] = 0;
            } else {
                $inArray['availableBudget'] = Currency::formatDollarAmountWithDecimal($availableBudget);
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            if (count($additionalGuarantorsInfo ?? []) > 0) {
                $gn = 1;
                foreach ($additionalGuarantorsInfo as $gk => $gv) {

                    $guaAddressInfo = '';
                    $guarantorDOB = '';
                    $appendComma = '';
                    if ($gv['guarantorAddress'] != '') {
                        $guaAddressInfo = $gv['guarantorAddress'];
                        $appendComma = ', ';
                    }
                    if ($gv['guarantorCity'] != '') {
                        $guaAddressInfo .= $appendComma . $gv['guarantorCity'];
                        $appendComma = ', ';
                    }
                    if ($gv['guarantorState'] != '') {
                        $guaAddressInfo .= $appendComma . $gv['guarantorState'];
                        $appendComma = ', ';
                    }
                    if ($gv['guarantorZip'] != '') {
                        $guaAddressInfo .= $appendComma . $gv['guarantorZip'];
                        $appendComma = '';
                    }

                    if (Dates::IsEmpty($gv['guarantorDOB'])) {
                        $guarantorDOB = '';
                    } else {
                        $guarantorDOB = Dates::formatDateWithRE($gv['guarantorDOB'], 'YMD', 'm/d/Y');
                    }

                    $inArray['guarantorFName' . $gn] = $gv['guarantorFName'];
                    $inArray['guarantorMName' . $gn] = $gv['guarantorMName'];
                    $inArray['guarantorLName' . $gn] = $gv['guarantorLName'];
                    $inArray['guarantorPhone' . $gn] = $gv['guarantorPhone'];
                    $inArray['guarantorCellNumber' . $gn] = $gv['guarantorCellNumber'];
                    $inArray['guarantorSSN' . $gn] = $gv['guarantorSSN'];
                    $inArray['guaAddressInfo' . $gn] = $guaAddressInfo;
                    $inArray['guarantorEmail' . $gn] = $gv['guarantorEmail'];
                    $inArray['guarantorNote' . $gn] = $gv['guarantorNote'];
                    $inArray['guarantorDOB' . $gn] = $guarantorDOB;
                    $gn++;
                }
            }
            if (count($alternateNamesInfo ?? []) > 0) {
                $an = 1;
                foreach ($alternateNamesInfo as $ak => $av) {
                    $inArray['alternateFName' . $an] = $av['alternateFName'];
                    $inArray['alternateMName' . $an] = $av['alternateMName'];
                    $inArray['alternateLName' . $an] = $av['alternateLName'];
                    $an++;
                }
            }
            /* HMLO BackGround Info */
            $fileHMLOBackGroundInfo = [];
            $PrimaryOccupancy = '';
            if (array_key_exists($LMRID, $fileHMLOBackGroundInfoArray)) {
                $fileHMLOBackGroundInfo = $fileHMLOBackGroundInfoArray[$LMRID];
                if (count($fileHMLOBackGroundInfo) > 0) {
                    $PrimaryOccupancy = trim($fileHMLOBackGroundInfo['isBorIntendToOccupyPropAsPRI']);
                    $inArray['PrimaryOccupancy'] = $PrimaryOccupancy;
                    $inArray['isBorUSCitizen'] = $fileHMLOBackGroundInfo['isBorUSCitizen'];
                    $inArray['isBorDeclaredBankruptPastYears'] = $fileHMLOBackGroundInfo['isBorDecalredBankruptPastYears'];
                    $inArray['hasBorAnyActiveLawsuits'] = $fileHMLOBackGroundInfo['hasBorAnyActiveLawsuits'];
                    $inArray['hasBorObligatedInForeclosure'] = $fileHMLOBackGroundInfo['hasBorObligatedInForeclosure'];
                    $inArray['haveBorOtherfraudRelatedCrimes'] = $fileHMLOBackGroundInfo['haveBorOtherFraudRelatedCrimes'];
                    $inArray['isAnyBoroutstandingJudgements'] = $fileHMLOBackGroundInfo['isAnyBorOutstandingJudgements'];
                    $inArray['hasBorPropertyTaxLiens'] = $fileHMLOBackGroundInfo['hasBorPropertyTaxLiens'];
                    $inArray['isBorPresenltyDelinquent'] = $fileHMLOBackGroundInfo['isBorPresenltyDelinquent'];
                    $inArray['borDesignatedBeneficiaryAgreement'] = $fileHMLOBackGroundInfo['borDesignatedBeneficiaryAgreement'];
                    $inArray['isBorPersonallyGuaranteeLoan'] = $fileHMLOBackGroundInfo['isBorPersonallyGuaranteeLoan'];
                }
            }

            $inArray['haveBorOwnInvestmentProperties'] = $fileHMLOExperienceInfo['haveBorOwnInvestmentProperties'];
            $inArray['areBorMemberOfInvestmentClub'] = $fileHMLOExperienceInfo['areBorMemberOfInvestmentClub'];
            $inArray['borClubName'] = $fileHMLOExperienceInfo['borClubName'];
            $inArray['haveBorProfLicences'] = $fileHMLOExperienceInfo['haveBorProfLicences'];
            $inArray['fullTimeRealEstateInvestor'] = $fileHMLOExperienceInfo['fullTimeRealEstateInvestor'];
            $inArray['borPrimaryInvestmentStrategy'] = $fileHMLOExperienceInfo['borPrimaryInvestmentStrategy'];
            $inArray['overallRealEstateInvesExp'] = $fileHMLOExperienceInfo['overallRealEstateInvesExp'];

            $inArray['borNoOfProSellExperience'] = $fileHMLOExperienceInfo['borNoOfProSellExperience'];
            $inArray['borNoOfProSellCompleted'] = $fileHMLOExperienceInfo['borNoOfProSellCompleted'];


            $inArray['purchaseCloseDate'] = Dates::formatDateWithRE($fileHMLOPropertyInfo['purchaseCloseDate'], 'YMD', 'm/d/Y');
            $inArray['acceptedPurchase'] = $fileHMLOPropertyInfo['acceptedPurchase'];
            $inArray['mersNumber'] = $fileHMLOPropertyInfo['mersNumber'];
            $inArray['estimatedPropValue'] = $propertyestimatedValue;
            $inArray['extensionFee'] = Currency::formatDollarAmountWithDecimal($fileHMLOPropertyInfo['extensionFee']);

            /* Escrow Information */
            $escrowInfo = [];
            $escrowRepFirstName = $escrowRepLastName = $escrowCompany = $escrowEmail = $escrowPhone = $escrowTollFree = $escrowFax = $escrowCell = $escrowNum = $escrowAddress = $escrowCity = $escrowState = $escrowZip = $escrowStateLong = '';
            if (array_key_exists($LMRID, $escrowInfoArray)) {
                $escrowInfo = $escrowInfoArray[$LMRID];
                if (count($escrowInfo) > 0) {
                    $escrowRepFirstName = trim($escrowInfo['escrowRepFirstName']);
                    $escrowRepLastName = trim($escrowInfo['escrowRepLastName']);
                    $escrowCompany = trim($escrowInfo['escrowCompany']);
                    $escrowEmail = trim($escrowInfo['escrowEmail']);
                    $escrowPhone = trim($escrowInfo['escrowPhone']);
                    $escrowTollFree = trim($escrowInfo['escrowTollFree']);
                    $escrowFax = trim($escrowInfo['escrowFax']);
                    $escrowCell = trim($escrowInfo['escrowCell']);
                    $escrowNum = trim($escrowInfo['escrowNum']);
                    $escrowAddress = trim($escrowInfo['escrowAddress']);
                    $escrowCity = trim($escrowInfo['escrowCity']);
                    $escrowState = trim($escrowInfo['escrowState']);
                    $escrowZip = trim($escrowInfo['escrowZip']);
                    $escrowStateLong = trim($escrowInfo['escrowStateLong']);

                    $inArray['escrowRepFirstName'] = $escrowRepFirstName;
                    $inArray['escrowRepLastName'] = $escrowRepLastName;
                    $inArray['escrowCompany'] = $escrowCompany;
                    $inArray['escrowEmail'] = $escrowEmail;
                    $inArray['escrowPhone'] = Strings::formatPhoneNumber(trim($escrowPhone));
                    $inArray['escrowTollFree'] = Strings::formatPhoneNumber(trim($escrowTollFree));
                    $inArray['escrowFax'] = Strings::formatPhoneNumber(trim($escrowFax));
                    $inArray['escrowCell'] = Strings::formatPhoneNumber(trim($escrowCell));
                    $inArray['escrowNum'] = trim($escrowNum);
                    $inArray['escrowAddress'] = trim($escrowAddress);
                    $inArray['escrowCity'] = $escrowCity;
                    $inArray['escrowState'] = Strings::getStateFullName(Arrays::fetchStates(), $escrowState);
                    $inArray['escrowZip'] = $escrowZip;
                    $inArray['escrowStateLong'] = $escrowStateLong;
                }
            }
            /* End Of Escrow Information */

            /* HOA1 Information */
            $HOA1Info = [];
            if (array_key_exists($LMRID, $HOA1InfoArray ?? [])) {
                $HOA1Info = $HOA1InfoArray[$LMRID];
                if (count($HOA1Info) > 0) {
                    $inArray['condominiumOrHOAFeeAmtReceiver'] = trim($HOA1Info['companyName']);
                    $inArray['HOContactName'] = trim($HOA1Info['contactName']);
                }
            }

            /* End Of HOA1 Information */
            /* HOA2 Information */
            $HOA2Info = [];
            if (array_key_exists($LMRID, $HOA2InfoArray ?? [])) {
                $HOA2Info = $HOA2InfoArray[$LMRID];
                if (count($HOA2Info) > 0) {
                    $inArray['HOA2CompanyName'] = trim($HOA2Info['companyName']);
                    $inArray['HOA2ContactName'] = trim($HOA2Info['contactName']);
                }
            }
            /* End Of HOA2 Information */

            /* listingPageArray Information */
            $listingPage = [];
            $pointOfContactName = $pointOfContactUserType = $pointOfContactEmail = $listingType = $listDescription = '';
            if (array_key_exists($LMRID, $listingPageArray ?? [])) {
                $listingPage = $listingPageArray[$LMRID];
                if (count($listingPage) > 0) {
                    $pointOfContactName = trim($listingPage['pointOfContactName']);
                    $pointOfContactUserType = trim($listingPage['pointOfContactUserType']);
                    $pointOfContactEmail = trim($listingPage['pointOfContactEmail']);
                    $listingType = trim($listingPage['listingType']);
                    if ($listingType == 'OFF') $listingType = 'Open For Funding';
                    if ($listingType == 'NFS') $listingType = 'Note for Sale';
                    if ($listingType == 'CL') $listingType = 'Closed Loan';
                    if ($listingType == 'PFS') $listingType = 'Property For Sale';
                    $listDescription = trim($listingPage['listDescription']);
                    $inArray['pointOfContactName'] = trim($pointOfContactName);
                    $inArray['pointOfContactUserType'] = trim($pointOfContactUserType);
                    $inArray['pointOfContactEmail'] = $pointOfContactEmail;
                    $inArray['listingType'] = $listingType;
                    $inArray['listDescription'] = $listDescription;
                }
            }
            /* End Of listingPageArray Information */

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }


            /* Billing Info */
            $ACTHolderAddr = '';
            $ACTHolderCity = '';
            $ACTHolderState = '';
            $ACTHolderZip = '';
            $ACTHolderBankName = '';
            $ACTHolderDepositDate = '';
            $BankRoutingNumber = '';
            $AHAddrInfo = '';
            $appendComma = '';
            if (array_key_exists($LMRID, $LMRACHInfoArray)) {
                $LMRACHInfo = $LMRACHInfoArray[$LMRID];
                if (count($LMRACHInfo) > 0) {
                    $ACTHolderAddr = trim($LMRACHInfo['acctHolderAddr']);
                    $ACTHolderCity = trim($LMRACHInfo['acctHolderCity']);
                    $ACTHolderState = trim($LMRACHInfo['acctHolderState']);
                    $ACTHolderZip = trim($LMRACHInfo['acctHolderZip']);
                    $ACTHolderBankName = trim($LMRACHInfo['bankName']);
                    $ACTHolderDepositDate = trim($LMRACHInfo['depositDate']);
                    $BankRoutingNumber = trim($LMRACHInfo['routingNo']);

                    if ($ACTHolderAddr != '') {
                        $AHAddrInfo = $ACTHolderAddr;
                        $appendComma = ', ';
                    }
                    if ($ACTHolderCity != '') {
                        $AHAddrInfo .= $appendComma . $ACTHolderCity;
                        $appendComma = ', ';
                    }
                    if ($ACTHolderState != '') {
                        $AHAddrInfo .= $appendComma . $ACTHolderState;
                        $appendComma = ', ';
                    }
                    if ($ACTHolderZip != '') {
                        $AHAddrInfo .= $appendComma . $ACTHolderZip;
                        $appendComma = '';
                    }

                    $inArray['ACTNumber'] = cypher::myDecryption(trim($LMRACHInfo['accountNo']));
                    $inArray['ACTHolderName'] = ucwords(trim($LMRACHInfo['accountName']));
                    $inArray['ACTHolderAddr'] = $ACTHolderAddr;
                    $inArray['ACTHolderCity'] = $ACTHolderCity;
                    $inArray['ACTHolderState'] = $ACTHolderState;
                    $inArray['ACTHolderZip'] = $ACTHolderZip;
                    $inArray['AHAddrInfo'] = $AHAddrInfo;
                    $inArray['ACTBankName'] = $ACTHolderBankName;
                    $inArray['ACTHolderDepositDate'] = Dates::formatDateWithRE($ACTHolderDepositDate, 'YMD', 'm/d/Y');
                    $inArray['BankRoutingNumber'] = cypher::myDecryption(trim($BankRoutingNumber));
                }
            }
            $newCCNumber = '';
            $CCNumber = '';
            $CCNoLen = 0;
            $CCAddress = '';
            $CCCity = '';
            $CCState = '';
            $CCZip = '';
            $CHAddrInfo = '';
            $appendComma = '';
            $CCType = '';
            $CCExpiryMonth = '';
            $CCExpiryYear = '';

            if (array_key_exists($LMRID, $LMRCCInfoArray)) {
                $LMRCCInfo = $LMRCCInfoArray[$LMRID];
                if (count($LMRCCInfo) > 0) {
                    $CCNumber = cypher::myDecryption(trim($LMRCCInfo['CCNumber']));
                    $CCAddress = trim($LMRCCInfo['CCAddress']);
                    $CCCity = trim($LMRCCInfo['CCCity']);
                    $CCState = trim($LMRCCInfo['CCState']);
                    $CCZip = trim($LMRCCInfo['CCZip']);
                    $CCType = cypher::myDecryption(trim($LMRCCInfo['CCType']));
                    $CCExpiryMonth = cypher::myDecryption(trim($LMRCCInfo['CCExpiryMonth']));
                    $CCExpiryMonth = date('M', mktime(0, 0, 0, intval($CCExpiryMonth), 10));
                    $CCExpiryYear = cypher::myDecryption(trim($LMRCCInfo['CCExpiryYear']));

                    if ($CCType != '') {
                        $newCreditCardTypeArraytemp = ['VISA', 'MASTER CARD', 'AMEX', 'DISCOVER'];
                        $newCreditCardTypeCodeArrayTemp = ['VI', 'CA', 'AX', 'DS'];

                        $key = array_search($CCType, $newCreditCardTypeCodeArrayTemp);
                        $CCType = $newCreditCardTypeArraytemp[$key];
                    }
                    if ($CCAddress != '') {
                        $CHAddrInfo = $CCAddress;
                        $appendComma = ', ';
                    }
                    if ($CCCity != '') {
                        $CHAddrInfo .= $appendComma . $CCCity;
                        $appendComma = ', ';
                    }
                    if ($CCState != '') {
                        $CHAddrInfo .= $appendComma . $CCState;
                        $appendComma = ', ';
                    }
                    if ($CCZip != '') {
                        $CHAddrInfo .= $appendComma . $CCZip;
                        $appendComma = '';
                    }

                    $CCNoLen = strlen($CCNumber) - 4;
                    if ($CCNoLen > 0) {
                        $tempDisplayCCNumber = $CCNumber;
                        for ($l = 0; $l < $CCNoLen; $l++) {
                            $tempDisplayCCNumber = substr_replace($tempDisplayCCNumber, '#', $l);
                        }
                        $newCCNumber = $tempDisplayCCNumber . substr($CCNumber, -4);
                    }

                    //$inArray['CCNumber']      = $newCCNumber;
                    $inArray['CCNumber'] = chunk_split($CCNumber, 4);
                    $inArray['CCName'] = ucwords(trim($LMRCCInfo['CCName']));
                    $inArray['CCSecCode'] = cypher::myDecryption(trim($LMRCCInfo['CCSecCode']));
                    $inArray['CCAddress'] = $CCAddress;
                    $inArray['CCCity'] = $CCCity;
                    $inArray['CCState'] = Strings::getStateFullName(Arrays::fetchStates(), $CCState);
                    $inArray['CCZip'] = $CCZip;
                    $inArray['CHAddrInfo'] = $CHAddrInfo;
                    $inArray['CCType'] = $CCType;
                    $inArray['CCExpiryMonth'] = $CCExpiryMonth;
                    $inArray['CCExpiryYear'] = $CCExpiryYear;
                    $inArray['CCExpiry'] = $CCExpiryMonth . ' / ' . $CCExpiryYear;
                }
            }
            if (trim($CHAddrInfo) != '') $billingAddrInfo = $CHAddrInfo;
            elseif (trim($AHAddrInfo) != '') $billingAddrInfo = $AHAddrInfo;
            $inArray['billingAddrInfo'] = $billingAddrInfo;

            /*get seller info*/
            if (count($getSellerInfo) > 0) {
                $inArray['sellerinfoFirstName'] = $getSellerInfo['sellerinfoFirstName'];
                $inArray['sellerinfoLastName'] = $getSellerInfo['sellerinfoLastName'];
                $inArray['sellerinfoEmail'] = $getSellerInfo['sellerinfoEmail'];
                $inArray['sellerinfoPhone'] = $getSellerInfo['sellerinfoPhone'];
                $inArray['sellerinfoAddress'] = $getSellerInfo['sellerinfoAddress'];
                $inArray['sellerinfoCity'] = $getSellerInfo['sellerinfoCity'];
                $inArray['sellerinfoState'] = $getSellerInfo['sellerinfoState'];
                $inArray['sellerinfoZip'] = $getSellerInfo['sellerinfoZip'];
            }
            /*get seller info end*/

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }


            /** Investor Merge Tags **/

            if (count($investorInfoArray ?? []) > 0) {
                $n = 1;
                foreach ($investorInfoArray as $in => $iv) {
                    $investedAmt = 0;
                    $investedAmountPercent = 0;
                    $inArray['fullName' . $n] = $iv['contactName'] . ' ' . $iv['contactLName'];
                    $inArray['investorEmail' . $n] = $iv['email'];
                    $inArray['address' . $n] = $iv['address'];
                    $inArray['companyName' . $n] = $iv['companyName'];
                    $inArray['investorTitleVesting' . $n] = $iv['investorTitleVesting'];
                    $inArray['investorYield' . $n] = Currency::formatDollarAmountWithDecimal($iv['investorYield']);
                    $inArray['expectedMonthlyPayment' . $n] = Currency::formatDollarAmountWithDecimal($iv['expectedMonthlyPayment']);
                    $investedAmt = Currency::formatDollarAmountWithDecimal($iv['investedAmount']);
                    if ($totalLoanAmount > 0) {
                        $investedAmountPercent = ((Strings::replaceCommaValues($investedAmt) / Strings::replaceCommaValues($totalLoanAmount)) * 100);
                    }
                    $inArray['investedAmountPercent' . $n] = Currency::formatDollarAmountWithDecimal($investedAmountPercent);
                    $n++;
                }
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            $userNumber = 0;
            $userRole = $userGroup = '';
            if (isset($_SESSION['userRole'])) $userRole = $_SESSION['userRole'];
            if (isset($_SESSION['userGroup'])) $userGroup = $_SESSION['userGroup'];
            if (isset($_SESSION['userNumber'])) $userNumber = $_SESSION['userNumber'];

            /*
             * Issue : LoggedInUser Info missing in googledocs.
             * Solution : We are pass logged in user id in that prod server url so it can generate the tags for doc wizard docs.
             * luid = Logged In User ID.
             * lur  = Logged In User Role.
             * lugr = Logged In User Group.
             */
            if (isset($_REQUEST['luid'])) {
                $userNumber = cypher::myDecryption($_REQUEST['luid']);
                $userRole = cypher::myDecryption($_REQUEST['lur']);
                $userGroup = cypher::myDecryption($_REQUEST['lugr']);
            }
            if (sizeof($loggedInUserInfo) > 0) {
                $userNumber = $loggedInUserInfo['userNumber'];
                $userRole = $loggedInUserInfo['userRole'];
                $userGroup = $loggedInUserInfo['userGroup'];
            }
            $ipArray['UID'] = $userNumber;
            $ipArray['UType'] = $userRole;
            $ipArray['userGroup'] = $userGroup;

            $loggedInUserInfoArray = UserAccess::getLoggedInUserInfo($ipArray);

            $fax = '';
            $phoneNumber = '';
            $email = '';
            $cellPhone = '';
            $directPhone = '';
            $pwd = '';
            $loginCredentials = '';
            $lName = '';
            $avatar = '';

            if (isset($loggedInUserInfoArray['lName'])) $lName = trim($loggedInUserInfoArray['lName']);

            if (count($loggedInUserInfoArray ?? []) > 0) {
                $userName = trim($loggedInUserInfoArray['fName']) . ' ' . $lName;
                $email = trim($loggedInUserInfoArray['email']);
                $pwd = trim($loggedInUserInfoArray['pwd']);
                $fax = Strings::formatPhoneNumber(trim($loggedInUserInfoArray['fax']));
                $phoneNumber = Strings::formatPhoneNumber(trim($loggedInUserInfoArray['phoneNumber']));
                $cellPhone = Strings::formatPhoneNumber(trim($loggedInUserInfoArray['cellNumber']));
                $directPhone = Strings::formatPhoneNumber(trim($loggedInUserInfoArray['directPhone']));
                $avatar = trim($loggedInUserInfoArray['avatar']);
                if ($email != '') $loginCredentials = 'User Name: ' . $email;
                if ($pwd != '') $loginCredentials .= "\nPassword: " . $pwd;
            }
            if ($clientEmail != '') $clientLoginCredentials = 'Client Email: ' . $clientEmail;
            if ($clientPwd != '') $clientLoginCredentials .= '<br>Password: ' . $clientPwd;
            $todaysdate = date('m/d/Y');
            $twoweeks = date('m/d/Y', mktime(0, 0, 0, date('m'), date('d') + 14, date('Y')));
            /* Logged in User Info */

            $inArray['userName'] = $userName;
            $inArray['fax'] = $fax;
            $inArray['phoneNumber'] = $phoneNumber;
            $inArray['email'] = $email;
            $inArray['cellPhone'] = $cellPhone;
            $inArray['directPhone'] = $directPhone;
            $inArray['twoweeks'] = $twoweeks;
            $inArray['loginCredentials'] = $loginCredentials;
            $inArray['clientLoginCredentials'] = $clientLoginCredentials;
            $inArray['myAvatar'] = $avatar ? rtrim($CONST_SITE_URL, '/') . $avatar : '';
            /* Logged in User Info */

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /**
             * Description    : Added the file Open URL in Both LM and HMLO Modules
             * Date        : Dec 11, 2017
             * Developer    : Viji & Venky
             **/

            $borrowerFileOpen = '';
            $BOBorrowerFileOpen = '';
            $AGBorrowerFileOpen = '';
            $BRBorrowerFileOpen = '';
            $borrDocUrl = '';
            $brokerLogoBorrowerRequiredDocs = '';
            $brDocUrl = '';
            $agDocUrl = '';
            $fileModuleCode = '';
            $fileModuleCode = $fileMC[0];
            /**
             * Google docs customization..
             */
            $vhostLink = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
                ? LMRequest::myFileInfo()->PCInfo()->vhost : CONST_SITE_URL;
            $vhostLink = HTTP::formatUrl($vhostLink);

            if ($googleDocs == 1 && ($LMRID > 0)) {
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['borrowerFileOpen'],
                    'linkurl' => $vhostLink.'backoffice/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . '&opt=enc',
                    'lintext' => 'Client File Url',
                ];
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['AGBorrowerFileOpen'],
                    'linkurl' => $vhostLink.'agent/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . '&opt=enc',
                    'lintext' => 'Loan Officer/Broker File Url',
                ];
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['BOBorrowerFileOpen'],
                    'linkurl' => $vhostLink.'backoffice/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . '&opt=enc',
                    'lintext' => 'Backoffice File Url',
                ];
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['BRBorrowerFileOpen'],
                    'linkurl' => $vhostLink.'branch/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . '&opt=enc',
                    'lintext' => 'Branch File Url',
                ];
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['borrDocUrl'],
                    'linkurl' => $vhostLink.'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Borrower'),
                    'lintext' => 'Upload Required Docs',
                ];
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['brDocUrl'],
                    'linkurl' => $vhostLink.'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Branch'),
                    'lintext' => 'Upload Required Docs',
                ];
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['agDocUrl'],
                    'linkurl' => $vhostLink.'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Agent/Broker'),
                    'lintext' => 'Upload Required Docs',
                ];
                $inArray['links'][] = [
                    'linktag' => $dynamicTagValue['loanOfficerDocUrl'],
                    'linkurl' => $vhostLink.'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('LoanOfficer'),
                    'lintext' => 'Upload Required Docs',
                ];
            } else {

                if ($LMRID > 0) {
                    $borrowerFileOpen = "<a target=\"_blank\" href=\"" . $vhostLink . 'client/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . "&opt=enc\" rel=\"nofollow\">" . trim($borrowerName) . '</a>';

                    $BOBorrowerFileOpen = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . "&opt=enc\" rel=\"nofollow\">" . trim($borrowerName) . '</a>';

                    $AGBorrowerFileOpen = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . "&opt=enc\" rel=\"nofollow\">" . trim($borrowerName) . '</a>';

                    $BRBorrowerFileOpen = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/LMRequest.php?eId=' . cypher::myEncryption($executiveID) . '&lId=' . cypher::myEncryption($LMRID) . "&opt=enc\" rel=\"nofollow\">" . trim($borrowerName) . '</a>';

                    /* Required Doc URLs */
                    $borrDocUrl = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Borrower') . "\">Upload Required Docs</a>";

                    $brokerLogoBorrowerRequiredDocs = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Borrower') . '&lg=' . cypher::myEncryption('brokerLogo') . "\">Upload Required Docs</a>";

                    $brDocUrl = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Branch') . "\">Upload Required Docs</a>";
                    $agDocUrl = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('Agent/Broker') . "\">Upload Required Docs</a>";

                    $loanOfficerDocUrl = "<a target=\"_blank\" href=\"" . $vhostLink . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&UType=' . cypher::myEncryption('LoanOfficer') . "\">Upload Required Docs</a>";
                }

                $inArray['borrowerFileOpen'] = $borrowerFileOpen;
                $inArray['BOBorrowerFileOpen'] = $BOBorrowerFileOpen;
                $inArray['AGBorrowerFileOpen'] = $AGBorrowerFileOpen;
                $inArray['BRBorrowerFileOpen'] = $BRBorrowerFileOpen;
                $inArray['borrDocUrl'] = $borrDocUrl;
                $inArray['brokerLogoBorrowerRequiredDocs'] = $brokerLogoBorrowerRequiredDocs;
                $inArray['brDocUrl'] = $brDocUrl;
                $inArray['agDocUrl'] = $agDocUrl;
                $inArray['loanOfficerDocUrl'] = $loanOfficerDocUrl;
            }

            /* Billing Info */
            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }


            /* Missing Docs Info */
            $missingDocInfo = [];
            $checklistNotesInfo = $checklistNotesInfoNew = [];
            if (array_key_exists($LMRID, $missingDocInfoArray)) $missingDocInfo = $missingDocInfoArray[$LMRID];
            if (array_key_exists($LMRID, $fileChecklistNotesInfo)) $checklistNotesInfo = $fileChecklistNotesInfo[$LMRID];
            if (array_key_exists($LMRID, $fileChecklistNotesInfoNew)) $checklistNotesInfoNew = $fileChecklistNotesInfoNew[$LMRID];

            $missingDocNameListInfo = '';
            $clientSpecificDocArray = [];
            $missingDocListInfo = '';
            $missingDocList = '';
            $missingDocNameList = '';
            $missingDocsLetterWithNotes = '';
            $unCheckedDocsListWithNotes = '';
            $tempPCCheckList = $tempfileCheckList = [];
            if (array_key_exists($PCID, $PCCheckList)) {
                $tempPCCheckList = $PCCheckList[$PCID];
            }
            if (array_key_exists($PCID, $fileCheckList)) {
                $tempfileCheckList = $fileCheckList[$PCID];
            }

            $clientSpecificDocArray = array_merge_recursive($tempPCCheckList, $tempfileCheckList);
            $LMRInternalServiceType = '';
            $clientInternalServiceType = [];
            if (array_key_exists($LMRID, $LMRInternalLoanprogramsFiles ?? [])) {
                $clientInternalServiceType = $LMRInternalLoanprogramsFiles[$LMRID];
                if (is_array($clientInternalServiceType)) {
                    $clientInternalServiceTypeArr = explode(',', $clientInternalServiceType[0]);
                } else {
                    $clientInternalServiceTypeArr = explode(',', $clientInternalServiceType);
                }

                if (count($clientInternalServiceTypeArr) > 0) {
                    for ($j = 0; $j < count($clientInternalServiceTypeArr); $j++) {
                        $clientInternalTypeCode = '';
                        $clientInternalTypeCode = trim($clientInternalServiceTypeArr[$j]);
                        if (array_key_exists($clientInternalTypeCode, $LMRClientTypeArray)) {
                            if ($j > 0) $LMRInternalServiceType .= ', ';
                            $LMRInternalServiceType .= trim($LMRClientTypeArray[$clientInternalTypeCode]['serviceType']);
                        }
                    }
                    $inArray['internalLoanProgram'] = $LMRInternalServiceType;
                }
            }

            if ((in_array('##Missing Docs Letter##', $allMergeTagsInContent)
                    || in_array('##UnChecked Docs##', $allMergeTagsInContent)
                    || in_array('##Missing Docs Letter With Notes##', $allMergeTagsInContent)
                    || in_array('##UnChecked Docs With Notes##', $allMergeTagsInContent))
                || (count($allMergeTagsInContent) == 0)
            ) {
                if (count($clientSpecificDocArray) > 0) {

                    if (array_key_exists($LMRID, $fileModuleInfoArray)) {
                        $selectedModuleTypeArray = [];
                        $missingType = '';
                        $tempClientArray = [];
                        $missingDocList = '';
                        $selectedModuleTypeArray = $fileModuleInfoArray[$LMRID];

                        for ($s1 = 0; $s1 < count($selectedModuleTypeArray); $s1++) {
                            $moduleType = '';
                            $mc = 0;
                            $mc1 = 0;
                            $moduleName = '';
                            $tempClientArray = [];
                            $tempPCCheckServiceType = '';
                            $missingType = '';
                            $missingDocList = '';
                            $missingDocNameList = '';
                            $unCheckedDocsWithNotes = '';
                            $missingDocNameListWithNotes = '';
                            $moduleType = trim($selectedModuleTypeArray[$s1]['moduleCode']);
                            $moduleName = trim($selectedModuleTypeArray[$s1]['moduleName']);

                            //  if (array_key_exists($moduleType, $LMRClientTypeArray))  {
                            $tempMissingType = '<b>' . $moduleName . '</b>';
                            //}

                            if (array_key_exists($moduleType, $clientSpecificDocArray)) {
                                $tempClientArray = $clientSpecificDocArray[$moduleType];

                                $missingDocNameListWithNotes = $missingDocNameList = '<ol><p>';
                                $unCheckedDocsWithNotes = $missingDocList = '<ol><p>';

                                //if(count($tempClientArray) > 0) $tempClientArray = sortDbResult($tempClientArray, 'serviceType', SORT_ASC); /* Bug: Checklist - Display Order Task Id: 148366691, Viji, Jul 06, 2017 */

                                for ($d1 = 0; $d1 < count($tempClientArray); $d1++) {
                                    $clientDocVal = '';
                                    $docName = '';
                                    $showDoc = 1;
                                    $checklistNotes = '';
                                    $PCCheckServiceType = '';
                                    $CLType = 'PCL';
                                    $checklistNotesNewArray = [];
                                    $missingDocNewInfo = [];
                                    if (array_key_exists('FMID', $tempClientArray[$d1])) {
                                        $clientDocVal = trim($tempClientArray[$d1]['FMID']);
                                        $CLType = 'FCL';
                                    } else {
                                        $clientDocVal = trim($tempClientArray[$d1]['PCMID']);
                                        $CLType = 'PCL';
                                    }
                                    $PCCheckServiceType = trim($tempClientArray[$d1]['serviceType']);

                                    if (array_key_exists($CLType, $checklistNotesInfo)) {
                                        $checklistNotesNewArray = $checklistNotesInfo[$CLType];
                                    }
                                    if (count($checklistNotesNewArray) > 0) {
                                        if (array_key_exists($clientDocVal, $checklistNotesNewArray)) {
                                            $checklistNotes = trim($checklistNotesNewArray[$clientDocVal]['notes']);
                                        }
                                    }
                                    if (count($missingDocInfo) > 0) {
                                        if (array_key_exists($CLType, $missingDocInfo)) {
                                            $missingDocNewInfo = $missingDocInfo[$CLType];
                                        }
                                    }
                                    if (count($missingDocNewInfo) > 0) {
                                        if (array_key_exists($clientDocVal, $missingDocNewInfo)) $showDoc = 0;
                                    }
                                    $docName = trim($tempClientArray[$d1]['docName']);

                                    //$missingType = $tempMissingType; /** We have removed the Dynamically                                              displayed the Module Name on                                                Nov 22, 2016 **/

                                    if (array_key_exists($PCCheckServiceType, $LMRClientTypeArray)) $missingType = '<b>' . $LMRClientTypeArray[$PCCheckServiceType]['serviceType'] . '</b>';

                                    if ($tempPCCheckServiceType != $PCCheckServiceType) {
                                        if ($mc > 0) {
                                            $missingDocNameListWithNotes .= '</p></ol><ol><p>';
                                            $missingDocNameList .= '</p></ol><ol><p>';
                                        }
                                        if ($mc1 > 0) {
                                            $unCheckedDocsWithNotes .= '</p></ol><ol><p>';
                                            $missingDocList .= '</p></ol><ol><p>';
                                        }
                                        $mc = 0;
                                        $mc1 = 0;
                                    }

                                    if ($showDoc == 0) {
                                        if ($mc == 0) {
                                            $missingDocNameList .= $missingType;
                                            $missingDocNameListWithNotes .= $missingType;
                                        }
                                        $missingDocNameList .= '<li> <b>' . $docName;
                                        $missingDocNameListWithNotes .= '<li> <b>' . $docName . '</b>';
                                        if ($checklistNotes != '') {
                                            $missingDocNameListWithNotes .= ' - <i>' . $checklistNotes . '</i>';
                                        }
                                        $missingDocNameList .= '</b></li>';
                                        $missingDocNameListWithNotes .= '</li>';
                                        $mc++;
                                    } else {
                                        if ($mc1 == 0) {
                                            $missingDocList .= $missingType;
                                            $unCheckedDocsWithNotes .= $missingType;
                                        }
                                        $missingDocList .= '<li> <b>' . $docName;
                                        $unCheckedDocsWithNotes .= '<li> <b>' . $docName . '</b>';
                                        if ($checklistNotes != '') {
                                            $unCheckedDocsWithNotes .= ' - <i>' . $checklistNotes . '</i>';
                                        }
                                        $missingDocList .= '</b></li>';
                                        $unCheckedDocsWithNotes .= '</li>';
                                        $mc1++;
                                    }
                                    $tempPCCheckServiceType = $PCCheckServiceType;
                                }

                                $missingDocNameList .= '</p></ol>';
                                $missingDocList .= '</p></ol>';
                                $missingDocNameListWithNotes .= '</p></ol>';
                                $unCheckedDocsWithNotes .= '</p></ol>';
                            }

                            if ($s1 > 0) {
                                $missingDocNameListInfo .= '<br>';
                                $missingDocListInfo .= '<br>';
                                $missingDocsLetterWithNotes .= '<br>';
                                $unCheckedDocsListWithNotes .= '<br>';
                            }

                            $missingDocNameListInfo .= $missingDocNameList;
                            $missingDocListInfo .= $missingDocList;
                            $missingDocsLetterWithNotes .= $missingDocNameListWithNotes;
                            $unCheckedDocsListWithNotes .= $unCheckedDocsWithNotes;
                        }
                    }
                }

                $inArray['missingDocsLetter'] = $missingDocNameListInfo;
                $inArray['unCheckedDocs'] = $missingDocListInfo;
                $inArray['missingDocsLetterWithNotes'] = $missingDocsLetterWithNotes;
                $inArray['unCheckedDocsWithNotes'] = $unCheckedDocsListWithNotes;
            }
            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }

            /* Missing Docs Info */

            /**
             * Description : Borrower Missing Document Template
             * Date        : May 03, 2017
             * Author      : Viji & Venkatesh, Suresh
             * Developer   : Suresh
             * Included    : ##Borrower Missing Docs## Tag Dynamic Values.
             **/
            if ((in_array('##Required Doc Status##', $allMergeTagsInContent)
                    || in_array('##Docs Pending Submission - Borrower##', $allMergeTagsInContent)
                    || in_array('##Required Doc Status Branch##', $allMergeTagsInContent)
                    || in_array('##Required Doc Status Broker##', $allMergeTagsInContent)
                    || in_array('##Required Doc Status Loan Officer##', $allMergeTagsInContent)
                    || in_array('##All Required Doc Status##', $allMergeTagsInContent)
                    || in_array('##Docs Pending Submission - BO##', $allMergeTagsInContent))
                || (count($allMergeTagsInContent) == 0)
            ) {
                $reqDocStatusForArray = ['Borrower', 'Branch', 'Broker', 'Loan Officer', 'All'];

                $hideRequiredDocColumn = glCustomJobForProcessingCompany::hideRequiredDocColumn($PCID);

                for ($stat = 0; $stat < count($reqDocStatusForArray); $stat++) {
                    $mergeTableData = [];
                    $mergeTableDataSubmission = [];
                    $mergeTableDataSubmissionBorrower = [];
                    /**
                     * Google Docs table headers.
                     */
                    if(!$hideRequiredDocColumn){
                        $mergeTableData[] = [
                            ['fieldColor' => '', 'fieldText' => 'Status'],
                            ['fieldColor' => '', 'fieldText' => 'Item Name'],
                            ['fieldColor' => '', 'fieldText' => 'File Name(s)'],
                            ['fieldColor' => '', 'fieldText' => 'Uploaded By/Date'],
                            ['fieldColor' => '', 'fieldText' => 'Notes'],
                            ['fieldColor' => '', 'fieldText' => 'Notes Underwriting'],
                        ];
                    }else{
                        $mergeTableData[] = [
                            ['fieldColor' => '', 'fieldText' => 'Status'],
                            ['fieldColor' => '', 'fieldText' => 'Item Name'],
                            ['fieldColor' => '', 'fieldText' => 'Notes'],
                        ];
                    }

                    $mergeTableDataSubmission[] = [
                        ['fieldColor' => '', 'fieldText' => 'Status'],
                        ['fieldColor' => '', 'fieldText' => 'Item Name'],
                        ['fieldColor' => '', 'fieldText' => 'File Name(s)'],
                        ['fieldColor' => '', 'fieldText' => 'Uploaded By/Date'],
                        ['fieldColor' => '', 'fieldText' => 'Notes'],
                        ['fieldColor' => '', 'fieldText' => 'Notes Underwriting'],
                    ];

                    $mergeTableDataSubmissionBorrower[] = [
                        ['fieldColor' => '', 'fieldText' => 'Status'],
                        ['fieldColor' => '', 'fieldText' => 'Item Name'],
                    ];

                    $tempStr = '';
                    $fileRequiredDocs = [];
                    if ($reqDocStatusForArray[$stat] == 'Borrower') {
                        if (isset($borrowerMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']])) {
                            $fileRequiredDocs = $borrowerMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']];
                        }
                    } elseif ($reqDocStatusForArray[$stat] == 'Branch') {
                        if (isset($branchMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']])) {
                            $fileRequiredDocs = $branchMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']];
                        }
                    } elseif ($reqDocStatusForArray[$stat] == 'Broker') {
                        if (isset($brokerMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']])) {
                            $fileRequiredDocs = $brokerMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']];
                        }
                    } elseif ($reqDocStatusForArray[$stat] == 'Loan Officer') {
                        if (isset($loanOfficerMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']])) {
                            $fileRequiredDocs = $loanOfficerMissingDoc[$fileModuleInfoArray[$LMRID][0]['moduleCode']];
                        }
                    } elseif (isset($allMissingDocs[$fileModuleInfoArray[$LMRID][0]['moduleCode']])) {
                        $fileRequiredDocs = $allMissingDocs[$fileModuleInfoArray[$LMRID][0]['moduleCode']];
                    }

                    $requiredDocsForinternalLoanPrograms = [];
                    if (isset($LMRInternalLoanprogramsFiles[$LMRID])) {
                        $requiredDocsForinternalLoanPrograms = requiredDocsForInternalLoanProgram::getReport([
                            'serviceTypes' => implode(',', $LMRInternalLoanprogramsFiles[$LMRID]),
                            'PCID'         => $PCID,
                            'LMRId'        => $LMRID,
                            'requiredBy'   => $reqDocStatusForArray[$stat],
                        ]);
                    }

                    $requiredDocsForadditionalLoanPrograms = [];
                    if (isset($LMRadditionalLoanprogramsFiles[$LMRID])) {
                        $requiredDocsForadditionalLoanPrograms = requiredDocsForAdditionalLoanProgram::getReport([
                            'serviceTypes' => implode(',', $LMRadditionalLoanprogramsFiles[$LMRID]),
                            'PCID'         => $PCID,
                            'LMRId'        => $LMRID,
                            'requiredBy'   => $reqDocStatusForArray[$stat],
                        ]);
                    }

                    $fileRequiredDocs = array_merge(
                        $fileRequiredDocs,
                        $requiredDocsForinternalLoanPrograms,
                        $requiredDocsForadditionalLoanPrograms
                    );

                    $tempFileRequiredDocs = [];

                    $requiredDocsArrayNewPCMID = Arrays::buildKeyByValue($fileRequiredDocs, 'PCMID');
                    $PMID_Keys = implode(',', array_filter(array_keys($requiredDocsArrayNewPCMID)));

                    $additionalLogicResult = [];
                    if ($PMID_Keys != '') {
                        $additionalLogicResult = getRequiredDocsAdditionalLogic::getReport(['PCMIDs' => $PMID_Keys, 'PCID' => $PCID]);
                    }
                    $requiredAdditionalCond = [];
                    $requiredAdditionalCond['typeOfHMLOLoanRequesting'] = trim($HMLOPropInfo['typeOfHMLOLoanRequesting']);
                    $requiredAdditionalCond['isHouseProperty'] = trim($propertyInfo['isHouseProperty']);

                    if (Property::$primaryPropertyInfo) {
                        $requiredAdditionalCond['propertyType'] = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType;
                        $requiredAdditionalCond['propertyState'] = Property::$primaryPropertyInfo->propertyState;
                    }

                    $requiredAdditionalCond['entityType'] = $HMLOBusinessEntity[$LMRID]['entityType'];
                    $requiredAdditionalCond['entityState'] = $HMLOBusinessEntity[$LMRID]['entityState'];
                    if (array_key_exists($LMRID, $HMLOInfoArray)) {
                        $HMLOInfo = $HMLOInfoArray[$LMRID];
                        $requiredAdditionalCond['borrowerCreditScore'] = trim($HMLOInfo['borCreditScoreRange']);
                    } else {
                        $requiredAdditionalCond['borrowerCreditScore'] = '';
                    }
                    $requiredAdditionalCond['isCoBorrower'] = trim($tblFileInfo['isCoBorrower']);
                    $requiredAdditionalCond['propertyNeedRehab'] = trim($HMLOPropInfo['propertyNeedRehab']);
                    $requiredAdditionalCond['isBlanketLoan'] = trim($HMLOPropInfo['isBlanketLoan']);
                    $requiredAdditionalCond['isBorUSCitizen'] = $fileHMLOBackGroundInfoArray[$LMRID]['isBorUSCitizen'];
                    $requiredAdditionalCond['borrowerType'] = $HMLOBusinessEntity[$LMRID]['borrowerType'];

                    $FileStatusByName = [];

                    $FileStatus = getDocStatusFileName::getReport(['LMRID' => $LMRID, 'PCID' => $PCID]);

                    $FileStatusByName = Arrays::buildKeyByValue($FileStatus, 'docName');
                    $fileRequiredDocs = BaseHTML::docAdditionalLogic($additionalLogicResult, $requiredAdditionalCond, $fileRequiredDocs);

                    $temp = PageVariables::$PCID;
                    PageVariables::$PCID = $PCID;
                    docStatusArray::reset();
                    docStatusArray::Init($PCID);
                    PageVariables::$PCID = $temp;


                    foreach ($fileRequiredDocs as $rd => $fileRequired) {


                        $docNotes = '';

                        $CLType = $fileRequired['CLType'];
                        $docName = $fileRequired['docName'];
                        $displayDocName = $fileRequired['displayDocName'];

                        /* File Not required docs */
                        if (isset($cklistNotRequiredInfo[$CLType][$fileRequired['CID']])) {
                            continue;
                        }
                        if (isset($cklistNotRequiredInfoName[$fileRequired['docName']])) {
                            continue;
                        }
                        if (isset($clientServiceTypeInfo[$LMRID])) {
                            if (!isset($fileRequired['internalLoanPrograms'])) {
                                if (trim($fileRequired['internalLoanPrograms']) != '') {
                                    doNothing();
                                } elseif (($fileRequired['serviceType'] != $clientServiceTypeInfo[$LMRID][0]['ClientType'])
                                    && $CLType != 'FCL') {
                                    continue;
                                }

                            }

                        }

                        /* File level required docs */
                        if ($CLType == 'FCL' && $fileRequired['fileID'] == $LMRID) {

                            if (isset($tempFileRequiredDocs[$docName])) {
                                if ($LMRID == $fileRequired['docFileId']) {
                                    if ($displayDocName != '') {
                                        $tempFileRequiredDocs[$docName]['displayDocName'] = $tempFileRequiredDocs[$docName]['displayDocName'] . "\n" . $displayDocName;
                                    } else {
                                        $tempFileRequiredDocs[$docName]['displayDocName'] = $displayDocName;
                                    }
                                }
                                if (isset($FileStatusByName[$docName])) {
                                    $tempFileRequiredDocs[$docName]['docStatus'] = $FileStatusByName[$docName][0]['docStatus'];
                                }
                            } else {
                                $tempFileRequiredDocs[$docName] = $fileRequired;

                                if ($LMRID != $fileRequired['docFileId']) {
                                    $tempFileRequiredDocs[$docName]['displayDocName'] = '';
                                    $tempFileRequiredDocs[$docName]['uploadedBy'] = '';
                                    $tempFileRequiredDocs[$docName]['uploadedDate'] = '';
                                    $tempFileRequiredDocs[$docName]['uploadingUserType'] = '';
                                }

                                if (isset($FileStatusByName[$docName])) {
                                    $tempFileRequiredDocs[$docName]['docStatus'] = $FileStatusByName[$docName][0]['docStatus'];
                                }
                                if (isset($checklistNotesInfoNew[$CLType])) {
                                    $tempFileRequiredDocs[$docName]['docNotes'] = '';
                                    $tempFileRequiredDocs[$docName]['docNotesUnderwriting'] = '';
                                    foreach ($checklistNotesInfoNew[$CLType][$docName] as $note) {
                                        if ($note['notesType'] == glFUModulesNotesTypeArray::INTERNAL) {
                                            continue;
                                        }
                                        if ($note['notesType'] == glFUModulesNotesTypeArray::UNDERWRITING) {
                                            $tempFileRequiredDocs[$docName]['docNotesUnderwriting'] .= $note['notes'] . '<br/>';
                                            continue;
                                        }
                                        $tempFileRequiredDocs[$docName]['docNotes'] .= $note['notes'] . '<br/>';
                                    }
                                }
                            }
                        }

                        /* PC level required docs */
                        if ($CLType == 'PCL' && $fileRequired['PCID'] == $PCID) {

                            if (isset($tempFileRequiredDocs[$docName])) {
                                if ($LMRID == $fileRequired['docFileId']) {
                                    if ($displayDocName != '') {
                                        $tempFileRequiredDocs[$docName]['displayDocName'] = $tempFileRequiredDocs[$docName]['displayDocName'] . "\n" . $displayDocName;
                                    } else {
                                        $tempFileRequiredDocs[$docName]['displayDocName'] = $displayDocName;
                                    }
                                }

                                if (isset($FileStatusByName[$docName])) {
                                    $tempFileRequiredDocs[$docName]['docStatus'] = $FileStatusByName[$docName][0]['docStatus'];
                                }
                            } else {
                                $tempFileRequiredDocs[$docName] = $fileRequired;

                                if ($LMRID != $fileRequired['docFileId']) {
                                    $tempFileRequiredDocs[$docName]['displayDocName'] = '';
                                    $tempFileRequiredDocs[$docName]['uploadedBy'] = '';
                                    $tempFileRequiredDocs[$docName]['uploadedDate'] = '';
                                    $tempFileRequiredDocs[$docName]['uploadingUserType'] = '';
                                }

                                if (isset($FileStatusByName[$docName])) {
                                    $tempFileRequiredDocs[$docName]['docStatus'] = $FileStatusByName[$docName][0]['docStatus'];
                                }
                                if (isset($checklistNotesInfoNew[$CLType])) {
                                    $tempFileRequiredDocs[$docName]['docNotes'] = '';
                                    $tempFileRequiredDocs[$docName]['docNotesUnderwriting'] = '';
                                    foreach ($checklistNotesInfoNew[$CLType][$docName] as $note) {
                                        if ($note['notesType'] == glFUModulesNotesTypeArray::INTERNAL) {
                                            continue;
                                        }
                                        if ($note['notesType'] == glFUModulesNotesTypeArray::UNDERWRITING) {
                                            $tempFileRequiredDocs[$docName]['docNotesUnderwriting'] .= $note['notes'] . '<br/>';
                                            continue;
                                        }
                                        $tempFileRequiredDocs[$docName]['docNotes'] .= $note['notes'] . '<br/>';
                                    }
                                }
                            }
                        }
                    }

                    $fileRequiredDocs = array_values($tempFileRequiredDocs);
                    if (count($fileRequiredDocs) > 0) {

                        $tempStr = "
<table border=\"1\" cellpadding=\"4\" width=\"100%\">
<tr>
<td><b>Status</b></td>
<td><b>Item Name</b></td>
<td style='$hideRequiredDocColumn'><b>File Name(s)</b></td>
<td style='$hideRequiredDocColumn'><b>Uploaded By/Date</b></td>
<td><b>Borrower/Broker Notes</b></td>
<td style='$hideRequiredDocColumn'><b>Underwriting Conditions</b></td>
</tr>";
                        $tempStrSubmission = $tempStr;
                        $tempStrSubmissionBorrower = "<table border=\"1\" cellpadding=\"4\" width=\"100%\">";

                        usort($fileRequiredDocs, function ($a, $b) {
                            return $a['displayOrder'] - $b['displayOrder'];
                        });

                        foreach ($fileRequiredDocs as $key => $fileRequired) {
                            $docStatus = $fileRequired['docStatus'] ?: docStatusArray::pendingStatusId($PCID);
                            $docStatusSortOrder = docStatusArray::getStatus($docStatus)->sortOrder;
                            $fileRequiredDocs[$key]['finalDisplayOrder'] = Strings::ZeroPad($docStatusSortOrder, 4) . '-' . Strings::ZeroPad($fileRequired['displayOrder'], 4) . '- ' . $fileRequired['docName'];
                        }

                        usort($fileRequiredDocs, function ($a, $b) {
                            return strcmp($a['finalDisplayOrder'], $b['finalDisplayOrder']);
                        });

                        foreach ($fileRequiredDocs as $rd => $fileRequired) {

                            $myUploadedBy = $docStatus = '';
                            $docStatus = $fileRequired['docStatus'] ?: docStatusArray::pendingStatusId($PCID);
                            $docName = $fileRequired['docName'];

                            $docStatusInt = $docStatus;
                            $docStatus = docStatusArray::getStatus($docStatus)->name;
                            $statusCls = docStatusArray::getStyle($docStatusInt);
                            $docStatusID = docStatusArray::getDocumentStatusId($docStatusInt, $PCID);
                            $docUploadStatus = docStatusArray::getDocumentStatusEnabledDisabled($docStatusInt, $PCID);
                            $colorCode = null;
                            $colorCode = docStatusArray::getDocStatusColourCode($docStatusID);

                            /* Employee information */
                            if (in_array($fileRequired['uploadingUserType'], ['Processor', 'Employee'])) {
                                $empInfoArray = getMyDetailsEmployee::getReport(['empId' => $fileRequired['uploadedBy']]);

                                if (isset($empInfoArray[$fileRequired['uploadedBy']])) {
                                    $myUploadedBy = $empInfoArray[$fileRequired['uploadedBy']]['processorName'] . ' ' . $empInfoArray[$fileRequired['uploadedBy']]['processorLName'] . ' - (' . $fileRequired['uploadingUserType'] . ')';
                                }
                            }
                            /* Branch information */
                            if ($fileRequired['uploadingUserType'] == 'Branch') {
                                $brInfoArray = getMyDetailsBranch::getReport(['executiveId' => $fileRequired['uploadedBy']]);

                                if (isset($brInfoArray[$fileRequired['uploadedBy']])) {
                                    $myUploadedBy = $brInfoArray[$fileRequired['uploadedBy']]['LMRExecutive'] . ' - (' . $fileRequired['uploadingUserType'] . ')';
                                }
                            }
                            /* Agent information */
                            if ($fileRequired['uploadingUserType'] == 'Agent') {
                                $agInfoArray = getMyDetailsAgent::getReport(['agentId' => $fileRequired['uploadedBy']]);

                                if (isset($agInfoArray[$fileRequired['uploadedBy']])) {
                                    $myUploadedBy = $agInfoArray[$fileRequired['uploadedBy']]['brokerName'] . ' - (' . $fileRequired['uploadingUserType'] . ')';
                                }
                            }
                            /* Client information */
                            if ($fileRequired['uploadingUserType'] == 'Client') {
                                if ($fileRequired['uploadedBy'] != '') {
                                    if ($fileRequired['uploadedBy'] > 0) {
                                        $clientInfoArray = getMyDetailsClient::getReport(['clientId' => $fileRequired['uploadedBy']]);

                                        if (isset($clientInfoArray[$fileRequired['uploadedBy']])) {
                                            $myUploadedBy = $clientInfoArray[$fileRequired['uploadedBy']]['clientFName'] . ' ' . $clientInfoArray[$fileRequired['uploadedBy']]['clientLName'] . ' - (' . $fileRequired['uploadingUserType'] . ')';
                                        }

                                    }
                                }

                            }
                            $docNotes = trim(preg_replace('/\s\s+/', "\n", $fileRequired['docNotes']));
                            $docNotesUnderwriting = trim(preg_replace('/\s\s+/', "\n", $fileRequired['docNotesUnderwriting']));
                            $docName = trim(preg_replace('/\s\s+/', "\n", $docName));
                            $displayDocName = trim(preg_replace('/\s\s+/', "\n", $fileRequired['displayDocName']));
                            $uploadedDate = Dates::formatDateWithRE($fileRequired['uploadedDate'], 'YMD', 'm/d/Y');

                            if ($fileRequired['hideForAdditionalLogic'] == '') {

                                $tempStr .= '<tr
                                data-doc_status="' . $docStatusInt . '" 
                                data-cid="' . $fileRequired['CID'] . '"
                                ' . $hideTr . ">
                                <td style=\"$statusCls\">" . $docStatus . '</td>
                                <td>' . $docName . '</td>
                                <td style="'.$hideRequiredDocColumn.'">' . $displayDocName . '</td>
                                <td style="'.$hideRequiredDocColumn.'">' . $myUploadedBy . '<br>' . $docUploadedDate . '</td>
                                <td>' . $docNotes . '</td>
                                <td style="'.$hideRequiredDocColumn.'">' . $docNotesUnderwriting . '</td>
                                </tr>';
                                if(!$hideRequiredDocColumn){
                                $mergeTableData[] = [
                                    ['fieldColor' => $colorCode, 'fieldText' => $docStatus],
                                    ['fieldColor' => '', 'fieldText' => $docName],
                                    ['fieldColor' => '', 'fieldText' => $displayDocName],
                                    ['fieldColor' => '', 'fieldText' => $myUploadedBy . ' ' . $uploadedDate],
                                    ['fieldColor' => '', 'fieldText' => $docNotes],
                                    ['fieldColor' => '', 'fieldText' => $docNotesUnderwriting],
                                ];
                                }else{
                                    $mergeTableData[] = [
                                    ['fieldColor' => $colorCode, 'fieldText' => $docStatus],
                                    ['fieldColor' => '', 'fieldText' => $docName],
                                    ['fieldColor' => '', 'fieldText' => $docNotes],
                                        ];
                                }
                                if (in_array($docStatusID, [9, 8, 5, 7, 3])) {
                                    $tempStrSubmission .= '<tr ' . $hideTr . "><td style=\"$statusCls\">" . $docStatus . '</td><td>' . $docName . '</td><td>' . $displayDocName . '</td><td>' . $myUploadedBy . '<br>' . $docUploadedDate . '</td><td>' . $docNotes . '</td></tr>';

                                    $mergeTableDataSubmission[] = [
                                        ['fieldColor' => $colorCode, 'fieldText' => $docStatus],
                                        ['fieldColor' => '', 'fieldText' => $docName],
                                        ['fieldColor' => '', 'fieldText' => $displayDocName],
                                        ['fieldColor' => '', 'fieldText' => $myUploadedBy . ' ' . $uploadedDate],
                                        ['fieldColor' => '', 'fieldText' => $docNotes],
                                        ['fieldColor' => '', 'fieldText' => $docNotesUnderwriting],
                                    ];


                                    if (!$docUploadStatus) { // If Not disabledUpload then show else hide
                                        $tempStrSubmissionBorrower .= '<tr ' . $hideTr . "><td style=\"$statusCls\">" . $docStatus . '</td><td>' . $docName . '</td></tr>';
                                        $mergeTableDataSubmissionBorrower[] = [
                                            [
                                                'fieldColor' => $colorCode,
                                                'fieldText'  => $docStatus
                                            ],
                                            [
                                                'fieldColor' => '',
                                                'fieldText'  => $docName
                                            ],
                                        ];
                                    }
                                }
                            }
                        }
                        $tempStr .= '</table>';
                        $tempStrSubmission .= '</table>';
                        $tempStrSubmissionBorrower .= '</table>';

                        if ($googleDocs == 1) {
                            if ($reqDocStatusForArray[$stat] == 'Borrower') {
                                $inArray['tables'][] = [
                                    'mergeTableTag'  => '##Required Doc Status##',
                                    'mergeTableData' => $mergeTableData,
                                ];
                                $inArray['tables'][] = [
                                    'mergeTableTag'  => '##Docs Pending Submission - Borrower##',
                                    'mergeTableData' => $mergeTableDataSubmissionBorrower,
                                ];
                            } elseif ($reqDocStatusForArray[$stat] == 'Branch') {
                                $inArray['tables'][] = [
                                    'mergeTableTag'  => '##Required Doc Status Branch##',
                                    'mergeTableData' => $mergeTableData,
                                ];
                            } elseif ($reqDocStatusForArray[$stat] == 'Broker') {
                                $inArray['tables'][] = [
                                    'mergeTableTag'  => '##Required Doc Status Broker##',
                                    'mergeTableData' => $mergeTableData,
                                ];
                            } elseif ($reqDocStatusForArray[$stat] == 'Loan Officer') {
                                $inArray['tables'][] = [
                                    'mergeTableTag'  => '##Required Doc Status Loan Officer##',
                                    'mergeTableData' => $mergeTableData,
                                ];
                            } else {
                                $inArray['tables'][] = [
                                    'mergeTableTag'  => '##All Required Doc Status##',
                                    'mergeTableData' => $mergeTableData,
                                ];
                                $inArray['tables'][] = [
                                    'mergeTableTag'  => '##Docs Pending Submission - BO##',
                                    'mergeTableData' => $mergeTableDataSubmission,
                                ];
                            }
                        }

                        if ($googleDocs != 1) {
                            $tempStr = str_replace('$', '&dollar;', $tempStr);  //ch24688 this line!
                            $tempStrSubmissionBorrower = str_replace('$', '&dollar;', $tempStrSubmissionBorrower); //sc30218 this line!
                            $tempStrSubmission = str_replace('$', '&dollar;', $tempStrSubmission);  //sc30218 this line!
                            if ($reqDocStatusForArray[$stat] == 'Borrower') {
                                $inArray['borrowerMissingDocsInfo'] = $tempStr;
                                $inArray['requiredDocsSubmissionBorrower'] = $tempStrSubmissionBorrower;
                            } elseif ($reqDocStatusForArray[$stat] == 'Branch') {
                                $inArray['branchMissingDocsInfo'] = $tempStr;
                            } elseif ($reqDocStatusForArray[$stat] == 'Broker') {
                                $inArray['brokerMissingDocsInfo'] = $tempStr;
                            } elseif ($reqDocStatusForArray[$stat] == 'Loan Officer') {
                                $inArray['loanOfficerMissingDocsInfo'] = $tempStr;
                            } else {
                                $inArray['allRequiredDocsInfo'] = $tempStr;
                                $inArray['requiredDocsSubmission'] = $tempStrSubmission;
                            }
                        }
                    }
                }
            }
            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /* Borrower Missing Doc Template Creation May 04, 2017 */

            /* Broker Info */
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                $inArray['brokerOriginatorNMLSId'] = 'None';
                $inArray['brokerOriginatingCoStateLicenseId'] = 'None';
                $inArray['brokerOriginatorStateLicenseId'] = 'None';
            }
            if (array_key_exists($brokerNumber, $BrokerInfo)) {
                $inArray['agentName'] = ucwords(trim($BrokerInfo[$brokerNumber]['firstName']) . ' ' . trim($BrokerInfo[$brokerNumber]['lastName']));
                $inArray['brokerPoitsPayee'] = ucwords(trim($BrokerInfo[$brokerNumber]['firstName']) . ' ' . trim($BrokerInfo[$brokerNumber]['lastName']));
                $inArray['brokerFeePayee'] = ucwords(trim($BrokerInfo[$brokerNumber]['firstName']) . ' ' . trim($BrokerInfo[$brokerNumber]['lastName']));
                $inArray['agentFirstName'] = ucwords(trim($BrokerInfo[$brokerNumber]['firstName']));
                $inArray['agentLastName'] = ucwords(trim($BrokerInfo[$brokerNumber]['lastName']));
                $inArray['agentCompany'] = ucwords(trim($BrokerInfo[$brokerNumber]['company']));
                $inArray['agentAddress'] = ucwords(trim($BrokerInfo[$brokerNumber]['addr']));
                $inArray['agentCity'] = ucwords(trim($BrokerInfo[$brokerNumber]['city']));
                $inArray['agentState'] = trim($BrokerInfo[$brokerNumber]['state']);
                $inArray['agentPhone'] = Strings::formatPhoneNumber(trim($BrokerInfo[$brokerNumber]['phoneNumber']));
                $inArray['agentCell'] = Strings::formatPhoneNumber(trim($BrokerInfo[$brokerNumber]['cellNumber']));
                $inArray['agentFax'] = Strings::formatPhoneNumber(trim($BrokerInfo[$brokerNumber]['fax']));
                $inArray['agentZip'] = trim($BrokerInfo[$brokerNumber]['zipCode']);
                $inArray['agentEmail'] = trim($BrokerInfo[$brokerNumber]['email']);
                $inArray['agentWebsite'] = trim($BrokerInfo[$brokerNumber]['website']);
                $inArray['agentNMLS'] = trim($BrokerInfo[$brokerNumber]['NMLSLicense']);
                $inArray['agentDRE'] = trim($BrokerInfo[$brokerNumber]['DRE']);
                if ($brokerNumber) {
                    $brokerInfoMultipleArray = getBrokerInfo::getReport(['brokerNumber' => $brokerNumber, 'opt' => 'All']);
                }
                $brokerWebForms = getWebFormsForBroker::getReport(['brokerInfoMultipleArray' => $brokerInfoMultipleArray, 'brokerNumber' => $brokerNumber, 'PCID' => $PCID]);

                $inArray['brokerfullappwebform'] = trim($brokerWebForms[$brokerNumber]['FA']);
                $inArray['brokerquickappwebform'] = trim($brokerWebForms[$brokerNumber]['QA']);

                if (trim($BrokerInfo[$brokerNumber]['firstName']) == 'null') {
                    $inArray['agentName'] = '';
                    $inArray['agentFirstName'] = '';
                    $inArray['agentEmail'] = '';
                }
                //Broker Logo
                $brokerLogoSrc = '';
                $brokerLogo = trim($BrokerInfo[$brokerNumber]['logo']) ?? '';
                if ($brokerLogo) {
                    $brokerLogoSrc = "<img src=\"" . $CONST_SITE_URL . 'brokerLogo/' . $brokerLogo . "\">";
                }
                $inArray['brokerLogo'] = $brokerLogoSrc;
                $inArray['brokerLogoURL'] = $brokerLogo ? $CONST_SITE_URL . 'brokerLogo/' . $brokerLogo : '';

                if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                    $inArray['brokerOriginatorNMLSId'] = LMRequest::getBrokerOriginatorNMLSID();
                    $inArray['brokerOriginatingCoStateLicenseId'] = LMRequest::getBrokerCompanyStateLicense();
                    $inArray['brokerOriginatorStateLicenseId'] = LMRequest::getBrokerPersonalStateLicense();
                }
            }
//pr($LoanOfficerInfo);die();
            /* Loan Officer Info */
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                $inArray['lenderOriginatorNMLSId'] = 'None';
                $inArray['originatingCoStateLicenseId'] = 'None';
                $inArray['lenderOriginatorStateLicenseId'] = 'None';
            }
            if (array_key_exists($loanOfficerNumber, $LoanOfficerInfo)) {
                $inArray['loanOfficerName'] = ucwords(trim($LoanOfficerInfo[$loanOfficerNumber]['firstName']) . ' ' . trim($LoanOfficerInfo[$loanOfficerNumber]['lastName']));
                $inArray['loanOfficerFName'] = ucwords(trim($LoanOfficerInfo[$loanOfficerNumber]['firstName']));
                $inArray['loanOfficerLName'] = ucwords(trim($LoanOfficerInfo[$loanOfficerNumber]['lastName']));
                $inArray['loanOfficerCompany'] = ucwords(trim($LoanOfficerInfo[$loanOfficerNumber]['company']));
                $inArray['loanOfficerAddress'] = ucwords(trim($LoanOfficerInfo[$loanOfficerNumber]['addr']));
                $inArray['loanOfficerCity'] = ucwords(trim($LoanOfficerInfo[$loanOfficerNumber]['city']));
                $inArray['loanOfficerState'] = trim($LoanOfficerInfo[$loanOfficerNumber]['state']);
                $inArray['loanOfficerPhone'] = Strings::formatPhoneNumber(trim($LoanOfficerInfo[$loanOfficerNumber]['phoneNumber']));
                $inArray['loanOfficerCell'] = Strings::formatPhoneNumber(trim($LoanOfficerInfo[$loanOfficerNumber]['cellNumber']));
                $inArray['loanOfficerFax'] = Strings::formatPhoneNumber(trim($LoanOfficerInfo[$loanOfficerNumber]['fax']));
                $inArray['loanOfficerZip'] = trim($LoanOfficerInfo[$loanOfficerNumber]['zipCode']);
                $inArray['loanOfficerEmail'] = trim($LoanOfficerInfo[$loanOfficerNumber]['email']);
                $inArray['loanOfficerWebsite'] = trim($LoanOfficerInfo[$loanOfficerNumber]['website']);
                $inArray['loanOfficerNMLS'] = trim($LoanOfficerInfo[$loanOfficerNumber]['NMLSLicense']);
                $inArray['loanOfficerLicNum'] = trim($LoanOfficerInfo[$loanOfficerNumber]['license']);
                $inArray['loanOfficerDRE'] = trim($LoanOfficerInfo[$loanOfficerNumber]['DRE']);
                $inArray['loanOfficerPrefComm'] = trim($LoanOfficerInfo[$loanOfficerNumber]['prefCommunication']);
                $inArray['loanOfficerAvatar'] = $LoanOfficerInfo[$loanOfficerNumber]['avatar'] ? rtrim($CONST_SITE_URL, '/') . $LoanOfficerInfo[$loanOfficerNumber]['avatar'] : '';

                if ($loanOfficerNumber > 0) {
                    $loInfoMultipleArray = getBrokerInfo::getReport(['brokerNumber' => $loanOfficerNumber, 'opt' => 'All']);
                }
                if (count($loInfoMultipleArray) > 0) {
                    $loanOfficerWebForms = getWebFormsForBroker::getReport(['brokerInfoMultipleArray' => $loInfoMultipleArray, 'brokerNumber' => $loanOfficerNumber, 'PCID' => $PCID]);
                    $inArray['loanOfficerfullappwebform'] = trim($loanOfficerWebForms[$loanOfficerNumber]['FA']);
                    $inArray['loanOfficerquickappwebform'] = trim($loanOfficerWebForms[$loanOfficerNumber]['QA']);
                }
                if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                    $inArray['lenderOriginatorNMLSId'] = LMRequest::getLenderOriginatorNMLSID();
                    $inArray['originatingCoStateLicenseId'] = LMRequest::getLoanOfficerCompanyStateLicense();
                    $inArray['lenderOriginatorStateLicenseId'] = LMRequest::getLoanOfficerPersonalStateLicense();
                }

            }
            /* Loan Officer Info */

            /* Agent Info */
            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }

            /* Branch Info */
            //pr($BranchInfo);
            if (array_key_exists($executiveID, $BranchInfo)) {
                $inArray['branchName'] = ucwords(trim($BranchInfo[$executiveID]['LMRExecutive']));
                $inArray['branchCompany'] = ucwords(trim($BranchInfo[$executiveID]['company']));
                $inArray['branchLocalPhone'] = Strings::formatPhoneNumber(trim($BranchInfo[$executiveID]['directPhone']));
                $inArray['branchTollFree'] = Strings::formatPhoneNumber(trim($BranchInfo[$executiveID]['tollFree']));
                $inArray['branchCellNumber'] = Strings::formatPhoneNumber(trim($BranchInfo[$executiveID]['cellNumber']));
                $inArray['branchFax'] = Strings::formatPhoneNumber(trim($BranchInfo[$executiveID]['fax']));
                $inArray['branchAddress'] = ucwords(trim($BranchInfo[$executiveID]['address']));
                $inArray['branchCity'] = ucwords(trim($BranchInfo[$executiveID]['city']));
                $inArray['branchState'] = trim($BranchInfo[$executiveID]['state']);
                $inArray['branchZipCode'] = trim($BranchInfo[$executiveID]['zipCode']);
                $inArray['branchEmail'] = trim($BranchInfo[$executiveID]['executiveEmail']);
                $inArray['branchWebsite'] = trim($BranchInfo[$executiveID]['website']);
                $inArray['branchStateLong'] = trim($BranchInfo[$executiveID]['brStateLongName']);
                $branchLogo = '';
                $branchLogo = trim($BranchInfo[$executiveID]['logo']);
                if (trim($branchLogo) != '') {
                    $inArray['branchLogo'] = "<img src=\"" . $CONST_SITE_URL . 'branchLogo/' . $branchLogo . "\">";
                } else {
                    $inArray['branchLogo'] = '';
                }
                $inArray['branchAvatar'] = $BranchInfo[$executiveID]['avatar'] ? rtrim($CONST_SITE_URL, '/') . $BranchInfo[$executiveID]['avatar'] : '';

                /**
                 * Borrower Full App Completion Link.
                 */
                $promoCodeInfoArray = getPromoCodeForBranch::getReport(['executiveEmails' => trim($BranchInfo[$executiveID]['executiveEmail'])]);
                if (count($promoCodeInfoArray) > 0) {
                    $branchReferralCode = trim($promoCodeInfoArray[strtolower(trim($BranchInfo[$executiveID]['executiveEmail']))]);
                }

                $linkToFullApp = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . "\">Link To Full App</a>";

                $linkToFullAppMultiStep = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&view=' . cypher::myEncryption('wizard') . "\">Link To Full App (Multi Step)</a>";

                $linkToQuickApp = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . "\">Link To Quick App</a>";

                $linkToQuickAppMultiStep = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&view=' . cypher::myEncryption('wizard') . "\">Link To Quick App (Multi Step)</a>";

                $linkToQAWithAccessToDocs = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . "\">QA With Access To Docs</a>";
                $linkToFAWithAccessToDocs = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . "\">FA With Access To Docs</a>";

                $linkToBranchFullApp = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&op=' . cypher::myEncryption('FA') . "\">Branch Link To Full App</a>";
                $linkToBranchQuickApp = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&op=' . cypher::myEncryption('QA') . "\">Branch Link To Quick App</a>";

                $iframeJsURL = $CONST_SITE_URL . '/assets/js/3rdParty/iframeSizer-4.1.1/iframeResizer.min.js';
                $branchiFrameFullApp = "<iframe src=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&op=' . cypher::myEncryption('FA') . "\" name=\"top\" width=\"100%\"  id=\"iframe1\" scrolling=\"no\" frameborder=\"0\" onload=\"window.top.scrollTo(0,0);\"></iframe><script type=\"text/javascript\" src=\"" . $iframeJsURL . "\"></script><script type=\"text/javascript\">iFrameResize({log:true});</script>";

                $branchiFrameFullApp = str_replace('<', '&#60;', $branchiFrameFullApp);
                $branchiFrameFullApp = str_replace('>', '&#62;', $branchiFrameFullApp);

                $branchiFrameQickApp = "<iframe src=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&op=' . cypher::myEncryption('QA') . "\" name=\"top\" width=\"100%\"  id=\"iframe1\" scrolling=\"no\" frameborder=\"0\" onload=\"window.top.scrollTo(0,0);\"></iframe><script type=\"text/javascript\" src=\"" . $iframeJsURL . "\"></script><script type=\"text/javascript\">iFrameResize({log:true});</script>";

                $branchiFrameQickApp = str_replace('<', '&#60;', $branchiFrameQickApp);
                $branchiFrameQickApp = str_replace('>', '&#62;', $branchiFrameQickApp);

                $branchiFrameFullApp = '<div><h4><code>' . $branchiFrameFullApp . '</code></h4></div>';
                $branchiFrameQickApp = '<div><h4><code>' . $branchiFrameQickApp . '</code></h4></div>';
                /**
                 * Broker Full App Completion Link.
                 */
                if (array_key_exists($brokerNumber, $BrokerInfo)) {
                    $preferredLMREmailIds = trim($BrokerInfo[$brokerNumber]['email']);
                    $inArray['brokerAvatar'] = $BrokerInfo[$brokerNumber]['avatar'] ? rtrim($CONST_SITE_URL, '/') . $BrokerInfo[$brokerNumber]['avatar'] : '';

                    $ip = ['executiveEmails' => $preferredLMREmailIds];
                    $agentLMRAffiliateInfoArray = getPromoCodeForBranch::getReport($ip);
                    if (count($agentLMRAffiliateInfoArray) > 0) {
                        $agentLMRAffiliateInfoArray = array_change_key_case($agentLMRAffiliateInfoArray, CASE_LOWER);
                        if (array_key_exists(strtolower(trim($BrokerInfo[$brokerNumber]['email'])), $agentLMRAffiliateInfoArray)) {
                            $agentReferralCode = $agentLMRAffiliateInfoArray[strtolower(trim($BrokerInfo[$brokerNumber]['email']))];

                            if ($googleDocs == 1) {
                                /* Broker link to full app. */
                                $inArray['links'][] = [
                                    'linktag' => '##Link To Full App - Broker##',
                                    'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA'),
                                    'lintext' => 'Broker Link To Full App',
                                ];
                                $inArray['links'][] = [
                                    'linktag' => '##Broker Link To Full App##',
                                    'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA'),
                                    'lintext' => 'Broker Link To Full App',
                                ];
                                /* Broker link to quick app. */
                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['brokerLinkToQuickApp'],
                                    'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA'),
                                    'lintext' => 'Broker Link To Quick App',
                                ];
                                $inArray['links'][] = [
                                    'linktag' => '##Broker Link To Full App Multi-Step##',
                                    'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA') . '&view=' . cypher::myEncryption('wizard'),
                                    'lintext' => 'Broker Link To Full App (Multi-Step)',
                                ];
                                $inArray['links'][] = [
                                    'linktag' => '##Broker Link To Quick App Multi-Step##',
                                    'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA') . '&view=' . cypher::myEncryption('wizard'),
                                    'lintext' => 'Broker Link To Quick App (Multi-Step)',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['QABorrowerRedacted'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink'),
                                    'lintext' => 'QABorrowerRedacted',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['QAReadOnly'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink'),
                                    'lintext' => 'QAReadOnly',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['FAReadOnly'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink'),
                                    'lintext' => 'FAReadOnly',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['FABorInfoRedacted'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes'),
                                    'lintext' => 'FABorInfoRedacted',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['QAReadonlyDocs'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes'),
                                    'lintext' => 'QAReadonlyDocs',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['FAReadonlyDocs'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes'),
                                    'lintext' => 'FAReadonlyDocs',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['QAReadOnlyOS'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&so=' . cypher::myEncryption('yes'),
                                    'lintext' => 'QAReadOnlyOS',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['QAReadOnlyDocOS'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes'),
                                    'lintext' => 'QAReadOnlyDocOS',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['FABorrowerRedactedOS'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes'),
                                    'lintext' => 'FABorrowerRedactedOS',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['FAReadOnlyOS'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&so=' . cypher::myEncryption('yes'),
                                    'lintext' => 'FAReadOnlyOS',
                                ];

                                $inArray['links'][] = [
                                    'linktag' => $dynamicTagValue['FReadOnlyDocOS'],
                                    'linkurl' => $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes'),
                                    'lintext' => 'FReadOnlyDocOS',
                                ];
                            } else {

                                $brokerLinkToFullApp = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA') . "\">Broker Link To Full App</a>";
                                $inArray['brokerLinkToFullApp'] = $brokerLinkToFullApp;

                                $brokerLinkToQuickApp = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA') . "\">Broker Link To Quick App</a>";
                                $inArray['brokerLinkToQuickApp'] = $brokerLinkToQuickApp;

                                $brokerLinkToFullAppMultiStep = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA') . '&view=' . cypher::myEncryption('wizard') . "\">Broker Link To Full App (Multi-Step)</a>";
                                $inArray['brokerLinkToFullAppMultiStep'] = $brokerLinkToFullAppMultiStep;

                                $brokerLinkToQuickAppMultiStep = "<a href =\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&fOpt=' . cypher::myEncryption('agent') . '&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA') . '&view=' . cypher::myEncryption('wizard') . "\">Broker Link To Quick App (Multi-Step)</a>";
                                $inArray['brokerLinkToQuickAppMultiStep'] = $brokerLinkToQuickAppMultiStep;

                                $QABorrowerRedacted = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . "\">Link to File</a>";
                                $inArray['QABorrowerRedacted'] = $QABorrowerRedacted;

                                $QAReadOnly = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $fileModuleCode . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . "\">Link to File</a>";
                                $inArray['QAReadOnly'] = $QAReadOnly;

                                $FAReadOnly = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . "\">Link to File</a>";
                                $inArray['FAReadOnly'] = $FAReadOnly;

                                $FABorInfoRedacted = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['FABorInfoRedacted'] = $FABorInfoRedacted;

                                $QAReadonlyDocs = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['QAReadonlyDocs'] = $QAReadonlyDocs;

                                $FAReadonlyDocs = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['FAReadonlyDocs'] = $FAReadonlyDocs;

                                $QABorrowerRedactedOS = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['QABorrowerRedactedOS'] = $QABorrowerRedactedOS;

                                $QAReadOnlyOS = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&so=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['QAReadOnlyOS'] = $QAReadOnlyOS;

                                $QAReadOnlyDocOS = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['QAReadOnlyDocOS'] = $QAReadOnlyDocOS;

                                $FABorrowerRedactedOS = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&bir=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['FABorrowerRedactedOS'] = $FABorrowerRedactedOS;

                                $FAReadOnlyOS = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&so=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['FAReadOnlyOS'] = $FAReadOnlyOS;

                                $FReadOnlyDocOS = "<a target=\"_blank\" href=\"" . $CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=' . cypher::myEncryption('branch') . '&lid=' . cypher::myEncryption($LMRID) . '&opt=' . cypher::myEncryption('Email') . '&ft=' . $ft . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes') . '&so=' . cypher::myEncryption('yes') . "\">Link to File</a>";
                                $inArray['FReadOnlyDocOS'] = $FReadOnlyDocOS;
                            }
                        }
                    }
                }


                $inArray['branchFullAppiFrameURL'] = $branchiFrameFullApp;
                $inArray['branchQuickAppiFrameURL'] = $branchiFrameQickApp;


                if ($printExecutionTime == 1) {
                    echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
                }
                $requiredDocLink = "<a href =\"" . $CONST_SITE_URL . 'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&UType=' . cypher::myEncryption('Borrower') . '&ft=' . $fileModuleCode . '&UName=' . cypher::myEncryption($borrowerName) . "\">Loan Status And Doc Portal</a>";

                if ($googleDocs == 1) {
                    /* Link to full links. */
                    $inArray['links'][] = [
                        'linktag' => '##Link To Full App - Borrower##',
                        'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA'),
                        'lintext' => 'Link To Full App',
                    ];
                    $inArray['links'][] = [
                        'linktag' => '##Link To Full App##',
                        'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA'),
                        'lintext' => 'Link To Full App',
                    ];
                    /* Link to quick links. */
                    $inArray['links'][] = [
                        'linktag' => $dynamicTagValue['linkToQuickApp'],
                        'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA'),
                        'lintext' => 'Link To Quick App',
                    ];
                    /* Required docs links. */
                    $inArray['links'][] = [
                        'linktag' => $dynamicTagValue['requiredDocLink'],
                        'linkurl' => $vhostLink.'backoffice/uploadLMRDocs.php?lid=' . cypher::myEncryption($LMRID) . '&UType=' . cypher::myEncryption('Borrower') . '&ft=' . $fileModuleCode . '&UName=' . cypher::myEncryption($borrowerName),
                        'lintext' => 'Loan Status And Doc Portal',
                    ];
                    $inArray['links'][] = [
                        'linktag' => $dynamicTagValue['QAAccessToDocs'],
                        'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('QA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes'),
                        'lintext' => 'QA With Access To Docs',
                    ];
                    $inArray['links'][] = [
                        'linktag' => $dynamicTagValue['FAAccessToDocs'],
                        'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&lid=' . cypher::myEncryption($LMRID) . '&ft=' . $fileModuleCode . '&opt=' . cypher::myEncryption('Email') . '&op=' . cypher::myEncryption('FA') . '&sl=' . cypher::myEncryption('shareLink') . '&aud=' . cypher::myEncryption('yes'),
                        'lintext' => 'FA With Access To Docs',
                    ];
                    $inArray['links'][] = [
                        'linktag' => '##Branch Full App URL##',
                        'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&op=' . cypher::myEncryption('FA'),
                        'lintext' => 'Branch Link To Full App',
                    ];
                    $inArray['links'][] = [
                        'linktag' => '##Branch Quick App URL##',
                        'linkurl' => $vhostLink.'HMLOWebForm.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&fOpt=8e614f58c0d670e4&op=' . cypher::myEncryption('QA'),
                        'lintext' => 'Branch Link To Quick App',
                    ];
                } else {
                    $inArray['requiredDocLink'] = $requiredDocLink;
                    $inArray['linkToFullApp'] = $linkToFullApp;
                    $inArray['linkToQuickApp'] = $linkToQuickApp;
                    $inArray['linkToFullAppMultiStep'] = $linkToFullAppMultiStep;
                    //$linkToFullApp . "&view=c5e7c430d26333b5";
                    $inArray['linkToQuickAppMultiStep'] = $linkToQuickAppMultiStep;
                    $inArray['QAAccessToDocs'] = $linkToQAWithAccessToDocs;
                    $inArray['FAAccessToDocs'] = $linkToFAWithAccessToDocs;
                    $inArray['branchFullAppURL'] = $linkToBranchFullApp;
                    $inArray['branchQuickAppURL'] = $linkToBranchQuickApp;
                    //$linkToQuickApp . "&view=c5e7c430d26333b5";
                }
            }
            /* Branch Info */

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }

            /* 2 nd Lender Info */


            if (count($lenderInfo2Array) > 0) {

                $inArray['1enderAddress2'] = trim($lenderInfo2Array['address1']);
                $inArray['lenderCity2'] = trim($lenderInfo2Array['city']);
                $inArray['lenderState2'] = trim($lenderInfo2Array['state']);
                $inArray['lenderZip2'] = trim($lenderInfo2Array['zipCode']);
                $LID2 = trim($lenderInfo2Array['LID']);
            }
            /*  2 nd Lender Info */

            /* 1 St Lender Info */

            if (count($lenderInfo1Array) > 0) {

                $inArray['1enderAddress1'] = trim($lenderInfo1Array['address1']);
                $inArray['lenderCity1'] = trim($lenderInfo1Array['city']);
                $inArray['lenderState1'] = trim($lenderInfo1Array['state']);
                $inArray['lenderZip1'] = trim($lenderInfo1Array['zipCode']);
                $LID1 = trim($lenderInfo1Array['LID']);
            }
            /*  1 St Lender Info */

            /* NOE Lender Info */
            $NOELenderInfoArray = [];
            if (count($lenderNOEArray) > 0) {
                if (array_key_exists($LID1, $lenderNOEArray)) {
                    $NOELenderInfoArray = $lenderNOEArray[$LID1];
                    if (count($NOELenderInfoArray) > 0) {

                        $inArray['NOELender1Phone'] = trim($NOELenderInfoArray['NOE_phoneNumber']);
                        $inArray['NOELender1Fax'] = trim($NOELenderInfoArray['NOE_fax']);
                        $inArray['NOELender1Address'] = trim($NOELenderInfoArray['NOE_address'] . ' ' . $NOELenderInfoArray['NOE_suite']);
                        $inArray['NOELender1City'] = trim($NOELenderInfoArray['NOE_city']);
                        $inArray['NOELender1State'] = trim($NOELenderInfoArray['NOE_state']);
                        $inArray['NOELender1Zip'] = trim($NOELenderInfoArray['NOE_zipCode']);
                    }
                }
                $NOELenderInfoArray = [];
                if (array_key_exists($LID2, $lenderNOEArray)) {
                    $NOELenderInfoArray = $lenderNOEArray[$LID2];
                    if (count($NOELenderInfoArray) > 0) {

                        $inArray['NOELender2Phone'] = trim($NOELenderInfoArray['NOE_phoneNumber']);
                        $inArray['NOELender2Fax'] = trim($NOELenderInfoArray['NOE_fax']);
                        $inArray['NOELender2Address'] = trim($NOELenderInfoArray['NOE_address'] . ' ' . $NOELenderInfoArray['NOE_suite']);
                        $inArray['NOELender2City'] = trim($NOELenderInfoArray['NOE_city']);
                        $inArray['NOELender2State'] = trim($NOELenderInfoArray['NOE_state']);
                        $inArray['NOELender2Zip'] = trim($NOELenderInfoArray['NOE_zipCode']);
                    }
                }
            }
            /**
             *
             * Description    : Added the new smart Tags in Hard Money LOS (BusinessEntity)
             * Date        : May 06, 2017
             * Author        : Viji, Venkatesh & Suresh
             **/
            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }

            if (array_key_exists($LMRID, $noOfDaysInCurrentStatusInfoArray ?? [])) {
                $noOfDaysInCurrentStatusInfo = $noOfDaysInCurrentStatusInfoArray[$LMRID];
                $inArray['daysInCurrentStatus'] = $noOfDaysInCurrentStatusInfo['noOfDays'];
            }
            if (array_key_exists($LMRID, $HUDLenderToPayInfoArray ?? [])) {
                $HUDLenderToPayInfo = $HUDLenderToPayInfoArray[$LMRID];
                if (count($HUDLenderToPayInfo)) {
                    if (array_key_exists('901', $HUDLenderToPayInfo)) {
                        $inArray['perdiemInterestHUD'] = Currency::formatDollarAmountWithDecimal($HUDLenderToPayInfo['901']['borrowerSettlementValue']);
                    }
                    if (array_key_exists('902', $HUDLenderToPayInfo)) {
                        $inArray['hazardInsurancePremiumHUD'] = Currency::formatDollarAmountWithDecimal($HUDLenderToPayInfo['902']['borrowerSettlementValue']);
                        $inArray['HUDMonthsTo902'] = $HUDLenderToPayInfo['902']['monthsTo'];
                    }
                }
            }
            if (array_key_exists($LMRID, $HUDReservesDepositInfoArray ?? [])) {
                $HUDReservesDepositInfo = $HUDReservesDepositInfoArray[$LMRID];
                if (count($HUDReservesDepositInfo)) {
                    if (array_key_exists('1002', $HUDReservesDepositInfo)) {
                        $inArray['hazardInsuranceofMonthsHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1002']['lenderMonths']);
                        $inArray['hazardInsurancePerMonthsHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1002']['lenderAmount']);
                        $inArray['hazardInsuranceTotalHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1002']['borrowerSettlementValue']);
                        if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && !Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1002']['borrowerSettlementValue'])) {
                            $inArray['hazardInsuranceTotalHUD'] = 'N/A';
                        }
                    } else {
                        if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                            $inArray['hazardInsuranceTotalHUD'] = 'N/A';
                        }
                    }
                    if (array_key_exists('1003', $HUDReservesDepositInfo)) {
                        $inArray['propertyTaxesofMonthsHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1003']['lenderMonths']);
                        $inArray['propertyTaxesPerMonthsHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1003']['lenderAmount']);
                        $inArray['propertyTaxesTotalHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1003']['borrowerSettlementValue']);
                        if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && !Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1003']['borrowerSettlementValue'])) {
                            $inArray['propertyTaxesTotalHUD'] = 'N/A';
                        }
                    } else {
                        if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                            $inArray['propertyTaxesTotalHUD'] = 'N/A';
                        }
                    }
                    if (array_key_exists('1004', $HUDReservesDepositInfo)) {
                        $inArray['floodInsuranceofMonthsHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1004']['lenderMonths']);
                        $inArray['floodInsurancePerMonthsHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1004']['lenderAmount']);
                        $inArray['floodInsuranceTotalHUD'] = Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1004']['borrowerSettlementValue']);
                        if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && !Currency::formatDollarAmountWithDecimal($HUDReservesDepositInfo['1004']['borrowerSettlementValue'])) {
                            $inArray['floodInsuranceTotalHUD'] = 'N/A';
                        }
                    } else {
                        if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                            $inArray['floodInsuranceTotalHUD'] = 'N/A';
                        }
                    }
                }
            } else {
                if (glCustomJobForProcessingCompany::isPC_CV3($PCID)) {
                    $inArray['hazardInsuranceTotalHUD'] = 'N/A';
                    $inArray['propertyTaxesTotalHUD'] = 'N/A';
                    $inArray['floodInsuranceTotalHUD'] = 'N/A';
                }
            }
            if (array_key_exists($LMRID, $HUDItemsPayableLoanInfoArray ?? [])) {
                $HUDItemsPayableLoanInfo = $HUDItemsPayableLoanInfoArray[$LMRID];
                if (count($HUDItemsPayableLoanInfo)) {
                    $inArray['appraisalFeePayee810'] = $HUDItemsPayableLoanInfo['HUD_App_Payee_810']['fieldName'];
                    $inArray['appraisalFeeTotal810'] = Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['HUD_App_Total_810']['fieldName']);
                    $inArray['appraisalFeePOC810'] = Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['HUD_App_POC_810']['fieldName']);

                    $inArray['appraisalFeePayee811'] = $HUDItemsPayableLoanInfo['HUD_App_Payee_811']['fieldName'];
                    $inArray['appraisalFeeTotal811'] = Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['HUD_App_Total_811']['fieldName']);
                    $inArray['appraisalFeePOC811'] = Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['HUD_App_POC_811']['fieldName']);

                    $inArray['appraisalFeePayee812'] = $HUDItemsPayableLoanInfo['HUD_App_Payee_812']['fieldName'];
                    $inArray['appraisalFeeTotal812'] = Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['HUD_App_Total_812']['fieldName']);
                    $inArray['appraisalFeePOC812'] = Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['HUD_App_POC_812']['fieldName']);
                    $underwritingFee804HUD = $HUDItemsPayableLoanInfo['804']['borrowerSettlementValue'] ?? 0;
                    $inArray['underwritingFee804HUD'] = Currency::formatDollarAmountWithDecimal($underwritingFee804HUD);
                    $processingFee809HUD = $HUDItemsPayableLoanInfo['809']['borrowerSettlementValue'] ?? 0;
                    $inArray['processingFee809HUD'] = Currency::formatDollarAmountWithDecimal($processingFee809HUD);
                    $inArray['interestPaymentHoldback'] = Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['808']['borrowerSettlementValue'] ?? 0);
                    if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && !Currency::formatDollarAmountWithDecimal($HUDItemsPayableLoanInfo['808']['borrowerSettlementValue'] ?? 0)) {
                        $inArray['interestPaymentHoldback'] = 'N/A';
                    }
                    $HUD_SS_801 = ($HUDItemsPayableLoanInfo['801']['borrowerSettlementValue'] ?? 0);
                    $HUD_SS_807 = ($HUDItemsPayableLoanInfo['807']['borrowerSettlementValue'] ?? 0);
                    $inArray['Total_HUD_801_804_807_809'] = Currency::formatDollarAmountWithDecimal(
                        HUDCalculation::total_HUD_801_804_807_809($HUD_SS_801, $underwritingFee804HUD, $HUD_SS_807, $processingFee809HUD) ?? 0);
                }
            }

            $fundingWorksheetTotalDebits = HUDCalculation::fundingWorksheetTotalDebits($LMRID);
            $fundingWorksheetTotalCredits = HUDCalculation::fundingWorksheetTotalCredits($LMRID);
            $fundingWorksheetWireTransferAmount = HUDCalculation::fundingWorksheetWireTransferAmount($LMRID);

            $HUD_V_202 = HUDCalculation::getPrincipalAmountOfNewLoans($LMRID);

            $appraisalFee = HUDCalculation::getAppraisalFeeDue($LMRID);
            $HUD_BS_810 = $appraisalFee['HUD_BS_810'] ?? 0;
            $HUD_BS_811 = $appraisalFee['HUD_BS_811'] ?? 0;
            $HUD_BS_812 = $appraisalFee['HUD_BS_812'] ?? 0;

            $inArray['fundingWorksheetTotalDebits'] = Currency::formatDollarAmountWithDecimalZeros($fundingWorksheetTotalDebits);
            $inArray['fundingWorksheetTotalCredits'] = Currency::formatDollarAmountWithDecimalZeros($fundingWorksheetTotalCredits);
            $inArray['fundingWorksheetWireTransferAmount'] = Currency::formatDollarAmountWithDecimalZeros($fundingWorksheetWireTransferAmount);
            $inArray['HUD_V_202'] = Currency::formatDollarAmountWithDecimalZeros($HUD_V_202);
            $inArray['HUD_BS_810'] = Currency::formatDollarAmountWithDecimalZeros($HUD_BS_810);
            $inArray['HUD_BS_811'] = Currency::formatDollarAmountWithDecimalZeros($HUD_BS_811);
            $inArray['HUD_BS_812'] = Currency::formatDollarAmountWithDecimalZeros($HUD_BS_812);


            if (array_key_exists($LMRID, $creditDecisionInfoArray ?? [])) {
                $creditDecisionInfo = $creditDecisionInfoArray[$LMRID];
                if (count($creditDecisionInfo)) {
                    $creditDecisionArray = creditDecision::decisionArray();
                    foreach ($creditDecisionInfo as $key => $value) {
                        $inArray['creditDecision'] = $creditDecisionArray[$value['creditDecision']];
                        $inArray['creditDecisionDate'] = Dates::formatDateWithRE($value['creditDecisionDate'], 'YMD', 'm/d/Y');
                        $inArray['guidelineVersion'] = $value['guidelineVersion'];
                    }

                }
            }
            if (array_key_exists($LMRID, $refinanceMortgageInfoArray ?? [])) {
                $refinanceMortgageInfo = $refinanceMortgageInfoArray[$LMRID];
                if (count($refinanceMortgageInfo)) {
                    foreach ($refinanceMortgageInfo as $key => $value) {
                        $inArray['refinanceCurrentLender'] = trim($value['refinanceCurrentLender']);
                        $inArray['costOfImprovementsMade'] = Currency::formatDollarAmountWithDecimal(trim($value['costOfImprovementsMade']));
                        $inArray['refinanceCurrentLoanBalance'] = Currency::formatDollarAmountWithDecimal(trim($value['refinanceCurrentLoanBalance']));
                        $inArray['refinanceCurrentRate'] = trim($value['refinanceCurrentRate']);
                        $inArray['originalPurchasePrice'] = Currency::formatDollarAmountWithDecimal(trim($value['originalPurchasePrice']));
                        $inArray['originalPurchaseDate'] = Dates::formatDateWithRE($value['originalPurchaseDate'], 'YMD', 'm/d/Y');
                        $inArray['refinanceMonthlyPayment'] = Currency::formatDollarAmountWithDecimal(trim($value['refinanceMonthlyPayment']));
                        $inArray['goodThroughDate'] = Dates::formatDateWithRE($value['goodThroughDate'], 'YMD', 'm/d/Y');
                    }
                }
            }

            if (array_key_exists($LMRID, $fileExtensionOptionsInfoArray ?? [])) {
                $fileExtensionOptionsInfo = $fileExtensionOptionsInfoArray[$LMRID];
                if (count($fileExtensionOptionsInfo)) {
                    foreach ($fileExtensionOptionsInfo as $key => $value) {
                        $feesExtensionAmount = 0;
                        $inArray['feesExtensionOptionPercentage'] = $value['extensionOptionPercentage'];
                        $inArray['feesExtensionOption'] = glHMLOExtensionOption::$glHMLOExtensionOption[$value['extensionOption']];
                        $inArray['feesExtensionRatePercentage'] = $value['extensionRatePercentage'];
                        $feesExtensionAmount = HMLOLoanTermsCalculation::calculatePercentageExtensionOption($totalLoanAmount, $value['extensionOptionPercentage']);
                        $inArray['feesExtensionAmount'] = Currency::formatDollarAmountWithDecimal($feesExtensionAmount);
                        $inArray['feesExtensionAmountInWords'] = Strings::makewords($feesExtensionAmount);

                        $inArray['extensionOptionPercentage'] = $value['extensionOptionPercentage'];
                        $inArray['extensionRatePercentage'] = $value['extensionRatePercentage'];
                        $inArray['extensionOptionsAmt'] = Currency::formatDollarAmountWithDecimal($feesExtensionAmount);
                        $inArray['extensionOption'] = glHMLOExtensionOption::$glHMLOExtensionOption[$value['extensionOption']];
                    }
                }
            }

            if (array_key_exists($LMRID, $extensionInfoArray ?? [])) {
                $extensionInfoDetails = [];
                $extensionInfoValueArr = [];
                $extensionInfoValue = '';
                $extensionInfo = $extensionInfoArray[$LMRID];
                if (count($extensionInfo)) {
                    foreach ($extensionInfo as $key => $value) {
                        $extensionInfoValue .= '<table border=\"0\" width=\"100%\">
                        <tr><td><b>Extension ' . ($key + 1) . '</b></td></tr>
                        <tr><td>Extension : ' . number_format($value['extensionPoints'], 2, '.', '') .
                            ' %(points) for ' . $value['extensionMonths'] . ' months @ ' . number_format($value['extensionRate'], 2, '.', '') . ' % (Interest Rate)<br>
                        Extension Date : ' . Dates::formatDateWithRE($value['extensionDate'], 'YMD', 'm/d/Y') . '<br>
                        Maturity Date : ' . Dates::formatDateWithRE($value['maturityDate'], 'YMD', 'm/d/Y') . '</td></tr>
					</table>';
                        if ($googleDocs == 1) {
                            $extensionHeader = "Extension " . ($key + 1) . "\n";
                            $extensionData = $extensionHeader . 'Extension : ' . number_format($value['extensionPoints'], 2, '.', '') .
                                ' %(points) for ' . $value['extensionMonths'] . ' months @ ' . number_format($value['extensionRate'], 2, '.', '') .
                                ' % (Interest Rate)' . "\n";
                            $extensionData = $extensionHeader . 'Extension : ' . number_format($value['extensionPoints'], 2, '.', '') .
                                ' %(points) for ' . $value['extensionMonths'] . ' months @ ' . number_format($value['extensionRate'], 2, '.', '') .
                                ' % (Interest Rate) ' . "\n";
                            $extensionInfoValueArr[] = [
                                ['fieldColor' => '', 'fieldText' => "$extensionData"],
                            ];
                        }
                    }
                    if ($googleDocs == 1) {
                        $inArray['tables'][] = [
                            'mergeTableTag'  => '##Extensions##',
                            'mergeTableData' => $extensionInfoValueArr,
                        ];
                    } else {
                        $inArray['extensions'] = $extensionInfoValue;
                    }
                }

            }

            if (array_key_exists($LMRID, $equipmentInfoArray ?? [])) {
                $equipmentInfo = $equipmentInfoArray[$LMRID];
                if (count($equipmentInfo) > 0) {
                    for ($eqp = 0; $eqp < count($equipmentInfo); $eqp++) {
                        $inArray['vendorName'] = $equipmentInfo[$eqp]['vendorName'];
                        $inArray['vendorPhone'] = $equipmentInfo[$eqp]['vendorPhone'];
                        $inArray['vendorContact'] = $equipmentInfo[$eqp]['vendorContact'];
                        $inArray['equipmentType'] = $equipmentInfo[$eqp]['equipmentType'];
                        $inArray['vinSerialNumber'] = $equipmentInfo[$eqp]['vinSerialNumber'];
                        $inArray['year'] = $equipmentInfo[$eqp]['YEAR'];
                        $inArray['salePrice'] = Currency::formatDollarAmountWithDecimal($equipmentInfo[$eqp]['salePrice']);
                        $inArray['make'] = $equipmentInfo[$eqp]['make'];
                        $inArray['model'] = $equipmentInfo[$eqp]['model'];
                        $inArray['mileage'] = $equipmentInfo[$eqp]['mileage'];
                        $inArray['quantity'] = $equipmentInfo[$eqp]['quantity'];
                        $inArray['equipmentDescription'] = $equipmentInfo[$eqp]['equipmentDescription'];
                    }
                }
            }

            if (array_key_exists($LMRID, $loanSettingsInfoArray ?? [])) {
                $loanSettingsInfo = $loanSettingsInfoArray[$LMRID];
                $initialTerms = $loanSettingsInfo['loanTerms'][0];
                $secondaryTerms = $loanSettingsInfo['loanTerms'][1];

                $inArray['indicatedRateDate'] = Dates::formatDateWithRE($loanSettingsInfo['indicatedRateDate'], 'YMD', 'm/d/Y');
                $inArray['indicatedRatePercent'] = $loanSettingsInfo['indicatedRatePercent'];
                $inArray['floorRate'] = Currency::formatDollarAmountWithDecimalZerosLimit($loanSettingsInfo['floorRate']);

                $inArray['initialTermYears'] = round(($initialTerms->TermYears / 12), 2);
                $inArray['initialTermMonths'] = $initialTerms->TermYears;
                $inArray['initialRateIndex'] = trim($initialTerms->RateIndex, ',');
                $inArray['initialRateMargin'] = Currency::formatDollarAmountWithDecimalZerosLimit($initialTerms->RateMargin, 8) . ' %';
                $inArray['initialAmor'] = $initialTerms->RateAmortization;

                $inArray['secondTermYears'] = round(($secondaryTerms->TermYears / 12), 2);
                $inArray['secondTermMonths'] = $secondaryTerms->TermYears;
                $inArray['secondRateMargin'] = Currency::formatDollarAmountWithDecimalZerosLimit($secondaryTerms->RateMargin, 8) . ' %';
                $inArray['secondRateIndex'] = trim($secondaryTerms->RateIndex, ',');
                //$inArray['secondRateFloor'] = Currency::formatDollarAmountWithDecimalZerosLimit($secondaryTerms->RateFloor,8).' %';
                $inArray['secondAmor'] = $secondaryTerms->RateAmortization;

                $inArray['MaxLTV'] = $loanSettingsInfo['maxLTVPercent'];
                $inArray['MinimumDSCRRatio'] = $loanSettingsInfo['minDSCRRatio'];
                $inArray['MinimumBalWithLender'] = Currency::formatDollarAmountWithDecimal($loanSettingsInfo['minActBal']);
            }

            if (array_key_exists($LMRID, $memberOfficerInfoArray ?? [])) {
                $memberOfficerInfo = $memberOfficerInfoArray[$LMRID];
                $membersOfTheEntity = '';
                $membersOfTheEntityWithTitle = '';
                $membersOfTheEntityWithAddr = '';
                $membersOfTheEntityInline = '';
                $membersOfTheEntityWithTitleInline = '';
                if (count($memberOfficerInfo) > 0) {
                    for ($h = 1; $h <= count($memberOfficerInfo); $h++) {
                        $memberName = '';
                        $memberTitle = '';
                        $memberOwnership = '';
                        $memberAddress = '';
                        $memberPhone = '';
                        $memberSSN = '';
                        $memberSSNumber = '';
                        $memberDOB = '';
                        $memberCreditScore = '';
                        $memberEmail = '';
                        $memberPersonalGuarantee = '';
                        $memberAuthorizedSigner = '';

                        $memberName = $memberOfficerInfo[$h - 1]['memberName'];
                        $inArray['member' . $h . 'Name'] = $memberName;

                        $memberTitle = $memberOfficerInfo[$h - 1]['memberTitle'];
                        $memberOwnership = $memberOfficerInfo[$h - 1]['memberOwnership'];
                        $memberAddress = $memberOfficerInfo[$h - 1]['memberAddress'];
                        $memberPhone = $memberOfficerInfo[$h - 1]['memberPhone'];
                        $memberSSN = $memberOfficerInfo[$h - 1]['memberSSN'];
                        $memberDOB = $memberOfficerInfo[$h - 1]['memberDOB'];
                        if (Dates::IsEmpty($memberDOB)) {
                            $memberDOB = '';
                        } else {
                            $memberDOB = Dates::formatDateWithRE($memberDOB, 'YMD', 'm/d/Y');
                        }
                        $memberCreditScore = $memberOfficerInfo[$h - 1]['memberCreditScore'];
                        $memberEmail = $memberOfficerInfo[$h - 1]['memberEmail'];
                        $memberPersonalGuarantee = $memberOfficerInfo[$h - 1]['memberPersonalGuarantee'];
                        $memberAuthorizedSigner = $memberOfficerInfo[$h - 1]['memberAuthorizedSigner'];

                        if ($memberName != '' || $memberTitle != '' || $memberOwnership > 0) {
                            $membersOfTheEntity .= trim($memberName) . ', ';
                            $membersOfTheEntity .= 'Title : ' . trim($memberTitle) . ', ';
                            $membersOfTheEntity .= 'Ownership : ' . trim($memberOwnership) . '%<br>';
                            //new tag without breaks
                            $membersOfTheEntityInline .= trim($memberName) . ', ';
                            $membersOfTheEntityInline .= 'Title : ' . trim($memberTitle) . ', ';
                            $membersOfTheEntityInline .= 'Ownership : ' . trim($memberOwnership) . '%, ';
                        }
                        if ($memberName != '' || $memberTitle != '' || $memberOwnership > 0) {
                            $membersOfTheEntityWithAddr .= trim($memberName) . ', ';
                            $membersOfTheEntityWithAddr .= 'Title: ' . trim($memberTitle) . ', ';
                            $membersOfTheEntityWithAddr .= 'Ownership: ' . trim($memberOwnership) . '%, Address:' . trim($memberAddress) . '<br>';
                        }
                        if ($memberName != '' || $memberTitle != '') {
                            $membersOfTheEntityWithTitle .= trim($memberName) . ', ';
                            $membersOfTheEntityWithTitle .= trim($memberTitle) . '<br>';
                            //new tag without breaks
                            $membersOfTheEntityWithTitleInline .= trim($memberName) . ', ';
                            $membersOfTheEntityWithTitleInline .= trim($memberTitle) . ', ';
                        }
                        if ($memberSSN) {
                            $memberSSNArray = Strings::splitSSNNumber(trim($memberSSN));
                            if (count($memberSSNArray) > 0) {
                                $memberSSNumber = trim($memberSSNArray['No1']) . '-' . trim($memberSSNArray['No2']) . '-' . trim($memberSSNArray['No3']);
                            }
                        }

                        $inArray['member' . $h . 'FullName'] = trim($memberName);
                        $inArray['member' . $h . 'Title'] = trim($memberTitle);
                        $inArray['member' . $h . 'DOB'] = trim($memberDOB);
                        $inArray['member' . $h . 'SSN'] = trim($memberSSNumber);
                        $inArray['member' . $h . 'CreditScore'] = trim($memberCreditScore);
                        $inArray['member' . $h . 'Address'] = trim($memberAddress);
                        $inArray['member' . $h . 'Phone'] = Strings::formatPhoneNumber(trim($memberPhone));
                        $inArray['member' . $h . 'Email'] = trim($memberEmail);
                        $inArray['member' . $h . 'Ownership'] = trim($memberOwnership);
                        $inArray['member' . $h . 'PersonalGuarantee'] = trim($memberPersonalGuarantee);
                        $inArray['member' . $h . 'AuthorizedSigner'] = trim($memberAuthorizedSigner);
                    }

                    $inArray['membersOfTheEntity'] = trim($membersOfTheEntity);
                    $inArray['membersOfTheEntityWithAddr'] = trim($membersOfTheEntityWithAddr);
                    $inArray['membersOfTheEntityWithTitle'] = trim($membersOfTheEntityWithTitle);

                    $inArray['membersOfTheEntityInline'] = trim($membersOfTheEntityInline);
                    //to remove the last comma
                    $membersOfTheEntityWithTitleInline = rtrim($membersOfTheEntityWithTitleInline, ', ');
                    $inArray['membersOfTheEntityWithTitleInline'] = trim(rtrim($membersOfTheEntityWithTitleInline, ','));
                }
            }

            /*  BusinessEntity Info */
            if (array_key_exists($LMRID, $HMLOBusinessEntity ?? [])) {
                $businessEntity = $HMLOBusinessEntity[$LMRID];
                if (count($businessEntity) > 0) {
                    $inArray['entityName'] = trim($businessEntity['entityName']);
                    $inArray['entityType'] = trim($businessEntity['entityType']);
                    $inArray['tradeName'] = trim($businessEntity['tradeName']);
                    $entityTypeLong = trim($businessEntity['entityType']);
                    $inArray['entityTypeLong'] = getEntityTypeLongName::getEntityName($entityTypeLong);
                    $inArray['ENINo'] = trim($businessEntity['ENINo']);
                    $inArray['entityAddress'] = trim($businessEntity['entityAddress']);
                    $inArray['entityAddress2'] = trim($businessEntity['entityAddress2']);
                    $inArray['entityCity'] = trim($businessEntity['entityCity']);
                    $inArray['entityState'] = trim($businessEntity['entityState']);
                    $inArray['entityStateLong'] = Strings::convertState(trim($businessEntity['entityState']));
                    $inArray['entityZip'] = trim($businessEntity['entityZip']);
                    $inArray['entityStateOfFormation'] = trim($businessEntity['entityStateOfFormation']);
                    $inArray['statesRegisterdIn'] = trim($businessEntity['statesRegisterdIn']);
                    $inArray['entityTradeName'] = trim($businessEntity['tradeName']);
                    if ($businessEntity['sameAsEntityAddr'] == 1) {
                        $inArray['entityBillAddress'] = trim($businessEntity['entityAddress']);
                        $inArray['entityBillCity'] = trim($businessEntity['entityCity']);
                        $inArray['entityBillState'] = trim($businessEntity['entityState']);
                        $inArray['entityBillZip'] = trim($businessEntity['entityZip']);
                    } else {
                        $inArray['entityBillAddress'] = trim($businessEntity['entityBillAddress']);
                        $inArray['entityBillCity'] = trim($businessEntity['entityBillCity']);
                        $inArray['entityBillState'] = trim($businessEntity['entityBillState']);
                        $inArray['entityBillZip'] = trim($businessEntity['entityBillZip']);
                    }
                    $inArray['entityStateOfFormationLong'] = Strings::convertState(trim($businessEntity['entityStateOfFormation']));
                    $inArray['businessCategory'] = trim($businessEntity['businessCategory']);
                    $inArray['productTypeOrServiceSold'] = trim($businessEntity['productTypeOrServiceSold']);
                    $inArray['terminalOrMakeModel'] = trim($businessEntity['terminalOrMakeModel']);
                    $inArray['businessPhone'] = trim($businessEntity['businessPhone']);
                    $startDateAtLocation = '';
                    $startDateAtLocation = $businessEntity['startDateAtLocation'];
                    if (Dates::IsEmpty($startDateAtLocation)) {
                        $startDateAtLocation = '';
                    } else {
                        $startDateAtLocation = Dates::formatDateWithRE($startDateAtLocation, 'YMD', 'm/d/Y');
                    }

                    $inArray['startDateAtLocation'] = trim($startDateAtLocation);
                    $inArray['entityPropertyOwnerShip'] = trim($businessEntity['entityPropertyOwnerShip']);
                    $inArray['landlordMortagageContactName'] = trim($businessEntity['landlordMortagageContactName']);
                    $inArray['landlordMortagagePhone'] = trim($businessEntity['landlordMortagagePhone']);
                    $inArray['rentMortagagePayment'] = Currency::formatDollarAmountWithDecimal($businessEntity['rentMortagagePayment']);
                    $inArray['avgMonthlyCreditcardSale'] = Currency::formatDollarAmountWithDecimal($businessEntity['avgMonthlyCreditcardSale']);
                    $inArray['avgTotalMonthlySale'] = Currency::formatDollarAmountWithDecimal($businessEntity['avgTotalMonthlySale']);
                    $inArray['annualGrossSales'] = Currency::formatDollarAmountWithDecimal($businessEntity['annualGrossSales']);
                    $inArray['annualGrossProfit'] = Currency::formatDollarAmountWithDecimal($businessEntity['annualGrossProfit']);
                    $inArray['ordinaryBusinessIncome'] = Currency::formatDollarAmountWithDecimal($businessEntity['ordinaryBusinessIncome']);
                    $inArray['organizationalRef'] = trim($businessEntity['organizationalRef']);
                    $inArray['dateOfFormation'] = Dates::formatDateWithRE(trim($businessEntity['dateOfFormation']), 'YMD', 'm/d/Y');
                    $inArray['borrowerType'] = $businessEntity['borrowerType'];
                    //$inArray['businessPhone'] = trim($businessEntity['businessPhone']);

//pr($businessEntity);die();
                }
            }
            if ($businessEntity['entityType'] > '') {
                if ($businessEntity['entityType'] == 'Trust') {
                    $inArray['smartBorrower'] = $inArray['entityName'];
                } else {
                    switch ($inArray['entityStateOfFormationLong']) {
                        case "Alabama":
                        case "Alaska":
                        case "Arizona":
                        case "Arkansas":
                        case "Idaho":
                        case "Illinois":
                        case "Indiana":
                        case "Iowa":
                        case "Ohio":
                        case "Oklahoma":
                        case "Oregon":
                            $joiner = 'an';
                            break;
                        default:
                            $joiner = 'a';
                    }
                    $inArray['smartBorrower'] = $inArray['entityName'] . ', ' . $joiner . ' ' . $inArray['entityStateOfFormationLong'] . ' ' . $inArray['entityTypeLong'];
                }
            } else {
                $inArray['smartBorrower'] = $borrowerName;
                if ($coBorrowerName > '') {
                    $inArray['smartBorrower'] .= ', ' . $coBorrowerName;
                }
            }


            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /*  HMLO New Loan Info */

            HMLOLoanTermsCalculation::isFileSPREO($PCID);
            if (array_key_exists($LMRID, $HMLONewLoanInfoArray)) {
                $HMLONewLoanInfo = $HMLONewLoanInfoArray[$LMRID];
                //pr($HMLONewLoanInfo); die();
                if (count($HMLONewLoanInfo) > 0) {

                    $loanTermExpireDate = '';
                    $LOISentDate = '';
                    $resaleClosingDate = '';
                    $downPaymentPercentage = '';
                    $interestChargedFromDate = '';
                    $interestChargedEndDate = '';
                    $isTaxesInsEscrowed = '';
                    $escrowedTaxes = '';
                    $escrowedIns = '';
                    $desiredLoanAmount = '';
                    $actualRentsInPlace = '';
                    $lessActualExpenses = '';
                    $netOperatingIncome = '';
                    $loanTermExpireDateLong = '';
                    $extensionOptionPercentage = 0;
                    $extensionRatePercentage = 0;
                    $extensionOption = '';
                    $originalPurchasePrice = 0;
                    $costOfImprovementsMade = 0;
                    $refinanceMonthlyPayment = 0;
                    $refinanceCurrentLoanBalance = 0;
                    $refinanceCurrentRate = 0;
                    $originalPurchaseDate = '';
                    $refinanceCurrentLender = '';
                    $loanTermExpireDate = trim($HMLONewLoanInfo['loanTermExpireDate']);
                    $LOISentDate = $HMLONewLoanInfo['LOISentDate'];
                    $resaleClosingDate = trim($HMLONewLoanInfo['resaleClosingDate']);
                    $interestChargedFromDate = trim($HMLONewLoanInfo['interestChargedFromDate']);
                    $interestChargedEndDate = trim($HMLONewLoanInfo['interestChargedEndDate']);

                    $inArray['exitFeePoints'] = trim($HMLONewLoanInfo['exitFeePoints']);
                    $inArray['exitFeeAmount'] = Currency::formatDollarAmountWithDecimal($HMLONewLoanInfo['exitFeeAmount']);
                    $inArray['aggregateDSCR'] = $HMLONewLoanInfo['aggregateDSCR'];

                    $actualRentsInPlace = Strings::replaceCommaValues($HMLONewLoanInfo['actualRentsInPlace']);
                    $lessActualExpenses = Strings::replaceCommaValues($HMLONewLoanInfo['lessActualExpenses']);
                    $vacancyFactorCommercial = Strings::replaceCommaValues($HMLONewLoanInfo['vacancyFactorCommercial']);
                    $actualRentsInPlaceCommercial = Strings::replaceCommaValues($HMLONewLoanInfo['actualRentsInPlaceCommercial']);
                    $otherIncome = Strings::replaceCommaValues($HMLONewLoanInfo['otherIncome']);
                    $otherIncomeVacancyRate = Strings::replaceCommaValues($HMLONewLoanInfo['otherIncomeVacancyRate']);
                    $tenantContribution = Strings::replaceCommaValues($HMLONewLoanInfo['tenantContribution']);
                    $tenantContributionVacancyRate = Strings::replaceCommaValues($HMLONewLoanInfo['tenantContributionVacancyRate']);
                    $waterSewer = Strings::replaceCommaValues($HMLONewLoanInfo['waterSewer']);
                    $electricity = Strings::replaceCommaValues($HMLONewLoanInfo['electricity']);
                    $gas = Strings::replaceCommaValues($HMLONewLoanInfo['gas']);
                    $repairsMaintenance = Strings::replaceCommaValues($HMLONewLoanInfo['repairsMaintenance']);
                    $legal = Strings::replaceCommaValues($HMLONewLoanInfo['legal']);
                    $payroll = Strings::replaceCommaValues($HMLONewLoanInfo['payroll']);
                    $misc = Strings::replaceCommaValues($HMLONewLoanInfo['misc']);
                    $commonAreaUtilities = Strings::replaceCommaValues($HMLONewLoanInfo['commonAreaUtilities']);
                    $elevatorMaintenance = Strings::replaceCommaValues($HMLONewLoanInfo['elevatorMaintenance']);
                    $replacementReserves = Strings::replaceCommaValues($HMLONewLoanInfo['replacementReserves']);
                    $other = Strings::replaceCommaValues($HMLONewLoanInfo['other']);
                    $tenantReimursements = Strings::replaceCommaValues($HMLONewLoanInfo['tenantReimursements']);
                    $managementExpense = Strings::replaceCommaValues($HMLONewLoanInfo['managementExpense']);
                    $inArray['recordingFees'] = Currency::formatDollarAmountWithDecimal($HMLONewLoanInfo['recordingFee']);
                    $inArray['loanInfoInsurancePremium'] = Currency::formatDollarAmountWithDecimal($HMLONewLoanInfo['insurancePremium']);

                    //$netOperatingIncome = calculateNetOperatingIncome($actualRentsInPlace, $lessActualExpenses); //trim($HMLONewLoanInfo['grossAnnualRentLargestTenant']);
                    $spcf_annualPremium_cal = $fileHMLOPropertyInfo['annualPremium'];
                    $vacancyFactor = $fileHMLONewLoanInfo['vacancyFactor'];
                    $spcf_hoafees_cal = $fileHMLONewLoanInfo['spcf_hoafees'];
                    $spcf_taxes1_cal = Strings::replaceCommaValues($taxes1);


                    // $netOperatingIncome = $actualRentsInPlace - $vacancy - $lessActualExpenses - $spcf_taxes1_cal - $spcf_annualPremium_cal - $spcf_hoafees_cal;
                    $vacancy = ($vacancyFactor * $actualRentsInPlace) / 100;
                    $vacancyCommercial = ($vacancyFactorCommercial * $actualRentsInPlaceCommercial) / 100.0;
                    $vacancyOtherIncome = ($otherIncome * $otherIncomeVacancyRate) / 100.0;
                    $vacancyTenantContribution = ($tenantContribution * $tenantContributionVacancyRate) / 100.0;

                    $netOperatingIncome = proposalFormula::calculateNetOperatingIncome($actualRentsInPlace,
                        $actualRentsInPlaceCommercial,
                        $tenantContribution,
                        $otherIncome,
                        $vacancy,
                        $vacancyCommercial,
                        $vacancyOtherIncome,
                        $vacancyTenantContribution,
                        $lessActualExpenses,
                        $waterSewer,
                        $electricity,
                        $gas,
                        $repairsMaintenance,
                        $legal,
                        $payroll,
                        $misc,
                        $commonAreaUtilities,
                        $elevatorMaintenance,
                        $replacementReserves,
                        $other,
                        $tenantReimursements,
                        $managementExpense,
                        $spcf_taxes1_cal,
                        $spcf_annualPremium_cal,
                        $spcf_hoafees_cal);

                    $netOperatingIncome = Strings::replaceCommaValues($netOperatingIncome);

                    $loanTermExpireDateLong = trim($HMLONewLoanInfo['loanTermExpireDateLong']);

                    if (Dates::IsEmpty($loanTermExpireDate)) {
                        $loanTermExpireDate = '';
                    } else {
                        $loanTermExpireDate = Dates::formatDateWithRE($loanTermExpireDate, 'YMD', 'm/d/Y');
                        $loanTermExpireDateLong = date('F d, Y', strtotime($loanTermExpireDate));
                    }
                    if (Dates::IsEmpty($LOISentDate)) {
                        $LOISentDate = '';
                    } else {
                        $LOISentDate = Dates::formatDateWithRE($LOISentDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($interestChargedFromDate)) {
                        $interestChargedFromDate = '';
                    } else {
                        $interestChargedFromDate = Dates::formatDateWithRE($interestChargedFromDate, 'YMD', 'm/d/Y');
                    }
                    if (Dates::IsEmpty($interestChargedEndDate)) {
                        $interestChargedEndDate = '';
                    } else {
                        $interestChargedEndDate = Dates::formatDateWithRE($interestChargedEndDate, 'YMD', 'm/d/Y');
                    }

                    if (Dates::IsEmpty($resaleClosingDate)) {
                        $resaleClosingDate = '';
                    } else {
                        $resaleClosingDate = Dates::formatDateWithRE($resaleClosingDate, 'YMD', 'm/d/Y');
                    }


                    $isTaxesInsEscrowed = trim($HMLONewLoanInfo['isTaxesInsEscrowed']);
                    if ($isTaxesInsEscrowed == 'Yes') {
                        $escrowedTaxes = $taxes1;
                        $escrowedIns = $insurance1;
                    } else {
                        $escrowedTaxes = '0';
                        $escrowedIns = '0';
                    }
                    $inArray['debtServiceCoverageRatio'] = proposalFormula::calculateDebtServiceRatio($totalMonthlyPayment, $netOperatingIncome) . ' %';
                    $inArray['debtServiceRatio'] = $inArray['debtServiceCoverageRatio'];

                    $inArray['debtServiceCoverageRatioNoSign'] = proposalFormula::calculateDebtServiceRatio($totalMonthlyPayment, $netOperatingIncome);
                    $inArray['debtServiceRatioNoSign'] = $inArray['debtServiceCoverageRatioNoSign'];

                    $inArray['debtServiceRatioPITIA'] = round(HMLOLoanTermsCalculation::$debtServiceRatioPITIA, 2) . ' %';
                    $inArray['debtServiceRatioPITIANoSign'] = round(HMLOLoanTermsCalculation::$debtServiceRatioPITIA, 2);
                    $inArray['escrowedTaxes'] = Currency::formatDollarAmountWithDecimal($escrowedTaxes);
                    $inArray['escrowedIns'] = $escrowedIns;
                    $inArray['loanTermExpireDate'] = $loanTermExpireDate;
                    $inArray['loanTermExpireDateLong'] = $loanTermExpireDateLong;
                    $inArray['LOISentDate'] = $LOISentDate;
                    $inArray['resaleClosingDate'] = $resaleClosingDate;
                    $inArray['closingCostFinanced'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['closingCostFinanced']));
                    $inArray['initialAdvance'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['initialAdvance']));
                    $inArray['HMLOLender'] = trim($HMLONewLoanInfo['HMLOLender']);
                    if (!$inArray['originalPurchaseDate']) {
                        $inArray['originalPurchaseDate'] = Dates::formatDateWithRE(trim($HMLONewLoanInfo['originalPurchaseDate']), 'YMD', 'm/d/Y');
                    }

                    $inArray['prepaidInterestReserve'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['prepaidInterestReserve']));
                    $inArray['downPaymentPercentage'] = trim($HMLONewLoanInfo['downPaymentPercentage']);
                    $inArray['desiredLoanAmount'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['desiredLoanAmount']));
                    $inArray['interestChargedFromDate'] = $interestChargedFromDate;
                    $inArray['interestChargedEndDate'] = $interestChargedEndDate;
                    $inArray['taxImpoundsFee'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['taxImpoundsFee']));
                    $inArray['insImpoundsFee'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['insImpoundsFee']));
                    $inArray['dueDiligence'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['dueDiligence']));
                    $inArray['discountFee'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['bufferAndMessengerFee']));
                    $inArray['resalePrice'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['resalePrice']));

                    $inArray['acutalRentInPlace'] = '$ ' . Integers::formatInputToInt($actualRentsInPlace);
                    $inArray['lessActualExpenses'] = '$ ' . Integers::formatInputToInt($lessActualExpenses);
                    $inArray['netOperatingIncome'] = '$ ' . Integers::formatInputToInt($netOperatingIncome);
                    $inArray['costOfCapital'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['costOfCapital']));
                    $inArray['landValue'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['landValue']));
                    $paymentbasedon = '';
                    if ($HMLONewLoanInfo['isLoanPaymentAmt'] == LoanTerms::TLA) {
                        $paymentbasedon = 'Total Loan Amount';
                    } elseif ($HMLONewLoanInfo['isLoanPaymentAmt'] == LoanTerms::ILA) {
                        $paymentbasedon = 'Current Loan Balance';
                    } else {
                        $paymentbasedon = 'Set Manual Payment';
                    }
                    $inArray['paymentBasedOn'] = $paymentbasedon;
                    $inArray['payOffMortagage1'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['payOffMortgage1']));
                    $inArray['payOffMortagage2'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['payOffMortgage2']));
                    $inArray['payOffTaxes'] = Currency::formatDollarAmountWithDecimal(trim($HMLONewLoanInfo['payOffOutstandingTaxes']));
                    $inArray['prepaidInterestReserveMonth'] = $HMLONewLoanInfo['noOfMonthsPrepaid'];

                    $inArray['totalAmtAndMontlyPay'] = Currency::formatDollarAmountWithDecimal(trim((Strings::replaceCommaValues($totalLoanAmount)) + (Strings::replaceCommaValues($lien1Payment))));
                    $inArray['totalCashOut'] = Currency::formatDollarAmountWithDecimal(trim($totalCashOutAmt));
                    $inArray['yieldSpread'] = $HMLONewLoanInfo['yieldSpread'];
                    $inArray['borNoOfSquareFeet'] = $HMLONewLoanInfo['borNoOfSquareFeet'];

                    //Rate Lock Fields
                    $inArray['rateLockDate'] = Dates::formatDateWithRE($HMLONewLoanInfo['rateLockDate'], 'YMD', 'm/d/Y');
                    $inArray['lockExpirationDate'] = Dates::formatDateWithRE($HMLONewLoanInfo['rateLockExpirationDate'], 'YMD', 'm/d/Y');
                    $inArray['rateLockExtension'] = $HMLONewLoanInfo['rateLockExtension'];
                    $inArray['rateLockNotes'] = $HMLONewLoanInfo['rateLockNotes'];

                    //Loan Tags
                    $inArray['plansAndPermitsStatus'] = $HMLONewLoanInfo['plansAndPermitsStatus'] ? glPlansAndPermitStatus::getGlPlansAndPermitStatus($PCID)[$HMLONewLoanInfo['plansAndPermitsStatus']] : '';
                    $inArray['doesProjectRequireReZoning'] = $HMLONewLoanInfo['isProjectRequireRezoning'] ? glYesNo::$glYesNo[$HMLONewLoanInfo['isProjectRequireRezoning']] : '';
                    $inArray['whatIsYourAnticipatedHoldTime'] = $HMLONewLoanInfo['anticipatedHoldTime'];
                    $inArray['whatIsYourAnticipatedPlansAndPermitTimeline'] = $HMLONewLoanInfo['anticipatedPlansPermitTimeline'];
                    $inArray['whatIsYourAnticipatedConstructionTimeline'] = $HMLONewLoanInfo['anticipatedConstructionTimeline'];


                    $inArray['managementExpense'] = Currency::formatDollarAmountWithDecimal($HMLONewLoanInfo['managementExpense']);
                    $inArray['OperatingExpense'] = Currency::formatDollarAmountWithDecimal($HMLONewLoanInfo['lessActualExpenses']);
                    if ($HMLONewLoanInfo['paymentFrequency']) {
                        $inArray['paymentFrequency'] = $glpaymentFrequency[$HMLONewLoanInfo['paymentFrequency']];
                    }

                    //Additional Questions
                    $inArray['secondaryHolderName'] = $HMLONewLoanInfo['secondaryHolderName'];
                    $inArray['secondaryFinancingAmount'] = $HMLONewLoanInfo['secondaryFinancingAmount'];
                }
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            $CLBInArray = ['initLAmt'         => $initialLoanAmount,
                           'prePIR'           => $prepaidInterestReserve,
                           'closingCost'      => $HMLONewLoanInfo['closingCostFinanced'],
                           'funDraw'          => $totalDrawsFunded,
                           'principalPayDown' => $paydownamount
            ];
            if(HMLOLoanTermsCalculation::$isFileSPREO) {
                $currentLoanBalance = proposalFormula::calculateCurrentLoanBalanceForSPREO($initialLoanAmount,
                   $HMLONewLoanInfo['prepaidInterestReserve']);
            } else {
                $currentLoanBalance = proposalFormula::calculateCurrentLoanBalance($CLBInArray, $typeOfHMLOLoanRequesting);
            }

            $inArray['currentLoanBalance'] = Currency::formatDollarAmountWithDecimal(trim($currentLoanBalance));
            $tempval = 0;
            $tempval = $tempTotalLoanAmount;
            if ($isLoanPaymentAmt == LoanTerms::ILA) {
                $tempval = $currentLoanBalance; // https://www.pivotaltracker.com/story/show/*********
            }
            if ($typeOfHMLOLoanRequesting == 'Transactional') {
                $tempval = $initialLoanAmount;
            }
            $tempTotalLoanAmount = 0;
            $lien1Rate = $tblFileInfo['lien1Rate'];
            $lien1Terms = trim($tblFileInfo['lien1Terms']);
            if (!in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                if ($isHMLO && !$lien1Terms) {
                    $lien1Terms = 'Interest Only';
                }
            }
            if ($isLoanPaymentAmt == 'SMP') {
                $lien1Terms = '';
            }
            if ($isLoanPaymentAmt != 'SMP') {
                $totalMonthlyPayment = proposalFormula::calculateHMLOLoanInfoTotalMonthlyPayment($tempval, $lien1Rate, $lien1Terms, $purchaseCloseDate, $accrualType);
            } else {
                $totalMonthlyPayment = $lien1Payment;
            }
            $inArray['netMonthlyPayment'] = Currency::formatDollarAmountWithDecimal($totalMonthlyPayment);
            $inArray['netMonthlyPaymentText'] = Strings::makewords(trim(Strings::replaceCommaValues($totalMonthlyPayment)));

            if (Strings::replaceCommaValues($inArray['lateChargeAmt']) && Strings::replaceCommaValues($totalMonthlyPayment) / Strings::replaceCommaValues($inArray['lateChargeAmt']) >
                Strings::replaceCommaValues($inArray['minLateFeeAmt'])) {
                $inArray['lateDue'] = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($totalMonthlyPayment) / Strings::replaceCommaValues($inArray['lateChargeAmt']));
            } else {
                $inArray['lateDue'] = Strings::replaceCommaValues($inArray['minLateFeeAmt']);
            }

            $monthlypitiIns = (Strings::replaceCommaValues($HMLOPropInfo['annualPremium']) / 12);
            $monthlypitiTax = (Strings::replaceCommaValues($incomeInfo['taxes1']) / 12);
            $inArray['monthlyPaymentPITI'] = Currency::formatDollarAmountWithDecimal(round(Strings::replaceCommaValues(trim($monthlypitiIns + $monthlypitiTax + Strings::replaceCommaValues($totalMonthlyPayment))), 2));

            $diemDaysArray = ['startDate' => $interestChargedFromDate, 'endDate' => $interestChargedEndDate, 'inclusive' => false];
            $diemDays = Dates::calculateNoOfDaysForTwoDate($diemDaysArray);
            $inArray['perdiemInterestofDays'] = $diemDays;
            if ($lien1Terms == LoanTerms::INTEREST_ONLY) {
                $outputArray = proposalFormula::getAccrualTypeBaseValues($purchaseCloseDate, $accrualType);
                $totalDailyInterestCharge = (Strings::replaceCommaValues($totalMonthlyPayment) / $outputArray->monthDays);
            } else {
                $dailyInteresInArray = [
                    'totalLoanAmount' => $totalLoanAmount,
                    'lien1Rate'       => $lien1Rate,
                ];
                $totalDailyInterestCharge = proposalFormula::calculateTotalDailyInterestCharge($dailyInteresInArray);
            }

            $dailyEstPerDiemArray = [
                'diemDays'                 => $diemDays,
                'totalDailyInterestCharge' => $totalDailyInterestCharge,
            ];
            //$totalEstPerDiem = calculateTotalEstPerDiem($dailyEstPerDiemArray);
            LMRequest::setLMRId($LMRId);
            $loanProgram = LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType;
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_BRIDGE_LOAN_RENOVATION) {
                $inArray['rehabCostFinanced'] = 'N/A';
            }

            $totalEstPerDiem = LMRequest::File()->getTblFileCalculatedValues_by_LMRId()->TotalPerDiemInterestAmount;

            $tempTotalLoanAmount = $totalLoanAmount;

            $paymentduedate = '';
            $paymentduedate = date('Y') . '-' . date('m') . '-' . trim($HMLOPropInfo['paymentDue']);
            $paymentduedate = Dates::formatDateWithRE($paymentduedate, 'YMD', 'm/d/Y');

            $amountdue = $HMLOPropInfo['amountPastDueOrOwed'];
            $payOffAmountArray = [
                'paymentduedate'           => $paymentduedate,
                'payOffDate'               => $payOffDate,
                'currentLoanBalance'       => $currentLoanBalance,
                'totalDailyInterestCharge' => $totalDailyInterestCharge,
                'totalMonthlyPayment'      => $totalMonthlyPayment,
                'amountdue'                => $amountdue,
            ];
            $payOffAmount = proposalFormula::calculatePayOffAmount($payOffAmountArray);

            $inArray['payOffAmount'] = Currency::formatDollarAmountWithDecimal(trim($payOffAmount));


            if ($HMLOPropInfo['paymentDue'] != '') {
                $latePayemntAppliedOn = date('m/d/Y', strtotime($paymentduedate . ' + ' . trim($HMLOPropInfo['latePayemntAppliedOn']) . ' days'));

                $latePayemntAppliedDateLong = date('M d, Y', strtotime($latePayemntAppliedOn));
            }
            $inArray['latePayemntAppliedOn'] = $latePayemntAppliedOn;// trim($HMLOPropInfo['latePayemntAppliedOn']);
            $inArray['latePayemntAppliedDateLong'] = $latePayemntAppliedDateLong;

            $loanTerms = $InrteresedBasedPayment = 0;
            $pos = strpos($loanTerm, ' ');
            $loanTerms = substr($loanTerm, 0, $pos);
            $nper = $loanTerms;
            $pmt = -$totalMonthlyPayment;
            $pv = Strings::replaceCommaValues($initialLoanAmount);
            $InrteresedBasedPayment = round((proposalFormula::RATE($nper, $pmt, $pv, $fv ?? 0, $guess ?? 0.1) * 12 * 100), 2);
            $inArray['interestRateBasedOnPayment'] = Currency::formatDollarAmountWithDecimal(trim($InrteresedBasedPayment));

            $inArray['perDiemInterestAmount'] = '$ ' . Currency::formatDollarAmountWithDecimal($totalDailyInterestCharge);
            $inArray['accuredPerDiemInterest'] = '$ ' . Currency::formatDollarAmountWithDecimal($totalEstPerDiem);

            $secondInArray = ['originationPointsValue' => $originationPointsValue, 'brokerPointsValue' => $brokerPointsValue, 'totalEstPerDiem' => $totalEstPerDiem];
            $totalFeesAndCost = proposalFormula::calculateTotalFeesAndCostNew($fileHMLONewLoanInfo, $secondInArray);

            $inArray['spcf_hoafees'] = '$ ' . Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfo['spcf_hoafees']);
            $inArray['spcf_hoafeesNoSign'] = Currency::formatDollarAmountWithDecimal($fileHMLONewLoanInfo['spcf_hoafees']);

            if ($fileHMLOPropertyInfo['prePaymentPenalty'] == 'No Pre-Pay Penalty') {
                $inArray['prePaymentPenaltyPercentage_prePaymentPenalty'] = $fileHMLOPropertyInfo['prePaymentPenalty'];
            } else {
                $inArray['prePaymentPenaltyPercentage_prePaymentPenalty'] = trim($fileHMLONewLoanInfo['prePaymentPenaltyPercentage']) . ' % for ' . $fileHMLOPropertyInfo['prePaymentPenalty'];
            }
            $inArray['prePaymentSelectVal'] = $fileHMLONewLoanInfo['prePaymentSelectVal'];
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && (!$fileHMLONewLoanInfo['prePaymentSelectVal'] || $fileHMLONewLoanInfo['prePaymentSelectVal'] == 'No')) {
                $inArray['prePaymentSelectVal'] = 'N/A';
            }
            if (glCustomJobForProcessingCompany::isPC_CRB($PCID)) {
                $prePaymentSelectValue = feesAndCost::formatPrePaymentPenaltyOptions_CRB($loanProgram);
            } else {
                $prePaymentSelectValue = feesAndCost::formatPrePaymentPenaltyOptions($fileHMLONewLoanInfo['prePaymentSelectVal']);
            }
            if ($googleDocs == 1) {
                $prePaymentSelectValue = str_replace('<br>', "\n", $prePaymentSelectValue);
            }
            $inArray['prePaymentSelectValue'] = $prePaymentSelectValue;
            $inArray['loanGuaranteeType'] = $fileHMLOPropertyInfo['loanGuaranteeType'];

            $netLenderFundsinputArray = [
                'totalLoanAmount'        => $tempTotalLoanAmount,
                'totalFeesAndCost'       => $totalFeesAndCost,
                'closingCostFinanced'    => $closingCostFinanced,
                'rehabCostFinanced'      => $rehabCostFinanced,
                'prepaidInterestReserve' => LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->prepaidInterestReserve,
                'initialDrawAmount' => LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->initialAdvance,
                'isAutoCalcTotalLoanAmountBasedOnLTC2' => (LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->autoCalcTLAARV == 'LTC2') ,
                'escrowFees' => LMRequest::File()->getTblFileHMLONewLoanInfo_by_fileID()->escrowFees,
                'isTransactionTypePurchaseCategory' => HMLOLoanTermsCalculation::isTransactionTypePurchaseCategory($typeOfHMLOLoanRequesting),
                'isTransactionTypeRefinanceCategory' => HMLOLoanTermsCalculation::isTransactionTypeRefinanceCategory($typeOfHMLOLoanRequesting),
                'payOffMortgage1' => $payOffMortgage1,
                'payOffMortgage2' => $payOffMortgage2,
                'payOffOutstandingTaxes' => $payOffOutstandingTaxes
            ];
            $netLenderFundsToBorrower = proposalFormula::calculateNetLenderFundsToBorrower($netLenderFundsinputArray);
            $inArray['netFundsToBorrower'] = Currency::formatDollarAmountWithDecimal(trim($netLenderFundsToBorrower));
            $inArray['totalFeesAndCost'] = Currency::formatDollarAmountWithDecimal(trim($totalFeesAndCost));

            /*  Loan Origination Info */
            $interestPaymentHoldBackAmount = HUDController::calculateHUDInterestPayment($totalLoanAmount, $rehabCostFinanced, $lien1Rate);

            //   $interestPaymentHoldback = HUDController::calculateHUDInterestPaymentTotal($interestPaymentHoldBackAmount, LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackMonth);
            $HUDInfo = tblLMRHUDItemsPayableLoan::get(['LMRId' => $LMRId, 'fieldID' => 805]);
            $inArray['HUD_BS_805'] = Currency::formatDollarAmountWithDecimal($HUDInfo->borrowerSettlementValue); //Holdback
            $inArray['interestPaymentHoldBackMonth'] = LMRequest::myFileInfo()->getFileHUDBasicInfo()->interestPaymentHoldBackMonth;

            $HUDInfo = tblLMRHUDItemsPayableLoan::get(['LMRId' => $LMRId, 'fieldID' => 807]);
            $inArray['HUD_BS_807'] = Currency::formatDollarAmountWithDecimal($HUDInfo->borrowerSettlementValue); //Holdback
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && (!$HUDInfo->borrowerSettlementValue || $HUDInfo->borrowerSettlementValue == '0.00')) {
                $inArray['HUD_BS_807'] = 'N/A';
            }


            $tblFileHUDTransaction = tblFileHUDTransaction::Get([
                'LMRId'   => $LMRId,
                'fieldID' => 303,
            ]);
            $HUD_V_303 = Strings::toNumber($tblFileHUDTransaction->fieldValue);
            $inArray['HUD_V_303'] = Currency::formatDollarAmountWithDecimal($HUD_V_303); //Cash to close

            $tblFileHUDTransaction_207 = tblFileHUDTransaction::Get([
                'LMRId'   => $LMRId,
                'fieldID' => 207,
            ]);
            $HUD_FN_207 = $tblFileHUDTransaction_207->fieldName ?? '';
            $HUD_V_207 = $tblFileHUDTransaction_207->fieldValue ?? '';
            $inArray['HUD_FN_207'] = $HUD_FN_207;
            $inArray['HUD_V_207'] = Currency::formatDollarAmountWithDecimal($HUD_V_207);

            $tblFileHUDTransaction_208 = tblFileHUDTransaction::Get([
                'LMRId'   => $LMRId,
                'fieldID' => 208,
            ]);
            $HUD_FN_208 = $tblFileHUDTransaction_208->fieldName ?? '';
            $HUD_V_208 = $tblFileHUDTransaction_208->fieldValue ?? '';
            $inArray['HUD_FN_208'] = $HUD_FN_208;
            $inArray['HUD_V_208'] = Currency::formatDollarAmountWithDecimal($HUD_V_208);

            $tblFileHUDTransaction_209 = tblFileHUDTransaction::Get([
                'LMRId'   => $LMRId,
                'fieldID' => 209,
            ]);
            $HUD_FN_209 = $tblFileHUDTransaction_209->fieldName ?? '';
            $HUD_V_209 = $tblFileHUDTransaction_209->fieldValue ?? '';
            $inArray['HUD_FN_209'] = $HUD_FN_209;
            $inArray['HUD_V_209'] = Currency::formatDollarAmountWithDecimal($HUD_V_209);

            //HUD - Per diem interest daily HUD
            $HUDInfo = tblLMRHUDItemsPayableLoan::get([
                'LMRId'     => $LMRId
                , 'fieldID' => 806
            ]);
            $HUD_BS_806 = 0;
            if ($HUDInfo) {
                $HUD_BS_806 = $HUDInfo->borrowerSettlementValue;
            }
            $PerDiemInterest = HUDController::calculatePerDiemInterestHUD($lien1Rate, $HUD_V_202, $HUD_BS_806);
            $inArray['perDiemInterestDailyHUD'] = Currency::formatDollarAmountWithDecimal($PerDiemInterest);

            if (array_key_exists($LMRID, $LOExplanationArray)) {
                $LOExplanation = $LOExplanationArray[$LMRID];
                if (count($LOExplanation) > 0) {
                    $inArray['borComment'] = trim($LOExplanation['borComment']);
                }
            }

            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            /*  HMLO Info */

            if (array_key_exists($LMRID, $HMLOInfoArray)) {
                $HMLOInfo = $HMLOInfoArray[$LMRID];
                if (count($HMLOInfo) > 0) {
                    $inArray['rehabCost'] = Currency::formatDollarAmountWithDecimal(trim($HMLOInfo['rehabCost']));
                    $inArray['rehabCostText'] = Strings::makewords(Strings::replaceCommaValues($inArray['rehabCost']));
                    if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_BRIDGE_LOAN_RENOVATION) {
                        $inArray['rehabCost'] = 'N/A';
                        $inArray['rehabCostText'] = 'N/A';
                    }
                    $inArray['midFicoScore'] = trim($HMLOInfo['midFicoScore']);
                    $inArray['midFicoScoreCoBor'] = trim($HMLOInfo['midFicoScoreCoBor']);
                    $inArray['borCreditScoreRange'] = trim($HMLOInfo['borCreditScoreRange']);
                    $inArray['coBorCreditScoreRange'] = trim($HMLOInfo['coBorCreditScoreRange']);
                    $inArray['coBorExperianScore'] = trim($HMLOInfo['coBorExperianScore']);
                    $inArray['coBorTransunionScore'] = trim($HMLOInfo['coBorTransunionScore']);
                    $inArray['coBorEquifaxScore'] = trim($HMLOInfo['coBorEquifaxScore']);
                    $inArray['borExperianScore'] = trim($HMLOInfo['borExperianScore']);
                    $inArray['borEquifaxScore'] = trim($HMLOInfo['borEquifaxScore']);
                    $inArray['borTransunionScore'] = trim($HMLOInfo['borTransunionScore']);
                    $inArray['borrowerCitizenship'] = trim($HMLOInfo['borrowerCitizenship']);
                    $inArray['borrowerDependentAges'] = trim($HMLOInfo['agesOfDependent']);
                    $inArray['borrowerNumberDependents'] = trim($HMLOInfo['numberOfDependents']);
                }
            }

            if (count($BorrowerExpInfo) > 0) {
                if (array_key_exists($LMRID, $BorrowerExpInfo)) {
                    $inArray['fixAndFlipExp'] = $BorrowerExpInfo[$LMRID]['borNoOfFlippingExperience'];
                    $inArray['fixAndFlipPropComplete'] = $BorrowerExpInfo[$LMRID]['borNoOfREPropertiesCompleted'];
                    $inArray['liquidAssets'] = Currency::formatDollarAmountWithDecimal(trim($BorrowerExpInfo[$LMRID]['liquidAssets']));
                    $inArray['borNoOfOwnProp'] = $BorrowerExpInfo[$LMRID]['borNoOfOwnProp'];
                    $inArray['activePrj'] = $BorrowerExpInfo[$LMRID]['borNoOfProjectCurrently'];

                    $inArray['coBorrowerFixAndFlipExp'] = $BorrowerExpInfo[$LMRID]['coBorNoOfFlippingExperience'];
                    $inArray['coBorrowerLiquidAssets'] = Currency::formatDollarAmountWithDecimal(trim($BorrowerExpInfo[$LMRID]['coBorliquidReserves']));
                    $inArray['coBorrowerActivePrj'] = $BorrowerExpInfo[$LMRID]['coBorNoOfProjectCurrently'];
                    $inArray['coBorNoOfOwnProp'] = $BorrowerExpInfo[$LMRID]['coBorNoOfOwnProp'];
                    $inArray['coBorrowerFixAndFlipPropComplete'] = $BorrowerExpInfo[$LMRID]['coBorNoOfREPropertiesCompleted'];
                }
            }


            /* PC Info */
            if (count($PCInfo) > 0) {
                if (array_key_exists($PCID, $PCInfo)) {
                    $appendComma = '';
                    $PCAddrInfo = '';
                    $PCAddress = '';
                    $PCCity = '';
                    $PCState = '';
                    $PCZip = '';
                    $procCompanyName = '';
                    $PCPhone = '';
                    $attorneyEmail = '';
                    $PCLogo = '';
                    $attorneyFName = '';
                    $attorneyMName = '';
                    $attorneyLName = '';
                    $attorneyName = '';
                    $PCFax = '';
                    $PCCell = $nmlsid = '';
                    $companyWebsite = '';
                    $borrowerLoginURL = '';
                    $PCStateName = '';

                    $procCompanyName = ucwords(trim($PCInfo[$PCID]['processingCompanyName']));
                    $PCAddress = trim($PCInfo[$PCID]['attorneyAddress']);
                    $PCCity = trim($PCInfo[$PCID]['attorneyCity']);
                    $PCState = trim($PCInfo[$PCID]['attorneyState']);
                    $PCZip = trim($PCInfo[$PCID]['attorneyZipCode']);
                    $PCPhone = Strings::formatPhoneNumber(trim($PCInfo[$PCID]['attorneyTelephone']));
                    $attorneyEmail = trim($PCInfo[$PCID]['attorneyEmail']);
                    $PCLogo = trim($PCInfo[$PCID]['procCompLogo']);
                    $attorneyFName = trim($PCInfo[$PCID]['attorneyFName']);
                    $attorneyMName = trim($PCInfo[$PCID]['attorneyMName']);
                    $attorneyLName = trim($PCInfo[$PCID]['attorneyLName']);
                    $attorneyName = $attorneyFName . ' ' . $attorneyMName . ' ' . $attorneyLName;
                    $PCFax = Strings::formatPhoneNumber(trim($PCInfo[$PCID]['attorneyFascimile']));
                    $PCCell = Strings::formatPhoneNumber(trim($PCInfo[$PCID]['attorneyCell']));
                    $companyWebsite = trim($PCInfo[$PCID]['processingCompanyWebsite']);
                    $borrowerLoginURL = trim($PCInfo[$PCID]['borrowerLoginURL']);
                    $nmlsid = trim($PCInfo[$PCID]['NMLSID']);
                    $PCStateName = trim($PCInfo[$PCID]['PCStateName']);

                    if ($borrowerLoginURL == '') { // If borrowerLoginURL is blank means default added this Link On Sep 08, 2017
                        if ($PCInfo[$PCID]['isPLO']) {
                            $borrowerLoginURL = 'https://' . $_SERVER['HTTP_HOST'] . '/client'; // For private labeld PC
                        } else {
                            $borrowerLoginURL = 'https://app.lendingwise.com/client';
                        }
                    }

                    if ($PCAddress != '') {
                        $PCAddrInfo = $PCAddress;
                        $appendComma = '<br>';
                    }
                    if ($PCCity != '') {
                        $PCAddrInfo .= $appendComma . $PCCity;
                        $appendComma = ', ';
                    }
                    if ($PCState != '') {
                        $PCAddrInfo .= $appendComma . $PCState;
                        $appendComma = ' ';
                    }
                    if ($PCZip != '') {
                        $PCAddrInfo .= $appendComma . $PCZip;
                        $appendComma = '';
                    }
                    $appendComma = '';
                    $PCsInfo = '';
                    $PCsInfo = '<b>' . $procCompanyName . '</b>';
                    $appendComma = '<br>';

                    if ($PCAddrInfo != '') {
                        $PCsInfo .= $appendComma . $PCAddrInfo;
                        $appendComma = '<br>';
                    }
                    if ($PCPhone != '') {
                        $PCsInfo .= $appendComma . 'Contact Phone: ' . $PCPhone;
                        $appendComma = '<br>';
                    }
                    if ($attorneyEmail != '') {
                        $PCsInfo .= $appendComma . 'Contact E-mail: ' . $attorneyEmail;
                        $appendComma = '<br>';
                    }
                    $appendComma = '';
                    $myStr = '<table><tr><td>';

                    if (trim($PCLogo) != '') {
                        $inArray['PCLogo'] = "<img src=\"" . $CONST_SITE_URL . 'PCLogo/' . $PCLogo . "\">";
                        $myStr .= "<img src=\"" . $CONST_SITE_URL . 'PCLogo/' . $PCLogo . "\">";
                    } else {
                        $inArray['PCLogo'] = '';
                    }

                    $myStr .= "</td><td style=\"text-align:left;\">" . $PCsInfo . '</td></tr></table>';

                    $inArray['PCName'] = $procCompanyName;
                    $inArray['attorneyName'] = ucwords($attorneyName);
                    $inArray['attorneyEmail'] = $attorneyEmail;
                    $inArray['PCAddress'] = ucwords($PCAddress);
                    $inArray['PCCity'] = ucwords($PCCity);
                    $inArray['PCState'] = $PCState;
                    $inArray['PCZip'] = $PCZip;
                    $inArray['PCPhone'] = $PCPhone;
                    $inArray['PCFax'] = $PCFax;
                    $inArray['PCCell'] = $PCCell;
                    $inArray['nmlsid'] = $nmlsid;
                    $inArray['PCWebsite'] = $companyWebsite;
                    $inArray['borrowerLoginURL'] = $borrowerLoginURL;
                    $inArray['companyStateLong'] = $PCStateName;
                    /* Company Info */
                    $inArray['companyHeader'] = $myStr;
                    /* Company Info */
                    $inArray['servicerName'] = $PCInfo[$PCID]['servicerName'];
                    $inArray['loanServicerAddress'] = $PCInfo[$PCID]['servicerAddress'];
                    $inArray['loanServicerEmail'] = $PCInfo[$PCID]['servicerEmail'];
                    $inArray['loanServicerPhone'] = Strings::formatPhoneNumber($PCInfo[$PCID]['servicerPhone']);
                    $inArray['payOffPhone'] = Strings::formatPhoneNumber($PCInfo[$PCID]['payoffPhoneNo']);
                    $inArray['payOffRequestEmail'] = $PCInfo[$PCID]['payOffRequestEmail'];
                    $inArray['lenderPayableInfo'] = urldecode($PCInfo[$PCID]['lenderPayableInfo']);
                    $inArray['adminUserTitle'] = $PCInfo[$PCID]['adminUserTitle'];
                }
            }
            /* PC Info */
            if ($printExecutionTime == 1) {
                echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
            }
            $loggedInUserSignature = '';

            if ($userName != '') $loggedInUserSignature = $userName;
            if ($procCompanyName != '') $loggedInUserSignature .= '<br>' . $procCompanyName;
            if ($cellPhone != '') $loggedInUserSignature .= '<br>Cell: ' . $cellPhone;
            if ($fax != '') $loggedInUserSignature .= '<br>Fax: ' . $fax;
            if ($email != '') $loggedInUserSignature .= '<br>' . $email;

            $inArray['loggedInUserSignature'] = $loggedInUserSignature;
            $inArray['todaysDateLong'] = date('F j, Y');
            $LRInfo2 = [];
            if (array_key_exists($LMRID, $listingRealtorInfo2)) {
                $LRInfo2 = $listingRealtorInfo2[$LMRID];
                if (count($LRInfo2) > 0) {
                    $inArray['totalJudgement'] = trim($LRInfo2['totalJudgement']);
                }
            }

            /* vendor and equipment info */
            if (array_key_exists($LMRID, $equipmentInfoArray ?? [])) {
                $equipmentInfo = $equipmentInfoArray[$LMRID];
                if (count($equipmentInfo) > 0) {
                    $inArray['vendorName'] = $equipmentInfo[0]['vendorName'];
                    $inArray['vendorPhone'] = $equipmentInfo[0]['vendorPhone'];
                    $inArray['vendorContact'] = $equipmentInfo[0]['vendorContact'];
                    $inArray['equipmentType'] = $equipmentInfo[0]['equipmentType'];
                    $inArray['equipmentDetailedType'] = $equipmentInfo[0]['equipmentDetailedType'];
                    $inArray['vinSerialNumber'] = $equipmentInfo[0]['vinSerialNumber'];
                    $inArray['year'] = $equipmentInfo[0]['YEAR'];
                    $inArray['salePrice'] = Currency::formatDollarAmountWithDecimal($equipmentInfo[0]['salePrice']);
                    $inArray['make'] = $equipmentInfo[0]['make'];
                    $inArray['model'] = $equipmentInfo[0]['model'];
                    $inArray['mileage'] = $equipmentInfo[0]['mileage'];
                    $inArray['quantity'] = $equipmentInfo[0]['quantity'];
                    $inArray['newOrUsed'] = $equipmentInfo[0]['own'];
                    $inArray['equipmentDescription'] = $equipmentInfo[0]['equipmentDescription'];
                    $vendorEquipmentTable = '<table>';
                    $vendorEquipmentArray = [];
                    $vendorEquipmentData = 'Vendor Name:<br>
Vendor Phone:<br>
Vendor Contact:<br>
Equipment Type:<br>
Equipment Detailed Type:<br>
Vin or Serial Number:<br>
Year:<br>
Sale Price:<br>
New Or Used:<br>
Make:<br>
Model:<br>
Mileage:<br>
Quantity:<br>
Equipment Description:';
                    for ($eqp = 0; $eqp < count($equipmentInfo); $eqp++) {
                        $vendorName = $equipmentInfo[$eqp]['vendorName'];
                        $vendorPhone = $equipmentInfo[$eqp]['vendorPhone'];
                        $vendorContact = $equipmentInfo[$eqp]['vendorContact'];
                        $equipmentType = $equipmentInfo[$eqp]['equipmentType'];
                        $equipmentDetailedType = $equipmentInfo[$eqp]['equipmentDetailedType'];
                        $vinSerialNumber = $equipmentInfo[$eqp]['vinSerialNumber'];
                        $year = $equipmentInfo[$eqp]['YEAR'];
                        $salePrice = $equipmentInfo[$eqp]['salePrice'];
                        $make = $equipmentInfo[$eqp]['make'];
                        $model = $equipmentInfo[$eqp]['model'];
                        $mileage = $equipmentInfo[$eqp]['mileage'];
                        $quantity = $equipmentInfo[$eqp]['quantity'];
                        $newOrUsed = $equipmentInfo[$eqp]['own'];
                        $equipmentDescription = $equipmentInfo[$eqp]['equipmentDescription'];
                        $vendorEquipmentDataValue = $vendorName . '<br>' .
                            $vendorPhone . '<br>' .
                            $vendorContact . '<br>' .
                            $equipmentType . '<br>' .
                            $equipmentDetailedType . '<br>' .
                            $vinSerialNumber . '<br>' .
                            $year . '<br>' .
                            $salePrice . '<br>' .
                            $newOrUsed . '<br>' .
                            $make . '<br>' .
                            $model . '<br>' .
                            $mileage . '<br>' .
                            $quantity . '<br>' .
                            $equipmentDescription;
                        $vendorEquipmentTable .= '<tr>
                                                    <td>Vendor Name:<br>
Vendor Phone:<br>
Vendor Contact:<br>
Equipment Type:<br>
Equipment Detailed Type:<br>
Vin or Serial Number:<br>
Year:<br>
Sale Price:<br>
New Or Used:<br>
Make:<br>
Model:<br>
Mileage:<br>
Quantity:<br>
Equipment Description:</td>
                                                    <td>' . $vendorName . '<br>' .
                            $vendorPhone . '<br>' .
                            $vendorContact . '<br>' .
                            $equipmentType . '<br>' .
                            $equipmentDetailedType . '<br>' .
                            $vinSerialNumber . '<br>' .
                            $year . '<br>' .
                            $salePrice . '<br>' .
                            $newOrUsed . '<br>' .
                            $make . '<br>' .
                            $model . '<br>' .
                            $mileage . '<br>' .
                            $quantity . '<br>' .
                            $equipmentDescription . '</td>
                                                   </tr>';
                        if ($googleDocs == 1) {
                            $vendorEquipmentArray[] = [
                                ['fieldColor' => '', 'fieldText' => "$vendorEquipmentData"],
                                ['fieldColor' => '', 'fieldText' => "$vendorEquipmentDataValue"],
                            ];
                        }
                    }
                    $vendorEquipmentTable .= '</table>';
                    if ($googleDocs == 1) {
                        $inArray['tables'][] = [
                            'mergeTableTag'  => '##Vendor and Equipment Info##',
                            'mergeTableData' => $vendorEquipmentArray,
                        ];
                    } else {
                        $inArray['venforEquipmentInfo'] = $vendorEquipmentTable;
                    }

                }
            }

            /* Credit Memo */
            $creditMemoArray = [];
            if (array_key_exists($LMRID, $getCreditMemo)) {
                $creditMemoArray = $getCreditMemo[$LMRID];
                $borrowerGuarantorBackgroundDescription = '';
                $borrowerGuarantorFinancialsDescription = '';
                $businessFinancialsDescription = '';
                $projectedFinancialsDescription = '';
                $propertyCollateralDescription = '';
                $tenantDescription = '';
                $loanStructureDescription = '';
                $strengthsWeaknessesMitigantsDescription = '';
                $otherDescription = '';

                if (count($creditMemoArray) > 0) {

                    for ($p = 0; $p < count($creditMemoArray); $p++) {
                        $creditMemoCategory = trim($creditMemoArray[$p]['memoCategory']);
                        switch ($creditMemoCategory) {
                            case 1 :
                                $borrowerGuarantorBackgroundDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 2 :
                                $borrowerGuarantorFinancialsDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 3 :
                                $businessFinancialsDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 4 :
                                $projectedFinancialsDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 5 :
                                $tenantDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 6 :
                                $propertyCollateralDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 7 :
                                $loanStructureDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 8 :
                                $strengthsWeaknessesMitigantsDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                            case 9 :
                                $otherDescription = trim($creditMemoArray[$p]['memoDescription']);
                                break;
                        }
                    }
                    $inArray['BorrowerGuarantorBackgroundDescription'] = trim($borrowerGuarantorBackgroundDescription);
                    $inArray['BorrowerGuarantorFinancialsDescription'] = trim($borrowerGuarantorFinancialsDescription);
                    $inArray['BusinessFinancialsDescription'] = trim($businessFinancialsDescription);
                    $inArray['ProjectedFinancialsDescription'] = trim($projectedFinancialsDescription);
                    $inArray['PropertyCollateralDescription'] = trim($propertyCollateralDescription);
                    $inArray['TenantDescription'] = trim($tenantDescription);
                    $inArray['LoanStructureDescription'] = trim($loanStructureDescription);
                    $inArray['StrengthsWeaknessesMitigantsDescription'] = trim($strengthsWeaknessesMitigantsDescription);
                    $inArray['OtherDescription'] = trim($otherDescription);
                }
            }
            $inArray['esignLink'] = '##Esign Links##';
            $inArray['rentRollRequest'] = $CONST_SITE_URL . 'rentRollWebForm.php?lid=' . cypher::myEncryption($LMRId) . '&op=' . cypher::myEncryption('BO') . '&edit=' . cypher::myEncryption('1');

            generateWebformLinks::$fileInfo = $fileInfo;
            generateWebformLinks::init($LMRId);

            $inArray['rentRollNonSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$rentRollWebFormBO . "\">Link to Rent Roll</a>";
            $inArray['rentRollSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$rentRollWebFormBOSign . "\">Link to Rent Roll</a>";
            $inArray['rentRollSignReadOnly'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$rentRollWebFormBOReadOnly . "\">Link to Rent Roll</a>";


            $inArray['uniformResidentialNonSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$uniformResLoanApp . "\">Link to Uniform Residential Loan App</a>";
            $inArray['uniformResidentialSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$uniformResLoanAppSign . "\">Link to Uniform Residential Loan App</a>";
            $inArray['uniformResidentialReadOnly'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$uniformResLoanAppReadOnly . "\">Link to Uniform Residential Loan App</a>";

            $inArray['pfsWebformNonSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$PFSWebformBO . "\">Link to Personal Financial Statement</a>";
            $inArray['pfsWebformSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$PFSWebformBOSign . "\">Link to Personal Financial Statement</a>";
            $inArray['pfsWebformReadOnly'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$PFSWebformBOReadOnly . "\">Link to Personal Financial Statement</a>";


            $inArray['cashFlowWebformNonSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$CashFlowWebformBO . "\">Link to Property Cash Flow</a>";
            $inArray['cashFlowWebformSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$CashFlowWebformBOSign . "\">Link to Property Cash Flow</a>";
            $inArray['cashFlowWebformSignReadOnly'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$CashFlowWebformBOReadOnly . "\">Link to Property Cash Flow</a>";

            $inArray['ACHCheckNonSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$paymentInfoWebFormBO . "\">Link to ACH/Check -Non Sign </a>";
            $inArray['ACHCheckSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$paymentInfoWebFormBOSign . "\">Link to ACH/Check -Sign </a>";
            $inArray['ACHCheckReadOnly'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$paymentInfoWebFormBOReadOnly . "\">Link to ACH/Check -Read Only </a>";

            $inArray['creditCardNonSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$creditPaymentInfoWebFormBO . "\">Link to Credit Card Info  -Non Sign </a>";
            $inArray['creditCardSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$creditPaymentInfoWebFormBOSign . "\">Link to Credit Card Info  -Sign </a>";
            $inArray['creditCardReadOnly'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$creditPaymentInfoWebFormBOReadOnly . "\">Link to Credit Card Info  -Read Only </a>";

            $inArray['ACHCheckCreditCardNonSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$ACHCreditPaymentInfoWebFormBO . "\">Link to ACH/Check/CreditCard  -Non Sign </a>";
            $inArray['ACHCheckCreditCardSign'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$ACHCreditPaymentInfoWebFormBOSign . "\">Link to ACH/Check/CreditCard  -Sign </a>";
            $inArray['ACHCheckCreditCardReadOnly'] = "<a target=\"_blank\" href=\"" . generateWebformLinks::$ACHCreditPaymentInfoWebFormBOReadOnly . "\">Link to ACH/Check/CreditCard  -Read Only </a>";

            //HUD-FundingClosingInfo
            $inArray['wireRequestedFromFunderDate'] = $HUDFundingClosingInfoData->wireRequestedFromFunderDate;
            $inArray['wireSentDate'] = $HUDFundingClosingInfoData->wireSentDate;
            $inArray['fedReferenceWireConfirmation'] = $HUDFundingClosingInfoData->fedReferenceWireConfirmation;
            $inArray['wireSettlementBankName'] = $HUDFundingClosingInfoData->wireSettlementBankName;
            $inArray['wireSettlementABANumber'] = $HUDFundingClosingInfoData->wireSettlementABANumber;
            $inArray['wireSettlementAccountNumber'] = $HUDFundingClosingInfoData->wireSettlementAccountNumber;
            $inArray['wireSettlementAccountHolderName'] = $HUDFundingClosingInfoData->wireSettlementAccountHolderName;
            $inArray['wireTitleEscrowReferenceNumber'] = $HUDFundingClosingInfoData->wireTitleEscrowReferenceNumber;
            $inArray['wireAmountRequested'] = $HUDFundingClosingInfoData->wireAmountRequested;

            $inArray['disclosureSentDate'] = Dates::formatDateWithRE(LMRequest::myFileInfo()->getFileAdminInfo()->disclosureSentDate, 'YMD', 'm/d/Y');
            $inArray['loanDocumentDate'] = Dates::formatDateWithRE(LMRequest::myFileInfo()->getFileAdminInfo()->loanDocumentDate, 'YMD', 'm/d/Y');

            //Draws Merge Tags
            //$inArray['rehabConstructionFinanced'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostFinanced);
            $inArray['rehabConstructionFinanced'] = Currency::formatDollarAmountWithDecimal($rehabCostFinanced);
            //totalDrawsFunded line#2867
            //availableBudget  line#2874
            $projectCompletionPercentage = 0;
            if ($rehabCostFinanced > 0) {
                $projectCompletionPercentage = Strings::replaceCommaValues($totalDrawsFunded) / Strings::replaceCommaValues($rehabCostFinanced) * 100;
            }
            $inArray['projectCompletionPercentage'] = Currency::formatDollarAmountWithDecimal($projectCompletionPercentage);
            $inArray['defaultDrawFee'] = Currency::formatDollarAmountWithDecimalZeros(LMRequest::myFileInfo()->fileHMLOInfo()->defaultDrawFee);

            $budgetAndDrawsObjects = LMRequest::myFileInfo()->budgetAndDrawsInfo();

            $summaryOfDraws = '<table width="100%">
                                <tr>
                                    <td>Date Requested</td>
                                    <td>Status</td>
                                    <td>Date Funded</td>
                                    <td>Approved Draw $</td>
                                    <td>Funded to borrower</td>
                                    <td>Draw Description</td>
                                </tr>';

            if ($googleDocs == 1) {
                $summaryOfDrawsValueArr[] = [
                    ['fieldColor' => '', 'fieldText' => 'Date Requested'],
                    ['fieldColor' => '', 'fieldText' => 'Status'],
                    ['fieldColor' => '', 'fieldText' => 'Date Funded'],
                    ['fieldColor' => '', 'fieldText' => 'Approved Draw $'],
                    ['fieldColor' => '', 'fieldText' => 'Funded to borrower'],
                    ['fieldColor' => '', 'fieldText' => 'Draw Description'],
                ];
            }
            foreach ($budgetAndDrawsObjects as $drawRow) {
                $dateRequested = Dates::formatDateWithRE($drawRow->dateRequested, 'YMD', 'm/d/Y');
                $dateFunded = Dates::formatDateWithRE($drawRow->dateFunded, 'YMD', 'm/d/Y');
                $summaryOfDraws .= '<tr>
                                <td>' . $dateRequested . '</td>
                                <td>' . $drawRow->drawStatus . '</td>
                                <td>' . $dateFunded . '</td>
                                <td>' . Currency::formatDollarAmountWithDecimalZeros($drawRow->drawApproved) . '</td>
                                <td>' . Currency::formatDollarAmountWithDecimalZeros($drawRow->drawFunded) . '</td>
                                <td>' . $drawRow->draw_notes . '</td>
</tr>';

                if ($googleDocs == 1) {
                    $summaryOfDrawsValueArr[] = [
                        ['fieldColor' => '', 'fieldText' => "$dateRequested"],
                        ['fieldColor' => '', 'fieldText' => "$drawRow->drawStatus"],
                        ['fieldColor' => '', 'fieldText' => "$dateFunded"],
                        ['fieldColor' => '', 'fieldText' => Currency::formatDollarAmountWithDecimalZeros($drawRow->drawApproved)],
                        ['fieldColor' => '', 'fieldText' => Currency::formatDollarAmountWithDecimalZeros($drawRow->drawFunded)],
                        ['fieldColor' => '', 'fieldText' => "$drawRow->draw_notes"],
                    ];
                }
            }
            $summaryOfDraws .= '
</table>';
            if ($googleDocs == 1) {
                $inArray['tables'][] = [
                    'mergeTableTag'  => '##Summary of Draws##',
                    'mergeTableData' => $summaryOfDrawsValueArr,
                ];
            } else {
                $inArray['summaryOfDraws'] = $summaryOfDraws;
            }


            //Draws Merge Tags Latest
            $budgetAndDrawsLatest = budgetAndDrawsInfo::getLatestInfo($LMRId);
            $inArray['drawRequestLatest'] = Currency::formatDollarAmountWithDecimalZeros($budgetAndDrawsLatest->drawRequest);
            $inArray['drawsFeeLatest'] = Currency::formatDollarAmountWithDecimalZeros($budgetAndDrawsLatest->drawsFee);
            $inArray['dateRequestedLatest'] = Dates::formatDateWithRE($budgetAndDrawsLatest->dateRequested, 'YMD', 'm/d/Y');
            $inArray['drawStatusLatest'] = $budgetAndDrawsLatest->drawStatus;
            $inArray['drawApprovedPercentageLatest'] = $budgetAndDrawsLatest->drawApproved;
            $inArray['approvedDrawAmountLatest'] = Currency::formatDollarAmountWithDecimalZeros($budgetAndDrawsLatest->amountAddedToTotalDrawsFunded);
            $inArray['fundedToBorrowerLatest'] = Currency::formatDollarAmountWithDecimalZeros($budgetAndDrawsLatest->drawFunded);
            $inArray['dateFundedLatest'] = Dates::formatDateWithRE($budgetAndDrawsLatest->dateFunded, 'YMD', 'm/d/Y');
            $inArray['drawNotesLatest'] = $budgetAndDrawsLatest->draw_notes;
            //Draws Merge Tags Latest End

            //ACT Tags Start
            $achData = LMRequest::File()->getTblACHInfo_by_LMRID();
            $inArray['ACHNameOfBank'] = $achData->bankName;
            $inArray['ACHAccountHolderName'] = $achData->accountName;
            $inArray['ACHRoutingNumber'] = Cypher::myDecryption($achData->routingNo);
            $inArray['ACHAccountNumber'] = Cypher::myDecryption($achData->accountNo);
            $inArray['ACHFirstWithdrawalDate'] = Dates::formatDateWithRE($achData->depositDate, 'YMD', 'm/d/Y');

            //Conditional Fields
            $borrowerType = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->borrowerType;
            if ($borrowerType == 'Entity') {
                //Entity Address
                $inArray['ACHBorrowerStreet'] = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->entityAddress;
                $inArray['ACHBorrowerCity'] = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->entityCity;
                $inArray['ACHBorrowerState'] = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->entityState;
                $inArray['ACHBorrowerZip'] = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->entityZip;
            } else {
                //Mailing Address
                $inArray['ACHBorrowerStreet'] = LMRequest::File()->mailingAddress . ' ' . LMRequest::File()->mailingUnit;
                $inArray['ACHBorrowerCity'] = LMRequest::File()->mailingCity;
                $inArray['ACHBorrowerState'] = LMRequest::File()->mailingState;
                $inArray['ACHBorrowerZip'] = LMRequest::File()->mailingZip;
            }

            $inArray['ACHWithdrawalAmount'] = LMRequest::getAchWithdrawalAmount();
            $inArray['ACTAccountType'] = $achData->ACTBankAccountType;
            $inArray['ACHBorrowerName'] = LMRequest::getAchBorrowerName();
            //ACT Tags End

            //ECOA Tags
            $inArray['applicationDate'] = LMRequest::File()->receivedDate
                ? Dates::formatDateWithRE(LMRequest::File()->receivedDate, 'YMD', 'm/d/Y')
                : '';
            $inArray['noiaIssueDate'] = LMRequest::myFileInfo()->adverseActionInfo()->dateNOIAEmailSent
                ? Dates::formatDateWithRE(LMRequest::myFileInfo()->adverseActionInfo()->dateNOIAEmailSent, 'YMD', 'm/d/Y')
                : '';
            $inArray['initialUWStatusDate'] = LMRequest::getInitialUWStatusDate();
            $inArray['suspenseNoticeIssueDate'] = creditDecisionController::getSuspenseNoticeIssueDate($LMRId);
            $inArray['approvalDate'] = LMRequest::getApprovalDate();
            $inArray['adverseActionDate'] = LMRequest::myFileInfo()->adverseActionInfo()->actionDate
                ? Dates::formatDateWithRE(LMRequest::myFileInfo()->adverseActionInfo()->actionDate, 'YMD', 'm/d/Y')
                : '';
            $inArray['actionDate'] = $inArray['adverseActionDate'];
            $inArray['dateDenied'] = LMRequest::myFileInfo()->adverseActionInfo()->dateDenied
                ? Dates::formatDateWithRE(LMRequest::myFileInfo()->adverseActionInfo()->dateDenied, 'YMD', 'm/d/Y')
                : '';
            //ECOA Tags End

            /* Loan Info V2 */
            $inArray['isPropertyHaveSubordinateFinancing'] = Strings::booleanTextVal(LMRequest::myFileInfo()->getLoanPropertySummary()->isPropertyHaveSubordinateFinancing);
            $inArray['subordinateFinancingAmount'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->subordinateFinancingAmount);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && Strings::booleanTextVal(LMRequest::myFileInfo()->getLoanPropertySummary()->isPropertyHaveSubordinateFinancing) == 'No') {
                $inArray['subordinateFinancingAmount'] = 'N/A';

            }
            $inArray['totalPropertiesPurchasePrice'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesPurchasePrice);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && ($HMLOPropInfo['typeOfHMLOLoanRequesting'] == 'Rate & Term Refinance' || $HMLOPropInfo['typeOfHMLOLoanRequesting'] == 'Cash-Out / Refinance')) {
                $inArray['totalPropertiesPurchasePrice'] = 'N/A';
            }
            $inArray['totalPropertiesEstimatedAsIs'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesEstimatedAsIs);
            $inArray['totalPropertiesAppraisedAsIs'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesAppraisedAsIs);
            $inArray['totalPropertiesAllocatedLoanAmount'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesAllocatedLoanAmount);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram == loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['totalPropertiesAllocatedLoanAmount'] = 'N/A';
            }
            $inArray['totalPropertiesLTP'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLTP);
            $inArray['totalPropertiesLTV'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLTV);
            $inArray['totalPropertiesLoanAmount'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount);
            $inArray['LTCTotalLoanAmount'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->LTCTotalLoanAmount);
            $inArray['bridgeCombinedLoanToValue'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->bridgeCombinedLoanToValue);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && Strings::booleanTextVal(LMRequest::myFileInfo()->getLoanPropertySummary()->isPropertyHaveSubordinateFinancing) == 'No') {
                $inArray['bridgeCombinedLoanToValue'] = 'N/A';
            }
            $inArray['totalPropertiesDSCR'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesDSCR);
            $inArray['totalPropertiesITIA'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesITIA);
            $inArray['monthly30YrPIPayment'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->monthly30YrPIPayment);
            $inArray['totalPropertiesNetOperatingIncome'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesNetOperatingIncome);
            $inArray['totalPropertiesPITIA'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesPITIA);
            $inArray['totalPropertiesMonthlyTotalExpenses'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyTotalExpenses);
            $inArray['totalPropertiesMonthlyHOAFees'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyHOAFees);
            $inArray['totalPropertiesMonthlyTaxes'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyTaxes);
            $inArray['totalPropertiesMonthlyInsurance'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesMonthlyInsurance);
            $inArray['propertyTotalProjectCost'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->propertyTotalProjectCost);

            $inArray['totalVerifiedAssets'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->assetInfo()->totalVerifiedAssets);

            $inArray['GUCLoanToCost'] = LMRequest::myFileInfo()->getLoanPropertySummary()->GUCLoanToCost ? LMRequest::myFileInfo()->getLoanPropertySummary()->GUCLoanToCost . '%' : '';
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['GUCLoanToCost'] = 'N/A';
            }
            $inArray['softCostPercentageOfBudget'] = LMRequest::myFileInfo()->getLoanPropertySummary()->softCostPercentageOfBudget ? LMRequest::myFileInfo()->getLoanPropertySummary()->softCostPercentageOfBudget . '%' : '';
            $inArray['initialLoanToCost'] = LMRequest::myFileInfo()->getLoanPropertySummary()->initialLoanToCost ? LMRequest::myFileInfo()->getLoanPropertySummary()->initialLoanToCost . '%' : '';
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['initialLoanToCost'] = 'N/A';
            }
            $inArray['grossProfitMargin'] = LMRequest::myFileInfo()->getLoanPropertySummary()->grossProfitMargin ? LMRequest::myFileInfo()->getLoanPropertySummary()->grossProfitMargin . '%' : '';
            $inArray['targetLTC'] = LMRequest::myFileInfo()->getLoanPropertySummary()->targetLTC ? LMRequest::myFileInfo()->getLoanPropertySummary()->targetLTC . '%' : '';
            $inArray['financedAtClosing'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->financedAtClosing);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['financedAtClosing'] = 'N/A';
            }
            $inArray['GUCConstructionFinanced'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->GUCConstructionFinanced);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['GUCConstructionFinanced'] = 'N/A';
            }
            $inArray['propertyAfterRepairValue'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getPrimaryProperty()->getTblPropertiesDetails_by_propertyId()->propertyAfterRepairValue);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && ($loanProgram == loanProgram::CV3_RENTAL_LOAN || $loanProgram == loanProgram::CV3_RENTAL_LOAN_PORTFOLIO)) {
                $inArray['propertyAfterRepairValue'] = 'N/A';
            }
            $inArray['propertyAppraisalRehabbedValue'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getPrimaryProperty()->getTblPropertiesAppraiserDetails_by_propertyId()[0]->propertyAppraisalRehabbedValue);
            $inArray['constructionTotalProjectCost'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->constructionTotalProjectCost);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['constructionTotalProjectCost'] = 'N/A';
            }
            $inArray['interestReserveType'] = glGroundUpConstruction::$interestReserveType[LMRequest::myFileInfo()->getLoanPropertySummary()->interestReserveType];
            $inArray['interestReserve'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserve);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && !(Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserve))) {
                $inArray['interestReserve'] = 'N/A';
            }
            $inArray['contingencyAmount'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyAmount);
            $inArray['contingencyPercentage'] = LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage ? LMRequest::myFileInfo()->getLoanPropertySummary()->contingencyPercentage . '%' : '';
            $inArray['constructionHardCost'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->constructionHardCost);
            $inArray['constructionSoftCost'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->constructionSoftCost);
            $inArray['constructionType'] = glGroundUpConstruction::$constructionType[LMRequest::myFileInfo()->getLoanPropertySummary()->constructionType];
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram != loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['constructionType'] = 'N/A';
            }
            $inArray['bridgeAfterRepairValue'] = LMRequest::myFileInfo()->getLoanPropertySummary()->bridgeAfterRepairValue ? LMRequest::myFileInfo()->getLoanPropertySummary()->bridgeAfterRepairValue . '%' : '';
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && ($loanProgram == loanProgram::CV3_RENTAL_LOAN || $loanProgram == loanProgram::CV3_RENTAL_LOAN_PORTFOLIO)) {
                $inArray['bridgeAfterRepairValue'] = 'N/A';
            }
            $inArray['totalLoanInterestPayment'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment);

            //
            $inArray['financedInterestReserveM'] = LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserveMonths ? LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserveMonths : '';
            if (!LMRequest::myFileInfo()->getLoanPropertySummary()->financedInterestReserveMonths) {
                $inArray['financedInterestReserveM'] = 'N/A';
            }
            $inArray['aggregateqltv'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalAggregateQualifyingLTV);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram == loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['aggregateqltv'] = 'N/A';
            }
            $inArray['loanToValueLTV'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->bridgeLoanToValue);
            if (glCustomJobForProcessingCompany::isPC_CV3($PCID) && $loanProgram == loanProgram::CV3_GROUND_UP_CONSTRUCTION) {
                $inArray['loanToValueLTV'] = 'N/A';
            }
            $V2SmartMonthlyPayment = 0;
            if (in_array($loanProgram, [loanProgram::CV3_BRIDGE_LOAN_RENOVATION, loanProgram::CV3_GROUND_UP_CONSTRUCTION])) {
                $V2SmartMonthlyPayment = LMRequest::myFileInfo()->getPrimaryProperty()->getTblPropertiesAnalysis_by_propertyId()->propertyInterrestOnlyPayment;
            } elseif (LMRequest::myFileInfo()->fileHMLOPropertyInfo()->loanTerm == '360 Months- 30 Year Fixed - Fully Amortizing') {
                $V2SmartMonthlyPayment = LMRequest::myFileInfo()->getLoanPropertySummary()->monthly30YrPIPayment;
            } else {
                $V2SmartMonthlyPayment = LMRequest::myFileInfo()->getLoanPropertySummary()->totalLoanInterestPayment;
            }
            $inArray['V2SmartMonthlyPayment'] = Currency::formatDollarAmountWithDecimal($V2SmartMonthlyPayment);

            $inArray['cv3OriginationPoint'] = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3OriginationPoint;
            $inArray['cv3OriginationAmount'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3OriginationAmount);
            $inArray['cv3ReferralPoint'] = LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3ReferralPoint;
            $inArray['cv3ReferralAmount'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->cv3ReferralAmount);
            $inArray['sellerCreditsFee'] = Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->sellerCreditsFee);

            $fasInfoObject = LMRequest::myFileInfo()->fileLOChekingSavingInfo();
            $fasRowIndex = 1;
            foreach ($fasInfoObject as $fasRow) {
                $inArray['accountType' . $fasRowIndex] = $fasRow->accType;
                $inArray['shareValueInBond' . $fasRowIndex] = Currency::formatDollarAmountWithDecimal($fasRow->shareValueInBond);
                $inArray['nameofSecurity' . $fasRowIndex] = $fasRow->nameofSecurity;
                $inArray['cost' . $fasRowIndex] = Currency::formatDollarAmountWithDecimal($fasRow->cost);
                $inArray['marketValueQuote' . $fasRowIndex] = Currency::formatDollarAmountWithDecimal($fasRow->marketValueQuote);
                $inArray['dateofQuote' . $fasRowIndex] = Dates::formatDateWithRE($fasRow->dateofQuote, 'YMD', 'm/d/Y');
                $inArray['typeofPolicy' . $fasRowIndex] = $fasRow->typeofPolicy;
                $inArray['faceAmountFinance' . $fasRowIndex] = Currency::formatDollarAmountWithDecimal($fasRow->faceAmount);
                $inArray['amountBorrowed' . $fasRowIndex] = Currency::formatDollarAmountWithDecimal($fasRow->amountBorrowed);
                $inArray['beneficiaries' . $fasRowIndex] = $fasRow->beneficiaries;
                $inArray['nameofInstitution' . $fasRowIndex] = $fasRow->nameAddrOfBank;
                $inArray['accountTitledAs' . $fasRowIndex] = $fasRow->accountTitledAs;
                $inArray['account' . $fasRowIndex] = $fasRow->accountNumber;
                $inArray['balanceValue' . $fasRowIndex] = Currency::formatDollarAmountWithDecimal($fasRow->balance);
                $inArray['owners' . $fasRowIndex] = $fasRow->owners;
                $inArray['description' . $fasRowIndex] = $fasRow->description;
                $inArray['statementDate' . $fasRowIndex] = Dates::formatDateWithRE($fasRow->statementDate, 'YMD', 'm/d/Y');
                $inArray['pledged' . $fasRowIndex] = $fasRow->pledged;
                $fasRowIndex++;
            }

            $LTC2TotalOriginationInterest = $originationPointsValue + $brokerPointsValue + LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalOriginationInterest;
            $inArray['LTC2TotalOriginationInterest'] = Currency::formatDollarAmountWithDecimal($LTC2TotalOriginationInterest);

            $LTC2TotalInterestReserveInterest = $prepaidInterestReserve + LMRequest::myFileInfo()->getFileCalculatedValues()->LTC2_additionalReserveInterest;
            $inArray['LTC2TotalInterestReserveInterest'] = Currency::formatDollarAmountWithDecimal($LTC2TotalInterestReserveInterest);

            foreach ($allMergeTagsInContent as $tag) {
                if (stristr($tag, '##Avatar_Agent_') !== false) {
                    $m = [];
                    $res = preg_match('/##Avatar_Agent_(.*?)##/si', $tag, $m);
                    $agentNumber = $m[1] ?? null;
                    if (!$agentNumber) {
                        continue;
                    }
                    $tblAgent = tblAgent::Get(['userNumber' => cypher::myDecryption($agentNumber)]);
                    if (!$tblAgent) {
                        continue;
                    }
                    $tag = str_replace('#', '', $tag);
                    $inArray[$tag] = $tblAgent->getURL();
                    continue;
                }

                if (stristr($tag, '##Avatar_Branch_') !== false) {
                    $m = [];
                    $res = preg_match('/##Avatar_Branch_(.*?)##/si', $tag, $m);
                    $agentNumber = $m[1] ?? null;
                    if (!$agentNumber) {
                        continue;
                    }
                    $tblAgent = tblBranch::Get(['executiveId' => cypher::myDecryption($agentNumber)]);
                    if (!$tblAgent) {
                        continue;
                    }
                    $tag = str_replace('#', '', $tag);
                    $inArray[$tag] = $tblAgent->getURL();
                    continue;
                }

                if (stristr($tag, '##Avatar_Employee_') !== false) {
                    $m = [];
                    $res = preg_match('/##Avatar_Employee_(.*?)##/si', $tag, $m);
                    $agentNumber = $m[1] ?? null;
                    if (!$agentNumber) {
                        continue;
                    }
                    $tblAgent = tblAdminUsers::Get(['AID' => cypher::myDecryption($agentNumber)]);
                    if (!$tblAgent) {
                        continue;
                    }
                    $tag = str_replace('#', '', $tag);
                    $inArray[$tag] = $tblAgent->getURL();
                    continue;
                }
            }
            $customMergeTags = getAllDynamicFieldTags::PCCustomFieldMergeTags($PCID);
            foreach ($customMergeTags[$PCID] as $tag) {
                if (stristr($tag, '##Custom Field_') !== false) {
                    $m = [];
                    $res = preg_match('/##Custom Field_(.*?)##/si', $tag, $m);
                    $customFieldId = $m[1] ?? null;
                    $inArray[$tag] =  json_decode(LMRequest::myFileInfo()->customFields()[$customFieldId]->userEntered) ?? '';
                }
            }
            $inputArray[$LMRID] = $inArray;
        }
        if ($printExecutionTime == 1) {
            echo __LINE__ . ' Location : ' . Dates::Timestamp() . "\n";
        }
        $inputArray['dynamicMergeTags'] = $dynamicMergeTags;
        return $inputArray;
    }
}
