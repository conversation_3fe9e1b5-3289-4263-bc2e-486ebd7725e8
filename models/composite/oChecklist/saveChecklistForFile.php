<?php

namespace models\composite\oChecklist;

use models\Database2;
use models\standard\Dates;
use models\types\strongType;

/**
 *
 */
class saveChecklistForFile extends strongType
{
    /**
     *
     * Description    : Saved the required docs for file related
     * Date        : April 20, 2017
     * Author        : <PERSON><PERSON> & Venkatesh
     **/

    public static function getReport($inArray): int
    {
        $resID = 0;
        $lastInsID = [];
        $crntDate = Dates::Timestamp();

        $checklistId = trim($inArray['checklistId'] ?? 0);
        $checklistItem = trim($inArray['checklistItem'] ?? '');
        $fileID = $inArray['fileID'] ?? 0;
        $PCID = $inArray['PCID'] ?? 0;

        $dispOrder = trim($inArray['dispOrder'] ?? '');
        $checklistServiceTypeArray = $inArray['checklistServiceType'] ?? [];
        $checklistDesc = $inArray['checklistDesc'] ?? '';
        $refDocName = $inArray['refDocName'] ?? '';
        $refDocUrl = $inArray['refDocUrl'] ?? '';
        $categoryId = $inArray['categoryId'] ?? null;


        if ($checklistId) {
            $qry = ' 
                UPDATE 
                    tblFileChecklistModules 
                SET 
                    docName = :docName
                    , displayOrder = :displayOrder
                    , dStatus = 1
                    , description = :description
                    ,refDocName= :refDocName
                    ,refDocUrl= :refDocUrl
                    ,categoryId= :categoryId
                 WHERE 
                 FMID in(' . $checklistId . ')
                ';
            Database2::getInstance()->update($qry, [
                'docName'      => $checklistItem,
                'displayOrder' => $dispOrder,
                'description'  => $checklistDesc,
                'refDocName'   => $refDocName,
                'refDocUrl'    => $refDocUrl,
                'categoryId'   => $categoryId,
            ]);
            if ($checklistId > 0) {
                $inArray['PCMID'] = $checklistId;
                $resID = saveChecklistForFileRequiredBy::getReport($inArray);
            }
            return $resID;
        }

        if (!sizeof($checklistServiceTypeArray)) {
            return $resID;
        }

        foreach ($checklistServiceTypeArray as $type) {

            $tempChecklistServiceType = $type['serviceType'];

            foreach ($tempChecklistServiceType as $checklistServiceType) {
                $checklistServiceType = trim($checklistServiceType);
                if (doesServiceTypeExistForFile::getReport([
                    'fileID'      => $fileID,
                    'docName'     => $checklistItem,
                    'serviceType' => $checklistServiceType,
                    'moduleType'  => $type['moduleType']
                ])) {
                    $_SESSION['Notifications']['ErrorMsgs'][] = stripslashes($checklistItem) . ' Already Exists ';
                } elseif (doesDocumentExistInPC::getReport([
                    'docName' => $checklistItem,
                    'PCID' => $PCID,
                    'serviceType' => $checklistServiceType,
                    'moduleType'  => $type['moduleType']
                ])) {
                    $_SESSION['Notifications']['ErrorMsgs'][] = 'Document Name ' . stripslashes($checklistItem) . ' Exists in PC Level Required Documents List, Please Choose a Different Name';
                } else {
                    $insSql = '
                        INSERT INTO 
                            tblFileChecklistModules
                            (
                                    fileID
                                    , docName
                                    , serviceType
                                    , moduleType, createdDate, displayOrder
                                    , description, refDocName, refDocUrl,categoryId
                                    ) 
                            VALUES (
                                    :fileID , :docName , :serviceType 
                                    , :moduleType , :createdDate 
                                    , :displayOrder , :description 
                                    , :refDocName , :refDocUrl , :categoryId
                                    );';
                    $insSqlParams = [
                        'fileID'       => $fileID,
                        'docName'      => $checklistItem,
                        'serviceType'  => $checklistServiceType,
                        'moduleType'   => $type['moduleType'],
                        'createdDate'  => $crntDate,
                        'displayOrder' => $dispOrder ?: 0,
                        'description'  => $checklistDesc,
                        'refDocName'   => $refDocName,
                        'refDocUrl'    => $refDocUrl,
                        'categoryId'    => $categoryId,
                    ];
                    $lastInsID [] = $resID = Database2::getInstance()->insert($insSql, $insSqlParams);
                }
            }
        }

        if (sizeof($lastInsID)) {
            $inArray['PCMID'] = implode(',', $lastInsID);
            $resReportID = saveChecklistForFileRequiredBy::getReport($inArray);
        }

        return $resID;
    }
}
