<?php

namespace models\composite\oChecklist;

use models\composite\oFile\getFileInfo;
use models\Controllers\backoffice\DocsForm\requiredAdditionalCond;
use models\Database2;
use models\lendingwise\tblFile;
use models\requiredDocsForFileDTO;
use models\types\strongType;

/**
 *
 */
class requiredDocsForFile extends requiredDocsForFileDTO
{


    /**
     * @param int $LMRId
     * @param int $PCID
     * @param string|null $moduleType
     * @param string|null $serviceType
     * @param string|null $requiredBy
     * @return self[]
     */
    public static function getReportObjects(
        int     $LMRId,
        int     $PCID,
        ?string $moduleType,
        ?string $serviceType,
        ?string $requiredBy
    ): array
    {
        $list = [];
        $res = self::getReport([
            'LMRId'       => $LMRId,
            'PCID'        => $PCID,
            'moduleType'  => $moduleType,
            'serviceType' => $serviceType,
            'requiredBy'  => $requiredBy
        ]);
        foreach ($res as $row) {
            $list[] = new self($row);
        }
        return $list;
    }

    /**
     * Description : Get File Checklist Items
     */
    public static function getReport($ip): ?array
    {
        $LMRId = trim($ip['LMRId'] ?? '');
        $PCID = trim($ip['PCID'] ?? 0);
        $moduleType = trim($ip['moduleType'] ?? '');
        $serviceType = trim($ip['serviceType'] ?? '');
        $requiredBy = trim($ip['requiredBy'] ?? '');
        $branchId = 0;

        if ($LMRId) {
            $tblFile = tblFile::Get(['LMRId' => $LMRId]);
            $branchId = intval($tblFile->FBRID);
        }

        $params = [
            'requiredBy' => $requiredBy,
            'LMRID'      => $LMRId,
            'dStatus'    => 1,
            'fileID'     => $LMRId,
            'PCID'       => $PCID,
        ];
        $moduleType = explode(',', $moduleType);
        $serviceType = explode(',', $serviceType);

        foreach ($moduleType as $i => $type) {
            $type = trim($type);
            if (!$type) {
                unset($moduleType[$i]);
                continue;
            }
            $params['moduleType' . $i] = $type;
        }

        foreach ($serviceType as $i => $type) {
            if (!$type) {
                unset($serviceType[$i]);
                continue;
            }
            $params['serviceType' . $i] = $type;
        }

        //FCL Query
        $reqDocsFCL = " ( SELECT 'fileRequiredDocItems' AS 'myOpt', t400.id as categoryId, t400.categoryName as categoryName, '' AS PCMID, :PCID AS PCID, t1.FMID, t1.fileID, 
        t1.docName, t1.dStatus, t1.serviceType,t1.moduleType, t1.createdDate,
        GROUP_CONCAT(DISTINCT t2.requiredBy SEPARATOR ', ') AS required,
	    CASE
		WHEN GROUP_CONCAT(DISTINCT t2.requiredBy SEPARATOR ', ') LIKE '%Lender%' THEN 1
		ELSE 0
	    END AS req,
	    t2.UID as createdUser,t2.URole as createdRole,
	    t3.CID, t3.updatedBy, t3.updatedUserType, t3.updatedOn, 'FCL' AS CLType, '99' as displayOrder,t1.description,
	    'FileRequiredDocs' AS myKey,
        
        
        '' as coBorrowerRelatedReqdoc,
        '' as rehabRelatedReqdoc,
        '' as noCrossCollRelatedReqdoc,
        '' as usCitizenRelatedReqdoc,
        t1.refDocName as refDocName,
	    t1.refDocUrl as refDocUrl

	    FROM tblFileChecklistModules t1
	    JOIN tblFileChecklistRequiredBy t2 ON t1.FMID = t2.FMID";
        if ($requiredBy) {
            $reqDocsFCL .= ' AND t2.requiredBy = :requiredBy ';
        }
        $reqDocsFCL .= ' LEFT JOIN tblLMRChecklistNotRequired t3 ON t1.FMID = t3.CID ';

        if ($LMRId) {
            $reqDocsFCL .= 'AND t3.LMRID IN ( :LMRID )';
        }
        $reqDocsFCL .= ' LEFT JOIN tblPCChecklistCategory t400 ON t1.categoryId = t400.id  ';
        $reqDocsFCL .= ' WHERE t1.dStatus = :dStatus ';
        if ($LMRId) {
            $reqDocsFCL .= ' AND t1.fileID IN ( :fileID ) ';
        }
        if ($moduleType) {
            $reqDocsFCL .= ' AND t1 . moduleType IN ( ' . Database2::GetPlaceholders(sizeof($moduleType), ':moduleType', true) . ' )';
        }
        if ($serviceType) {
            $reqDocsFCL .= ' AND t1 . serviceType IN (  ' . Database2::GetPlaceholders(sizeof($serviceType), ':serviceType', true) . ' )';
        }
        $reqDocsFCL .= ' GROUP BY t2.FMID, t3.LMRID ORDER BY t2.requiredBy )';
        //End of FCL Query

        //PCL Query
        $reqDocsPCL = "( SELECT 'fileRequiredDocItems' AS 'myOpt', t400.id as categoryId,t400.categoryName as categoryName,
        t1.PCMID, t1.PCID, '' AS FMID, '' AS fileID,
        t1.docName, t1.dStatus, t1.serviceType,t1.moduleType, t1.createdDate,
        GROUP_CONCAT(DISTINCT t2.requiredBy SEPARATOR ', ') AS required,
	    CASE
		WHEN GROUP_CONCAT(DISTINCT t2.requiredBy SEPARATOR ', ') LIKE '%Lender%' THEN 1
		ELSE 0
	    END AS req,
		t2.UID as createdUser,t2.URole as createdRole,
	    t3.CID, t3.updatedBy, t3.updatedUserType, t3.updatedOn, 'PCL' AS CLType, t1.displayOrder,t1.description,
	    'PCRequiredDocs' AS myKey,
	    t1.coBorrowerRelatedReqdoc,
	    t1.rehabRelatedReqdoc,
	    t1.noCrossCollRelatedReqdoc,
	    t1.usCitizenRelatedReqdoc,
	    t1.refDocName,
	    t1.refDocUrl
	    
	    FROM tblPCChecklistModules t1 ";
        if ($branchId) {
            $reqDocsPCL .= " LEFT join tblPCChecklistBranch tPCB on t1.PCMID = tPCB.PCMID ";
        }
        $reqDocsPCL .= "JOIN tblPCChecklistRequiredBy t2 ON t1.PCMID = t2.PCMID";

        if ($requiredBy) {
            $reqDocsPCL .= ' AND t2.requiredBy = :requiredBy ';
        }
        $reqDocsPCL .= ' LEFT JOIN tblLMRChecklistNotRequired t3 ON t1.PCMID = t3.CID  ';

        if ($LMRId) {
            $reqDocsPCL .= ' AND t3.LMRID IN ( :LMRID ) ';
        }
        $reqDocsPCL .= ' LEFT JOIN tblPCChecklistCategory t400 ON t1.categoryId = t400.id  ';

        $reqDocsPCL .= ' WHERE t1 . dStatus = :dStatus ';

        if ($PCID) {
            $reqDocsPCL .= ' AND t1 . PCID IN ( :PCID )';
        }
        if ($moduleType) {
            $reqDocsPCL .= ' AND t1 . moduleType IN ( ' . Database2::GetPlaceholders(sizeof($moduleType), ':moduleType', true) . ' )';
        }
        if ($serviceType) {
            $reqDocsPCL .= ' AND t1 . serviceType IN (  ' . Database2::GetPlaceholders(sizeof($serviceType), ':serviceType', true) . ' )';
        }
        if ($branchId) {
            $reqDocsPCL .= " AND (tPCB.branchId is null or tPCB.branchId = :branchId )";
            $params['branchId'] = $branchId;
        }
        $reqDocsPCL .= '  GROUP BY t1 . PCMID ORDER BY t2 . requiredBy ) ORDER BY required desc, displayOrder ';
        //End of PCL Query

        $reqDocs = $reqDocsFCL . ' UNION ' . $reqDocsPCL;

        $reqDocs = 'SELECT *,MAX(createdDate) AS createdDa FROM(' . $reqDocs . ') AS last_table group by docName ';

        $reqDocsSub1 = " SELECT finalResult .*,(CASE WHEN PCMID = '' THEN FMID  ELSE PCMID  END  ) AS   'missdoc' ,
		docstatus . updatedBy    as updateBy,docstatus . updatedName,docstatus . updatedUserType,docstatus . updatedOn as updateOn,docstatus . docStatus
		FROM(";

        $reqDocsSub2 = ')AS finalResult
		LEFT JOIN(														
        SELECT docId, fileID,CLType, updatedBy,updatedName,updatedUserType,docStatus,updatedOn 
        FROM tblMissingDocumentsStatusAudit AS tmsu
        INNER JOIN 
        (SELECT MAX(id) AS id FROM tblMissingDocumentsStatusAudit  WHERE fileID  IN (' . $LMRId . ")  GROUP BY docId) last_updates 
        ON last_updates.id = tmsu.id AND  tmsu.fileID IN ( :fileID )             
        )  docstatus  ON   docstatus . docId = (CASE WHEN PCMID = '' THEN FMID  ELSE PCMID  END)
        AND   docstatus . fileID IN ( :fileID )
        GROUP BY (CASE WHEN PCMID = '' THEN FMID  ELSE PCMID  END)  order by finalResult.displayOrder asc, finalResult.createdDate asc";

        $reqDocs = $reqDocsSub1 . $reqDocs . $reqDocsSub2; //' ; debugQueryLW';

        // exit(Database2::getInstance()->safeQuery($reqDocs, $params));

        return Database2::getInstance()->queryData($reqDocs, $params);
    }


    /**
     * @param getRequiredDocsAdditionalLogic[][][] $additionalLogicResult
     * @param requiredAdditionalCond|null $requiredAdditionalCond $requiredAdditionalCond
     * @param self[] $mSTArray
     * @return array
     */
    public static function docAdditionalLogic(
        ?array                  $additionalLogicResult,
        ?requiredAdditionalCond $requiredAdditionalCond,
        array                   $mSTArray
    ): array
    {
        if (!$additionalLogicResult) {
            return $mSTArray;
        }

        $eDocNew = [];

        foreach ($mSTArray as $eDoc) {
            $hideForAdditionalLogic = '';
            $reqDocName = $eDoc->docName;

            if (array_key_exists($reqDocName, $additionalLogicResult)) {

                if (isset($additionalLogicResult[$reqDocName]['transactionType'])) {
                    if (!in_array($requiredAdditionalCond->typeOfHMLOLoanRequesting, explode(',', $additionalLogicResult[$reqDocName]['transactionType'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic1';
                    }
                }

                if (isset($additionalLogicResult[$reqDocName]['borrowerOccupancy'])) {
                    if (!in_array($requiredAdditionalCond->isHouseProperty, explode(',', $additionalLogicResult[$reqDocName]['borrowerOccupancy'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic3';
                    }
                }

                if (isset($additionalLogicResult[$reqDocName]['propertyType'])) {
                    if (!in_array($requiredAdditionalCond->propertyType, explode(',', $additionalLogicResult[$reqDocName]['propertyType'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic5';
                    }
                }

                if (isset($additionalLogicResult[$reqDocName]['propertyState'])) {
                    if (!in_array($requiredAdditionalCond->propertyState, explode(',', $additionalLogicResult[$reqDocName]['propertyState'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic7';
                    }
                }

                if (isset($additionalLogicResult[$reqDocName]['entityType'])) {
                    if (!in_array($requiredAdditionalCond->entityType, explode(',', $additionalLogicResult[$reqDocName]['entityType'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic9';
                    }
                }

                if (isset($additionalLogicResult[$reqDocName]['entityState'])) {
                    if (!in_array($requiredAdditionalCond->entityState, explode(',', $additionalLogicResult[$reqDocName]['entityState'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic11';
                    }
                }


                if (isset($additionalLogicResult[$reqDocName]['borrowerCreditScore'])) {
                    if (!in_array($requiredAdditionalCond->borrowerCreditScore, explode(',', $additionalLogicResult[$reqDocName]['borrowerCreditScore'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic12';
                    }
                }
                if (isset($additionalLogicResult[$reqDocName]['borrowerType'])) {
                    if (!in_array($requiredAdditionalCond->borrowerType, explode(',', $additionalLogicResult[$reqDocName]['borrowerType'][0]->val))) {
                        $hideForAdditionalLogic = 'hideForAdditionalLogic17';
                    }
                }
            }
            if ($eDoc->coBorrowerRelatedReqdoc) {
                if ($requiredAdditionalCond->isCoBorrower != 1) {
                    $hideForAdditionalLogic = 'hideForAdditionalLogic13';
                }
            }


            if ($eDoc->rehabRelatedReqdoc) {
                if ($requiredAdditionalCond->propertyNeedRehab != 'Yes') {
                    $hideForAdditionalLogic = 'hideForAdditionalLogic14';
                }
            }


            if ($eDoc->noCrossCollRelatedReqdoc) {
                if ($requiredAdditionalCond->isBlanketLoan != 'Yes') {
                    $hideForAdditionalLogic = 'hideForAdditionalLogic15';
                }
            }

            if ($eDoc->usCitizenRelatedReqdoc) {
                if ($requiredAdditionalCond->isBorUSCitizen == 'Yes' || $requiredAdditionalCond->isBorUSCitizen == '' || $requiredAdditionalCond->isBorUSCitizen == 'NA') {
                    $hideForAdditionalLogic = 'hideForAdditionalLogic16';
                }
            }


            $eDoc->hideForAdditionalLogic = $hideForAdditionalLogic;
            $eDocNew[] = $eDoc;
        }
        return $eDocNew;
    }
}
