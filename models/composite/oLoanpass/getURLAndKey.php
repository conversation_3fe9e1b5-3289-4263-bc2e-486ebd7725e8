<?php

namespace models\composite\oLoanpass;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPCID;
use models\Database2;
use models\types\strongType;

/**
 *
 */
class getURLAnd<PERSON>ey extends strongType
{

    /**
     * @param $PCID
     * @return void
     */
    public static function getReport($PCID)
    {
        if (glCustomJobForProcessingCompany::loanPassUrl($PCID) && glPCID::PCID_PROD_CV3 == $PCID) {
            define('CONST_PRICING_ENGINE_ENV', $_ENV['PRICING_ENGINE_ENV_CV3'] ?? null);
            define('CONST_PRICING_ENGINE_KEY', $_ENV['PRICING_ENGINE_KEY_CV3'] ?? null);
        } elseif (glCustomJobForProcessingCompany::loanPassUrl($PCID) && glPCID::PCID_DOMINION == $PCID) {
            define('CONST_PRICING_ENGINE_ENV', $_ENV['PRICING_ENGINE_ENV_DOMINION'] ?? null);
            define('CONST_PRICING_ENGINE_KEY', $_ENV['PRICING_ENGINE_KEY_DOMINION'] ?? null);
        } elseif (glCustomJobForProcessingCompany::loanPassUrl($PCID) && glPCID::PCID_RELENDING == $PCID) {
            define('CONST_PRICING_ENGINE_ENV', $_ENV['PRICING_ENGINE_ENV_RELENDING'] ?? null);
            define('CONST_PRICING_ENGINE_KEY', $_ENV['PRICING_ENGINE_KEY_RELENDING'] ?? null);
        } elseif (glCustomJobForProcessingCompany::loanPassUrl($PCID) && glPCID::PCID_PIEDPIPER == $PCID) {
            define('CONST_PRICING_ENGINE_ENV', $_ENV['PRICING_ENGINE_ENV_PIEDPIPER'] ?? null);
            define('CONST_PRICING_ENGINE_KEY', $_ENV['PRICING_ENGINE_KEY_PIEDPIPER'] ?? null);
        } elseif (glCustomJobForProcessingCompany::loanPassUrl($PCID) && glPCID::PCID_3198 == $PCID) {
            define('CONST_PRICING_ENGINE_ENV', $_ENV['PRICING_ENGINE_ENV_GLOBALINTEGRITYFINANCE'] ?? null);
            define('CONST_PRICING_ENGINE_KEY', $_ENV['PRICING_ENGINE_KEY_GLOBALINTEGRITYFINANCE'] ?? null);
        } else {
            define('CONST_PRICING_ENGINE_ENV', $_ENV['PRICING_ENGINE_ENV'] ?? null);
            define('CONST_PRICING_ENGINE_KEY', $_ENV['PRICING_ENGINE_KEY'] ?? null);
        }
    }
}