<?php

namespace models\composite\oContacts;

use models\APIHelper;
use models\Database2;
use models\standard\Arrays;

class contactsList
{
    public static function getReport($ip): array
    {
        $CIDArray = Arrays::explodeIntVals($ip['CID'])??[];
        $sql = APIHelper::getSQL(__DIR__ . '/sql/selectedContacts.sql');
        if (sizeof($CIDArray)) {
            foreach ($CIDArray as $i => $item) {
                $params['CID' . $i] = $item;
            }
            $sql = str_replace('\'--CID--\'', Database2::GetPlaceholders(sizeof($CIDArray), ':CID', true), $sql);
        }
        return Database2::getInstance()->queryData($sql, $params);
    }

}