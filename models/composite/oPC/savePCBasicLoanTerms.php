<?php

namespace models\composite\oPC;

use models\Database2;
use models\FileStorage;
use models\lendingwise\tblPCHMLOBasicLoanBusinessCategories;
use models\lendingwise\tblPCHMLOBasicLoanExitStrategy;
use models\lendingwise\tblPCHMLOBasicLoanLienPositions;
use models\lendingwise\tblPCHMLOBasicLoanRateLockPeriod;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;
use models\UploadServer;


/**
 *
 */
class savePCBasicLoanTerms extends strongType
{
    /**
     * @param $ip
     * @return array|int|string
     */
    public static function getReport($ip)
    {
        $PCID = 0;
        $UType = '';
        $UID = 0;
        $BLID = 0;
        $cnt = 0;
        $recordDate = Dates::Timestamp();

        if (array_key_exists('UID', $ip)) $UID = $ip['UID'];
        if (array_key_exists('UType', $ip)) $UType = $ip['UType'];
        if (array_key_exists('PCID', $ip)) $PCID = $ip['PCID'];
        if (array_key_exists('BLID', $ip)) $BLID = $ip['BLID'];

        if ($PCID == 0) {
            return $cnt;
        }
        if (isset($ip['Operation'])) {
            $_REQUEST = $ip['p'];
        }

        $PCBasicLoanTermsArray = [
            'minLoanAmount',
            'minRate',
            'minPoints',
            'originationPointsRate',
            'originationPointsValue',
            'brokerPointsRate',
            'brokerPointsValue',
            'applicationFee',
            'estdTitleClosingFee',
            'processingFee',
            'appraisalFee',
            'drawsSetUpFee',
            'drawsFee',
            'miscellaneousFee',
            'closingCostFinanced',
            'maxLoanAmount',
            'maxRate',
            'maxPoints',
            'loanPgmDetails',
            'valuationBPOFee',
            'valuationAVMFee',
            'creditReportFee',
            'backgroundCheckFee',
            'floodCertificateFee',
            'documentPreparationFee',
            'wireFee',
            'servicingSetUpFee',
            'taxServiceFee',
            'floodServiceFee',
            'inspectionFees',
            'projectFeasibility',
            'dueDiligence',
            'UccLienSearch',
            'otherFee',
            'taxImpoundsMonth',
            'taxImpoundsMonthAmt',
            'taxImpoundsFee',
            'insImpoundsMonthAmt',
            'insImpoundsFee',
            'thirdPartyFees',
            'insImpoundsMonth',
            'downPaymentPercentage',
            'rehabCostPercentageFinanced',
            'reqForLoanProUnderwriting',
            'closingCostFinancingFee',
            'escrowFees',
            'recordingFee',
            'underwritingFees',
            'propertyTax',
            'bufferAndMessengerFee',
            'travelNotaryFee',
            'prePaidInterest',
            'realEstateTaxes',
            'insurancePremium',
            'payOffLiensCreditors',
            'wireTransferFeeToTitle',
            'wireTransferFeeToEscrow',
            'pastDuePropertyTaxes',
            'attorneyFee',
            'totalLTC',
            'minMidFico',
            'maxMidFico',
            'minPropertyForFixFlop',
            'maxPropertyForFixFlop',
            'minPropertyForGrndConst',
            'maxPropertyForGrndConst',
            'survey',
            'wholeSaleAdminFee',
            'moduleCode',
            'marketAnnualGrossSales',
            'marketMaxNSFsAllowed',
            'marketAverageBankBalance',
            'marketAvgTotalMonthlySale',
            'marketIsTherePrePaymentPenalty',
            'marketPlaceCompanyDetails',
            'enablePrivateMarketPlaceOnly',
            'marketPlaceContactInfoDetails',
            'minDSCR',
        ];
        /** DIRECT Variables **/


        $PCBasicLoanTermsReplaceCommaArray = [
            'minLoanAmount',
            'minRate',
            'minPoints',
            'originationPointsRate',
            'originationPointsValue',
            'brokerPointsRate',
            'brokerPointsValue',
            'applicationFee',
            'estdTitleClosingFee',
            'processingFee',
            'appraisalFee',
            'drawsSetUpFee',
            'drawsFee',
            'miscellaneousFee',
            'closingCostFinanced',
            'maxLoanAmount',
            'maxRate',
            'maxPoints',
            'valuationBPOFee',
            'valuationAVMFee',
            'creditReportFee',
            'backgroundCheckFee',
            'floodCertificateFee',
            'documentPreparationFee',
            'wireFee',
            'servicingSetUpFee',
            'taxServiceFee',
            'floodServiceFee',
            'inspectionFees',
            'projectFeasibility',
            'dueDiligence',
            'UccLienSearch',
            'otherFee',
            'taxImpoundsMonth',
            'taxImpoundsMonthAmt',
            'taxImpoundsFee',
            'insImpoundsMonthAmt',
            'insImpoundsFee',
            'thirdPartyFees',
            'insImpoundsMonth',
            'closingCostFinancingFee',
            'attorneyFee',
            'escrowFees',
            'recordingFee',
            'underwritingFees',
            'propertyTax',
            'bufferAndMessengerFee',
            'travelNotaryFee',
            'prePaidInterest',
            'realEstateTaxes',
            'insurancePremium',
            'payOffLiensCreditors',
            'wireTransferFeeToTitle',
            'wireTransferFeeToEscrow',
            'pastDuePropertyTaxes',
            'totalLTC',
            'minMidFico',
            'maxMidFico',
            'minPropertyForFixFlop',
            'maxPropertyForFixFlop',
            'minPropertyForGrndConst',
            'maxPropertyForGrndConst',
            'survey',
            'wholeSaleAdminFee',
            'marketAverageBankBalance',
            'marketAvgTotalMonthlySale',
            'marketAnnualGrossSales',
            'marketMaxNSFsAllowed',
            'minDSCR',
        ];
        /** DIRECT Variables **/


        $qu = '';
        $qs = '';
        $qc = '';
        $qv = '';

        $sqlParams = [];
        for ($f = 0; $f < count($PCBasicLoanTermsArray); $f++) {
            if (isset($_REQUEST["$PCBasicLoanTermsArray[$f]"])) {
                $qc .= $qs . " $PCBasicLoanTermsArray[$f] ";
                if (in_array($PCBasicLoanTermsArray[$f], $PCBasicLoanTermsReplaceCommaArray)) {
                    $postVal = trim(Strings::replaceCommaValues($_REQUEST["$PCBasicLoanTermsArray[$f]"]));
                } else {
                    $postVal = trim(urlencode(Strings::stripQuote($_REQUEST["$PCBasicLoanTermsArray[$f]"])));
                }
                $qv .= $qs . ':' . $PCBasicLoanTermsArray[$f];
                $qu .= $qs . " $PCBasicLoanTermsArray[$f] = :$PCBasicLoanTermsArray[$f]  ";
                $qs = ', ';
                $sqlParams[$PCBasicLoanTermsArray[$f]] = $postVal;
            }
        }

        if (isset($_REQUEST['PCBorrCreditScoreRange'])) {
            $PCBorrCreditScoreRange = $_REQUEST['PCBorrCreditScoreRange'];
            if (is_array($PCBorrCreditScoreRange)) {
                $qc .= $qs . ' PCBorrCreditScoreRange ';
                $qv .= $qs . ' :PCBorrCreditScoreRange ';
                $qu .= $qs . ' PCBorrCreditScoreRange = :PCBorrCreditScoreRange ';
                $qs = ', ';
                $sqlParams['PCBorrCreditScoreRange'] = implode(',', $PCBorrCreditScoreRange);
            }
        } else {
            $qu .= $qs . " PCBorrCreditScoreRange = '' ";
        }

        if (isset($_REQUEST['enableRehabConstruction'])) {
            $enableRehabConstruction = $_REQUEST['enableRehabConstruction'];
            $qc .= $qs . ' enableRehabConstruction ';
            $qv .= $qs . '  :enableRehabConstruction ';
            $qu .= $qs . ' enableRehabConstruction = :enableRehabConstruction ';

            $sqlParams['enableRehabConstruction'] = $enableRehabConstruction;
        } else {
            $qu .= $qs . " enableRehabConstruction = '0' ";
        }


        if ($BLID > 0) {
            if ($qu != '') {
                $sqlParams['BLID'] = $BLID;
                $qry = ' UPDATE tblPCHMLOBasicLoanInfo SET ' . $qu . ' WHERE BLID = :BLID ; ';
                $cnt = Database2::getInstance()->executeQuery($qry, $sqlParams);
            }
        } elseif ($qc != '' && $qv != '') {
            $sqlParams['PCID'] = $PCID;
            $sqlParams['recordDate'] = $recordDate;
            $sqlParams['UID'] = $UID;
            $sqlParams['UType'] = $UType;

            $qry = ' INSERT INTO tblPCHMLOBasicLoanInfo (
PCID
, recordDate
, UID
, UType
, ' . $qc . '
) VALUES (
:PCID
, :recordDate
, :UID
, :UType
, ' . $qv . '
); ';
            $cnt = Database2::getInstance()->insert($qry, $sqlParams);
            $BLID = $cnt;
        }


        $loanPgmArray = [];
        $sqlParams = [];
        if (isset($_REQUEST['loanPgm'])) {
            $loanPgmArray = $_REQUEST['loanPgm'];
        }

        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanPgmInfo WHERE PCID = :PCID AND BLID = :BLID ;';
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        if (count($loanPgmArray) > 0) {
            $sqlParams = [];
            $qryIns = ' INSERT INTO tblPCHMLOBasicLoanPgmInfo (
PCID
, BLID
, recordDate
, UID
, UType
, loanPgm
) VALUES (
:PCID
, :BLID
, :recordDate
, :UID
, :UType
, :loanPgm
); ';

            for ($f = 0; $f < count($loanPgmArray); $f++) {
                $sqlParams[] = [
                    'PCID' => $PCID,
                    'BLID' => $BLID,
                    'recordDate' => $recordDate,
                    'UID' => $UID,
                    'UType' => $UType,
                    'loanPgm' => $loanPgmArray[$f],
                ];
            }
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }


        $transactionTypeArray = [];
        if (isset($_REQUEST['transactionType'])) {
            $transactionTypeArray = $_REQUEST['transactionType'];
        }
        $sqlParams = [];
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM  tblPCHMLOBasicLoanTransactionType WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        if (count($transactionTypeArray)) {
            $maxLTVArray = [
                'purchase',
                'ratetermrefinance',
                'cashoutrefinance',
                'transactional',
                'blanketloan',
                'commercialpurchase',
                'commercialratetermrefinance',
                'commercialcashoutrefinance',
                'lineofcredit',
                'refinance',
                'newconstructionexistingland',
                'delayedpurchase'
            ];
            $maxLTVRehabArray = ['afterrehabpurchase', 'afterrehabcommercialpurchase'];

            $sqlParams = [];
            $qryIns = ' INSERT INTO tblPCHMLOBasicLoanTransactionType (
PCID, 
BLID, 
recordDate,
UID, 
UType, 
transactionType, 
maxLTV, 
maxLTVAfterRehab
) VALUES (
:PCID , 
:BLID , 
:recordDate ,
:UID , 
:UType , 
:transactionType , 
:maxLTV , 
:maxLTVAfterRehab
) ';

            for ($f = 0; $f < count($transactionTypeArray); $f++) {
                $maxLTV = 0;
                $maxLTVAfterRehab = 0;

                for ($f1 = 0; $f1 < count($maxLTVArray); $f1++) {
                    if (Strings::removeSpaceWithSpecialChars($transactionTypeArray[$f]) == $maxLTVArray[$f1]) {
                        if (isset($_REQUEST[$maxLTVArray[$f1]])) {
                            $maxLTV = trim($_REQUEST[$maxLTVArray[$f1]]);
                            break;
                        }
                    }
                }
                for ($f2 = 0; $f2 < count($maxLTVRehabArray); $f2++) {
                    if (Strings::removeSpaceWithSpecialChars('afterrehab' . $transactionTypeArray[$f]) == $maxLTVRehabArray[$f2]) {
                        if (isset($_REQUEST[$maxLTVRehabArray[$f2]])) {
                            $maxLTVAfterRehab = trim($_REQUEST[$maxLTVRehabArray[$f2]]);
                            break;
                        }
                    }
                }

                $sqlParams[] = [
                    'PCID' => $PCID,
                    'BLID' => $BLID,
                    'recordDate' => $recordDate,
                    'UID' => $UID,
                    'UType' => $UType,
                    'transactionType' => trim($transactionTypeArray[$f]),
                    'maxLTV' => $maxLTV,
                    'maxLTVAfterRehab' => $maxLTVAfterRehab,
                ];
            }
            if (count($transactionTypeArray) > 0) {
                Database2::getInstance()->insertMulti($qryIns, $sqlParams);
            }
        }


        /**************Loan Marketplace Loan Program */
        $marketLoanProgramArray = [];
        $sqlParams = [];
        if (isset($_REQUEST['marketLoanProgram'])) {
            $marketLoanProgramArray = $_REQUEST['marketLoanProgram'];
        }
//$qryDel = " DELETE FROM  tblPCHMLOBasicLoanMarketPlaceLoanProgram WHERE PCID = " . $PCID . " AND BLID = " . $BLID;
//Database2::getInstance()->executeQry(array('qry' => $qryDel));

        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM  tblPCHMLOBasicLoanMarketPlaceLoanProgram WHERE PCID = :PCID AND BLID = :BLID ; ';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $sqlParams = [];
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanMarketPlaceLoanProgram (
PCID,
BLID, 
recordDate, 
UID, 
UType, 
MPLID
) VALUES (
:PCID ,
:BLID , 
:recordDate , 
:UID , 
:UType , 
:MPLID    
); ';
        for ($f = 0; $f < count($marketLoanProgramArray); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'MPLID' => $marketLoanProgramArray[$f],
            ];
        }
        if (count($marketLoanProgramArray) > 0) {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }
        /* End Of Loan Marketplace Loan Program*/


        /**************Notifications tabs*/
        $selectedBranchesList = $selectedBrokersList = $selectedEmployeeList = $selectedContactsList = [];
        if (isset($_REQUEST['selectedBranchesList'])) {
            $selectedBranchesList = $_REQUEST['selectedBranchesList'];
        }
        if (isset($_REQUEST['selectedBrokersList'])) {
            $selectedBrokersList = $_REQUEST['selectedBrokersList'];
        }
        if (isset($_REQUEST['selectedEmployeeList'])) {
            $selectedEmployeeList = $_REQUEST['selectedEmployeeList'];
        }
        if (isset($_REQUEST['selectedContactsList'])) {
            $selectedContactsList = $_REQUEST['selectedContactsList'];
        }


//$qryDel = " DELETE FROM  tblPCHMLOBasicLoanMarketLoanNotification WHERE PCID = " . $PCID . " AND BLID = " . $BLID;
//Database2::getInstance()->executeQry(array('qry' => $qryDel));
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanMarketLoanNotification WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $sqlParams = [];
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanMarketLoanNotification (
PCID, 
BLID, 
NotificationUserId, 
NotificationuserGroup
) VALUES(
:PCID , 
:BLID , 
:NotificationUserId , 
:NotificationuserGroup               
) ';

        for ($f = 0; $f < count($selectedBranchesList); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'NotificationUserId' => $selectedBranchesList[$f],
                'NotificationuserGroup' => 'Branch',
            ];
        }
        for ($f3 = 0; $f3 < count($selectedBrokersList); $f3++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'NotificationUserId' => $selectedBrokersList[$f3],
                'NotificationuserGroup' => 'Agent',
            ];
        }

        for ($f4 = 0; $f4 < count($selectedEmployeeList); $f4++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'NotificationUserId' => $selectedEmployeeList[$f4],
                'NotificationuserGroup' => 'Employee',
            ];
        }
        for ($f5 = 0; $f5 < count($selectedContactsList); $f5++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'NotificationUserId' => $selectedContactsList[$f5],
                'NotificationuserGroup' => 'Contact',
            ];
        }

        if ((count($selectedBranchesList) > 0 || $selectedBrokersList > 0 || $selectedEmployeeList > 0) && $qryIns != '') {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }
        /* End Of Loan Marketplace Loan Program*/


        /*** Custom Email Id's*/
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM  tblPCHMLOBasicLoanMarketLoanCustomNotification WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $docRecipientEmail = [];
        $sqlParams = [];
        if (isset($_REQUEST['docRecipientEmail'])) {
            $docRecipientEmail = $_REQUEST['docRecipientEmail'];
            $docRecipientEmail = explode(';', $docRecipientEmail);
        }
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanMarketLoanCustomNotification (
PCID, 
BLID, 
NotificationUserEmailId
) VALUES(
:PCID , 
:BLID , 
:NotificationUserEmailId     
) ';
        for ($fp = 0; $fp < count($docRecipientEmail); $fp++) {
            if ($docRecipientEmail[$fp] != '') {
                $sqlParams[] = [
                    'PCID' => $PCID,
                    'BLID' => $BLID,
                    'NotificationUserEmailId' => $docRecipientEmail[$fp],
                ];
            }
        }
        if (count($docRecipientEmail) > 0) {
            if ($qryIns != '') {
                Database2::getInstance()->insertMulti($qryIns, $sqlParams);
            }
        }
        /*** End of Custom Email Id's*/

        // Property Type
        $propertyTypeArray = [];
        if (isset($_REQUEST['propertyType'])) {
            $propertyTypeArray = $_REQUEST['propertyType'];
        }

        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = "DELETE FROM tblPCHMLOBasicLoanPropertyType WHERE PCID = :PCID AND BLID = :BLID ;";
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        if (count($propertyTypeArray) > 0) {
            $sqlParams = [];
            $qryIns = "INSERT INTO tblPCHMLOBasicLoanPropertyType
                       (
                            PCID, 
                            BLID, 
                            recordDate,
                            UID, 
                            UType,
                            propertyType
                        ) VALUES (
                            :PCID, 
                            :BLID, 
                            :recordDate,
                            :UID, 
                            :UType,
                            :propertyType         
                        )";

            foreach ($propertyTypeArray as $propertyType) {
                $sqlParams[] = [
                    'PCID' => $PCID,
                    'BLID' => $BLID,
                    'recordDate' => $recordDate,
                    'UID' => $UID,
                    'UType' => $UType,
                    'propertyType' => trim($propertyType),
                ];
            }
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }

        // Future Property Type
        $futurePropertyTypeArray = [];
        if (isset($_REQUEST['futurePropertyType'])) {
            $futurePropertyTypeArray = $_REQUEST['futurePropertyType'];
        }

        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = "DELETE FROM tblPCHMLOBasicLoanFuturePropertyType WHERE PCID = :PCID AND BLID = :BLID ;";
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        if (count($futurePropertyTypeArray) > 0) {
            $sqlParams = [];
            $qryIns = "INSERT INTO tblPCHMLOBasicLoanFuturePropertyType
                       (
                            PCID, 
                            BLID, 
                            recordDate,
                            UID, 
                            UType,
                            FuturePropertyType
                        ) VALUES (
                            :PCID, 
                            :BLID, 
                            :recordDate,
                            :UID, 
                            :UType,
                            :futurePropertyType         
                        )";

            foreach ($futurePropertyTypeArray as $futurePropertyType) {
                $sqlParams[] = [
                    'PCID' => $PCID,
                    'BLID' => $BLID,
                    'recordDate' => $recordDate,
                    'UID' => $UID,
                    'UType' => $UType,
                    'futurePropertyType' => trim($futurePropertyType),
                ];
            }
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }

        //EntityType
        $entityTypeArray = [];
        if (isset($_REQUEST['entityType'])) {
            $entityTypeArray = $_REQUEST['entityType'];
        }
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDelET = 'DELETE FROM  tblPCHMLOBasicLoanEntityType WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDelET, $sqlParams);
        $sqlParams = [];
        $qryInsET = ' INSERT INTO tblPCHMLOBasicLoanEntityType (
PCID, 
BLID, 
recordDate,
UID, 
UType,
entityType
) VALUES (
:PCID , 
:BLID , 
:recordDate ,
:UID , 
:UType ,
:entityType         
)';
        foreach ($entityTypeArray as $et) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'entityType' => trim($et),
            ];
        }
        if (count($entityTypeArray) > 0) {
            Database2::getInstance()->insertMulti($qryInsET, $sqlParams);
        }

        $extnOptionArray = [];
        if (isset($_REQUEST['extnOption'])) {
            $extnOptionArray = $_REQUEST['extnOption'];
        }
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanExtensionOption WHERE PCID = :PCID  AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $sqlParams = [];
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanExtensionOption (
PCID,
BLID,
recordDate,
UID,
UType,
extnOption
) VALUES (
:PCID ,
:BLID ,
:recordDate ,
:UID ,
:UType ,
:extnOption         
);';

        for ($f = 0; $f < count($extnOptionArray); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'extnOption' => $extnOptionArray[$f],
            ];
        }
        if (count($extnOptionArray) > 0) {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }


        $loanTermArray = [];
        if (isset($_REQUEST['loanTerm'])) {
            $loanTermArray = $_REQUEST['loanTerm'];
        }
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM  tblPCHMLOBasicLoanTermInfo WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);


        $sqlParams = [];
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanTermInfo (
PCID, 
BLID,
recordDate, 
UID, 
UType, 
loanTerm
) VALUES(
:PCID , 
:BLID ,
:recordDate , 
:UID , 
:UType, 
:loanTerm
) ;';

        for ($f = 0; $f < count($loanTermArray); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'loanTerm' => trim($loanTermArray[$f]),
            ];
        }
        if (count($loanTermArray) > 0) {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }


        $occupancyArray = [];
        if (isset($_REQUEST['occupancy'])) {
            $occupancyArray = $_REQUEST['occupancy'];
        }
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM  tblPCHMLOBasicLoanOccupancy WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $sqlParams = [];
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanOccupancy (
PCID,
BLID,
recordDate,
UID,
UType,
occupancy
) VALUES (
:PCID ,
:BLID ,
:recordDate ,
:UID ,
:UType ,
:occupancy 
); ';
        for ($f = 0; $f < count($occupancyArray); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'occupancy' => trim($occupancyArray[$f]),
            ];
        }
        if (count($occupancyArray) > 0) {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }


        $stateArray = [];
        if (isset($_REQUEST['state'])) {
            $stateArray = $_REQUEST['state'];
        }
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM  tblPCHMLOBasicLoanStateInfo WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $sqlParams = [];
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanStateInfo (
PCID, 
BLID, 
recordDate, 
UID, 
UType, 
stateCode
) VALUES (
:PCID , 
:BLID , 
:recordDate , 
:UID , 
:UType , 
:stateCode      
) ';
        for ($f = 0; $f < count($stateArray); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'stateCode' => trim($stateArray[$f]),
            ];
        }
        if (count($stateArray) > 0) {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }


        $nichesArray = [];
        if (isset($_REQUEST['niches'])) {
            $nichesArray = $_REQUEST['niches'];
        }
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM  tblPCHMLOBasicLoanNichesInfo WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $sqlParams = [];
        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanNichesInfo (
PCID,
BLID, 
recordDate,
UID,
UType,
nichesID
) VALUES (
:PCID ,
:BLID , 
:recordDate ,
:UID ,
:UType ,
:nichesID        
) ';

        for ($f = 0; $f < count($nichesArray); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'nichesID' => $nichesArray[$f],
            ];
        }
        if (count($nichesArray) > 0) {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }


        /**** tblPCHMLOBasicLoanAmortizationInfo ******/
        $lien1TermsArray = [];
        if (isset($_REQUEST['lien1Terms'])) {
            $lien1TermsArray = $_REQUEST['lien1Terms'];
        }
        $sqlParams = [
            'PCID' => $PCID,
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanAmortizationInfo WHERE PCID = :PCID AND BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanAmortizationInfo (
PCID,
BLID,
recordDate,
UID,
UType,
AmortizationVal
) VALUES (
:PCID ,
:BLID ,
:recordDate ,
:UID ,
:UType ,
:AmortizationVal    
) ';
        $sqlParams = [];
        for ($f = 0; $f < count($lien1TermsArray); $f++) {
            $sqlParams[] = [
                'PCID' => $PCID,
                'BLID' => $BLID,
                'recordDate' => $recordDate,
                'UID' => $UID,
                'UType' => $UType,
                'AmortizationVal' => $lien1TermsArray[$f],
            ];
        }
        if (count($lien1TermsArray) > 0) {
            Database2::getInstance()->insertMulti($qryIns, $sqlParams);
        }

        /**** tblPCHMLOBasicLoanRateLockPeriod ******/
        //Delete existing records
        $tblPCHMLOBasicLoanRateLockPeriodDelete = tblPCHMLOBasicLoanRateLockPeriod::GetAll([
            'PCID' => $PCID,
            'BLID' => $BLID,
        ]);
        foreach ($tblPCHMLOBasicLoanRateLockPeriodDelete as $eachtblPCHMLOBasicLoanRateLockPeriodDelete) {
            $eachtblPCHMLOBasicLoanRateLockPeriodDelete->Delete();
        }
        //Insert new records
        $rateLockPeriodArray = [];
        if (isset($_REQUEST['rateLockPeriod'])) {
            $rateLockPeriodArray = $_REQUEST['rateLockPeriod'];
        }
        foreach ($rateLockPeriodArray as $rateLockPeriod) {
            $tblPCHMLOBasicLoanRateLockPeriod = new tblPCHMLOBasicLoanRateLockPeriod();
            $tblPCHMLOBasicLoanRateLockPeriod->PCID = $PCID;
            $tblPCHMLOBasicLoanRateLockPeriod->BLID = $BLID;
            $tblPCHMLOBasicLoanRateLockPeriod->rateLockPeriod = (int)$rateLockPeriod;
            $tblPCHMLOBasicLoanRateLockPeriod->UID = $UID;
            $tblPCHMLOBasicLoanRateLockPeriod->UType = $UType;
            $tblPCHMLOBasicLoanRateLockPeriod->recordDate = $recordDate;
            $tblPCHMLOBasicLoanRateLockPeriod->Save();
        }
        /**** // tblPCHMLOBasicLoanRateLockPeriod //******/



        /**** tblPCHMLOBasicLoanExitStrategy ******/
        //Delete existing records
        $tblPCHMLOBasicLoanExitStrategyDelete = tblPCHMLOBasicLoanExitStrategy::GetAll([
            'BLID' => $BLID,
        ]);
        foreach ($tblPCHMLOBasicLoanExitStrategyDelete as $eachtblPCHMLOBasicLoanExitStrategyDelete) {
            $eachtblPCHMLOBasicLoanExitStrategyDelete->Delete();
        }
        //Insert new records
        $exitStrategyArray = Request::isset('exitStrategy') ? $_REQUEST['exitStrategy'] : [];
        foreach ($exitStrategyArray as $exitStrategy) {
            $tblPCHMLOBasicLoanExitStrategy = new tblPCHMLOBasicLoanExitStrategy();
            $tblPCHMLOBasicLoanExitStrategy->BLID = $BLID;
            $tblPCHMLOBasicLoanExitStrategy->exitStrategy = $exitStrategy;
            $tblPCHMLOBasicLoanExitStrategy->Save();
        }
        /**** // tblPCHMLOBasicLoanExitStrategy //******/



        /**** tblPCHMLOBasicLoanSBALoanProduct ******/
        $marketSbaLoanProductArray = [];
        if (isset($_REQUEST['marketSbaLoanProduct'])) {
            $marketSbaLoanProductArray = $_REQUEST['marketSbaLoanProduct'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanSBALoanProduct WHERE BLID = :BLID ;';
        $sqlParams = [
            'BLID' => $BLID,
        ];
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanSBALoanProduct ( 
BLID,
SBALoanProductVal 
) VALUES ( 
:BLID,
:SBALoanProductVal 
) ';
        $sqlParamsIns = [];

        foreach ($marketSbaLoanProductArray as $eachMarketSbaLoanProduct) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'SBALoanProductVal' => $eachMarketSbaLoanProduct,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanSBALoanProduct ******/


        /**** tblPCHMLOBasicLoanEquipmentType ******/
        $marketEquipmentTypeArray = [];
        if (isset($_REQUEST['marketEquipmentType'])) {
            $marketEquipmentTypeArray = $_REQUEST['marketEquipmentType'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanEquipmentType WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanEquipmentType ( 
BLID,
equipmentTypeVal
) VALUES ( 
:BLID,
:equipmentTypeVal 
) ';
        $sqlParamsIns = [];

        foreach ($marketEquipmentTypeArray as $eachMarketEquipmentType) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'equipmentTypeVal' => $eachMarketEquipmentType,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanEquipmentType ******/


        /**** tblPCHMLOBasicLoanEntityStateOfFormation ******/
        $marketEntityStateOfFormationArray = [];
        if (isset($_REQUEST['marketEntityStateOfFormation'])) {
            $marketEntityStateOfFormationArray = $_REQUEST['marketEntityStateOfFormation'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanEntityStateOfFormation WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanEntityStateOfFormation ( 
BLID,
sateCode
) VALUES ( 
:BLID ,
:sateCode
) ';
        $sqlParamsIns = [];

        foreach ($marketEntityStateOfFormationArray as $eachEntityStateOfFormation) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'sateCode' => $eachEntityStateOfFormation,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanEntityStateOfFormation ******/


        /**** tblPCHMLOBasicLoanElgibleState ******/
        $elgibleStatesArray = [];
        if (isset($_REQUEST['elgibleStates'])) {
            $elgibleStatesArray = $_REQUEST['elgibleStates'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanElgibleState WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanElgibleState ( 
BLID,
stateCode
) VALUES ( 
:BLID ,
:stateCode
) ';
        $sqlParamsIns = [];

        foreach ($elgibleStatesArray as $eachElgibleState) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'stateCode' => $eachElgibleState,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanEntityStateOfFormation ******/


        /**** tblPCHMLOBasicLoanPaymentFrequency ******/
        $marketPaymentFrequencyArray = [];
        if (isset($_REQUEST['marketPaymentFrequency'])) {
            $marketPaymentFrequencyArray = $_REQUEST['marketPaymentFrequency'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanPaymentFrequency WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanPaymentFrequency ( 
BLID, 
paymentFrequencyVal 
) VALUES ( 
:BLID,
:paymentFrequencyVal 
) ';
        $sqlParamsIns = [];

        foreach ($marketPaymentFrequencyArray as $eachPaymentFrequency) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'paymentFrequencyVal' => $eachPaymentFrequency,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanPaymentFrequency ******/


        /**** tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy ******/
        $marketplaceMinSeasoningPersonalBankruptcyArray = [];
        if (isset($_REQUEST['marketplaceMinSeasoningPersonalBankruptcy'])) {
            $marketplaceMinSeasoningPersonalBankruptcyArray = $_REQUEST['marketplaceMinSeasoningPersonalBankruptcy'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy (
BLID,
MinSeasoningPersonalBankruptcyVal
) VALUES ( 
:BLID,
:MinSeasoningPersonalBankruptcyVal 
) ';
        $sqlParamsIns = [];

        foreach ($marketplaceMinSeasoningPersonalBankruptcyArray as $eachMinSeasoningPersonalBankruptcy) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'MinSeasoningPersonalBankruptcyVal' => (int)$eachMinSeasoningPersonalBankruptcy ?: null,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy ******/


        /**** tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy ******/
        $marketplaceMinSeasoningBusinessBankruptcyArray = [];
        if (isset($_REQUEST['marketplaceMinSeasoningBusinessBankruptcy'])) {
            $marketplaceMinSeasoningBusinessBankruptcyArray = $_REQUEST['marketplaceMinSeasoningBusinessBankruptcy'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy ( 
BLID,
MinSeasoningBusinessBankruptcyVal 
) VALUES ( 
:BLID,
:MinSeasoningBusinessBankruptcyVal
) ';
        $sqlParamsIns = [];

        foreach ($marketplaceMinSeasoningBusinessBankruptcyArray as $eachMinSeasoningBusinessBankruptcy) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'MinSeasoningBusinessBankruptcyVal' => $eachMinSeasoningBusinessBankruptcy,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy ******/


        /**** tblPCHMLOBasicLoanMinSeasoningForeclosure ******/
        $marketplaceMinSeasoningForeclosureArray = [];
        if (isset($_REQUEST['marketplaceMinSeasoningForeclosure'])) {
            $marketplaceMinSeasoningForeclosureArray = $_REQUEST['marketplaceMinSeasoningForeclosure'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanMinSeasoningForeclosure WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanMinSeasoningForeclosure ( 
BLID,
MinSeasoningForeclosureVal 
) VALUES (
:BLID, 
:MinSeasoningForeclosureVal 
) ';
        $sqlParamsIns = [];

        foreach ($marketplaceMinSeasoningForeclosureArray as $eachMinSeasoningForeClosure) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'MinSeasoningForeclosureVal' => (int)$eachMinSeasoningForeClosure ?: null,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy ******/


        /**** tblPCHMLOBasicLoanLoanPurpose ******/
        $marketLoanPurposeArray = [];
        if (isset($_REQUEST['marketLoanPurpose'])) {
            $marketLoanPurposeArray = $_REQUEST['marketLoanPurpose'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanLoanPurpose WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanLoanPurpose ( BLID, purposeName ) VALUES ( :BLID, :purposeName ) ';
        $sqlParamsIns = [];

        foreach ($marketLoanPurposeArray as $eachLoanPurpose) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'purposeName' => $eachLoanPurpose,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanLoanPurpose ******/


        /**** tblPCHMLOBasicLoanMinTimeInBusiness ******/
        $marketMinTimeInBusinessArray = [];
        if (isset($_REQUEST['marketMinTimeInBusiness'])) {
            $marketMinTimeInBusinessArray = $_REQUEST['marketMinTimeInBusiness'];
        }
        $sqlParams = [
            'BLID' => $BLID,
        ];
        $qryDel = 'DELETE FROM tblPCHMLOBasicLoanMinTimeInBusiness WHERE BLID = :BLID ;';
        Database2::getInstance()->executeQuery($qryDel, $sqlParams);

        $qryIns = ' INSERT INTO tblPCHMLOBasicLoanMinTimeInBusiness ( BLID, minTimeVal ) VALUES ( :BLID, :minTimeVal ) ';
        $sqlParamsIns = [];

        foreach ($marketMinTimeInBusinessArray as $eachMinTimeInBusiness) {
            $sqlParamsIns[] = [
                'BLID' => $BLID,
                'minTimeVal' => $eachMinTimeInBusiness,
            ];
        }
        Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
        /**** ENd of tblPCHMLOBasicLoanMinTimeInBusiness ******/

        /** Start tblPCHMLOBasicLoanBusinessCategories */
        $businessCategoryArray = [];
        if (isset($_REQUEST['businessCategory'])) {
            $businessCategoryArray = $_REQUEST['businessCategory'];
        }

        $tblPCHMLOBasicLoanBusinessCategories = tblPCHMLOBasicLoanBusinessCategories::GetAll([
           'BLID' => $BLID
        ]);
        foreach ($tblPCHMLOBasicLoanBusinessCategories as $eachCategory) {
            $eachCategory->Delete();
        }
        $tblPCHMLOBasicLoanBusinessCategories = new tblPCHMLOBasicLoanBusinessCategories();
        foreach ($businessCategoryArray as $item) {
            $tblPCHMLOBasicLoanBusinessCategories->id = null;
            $tblPCHMLOBasicLoanBusinessCategories->BLID = $BLID;
            $tblPCHMLOBasicLoanBusinessCategories->businessCategory = $item;
            $tblPCHMLOBasicLoanBusinessCategories->Save();
        }
        /** End tblPCHMLOBasicLoanBusinessCategories */

        /** Start tblPCHMLOBasicLoanLienPositions */
        $lienPositionArray = [];
        if (isset($_REQUEST['lienPosition'])) {
            $lienPositionArray = $_REQUEST['lienPosition'];
        }
        $tblPCHMLOBasicLoanLienPositions = tblPCHMLOBasicLoanLienPositions::GetAll([
           'BLID' => $BLID
        ]);
        foreach ($tblPCHMLOBasicLoanLienPositions as $eachLienPosition) {
            $eachLienPosition->Delete();
        }
        $tblPCHMLOBasicLoanLienPositions = new tblPCHMLOBasicLoanLienPositions();
        foreach ($lienPositionArray as $item) {
            $tblPCHMLOBasicLoanLienPositions->id = null;
            $tblPCHMLOBasicLoanLienPositions->BLID = $BLID;
            $tblPCHMLOBasicLoanLienPositions->lienPosition = $item;
            $tblPCHMLOBasicLoanLienPositions->Save();
        }
        /** End tblPCHMLOBasicLoanLienPositions */


        /* tblPCHMLOBasicLoanFiles */
        if (isset($_FILES['fileUpload'])) {
            $fileUploadNameArray = $_REQUEST['fileUploadName'];
            $fileUploadIdArray = $_REQUEST['fileUploadId'];
            $fileUploadArray = $_FILES['fileUpload']['name'];


            $qryIns = ' INSERT INTO tblPCHMLOBasicLoanFiles (
    BLID, 
    fileGivenName,
    fileName
) VALUES (
    :BLID ,
    :fileGivenName ,
    :fileName 
) ';
            $sqlParamsIns = [];

            foreach ($fileUploadArray as $eachfileUploadId => $eachfileUpload) {
                if ($_FILES['fileUpload']['tmp_name'][$eachfileUploadId] != '') {
                    $tempFileContent = base64_encode(FileStorage::getFile($_FILES['fileUpload']['tmp_name'][$eachfileUploadId]));
                    if (trim($tempFileContent) != '') {
                        $fileArray['PCDocs'] = 1;
                        $fileArray['oldFPCID'] = $PCID;
                        $fileArray['tmpFileContent'] = $tempFileContent;
                        $fileArray['fileDocName'] = $fileDocName = $PCID . 'mp_' . $eachfileUploadId . date('ymdhis') . (substr($eachfileUpload, -7));
                        $res = UploadServer::upload($fileArray);
                        if ($res == 'Success') {
                            $sqlParamsIns[] = [
                                'BLID' => $BLID,
                                'fileGivenName' => $fileUploadNameArray[$eachfileUploadId],
                                'fileName' => $fileDocName,
                            ];
                        }
                    }
                } else {
                    $uploadedFilename = $fileUploadNameArray[$eachfileUploadId];
                    $uploadedFileId = $fileUploadIdArray[$eachfileUploadId];
                    self::updateCustomloanUploadDocument(['id' => $fileUploadIdArray[$eachfileUploadId], 'fileGivenName' => $uploadedFilename]);
                }
            }
            if (count($sqlParamsIns) > 0) {
                $sqlParams = [
                    'BLID' => $BLID,
                ];
                //$qryDel = 'DELETE FROM tblPCHMLOBasicLoanFiles WHERE BLID = :BLID ;';
                // Database2::getInstance()->executeQuery($qryDel, $sqlParams);
                Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
            }
        }
        /* End of tblPCHMLOBasicLoanFiles */

//tblPCHMLOBasicLoanlinks
        if (isset($_REQUEST['links'])) {

            $qryIns = ' INSERT INTO tblPCHMLOBasicLoanlinks (BLID, linkName, linkUrl) VALUES ( :BLID , :linkName , :linkUrl ) ';
            $sqlParamsIns = [];

            foreach ($_REQUEST['links']['linkUrl'] as $eachLinkid => $eachLinkVal) {
                if ($eachLinkVal != '') {
                    $sqlParamsIns[] = [
                        'BLID' => $BLID,
                        'linkName' => $_REQUEST['links']['linkName'][$eachLinkid],
                        'linkUrl' => $eachLinkVal,
                    ];
                }
            }
            if (count($sqlParamsIns) > 0) {
                $sqlParams = [
                    'BLID' => $BLID,
                ];
                $qryDel = 'DELETE FROM tblPCHMLOBasicLoanlinks WHERE BLID = :BLID ;';
                Database2::getInstance()->executeQuery($qryDel, $sqlParams);
                Database2::getInstance()->insertMulti($qryIns, $sqlParamsIns);
            }

        }
//End of tblPCHMLOBasicLoanlinks

        return $cnt;
    }

    /**
     * @param int $id
     * @return int
     */
    public static function deleteCustomloanUploadDocument(int $id): int
    {
        if (!($id > 0)) return 0;
        $sql = ' delete from tblPCHMLOBasicLoanFiles where id = :documentId ';
        $sqlParams['documentId'] = $id;
        $result = Database2::getInstance()->executeQuery($sql, $sqlParams);
        return $result['affected_rows'];
    }

    /**
     * @param array $sqlParams
     * @return int
     */
    public static function updateCustomloanUploadDocument(array $sqlParams): int
    {
        $updateQuery = 'update tblPCHMLOBasicLoanFiles set fileGivenName = :fileGivenName where id = :id ;';
        $result = Database2::getInstance()->executeQuery($updateQuery, $sqlParams);
        return $result['affected_rows'];

    }
}
