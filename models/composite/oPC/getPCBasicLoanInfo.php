<?php

namespace models\composite\oPC;

use models\Database2;
use models\storedProc\SP_GetPCBasicLoanTerm;
use models\types\strongType;

/**
 *
 */
class getPCBasicLoanInfo extends strongType
{
    /**
     * @param $inArray
     * @return array
     */
    public static function getReport($inArray): array
    {

        $PCID = 0;
        $BLID = 0;

        if (array_key_exists('PCID', $inArray)) $PCID = trim($inArray['PCID']);
        if (array_key_exists('BLID', $inArray)) $BLID = trim($inArray['BLID']);

        $infoArray = SP_GetPCBasicLoanTerm::getReport($PCID, $BLID);

        if (count($infoArray) > 0) {

            $multiArray = [
                'loanPgmInfo'
                , 'transactionTypeInfo'
                , 'loanPropertyTypeInfo'
                , 'extnOptionInfo'
                , 'loanTermInfo'
                , 'loanOccupancyInfo'
                , 'loanStateInfo'
                , 'loanNichesInfo'
                , 'marketPlaceLoanProgramInfo'
                , 'marketPlaceLoanProgranNotification'
                , 'marketPlaceLoanProgranCustomNotification'
                , 'loanAmortizationInfo'
                , 'rateLockPeriodInfo'
                , 'marketPlaceFiles'
                , 'marketPlaceLinks'
                , 'marketSBALoanProduct'
                , 'marketEquipmentType'
                , 'marketEntityStateOfFormation'
                , 'marketPaymentFrequency'
                , 'marketplaceMinSeasoningPersonalBankruptcy'
                , 'marketplaceMinSeasoningBusinessBankruptcy'
                , 'marketplaceMinSeasoningForeclosure'
                , 'marketplaceLoanPurpose'
                , 'marketMinTimeInBusiness'
                , 'marketeElgibleState'
                , 'loanFuturePropertyTypeInfo'
                , 'exitStrategyInfo'
            ];
            foreach ($multiArray as $value) {
                $newArray = $res = [];
                $tempBLID = '';
                $temp = trim($value);
                if (array_key_exists($temp, $infoArray)) {
                    $tempArray = $infoArray[$temp];
                    foreach ($tempArray as $val) {
                        $BLID = trim($val['BLID']);

                        if ($BLID != $tempBLID) $newArray = [];
                        $newArray[] = $val;

                        $res[$BLID] = $newArray;
                        $tempBLID = $BLID;
                    }
                }
                $infoArray[$temp] = $res;
            }

            if (array_key_exists('glNiches', $infoArray)) {
                $temp = 'glNiches';
                $newArrayTIDArray = [];
                $tempArray = $infoArray[$temp];
                foreach ($tempArray as $val) {
                    $NID = trim($val['NID']);
                    $newArrayTIDArray[$NID] = $val;
                }
                $infoArray[$temp] = $newArrayTIDArray;
            }
        }

        return $infoArray;
    }
}
