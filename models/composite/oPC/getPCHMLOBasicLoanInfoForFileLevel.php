<?php

namespace models\composite\oPC;

use models\storedProc\SP_GetPCHMLOBasicLoanInfoForFileLevel;
use models\types\strongType;

/**
 *
 */
class getPCHMLOBasicLoanInfoForFileLevel extends strongType
{
    // DEPRECATED DO NOT USE, INSTEAD USE SP_GetPCHMLOBasicLoanInfoForFileLevel::getReport()
    public static function getReportParams(
        int    $PCID,
        string $loanPgm
    ): array
    {
        return self::getReport([
            'PCID'    => $PCID,
            'loanPgm' => $loanPgm,
        ]);
    }

    /**
     * @param $inArray
     * @return array
     */
    public static function getReport($inArray): array
    {
        $PCID = 0;
        $loanPgm = '';

        if (array_key_exists('PCID', $inArray)) $PCID = intval($inArray['PCID']);
        if (array_key_exists('loanPgm', $inArray)) $loanPgm = trim($inArray['loanPgm']);

        return SP_GetPCHMLOBasicLoanInfoForFileLevel::getReport($loanPgm, $PCID);
    }
}
