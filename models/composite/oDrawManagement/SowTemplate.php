<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblPcDrawTemplateSettings;
use models\composite\oDrawManagement\SowCategory;
use models\lendingwise\tblDrawTemplateCategories;
use models\lendingwise\db\tblDrawTemplateCategories_db;
use models\types\strongType;

class SowTemplate extends strongType
{
    /**
     * @var int|null The ID of the template.
     */
    public ?int $id = null;

    /**
     * @var string|null The PC ID associated with the template.
     */
    public ?string $pcId = null;

    /**
     * @var SowCategory[] An array of SowCategory objects, indexed by category ID.
     */
    public array $categories = [];

    /**
     * SowTemplate constructor.
     * @param tblPcDrawTemplateSettings|null $template The database template object to initialize from.
     */
    public function __construct(?tblPcDrawTemplateSettings $template = null) {
        $this->id = $template->id;
        $this->pcId = $template->PCID;
        if ($template->id) $this->loadCategories();
    }
    /**
     * Load categories for a given template ID
     * @param int $templateId Template ID to load categories for
     * @return array Returns an array of SowCategory objects or an empty array
     */
    private function loadCategories(): void
    {
        $categoriesData = tblDrawTemplateCategories::GetAll(
            [tblDrawTemplateCategories_db::COLUMN_TEMPLATEID => $this->id],
            [tblDrawTemplateCategories_db::COLUMN_ORDER => 'ASC']
        );

        foreach ($categoriesData as $tblCategoryObj) {
            $this->addCategory(new SowCategory($tblCategoryObj));
        }
        $this->sortCategories();
    }

    /**
     * Adds a SowCategory object to the template's categories.
     * @param SowCategory $category The category object to add.
     * @return void
     */
    public function addCategory(SowCategory $category): void {
        $this->categories[$category->id] = $category;
    }

    /**
     * Sorts the categories by their order property.
     * @return void
     */
    private function sortCategories(): void {
        uasort($this->categories, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Converts the template object and its categories to an associative array.
     * @return array An associative array representation of the template.
     */
    public function toArray(): array {
        $categories = [];
        foreach ($this->categories as $category) {
            $categories[] = $category->toArray();
        }
        return [
            "id" => $this->id,
            "pcId" => $this->pcId,
            "categories" => $categories
        ];
    }

    /**
     * Retrieves all categories associated with this template.
     * @return SowCategory[] An array of SowCategory objects.
     */
    public function getAllCategories(): array {
        return $this->categories;
    }

    /**
     * Retrieves a specific category by its ID.
     * @param int $categoryId The ID of the category to retrieve.
     * @return SowCategory|null The SowCategory object if found, otherwise null.
     */
    public function getCategoryById(int $categoryId): ?SowCategory {
        return $this->categories[$categoryId] ?? null;
    }

    /**
     * Deletes categories and their associated line items from the template.
     *
     * @param array $categoriesIdsToDelete An array of category IDs to delete.
     * @return void
     */
    public function deleteCategories(array $categoriesIdsToDelete): void {
        if (empty($categoriesIdsToDelete)) return;

        foreach ($categoriesIdsToDelete as $categoryId) {
            if(isset($this->categories[$categoryId])) {
                foreach($this->categories[$categoryId]->lineItems as $lineItem) {
                    $lineItem->delete();
                }
                $this->categories[$categoryId]->delete();
                unset($this->categories[$categoryId]);
            }
        }
    }

    /**
     * Deletes specific line items from the template across all categories.
     *
     * @param array $lineItemIdsToDelete An array of line item IDs to delete.
     * @return void
     */
    public function deleteLineItems(array $lineItemIdsToDelete): void {
        if (empty($lineItemIdsToDelete)) return;

        $idsToDelete = array_flip($lineItemIdsToDelete);

        foreach ($this->categories as $category) {
            foreach ($category->lineItems as $lineItemId => $lineItem) {
                if (isset($idsToDelete[$lineItemId])) {
                    $lineItem->delete();
                    unset($category->lineItems[$lineItemId]);
                    unset($idsToDelete[$lineItemId]);
                    if (empty($idsToDelete)) {
                        return;
                    }
                }
            }
        }
    }
}
