<?php

namespace models\composite\oThirdPartyServices;

use models\types\strongType;
use models\PageVariables;
use models\thirdPartyServicesResponse;

class socialSecurityVerification extends strongType
{
    public static function getReport(?generate_third_party_docs $inputArray = null): thirdPartyServicesResponse
    {
        $request_type = $inputArray->request_type == 'Submit' ? 'Original' : $inputArray->request_type;
        $dataArray = $inputArray->toArray();
        $loanNumber = explode('_', $inputArray->loanIdentifier);
        $loanNumber = end($loanNumber);
        $dataArray['loanIdentifier'] = $loanNumber;
        $dataArray['request_type'] = $request_type;
        $dataArray['SSA89Document'] = $inputArray->ssa89;
        $dataArray['requestingParty'] = PageVariables::PC()->processingCompanyName;

        $postXML = '<?xml version="1.0" encoding="utf-8"?>';
        $rawXML = file_get_contents(__DIR__ . '/xml/socialSecurityXactus.xml');
        $XMLArr = generateXML::fetch($rawXML, $dataArray);
        $postXML .= $XMLArr["BEGIN"];
        if ($request_type == 'Original') {
            $postXML .= $XMLArr["ORIGINAL"];
        } else {
            $postXML .= $XMLArr["ORIGINAL_ELSE"];
        }
        $postXML .= $XMLArr["PART_1"];
        if ($request_type == 'Original') {
            $postXML .= $XMLArr["ORIGINAL_2"];
        }
        $postXML .= $XMLArr["END"];
        unset($dataArray);

        $xmlResponse = post_xml::getReport($postXML, $inputArray);

        return new thirdPartyServicesResponse([
            'request_xml' => $postXML,
            'response_xml' => $xmlResponse,
            'orderDetails' => $inputArray,
            'option' => $inputArray->option,
        ]);
    }
}