<?php

namespace models\composite\oThirdPartyServices;

use models\types\strongType;
use models\Curl;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;
use models\constants\gl\glThirdPartyGeraciConstants;

class submitLegalDocsData extends strongType
{
    public static function getReport($request, $authorization): array
    {
        $result = [
            'status' => false,
            'message' => 'Error Encountered. Please contact support!',
        ];

        //Borrower Information
        $borrowerIndividualOrEntity = Request::GetClean("borrowerEntityOrIndividual");
        if ($borrowerIndividualOrEntity === 'entity') {
            $borrowerName = Request::GetClean("entityName");
            $entityType = Request::GetClean("entityType");
            $borrowerAddress = Request::GetClean("entityAddress");
            $borrowerCity = Request::GetClean("entityCity");
            $borrowerState = Request::GetClean("entityState");
            $borrowerZip = Request::GetClean("entityZip");
        } else {
            $borrowerName = Request::GetClean("borrowerName");
            $entityType = "individual";
            $borrowerAddress = Request::GetClean("borrowerAddress");
            $borrowerCity = Request::GetClean("borrowerCity");
            $borrowerState = Request::GetClean("borrowerState");
            $borrowerZip = Request::GetClean("borrowerZip");
        }

        $complexBorrowerSigners = self::getNestedSigners(Request::GetClean("signer"));
        $borrowers[] = [
            "name" => $borrowerName,
            "entityType" => glThirdPartyGeraciConstants::$entityTypes[$entityType] ?? '',
            "formationState" => glThirdPartyGeraciConstants::$states[Request::GetClean("entityStateOfFormation")] ?? '',
            "complexSigners" => $complexBorrowerSigners
        ];
        $borrowerInformation = [
            "borrowers" => $borrowers,
            "noticeTo" => Request::GetClean("emailOnBehalfOfBorrower") ?? '',
            "deliveryTo" => Request::GetClean("emailOnBehalfOfBorrowerName") ?? '',
            "address" => [
                "street" => $borrowerAddress,
                "city" => $borrowerCity,
                "state" => glThirdPartyGeraciConstants::$states[$borrowerState] ?? '',
                "zip" => $borrowerZip
            ]
        ];

        //Guarantor Information
        $isLoanGuaranty = Request::GetClean("isLoanGuaranty") === "yes";
        $guarantors = [];
        if ($isLoanGuaranty) {
            foreach (Request::GetClean("guarantors") ?? [] as $guarantor) {
                $complexGuarantorSigners = self::getNestedSigners($guarantor['signer']);
                $guarantors[] = [
                    "name" => $guarantor["name"],
                    "entityType" => glThirdPartyGeraciConstants::$entityTypes[$guarantor["type"]] ?? '',
                    "formationState" => glThirdPartyGeraciConstants::$states[$guarantor["stateOfFormation"]] ?? '',
                    "guarantorType" => $guarantor["guarantyType"],
                    "addressType" => $guarantor["addressType"],
                    "address" => [
                        "street" => $guarantor["address"],
                        "city" => $guarantor["city"],
                        "state" => glThirdPartyGeraciConstants::$states[$guarantor["state"]] ?? '',
                        "zip" => $guarantor["zip"]
                    ],
                    "complexSigners" => $complexGuarantorSigners
                ];
            }
        }
        $guarantorInformation = [
            "isGuaranty" => $isLoanGuaranty,
            "guarantors" => $guarantors
        ];

        //Lender Information
        $lenderInformation = [
            "name" => Request::GetClean("lenderName"),
            "entityType" => glThirdPartyGeraciConstants::$entityTypes[Request::GetClean("lenderType")] ?? '',
            "formationState" => glThirdPartyGeraciConstants::$states[Request::GetClean("lenderStateOfFormation")] ?? '',
            "noticeTo" => Request::GetClean("lenderNoticeTo") ?? '',
            "noticeRecipient" => Request::GetClean("lenderNoticeDeliverTo") ?? '',
            "address" => [
                "street" => Request::GetClean("lenderAddress"),
                "city" => Request::GetClean("lenderCity"),
                "state" => glThirdPartyGeraciConstants::$states[Request::GetClean("lenderState")] ?? '',
                "zip" => Request::GetClean("lenderZip")
            ]
        ];

        //Property Information
        $borrowerVesting = Request::GetClean("subjectPropertyBorrowerVesting");
        $borrowerVesting = $borrowerVesting === "Other" ? Request::GetClean('subjectPropertyBorrowerVestingOther') : $borrowerVesting;
        $collateralProperties = [
            "county" => Request::GetClean("subjectPropertyCounty"),
            "propertyID" => Request::GetClean("subjectPropertyParcelNo"),
            "lienPosition" => (int)Request::GetClean("subjectPropertyLienPosition"),
            "owners" => [
                [
                    "owner" => $borrowerName,
                    "vesting" => $borrowerVesting
                ]
            ],
            "address" => [
                "street" => Request::GetClean("subjectPropertyAddress"),
                "city" => Request::GetClean("subjectPropertyCity"),
                "state" => glThirdPartyGeraciConstants::$states[Request::GetClean("subjectPropertyState")] ?? '',
                "zip" => Request::GetClean("subjectPropertyZip")
            ],
        ];
        $propertyInformation = [
            "governingLawState" => glThirdPartyGeraciConstants::$states[Request::GetClean("subjectPropertyGoverningLawState")] ?? '',
            "noteCounty" => Request::GetClean("subjectPropertyDisputeCounty"),
            "collateralProperties" => [$collateralProperties]
        ];

        //Loan Information
        $isVariableRateLoan = Request::GetClean("variableRateLoan") === "yes";
        $variableLoanInformation = null;
        if ($isVariableRateLoan) {
            $variableLoanInformation = [
                "variableMargin" => Strings::replaceCommaValues(Request::GetClean("variableMarginPercent")),
                "variableFirstChange" => (int)Request::GetClean("variableMonthsBeforeFirstChange"),
                "variableSubsequentChange" => (int)Request::GetClean("variableMonthsBetweenChangeDates"),
                "variableFirstCap" => Strings::replaceCommaValues(Request::GetClean("variableFirstCap")),
                "variableSubsequentCap" => Strings::replaceCommaValues(Request::GetClean("variableSubsequentCap")),
                "variableFloor" => Strings::replaceCommaValues(Request::GetClean("variableFloorRate")),
                "variableCeiling" => Strings::replaceCommaValues(Request::GetClean("variableCeilingRate")),
                "index" => glThirdPartyGeraciConstants::$variableRateIndex[Request::GetClean("variableRateIndex")] ?? ''
            ];
        }
        $loanInformation = [
            "loanNumber" => Request::GetClean("loanNumber"),
            "closingDate" => Dates::dateFormatDatabase(Request::GetClean("actualClosingDate")),
            "term" => (int)Request::GetClean("loanTerm"),
            "amount" => Strings::replaceCommaValues(Request::GetClean("loanAmount")),
            "rate" => Strings::replaceCommaValues(Request::GetClean("interestRate")),
            "defaultRate" => Strings::replaceCommaValues(Request::GetClean("defaultInterestRate")),
            "calculation" => glThirdPartyGeraciConstants::$accrualTypes[Request::GetClean("accrualType")] ?? '',
            "isInterestOnly" => Request::GetClean("initialInterestOnlyPeriod") === "yes",
            "ioPeriod" => (int)Request::GetClean("interestOnlyMonths"),
            "amortization" => (int)Request::GetClean("lien1Terms"),
            "isMERS" => Request::GetClean("insertMERSLanguage") === "yes",
            "mersNumber" => Request::GetClean("mersMINNumber"),
            "isVariable" => $isVariableRateLoan,
            "variableInformation" => $variableLoanInformation
        ];

        //Loan Features
        $isConstruction = Request::GetClean("isLenderHoldback") === "yes";
        $construction = null;
        if ($isConstruction) {
            $construction = [
                "reserveAmount" => Strings::replaceCommaValues(Request::GetClean("rehabCostFinanced")),
                "isNonDutch" => Request::GetClean("isNonDutch") === "yes",
                "constructionType" => Request::GetClean("constructionReserveType"),
            ];
        }
        $isImpounds = Request::GetClean("includeTaxInsuranceImpounds") === "yes";
        $impounds = null;
        if ($isImpounds) {
            $impounds = [
                "initialTax" => Strings::replaceCommaValues(Request::GetClean("initialTaxEscrowAmount")),
                "initialInsurance" => Strings::replaceCommaValues(Request::GetClean("initialInsuranceEscrowAmount")),
                "initialFlood" => Strings::replaceCommaValues(Request::GetClean("initialFloodInsuranceImpound")),
                "monthlyTax" => Strings::replaceCommaValues(Request::GetClean("monthlyTaxPayment")),
                "monthlyInsurance" => Strings::replaceCommaValues(Request::GetClean("monthlyPropertyInsurance")),
                "monthlyFlood" => Strings::replaceCommaValues(Request::GetClean("monthlyFloodInsurance")),
                "monthlyCapEx" => Strings::replaceCommaValues(Request::GetClean("monthlyCapExImpound"))
            ];
        }
        $isExtension = Request::GetClean("conditionalRightExtension") === "yes";
        $extension = null;
        if ($isExtension) {
            $extension = [
                "extensions" => (int)Request::GetClean("numberOfExtensions"),
                "monthsPerExtension" => (int)Request::GetClean("monthsPerExtension"),
            ];
            $extensionFeeType = Request::GetClean("extensionFeeType");
            $extension['feeType'] = $extensionFeeType;
            if ($extensionFeeType === "Percent") {
                $extension['feeAmount'] = Strings::replaceCommaValues(Request::GetClean("percentOfLoanBalance"));
            } elseif ($extensionFeeType === "Dollar") {
                $extension['feeAmount'] = Strings::replaceCommaValues(Request::GetClean("amountOfExtensionFee"));
            }
        }
        $loanFeatures = [
            "isPrepaymentPremium" => Request::GetClean("isPrepaymentPremium") === "yes",
            "prepaymentPremium" => Request::GetClean("prepaymentPremiumOptions") ?? '',
            "prepaymentTerm0" => (int)Request::GetClean("guaranteedInterestMonths") ?? '',
            "prepaymentTerm1" => (int)Request::GetClean("prepayStepYears") ?? '',
            "prepaymentTerm2" => (int)Request::GetClean("lockoutPeriod") ?? '',
            "interestReserve" => Request::GetClean("includeDebtServiceReserve") ?? '',
            "interestReserveDollars" => Strings::replaceCommaValues(Request::GetClean("specificDollarAmount")) ?? '',
            "interestReserveMonths" => (int)Request::GetClean("numMonthsToCalculate") ?? '',
            "isExitFee" => Request::GetClean("includeExitFee") === "yes",
            "exitFee" => Strings::replaceCommaValues(Request::GetClean("exitFeeAmount")) ?? '',
            "isConstruction" => $isConstruction,
            "construction" => $construction,
            "isImpounds" => $isImpounds,
            "impounds" => $impounds,
            "isExtension" => $isExtension,
            "extension" => $extension
        ];

        //Title Information
        $titleInformation = [
            "name" => Request::GetClean("titleCompanyName"),
            "contact" => Request::GetClean("titleOfficerContactName"),
            "email" => Request::GetClean("titleOfficerContactEmail"),
            "orderNumber" => Request::GetClean("titleOrderNumber"),
            "reportDate" => Dates::dateFormatDatabase(Request::GetClean('titleReportEffectiveDate')),
            "exceptionItems" => Request::GetClean("titleExceptionItems")
        ];
        $isEscrow = Request::GetClean("includeEscrowInfo") === "yes";
        $escrowInformation = null;
        if ($isEscrow) {
            $escrowInformation = [
                "name" => Request::GetClean("escrowCompanyName"),
                "contact" => Request::GetClean("escrowOfficerContactName"),
                "email" => Request::GetClean("escrowOfficerContactEmail"),
            ];
        }

        //Settlement Information
        $otherFees = [];
        foreach (Request::GetClean("otherFees") ?? [] as $otherFee) {
            $otherFeeAmount = $otherFee["fee"];
            if (!empty($otherFeeAmount)) {
                $otherFees[] = [
                    "amount" => Strings::replaceCommaValues($otherFeeAmount),
                    "feeDescription" => $otherFee["description"],
                    "delivery" => $otherFee["comment"],
                    "payee" => $otherFee["paidTo"]
                ];
            }
        }
        $settlementInformation = [
            "brokerFees" => [[
                "amount" => Strings::replaceCommaValues(Request::GetClean("brokerFees")),
                "feeDescription" => Request::GetClean("brokerFeesDescription"),
                "delivery" => Request::GetClean("brokerFeesComment")
            ]],
            "lenderFees" => [[
                "amount" => Strings::replaceCommaValues(Request::GetClean("lenderFees")),
                "feeDescription" => Request::GetClean("lenderFeesDescription"),
                "delivery" => Request::GetClean("lenderFeesComment")
            ]],
            "otherFees" => $otherFees
        ];

        //Loan Preparer Information
        $loanPreparerInformation = [
            "name" => Request::GetClean("loanPreparerName"),
            "email" => Request::GetClean("loanPreparerEmail"),
            "addressType" => Request::GetClean("loanPreparerAddressType"),
        ];

        //Closing Contact Information
        $closingContactInformation = [
            "name" => Request::GetClean("closingContactName"),
            "email" => Request::GetClean("closingContactEmail")
        ];

        //Additional Documents
        $additionalDocuments = [
            "isW9" => Request::GetClean("includeW9") === "yes",
            "isFirstPaymentLetter" => Request::GetClean("includeFirstPaymentLetter") === "yes",
            "firstPaymentAmount" => Strings::replaceCommaValues(Request::GetClean("firstPaymentAmount"))
        ];

        //Final Data to be submitted
        $docsData = [
            "borrowerInformation"   => $borrowerInformation,
            "guarantorInformation"  => $guarantorInformation,
            "lenderInformation"     => $lenderInformation,
            "servicerInformation"   => ["servicer" => Request::GetClean("lenderLoanServicer")],
            "collateral"            => $propertyInformation,
            "loan"                  => $loanInformation,
            "features"              => $loanFeatures,
            "isACH"                 => $request["includeACHDelivery"] === "yes",
            "titleInformation"      => $titleInformation,
            "isEscrow"              => $isEscrow,
            "escrowInformation"     => $escrowInformation,
            "settlement"            => $settlementInformation,
            "preparerInformation"   => $loanPreparerInformation,
            "closingContact"        => $closingContactInformation,
            "additionalDocuments"   => $additionalDocuments
        ];

        $convertedData = Curl::PostJSON(CONST_LIGHTNING_DOCS_API_URL, $docsData);
        $convertedData = json_decode($convertedData->Body, true);
        if (!isset($convertedData['status'])) {
            $login = [
                "KeyID" => CONST_LIGHTNING_DOCS_API_KEY,
                "Secret" => CONST_LIGHTNING_DOCS_API_SECRET
            ];
            $login = Curl::PostJSON(CONST_KNACKLY_API_URL . "/auth/login", $login);
            $login = json_decode($login->Body, true)['token'];

            $URL = CONST_KNACKLY_API_URL . "/catalogs/" . $authorization['catalogName'] . "/items";
            $headers = [
                "Authorization" => $login
            ];
            $submitData = Curl::PostJSON($URL, $convertedData, $headers);
            $submitData = json_decode($submitData->Body, true);
            if (!empty($submitData['id'])) {
                $result = [
                    'status' => true,
                    'message' => 'Data submitted successfully!',
                ];
            }
        }

        $result['data'] = [
            'request' => $docsData,
            'response' => $submitData ?? $convertedData,
        ];

        return $result;
    }


    public static function getNestedSigners($input): array
    {
        $result = [];
        foreach ($input as $value) {
            if (isset($value["signer"])) {
                $signers = self::getNestedSigners($value["signer"]);
            }
            $result[] = [
                "name" => $value["name"],
                "signerTitle" => $value["title"],
                "entityType" => glThirdPartyGeraciConstants::$entityTypes[$value["type"]] ?? '',
                "formationState" => glThirdPartyGeraciConstants::$states[$value["stateOfFormation"]] ?? '',
                "signers" => $signers ?? []
            ];
        }

        return $result;
    }
}