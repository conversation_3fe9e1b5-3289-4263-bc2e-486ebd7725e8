<?php

namespace models\composite\oThirdPartyServices;

use models\types\strongType;
use models\Curl;
use models\constants\gl\glCRAServices;
use models\constants\gl\glThirdPartyServicesCRA;
use models\constants\gl\glCustomJobForProcessingCompany;


class post_xml extends strongType
{
    /**
     * Post information to third party services using curl.
     */
    public static function getReport($postXML, $inputArray)
    {
        $glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
        $cra = $inputArray->cra;
        $service = $inputArray->service;
        $PCID = $inputArray->FPCID;
        $authorization = getThirdPartyServicesAuthorization::getReport(
            $PCID,
            $inputArray->cra,
            $inputArray->userGroup,
            $inputArray->userNumber
        );

        if (!isset($glThirdPartyServicesCRA[$cra])) {
            return [];
        }

        $URL = $glThirdPartyServicesCRA[$cra]->Services[$service]['link'];
        if ($cra === 'xactus') {
            $URL = glCustomJobForProcessingCompany::getXactusURL($PCID) . $URL;
            if ($service === glCRAServices::SOCIAL_SECURITY) {
                $postXML = str_replace('[-]loginUsername[-]', $authorization['username'], $postXML);
                $postXML = str_replace('[-]loginPassword[-]', $authorization['password'], $postXML);
            } else {
                $URL .= '?LoginAccountIdentifier=' . $authorization['username'] . '&LoginAccountPassword=' . $authorization['password'];
            }
        } else {
            $headers = [
                'authorization' => 'Basic ' . base64_encode($authorization['username'] . ':' . $authorization['password']),
                'MCL-Interface' => 'LendingWise01142020',
            ];
        }

        $result = Curl::PostXML($URL, $postXML, $headers ?? []);

        return $result->Body;
    }
}