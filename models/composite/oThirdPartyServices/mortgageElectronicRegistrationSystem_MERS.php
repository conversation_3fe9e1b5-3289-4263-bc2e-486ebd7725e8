<?php

namespace models\composite\oThirdPartyServices;

use models\thirdPartyServicesResponse;
use models\types\strongType;

/**
 *
 */
class mortgageElectronicRegistrationSystem_MERS extends strongType
{
    /**
     * Report : Mortgage Electronic Registration System (MERS).
     * Functionality Includes in : Polling, Retrieving reports.
     * @option is Cron or not.
     */
    public static function getReport(?generate_third_party_docs $inputArray = null): thirdPartyServicesResponse
    {
        $inputArray->creditCardNumber = str_replace(' ', '', $inputArray->creditCardNumber);
        $inputArray->cardNumberOnBack = str_replace(' ', '', $inputArray->cardNumberOnBack);
        $inputArray->cardHolderName = str_replace('.', '', $inputArray->cardHolderName);
        $loanNumber = explode('_', $inputArray->loanIdentifier);
        $loanNumber = end($loanNumber);

        $request_type = $inputArray->request_type;
        if ($request_type == 'Submit') {
            $request_type = 'Order';
        } else {
            $request_type = 'Get';
        }

        $postXML = '<?xml version="1.0" encoding="utf-8"?>
<MESSAGE MessageType="Request" xmlns="http://www.mismo.org/residential/2009/schemas" xmlns:p2="http://www.w3.org/1999/xlink" xmlns:p3="inetapi/MISMO3_4_MCL_Extension.xsd">
	<ABOUT_VERSIONS>
		<ABOUT_VERSION>
			<DataVersionIdentifier>201703</DataVersionIdentifier>
		</ABOUT_VERSION>
	</ABOUT_VERSIONS>
	<DEAL_SETS>
		<DEAL_SET>
			<DEALS>
				<DEAL>
					<LOANS>
						<LOAN>
							<LOAN_IDENTIFIERS>
								<LOAN_IDENTIFIER>
									<LoanIdentifier>' . $loanNumber . '</LoanIdentifier>
								</LOAN_IDENTIFIER>
							</LOAN_IDENTIFIERS>
						</LOAN>
					</LOANS>
					<PARTIES>
						<PARTY p2:label="Party1">
							<INDIVIDUAL>
								<NAME>
									<FirstName>' . $inputArray->borrowerName . '</FirstName>
									<LastName>' . $inputArray->borrowerLName . '</LastName>
									<MiddleName></MiddleName>
									<SuffixName></SuffixName>
								</NAME>
							</INDIVIDUAL>
							<ROLES>
								<ROLE>
									<ROLE_DETAIL>
										<PartyRoleType>Borrower</PartyRoleType>
									</ROLE_DETAIL>
								</ROLE>
							</ROLES>
							<TAXPAYER_IDENTIFIERS>
								<TAXPAYER_IDENTIFIER>
									<TaxpayerIdentifierType>SocialSecurityNumber</TaxpayerIdentifierType>
									<TaxpayerIdentifierValue>' . $inputArray->ssnNumber . '</TaxpayerIdentifierValue>
								</TAXPAYER_IDENTIFIER>
							</TAXPAYER_IDENTIFIERS>
						</PARTY>			
					</PARTIES>
					<RELATIONSHIPS>
						<!-- Link borrower to the service -->
						<RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE" p2:from="Party1" p2:to="Service1"/>
';
        if ($request_type == 'Order') {
            $postXML .= '
						<!-- If a subject property is being provided, link the property to the service -->
						<RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PROPERTY_IsVerifiedBy_SERVICE" p2:from="Property1" p2:to="Service1"/>
';
        }
        $postXML .= '
					</RELATIONSHIPS>
					<SERVICES>
						<SERVICE p2:label="Service1">
							<FRAUD>
								<EXTENSION>
									<OTHER>
										<p3:FRAUD_DETAIL>
											<p3:COMMON_FRAUD_DETAIL>
												<p3:FraudRequestActionType>' . $request_type . '</p3:FraudRequestActionType>
											</p3:COMMON_FRAUD_DETAIL>
										</p3:FRAUD_DETAIL>
									</OTHER>
								</EXTENSION>
							</FRAUD>
							<SERVICE_PRODUCT>
								<SERVICE_PRODUCT_REQUEST>
									<SERVICE_PRODUCT_DETAIL>
										<ServiceProductDescription>MERS</ServiceProductDescription>
										<ServiceProductIdentifier>' . $inputArray->ssnNumber . '</ServiceProductIdentifier>
										<EXTENSION>
											<OTHER>
												<!-- Recommend requesting only the formats you need, to minimize processing time -->
												<p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
													<p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
														<p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
															<p3:PreferredResponseFormatType>Pdf</p3:PreferredResponseFormatType>
														</p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
													</p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
												</p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
											</OTHER>
										</EXTENSION>
									</SERVICE_PRODUCT_DETAIL>
								</SERVICE_PRODUCT_REQUEST>
							</SERVICE_PRODUCT>
';
        if ($request_type == 'Get') {
            $postXML .= '
							<SERVICE_PRODUCT_FULFILLMENT>
								<SERVICE_PRODUCT_FULFILLMENT_DETAIL>                  
									<VendorOrderIdentifier>' . $inputArray->vendorOrderIdentifier . '</VendorOrderIdentifier>
								</SERVICE_PRODUCT_FULFILLMENT_DETAIL>
							</SERVICE_PRODUCT_FULFILLMENT>
';
        }
        $postXML .= '
						</SERVICE>
					</SERVICES>
				</DEAL>
			</DEALS>
		</DEAL_SET>
	</DEAL_SETS>
</MESSAGE>
';

        $xmlResponse = post_xml::getReport($postXML, $inputArray);

        return new thirdPartyServicesResponse([
            'request_xml' => $postXML,
            'response_xml' => $xmlResponse,
            'orderDetails' => $inputArray,
            'option' => $inputArray->option,
        ]);
    }
}