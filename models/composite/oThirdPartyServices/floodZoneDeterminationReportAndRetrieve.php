<?php

namespace models\composite\oThirdPartyServices;

use models\types\strongType;
use models\PageVariables;
use models\constants\gl\glCRAServices;
use models\thirdPartyServicesResponse;

/**
 *
 */
class floodZoneDeterminationReportAndRetrieve extends strongType
{
    public static function getReport(?generate_third_party_docs $inputArray = null): thirdPartyServicesResponse
    {
        $request_type = $inputArray->request_type == 'Submit' ? 'Original' : $inputArray->request_type;
        $dataArray = $inputArray->toArray();
        $loanNumber = explode('_', $inputArray->loanIdentifier);
        $loanNumber = end($loanNumber);
        $dataArray['loanIdentifier'] = $loanNumber;
        $dataArray['request_type'] = $request_type;
        $dataArray['requestingParty'] = PageVariables::PC()->processingCompanyName;
        $dataArray['requestDescription'] = 'Residential';
        if ($inputArray->service === glCRAServices::CO_BORROWER_FLOOD) {
            $dataArray['borrowerName'] = $inputArray->coBorrowerFName;
            $dataArray['borrowerLName'] = $inputArray->coBorrowerLName;
        } elseif ($inputArray->borrowerType !== 'Individual') {
            $dataArray['requestDescription'] = 'Commercial';
            $dataArray['borrowerName'] = '';
            $dataArray['borrowerLName'] = $inputArray->entityName;
        }

        $inputArray->creditCardNumber = str_replace(' ', '', $inputArray->creditCardNumber);
        $inputArray->cardNumberOnBack = str_replace(' ', '', $inputArray->cardNumberOnBack);
        $inputArray->cardHolderName = str_replace('.', '', $inputArray->cardHolderName);

        $postXML = '<?xml version="1.0" encoding="utf-8"?>';
        if ($inputArray->cra === 'xactus') {
            $rawXML = file_get_contents(__DIR__ . '/xml/floodXactus.xml');
            $XMLArr = generateXML::fetch($rawXML, $dataArray);
            $postXML .= $XMLArr["BEGIN"];
            if ($request_type == 'Original') {
                $postXML .= $XMLArr["ORIGINAL"];
                if ($inputArray->service === glCRAServices::JOINT_FLOOD) {
                    $postXML .= $XMLArr["JOINT"];
                }
                $postXML .= $XMLArr["PART_1"];
            } else {
                $postXML .= $XMLArr["ORIGINAL_ELSE"];
            }
        } else {
            $rawXML = file_get_contents(__DIR__ . '/xml/floodMeridianLink.xml');
            $XMLArr = generateXML::fetch($rawXML, $dataArray);
            $postXML .= $XMLArr["BEGIN"];
            if ($request_type === 'Original') {
                $postXML .= $XMLArr["ORIGINAL_1"];
            } else {
                $postXML .= $XMLArr["ORIGINAL_ELSE_1"];
            }
            $postXML .= $XMLArr["PART_1"];
            if ($inputArray->borrowerType === 'Individual') {
                $postXML .= $XMLArr["INDIVIDUAL"];
            } else {
                $postXML .= $XMLArr["BUSINESS"];
            }
            $postXML .= $XMLArr["PART_2"];
            if ($request_type === 'Original') {
                $postXML .= $XMLArr["ORIGINAL_2"];
            } else {
                $postXML .= $XMLArr["ORIGINAL_ELSE_2"];
            }
            $postXML .= $XMLArr["PART_3"];
            if ($request_type === 'Original') {
                $postXML .= $XMLArr["ORIGINAL_3"];
            }
            $postXML .= $XMLArr["PART_4"];
            if ($request_type === 'Original' && $inputArray->sendCCInfo == 1) {
                $postXML .= $XMLArr["ORIGINAL_AND_SEND_CC"];
            }
            $postXML .= $XMLArr["PART_5"];
            if ($request_type === 'Original') {
                $postXML .= $XMLArr["ORIGINAL_4"];
            } else {
                $postXML .= $XMLArr["ORIGINAL_ELSE_4"];
            }
        }
        $postXML .= $XMLArr["END"];
        unset($dataArray);

        $xmlResponse = post_xml::getReport($postXML, $inputArray);

        return new thirdPartyServicesResponse([
            'request_xml' => $postXML,
            'response_xml' => $xmlResponse,
            'orderDetails' => $inputArray,
            'option' => $inputArray->option,
        ]);
    }
}