<?php

namespace models\composite\oThirdPartyServices;

use models\APIHelper;
use models\constants\gl\glCRAServices;
use models\Database2;
use models\Log;
use models\standard\Dates;
use models\standard\Strings;
use models\thirdPartyServicesResponse;
use models\types\strongType;

/**
 *
 */
class generate_third_party_docs extends strongType
{
    public ?int $LMRId = null;
    public ?string $tID = null;
    public ?string $cra = null;
    public ?string $service = null;
    public ?string $valuationModel = null;
    public ?string $request_type = null;
    public ?string $paymentThrough = null;
    public ?string $option = null;
    public ?string $vendorOrderIdentifier = null;
    public ?string $loanIdentifier = null;
    public ?string $response_xml = null;
    public ?string $statusCode = null;
    public ?string $authorization = null;
    public ?string $age = null;
    public ?string $created_at = null;

    // populated by getLoanFileInformation
    public ?int $FPCID = null;
    public ?string $borrowerType = null;
    public ?string $borrowerName = null;
    public ?string $borrowerLName = null;
    public ?string $borrowerEmail = null;
    public ?string $phoneNumber = null;
    public ?string $cellNumber = null;
    public ?string $ssnNumber = null;
    public ?string $borrowerDOB = null;
    public ?string $propertyAddress = null;
    public ?string $propertyCity = null;
    public ?string $propertyState = null;
    public ?string $propertyZip = null;
    public ?string $presentAddress = null;
    public ?string $presentCity = null;
    public ?string $presentZip = null;
    public ?string $presentState = null;
    public ?string $legalDescription = null;
    public ?string $parcelNo = null;
    public ?string $entityName = null;
    public ?string $entityAddress = null;
    public ?string $entityCity = null;
    public ?string $entityZip = null;
    public ?string $entityState = null;
    public ?string $coBorrowerFName = null;
    public ?string $coBorrowerLName = null;
    public ?string $coBPhoneNumber = null;
    public ?string $coBorrowerEmail = null;
    public ?string $coBSsnNumber = null;
    public ?string $coBorrowerDOB = null;
    public ?string $coBPresentAddress = null;
    public ?string $coBPresentCity = null;
    public ?string $coBPresentState = null;
    public ?string $coBPresentZip = null;
    public ?string $typeOfHMLOLoanRequesting = null;
    public ?string $propertyValue = null;

    //
    public ?string $creditCardType = null;
    public ?string $creditCardNumber = null;
    public ?string $expirationMonth = null;
    public ?string $expirationYear = null;
    public ?string $cardHolderName = null;
    public ?string $cardNumberOnBack = null;
    public ?string $billingAddress1 = null;
    public ?string $billingCity = null;
    public ?string $billingState = null;
    public ?string $billingZip = null;

    public ?float $homeValue = null;
    public ?int $sendCCInfo = null;
    public ?string $employer1Phone = null;
    public ?string $businessPhone = null;

    public ?string $notificationEmail = null;
    public ?int $userNumber = null;
    public ?string $userGroup = null;
    public ?string $firstName = null;
    public ?string $lastName = null;
    public ?string $userRole = null;

    public ?string $PCDetailID = null;
    public ?string $PCID = null;
    public ?string $serviceKey = null;
    public ?string $username = null;
    public ?string $password = null;
    public ?string $billingAddress2 = null;

    public ?string $APIKey = null;
    public ?string $APISecret = null;
    public ?string $catalogName = null;
    public ?string $ssa89 = null;

    /**
     * Retrieve/Polling 3rd party document.
     * Cron File : generate_third_party_docs.php
     */
    public static function getReport(
        bool $debug = false,
        int  $tID = null
    )
    {
        $result = self::getRecords($debug, $tID);
        shuffle($result);

        $m = sizeof($result);
        foreach ($result as $i => $record) {
            Log::Insert(($i + 1) . ' / ' . $m . ' : ' . $record->tID);
            getLoanFileInformation::getReport($record);
        }
        return null;
    }

    /**
     * @param bool $debug
     * @param int|null $tID
     * @return self[]
     */
    public static function getRecords(
        bool $debug = false,
        int  $tID = null
    ): array
    {
        $qry = APIHelper::getSQL(__DIR__ . '/generate_third_party_docs/getRecords.sql');

        $result = Database2::getInstance()->queryData($qry, [
            'debug' => $debug ? 1 : 0,
            'tID'   => $tID,
        ], function ($row) {
            return new self($row);
        });

        return $result;
    }

    public function Save(): thirdPartyServicesResponse
    {
        return $this->processThirdPartyServices();
    }

    /**
     * Process all service based report generation.
     */
    public function processThirdPartyServices(): thirdPartyServicesResponse
    {
        // clean up needs to happen here
        $this->borrowerName = Strings::AlphaNumericOnly($this->borrowerName);
        $this->borrowerLName = Strings::AlphaNumericOnly($this->borrowerLName);

        $this->creditCardNumber = Strings::getNumberValue($this->creditCardNumber);
        $this->cardNumberOnBack = Strings::getNumberValue($this->cardNumberOnBack);

        $this->cardHolderName = Strings::AlphaNumericOnly($this->cardHolderName);

        $this->ssnNumber = Strings::getNumberValue($this->ssnNumber);
        $this->phoneNumber = Strings::PhoneNumberNoExt($this->phoneNumber);
        $this->cellNumber = Strings::PhoneNumberNoExt($this->cellNumber);
        $this->employer1Phone = Strings::PhoneNumberNoExt($this->employer1Phone);
        $this->businessPhone = Strings::PhoneNumberNoExt($this->businessPhone);
        $this->coBSsnNumber = Strings::getNumberValue($this->coBSsnNumber);
        $this->coBPhoneNumber = Strings::PhoneNumberNoExt($this->coBPhoneNumber);

        $this->borrowerDOB = Dates::Datestamp($this->borrowerDOB);
        $this->coBorrowerDOB = Dates::Datestamp($this->coBorrowerDOB);


        if ($this->phoneNumber === '1231231234') {
            $this->phoneNumber = '6029301965'; // can't use an obviously fake number
        }

        if ($this->employer1Phone === '1231231234') {
            $this->employer1Phone = '6029301965'; // can't use an obviously fake number
        }

        return processThirdPartyServices::processReport($this);
    }
}