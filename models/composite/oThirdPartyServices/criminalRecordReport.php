<?php

namespace models\composite\oThirdPartyServices;

use models\thirdPartyServicesResponse;
use models\types\strongType;

/**
 *
 */
class criminalRecordReport extends strongType
{
    /**
     * Report : Criminal Record Report.
     * Functionality Includes in : Polling, Retrieving reports.
     * @option is Cron or not.
     */
    public static function getReport(?generate_third_party_docs $inputArray = null): thirdPartyServicesResponse
    {
        $inputArray->creditCardNumber = str_replace(' ', '', $inputArray->creditCardNumber);
        $inputArray->cardNumberOnBack = str_replace(' ', '', $inputArray->cardNumberOnBack);
        $inputArray->cardHolderName = str_replace('.', '', $inputArray->cardHolderName);
        $loanNumber = explode('_', $inputArray->loanIdentifier);
        $loanNumber = end($loanNumber);

        $request_type = $inputArray->request_type;

        $postXML = '<?xml version="1.0" encoding="utf-8"?>
<MESSAGE MessageType="Request" xmlns="http://www.mismo.org/residential/2009/schemas" xmlns:p2="http://www.w3.org/1999/xlink" xmlns:p3="inetapi/MISMO3_4_MCL_Extension.xsd">
	<ABOUT_VERSIONS>
		<ABOUT_VERSION>
			<DataVersionIdentifier>201706</DataVersionIdentifier>
		</ABOUT_VERSION>
	</ABOUT_VERSIONS>
	<DEAL_SETS>
		<DEAL_SET>
			<DEALS>
				<DEAL>
';
        if ($request_type == 'Submit') {
            $postXML .= '
					<LOANS>
						<LOAN>
							<LOAN_IDENTIFIERS>
								<LOAN_IDENTIFIER>
									<LoanIdentifier>' . $loanNumber . '</LoanIdentifier>
								</LOAN_IDENTIFIER>
							</LOAN_IDENTIFIERS>
							<TERMS_OF_LOAN>
								<LoanPurposeType>' . $inputArray->typeOfHMLOLoanRequesting . '</LoanPurposeType>
								<LoanPurposeTypeOtherDescription>EmployeesApplicationForCredit</LoanPurposeTypeOtherDescription>
							</TERMS_OF_LOAN>
						</LOAN>
					</LOANS>
';
        }
        $postXML .= '
					<PARTIES>
						<PARTY p2:label="Party1">
							<INDIVIDUAL>
								<NAME>
									<FirstName>' . $inputArray->borrowerName . '</FirstName>
									<LastName>' . $inputArray->borrowerLName . '</LastName>
									<MiddleName/>
									<SuffixName/>
								</NAME>
							</INDIVIDUAL>
							<ROLES>
								<ROLE>
									<BORROWER>
										<BORROWER_DETAIL>
											<BorrowerBirthDate>' . $inputArray->borrowerDOB . '</BorrowerBirthDate>
										</BORROWER_DETAIL>
										<RESIDENCES>
											<RESIDENCE>
												<ADDRESS>
													<AddressLineText>' . $inputArray->presentAddress . '</AddressLineText>
													<CityName>' . $inputArray->presentCity . '</CityName>
													<PostalCode>' . $inputArray->presentZip . '</PostalCode>
													<StateCode>' . $inputArray->presentState . '</StateCode>
												</ADDRESS>
												<RESIDENCE_DETAIL>
													<BorrowerResidencyType>Current</BorrowerResidencyType>
												</RESIDENCE_DETAIL>
											</RESIDENCE>
										</RESIDENCES>
									</BORROWER>
									<ROLE_DETAIL>
										<PartyRoleType>Borrower</PartyRoleType>
									</ROLE_DETAIL>
								</ROLE>
							</ROLES>
							<TAXPAYER_IDENTIFIERS>
								<TAXPAYER_IDENTIFIER>
									<TaxpayerIdentifierType>SocialSecurityNumber</TaxpayerIdentifierType>
									<TaxpayerIdentifierValue>' . $inputArray->ssnNumber . '</TaxpayerIdentifierValue>
								</TAXPAYER_IDENTIFIER>
							</TAXPAYER_IDENTIFIERS>
						</PARTY>
					</PARTIES>
					<RELATIONSHIPS>
						<!-- Link the borrower to the service -->
						<RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE" p2:from="Party1" p2:to="Service1" />
					</RELATIONSHIPS>
					<SERVICES>
						<SERVICE p2:label="Service1">
							<CREDIT>
								<CREDIT_REQUEST>
									<CREDIT_REQUEST_DATAS>
										<CREDIT_REQUEST_DATA>
											<CREDIT_REQUEST_DATA_DETAIL>
												<CreditReportRequestActionType>' . $request_type . '</CreditReportRequestActionType>
											</CREDIT_REQUEST_DATA_DETAIL>
										</CREDIT_REQUEST_DATA>
									</CREDIT_REQUEST_DATAS>
								</CREDIT_REQUEST>
							</CREDIT>
';
        if ($request_type == 'Submit' && $inputArray->sendCCInfo == 1) {
            $postXML .= '
							<SERVICE_PAYMENTS>
								<SERVICE_PAYMENT>
                                    <ADDRESS>
                                        <AddressLineText>' . $inputArray->billingAddress1 . '</AddressLineText>
                                        <CityName>' . $inputArray->billingCity . '</CityName>
                                        <PostalCode>' . $inputArray->billingZip . '</PostalCode>
                                        <StateCode>' . $inputArray->billingState . '</StateCode>
                                    </ADDRESS>
                                    <NAME>
                                        <FirstName>' . $inputArray->cardHolderName . '</FirstName>
                                        <LastName></LastName>
                                        <MiddleName></MiddleName>
                                    </NAME>
                                    <SERVICE_PAYMENT_DETAIL>
                                        <ServicePaymentAccountIdentifier>' . $inputArray->creditCardNumber . '</ServicePaymentAccountIdentifier>
                                        <ServicePaymentCreditAccountExpirationDate>' . $inputArray->expirationYear . '-' . sprintf('%02d', $inputArray->expirationMonth) . '</ServicePaymentCreditAccountExpirationDate>
                                        <ServicePaymentSecondaryCreditAccountIdentifier>' . $inputArray->cardNumberOnBack . '</ServicePaymentSecondaryCreditAccountIdentifier>
                                    </SERVICE_PAYMENT_DETAIL>
								</SERVICE_PAYMENT> 
							  </SERVICE_PAYMENTS>
';
        }
        $postXML .= '
							<SERVICE_PRODUCT>
								<SERVICE_PRODUCT_REQUEST>
									<SERVICE_PRODUCT_DETAIL>
										<ServiceProductDescription>CriminalRecord</ServiceProductDescription>
										<ServiceProductIdentifier>ThisValueWillBeEchoedBack</ServiceProductIdentifier>
										<EXTENSION>
											<OTHER>
												<p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
													<p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
														<p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
															<p3:PreferredResponseFormatType>Pdf</p3:PreferredResponseFormatType>
														</p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
													</p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
												</p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
											</OTHER>
										</EXTENSION>
									</SERVICE_PRODUCT_DETAIL>
								</SERVICE_PRODUCT_REQUEST>
							</SERVICE_PRODUCT>
';
        if ($request_type == 'StatusQuery') {
            $postXML .= '
							<SERVICE_PRODUCT_FULFILLMENT>
								<SERVICE_PRODUCT_FULFILLMENT_DETAIL>                  
									<VendorOrderIdentifier>' . $inputArray->vendorOrderIdentifier . '</VendorOrderIdentifier>
								</SERVICE_PRODUCT_FULFILLMENT_DETAIL>
							</SERVICE_PRODUCT_FULFILLMENT>
';
        }
        $postXML .= '
						</SERVICE>
					</SERVICES>
				</DEAL>
			</DEALS>
		</DEAL_SET>
	</DEAL_SETS>
</MESSAGE>
';
        $xmlResponse = post_xml::getReport($postXML, $inputArray);

        return new thirdPartyServicesResponse([
            'request_xml' => $postXML,
            'response_xml' => $xmlResponse,
            'orderDetails' => $inputArray,
            'option' => $inputArray->option,
        ]);
    }
}