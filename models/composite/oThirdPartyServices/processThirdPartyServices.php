<?php

namespace models\composite\oThirdPartyServices;

use models\constants\gl\glCRAServices;
use models\thirdPartyServicesResponse;
use models\types\strongType;

/**
 *
 */
class processThirdPartyServices extends strongType
{
    /**
     * Process all service based report generation.
     */
    public static function getReport(generate_third_party_docs $inputArray): thirdPartyServicesResponse
    {
        return self::processReport($inputArray);
    }

    public static function processReport($inputArray)
    {
        if (!empty($inputArray->expirationMonth)) {
            $inputArray->expirationMonth = str_pad($inputArray->expirationMonth, 2, 0, STR_PAD_LEFT);
        }

        $res = null;
        switch($inputArray->service) {
            case glCRAServices::BORROWER_CREDIT_REPORT:
            case glCRAServices::CO_BORROWER_CREDIT_REPORT:
            case glCRAServices::JOINT_CREDIT_REPORT:
            case glCRAServices::BORROWER_SOFT_PULL:
            case glCRAServices::CO_BORROWER_SOFT_PULL:
            case glCRAServices::JOINT_SOFT_PULL:
                $res = consumerCreditReportAndRetrieve::getReport($inputArray);
                break;
            case glCRAServices::BORROWER_FRAUD:
            case glCRAServices::CO_BORROWER_FRAUD:
            case glCRAServices::JOINT_FRAUD:
                $res = fraudReport::getReport($inputArray);
                break;
            case glCRAServices::BUSINESS_CREDIT_REPORT:
                $res = businessCreditReportRequestAndRetrieve::getReport($inputArray);
                break;
            case glCRAServices::AVM:
                $res = automatedValuationModelReportAndRetrieve::getReport($inputArray);
                break;
            case glCRAServices::FLOOD:
            case glCRAServices::CO_BORROWER_FLOOD:
            case glCRAServices::JOINT_FLOOD:
                $res = floodZoneDeterminationReportAndRetrieve::getReport($inputArray);
                break;
            case glCRAServices::MERS:
                $res = mortgageElectronicRegistrationSystem_MERS::getReport($inputArray);
                break;
            case glCRAServices::CRIMINAL_RECORD_REPORT:
                $res = criminalRecordReport::getReport($inputArray);
                break;
            case glCRAServices::SOCIAL_SECURITY:
                $res = socialSecurityVerification::getReport($inputArray);
                break;
        }

        saveAndUpdateThirdPartyService::getReport($res);

        return $res;
    }
}