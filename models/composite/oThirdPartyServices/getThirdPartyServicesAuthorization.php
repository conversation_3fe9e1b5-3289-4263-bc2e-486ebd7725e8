<?php

namespace models\composite\oThirdPartyServices;

use models\standard\Strings;
use models\types\strongType;

class getThirdPartyServicesAuthorization extends strongType
{
    public static function getReport($PCID, $serviceName, $userGroup = null, $userNumber = null, $serviceType = 'cra')
    {
        $userGroup = $userGroup ?: ($_SESSION['userGroup'] ?? null);
        $userNumber = $userNumber ?: ($_SESSION['userNumber'] ?? null);
        $serviceKey = $serviceType . '_' . Strings::cleanString($serviceName);

        $authorization = getThirdPartyServicesUserDetails::getReport($userGroup, $userNumber, $serviceKey);
        $authorization = $authorization[$serviceKey];
        if ((empty($authorization['username']) || empty($authorization['password'])) && $userGroup != 'Branch') {
            $authorization = getThirdPartyServicesUserDetails::getReport('Branch', $userNumber, $serviceKey);
            $authorization = $authorization[$serviceKey];
        }
        if (empty($authorization['username']) || empty($authorization['password'])) {
            $authorization = getThirdPartyServicesDetails::getReport($PCID, $serviceKey);
            $authorization = $authorization[$serviceKey];
        }

        return $authorization;
    }
}