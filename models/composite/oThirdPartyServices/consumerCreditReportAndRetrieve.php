<?php

namespace models\composite\oThirdPartyServices;

use models\types\strongType;
use models\constants\gl\glCRAServices;
use models\thirdPartyServicesResponse;

/**
 *
 */
class consumerCreditReportAndRetrieve extends strongType
{
    /**
     * Report : Consumer Credit Report (Bo<PERSON><PERSON>, Co-borrower, Joint).
     * Functionality Includes in : Polling, Retrieving reports.
     * @option is Cron or not.
     */
    public static function getReport(?generate_third_party_docs $inputArray = null): thirdPartyServicesResponse
    {
        $dataArray = $inputArray->toArray();
        if (in_array($inputArray->service, [glCRAServices::CO_BORROWER_CREDIT_REPORT, glCRAServices::CO_BORROWER_SOFT_PULL])) {
            $dataArray['borrowerName'] = $inputArray->coBorrowerFName;
            $dataArray['borrowerLName'] = $inputArray->coBorrowerLName;
            $dataArray['borrowerDOB'] = $inputArray->coBorrowerDOB;
            $dataArray['presentAddress'] = $inputArray->coBPresentAddress;
            $dataArray['presentCity'] = $inputArray->coBPresentCity;
            $dataArray['presentZip'] = $inputArray->coBPresentZip;
            $dataArray['presentState'] = $inputArray->coBPresentState;
            $dataArray['ssnNumber'] = $inputArray->coBSsnNumber;
        }

        $isJoint = in_array($inputArray->service, [glCRAServices::JOINT_CREDIT_REPORT, glCRAServices::JOINT_SOFT_PULL]);
        $request_type = $inputArray->request_type;
        $loanNumber = explode('_', $inputArray->loanIdentifier);
        $loanNumber = end($loanNumber);
        $dataArray['loanIdentifier'] = $loanNumber;
        $dataArray['CRARequestType'] = $isJoint ? 'Joint' : 'Individual';
        $dataArray['expirationYearMonth'] = $inputArray->expirationYear . '-' . sprintf('%02d', $inputArray->expirationMonth);
        $dataArray['currentDateTime'] = gmdate('Y-m-d\TH:i:s\Z');
        $dataArray['CRAReportType'] = 'Merge';
        $dataArray['CRAReportTypeOther'] = '';
        if (in_array($inputArray->service, [glCRAServices::BORROWER_SOFT_PULL, glCRAServices::CO_BORROWER_SOFT_PULL, glCRAServices::JOINT_SOFT_PULL])) {
            $dataArray['CRAReportType'] = 'Other';
            $dataArray['CRAReportTypeOther'] = 'SoftCheck';
        }

        $postXML = '<?xml version="1.0" encoding="utf-8"?>';
        if ($inputArray->cra === 'xactus') {
            $rawXML = file_get_contents(__DIR__ . '/xml/borrowerCreditXactus.xml');
            $XMLArr = generateXML::fetch($rawXML, $dataArray);
            $postXML .= $XMLArr["BEGIN"];
            if ($isJoint) {
                $postXML .= $XMLArr["JOINT"];
            }
        } else {
            $rawXML = file_get_contents(__DIR__ . '/xml/borrowerCreditMeridianLink.xml');
            $XMLArr = generateXML::fetch($rawXML, $dataArray);
            $postXML .= $XMLArr["BEGIN"];
            if ($request_type != 'StatusQuery') {
                $postXML .= $XMLArr["NOT_STATUS_QUERY_1"];
            }
            $postXML .= $XMLArr["PART_1"];
            if ($isJoint) {
                $postXML .= $XMLArr["JOINT"];
            }
            $postXML .= $XMLArr["PART_2"];
            if ($request_type == 'StatusQuery') {
                $postXML .= $XMLArr["STATUS_QUERY_1"];
            } else {
                $postXML .= $XMLArr["STATUS_QUERY_ELSE_1"];
            }
            $postXML .= $XMLArr["PART_3"];
            if ($request_type != 'StatusQuery') {
                $postXML .= $XMLArr["NOT_STATUS_QUERY_2"];
            }
            $postXML .= $XMLArr["PART_4"];
            if ($request_type == 'Submit' && $inputArray->sendCCInfo == 1) {
                $postXML .= $XMLArr["SUBMIT_AND_SEND_CC"];
            }
            $postXML .= $XMLArr["PART_5"];
            if ($request_type == 'StatusQuery') {
                $postXML .= $XMLArr["STATUS_QUERY_2"];
            }
        }
        $postXML .= $XMLArr["END"];
        unset($dataArray);

        $xmlResponse = post_xml::getReport($postXML, $inputArray);

        return new thirdPartyServicesResponse([
            'request_xml' => $postXML,
            'response_xml' => $xmlResponse,
            'orderDetails' => $inputArray,
            'option' => $inputArray->option,
        ]);
    }
}