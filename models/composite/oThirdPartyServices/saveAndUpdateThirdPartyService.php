<?php

namespace models\composite\oThirdPartyServices;

use models\constants\gl\glCRAServices;
use models\Database2;
use models\cypher;
use models\standard\Dates;
use models\standard\Strings;
use models\thirdPartyServicesResponse;
use models\types\strongType;

/**
 *
 */
class saveAndUpdateThirdPartyService extends strongType
{
    /**
     * Insert or update third party service request.
     */
    public static function getReport(thirdPartyServicesResponse $inputArray)
    {
        $type = $inputArray->type ?? 'XML';
        $recordDate = Dates::Timestamp();
        $cra = $inputArray->orderDetails->cra;
        $service = $inputArray->orderDetails->service;
        $request_xml = $inputArray->request_xml;
        $response_xml = $inputArray->response_xml;

        $jsonArray = [];
        if ($type == 'XML') {
            // Removing service product content.
            if (strpos($response_xml, '<SERVICE_PRODUCT>') !== false) {
                $str1 = substr($response_xml, 0, strpos($response_xml, '<SERVICE_PRODUCT>'));
                $str2 = substr($response_xml, strpos($response_xml, '</SERVICE_PRODUCT>') + 18);
                $response_xml = $str1 . $str2;
            }

            $xml = @simplexml_load_string($response_xml);
            $json = json_encode($xml);
            $jsonArray = json_decode($json, true);

            $statusCode = $jsonArray['DEAL_SETS']['DEAL_SET']['DEALS']['DEAL']['SERVICES']['SERVICE']['STATUSES']['STATUS']['StatusCode'] ?? null;
            $statusMess = $jsonArray['DEAL_SETS']['DEAL_SET']['DEALS']['DEAL']['SERVICES']['SERVICE']['STATUSES']['STATUS']['StatusDescription'] ?? null;
            $errCode = $jsonArray['DEAL_SETS']['DEAL_SET_SERVICES']['DEAL_SET_SERVICE']['ERRORS']['ERROR']['ERROR_MESSAGES']['ERROR_MESSAGE']['ErrorMessageCategoryCode'] ?? null;
            $errMess = $jsonArray['DEAL_SETS']['DEAL_SET_SERVICES']['DEAL_SET_SERVICE']['ERRORS']['ERROR']['ERROR_MESSAGES']['ERROR_MESSAGE']['ErrorMessageText'] ?? null;
            $venID = $jsonArray['DEAL_SETS']['DEAL_SET']['DEALS']['DEAL']['SERVICES']['SERVICE']['SERVICE_PRODUCT_FULFILLMENT']['SERVICE_PRODUCT_FULFILLMENT_DETAIL']['VendorOrderIdentifier'] ?? null;
        }

        if ($cra === 'xactus') {
            if (in_array($service, [glCRAServices::BORROWER_FRAUD, glCRAServices::CO_BORROWER_FRAUD, glCRAServices::JOINT_FRAUD])) {
                $jsonArray = json_decode($response_xml, true);
                $statusCode = $jsonArray['status'];
                if (!in_array($statusCode, ['Completed', 'Pending'])) {
                    $errCode = $statusCode;
                    $errMess = $jsonArray['error_description'] ?? $jsonArray['error'];
                } else {
                    $venID = $jsonArray['reportID'] ?? null;
                }
            } elseif (in_array($service, [glCRAServices::FLOOD, glCRAServices::CO_BORROWER_FLOOD, glCRAServices::JOINT_FLOOD, glCRAServices::SOCIAL_SECURITY])) {
                $errCode = $jsonArray['RESPONSE']['STATUS']['@attributes']['_Condition'] ?? null;
                $errMess = $jsonArray['RESPONSE']['STATUS']['@attributes']['_Description'] ?? null;
                if (in_array($errCode, ['success', 'Status', 'Pending'])) {
                    $statusCode = $errCode == 'success' ? 'Completed' : 'Pending';
                    $errCode = '';
                    if ($service === glCRAServices::SOCIAL_SECURITY) {
                        $venID = $jsonArray['RESPONSE']['RESPONSE_DATA']['EXTENSION']['EXTENSION_SECTION']['EXTENSION_SECTION_DATA']['VERIFICATION_RESPONSE']['@attributes']['VendorOrderIdentifier'] ?? null;
                    } else {
                        foreach ($jsonArray['RESPONSE']['KEY'] as $val) {
                            if ($val['@attributes']['_Name'] == 'VendorOrderID') {
                                $venID = $val['@attributes']['_Value'];
                            }
                        }
                    }
                }
            } else {
                $errCode = $jsonArray['DEAL_SETS']['DEAL_SET']['DEALS']['DEAL']['SERVICES']['SERVICE']['STATUSES']['STATUS']['StatusConditionDescription'] ?? $statusCode;
                $errMess = $jsonArray['DEAL_SETS']['DEAL_SET']['DEALS']['DEAL']['SERVICES']['SERVICE']['CREDIT']['CREDIT_RESPONSE']['CREDIT_ERROR_MESSAGES']['CREDIT_ERROR_MESSAGE']['CreditErrorMessageText'] ?? $statusMess;
                $venID = $jsonArray['DOCUMENT_SETS']['DOCUMENT_SET']['DOCUMENTS']['DOCUMENT']['DEAL_SETS']['DEAL_SET']['DEALS']['DEAL']['SERVICES']['SERVICE']['CREDIT']['CREDIT_RESPONSE']['CREDIT_RESPONSE_DETAIL']['CreditReportIdentifier'] ?? null;
                if (empty($errCode)) {
                    $statusCode = 'Completed';
                }
            }
        }

        $errors = '';
        if (!empty($errCode) || empty($statusCode) || $statusCode == "Error") {
            $errMess = $errMess ?? $statusMess;
            $errors = "<span style='color:red;'><b>" . $errCode . '</b><br>' . $errMess . '<span>';
            Strings::SetSess('msg',"Error: " . $errMess);
        }

        $userInfo = $inputArray->orderDetails->firstName
            . ' ' . $inputArray->orderDetails->lastName
            . ' (' . $inputArray->orderDetails->userRole . ')'; // Logged in user info.
        if ($inputArray->option == 'Cron' && $inputArray->orderDetails->tID > 0) {
            $qry = " 
                UPDATE 
                    tblThirdPartyService 
                SET
                    request_type = :request_type ,
                    statusCode = :statusCode ,
                    request_xml = :request_xml ,
                    response_xml = :response_xml ,
                    errors = :errors ,
                    updated_at = :updated_at 
                WHERE 
                    tID = :tID 
			";
            Database2::getInstance()->executeQuery($qry, [
                'request_type' => 'StatusQuery',
                'statusCode' => $statusCode,
                'request_xml' => urlencode($request_xml),
                'response_xml' => urlencode($response_xml),
                'errors' => urlencode($errors),
                'updated_at' => $recordDate,
                'tID' => $inputArray->orderDetails->tID,
            ]);

            if ($statusCode == 'Completed') {
                $inArray = [];
                $result = viewThirdPartyDocs::getReport(cypher::myEncryption($inputArray->orderDetails->tID));
                $inArray['LMRId'] = $result['LMRId'];
                $inArray['displayDocName'] = $result['LoanIdentifier'];
                $inArray['sessUserNumber'] = $result['createdUserId'];
                $inArray['sessUserGroup'] = $result['createdUserGroup'];
                $inArray['docUrl'] = CONST_SITE_URL . 'backoffice/viewThirdPartyDocs.php?tID=' . cypher::myEncryption($inputArray->orderDetails->tID);
                insertIntoDocs::getReport($inArray);
            }

        } else {
            $qry = ' 
                    INSERT INTO tblThirdPartyService(
                                 LMRId
                                 , PCID
                                 , cra
                                 , service
                                 , valuationModel
                                 , paymentThrough
                                 , request_type
                                 , loanIdentifier
                                 , statusCode
                                 , errors
                                 , vendorOrderIdentifier
                                 , request_xml
                                 , response_xml
                                 , notificationEmail
                                 , created_at
             ';
            if (strpos($response_xml, '<EmbeddedContentXML>') !== false|| strpos($response_xml, '<DOCUMENT>') !== false) {
                $qry .= ', updated_at';
            }
            $qry .= ", created_by,createdUserId, createdUserGroup)
			VALUES (
			:LMRId ,
			:PCID ,
			:cra ,
			:service ,
			:valuationModel ,
			:paymentThrough ,
			:request_type ,
			:loanIdentifier ,
			:statusCode ,
			:errors ,
			:vendorOrderIdentifier ,
			:request_xml ,
			:response_xml ,
			:notificationEmail ,
			:created_at 
            ";
            if (strpos($response_xml, '<EmbeddedContentXML>') !== false || strpos($response_xml, '<DOCUMENT>') !== false) {
                $qry .= ", :updated_at ";
            }
            $qry .= ", :created_by , :createdUserId , :createdUserGroup );";
            Database2::getInstance()->executeQuery($qry, [
                'LMRId' => $inputArray->orderDetails->LMRId ?? null
                , 'PCID' =>  $inputArray->orderDetails->FPCID ?? null
                , 'cra' => $cra ?? null
                , 'service' =>  $service ?? null
                , 'valuationModel' =>  $inputArray->orderDetails->valuationModel ?? null
                , 'paymentThrough' =>  ($inputArray->orderDetails->paymentThrough ?? null) ?: '' // can't be null, empty string if not specifying cc info
                , 'request_type' =>  $inputArray->orderDetails->request_type ?? null
                , 'loanIdentifier' =>  $inputArray->orderDetails->loanIdentifier ?? null
                , 'statusCode' => $statusCode
                , 'errors' => urlencode($errors)
                , 'vendorOrderIdentifier' => $venID
                , 'request_xml' => urlencode($request_xml)
                , 'response_xml' => urlencode($response_xml)
                , 'notificationEmail' => $inputArray->orderDetails->notificationEmail
                , 'created_at' => $recordDate
                , 'updated_at' => $recordDate
                , 'created_by' => $userInfo
                , 'createdUserId' => $inputArray->orderDetails->userNumber
                , 'createdUserGroup' => $inputArray->orderDetails->userGroup
            ]);
        }
        return null;
    }
}