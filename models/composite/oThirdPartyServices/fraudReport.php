<?php

namespace models\composite\oThirdPartyServices;

use models\types\strongType;
use models\Curl;
use models\constants\gl\glCRAServices;
use models\thirdPartyServicesResponse;
use models\constants\gl\glThirdPartyServicesCRA;
use models\constants\gl\glCustomJobForProcessingCompany;

class fraudReport extends strongType
{
    public static function getReport(?generate_third_party_docs $inputArray = null): thirdPartyServicesResponse
    {
        $request_type = $inputArray->request_type;
        $loanNumber = explode('_', $inputArray->loanIdentifier);
        $loanNumber = end($loanNumber);
        $PCID = $inputArray->FPCID;
        $borrowerInfo = [];

        if ($request_type == 'Submit') {
            $endURL = "/order/sync";
            if (in_array($inputArray->service, [glCRAServices::BORROWER_FRAUD, glCRAServices::JOINT_FRAUD])) {
                $borrowerInfo[] = [
                    "borrowerFirstName" => $inputArray->borrowerName,
                    "borrowerLastName" => $inputArray->borrowerLName,
                    "borrowerSSN" => $inputArray->ssnNumber,
                    "borrowerDateofBirth" => $inputArray->borrowerDOB,
                    "residentialStreetAddress" => $inputArray->presentAddress,
                    "residentialCityName" => $inputArray->presentCity,
                    "residentialState" => $inputArray->presentState,
                    "residentialZip" => $inputArray->presentZip,
                ];
            }
            if (in_array($inputArray->service, [glCRAServices::CO_BORROWER_FRAUD, glCRAServices::JOINT_FRAUD])) {
                $borrowerInfo[] = [
                    'borrowerFirstName' => $inputArray->coBorrowerFName,
                    "borrowerLastName" => $inputArray->coBorrowerLName,
                    "borrowerSSN" => $inputArray->coBSsnNumber,
                    "borrowerDateofBirth" => $inputArray->coBorrowerDOB,
                    "residentialStreetAddress" => $inputArray->coBPresentAddress,
                    "residentialCityName" => $inputArray->coBPresentCity,
                    "residentialState" => $inputArray->coBPresentState,
                    "residentialZip" => $inputArray->coBPresentZip,
                ];
            }

            $postJson = [
                "loanNumber" => $loanNumber,
                "fraudPlusPackageId" => "Insert Fraud Package ID",
                "pdfReport" => true,
                "borrowersInformation" => $borrowerInfo
            ];
        } else {
            $endURL = "/status";
            $postJson = [
                "reportID" => $inputArray->vendorOrderIdentifier,
                "pdfReport" => true
            ];
        }

        $authorization = getThirdPartyServicesAuthorization::getReport(
            $PCID,
            $inputArray->cra,
            $inputArray->userGroup,
            $inputArray->userNumber
        );
        $token = [
            'username' => $authorization['username'],
            'password' => $authorization['password'],
            'client_id' => $authorization['APIKey'],
            'client_secret' => $authorization['APISecret'],
        ];
        $baseURL = glCustomJobForProcessingCompany::getXactusURL($PCID);
        $token = Curl::PostForm($baseURL . "/api/auth/token", $token);
        $token = json_decode($token->Body, true);
        if ($token['status'] === 'success') {
            $glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
            $token = $token['access_token'];

            $URL = $baseURL . $glThirdPartyServicesCRA[$inputArray->cra]->Services[$inputArray->service]['link'] . $endURL;
            $headers = [
                "Authorization" => "Bearer " . $token
            ];
            $jsonResponse = Curl::PostJSON($URL, $postJson, $headers);
            $jsonResponse = $jsonResponse->Body;
        }

        return new thirdPartyServicesResponse([
            'request_xml' => json_encode($postJson),
            'response_xml' => $jsonResponse ?? json_encode($token),
            'orderDetails' => $inputArray,
            'option' => $inputArray->option,
            'type' => 'JSON'
        ]);
    }

}