<MESSAGE MessageType="Request" xmlns="http://www.mismo.org/residential/2009/schemas"
         xmlns:p2="http://www.w3.org/1999/xlink" xmlns:p3="inetapi/MISMO3_4_MCL_Extension.xsd">
    <ABOUT_VERSIONS>
        <ABOUT_VERSION>
            <DataVersionIdentifier>201703</DataVersionIdentifier>
        </ABOUT_VERSION>
    </ABOUT_VERSIONS>
    <DEAL_SETS>
        <DEAL_SET>
            <DEALS>
                <DEAL>
                    <!-- The property can be placed in either the ASSETS or the COLLATERALS containers. SmartAPI has no preference and will process either scenario the same way  -->
                    <COLLATERALS>
                        <COLLATERAL>
                            |-|BREAKPOINT|-|ORIGINAL_1|-|
                            <SUBJECT_PROPERTY p2:label="Property1">
                            |-|BREAKPOINT|-|ORIGINAL_ELSE_1|-|
                            <SUBJECT_PROPERTY p2:label="Asset1">
                                |-|BREAKPOINT|-|PART_1|-|
                                <ADDRESS>
                                    <AddressLineText>[-]propertyAddress[-]</AddressLineText>
                                    <CityName>[-]propertyCity[-]</CityName>
                                    <PostalCode>[-]propertyZip[-]</PostalCode>
                                    <StateCode>[-]propertyState[-]</StateCode>
                                </ADDRESS>
                            </SUBJECT_PROPERTY>
                        </COLLATERAL>
                    </COLLATERALS>
                    <LOANS>
                        <LOAN>
                            <LOAN_IDENTIFIERS>
                                <LOAN_IDENTIFIER>
                                    <!--LoanIdentifier is not required, though highly recommended and should match what is being used by the company-->
                                    <LoanIdentifier>[-]loanIdentifier[-]</LoanIdentifier>
                                </LOAN_IDENTIFIER>
                            </LOAN_IDENTIFIERS>
                        </LOAN>
                    </LOANS>
                    <PARTIES>
                        <PARTY p2:label="Party1">
                            |-|BREAKPOINT|-|INDIVIDUAL|-|
                            <INDIVIDUAL>
                                <NAME>
                                    <FirstName>[-]borrowerName[-]</FirstName>
                                    <LastName>[-]borrowerLName[-]</LastName>
                                    <MiddleName></MiddleName>
                                    <SuffixName></SuffixName>
                                </NAME>
                            </INDIVIDUAL>
                            <ROLES>
                                <ROLE>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>Borrower</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                            |-|BREAKPOINT|-|BUSINESS|-|
                            <LEGAL_ENTITY>
                                <LEGAL_ENTITY_DETAIL>
                                    <FullName>[-]entityName[-]</FullName>
                                </LEGAL_ENTITY_DETAIL>
                            </LEGAL_ENTITY>
                            <ROLES>
                                <ROLE>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>Other</PartyRoleType>
                                        <PartyRoleTypeAdditionalDescription>Business</PartyRoleTypeAdditionalDescription>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                            |-|BREAKPOINT|-|PART_2|-|
                        </PARTY>
                    </PARTIES>
                    <RELATIONSHIPS>
                        <!--Link the applicant and the property to the service -->
                        <RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE" p2:from="Party1" p2:to="Service1"/>
                        |-|BREAKPOINT|-|ORIGINAL_2|-|
                        <RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PROPERTY_IsVerifiedBy_SERVICE" p2:from="Property1" p2:to="Service1"/>
                    </RELATIONSHIPS>
                    |-|BREAKPOINT|-|ORIGINAL_ELSE_2|-|
                    <RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PROPERTY_IsVerifiedBy_SERVICE" p2:from="Asset1" p2:to="Service1"/>
                    </RELATIONSHIPS>
                |-|BREAKPOINT|-|PART_3|-|
                <SERVICES>
                    <SERVICE p2:label="Service1">
                        <FLOOD>
                            <FLOOD_REQUEST>
                                <FLOOD_REQUEST_DETAIL>
                                    <FloodRequestActionType>[-]request_type[-]</FloodRequestActionType>
                                    |-|BREAKPOINT|-|ORIGINAL_3|-|
                                    <EXTENSION>
                                        <OTHER>
                                            <p3:RequestHMDA>true</p3:RequestHMDA>
                                            <p3:RequestLifeOfLoan>true</p3:RequestLifeOfLoan>
                                        </OTHER>
                                    </EXTENSION>
                                    |-|BREAKPOINT|-|PART_4|-|
                                </FLOOD_REQUEST_DETAIL>
                            </FLOOD_REQUEST>
                        </FLOOD>
                        |-|BREAKPOINT|-|ORIGINAL_AND_SEND_CC|-|
                        <SERVICE_PAYMENTS>
                            <SERVICE_PAYMENT>
                                <ADDRESS>
                                    <AddressLineText>[-]billingAddress1[-]</AddressLineText>
                                    <CityName>[-]billingCity[-]</CityName>
                                    <PostalCode>[-]billingZip[-]</PostalCode>
                                    <StateCode>[-]billingState[-]</StateCode>
                                </ADDRESS>
                                <NAME>
                                    <FirstName>[-]cardHolderName[-]</FirstName>
                                    <LastName></LastName>
                                    <MiddleName></MiddleName>
                                </NAME>
                                <SERVICE_PAYMENT_DETAIL>
                                    <ServicePaymentAccountIdentifier>[-]creditCardNumber[-]</ServicePaymentAccountIdentifier>
                                    <ServicePaymentCreditAccountExpirationDate>[-]expirationYear[-]-[-]expirationMonth[-]</ServicePaymentCreditAccountExpirationDate>
                                    <ServicePaymentSecondaryCreditAccountIdentifier>[-]cardNumberOnBack[-]</ServicePaymentSecondaryCreditAccountIdentifier>
                                </SERVICE_PAYMENT_DETAIL>
                            </SERVICE_PAYMENT>
                        </SERVICE_PAYMENTS>
                        |-|BREAKPOINT|-|PART_5|-|
                        <SERVICE_PRODUCT>
                            <SERVICE_PRODUCT_REQUEST>
                                <SERVICE_PRODUCT_DETAIL>
                                    <ServiceProductDescription>Flood</ServiceProductDescription>
                                    <ServiceProductIdentifier>[-]loanIdentifier[-]</ServiceProductIdentifier>
                                    <EXTENSION>
                                        <OTHER>
                                            <p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
                                                <p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
                                                    <p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
                                                        <p3:PreferredResponseFormatType>Pdf</p3:PreferredResponseFormatType>
                                                    </p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
                                                </p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
                                            </p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
                                        </OTHER>
                                    </EXTENSION>
                                </SERVICE_PRODUCT_DETAIL>
                            </SERVICE_PRODUCT_REQUEST>
                        </SERVICE_PRODUCT>
                        <SERVICE_PRODUCT_FULFILLMENT>
                            <SERVICE_PRODUCT_FULFILLMENT_DETAIL>
                                |-|BREAKPOINT|-|ORIGINAL_4|-|
                                <EXTENSION>
                                    <OTHER>
                                        <!--Setting rush to \'true\' may increase final fee for the order -->
                                        <p3:Rush>false</p3:Rush>
                                    </OTHER>
                                </EXTENSION>
                                |-|BREAKPOINT|-|ORIGINAL_ELSE_4|-|
                                <VendorOrderIdentifier>[-]vendorOrderIdentifier[-]</VendorOrderIdentifier>
                                |-|BREAKPOINT|-|END|-|
                            </SERVICE_PRODUCT_FULFILLMENT_DETAIL>
                        </SERVICE_PRODUCT_FULFILLMENT>
                    </SERVICE>
                </SERVICES>
            </DEAL>
        </DEALS>
        </DEAL_SET>
    </DEAL_SETS>
</MESSAGE>