<MESSAGE MISMOReferenceModelIdentifier="3.4" xmlns="http://www.mismo.org/residential/2009/schemas"
         xmlns:xlink="http://www.w3.org/1999/xlink">
    <ABOUT_VERSIONS>
        <ABOUT_VERSION>
            <DataVersionIdentifier IdentifierOwnerURI="http://www.AcmeLOSSoftware.com">V6.2.(2014-05)
            </DataVersionIdentifier>
        </ABOUT_VERSION>
    </ABOUT_VERSIONS>
    <DEAL_SETS>
        <DEAL_SET>
            <DEALS>
                <DEAL>
                    <LOANS>
                        <LOAN LoanRoleType="SubjectLoan">
                            <LOAN_IDENTIFIERS>
                                <LOAN_IDENTIFIER>
                                    <LoanIdentifier>[-]loanIdentifier[-]</LoanIdentifier>
                                </LOAN_IDENTIFIER>
                            </LOAN_IDENTIFIERS>
                        </LOAN>
                    </LOANS>
                    <!-- The Borrower Parties -->
                    <PARTIES>
                        <PARTY SequenceNumber="1">
                            <INDIVIDUAL>
                                <NAME>
                                    <FirstName>[-]borrowerName[-]</FirstName>
                                    <FullName>[-]borrowerName[-] [-]borrowerLName[-]</FullName>
                                    <LastName>[-]borrowerLName[-]</LastName>
                                    <MiddleName></MiddleName>
                                </NAME>
                            </INDIVIDUAL>
                            <ROLES>
                                <ROLE xlink:label="Borrower01">
                                    <BORROWER>
                                        <BORROWER_DETAIL>
                                            <BorrowerBirthDate>[-]borrowerDOB[-]</BorrowerBirthDate>
                                        </BORROWER_DETAIL>
                                        <RESIDENCES>
                                            <RESIDENCE SequenceNumber="1">
                                                <ADDRESS>
                                                    <AddressLineText>[-]presentAddress[-]</AddressLineText>
                                                    <CityName>[-]presentCity[-]</CityName>
                                                    <PostalCode>[-]presentZip[-]</PostalCode>
                                                    <StateCode>[-]presentState[-]</StateCode>
                                                </ADDRESS>
                                                <RESIDENCE_DETAIL>
                                                    <BorrowerResidencyType>Current</BorrowerResidencyType>
                                                </RESIDENCE_DETAIL>
                                            </RESIDENCE>
                                        </RESIDENCES>
                                    </BORROWER>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>Borrower</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                            <TAXPAYER_IDENTIFIERS>
                                <TAXPAYER_IDENTIFIER>
                                    <TaxpayerIdentifierType>SocialSecurityNumber</TaxpayerIdentifierType>
                                    <TaxpayerIdentifierValue>[-]ssnNumber[-]</TaxpayerIdentifierValue>
                                </TAXPAYER_IDENTIFIER>
                            </TAXPAYER_IDENTIFIERS>
                        </PARTY>
                        |-|BREAKPOINT|-|JOINT|-|
                        <PARTY SequenceNumber="2">
                            <INDIVIDUAL>
                                <NAME>
                                    <FirstName>[-]coBorrowerFName[-]</FirstName>
                                    <FullName>[-]coBorrowerFName[-] [-]coBorrowerLName[-]</FullName>
                                    <LastName>[-]coBorrowerLName[-]</LastName>
                                    <MiddleName></MiddleName>
                                </NAME>
                            </INDIVIDUAL>
                            <ROLES>
                                <ROLE xlink:label="Borrower02">
                                    <BORROWER>
                                        <BORROWER_DETAIL>
                                            <BorrowerBirthDate>[-]coBorrowerDOB[-]</BorrowerBirthDate>
                                        </BORROWER_DETAIL>
                                        <RESIDENCES>
                                            <RESIDENCE SequenceNumber="1">
                                                <ADDRESS>
                                                    <AddressLineText>[-]coBPresentAddress[-]</AddressLineText>
                                                    <CityName>[-]coBPresentCity[-]</CityName>
                                                    <PostalCode>[-]coBPresentZip[-]</PostalCode>
                                                    <StateCode>[-]coBPresentState[-]</StateCode>
                                                </ADDRESS>
                                                <RESIDENCE_DETAIL>
                                                    <BorrowerResidencyType>Current</BorrowerResidencyType>
                                                </RESIDENCE_DETAIL>
                                            </RESIDENCE>
                                        </RESIDENCES>
                                    </BORROWER>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>Borrower</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                            <TAXPAYER_IDENTIFIERS>
                                <TAXPAYER_IDENTIFIER>
                                    <TaxpayerIdentifierType>SocialSecurityNumber</TaxpayerIdentifierType>
                                    <TaxpayerIdentifierValue>[-]coBSsnNumber[-]</TaxpayerIdentifierValue>
                                </TAXPAYER_IDENTIFIER>
                            </TAXPAYER_IDENTIFIERS>
                        </PARTY>
                        |-|BREAKPOINT|-|END|-|
                    </PARTIES>
                    <RELATIONSHIPS>
                        <RELATIONSHIP xlink:from="CreditRequestData001" xlink:to="Borrower01"
                                      xlink:arcrole="urn:fdc:mismo.org:2009:residential/CREDIT_REQUEST_DATA_IsAssociatedWith_ROLE"/>
                        <RELATIONSHIP xlink:from="CreditRequestData001" xlink:to="Borrower02"
                                      xlink:arcrole="urn:fdc:mismo.org:2009:residential/CREDIT_REQUEST_DATA_IsAssociatedWith_ROLE"/>
                    </RELATIONSHIPS>
                    <SERVICES>
                        <SERVICE>
                            <CREDIT>
                                <CREDIT_REQUEST>
                                    <CREDIT_REQUEST_DATAS>
                                        <CREDIT_REQUEST_DATA xlink:label="CreditRequestData001">
                                            <CREDIT_REPOSITORY_INCLUDED>
                                                <CreditRepositoryIncludedEquifaxIndicator>true</CreditRepositoryIncludedEquifaxIndicator>
                                                <CreditRepositoryIncludedExperianIndicator>true</CreditRepositoryIncludedExperianIndicator>
                                                <CreditRepositoryIncludedTransUnionIndicator>true</CreditRepositoryIncludedTransUnionIndicator>
                                            </CREDIT_REPOSITORY_INCLUDED>
                                            <CREDIT_REQUEST_DATA_DETAIL>
                                                <CreditReportIdentifier>[-]loanIdentifier[-]</CreditReportIdentifier>
                                                <CreditReportRequestActionType>Submit</CreditReportRequestActionType>
                                                <CreditReportType>[-]CRAReportType[-]</CreditReportType>
                                                <CreditReportTypeOtherDescription>[-]CRAReportTypeOther[-]</CreditReportTypeOtherDescription>
                                                <CreditRequestDatetime>[-]currentDateTime[-]</CreditRequestDatetime>
                                                <CreditRequestType>[-]CRARequestType[-]</CreditRequestType>
                                            </CREDIT_REQUEST_DATA_DETAIL>
                                        </CREDIT_REQUEST_DATA>
                                    </CREDIT_REQUEST_DATAS>
                                </CREDIT_REQUEST>
                            </CREDIT>
                        </SERVICE>
                    </SERVICES>
                </DEAL>
            </DEALS>
        </DEAL_SET>
    </DEAL_SETS>
</MESSAGE>