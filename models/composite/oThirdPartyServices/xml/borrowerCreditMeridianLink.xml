<MESSAGE xmlns="http://www.mismo.org/residential/2009/schemas" xmlns:xlink="http://www.w3.org/1999/xlink"
         xmlns:p3="inetapi/MISMO3_4_MCL_Extension.xsd" MessageType="Request">
    <ABOUT_VERSIONS>
        <ABOUT_VERSION>
            <DataVersionIdentifier>201703</DataVersionIdentifier>
        </ABOUT_VERSION>
    </ABOUT_VERSIONS>
    <DEAL_SETS>
        <DEAL_SET>
            <DEALS>
                <DEAL>
                    <LOANS>
                        <LOAN>
                            <LOAN_IDENTIFIERS>
                                <LOAN_IDENTIFIER>
                                    <LoanIdentifier>[-]loanIdentifier[-]</LoanIdentifier>
                                </LOAN_IDENTIFIER>
                            </LOAN_IDENTIFIERS>
                            <TERMS_OF_LOAN>
                                <LoanPurposeType>Other</LoanPurposeType>
                                <LoanPurposeTypeOtherDescription>Auto</LoanPurposeTypeOtherDescription>
                            </TERMS_OF_LOAN>
                        </LOAN>
                    </LOANS>
                    <PARTIES>
                        <PARTY SequenceNumber="1" xlink:label="Party1">
                            <INDIVIDUAL>
                                |-|BREAKPOINT|-|NOT_STATUS_QUERY_1|-|
                                <CONTACT_POINTS>
                                    <CONTACT_POINT>
                                        <CONTACT_POINT_TELEPHONE>
                                            <ContactPointTelephoneExtensionValue></ContactPointTelephoneExtensionValue>
                                            <ContactPointTelephoneValue>[-]phoneNumber[-]</ContactPointTelephoneValue>
                                        </CONTACT_POINT_TELEPHONE>
                                        <CONTACT_POINT_DETAIL>
                                            <ContactPointRoleType>Home</ContactPointRoleType>
                                        </CONTACT_POINT_DETAIL>
                                    </CONTACT_POINT>
                                </CONTACT_POINTS>
                                |-|BREAKPOINT|-|PART_1|-|
                                <NAME>
                                    <FirstName>[-]borrowerName[-]</FirstName>
                                    <LastName>[-]borrowerLName[-]</LastName>
                                    <MiddleName></MiddleName>
                                    <SuffixName></SuffixName>
                                </NAME>
                            </INDIVIDUAL>
                            <ROLES>
                                <ROLE>
                                    <BORROWER>
                                        <BORROWER_DETAIL>
                                            <BorrowerBirthDate>[-]borrowerDOB[-]</BorrowerBirthDate>
                                        </BORROWER_DETAIL>
                                        <RESIDENCES>
                                            <RESIDENCE>
                                                <ADDRESS>
                                                    <AddressLineText>[-]presentAddress[-]</AddressLineText>
                                                    <CityName>[-]presentCity[-]</CityName>
                                                    <PostalCode>[-]presentZip[-]</PostalCode>
                                                    <StateCode>[-]presentState[-]</StateCode>
                                                </ADDRESS>
                                                <RESIDENCE_DETAIL>
                                                    <BorrowerResidencyType>Current</BorrowerResidencyType>
                                                </RESIDENCE_DETAIL>
                                            </RESIDENCE>
                                        </RESIDENCES>
                                    </BORROWER>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>Borrower</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                            <TAXPAYER_IDENTIFIERS>
                                <TAXPAYER_IDENTIFIER>
                                    <TaxpayerIdentifierType>SocialSecurityNumber</TaxpayerIdentifierType>
                                    <TaxpayerIdentifierValue>[-]ssnNumber[-]</TaxpayerIdentifierValue>
                                </TAXPAYER_IDENTIFIER>
                            </TAXPAYER_IDENTIFIERS>
                        </PARTY>
                        |-|BREAKPOINT|-|JOINT|-|
                        <PARTY SequenceNumber="2" xlink:label="Party2">
                            <INDIVIDUAL>
                                <NAME>
                                    <FirstName>[-]coBorrowerFName[-]</FirstName>
                                    <LastName>[-]coBorrowerLName[-]</LastName>
                                    <MiddleName></MiddleName>
                                    <SuffixName></SuffixName>
                                </NAME>
                            </INDIVIDUAL>
                            <ROLES>
                                <ROLE>
                                    <BORROWER>
                                        <BORROWER_DETAIL>
                                            <BorrowerBirthDate>[-]coBorrowerDOB[-]</BorrowerBirthDate>
                                        </BORROWER_DETAIL>
                                        <RESIDENCES>
                                            <RESIDENCE>
                                                <ADDRESS>
                                                    <AddressLineText>[-]coBPresentAddress[-]</AddressLineText>
                                                    <CityName>[-]coBPresentCity[-]</CityName>
                                                    <PostalCode>[-]coBPresentZip[-]</PostalCode>
                                                    <StateCode>[-]coBPresentState[-]</StateCode>
                                                </ADDRESS>
                                                <RESIDENCE_DETAIL>
                                                    <BorrowerResidencyType>Current</BorrowerResidencyType>
                                                </RESIDENCE_DETAIL>
                                            </RESIDENCE>
                                        </RESIDENCES>
                                    </BORROWER>
                                    <ROLE_DETAIL>
                                        <PartyRoleType>Borrower</PartyRoleType>
                                    </ROLE_DETAIL>
                                </ROLE>
                            </ROLES>
                            <TAXPAYER_IDENTIFIERS>
                                <TAXPAYER_IDENTIFIER>
                                    <TaxpayerIdentifierType>SocialSecurityNumber</TaxpayerIdentifierType>
                                    <TaxpayerIdentifierValue>[-]coBSsnNumber[-]</TaxpayerIdentifierValue>
                                </TAXPAYER_IDENTIFIER>
                            </TAXPAYER_IDENTIFIERS>
                        </PARTY>
                        |-|BREAKPOINT|-|PART_2|-|
                    </PARTIES>
                    <RELATIONSHIPS>
                        |-|BREAKPOINT|-|STATUS_QUERY_1|-|
                        <!-- Link the Party (the borrower) to the Service (credit order) -->
                        <RELATIONSHIP xlink:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE"
                                      xlink:from="Party1" xlink:to="Service1"/>
                        <RELATIONSHIP xlink:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE"
                                      xlink:from="Party2" xlink:to="Service1"/>
                        |-|BREAKPOINT|-|STATUS_QUERY_ELSE_1|-|
                        <!-- Link borrower to the service -->
                        <RELATIONSHIP xlink:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE"
                                      xlink:from="Party1" xlink:to="Service1"/>
                        <RELATIONSHIP xlink:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE"
                                      xlink:from="Party2" xlink:to="Service1"/>
                        <!-- If a subject property is being provided, link the property to the service. Though optional, the subject property is often used with credit report add-on products -->
                        <RELATIONSHIP
                                xlink:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PROPERTY_IsVerifiedBy_SERVICE"
                                xlink:from="Property1" xlink:to="Service1"/>
                        |-|BREAKPOINT|-|PART_3|-|
                    </RELATIONSHIPS>
                    <SERVICES>
                        <SERVICE xlink:label="Service1">
                            <CREDIT>
                                <CREDIT_REQUEST>
                                    <CREDIT_REQUEST_DATAS>
                                        <CREDIT_REQUEST_DATA>
                                            |-|BREAKPOINT|-|NOT_STATUS_QUERY_2|-|
                                            <CREDIT_REPOSITORY_INCLUDED>
                                                <CreditRepositoryIncludedEquifaxIndicator>true</CreditRepositoryIncludedEquifaxIndicator>
                                                <CreditRepositoryIncludedExperianIndicator>true</CreditRepositoryIncludedExperianIndicator>
                                                <CreditRepositoryIncludedTransUnionIndicator>true</CreditRepositoryIncludedTransUnionIndicator>
                                                <EXTENSION>
                                                    <OTHER>
                                                        <p3:RequestEquifaxScore>true</p3:RequestEquifaxScore>
                                                        <p3:RequestExperianFraud>true</p3:RequestExperianFraud>
                                                        <p3:RequestExperianScore>true</p3:RequestExperianScore>
                                                        <p3:RequestTransUnionFraud>true</p3:RequestTransUnionFraud>
                                                        <p3:RequestTransUnionScore>true</p3:RequestTransUnionScore>
                                                    </OTHER>
                                                </EXTENSION>
                                            </CREDIT_REPOSITORY_INCLUDED>
                                            |-|BREAKPOINT|-|PART_4|-|
                                            <CREDIT_REQUEST_DATA_DETAIL>
                                                <CreditReportRequestActionType>[-]request_type[-]</CreditReportRequestActionType>
                                            </CREDIT_REQUEST_DATA_DETAIL>
                                        </CREDIT_REQUEST_DATA>
                                    </CREDIT_REQUEST_DATAS>
                                </CREDIT_REQUEST>
                            </CREDIT>
                            |-|BREAKPOINT|-|SUBMIT_AND_SEND_CC|-|
                            <SERVICE_PAYMENTS>
                                <SERVICE_PAYMENT>
                                    <ADDRESS>
                                        <AddressLineText>[-]billingAddress1[-]</AddressLineText>
                                        <CityName>[-]billingCity[-]</CityName>
                                        <PostalCode>[-]billingZip[-]</PostalCode>
                                        <StateCode>[-]billingState[-]</StateCode>
                                    </ADDRESS>
                                    <NAME>
                                        <FirstName>[-]cardHolderName[-]</FirstName>
                                        <LastName></LastName>
                                        <MiddleName></MiddleName>
                                    </NAME>
                                    <SERVICE_PAYMENT_DETAIL>
                                        <ServicePaymentAccountIdentifier>[-]creditCardNumber[-]</ServicePaymentAccountIdentifier>
                                        <ServicePaymentCreditAccountExpirationDate>[-]expirationYearMonth[-]</ServicePaymentCreditAccountExpirationDate>
                                        <ServicePaymentSecondaryCreditAccountIdentifier>[-]cardNumberOnBack[-]</ServicePaymentSecondaryCreditAccountIdentifier>
                                    </SERVICE_PAYMENT_DETAIL>
                                </SERVICE_PAYMENT>
                            </SERVICE_PAYMENTS>
                            |-|BREAKPOINT|-|PART_5|-|
                            <SERVICE_PRODUCT>
                                <SERVICE_PRODUCT_REQUEST>
                                    <SERVICE_PRODUCT_DETAIL>
                                        <ServiceProductDescription>CreditOrder</ServiceProductDescription>
                                        <ServiceProductIdentifier>[-]loanIdentifier[-]</ServiceProductIdentifier>
                                        <EXTENSION>
                                            <OTHER>
                                                <!-- Recommend requesting only the formats you need, to minimize processing time -->
                                                <p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
                                                    <p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
                                                        <p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
                                                            <p3:PreferredResponseFormatType>Pdf</p3:PreferredResponseFormatType>
                                                        </p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
                                                    </p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
                                                </p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
                                            </OTHER>
                                        </EXTENSION>
                                    </SERVICE_PRODUCT_DETAIL>
                                </SERVICE_PRODUCT_REQUEST>
                            </SERVICE_PRODUCT>
                            |-|BREAKPOINT|-|STATUS_QUERY_2|-|
                            <SERVICE_PRODUCT_FULFILLMENT>
                                <SERVICE_PRODUCT_FULFILLMENT_DETAIL>
                                    <VendorOrderIdentifier>[-]vendorOrderIdentifier[-]</VendorOrderIdentifier>
                                </SERVICE_PRODUCT_FULFILLMENT_DETAIL>
                            </SERVICE_PRODUCT_FULFILLMENT>
                            |-|BREAKPOINT|-|END|-|
                        </SERVICE>
                    </SERVICES>
                </DEAL>
            </DEALS>
        </DEAL_SET>
    </DEAL_SETS>
</MESSAGE>