<REQUEST_GROUP MISMOVersionID="2.4">
    <REQUESTING_PARTY _Name="[-]requestingParty[-]"/>
    <SUBMITTING_PARTY _Name="LendingWise"/>
    <REQUEST LoginAccountIdentifier="[-]loginUsername[-]" LoginAccountPassword="[-]loginPassword[-]">
        <REQUEST_DATA>
            <EXTENSION>
                <EXTENSION_SECTION>
                    <EXTENSION_SECTION_DATA>
                        |-|BREAKPOINT|-|ORIGINAL|-|
                        <VERIFICATION_REQUEST _ActionType="[-]request_type[-]" LenderCaseIdentifier="[-]loanIdentifier[-]">
                            <BORROWER BorrowerID="Borrower" _FirstName="[-]borrowerName[-]" _MiddleName="" _LastName="[-]borrowerLName[-]"
                                      _NameSuffix="" _SSN="[-]ssnNumber[-]" _BirthDate="[-]borrowerDOB[-]"/>
                            <EMBEDDED_FILE _Type="PDF" _EncodingType="Base64">
                                <DOCUMENT>[-]SSA89Document[-]</DOCUMENT>
                            </EMBEDDED_FILE>
                        </VERIFICATION_REQUEST>
                        |-|BREAKPOINT|-|ORIGINAL_ELSE|-|
                        <VERIFICATION_REQUEST VendorOrderIdentifier="[-]vendorOrderIdentifier[-]" _ActionType="[-]request_type[-]"></VERIFICATION_REQUEST>
                        |-|BREAKPOINT|-|PART_1|-|
                    </EXTENSION_SECTION_DATA>
                </EXTENSION_SECTION>
            </EXTENSION>
            |-|BREAKPOINT|-|ORIGINAL_2|-|
            <LOAN>
                <_APPLICATION>
                    <BORROWER _PrintPositionType="Borrower">
                        <_RESIDENCE _StreetAddress="[-]propertyAddress[-]" _City="[-]propertyCity[-]" _State="[-]propertyState[-]"
                                    _PostalCode="[-]propertyZip[-]" BorrowerResidencyType="Current"/>
                    </BORROWER>
                </_APPLICATION>
            </LOAN>
            |-|BREAKPOINT|-|END|-|
        </REQUEST_DATA>
    </REQUEST>
</REQUEST_GROUP>