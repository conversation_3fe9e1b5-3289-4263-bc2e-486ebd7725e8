<?php

namespace models\composite\oThirdPartyServices;

use models\thirdPartyServicesResponse;
use models\types\strongType;

/**
 *
 */
class automatedValuationModelReportAndRetrieve extends strongType
{
    /**
     * Report : Automated Valuation Model (AVM).
     * Functionality Includes in : Polling, Retrieving reports.
     * @option is Cron or not.
     */
    public static function getReport(?generate_third_party_docs $inputArray = null): thirdPartyServicesResponse
    {
        $inputArray->creditCardNumber = str_replace(' ', '', $inputArray->creditCardNumber);
        $inputArray->cardNumberOnBack = str_replace(' ', '', $inputArray->cardNumberOnBack);
        $inputArray->cardHolderName = str_replace('.', '', $inputArray->cardHolderName);
        $request_type = $inputArray->request_type;
        $loanNumber = explode('_', $inputArray->loanIdentifier);
        $loanNumber = end($loanNumber);

        if ($request_type == 'StatusQuery') {
            $request_type = 'Get';
        } else {
            $request_type = 'Order';
        }

        $postXML = '<?xml version="1.0" encoding="utf-8"?>
<MESSAGE MessageType="Request" xmlns="http://www.mismo.org/residential/2009/schemas" xmlns:p2="http://www.w3.org/1999/xlink" xmlns:p3="inetapi/MISMO3_4_MCL_Extension.xsd">
	<ABOUT_VERSIONS>
		<ABOUT_VERSION>
			<DataVersionIdentifier>201703</DataVersionIdentifier>
		</ABOUT_VERSION>
	</ABOUT_VERSIONS>
	<DEAL_SETS>
		<DEAL_SET>
			<DEALS>
				<DEAL>';
        if ($request_type == 'Order') {
            $postXML .= '<COLLATERALS>
						<COLLATERAL>
							<SUBJECT_PROPERTY p2:label="Property1">
								<ADDRESS>
									<AddressLineText>' . $inputArray->propertyAddress . '</AddressLineText>
									<CityName>' . $inputArray->propertyCity . '</CityName>
									<PostalCode>' . $inputArray->propertyZip . '</PostalCode>
									<StateCode>' . $inputArray->propertyState . '</StateCode>
								</ADDRESS>
									<NEIGHBORHOOD>
										<PRESENT_LAND_USES>
											<PRESENT_LAND_USE>
												<NeighborhoodLandUseType>SingleFamily</NeighborhoodLandUseType>
											</PRESENT_LAND_USE>
										</PRESENT_LAND_USES>
									</NEIGHBORHOOD>
									<PROPERTY_DETAIL>
										<PropertyEstimatedValueAmount>' . $inputArray->homeValue . '</PropertyEstimatedValueAmount>
									</PROPERTY_DETAIL>
							</SUBJECT_PROPERTY>
						</COLLATERAL>
					</COLLATERALS>
';
        }
        $postXML .= '
					<LOANS>
						<LOAN>
							<LOAN_IDENTIFIERS>
								<LOAN_IDENTIFIER>
									<LoanIdentifier>' . $loanNumber . '</LoanIdentifier>
								</LOAN_IDENTIFIER>
							</LOAN_IDENTIFIERS>
						</LOAN>
					</LOANS>
					<PARTIES>
						<PARTY p2:label="Party1">
							<INDIVIDUAL>
								<CONTACT_POINTS>
									<CONTACT_POINT>
										<CONTACT_POINT_TELEPHONE>
											<ContactPointTelephoneExtensionValue></ContactPointTelephoneExtensionValue>
											<ContactPointTelephoneValue>' . $inputArray->phoneNumber . '</ContactPointTelephoneValue>
										</CONTACT_POINT_TELEPHONE>
										<CONTACT_POINT_DETAIL>
											<ContactPointRoleType>Home</ContactPointRoleType>
										</CONTACT_POINT_DETAIL>
									</CONTACT_POINT>
								</CONTACT_POINTS>
							</INDIVIDUAL>
							<ROLES>
								<ROLE>
									<ROLE_DETAIL>
										<PartyRoleType>Borrower</PartyRoleType>
									</ROLE_DETAIL>
								</ROLE>
							</ROLES>
						</PARTY> 
					</PARTIES>
					<RELATIONSHIPS>
						<!-- Link the borrower to the service. Link the property to the service -->
						<RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PARTY_IsVerifiedBy_SERVICE" p2:from="Party1" p2:to="Service1" />
';
        if ($request_type == 'Order') {
            $postXML .= '
						<RELATIONSHIP p2:arcrole="urn:fdc:Meridianlink.com:2017:mortgage/PROPERTY_IsVerifiedBy_SERVICE" p2:from="Property1" p2:to="Service1" />
';
        }
        $postXML .= '
					</RELATIONSHIPS>
					<SERVICES>
						<SERVICE p2:label="Service1">
							<VALUATION>
								<VALUATION_REQUEST>
									<VALUATION_REQUEST_DETAIL>
										<ValuationRequestActionType>Other</ValuationRequestActionType>
										<ValuationRequestActionTypeOtherDescription>' . $request_type . '</ValuationRequestActionTypeOtherDescription>
';
        if ($request_type == 'Order') {
            $GEOAVM = $HVE = $IVAL = $PASS = 'false';
            if ($inputArray->valuationModel != '') ${$inputArray->valuationModel} = 'true';
            $postXML .= '
										<EXTENSION>
											<OTHER>
												<!-- Concurrent searching allows you to set multiple flags to true. See integration guide for more details. -->
												<p3:RequestGEOAVM>' . $GEOAVM . '</p3:RequestGEOAVM>
												<p3:RequestHVE>' . $HVE . '</p3:RequestHVE>
												<p3:RequestIVAL>' . $IVAL . '</p3:RequestIVAL>
												<p3:RequestPASS>' . $PASS . '</p3:RequestPASS>
												<p3:RequestStopOnFirstHit>false</p3:RequestStopOnFirstHit>
											</OTHER>
										</EXTENSION>
';
        }
        $postXML .= '
									</VALUATION_REQUEST_DETAIL>
								</VALUATION_REQUEST>
							</VALUATION>
';
        if ($request_type == 'Order' && $inputArray->sendCCInfo == 1) {
            $postXML .= '
							<SERVICE_PAYMENTS>
								<SERVICE_PAYMENT>
                                    <ADDRESS>
                                        <AddressLineText>' . $inputArray->billingAddress1 . '</AddressLineText>
                                        <CityName>' . $inputArray->billingCity . '</CityName>
                                        <PostalCode>' . $inputArray->billingZip . '</PostalCode>
                                        <StateCode>' . $inputArray->billingState . '</StateCode>
                                    </ADDRESS>
                                    <NAME>
                                        <FirstName>' . $inputArray->cardHolderName . '</FirstName>
                                        <LastName></LastName>
                                        <MiddleName></MiddleName>
                                    </NAME>
                                    <SERVICE_PAYMENT_DETAIL>
                                        <ServicePaymentAccountIdentifier>' . $inputArray->creditCardNumber . '</ServicePaymentAccountIdentifier>
                                        <ServicePaymentCreditAccountExpirationDate>' . $inputArray->expirationYear . '-' . $inputArray->expirationMonth . '</ServicePaymentCreditAccountExpirationDate>
                                        <ServicePaymentSecondaryCreditAccountIdentifier>' . $inputArray->cardNumberOnBack . '</ServicePaymentSecondaryCreditAccountIdentifier>
                                    </SERVICE_PAYMENT_DETAIL>
								</SERVICE_PAYMENT>
							</SERVICE_PAYMENTS>';
        }
        $postXML .= '
							<SERVICE_PRODUCT>
								<SERVICE_PRODUCT_REQUEST>
									<SERVICE_PRODUCT_DETAIL>
										<ServiceProductDescription>AVM</ServiceProductDescription>
										<ServiceProductIdentifier>' . $loanNumber . '</ServiceProductIdentifier>
										<EXTENSION>
											<OTHER>
												<p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
													<p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
														<p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
															<p3:PreferredResponseFormatType>Pdf</p3:PreferredResponseFormatType>
														</p3:SERVICE_PREFERRED_RESPONSE_FORMAT_DETAIL>
													</p3:SERVICE_PREFERRED_RESPONSE_FORMAT>
												</p3:SERVICE_PREFERRED_RESPONSE_FORMATS>
											</OTHER>
										</EXTENSION>
									</SERVICE_PRODUCT_DETAIL>
								</SERVICE_PRODUCT_REQUEST>
							</SERVICE_PRODUCT>
							<SERVICE_PRODUCT_FULFILLMENT>';
        if ($request_type != 'Order') {
            $postXML .= '
								<SERVICE_PRODUCT_FULFILLMENT_DETAIL>
									<VendorOrderIdentifier>' . $inputArray->vendorOrderIdentifier . '</VendorOrderIdentifier>
								</SERVICE_PRODUCT_FULFILLMENT_DETAIL>';
        }
        $postXML .= '
							</SERVICE_PRODUCT_FULFILLMENT>
						</SERVICE>
					</SERVICES>
				</DEAL>
			</DEALS>
		</DEAL_SET>
	</DEAL_SETS>
</MESSAGE>';

        $xmlResponse = post_xml::getReport($postXML, $inputArray);

        /**
         * Save or update third party requests
         */
        return new thirdPartyServicesResponse([
            'request_xml' => $postXML,
            'response_xml' => $xmlResponse,
            'orderDetails' => $inputArray,
            'option' => $inputArray->option,
        ]);
    }
}