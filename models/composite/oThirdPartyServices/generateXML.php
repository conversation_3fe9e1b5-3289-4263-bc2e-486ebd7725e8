<?php

namespace models\composite\oThirdPartyServices;

use models\types\strongType;

class generateXML extends strongType
{
    private static string $breakPointDelimiter = '|-|';
    private static string $elementDelimiter = '[-]';

    public static function fetch($xml, $data, $replace = true): array
    {
        if ($replace) {
            $xml = self::replace($xml, $data);
        }
        $rawXMLArr = explode(self::$breakPointDelimiter . "BREAKPOINT" . self::$breakPointDelimiter, $xml);
        $XMLArr =[];
        foreach ($rawXMLArr as $value) {
            $pos = strpos($value, self::$breakPointDelimiter);
            if ($pos !== false) {
                $breakPointDelimiterLength = strlen(self::$breakPointDelimiter);
                $key = trim(substr($value, 0, $pos + $breakPointDelimiterLength), self::$breakPointDelimiter);
                $XMLArr[$key] = substr($value, $pos + $breakPointDelimiterLength);
            } else {
                $XMLArr["BEGIN"] = $value;
            }
        }
        return $XMLArr;
    }

    public static function replace($xml, $data): string
    {
        foreach ($data as $key => $value) {
            $xml = str_replace(self::$elementDelimiter . $key . self::$elementDelimiter, $value, $xml);
        }
        return $xml;
    }
}