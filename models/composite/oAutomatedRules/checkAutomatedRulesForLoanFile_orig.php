<?php

namespace models\composite\oAutomatedRules;

use models\constants\automationConstants;
use models\Controllers\backoffice\automation;
use models\Controllers\backoffice\LMRequest;
use models\Database2;
use models\types\strongType;

/**
 *
 */
class checkAutomatedRulesForLoanFile_orig extends strongType
{
    /**
     * @param $params
     * @return array|null
     */
    public static function getReport($params): ?array
    {

        $PCID = $fileType = $PFS = $FSS = $workflows = '';
        $lup = $luFss = '';
        if (array_key_exists('PCID', $params)) $PCID = trim($params['PCID']);
        if (array_key_exists('fileType', $params)) $fileType = trim($params['fileType']);
        if (array_key_exists('PFS', $params)) $PFS = trim($params['PFS']);
        if (array_key_exists('FSS', $params)) $FSS = $params['FSS'];
        if (array_key_exists('workflows', $params)) $workflows = $params['workflows'];

        if (array_key_exists('lup', $params)) $lup = $params['lup'];
        if (array_key_exists('luFss', $params)) $luFss = $params['luFss'];
        $dataChanged = $params['dataChanged'] ?? '';
        $fileRow = $params['fileRow'] == 'Insert' ? 'Create' : $params['fileRow'];
        $fileRowParam = automation::getLoanFileActionCode($fileRow);
        $publicUser = $_POST['publicUser'] ?? false;
        $autoNoOfDayStatus = $params['autoNoOfDayStatus'] ?? '';
        $noOfDayStatus = $params['noOfDayStatus'] ?? 0;
        if ($autoNoOfDayStatus === 'Yes' && $noOfDayStatus > 0) {
            $noOfDayQuery = ' AND noOfDayStatus1 = ' . $noOfDayStatus . ' ';
        } else {
            $noOfDayQuery = ' AND ( noOfDayStatus1 = 0 OR noOfDayStatus1 IS NULL ) ';
        }

        //File Branch Details
        $LMRId = $params['LMRId'] ?? 0;
        LMRequest::$LMRId = $LMRId;
        $fileBranchId = LMRequest::File()->FBRID ?? 0;
        $fileBrokerId = LMRequest::File()->brokerNumber ?? 0;
        $fileLoanOfficerId = LMRequest::File()->secondaryBrokerNumber ?? 0;
        $borrowerStatus = LMRequest::File()->getTblFileClients_by_clientId()->internalInfoStatus ?? '';
        $borrowerType = LMRequest::File()->getTblFileHMLOBusinessEntity_by_fileID()->borrowerType ?? '';
        $fileLoanProgram = LMRequest::File()->getTblLMRClientType_by_LMRID()->ClientType;
        //prepare the conditional query
        $finaltblARIds = [];

        if (($PCID != '' && $fileType != '') && ($PFS != '' || count($FSS) > 0 || count($workflows) > 0 || $dataChanged == 'Yes')) {
            $sql = '
SELECT id
FROM tblAutomatedRules 
WHERE PCID = :PCID 
  AND fileType = :fileType 
  AND ruleStatus = :ruleStatus
  AND rowStatus = :rowStatus 
;';
            $sqlParams = [
                'PCID' => $PCID,
                'fileType' => $fileType,
                'ruleStatus' => 1,
                'rowStatus' => 1,
            ];
            $ARIds = Database2::getInstance()->queryData($sql, $sqlParams);
            $list = [];
            $lup = $lup ? explode(',', $lup) : [];
            if (count($ARIds) > 0) { // we have rules
                foreach ($ARIds as $ruleId) {
                    $eachRuleId = $ruleId['id'];
                    //Conditions Query
                    if($publicUser) {
                        $selQry2 = '
SELECT * 
FROM tblAutomatedRulesConditions 
WHERE tblARId = :tblARId
AND status = 1 ' . $noOfDayQuery . ' AND
((ruleFor1 = \'PFS\' OR ruleFor1 = \'FCU\')  OR (ruleFor2 = \'PFS\' OR ruleFor2 = \'FCU\')  OR (ruleFor3 = \'PFS\' OR ruleFor3 = \'FCU\'))
ORDER BY tblARId
;';

                        $selQry2Params = [
                            'tblARId' => $eachRuleId,
                        ];
                    } else {
                        $selQry2 = '
SELECT * 
FROM tblAutomatedRulesConditions 
WHERE tblARId = :tblARId
AND status = 1 ' . $noOfDayQuery . ' AND
((ruleFor1 IN ( ' . Database2::GetPlaceholders(sizeof($lup), ':ruleFor', true) . ' )
    OR ruleFor2 IN ( ' . Database2::GetPlaceholders(sizeof($lup), ':ruleFor', true) . ' )
    OR ruleFor3 IN ( ' . Database2::GetPlaceholders(sizeof($lup), ':ruleFor', true) . ' )))
ORDER BY tblARId;';

                        $selQry2Params = [
                            'tblARId' => $eachRuleId,
                        ];
                        foreach ($lup as $i => $value) {
                            $selQry2Params['ruleFor'. $i] = trim($value);
                        }
                    }

                    $selCondResult = Database2::getInstance()->queryData($selQry2, $selQry2Params);

                    if (count($selCondResult) > 0) { // we have conditions
                        foreach ($selCondResult as $buildQry) {
                            $selDynQuery = "
SELECT * 
FROM tblAutomatedRulesConditions 
WHERE status = 1 $noOfDayQuery AND tblARId = $eachRuleId AND ";
                            $and_clause = [];
                            for ($r = 1; $r <= 3; $r++) {
                                $condQuery = '';
                                $x = $r - 1;
                                $mainRuleFor = $buildQry["ruleFor$r"];
                                $isIsNot = $buildQry["isIsNot$r"];
                                if ($isIsNot) { // not null (new values)
                                    if ($isIsNot == 'IS') {
                                        $isIsNot = 'LIKE';
                                    } elseif ($isIsNot == 'IS NOT') {
                                        $isIsNot = 'NOT LIKE';
                                    }
                                } else {  //null (old values)
                                    $isIsNot = 'LIKE';
                                }
                                if ($mainRuleFor) {
                                    switch ($mainRuleFor) {
                                        case automationConstants::$automation_FCU:
                                            //Loan File Status - Create/Update
                                            if ($fileRowParam) {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'FCU' AND CONCAT('~', params$r, '~') $isIsNot '%~$fileRowParam~%') ";
                                            } else {
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                        case automationConstants::$automation_PFS:
                                            //Primary File Status (Single Value)
                                            if ($PFS != '') {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'PFS' AND CONCAT('~', params$r, '~') $isIsNot '%~$PFS~%') ";
                                            } else {
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                        case automationConstants::$automation_FSS:
                                            //File Sub Status (Multiple Values)
                                            $fileSubStatus = '';
                                            $conditionType = $buildQry["conditionType$x"];
                                            if (count($FSS) > 0) {
                                                $fssNo = 1;
                                                foreach ($FSS as $str) {
                                                    $substatusId = $str['substatusId'];
                                                    if ($fssNo > 1) {
                                                        $fileSubStatus .= ' OR ';
                                                    }
                                                    $fileSubStatus .= " CONCAT('~', params$r, '~') $isIsNot '%~$substatusId~%' ";
                                                    $fssNo++;
                                                }
                                                if ($fileSubStatus != '') {
                                                    if ($r > 1) {
                                                        if ($buildQry["conditionType$x"] != '') {
                                                            $condQuery .= $buildQry["conditionType$x"];
                                                        }
                                                    }
                                                    $condQuery .= " (ruleFor$r = 'FSS' AND ($fileSubStatus)) ";
                                                }
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;

                                            break;
                                        case automationConstants::$automation_Workflow:
                                            //Workflows (Multiple Values)
                                            $workflow = '';
                                            $conditionType = $buildQry["conditionType$x"];
                                            if (count($workflows) > 0) {
                                                $wfNo = 1;
                                                foreach ($workflows as $wf) {
                                                    $wfStepId = $wf['WFSID'];
                                                    if ($wfNo > 1) {
                                                        $workflow .= ' OR ';
                                                    }
                                                    $workflow .= " CONCAT('~', params$r, '~') $isIsNot '%~$wfStepId~%' ";
                                                    $wfNo++;
                                                }
                                                if ($workflow != '') {
                                                    if ($r > 1) {
                                                        if ($buildQry["conditionType$x"] != '') {
                                                            $condQuery .= $buildQry["conditionType$x"];
                                                        }
                                                    }
                                                    $condQuery .= " (ruleFor$r = 'Workflow' AND ($workflow)) ";
                                                }
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;

                                            break;
                                        case automationConstants::$automation_Branch:
                                            //Branch (Single Value)
                                            $conditionType = $buildQry["conditionType$x"];
                                            if ($fileBranchId) {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'Branch' AND CONCAT('~', params$r, '~') $isIsNot '%~$fileBranchId~%') ";
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                        case automationConstants::$automation_Broker:
                                            //Broker (Single Value)
                                            $conditionType = $buildQry["conditionType$x"];
                                            if ($fileBrokerId) {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'Broker' AND CONCAT('~', params$r, '~') $isIsNot '%~$fileBrokerId~%') ";
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                        case automationConstants::$automation_LO:
                                            //Loan Officer (Single Value)
                                            $conditionType = $buildQry["conditionType$x"];
                                            if ($fileLoanOfficerId) {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'LO' AND CONCAT('~', params$r, '~') $isIsNot '%~$fileLoanOfficerId~%') ";
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                        case automationConstants::$automation_BorrowerStatus:
                                            //Borrower Status (Single Value)
                                            $conditionType = $buildQry["conditionType$x"];
                                            if ($borrowerStatus) {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'BorrowerStatus' AND CONCAT('~', params$r, '~') $isIsNot '%~$borrowerStatus~%') ";
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                        case automationConstants::$automation_BorrowerType:
                                            //Borrower Type (Single Value)
                                            $conditionType = $buildQry["conditionType$x"];
                                            if ($borrowerType) {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'BorrowerType' AND CONCAT('~', params$r, '~') $isIsNot '%~$borrowerType~%') ";
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                        case automationConstants::$automation_LoanProgram:
                                            //Loan Program (Single Value)
                                            $conditionType = $buildQry["conditionType$x"];
                                            if ($fileLoanProgram) {
                                                if ($r > 1) {
                                                    if ($buildQry["conditionType$x"] != '') {
                                                        $condQuery .= $buildQry["conditionType$x"];
                                                    }
                                                }
                                                $condQuery .= " (ruleFor$r = 'LoanProgram' AND CONCAT('~', params$r, '~') $isIsNot '%~$fileLoanProgram~%') ";
                                            } else {
                                                if ($r > 1) {
                                                    $condQuery .= $conditionType;
                                                }
                                                $condQuery .= " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                            }
                                            $and_clause [] = $condQuery;
                                            break;
                                    }
                                } else {
                                    $condQuery = " (ruleFor$r IS NULL OR ruleFor$r = '') ";
                                    $and_clause [] = $condQuery;
                                }

                            }
                            if (sizeof($and_clause)) {
                                $sql = '';
                                foreach($and_clause as $i => $clause) {
                                    if(
                                        strcasecmp(substr($clause, 0, 3), 'AND') == 0
                                    ) {
                                        if(!$sql) {
                                            $sql .= '1';
                                        }
                                        $sql .= ' ' . $clause . ' ';
                                        unset($and_clause[$i]);
                                        continue;
                                    }

                                    if(
                                        strcasecmp(substr($clause, 0, 2), 'OR') == 0
                                    ) {
                                        if(!$sql) {
                                            $sql .= '0';
                                        }
                                        $sql .= ' ' . $clause . ' ';
                                        unset($and_clause[$i]);
                                        continue;
                                    }
                                }
                                if(sizeof($and_clause)) {
                                    $sql .= ($sql ? ' AND ' : '') . implode(' AND ', $and_clause);
                                }

                                $selDynQuery = $selDynQuery . '(' . $sql . ');';
                                $list[] = [$selDynQuery, $and_clause];
                                $tblARIds = Database2::getInstance()->fetchRecords(['qry' => $selDynQuery]);
                                if (count($tblARIds) > 0) {
                                    foreach ($tblARIds as $tblARId) {
                                        $finaltblARIds[] = $tblARId['tblARId'];
                                    }
                                }
                            }
                        }
                    }
                }
//                Debug($list);
            }
        }
        $finalResult = [];
        if (count($finaltblARIds) > 0) {
            $finalIds = array_unique($finaltblARIds);
            $fetchIds = implode(',', $finalIds);
            $allActions = "
SELECT * FROM tblAutomatedRules t1
            INNER JOIN tblAutomatedRulesEvents t2 ON t1.`id` = t2.`tblARId`
            WHERE t1.ruleStatus = 1
            AND t1.rowStatus = 1
            AND t2.tblARId IN ($fetchIds)
            AND t2.eventStatus = 1 
            AND t2.rowStatus = 1 
            ORDER BY `action` DESC;
";
            $finalResult = Database2::getInstance()->fetchRecords(['qry' => $allActions]);
        }
        return $finalResult;
    }
}