<?php

namespace models\composite\oFileUpdate;

use models\Database2;
use models\composite\oAffiliate\insertAffiliateInfo;
use models\composite\oBranch\savePreferredAgentForBranch;
use models\composite\oBroker\saveAgentInfo;
use models\composite\oBroker\saveAgentPCs;
use models\constants\gl\glCustomLeadsReceiver;
use models\cypher;
use models\lendingwise\db\tblFile2_db;
use models\lendingwise\db\tblFile_db;
use models\lendingwise\db\tblFileResponse_db;
use models\lendingwise\tblFile;
use models\lendingwise\tblFile2;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFileResponse;
use models\lendingwise\tblFileUpdateAudit;
use models\lendingwise\tblShortSale;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\storedProc\SP_RecordFileStatus;
use models\types\strongType;


/**
 *
 */
class saveClientInfo extends strongType
{
    private static string $qc1 = '';
    private static string $qs1 = '';
    private static string $qv1 = '';
    private static string $qu1 = '';
    private static array $params = [];

    public static function existingLMRId(
        $branchId,
        $saveTab,
        $ip,
        $responseId,
        $userGroup,
        $isSysNotesPrivate,
        $LMRId,
        $p
    )
    {
        $PSID = 0;
        $allowToUpdateFileAdminSection = 0;
        if (isset($_REQUEST['primaryStatus'])) $PSID = trim($_REQUEST['primaryStatus']);
        if (isset($_REQUEST['allowToUpdateFileAdminSection'])) $allowToUpdateFileAdminSection = trim($_REQUEST['allowToUpdateFileAdminSection']);


        $tblFileResponse = tblFileResponse::Get([tblFileResponse_db::COLUMN_LMRID => $LMRId]);
        if (!$tblFileResponse) {
            $tblFileResponse = new tblFileResponse();
            $tblFileResponse->LMRId = $LMRId;
        }
        $tblFileResponse->executiveId = intval($branchId);


        if ($PSID > 0 && (($saveTab == 'CI' &&
                    ($ip['URole'] == 'Super'
                        || $ip['URole'] == 'Manager'
                        || $allowToUpdateFileAdminSection == 1)
                ) || $saveTab == 'SLM' || $saveTab == 'LSR')
        ) {
            $tblFileResponse->primeStatusId = $PSID;
        }

        if ($saveTab == 'LSR' && isset($_REQUEST['fileNumber'])) {
            $tblFileResponse->fileNumber = HTTP::escapeQuoteForPOST($_REQUEST['fileNumber']);
        }

        if (array_key_exists('3rdPartyFileId', $p)) {
            $tblFileResponse->_3rdPartyFileId = HTTP::escapeQuoteForPOST($_REQUEST['3rdPartyFileId']);
        }

        if (array_key_exists('sbaLoanProduct', $p)) {
            $tblFileResponse->sbaLoanProduct = intval(Request::GetClean('sbaLoanProduct') ?? null);
        }

        if ($saveTab == 'SVV' && isset($_REQUEST['ANINo'])) {
            $tblFileResponse->ANINo = HTTP::escapeQuoteForPOST($_REQUEST['ANINo']);
        }

        $tblFileResponse->Save();

        if ($PSID > 0 && (($saveTab == 'CI' && (
                        $ip['URole'] == 'Super'
                        || $ip['URole'] == 'Manager'
                        || $allowToUpdateFileAdminSection == 1)
                )
                || $saveTab == 'SLM'
                || $saveTab == 'LSR'
            )
        ) {


            if (trim($ip['OSID']) != $PSID) {
                $inArray['PSID'] = $ip['OSID'];
                $previousPSName = getPCStatusName::getReport($inArray);

                $primaryStatus = '';
                if ($PSID > 0) {
                    $inArray['PSID'] = $PSID;
                    $primaryStatus = getPCStatusName::getReport($inArray);
                }
                $inArray['execName'] = $ip['userName'];
                $inArray['role'] = $userGroup;
                $inArray['processorName'] = $ip['userName'];
                $inArray['processorNotes'] = 'Primary file status changed from ' . $previousPSName . ' to ' . $primaryStatus;
                $inArray['displayIn'] = 'NH';
                $inArray['employeeId'] = 0;
                $inArray['executiveId'] = 0;
                $inArray['brokerNumber'] = 0;
                if ($userGroup == 'Employee') {
                    $inArray['employeeId'] = $ip['UID'];
                } elseif ($userGroup == 'Branch') {
                    $inArray['executiveId'] = $ip['UID'];
                } elseif ($userGroup == 'Agent') {
                    $inArray['brokerNumber'] = $ip['UID'];
                }
                $inArray['privateNotes'] = $isSysNotesPrivate;
                $inArray['isSysNotes'] = 1;
                $inArray['fileID'] = $LMRId;
                saveFileNotes::getReport($inArray);

                SP_RecordFileStatus::getReport(
                    intval($LMRId)
                    , intval($ip['OSID'])
                    , intval($PSID)
                    , 'Pri'
                    , intval($ip['UID'])
                    , trim($ip['URole'])
                    , intval($branchId)
                    , null
                    , null
                );
            }
        }
    }

    public static function newLMRId(
        $PCID,
        $glCustomLeadsReceiver,
        $saveTab,
        $saveOpt,
        $newLMRId,
        $ip,
        $branchId,
        $p
    ): ?int
    {
        // default value required
        $_REQUEST['ANINo'] = $_REQUEST['ANINo'] ?? '';


        $newLMRId = intval($newLMRId);

        $PSID = 0;
        $params = [];
        if (in_array($PCID, $glCustomLeadsReceiver) && $saveTab == 'LR') {

            $tblFile = tblFile::Get([
                tblFile_db::COLUMN_LMRID => $newLMRId,
            ]);
            $tblFile->loanNumber = $newLMRId;
            $tblFile->Save();

            $tblFileHMLO = new tblFileHMLO();
            $tblFileHMLO->fileID = $newLMRId;
            $tblFileHMLO->REBroker = 'No';
            $tblFileHMLO->coBorCompanyName = '';
            $tblFileHMLO->Save();
        }

        $tblFileResponse = tblFileResponse::Get([
            tblFileResponse_db::COLUMN_LMRID => $newLMRId,
        ]);

        if (!$tblFileResponse) {
            $tblFileResponse = new tblFileResponse();
            $tblFileResponse->LMRId = $newLMRId;
        }

        if (isset($_REQUEST['primaryStatus'])) {
            $PSID = intval($_REQUEST['primaryStatus']);
            $tblFileResponse->primeStatusId = $PSID;
        }

        if (array_key_exists('primeStatus', $ip)) {
            $PSID = intval($ip['primeStatus']);
            $tblFileResponse->primeStatusId = $PSID;
        }

        if ($saveTab == 'QC') {
            if (array_key_exists('primeStatusId', $ip)) {
                $PSID = intval($ip['primeStatusId']);
                $tblFileResponse->primeStatusId = $PSID;
            }
        }

        /* Edit to webform and lead notification template for PC = NVA Branch = After Hours Intake on Added Condition :: $saveOpt == 'SVV' Dec 10, 2016 */

        if ($saveTab == 'LR' || $saveTab == 'HMLO' || $saveOpt == 'SVV') {
            /** LR = Lead Receiver **/
            $fileNumber = '';
            $ANINo = '0';

            if (array_key_exists('leadSource', $ip)) {
                $tblFileResponse->leadSource = $ip['leadSource'];
            }

            if (isset($_REQUEST['fileNumber'])) {
                $fileNumber = trim($_REQUEST['fileNumber']);
                $tblFileResponse->fileNumber = $fileNumber;
            }

            if (isset($_REQUEST['ANINo'])) {
                //Screener Name Added Dec 28, 2016
                $ANINo = trim($_REQUEST['ANINo']);
                $tblFileResponse->ANINo = $ANINo;
            }

            if (isset($_REQUEST['screenerName'])) {
                $screenerName = trim($_REQUEST['screenerName']);
                $tblFileResponse->screenerName = $screenerName;
            } //Screener Name Added Dec 30, 2016
        }

        if (array_key_exists('3rdPartyFileId', $p)) {
            $tblFileResponse->_3rdPartyFileId = $p['3rdPartyFileId'];
        }
        if (array_key_exists('sbaLoanProduct', $p)) {
            $tblFileResponse->sbaLoanProduct = intval($p['sbaLoanProduct'] ?? null);
        }

        $tblFileResponse->executiveId = $branchId;

        $tblFileResponse->Save();

        $newResponseId = $tblFileResponse->LMRResponseId;

        SP_RecordFileStatus::getReport(
            $newLMRId
            , 0
            , $PSID
            , 'Pri'
            , intval($ip['UID'])
            , trim($ip['URole'])
            , intval($branchId)
            , null
            , null
        );

        return $newResponseId;
    }

    public static function saveFileAudit(
        $publicUser,
        $LMRId,
        $clientId,
        $FAction
    )
    {
        if ($publicUser && $LMRId) {
            /*$UType = cypher::myDecryption($ip['p']['UType']) ?? '';
            if ($UType == 'Agent/Broker') { //Broker
                $UType = 'Agent';
                $UID = cypher::myDecryption($ip['p']['agentId']);
            } elseif ($UType == 'Borrower') { //Borrower
                $UType = 'Client';
                $UID = $clientId;
            }*/
            //This is always Borrower
            $tblFileUpdateAudit = new tblFileUpdateAudit();
            $tblFileUpdateAudit->fileID = $LMRId;
            $tblFileUpdateAudit->UID = intval($clientId);
            $tblFileUpdateAudit->UType = 'Client';
            $tblFileUpdateAudit->recordDate = Dates::Timestamp();
            $tblFileUpdateAudit->FAction = $FAction;
            $tblFileUpdateAudit->Save();
        }
    }

    public static function result(
        $agentId,
        $CID,
        $clientId,
        $userGroup,
        $saveTab,
        $publicUser,
        $formType,
        $formTypeView,
        $UID,
        $clientInfo,
        $clientEmail,
        $isLOOpt,
        $ip,
        $saveOpt,
        $secondaryAgentId,
        $LMRId,
        $branchId,
        $PCID
    ): array
    {
        $qc = '';
        $qv = '';
        $qu = '';
        $qs = '';
        $params = [];

        $tblFile = tblFile::Get(['LMRId' => $LMRId]) ?? new tblFile();

        if ($agentId > 0) {
            $tblFile->brokerNumber = $agentId;
        }
        if ($CID > 0 && trim($clientId) != trim($CID)) {
            $tblFile->clientId = $CID;
        }
        if ($userGroup || $saveTab || $publicUser == 1) {
            if ($saveTab == 'LR') {
                $createduserGroup = 'Lead';
            } elseif ($publicUser == 1) {
                $createduserGroup = 'Web Form';
                if ($formType) {
                    $createduserGroup = $formType . ' ' . $createduserGroup;
                }
                if ($formTypeView) {
                    $createduserGroup = $createduserGroup . ' ' . $formTypeView;
                }
            } else {
                $createduserGroup = $userGroup;
            }
            $tblFile->createdUserType = $createduserGroup;
        }

        if (trim($UID) > 0) {
            $tblFile->createdBy = $UID;
        }

        $LMRClientType = [];
        if (Request::isset('LMRClientType')) {
            $LMRClientType = Request::GetClean('LMRClientType');
        }

        if (Request::isset('borrowerFName')) {
            $borrowerName = Request::GetClean('borrowerFName');
            if ($borrowerName == '') $_REQUEST['borrowerFName'] = 'Undisclosed';
        }
        if (!(in_array('HOA', $LMRClientType) && $saveTab == 'CI' && $publicUser != 1)) {
            if (Request::isset('fName')) {
                $tblFile->borrowerName = Strings::stripQuote(HTTP::escapeQuoteForPOST(Request::GetClean('fName')));
                $tblFile->enc_borrowerName = cypher::myEncryption(stripslashes(Request::GetClean('fName')));
            }
            if (Request::isset('borrowerName')) {
                $tblFile->borrowerName = Strings::stripQuote(HTTP::escapeQuoteForPOST(Request::GetClean('borrowerName')));
                $tblFile->enc_borrowerName = cypher::myEncryption(stripslashes(Request::GetClean('borrowerName')));
            }

            if (Request::isset('borrowerFName')) {
                $tblFile->borrowerName = Strings::stripQuote(HTTP::escapeQuoteForPOST(Request::GetClean('borrowerFName')));
                $tblFile->enc_borrowerName = cypher::myEncryption(stripslashes(Request::GetClean('borrowerFName')));
            } elseif (array_key_exists('clientFName', $clientInfo)) {                          /* Insert/Update Form Client Profile */
                $tblFile->borrowerName = Strings::stripQuote(HTTP::escapeQuoteForPOST($clientInfo['clientFName']));
                $tblFile->enc_borrowerName = cypher::myEncryption(stripslashes($clientInfo['clientFName']));
            }

            if (Request::isset('lName')) {
                $tblFile->borrowerLName = Strings::stripQuote(HTTP::escapeQuoteForPOST(Request::GetClean('lName')));
                $tblFile->enc_borrowerLName = cypher::myEncryption(stripslashes(Request::GetClean('lName')));
            }

            if (Request::isset('borrowerMName')) {
                $tblFile->borrowerMName = Strings::stripQuote(HTTP::escapeQuoteForPOST(Request::GetClean('borrowerMName')));
            }

            if (Request::isset('borrowerLName')) {
                $tblFile->borrowerLName = Strings::stripQuote(HTTP::escapeQuoteForPOST(Request::GetClean('borrowerLName')));
                $tblFile->enc_borrowerLName = cypher::myEncryption(stripslashes(Request::GetClean('borrowerLName')));

            } elseif (array_key_exists('clientLName', $clientInfo) && $clientInfo['clientLName']) {                          /* Insert/Update Form Client Profile */
                $tblFile->borrowerLName = Strings::stripQuote(HTTP::escapeQuoteForPOST($clientInfo['clientLName']));
                $tblFile->enc_borrowerLName = cypher::myEncryption(stripslashes($clientInfo['clientLName']));
            }

            if (Request::isset('ssn1') || Request::isset('ssn2') || Request::isset('ssn3')) {
                $tblFile->ssnNumber = Request::GetClean('ssn1') . Request::GetClean('ssn2') . Request::GetClean('ssn3');
                $tblFile->enc_ssnNumber = cypher::myEncryption(Request::GetClean('ssn1') . Request::GetClean('ssn2') . Request::GetClean('ssn3'));
            }

            if (Request::isset('ssn')) {
                $tblFile->ssnNumber = Strings::getNumberValue(Request::GetClean('ssn'));
                $tblFile->enc_ssnNumber = cypher::myEncryption(Strings::getNumberValue(Request::GetClean('ssn')));
            }
            if (Request::isset('borrowerDOB')) {
                $borrowerDOB = Request::GetClean('borrowerDOB');
                if (Dates::IsEmpty($borrowerDOB)) {
                    $borrowerDOB = null;
                } else {
                    $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'MDY', 'Y-m-d');
                }
                $tblFile->borrowerDOB = $borrowerDOB;
            }

            if (Request::isset('borrowerPOB')) {
                $tblFile->borrowerPOB = Request::GetClean('borrowerPOB');
            }

            if (Request::isset('cSaleDate') && $saveTab != 'LR') {
                $salesDate = Request::GetClean('cSaleDate');

                if (Dates::IsEmpty($salesDate)) {
                    $salesDate = null;
                } else {
                    $salesDate = Dates::formatDateWithRE($salesDate, 'MDY', 'Y-m-d');
                }
                $tblFile->salesDate = $salesDate;
            }

            if (Request::isset('loanSalesDate') && $saveTab != 'LR') {
                $salesDate = Request::GetClean('loanSalesDate');

                if (Dates::IsEmpty($salesDate)) {
                    $salesDate = null;
                } else {
                    $salesDate = Dates::formatDateWithRE($salesDate, 'MDY', 'Y-m-d');
                }
                $tblFile->salesDate = $salesDate;
            }
            if (Request::isset('dob')) {
                $borrowerDOB = Request::GetClean('dob');
                if (Dates::IsEmpty($borrowerDOB)) {
                    $borrowerDOB = null;
                } else {
                    $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'MDY', 'Y-m-d');
                }
                $tblFile->borrowerDOB = $borrowerDOB;
                $tblFile->enc_borrowerDOB = cypher::myEncryption(trim($borrowerDOB));
            }
            if (Request::isset('borrowerTimeZone')) {
                $tblFile->borrowerTimeZone = Request::GetClean('borrowerTimeZone');
            }

            if (Request::isset('phNo1') || Request::isset('phNo2') || Request::isset('phNo3')) {
                $tblFile->phoneNumber = Request::GetClean('phNo1') . Request::GetClean('phNo2') . Request::GetClean('phNo3');
            }

            if (Request::isset('phone1') && !strpos($qc, 'phoneNumber')) {

                if (Request::GetClean('phone1')) {
                    $tblFile->phoneNumber = Strings::getNumberValue(Request::GetClean('phone1'));
                } elseif (array_key_exists('clientPhone', $clientInfo) && ($clientInfo['clientPhone'])) {
                    /* Insert/Update Form Client Profile */
                    $tblFile->phoneNumber = Strings::getNumberValue($clientInfo['clientPhone']);
                }
            } elseif (Request::isset('phoneNumber') && !strpos($qc, 'phoneNumber')) {

                if (Request::GetClean('phoneNumber')) {
                    $tblFile->phoneNumber = Strings::getNumberValue(Request::GetClean('phoneNumber'));
                } elseif (array_key_exists('clientPhone', $clientInfo) && ($clientInfo['clientPhone'])) {
                    /* Insert/Update Form Client Profile */
                    $tblFile->phoneNumber = Strings::getNumberValue($clientInfo['clientPhone']);
                }
            }

            if (Request::isset('altPhoneNumber')) {
                $tblFile->altPhoneNumber = Strings::getNumberValue(Request::GetClean('altPhoneNumber'));
            }

            if (Request::isset('cell')) {
                $tblFile->cellNumber = Strings::getNumberValue(Request::GetClean('cell'));
            } elseif (array_key_exists('clientCell', $clientInfo) && ($clientInfo['clientCell'])) {                          /* Insert/Update Form Client Profile */
                $tblFile->cellNumber = Strings::getNumberValue($clientInfo['clientCell']);
            }

            if (Request::isset('fax1') || Request::isset('fax2') || Request::isset('fax3')) {
                $tblFile->fax = Request::GetClean('fax1') . Request::GetClean('fax2') . Request::GetClean('fax3');
            }

            if (Request::isset('fax')) {
                $tblFile->fax = Strings::getNumberValue(Request::GetClean('fax'));
            }
            if (Request::isset('borrFax')) {
                $tblFile->fax = Strings::getNumberValue(Request::GetClean('borrFax'));
            }
            if (Request::isset('workNumber')) {
                $tblFile->workNumber = Strings::getNumberValue(Request::GetClean('workNumber'));
            }
            if (Request::isset('marriageDate')) {
                $marriageDate = Request::GetClean('marriageDate');
                if (Dates::IsEmpty($marriageDate)) {
                    $marriageDate = null;
                } else {
                    $marriageDate = Dates::formatDateWithRE($marriageDate, 'MDY', 'Y-m-d');
                }
                $tblFile->marriageDate = $marriageDate;
            }
            if (Request::isset('divorceDate')) {
                $divorceDate = Request::GetClean('divorceDate');
                if (Dates::IsEmpty($divorceDate)) {
                    $divorceDate = null;
                } else {
                    $divorceDate = Dates::formatDateWithRE($divorceDate, 'MDY', 'Y-m-d');
                }
                $tblFile->divorceDate = $divorceDate;
            }
            if (Request::isset('coBorrowerDOB')) {
                $coBorrowerDOB = Request::GetClean('coBorrowerDOB');
                if (Dates::IsEmpty($coBorrowerDOB)) {
                    $coBorrowerDOB = null;
                } else {
                    $coBorrowerDOB = Dates::formatDateWithRE($coBorrowerDOB, 'MDY', 'Y-m-d');
                }
                $tblFile->coBorrowerDOB = $coBorrowerDOB;
            }

            if (Request::isset('coborrowerPOB')) {
                $tblFile->coborrowerPOB = Request::GetClean('coborrowerPOB');
            }

            if (Request::isset('cDob')) {
                $coBorrowerDOB = Request::GetClean('cDob');
                if (Dates::IsEmpty($coBorrowerDOB)) {
                    $coBorrowerDOB = null;
                } else {
                    $coBorrowerDOB = Dates::formatDateWithRE($coBorrowerDOB, 'MDY', 'Y-m-d');
                }
                $tblFile->coBorrowerDOB = $coBorrowerDOB;
            }
            if (Request::isset('mortgageInvestor1')) {
                $mortgageInvestor1 = Request::GetClean('mortgageInvestor1');
                if ($mortgageInvestor1 == '') {
                    $mortgageInvestor1 = 0;
                }
                $tblFile->mortgageInvestor1 = $mortgageInvestor1;
            }
            if (Request::isset('mortgageInvestor2')) {
                $mortgageInvestor2 = Request::GetClean('mortgageInvestor2');
                if ($mortgageInvestor2 == '') {
                    $mortgageInvestor2 = 0;
                }
                $tblFile->mortgageInvestor2 = $mortgageInvestor2;
            }
            if (Request::isset('coBorrowerTimeZone')) {
                $tblFile->coBorrowerTimeZone = Request::GetClean('coBorrowerTimeZone');
            }


            if (Request::isset('cSsn')) {
                $tblFile->coBSsnNumber = Strings::getNumberValue(Request::GetClean('cSsn'));
            }
            if (Request::isset('coBSsn1') || Request::isset('coBSsn2') || Request::isset('coBSsn3')) {
                $tblFile->coBSsnNumber = Request::GetClean('coBSsn1') . Request::GetClean('coBSsn2') . Request::GetClean('coBSsn3');
            }
            if (Request::isset('cPhone1')) {
                $tblFile->coBPhoneNumber = Strings::getNumberValue(Request::GetClean('cPhone1'));
            }
            if (Request::isset('coBPhNo1') || Request::isset('coBPhNo2') || Request::isset('coBPhNo3')) {
                $tblFile->coBPhoneNumber = Request::GetClean('coBPhNo1') . Request::GetClean('coBPhNo2') . Request::GetClean('coBPhNo3');
            }
            if (Request::isset('coBAltPhNo1') || Request::isset('coBAltPhNo2') || Request::isset('coBAltPhNo3') || Request::isset('coBAltExt')) {
                $tblFile->coBAltPhoneNumber = Request::GetClean('coBAltPhNo1') . Request::GetClean('coBAltPhNo2') . Request::GetClean('coBAltPhNo3') . Request::GetClean('coBAltExt');
            }
            if (Request::isset('coBCellNumber')) {
                $tblFile->coBCellNumber = Strings::getNumberValue(Request::GetClean('coBCellNumber'));
            }
            if (Request::isset('coBCellNo1') || Request::isset('coBCellNo2') || Request::isset('coBCellNo3')) {
                $tblFile->coBCellNumber = Request::GetClean('coBCellNo1') . Request::GetClean('coBCellNo2') . Request::GetClean('coBCellNo3');
            }
            if (Request::isset('coBFax1') || Request::isset('coBFax2') || Request::isset('coBFax3')) {
                $tblFile->coBFax = Request::GetClean('coBFax1') . Request::GetClean('coBFax2') . Request::GetClean('coBFax3');
            }
            if (Request::isset('coBFax')) {
                $tblFile->coBFax = Strings::getNumberValue(Request::GetClean('coBFax'));
            }
            if (Request::isset('coBWorkNo1') || Request::isset('coBWorkNo2') || Request::isset('coBWorkNo3') || Request::isset('coBWorkNoExt')) {
                $tblFile->coBorrowerWorkNumber = Request::GetClean('coBWorkNo1') . Request::GetClean('coBWorkNo2') . Request::GetClean('coBWorkNo3') . Request::GetClean('coBWorkNoExt');
            }
            if (Request::isset('lien1LPMade')) {
                $lien1LPMade = Request::GetClean('lien1LPMade');
                if (Dates::IsEmpty($lien1LPMade)) {
                    $lien1LPMade = null;
                } else {
                    $lien1LPMade = Dates::formatDateWithRE($lien1LPMade, 'MDY', 'Y-m-d');
                }
                $tblFile->lien1LPMade = $lien1LPMade;
            }

            if (Request::isset('lien2LPMade')) {
                $lien2LPMade = Request::GetClean('lien2LPMade');
                if (Dates::IsEmpty($lien2LPMade)) {
                    $lien2LPMade = null;
                } else {
                    $lien2LPMade = Dates::formatDateWithRE($lien2LPMade, 'MDY', 'Y-m-d');
                }
                $tblFile->lien2LPMade = $lien2LPMade;
            }
        }

        if (Request::isset('propertyAddress')) {
            $tblFile->propertyAddress = HTTP::escapeQuoteForPOST(Request::GetClean('propertyAddress'));
            $tblFile->enc_propertyAddress = cypher::myEncryption(stripslashes(Request::GetClean('propertyAddress')));
        }
        if (Request::isset('propAddr')) {
            $tblFile->propertyAddress = HTTP::escapeQuoteForPOST(Request::GetClean('propAddr'));
            $tblFile->enc_propertyAddress = cypher::myEncryption(stripslashes(Request::GetClean('propAddr')));
        }
        if (Request::isset('propertyCity')) {
            $tblFile->propertyCity = HTTP::escapeQuoteForPOST(Request::GetClean('propertyCity'));
            $tblFile->enc_propertyCity = cypher::myEncryption(stripslashes(Request::GetClean('propertyCity')));
        }
        if (Request::isset('propCity')) {
            $tblFile->propertyCity = HTTP::escapeQuoteForPOST(Request::GetClean('propCity'));
            $tblFile->enc_propertyCity = cypher::myEncryption(stripslashes(Request::GetClean('propCity')));
        }
        if (Request::isset('propertyState')) {
            $tblFile->propertyState = HTTP::escapeQuoteForPOST(Request::GetClean('propertyState'));
            $tblFile->enc_propertyState = cypher::myEncryption(stripslashes(Request::GetClean('propertyState')));
        }
        if (Request::isset('propState')) {
            $tblFile->propertyState = HTTP::escapeQuoteForPOST(Request::GetClean('propState'));
            $tblFile->enc_propertyState = cypher::myEncryption(stripslashes(Request::GetClean('propState')));
        }
        if (Request::isset('propertyZip')) {
            $tblFile->propertyZip = HTTP::escapeQuoteForPOST(Request::GetClean('propertyZip'));
            $tblFile->enc_propertyZip = cypher::myEncryption(stripslashes(Request::GetClean('propertyZip')));
        }
        if (Request::isset('propZip')) {
            $tblFile->propertyZip = HTTP::escapeQuoteForPOST(Request::GetClean('propZip'));
            $tblFile->enc_propertyZip = cypher::myEncryption(stripslashes(Request::GetClean('propZip')));
        }
        if (Request::isset('borrowerEmail')) {
            $borrowerEmail = Request::GetClean('borrowerEmail') ?? '';
            if (!$borrowerEmail) {
                $borrowerEmail = $clientEmail;
            }
            $tblFile->borrowerEmail = HTTP::escapeQuoteForPOST($borrowerEmail);
            $tblFile->enc_borrowerEmail = cypher::myEncryption(stripslashes($borrowerEmail));
        }
        if (Request::isset('borrowerSecondaryEmail')) {
            $tblFile->borrowerSecondaryEmail = HTTP::escapeQuoteForPOST(Request::GetClean('borrowerSecondaryEmail'));
        }
        if (Request::isset('maritalStatusCoBor')) {
            $tblFile->maritalStatusCoBor = HTTP::escapeQuoteForPOST(Request::GetClean('maritalStatusCoBor'));
        }

        if (Request::isset('email')) {
            if (Request::GetClean('email') == '' && $saveTab == 'LR') {
                $tblFile->borrowerEmail = HTTP::escapeQuoteForPOST($clientEmail);
                $tblFile->enc_borrowerEmail = cypher::myEncryption(stripslashes($clientEmail));
            } else {
                $email = Request::GetClean('email') ?? '';
                if (!$email) {
                    $email = $clientEmail;
                }
                $tblFile->borrowerEmail = HTTP::escapeQuoteForPOST($email);
                $tblFile->enc_borrowerEmail = cypher::myEncryption(stripslashes($email));
            }
        }

        if (Request::isset('occupancy') && $isLOOpt != 1) {
            $tblFile->occupancy = HTTP::escapeQuoteForPOST(Request::GetClean('occupancy'));
        }

        if (Request::isset('driverLicenseState')) {
            $tblFile->driverLicenseState = HTTP::escapeQuoteForPOST(Request::GetClean('driverLicenseState'));
        }

        if (Request::isset('driverLicenseNumber')) {
            $tblFile->driverLicenseNumber = HTTP::escapeQuoteForPOST(Request::GetClean('driverLicenseNumber'));
        }

        if (isset($_POST['coBorDriverLicenseState'])) {
            $tblFile->coBorDriverLicenseState = HTTP::escapeQuoteForPOST(Request::GetClean('coBorDriverLicenseState'));
        }

        if (isset($_POST['coBorDriverLicenseNumber'])) {
            $tblFile->coBorDriverLicenseNumber = HTTP::escapeQuoteForPOST(Request::GetClean('coBorDriverLicenseNumber'));
        }

        if ($saveTab == 'SSREG' || $saveTab == 'CI' || $saveTab == 'HMLO') {
            /** SSREG= SS Registration/ iframe module **/
            $REBrokerId = $ip['REBrokerId'];
            if (trim($REBrokerId) == '') {
                $REBrokerId = 0;
            }

            $tblFile->REBrokerId = $REBrokerId;

            if (Request::isset('receiveNotice')) {
                $tblFile->receiveNotice = HTTP::escapeQuoteForPOST(Request::GetClean('receiveNotice'));
            }

            if (Request::isset('mortgageLates')) {
                $tblFile->mortgageLates = HTTP::escapeQuoteForPOST(Request::GetClean('mortgageLates'));
            }

            if (Request::isset('receiveModification')) {
                $tblFile->receiveModification = HTTP::escapeQuoteForPOST(Request::GetClean('receiveModification'));
            }
        }
        if ($saveTab == 'HR') {
            /** HOME Report **/
            if (Request::isset('receiveModification')) {
                $tblFile->receiveModification = HTTP::escapeQuoteForPOST(Request::GetClean('receiveModification'));
            }
            if (Request::isset('receiveModificationNotes')) {
                $tblFile->receiveModificationNotes = HTTP::escapeQuoteForPOST(Request::GetClean('receiveModificationNotes'));
            }
        }

        if ($saveOpt == 'SVV') {
            if (array_key_exists('NVANotes', $ip)) {
                $tblFile->mortgageNotes = rawurlencode($ip['NVANotes']);
            }
        }

        if ($saveTab == 'US') {
            if (array_key_exists('mortgageNotes', $ip)) {
                $tblFile->mortgageNotes = rawurlencode($ip['mortgageNotes']);
            }
            if (Request::isset('closedDate')) {
                $closedDate = Request::GetClean('closedDate');
                if (Dates::IsEmpty($closedDate)) {
                    $closedDate = null;
                } else {
                    $closedDate = Dates::formatDateWithRE($closedDate, 'MDY', 'Y-m-d');
                }
                $tblFile->closedDate = $closedDate;
            }
        }

        if (array_key_exists('FUMortgageNotes', $ip)) {
            $tblFile->mortgageNotes = rawurlencode($ip['FUMortgageNotes']);
        }

        if ($saveTab == 'JA' || $saveOpt == 'FS' || $saveTab == 'US' || $saveTab == 'QA') {
            /** JA = Joinder App **/
            $isCoBorrower = 0;
            if (array_key_exists('isCoBorrower', $ip)) {
                $isCoBorrower = $ip['isCoBorrower'];
            }
            $tblFile->isCoBorrower = intval($isCoBorrower);
        }

        if (Request::isset('receivedDate')) {
            $receivedDate = Request::GetClean('receivedDate');
            if (Dates::IsEmpty($receivedDate)) {
                $receivedDate = null;
            } else {
                $receivedDate = Dates::formatDateWithRE($receivedDate, 'MDY', 'Y-m-d');
            }
            $tblFile->receivedDate = $receivedDate;
        }

        $columnDataType = [
            'homeValue' => Database2::DATATYPE_MONEY,
        ];

        if ($saveTab == 'LR') {
            /** LR = Lead Receiver **/
            $leadReceiveArray = [
                'coBFN'                    => 'coBorrowerFName',
                'coBLN'                    => 'coBorrowerLName',
                'coBEmail'                 => 'coBorrowerEmail',
                'isCoBorrower'             => 'isCoBorrower',
                'propertyType'             => 'propertyType',
                'mnthsBehind'              => 'noOfMonthsBehind1',
                'lien1Rate'                => 'lien1Rate',
                'rate2'                    => 'lien2Rate',
                'amount1'                  => 'lien1Amount',
                'amount2'                  => 'lien2Amount',
                'includeTI'                => 'areTaxesInsuranceEscrowed',
                'balance1'                 => 'lien1OriginalBalance',
                'balance2'                 => 'lien2OriginalBalance',
                'mtgBehind'                => 'mortgageLates',
                'payment1'                 => 'lien1Payment',
                'payment2'                 => 'lien2Payment',
                'homeValue'                => 'homeValue',
                'lender1'                  => 'servicer1',
                'loanType1'                => 'loanType',
                'receivedDate'             => 'receivedDate',
                'propCounty'               => 'propertyCounty',
                'workNo1'                  => 'workNumber',
                'borrowerNotes'            => 'mortgageNotes', /* HMLO fields */
                'loanAmtRequested'         => 'lien1OriginalBalance', /* HMLO fields */
                'mailingAddress'           => 'mailingAddress',
                'mailingCity'              => 'mailingCity',
                'mailingState'             => 'mailingState',
                'mailingZip'               => 'mailingZip',
                'coBSsn'                   => 'coBSsnNumber',
                'coBorrowerMailingAddress' => 'coBorrowerMailingAddress',
                'maritalStatus'            => 'maritalStatus',
                'previousAddress'          => 'previousAddress',
                'previousCity'             => 'previousCity',
                'previousState'            => 'previousState',
                'previousZip'              => 'previousZip',
                'coBDOB'                   => 'coBorrowerDOB',
                'coBCell'                  => 'coBCellNumber',
                'coBPhoneNumber'           => 'coBPhoneNumber',
                'loanNumber'               => 'loanNumber',
            ];
            foreach ($leadReceiveArray as $leadFldKey => $leadFldVal) {
                $postVal = '';
                if (Request::isset($leadFldKey)) {
                    if ($leadFldKey == 'loanType1') { // Intake tab - Client Document START - May 15, 2015
                        if (trim(Request::GetClean($leadFldKey)) == 'Adjust') {
                            $postVal = 'ARM';
                        }
                        if (trim(Request::GetClean($leadFldKey)) == 'Fixed') {
                            $postVal = 'Fixed Rate';
                        }
                    } elseif ($leadFldKey == 'receivedDate' || $leadFldKey == 'coBDOB') { // THSD Lead for SLM START - Aug 3, 2015
                        $postVal = Dates::formatDateWithRE(Request::GetClean($leadFldKey), 'MDY', 'Y-m-d');
                    } elseif ($leadFldKey == 'mortgageNotes') { // HMLO Lead START - Mar 28, 2017
                        $postVal = rawurlencode(stripslashes(Request::GetClean($leadFldKey)));
                    } elseif ($leadFldKey == 'workNumber') { // HMLO Lead START - Mar 28, 2017
                        $postVal = Strings::getNumberValue(Request::GetClean($leadFldKey));
                    } elseif ($leadFldKey == 'isCoBorrower') {
                        $postVal = intval(Request::GetClean($leadFldKey));
                    } else {
                        $postVal = Strings::stripQuote(Request::GetClean($leadFldKey));
                    }
                    if (array_key_exists($leadFldKey, $columnDataType)) {
                        $postVal = Database2::strongTypeValue($postVal, $columnDataType[$leadFldKey]);
                    }
                    $tblFile->$leadFldVal = $postVal;
                }
            }
        } else {
            $LMRClientType = [];
            if (Request::isset('LMRClientType')) {
                $LMRClientType = Request::GetClean('LMRClientType');
            }
            if (in_array('HOA', $LMRClientType) && $saveTab == 'CI' && $publicUser != 1) {
                $DFArray = ['propertyCounty', 'occupancy', 'propertyType', 'homeValue', 'areTaxesInsuranceEscrowed', 'areInsuranceEscrowed'];
                /** DIRECT Variables **/
            } else {
                $DFArray = [
                    'propertyCounty', 'mailingAddress', 'mailingCity', 'mailingState', 'mailingZip', 'mailingUnit', 'mailingCountry',
                    'previousAddress', 'previousCity', 'previousState', 'previousZip', 'maritalStatus', 'maidenName', 'spouseName',
                    'isCoBorrower', 'coBorrowerFName', 'coBorrowerLName', 'coBorrowerEmail', 'coBorrowerMailingAddress', 'coBorrowerMailingCity',
                    'coBorrowerMailingState', 'coBorrowerCounty', 'coBorrowerMailingZip', 'coBorPreviousAddress', 'coBorPreviousCity',
                    'coBorPreviousState', 'coBorPreviousZip', 'propertyType',
                    'homeValue', 'loanType', 'lien1Terms', 'lien1Rate', 'lien1Amount', 'lien1OriginalBalance', 'loanNumber', 'lien1BalanceDue',
                    'noOfMonthsBehind1', 'mortgageOwner1', 'lien1Payment', 'loanType2', 'lien2Terms', 'lien2Rate', 'lien2Amount',
                    'lien2OriginalBalance', 'lien2Payment', 'loanNumber2', 'lien2BalanceDue', 'noOfMonthsBehind2',
                    'mortgageOwner2', 'mortgageNotes', 'mailingAddressAsProp', 'previousAddrAsMailing', 'mailingAddressAsBorrower',
                    'coBorPreviousAddrAsMailing', 'serviceProvider', 'coBServiceProvider', 'remainingMonths', 'receiveNotice',
                    'receiveModification', 'receiveModificationNotes', 'areTaxesInsuranceEscrowed', 'areInsuranceEscrowed',
                ];
                /** DIRECT Variables **/
            }
            if ($saveTab == 'CI' || $saveTab == 'HMLO') {
                unset($DFArray[array_search('receiveNotice', $DFArray)]);
                unset($DFArray[array_search('receiveModification', $DFArray)]);
                $DFArray = array_values($DFArray);
            }
            //
            if (array_key_exists('FUMortgageNotes', $ip)) {
                unset($DFArray[array_search('mortgageNotes', $DFArray)]);
                $DFArray = array_values($DFArray);
            }
            foreach ($DFArray as $column) {
                if (Request::isset($column)) {
                    $qc .= $qs . " " . $column . ' ';
                    $postVal = Strings::stripQuote(HTTP::escapeQuoteForPOST(Request::GetClean($column)));
                    if (($column == 'mortgageOwner1') || ($column == 'mortgageOwner2')) {
                        if ($postVal == '') {
                            $postVal = 0;
                        }
                    }
                    if ($column == 'mortgageNotes') {
                        $postVal = rawurlencode(stripslashes($postVal));
                    }
                    if ($column == 'mailingAddressAsProp') {
                        $postVal = substr($postVal, 0, 5);
                    }
                    if (array_key_exists($column, $columnDataType)) {
                        $postVal = Database2::strongTypeValue($postVal, $columnDataType[$column]);
                    }
                    if ($column === 'mailingUnit') {
                        $postVal = Strings::AlphaNumericOnly($postVal);
                    }
                    if ($column === 'coBorrowerMailingZip') {
                        $postVal = Strings::NumbersOnly($postVal);
                    }
                    $params[$column] = $postVal;

                    if ($column == 'isCoBorrower' || $column == 'remainingMonths') {
                        $tblFile->$column = intval($postVal);

                    } else {
                        $tblFile->$column = $postVal;
                    }
                } elseif (isset($clientInfo[$column]) && ($clientInfo[$column])) {
                    $tblFile->$column = HTTP::escapeQuoteForPOST($clientInfo[$column]);
                }
            }


            if (Request::isset('loanLoanNumber') && !strpos($qc, 'loanNumber')) {
                $tblFile->loanNumber = HTTP::escapeQuoteForPOST(Request::GetClean('loanLoanNumber'));
            }

            if (Request::isset('lienLoanNumber') && !strpos($qc, 'loanNumber')) {
                $tblFile->loanNumber = HTTP::escapeQuoteForPOST(Request::GetClean('lienLoanNumber'));
            }
            /* this code need to be revisited */
            if (Request::isset('lien1RateLMS') && Request::GetClean('lien1Rate')) {
                $tblFile->lien1Rate = Strings::replaceCommaValues(Request::GetClean('lien1RateLMS'));
            }
        }

        $TFArray = [
            'servicer1', 'originalLender1', 'servicer2', 'originalLender2',

        ];

        /** DIRECT Variables - Clear default Text msg **/
        $typeText = '';
        if (Request::isset('typeText')) $typeText = Request::GetClean('typeText');
        foreach ($TFArray as $column) {
            if (Request::isset($column)) {
                if (Request::GetClean($column) == $typeText) {
                    $val = '';
                } else {
                    $val = Request::GetClean($column);
                }
                $tblFile->$column = $val;
            }
        }

        $fModules = [];
        if (Request::isset('fileModule')) $fModules = Request::GetClean('fileModule');
        if (in_array('LM', $fModules) || in_array('SS', $fModules)) {
            if (Request::isset('noOfPeopleInProperty')) {
                $value = Request::GetClean('noOfPeopleInProperty');
                $tblFile->noOfPeopleInProperty = is_numeric($value) ? (int)$value : null;
            }

        }
        if ($secondaryAgentId > 0) {
            $tblFile->secondaryBrokerNumber = $secondaryAgentId;
        } else {
            $tblFile->secondaryBrokerNumber = NULL;
        }

        $tblFile->lastUpdatedDate = Dates::Timestamp();
        $tblFile->FBRID = intval($branchId);
        $tblFile->FPCID = $PCID;
        $tblFile->LMRId = $LMRId;

        if ($LMRId > 0) {
            $FAction = 'U';
            $tblFile->Save();
        } else {
            $FAction = 'C';
            $tblFile->recordDate = Dates::Timestamp();
            $tblFile->oldFPCID = $PCID;
            $tblFile->Save();
            $LMRId = $tblFile->LMRId;
        }
        if ($publicUser && $LMRId) {
            self::saveFileAudit(
                $publicUser,
                $LMRId,
                $clientId,
                $FAction
            );
        }
        $fileResult = [];
        $fileResult['fileID']['LMRId'] = $LMRId;
        $fileResult['fileID']['affected_rows'] = 1;
        if ($saveTab == 'CI' && $LMRId) {
            $tblFileUpdateAudit = new tblFileUpdateAudit();
            $tblFileUpdateAudit->fileID = $LMRId;
            $tblFileUpdateAudit->UID = $ip['UID'];
            $tblFileUpdateAudit->UType = $userGroup;
            $tblFileUpdateAudit->recordDate = Dates::Timestamp();
            $tblFileUpdateAudit->FAction = 'U';
            $tblFileUpdateAudit->tabName = 'Client Info';
            $tblFileUpdateAudit->Save();
        }

        return $fileResult;
    }


    public static function brokerArray(
        $PCID
    ): array
    {
        $dummyEmailAgent = $PCID . '@dummyAgentemail.com';
        $dummyBrokerArray = [
            'firstName'                                     => 'null',
            'lastName'                                      => '',
            'company'                                       => '',
            'email'                                         => $dummyEmailAgent,
            'phoneNumber'                                   => '',
            'registerDate'                                  => '',
            'promoCode'                                     => '',
            'pwd'                                           => $PCID,
            'city'                                          => '',
            'cellNumber'                                    => '',
            'fax'                                           => '',
            'agreedTC'                                      => 0,
            'allowAgentToAccessLMRDocs'                     => 0,
            'listAllAgents'                                 => 0,
            'allowAgentToCreateTasks'                       => 0,
            'allowAgentToSeeDashboard'                      => 0,
            'allowedToDeleteUplodedDocs'                    => 0,
            'allowedToEditOwnNotes'                         => 0,
            'seeBilling'                                    => 0,
            'allowToSendMarketingEmailForBRBO'              => 0,
            'allowToSeeWebForms'                            => 0,
            'allowToViewMarketPlace'                        => 0,
            'allowToLockLoanFileAgent'                      => 0,
            'allowToupdateFileAndClient'                    => 0,
            'allowToSendMassEmail'                          => 0,
            'sendNewDealAlert'                              => 0,
            'allowWorkflowEdit'                             => 0,
            'allowAgentToGetBorrowerUploadDocsNotification' => 0,
            'allowEmailCampaign'                            => 0,
        ];

        // test this - 2022-12-29
        insertAffiliateInfo::insertPromoCodeIfNotExists(
            $dummyEmailAgent,
            'null',
            '',
            '',
            '',
            '',
        );

        return saveAgentInfo::getReport($dummyBrokerArray);
    }

    public static function upEmailInClientPro(
        $clientEmail,
        $PCID
    )
    {
        $sql = " 
                    UPDATE 
                        tblFile 
                    SET 
                        borrowerEmail = :borrowerEmail 
                        , enc_borrowerEmail = :enc_borrowerEmail
                        , lastUpdatedDate = :lastUpdatedDate
                    WHERE 
                        borrowerEmail = :orignalborEmail
                        AND FPCID = :FPCID
                     ";
        Database2::getInstance()->update($sql, [
            'lastUpdatedDate'   => Dates::Timestamp(),
            'orignalborEmail'   => $_REQUEST['orignalborEmail'],
            'borrowerEmail'     => HTTP::escapeQuoteForPOST($clientEmail),
            'enc_borrowerEmail' => trim(cypher::myEncryption(trim(stripslashes($clientEmail)))),
            'FPCID'             => $PCID,
        ]);
    }

    public static function updateTblFileResponse(
        $p,
        $LMRId
    )
    {
        $qryUpdate = '';
        $qs = '';
        $params = [];
        $params['LMRId'] = $LMRId;

        if (isset($_REQUEST['borrowerCallBack'])) {
            $borrowerCallBack = trim($_REQUEST['borrowerCallBack']);
            if (Dates::IsEmpty($borrowerCallBack)) {
                $borrowerCallBack = null;
            } else {
                $borrowerCallBack = trim(Dates::formatDateWithRE($borrowerCallBack, 'MDY', 'Y-m-d'));
            }
            $params['borrowerCallBack'] = $borrowerCallBack;
            $qryUpdate .= " borrowerCallBack = :borrowerCallBack ";
            $qs = ', ';
        }
        if (array_key_exists('3rdPartyFileId', $p)) {
            $params['3rdPartyFileId'] = HTTP::escapeQuoteForPOST($p['3rdPartyFileId']);

            $qryUpdate .= $qs . " 3rdPartyFileId = :3rdPartyFileId ";
            $qs = ', ';
        }
        if (array_key_exists('sbaLoanProduct', $p)) {
            $params['sbaLoanProduct'] = HTTP::escapeQuoteForPOST($p['sbaLoanProduct']);

            $qryUpdate .= $qs . " sbaLoanProduct = :sbaLoanProduct ";
            $qs = ', ';
        }

        if (isset($_REQUEST['welcomeCallDate'])) {
            $welcomeCallDate = trim($_REQUEST['welcomeCallDate']);
            if (Dates::IsEmpty($welcomeCallDate)) {
                $welcomeCallDate = null;
            } else {
                $welcomeCallDate = trim(Dates::formatDateWithRE($welcomeCallDate, 'MDY', 'Y-m-d'));
            }
            $params['welcomeCallDate'] = $welcomeCallDate;

            $qryUpdate .= $qs . " welcomeCallDate = :welcomeCallDate ";
        }

        if (isset($params['sbaLoanProduct'])) {
            $params['sbaLoanProduct'] = intval($params['sbaLoanProduct']);
        }

        if ($qryUpdate) {
            $qry = ' UPDATE tblFileResponse SET ' . $qryUpdate . '  WHERE LMRId = :LMRId ';
            Database2::getInstance()->executeQuery($qry, $params);
        }
    }

    /**
     * @param $ip
     * @return array
     */
    public static function getReport($ip)
    {
        global $userGroup;

        $glCustomLeadsReceiver = glCustomLeadsReceiver::$glCustomLeadsReceiver;

        self::$qc1 = '';
        self::$qv1 = '';
        self::$qs1 = '';
        self::$qu1 = '';

        $opt = '';
        $screenerName = '';
        $getClientInfo = [];
        $clientInfo = [];

        $newLMRId = 0;
        $responseId = 0;
        $newResponseId = 0;
        $branchId = 0;
        $PCID = 0;
        $rsL = 0;
        $agentId = 0;
        $secondaryAgentId = 0;
        $clientId = 0;
        $CID = 0;
        $clientEmail = '';
        $isLOOpt = 0;
        $leadSource = '';
        $SID = 0;
        $userGroup = '';
        $publicUser = 0;
        $UID = '';
        $isSysNotesPrivate = 0;
        $LMRId = $ip['LMRId'];

        $params = [];


        if (array_key_exists('responseId', $ip)) $responseId = $ip['responseId'];
        if (array_key_exists('agentId', $ip)) $agentId = $ip['agentId'];
        if (array_key_exists('secondaryAgentId', $ip)) $secondaryAgentId = $ip['secondaryAgentId'];
        if (array_key_exists('branchId', $ip)) $branchId = $ip['branchId'];
        if (array_key_exists('clientId', $ip)) $clientId = $ip['clientId'];
        if (array_key_exists('PCID', $ip)) $PCID = $ip['PCID'];
        if (array_key_exists('opt', $ip)) $opt = $ip['opt'];
        if (array_key_exists('SID', $ip)) $SID = $ip['SID'];
        if (array_key_exists('userGroup', $ip)) $userGroup = $ip['userGroup'];
        if (array_key_exists('publicUser', $ip)) $publicUser = $ip['publicUser'];
        if (array_key_exists('isLOOpt', $ip)) $isLOOpt = $ip['isLOOpt'];
        if (array_key_exists('UID', $ip)) $UID = $ip['UID'];
        if (array_key_exists('isSysNotesPrivate', $_REQUEST)) $isSysNotesPrivate = trim($_REQUEST['isSysNotesPrivate']);
        $formType = '';
        if (isset($ip['p']['wfOpt'])) {
            if ($ip['p']['wfOpt']) {
                $formType = cypher::myDecryption($ip['p']['wfOpt']);
            }
        }
        $formTypeView = '';
        if (isset($ip['p']['webFormView'])) {
            if ($ip['p']['webFormView']) {
                $formTypeView = $ip['p']['webFormView'];
            }
        }
        /* Get Client Information */
        if (array_key_exists('getClientInfo', $ip)) $getClientInfo = $ip['getClientInfo'];
        if (array_key_exists('clientInfo', $getClientInfo)) $clientInfo = $getClientInfo['clientInfo'][0];

        $methodOfContact = '';

        if (isset($_REQUEST['methodOfContact'])) {
            if (count($_REQUEST['methodOfContact']) > 0) {
                $methodOfContact = implode(',', $_REQUEST['methodOfContact']);
            }
        }

        $saveTab = $ip['saveTab'];

        $saveOpt = '';
        if ($saveTab == 'FP') {
            if (isset($_REQUEST['saveOpt'])) {
                $saveOpt = trim($_REQUEST['saveOpt']);
            }
        }

        $p = $ip['p'];
        /** POST Variables **/
        /** Check & Save Client record **/
        $clientResult = checkAndSaveFileClient::getReport($ip ?? []);

        if (count($clientResult) > 0) {
            $CID = trim($clientResult['CID']);
            $clientEmail = trim($clientResult['clientEmail']);
            if (!$clientEmail) {
                $clientEmail = $CID . '@dummyemail.com';
            }
        }

        if (!$agentId) {
            //$agentId = getDefaultBranchAgent::getReport($ip ?? []);
            if (!$ip['p']['dummyBrokerId']) {
                $brokerArray = self::brokerArray($PCID);
                if (count($brokerArray) > 0) {
                    $agentId = 0;
                    if (array_key_exists('brokerNumber', $brokerArray)) {
                        $agentId = $brokerArray['brokerNumber'];
                    }
                    if ($agentId) {
                        savePreferredAgentForBranch::getReport([
                            'branchId' => $branchId,
                            'agentId'  => $agentId,
                            'PCID'     => $PCID,
                        ]);
                        saveAgentPCs::getReport([
                            'PCID'         => $PCID,
                            'brokerNumber' => $agentId,
                        ]);
                    }
                }
            } else {
                if (isset($ip['p']['dummyBrokerId'])) {
                    if ($ip['p']['dummyBrokerId'] && $ip['p']['dummyBrokerId'] != '0') {
                        $agentId = $ip['p']['dummyBrokerId'];
                    }
                }
            }
        }

        $result = self::result(
            $agentId,
            $CID,
            $clientId,
            $userGroup,
            $saveTab,
            $publicUser,
            $formType,
            $formTypeView,
            $UID,
            $clientInfo,
            $clientEmail,
            $isLOOpt,
            $ip,
            $saveOpt,
            $secondaryAgentId,
            $LMRId,
            $branchId,
            $PCID
        );

        if (count($result) > 0) {
            if (array_key_exists('fileID', $result)) {
                if (!$LMRId) {
                    $newLMRId = trim($result['fileID']['LMRId']);
                }
                $rsL = 1;
            }
        }

        if ($_REQUEST['upEmailInClientPro'] == 'Yes') {              /* Updated all the old loan files for existing email card 188*/
            self::upEmailInClientPro($clientEmail, $PCID);
        }

        if ($newLMRId == 0 && $LMRId == 0) {
            return [
                'LMRId'      => 0,
                'upCnt'      => 0,
                'responseId' => 0,
            ];
            /** LMR Record fails **/
        }

        if ($newLMRId > 0) {
            $ip['LMRId'] = $newLMRId;
            $ip['newLMRId'] = $newLMRId;
            self::saveFileAudit(
                $publicUser,
                $newLMRId,
                $clientId,
                'C'
            );
        }
        if ($saveTab == 'QAF') {
            return ['LMRId' => $LMRId];
        }

        $LMRClientType = '';
        $moduleCode = [];
        if ($opt == 'CFPB' || $opt == 'LSR' || ($userGroup == 'Client' && $LMRId > 0 && $_SERVER['REQUEST_URI'] !== '/DoItUrSelf/saveBorrowerInfo.php')) {
            // make exception for do it yourself
            doNothing();
        } elseif ($opt == 'joinder') {
            if ($newLMRId > 0) {
                $ip['LMRClientType'] = $LMRClientType = 'JL';
                $ip['moduleCode'] = ['LM'];
                saveFileModulesAndServices::getReport($ip);
                /** Services Requested & if SS update hardship type **/
            }
        } elseif ($opt == 'SSREG') {
            if ($newLMRId > 0) {
                $ip['LMRClientType'] = $LMRClientType = 'SS';
                $ip['moduleCode'] = ['SS'];
                saveFileModulesAndServices::getReport($ip);
                /** Services Requested & if SS update hardship type **/
            }
        } elseif ($opt == 'SLM') {
            if (isset($_REQUEST['LMRClientType'])) $LMRClientType = is_array($_REQUEST['LMRClientType']) ? ($_REQUEST['LMRClientType'][0] ?? null) : $_REQUEST['LMRClientType'];
            if (isset($_REQUEST['fileModule'])) $moduleCode = $_REQUEST['fileModule'];
            if ($newLMRId > 0) {
                $LMRClientType = 'SLM';
                $moduleCode = ['SLM'];
            }
            $ip['LMRClientType'] = $LMRClientType;
            $ip['moduleCode'] = $moduleCode;
            saveFileModulesAndServices::getReport($ip);
            /** Services Requested & if SS update hardship type **/
        } elseif ($saveTab == 'LR') {
            /*
* Ticket: https://trello.com/c/p1OhdNkZ
* Date  : January 15, 2019
* Description : Add Service Type & File type.
*/
            $LMRClientType = '';
            $fileType = [];

            if (isset($_REQUEST['loanPrograms'])) {                                            // | What kind of program are you looking for?

                if (is_array($_REQUEST['loanPrograms'])) {
                    Debug(['error' => 'loanPrograms is an array', $_REQUEST]);
                    $LMRClientType = $_REQUEST['loanPrograms'];
                } else {
                    $LMRClientType = $_REQUEST['loanPrograms'];
                }
            }

            if (isset($_REQUEST['fileType'])) {                                                // | File Type

                if (is_array($_REQUEST['fileType'])) {
                    $fileType = $_REQUEST['fileType'];
                } else {
                    $fileType = [$_REQUEST['fileType']];
                }
            }

            $ip['LMRClientType'] = $LMRClientType;
            $ip['moduleCode'] = $fileType;
            saveFileModulesAndServices::getReport($ip);       //Services Requested
        } else {
            $LMRClientType = '';
            $moduleCode = [];
            if (isset($_REQUEST['LMRClientType'])) $LMRClientType = is_array($_REQUEST['LMRClientType']) ? ($_REQUEST['LMRClientType'][0] ?? null) : $_REQUEST['LMRClientType'];
            if (isset($_REQUEST['fileModule'])) $moduleCode = $_REQUEST['fileModule'];

            if($ip['p']['tabOpt'] == 1003 && !$moduleCode){
                $moduleCode = $ip['p']['ft'];
            }

            $ip['LMRClientType'] = $LMRClientType;
            $ip['moduleCode'] = $moduleCode;
            if ($saveTab != 'HR' && $saveTab != 'INT') {
                saveFileModules::getReport($ip);
                /** Modules Requested **/
                saveFileServices::getReport($ip);
                /** Services Requested & if SS update hardship type **/
            }
        }

//        Debug('meh');


        /** Response Table Save **/

        $crntDate = date('Y-m-d');

        if (isset($_REQUEST['LMRInternalLoanProgram']) || isset($_REQUEST['LMRInternalLoanProgramShadow'])) {
            $ip['LMRInternalLoanProgram'] = $_REQUEST['LMRInternalLoanProgram'];
            saveFileInternalLoanPrograms::getReport($ip);
        }

        /* Additional Loan Program saving start*/
        $LMRadditionalLoanProgram = [];
        if (isset($_REQUEST['LMRadditionalLoanProgram'])) {
            $LMRadditionalLoanProgram = $_REQUEST['LMRadditionalLoanProgram'];
        }
        $ip['LMRadditionalLoanProgram'] = $LMRadditionalLoanProgram;
        saveFileAdditionalLoanPrograms::getReport($ip);
        /* Additional Loan Program saving end */

        if ($LMRId > 0 && ($saveTab == 'HMLO' || $saveTab == 'CI')) {
            /** Updated 08-02-2019 Lead Source Issue */
            if (array_key_exists('leadSource', $ip)) {
                $qry = " UPDATE tblFileResponse SET leadSource = :leadSource WHERE LMRId = :LMRId ";
                Database2::getInstance()->executeQuery($qry, [
                    'leadSource' => HTTP::escapeQuoteForPOST($ip['leadSource']),
                    'LMRId'      => $LMRId,
                ]);
            }
        }

        if ($LMRId > 0 && $saveTab == 'CI') {
            self::updateTblFileResponse(
                $p,
                $LMRId
            );
        }

        if ($newLMRId > 0) {
            $newResponseId = self::newLMRId(
                $PCID,
                $glCustomLeadsReceiver,
                $saveTab,
                $saveOpt,
                $newLMRId,
                $ip,
                $branchId,
                $p
            );
        } elseif ($responseId > 0) {
            self::existingLMRId(
                $branchId,
                $saveTab,
                $ip,
                $responseId,
                $userGroup,
                $isSysNotesPrivate,
                $LMRId,
                $p
            );
        }

        if ($newLMRId > 0) {
            updateFileLastUpdatedDate::getReport(['fileID' => $newLMRId]);
        }
        if ($agentId > 0) {
            /** Check whether Agent is preferred to the branch if NOT add him as a preferred agents. **/

            $qry = '
SELECT serialNumber
FROM tblAgentBranch 
WHERE brokerNumber = :brokerNumber 
  AND BID = :BID 
  AND activeStatus = :activeStatus
';
            $resultArray = Database2::getInstance()->queryData($qry, [
                'brokerNumber' => $agentId,
                'BID'          => $branchId,
                'activeStatus' => 1,
            ]);
            if (!count($resultArray) && $branchId) {
                $q = " 
INSERT INTO tblAgentBranch
    (
     BID
     , brokerNumber
     , contactDate
     , PCID
     ) VALUES (
         :branchId
         , :agentId
         , :crntDate
         , :PCID
     )
                       ";
                Database2::getInstance()->insert($q, [
                    'branchId' => $branchId,
                    'agentId'  => $agentId,
                    'crntDate' => $crntDate,
                    'PCID'     => $PCID,
                ]);
            }
        }

        if ($secondaryAgentId > 0) {
            /** Check whether Agent is preferred to the branch if NOT add him as a preferred agents. **/

            $qry = '
                SELECT serialNumber 
                FROM tblAgentBranch
                    WHERE brokerNumber = :brokerNumber
                      AND BID = :BID 
                      AND activeStatus = :activeStatus
                ';
            $resultArray = Database2::getInstance()->queryData($qry, [
                'brokerNumber' => $secondaryAgentId,
                'BID'          => $branchId,
                'activeStatus' => 1,
            ]);
            if (!count($resultArray) && $branchId) {
                $q = " 
INSERT INTO tblAgentBranch(BID, brokerNumber, contactDate, PCID)
                         VALUES (
                                 :branchId
                                 , :secondaryAgentId
                                 , :crntDate
                                 , :PCID
                                 )
                       ";
                Database2::getInstance()->insert($q, [
                    'branchId'         => $branchId,
                    'secondaryAgentId' => $secondaryAgentId,
                    'crntDate'         => $crntDate,
                    'PCID'             => $PCID,
                ]);
            }
        }

        if ($saveTab == 'CI' || $saveTab == 'SLM' || $saveTab == 'QA' || $saveTab == 'HR'
            || $saveTab == 'INT' || $saveTab == 'LR' || $saveTab == 'US' || $saveTab == 'FU'
            || $saveTab == 'LSR' || $saveTab == 'HMLO' || $saveOpt == 'SVV') {

            /** Save International phone numbers **/

            $LMRId = $ip['LMRId'];
        }
        $fileModule = [];
        if (isset($_REQUEST['fileModule'])) {
            $fileModule = $_REQUEST['fileModule'];
        }

        if (in_array('FU', $fileModule)) {
            $LMRId = $ip['LMRId'];
        } elseif ($saveTab == 'CI' || $saveTab == 'SLM' || $saveTab == 'QA' || $saveTab == 'HR' || $saveTab == 'INT'
            || $saveTab == 'LR' || $saveTab == 'US' || $saveTab == 'HMLO' || $saveOpt == 'SVV') {
            $CTArray = [
                'worldPhone',
                'worldPhoneCoB',
                'taxes1',
                'insurance1',
                'mortgageInsurance1',
                'HOAFees1',
                'floodInsurance1', 'pastDueMtg',
                'pastDueHOA', 'escrowAdvances', 'projectedEscrowAdvances',
                'borrowerPinNo', 'coBorrowerPinNo',
                'preQualifierName', 'driverLicenseNo',
                'borrowerMaidenName', 'noticeAccelerationDate',
                'driverLicenseState', 'gender', 'timeToContact',
                'bestTime', 'transferOfServicingDate',
                'coMethodOfContact', 'coTimeToContact',
                'coBestTime', 'FSAID', 'chA1', 'chA2',
                'chA3', 'chA4', 'loanReliefObjective',
                'significantDate', 'processingEmail',
                'borrowerMInitial', 'chQ1', 'chQ2',
                'chQ3', 'chQ4', 'processingPassword',
                'IPAddress',

            ];
            /** Child table Variables **/
            for ($f = 0; $f < count($CTArray); $f++) {
                ${$CTArray[$f]} = '';
                if (isset($_REQUEST["$CTArray[$f]"])) {
                    ${$CTArray[$f]} = trim($_REQUEST["$CTArray[$f]"]);
                }
            }
            $params = [];


            if ($publicUser == 1 && (in_array($PCID, CONST_API_ACCESS_PC))) {
                $streetName = $_REQUEST['streetName'] ?? '';
                $streetNumber = $_REQUEST['streetNumber'] ?? '';
                $streetType = $_REQUEST['streetType'] ?? '';

                $_REQUEST['presentAddress'] = $streetNumber . ' ' . $streetName . ' ' . $streetType;

                $sqry = 'SELECT * FROM tblFileStreetData WHERE LMRID = :LMRID ; ';
                $sresultArray = Database2::getInstance()->queryData($sqry, [
                    'LMRID' => $ip['LMRId'],
                ]);

                if (count($sresultArray)) {

                    //update
                    $supdateqry = ' update tblFileStreetData set ';
                    $comasep = '';
                    if (isset($_REQUEST['streetName'])) {
                        $supdateqry .= " streetName = :streetName ";
                        $comasep = ',';
                    }
                    if (isset($_REQUEST['streetNumber'])) {
                        $supdateqry .= $comasep . " streetNumber = :streetNumber ";
                        $comasep = ',';
                    }
                    if (isset($_REQUEST['streetType'])) {
                        $supdateqry .= $comasep . " streetType = :streetType ";
                    }
                    $supdateqry .= ' where  LMRID = :LMRId ; ';
                    if ($supdateqry) {
                        Database2::getInstance()->update($supdateqry, [
                            'streetName'   => $streetName,
                            'streetNumber' => $streetNumber,
                            'streetType'   => $streetType,
                            'LMRId'        => $ip['LMRId'],
                        ]);
                    }
                } else {
                    $sinsertQry = '
insert into tblFileStreetData(LMRID,streetName,streetNumber,streetType
) values(
         :LMRId
         , :streetName
         , :streetNumber
         , :streetType
         ) 
     ';
                    Database2::getInstance()->insert($sinsertQry, [
                        'streetName'   => $streetName,
                        'streetNumber' => $streetNumber,
                        'streetType'   => $streetType,
                        'LMRId'        => $ip['LMRId'],
                    ]);
                }
            }

            $tblFile2 = tblFile2::Get([tblFile2_db::COLUMN_LMRID=>$LMRId]) ?? new tblFile2();
            if ($LMRClientType == 'HOA' && $saveTab == 'CI' && $publicUser != 1) {
            } else {
                $file2FieldsArray = [
                    'worldPhone', 'worldPhoneCoB', 'borrowerPinNo', 'coBorrowerPinNo',
                    'preQualifierName', 'driverLicenseNo', 'borrowerMaidenName',
                    'driverLicenseState', 'gender', 'timeToContact', 'bestTime',
                    'coMethodOfContact', 'coTimeToContact', 'coBestTime', 'FSAID',
                    'chA1', 'chA2', 'chA3', 'chA4', 'loanReliefObjective',
                    'processingEmail', 'borrowerMInitial', 'chQ1', 'chQ2', 'chQ3', 'chQ4',
                    'processingPassword', 'nonBorrowerName', 'nonBorrowerEmail',
                    'monthlyContribution', 'isNonBorrower', 'IPAddress', 'presentAddress',
                    'presentCity', 'presentState', 'presentCounty', 'presentZip', 'presentPropLengthTime',
                    'previousPropLengthTime', 'coBPresentAddress', 'coBPresentCity',
                    'coBPresentState', 'coBPresentZip', 'coBNoOfDependent', 'borPresentPropType',
                    'borMailingPropType', 'borFormerPropType', 'coBPresentPropType',
                    'coBMailingPropType', 'coBFormerPropType', 'mailingAddrAsPresent',
                    'coBorMailingAddrAsPresent', 'guarantorNotes', 'presentPropLengthTimeCoBor',
                    'presentUnit', 'presentCountry', 'presentPropLengthMonths', 'currentRPM',
                    'previousUnit', 'previousCountry', 'previousPropLengthMonths', 'previousRPM',
                    'cobor_guarantee'
                ];
                /** DIRECT Variables **/

                if (isset($_REQUEST['noticeAccelerationDate'])) {
                    $noticeAccelerationDate = trim($_REQUEST['noticeAccelerationDate']);
                    if (Dates::IsEmpty($noticeAccelerationDate)) {
                        $noticeAccelerationDate = null;
                    } else {
                        $noticeAccelerationDate = trim(Dates::formatDateWithRE($noticeAccelerationDate, 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->noticeAccelerationDate = $noticeAccelerationDate;
                }
                if (isset($_REQUEST['transferOfServicingDate'])) {
                    $transferOfServicingDate = trim($_REQUEST['transferOfServicingDate']);
                    if (Dates::IsEmpty($transferOfServicingDate)) {
                        $transferOfServicingDate = null;
                    } else {
                        $transferOfServicingDate = trim(Dates::formatDateWithRE($transferOfServicingDate, 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->transferOfServicingDate = $transferOfServicingDate;
                }
                if (isset($_REQUEST['significantDate'])) {
                    $significantDate = trim($_REQUEST['significantDate']);
                    if (Dates::IsEmpty($significantDate)) {
                        $significantDate = null;
                    } else {
                        $significantDate = trim(Dates::formatDateWithRE($significantDate, 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->significantDate = $significantDate;
                }
                if (isset($_REQUEST['nonBorrowerDOB'])) {
                    $nonBorrowerDOB = trim($_REQUEST['nonBorrowerDOB']);
                    if (Dates::IsEmpty($nonBorrowerDOB)) {
                        $nonBorrowerDOB = null;
                    } else {
                        $nonBorrowerDOB = trim(Dates::formatDateWithRE($nonBorrowerDOB, 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->nonBorrowerDOB = $nonBorrowerDOB;
                }

                if (isset($_REQUEST['nonBorrowerSSN'])) {
                    $nonBorrowerSSN = trim(str_replace(' - ', '', $_REQUEST['nonBorrowerSSN']));
                    $tblFile2->nonBorrowerSSN = $nonBorrowerSSN;
                }
                if (isset($_REQUEST['methodOfContact']) && $_REQUEST['methodOfContact'] != '') {
                    $methodOfContact = implode(',', $_REQUEST['methodOfContact']);
                    $tblFile2->methodOfContact = $methodOfContact;
                    if ($methodOfContact) {
                        self::$qc1 .= self::$qs1 . ' methodOfContact ';
                        self::$qv1 .= self::$qs1 . " '" . trim($methodOfContact) . "' ";
                        self::$qu1 .= self::$qs1 . " methodOfContact = '" . trim($methodOfContact) . "' ";
                        self::$qs1 = ', ';
                    }
                } elseif (isset($_REQUEST['methodOfContactHidden'])) {
                    $methodOfContact = '';
                    $tblFile2->methodOfContact = $methodOfContact;
                }
                if (isset($_REQUEST['coborLicenseIssuance'])) {
                    $coborLicenseIssuance = trim($_REQUEST['coborLicenseIssuance']);
                    if (Dates::IsEmpty($coborLicenseIssuance)) {
                        $coborLicenseIssuance = null;
                    } else {
                        $coborLicenseIssuance = trim(Dates::formatDateWithRE($coborLicenseIssuance, 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->coborLicenseIssuance = $coborLicenseIssuance;
                }
                if (isset($_REQUEST['coborLicenseExpiration'])) {
                    $coborLicenseExpiration = trim($_REQUEST['coborLicenseExpiration']);
                    if (Dates::IsEmpty($coborLicenseExpiration)) {
                        $coborLicenseExpiration = null;
                    } else {
                        $coborLicenseExpiration = trim(Dates::formatDateWithRE($coborLicenseExpiration, 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->coborLicenseExpiration = $coborLicenseExpiration;
                }
                /**
                 * Client Address To Borrower Address
                 */
                $clientAddrFields = [
                    'presentAddress' => 'clientAddress',
                    'presentCity'    => 'clientCity',
                    'presentState'   => 'clientState',
                    'presentZip'     => 'clientZip',
                ];
                $currencyFieldArray = ['currentRPM', 'previousRPM'];

                if (isset($_REQUEST['presentPropLengthMonths'])) {
                    $_REQUEST['presentPropLengthMonths'] = intval($_REQUEST['presentPropLengthMonths']);
                }

                if (isset($_REQUEST['previousPropLengthMonths'])) {
                    $_REQUEST['previousPropLengthMonths'] = intval($_REQUEST['previousPropLengthMonths']);
                }

                foreach ($file2FieldsArray as $f => $column) {
                    if (isset($_REQUEST[$column])) {
                        if (in_array($column, $currencyFieldArray)) {
                            $postVal = trim(Strings::replaceCommaValues($_REQUEST["$column"]));
                        } else {
                            $postVal = trim($_REQUEST["$column"]);
                        }
                        $tblFile2->$column = $postVal;

                        if ($postVal && array_key_exists($column, $clientAddrFields)) {
                            $colname = $clientAddrFields[$column];
                            self::$qc1 .= self::$qs1 . " $colname ";
                            self::$qv1 .= self::$qs1 . " '" . $postVal . "' ";
                            self::$qu1 .= self::$qs1 . " $colname = '" . $postVal . "' ";
                            self::$qs1 = ', ';
                        }
                    } elseif (array_key_exists($column, $clientAddrFields)) {
                        if (array_key_exists($clientAddrFields[$column], $clientInfo)) {
                            $tF = $clientAddrFields[$column];
                            if (trim($clientInfo[$tF])) {
                                $postVal = trim($clientInfo[$tF]);
                                $tblFile2->$column = $postVal;
                            }
                        }
                    }
                }
                if($saveTab == 'LR'){
                    $borrowerAddressFieldArray = [
                        'city' => 'presentCity',
                        'state' => 'presentState',
                        'zip' => 'presentZip',
                    ];
                    foreach ($borrowerAddressFieldArray as $f => $column) {
                        if (Request::isset($f)) {
                            $postVal = Request::GetClean("$f");
                            $tblFile2->$column = $postVal;
                        }
                    }
                }
                if (Request::isset('address')) {
                    $postVal = Request::GetClean('address');
                    $tblFile2->presentAddress = $postVal;
                }

                if(!$tblFile2->noticeAccelerationDate) {
                    $tblFile2->noticeAccelerationDate = null;
                }

                if(!$tblFile2->transferOfServicingDate) {
                    $tblFile2->transferOfServicingDate = null;
                }

                if(!$tblFile2->coborLicenseIssuance) {
                    $tblFile2->coborLicenseIssuance = null;
                }

                if(!$tblFile2->coborLicenseExpiration) {
                    $tblFile2->coborLicenseExpiration = null;
                }

                if(Request::isset('coBorrowerMName')) {
                    $tblFile2->coBorrowerMName = Request::GetClean('coBorrowerMName');
                }
                if(Request::isset('coBorrowerUnit')) {
                    $tblFile2->coBorrowerUnit = Request::GetClean('coBorrowerUnit');
                }
                if(Request::isset('coBorrowerCounty')) {
                    $tblFile2->coBorrowerCounty = Request::GetClean('coBorrowerCounty');
                }
                if(Request::isset('coBorrowerMailingUnit')) {
                    $tblFile2->coBorrowerMailingUnit = Request::GetClean('coBorrowerMailingUnit');
                }
                if(Request::isset('coBorrowerMailingCounty')) {
                    $tblFile2->coBorrowerMailingCounty = Request::GetClean('coBorrowerMailingCounty');
                }
                if(Request::isset('coBorrowerPreviousUnit')) {
                    $tblFile2->coBorrowerPreviousUnit = Request::GetClean('coBorrowerPreviousUnit');
                }
                if(Request::isset('coBorrowerPreviousCounty')) {
                    $tblFile2->coBorrowerPreviousCounty = Request::GetClean('coBorrowerPreviousCounty');
                }

                if(Request::isset('driverLicenseIssuanceDate')){
                    if (Dates::IsEmpty(Request::GetClean('driverLicenseIssuanceDate'))) {
                        $driverLicenseIssuanceDate =  null;
                    } else {
                        $driverLicenseIssuanceDate = trim(Dates::formatDateWithRE(Request::GetClean('driverLicenseIssuanceDate'), 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->driverLicenseIssuanceDate = $driverLicenseIssuanceDate;
                }
                if(Request::isset('driverLicenseExpirationDate')){
                    if (Dates::IsEmpty(Request::GetClean('driverLicenseExpirationDate'))) {
                        $driverLicenseExpirationDate =  null;
                    } else {
                        $driverLicenseExpirationDate = trim(Dates::formatDateWithRE(Request::GetClean('driverLicenseExpirationDate'), 'MDY', 'Y-m-d'));
                    }
                    $tblFile2->driverLicenseExpirationDate = $driverLicenseExpirationDate;
                }
                if(!$tblFile2->LMRID){
                    $tblFile2->LMRID = $LMRId;
                    $tblFile2->IPAddress = $_SERVER['REMOTE_ADDR'];
                }
                $tblFile2->Save();

            }
            /** Save Income info **/

            $q = 'SELECT IID FROM tblIncomeInfo WHERE LMRId = :LMRId ';
            $sqlParams = [
                'LMRId' => $LMRId,
            ];
            $resultArray = Database2::getInstance()->queryData($q, $sqlParams, null, true);
            $incomeFieldsArray = [
                'taxes1', 'insurance1',
                'mortgageInsurance1', 'HOAFees1',
                'floodInsurance1', 'pastDueMtg',
                'pastDueHOA', 'projectedEscrowAdvances',
                'borLineOfWorkProfession',
                'cobLineOfWorkProfession',
            ];
            /** DIRECT Variables **/

            $qu = '';
            $qs = '';
            $qc = '';
            $qv = '';
            $params = [];
            $params['LMRId'] = $LMRId;

            foreach ($incomeFieldsArray as $f => $column) {
                if (isset($_REQUEST[$column])) {
                    $params[$column] = trim($_REQUEST[$column]);

                    $qc .= $qs . " $column ";
                    $qv .= $qs . " :$column ";
                    $qu .= $qs . " $column = :$column ";
                    $qs = ', ';
                }
            }

            if (isset($params['noUnitsOccupied'])) {
                $params['noUnitsOccupied'] = intval($params['noUnitsOccupied']);
            }

            if (isset($params['borLineOfWorkProfession'])) {
                $params['borLineOfWorkProfession'] = intval($params['borLineOfWorkProfession']);
            }

            if (isset($params['cobLineOfWorkProfession'])) {
                $params['cobLineOfWorkProfession'] = intval($params['cobLineOfWorkProfession']);
            }

            if (count($resultArray) > 0) {
                if ($qu) {
                    $qry = ' UPDATE tblIncomeInfo SET ' . $qu . ' WHERE LMRId = :LMRId ;';
                    Database2::getInstance()->update($qry, $params);
                }
            } elseif ($qc && $qv) {
                $qry = ' INSERT INTO tblIncomeInfo (LMRId, ' . $qc . ") VALUES ( :LMRId , " . $qv . ');';
                Database2::getInstance()->insert($qry, $params);
            }


            if (isset($_REQUEST['zillowValue']) && $LMRId > 0) { // Zillow Value Save - https://www.pivotaltracker.com/story/show/161583314
                $q = 'SELECT SSID FROM tblShortSale WHERE LMRId = :LMRId ';
                $shortsaleresArray = Database2::getInstance()->queryData($q, [
                    'LMRId' => $LMRId,
                ], null, true);
                if (count($shortsaleresArray)) {

                    $qryU = " UPDATE tblShortSale SET zillowValue = :zillowValue where LMRId = :LMRId ";
                    Database2::getInstance()->update($qryU, [
                        'zillowValue' => Strings::replaceCommaValues($_REQUEST['zillowValue']),
                        'LMRId'       => $LMRId,
                    ]);
                } else {
                    $qry = " INSERT INTO tblShortSale (LMRId, zillowValue) VALUES ( :LMRId, :zillowValue );";
                    Database2::getInstance()->insert($qry, [
                        'zillowValue' => Strings::replaceCommaValues($_REQUEST['zillowValue']),
                        'LMRId'       => $LMRId,
                    ]);
                }
            }

            if ($saveTab == 'LR') {
                $tblShortSale = tblShortSale::Get(['LMRId' => $LMRId]) ?? new tblShortSale();
                $tblShortSale->LMRId = $LMRId;
                if (Request::isset('costBasis')) {
                    $tblShortSale->costBasis = Strings::replaceCommaValues(Request::GetClean('costBasis'));
                }
                if (Request::isset('costSpent')) {
                    $tblShortSale->costSpent = Strings::replaceCommaValues(Request::GetClean('costSpent'));
                }
                $tblShortSale->save();
            }


            /**
             ** Description    : Save Hard / Private Money LOS info
             ** Developer    : Viji & Venkatesh
             ** Author        : AwataSoftsys
             ** Date            : Nov 21, 2016
             **/
            if (in_array('HMLO', $fileModule)) {
                $fieldsUpdateCount = 0;
                $HMLOFieldsArray = [
                    'borCompanyName',
                    'borCreditScoreRange',
                    'midFicoScore',
                    'borCreditScore',
                    'coBorCompanyName',
                    'coBorCreditScoreRange',
                    'coBorCreditScore',
                    'rehabCost',
                    'afterRepairValue',
                    'rehabDuration',
                    'borrowerCitizenship',
                ];
                $tblFileHMLO = tblFileHMLO::get(['fileID' => $LMRId]) ?? new tblFileHMLO();
                $tblFileHMLO->fileID = $LMRId;
                if (!$tblFileHMLO->HMLOID) {
                    $tblFileHMLO->recordDate = Dates::Timestamp();
                }
                foreach ($HMLOFieldsArray as $f => $column) {
                    if (isset($_REQUEST[$column])) {
                        if ($column == 'rehabCost') {
                            $tblFileHMLO->$column = trim(Strings::replaceCommaValues($_REQUEST[$column]));
                        } else {
                            $tblFileHMLO->$column = trim($_REQUEST[$column]);
                        }
                        $fieldsUpdateCount++;
                    }
                }
                $tblFileHMLO->authorizationStatus = Request::isset('authorizationStatus')
                    ? (intval(Request::GetClean('authorizationStatus')) ?: null)
                    : $tblFileHMLO->authorizationStatus;
                if(Request::isset('authorizationStatus')) {
                    $fieldsUpdateCount++;
                }
                if ($fieldsUpdateCount) {
                    $tblFileHMLO->save();
                }
            }
        }
        if ((isset($_REQUEST['loanOriginationDate'])) && (
                $saveTab == 'CI'
                || $saveTab == 'HR'
                || $saveTab == 'INT'
                || $saveTab == 'LR'
                || $saveTab == 'HMLO'
            ) && $LMRId > 0) {

            $noteDate = trim($_REQUEST['loanOriginationDate']);
            if (Dates::IsEmpty($noteDate)) {
                $noteDate = null;
            } elseif ($saveTab == 'LR') {
                $noteDate = trim(Dates::formatDateWithRE($noteDate, 'MY', 'Y-m-d')); /* Intake tab of Client Document PC */
            } else {
                $noteDate = trim(Dates::formatDateWithRE($noteDate, 'MDY', 'Y-m-d'));
            }


            if ($SID > 0) {
                $qry = " UPDATE tblRestInfo SET noteDate = :noteDate WHERE SID = :SID ";
                Database2::getInstance()->executeQuery($qry, [
                    'noteDate' => $noteDate,
                    'SID'      => $SID,
                ]);
            } else {
                $qry = " insert INTO tblRestInfo (LMRId, noteDate) values ( :LMRId , :noteDate ) ";

                Database2::getInstance()->executeQuery($qry, [
                    'LMRId'    => $LMRId,
                    'noteDate' => $noteDate,
                ]);
            }
        }

        if ((isset($_REQUEST['loanOriginationDate'])) && $saveTab == 'FP' && $newLMRId > 0) {
            /** Fraud Stopper Intake form **/

            $noteDate = trim($_REQUEST['loanOriginationDate']);
            if (Dates::IsEmpty($noteDate)) {
                $noteDate = null;
            } else {
                $noteDate = trim(Dates::formatDateWithRE($noteDate, 'MDY', 'Y-m-d'));
            }

            $qry = " INSERT INTO tblRestInfo (LMRId, noteDate) VALUES ( :LMRId , :noteDate ) ";
            Database2::getInstance()->executeQuery($qry, [
                'LMRId'    => $newLMRId,
                'noteDate' => $noteDate,
            ]);
        }

        $lenderNames = [];
        /** Save lender info with Un-approve status to DB if New **/
        if (trim($servicer1)) {
            $lenderNames[] = $servicer1;
        }
        if (trim($servicer2)) {
            $lenderNames[] = $servicer2;
        }
        if (trim($originalLender1)) {
            $lenderNames[] = $originalLender1;
        }
        if (trim($originalLender2)) {
            $lenderNames[] = $originalLender2;
        }
        if (isset($_REQUEST['lender1'])) {
            $lenderNames[] = trim($_REQUEST['lender1']);
        }
        if (isset($_REQUEST['lender2'])) {
            $lenderNames[] = trim($_REQUEST['lender2']);
        }
        if (count($lenderNames) > 0) {
            saveNewLender::getReport(['lender' => $lenderNames]);
        }

        // Stop the automatic case email for PC = Fraud Stoppers 1368 (15 Jul, 2016)
        // Email to Fraud Stoppers Client when creating New File (1368 => Fraud Stoppers, 820 => dave, 2=>awata )*/
        // Please remove the automatic welcome email for PC = Fraud Stoppers (25 May,2016)
        // Added the automatic welcome email for PC = Fraud Stoppers (1 Aug, 2016)
        // Please remove the automatic welcome email for PC = Fraud Stoppers (07 Sep,2016)
        // Added the automatic welcome email for PC = Fraud Stoppers (Dec 16, 2016)
        // only Allowed the welcome email for Branch = 4190 (Fraud Stoppers PMA)(Dec 20, 2016)
        // We have Discontinue Fraud Stoppers welcome email for Branch = 4190 (Fraud Stoppers PMA)(May 06, 2017)
        // Added the automatic welcome email for PC = Fraud Stoppers (June 05, 2017)
        // Disable welcome email for PC = Fraud Stoppers - Pivotal # - 151051702 (Sept 14, 2017)
        /*
Add the automatic welcome email for PC = Fraud Stoppers 1368 (28 Nov, 2017) Pivotal # - 153188777
Disabled welcome email for PC = Fraud Stoppers - Pivotal # - 155406202 (Feb 27, 2018)

          }*/

        /*deepthi code for borrower sync Start*/

        /*if (isset($_REQUEST['PublishBInfo']) && trim($_REQUEST['PublishBInfo'])) {
            self::$params['PublishBInfo'] = HTTP::escapeQuoteForPOST($_REQUEST['PublishBInfo']);
            self::$qc1 .= self::$qs1 . ' publishBInfo ';
            self::$qv1 .= self::$qs1 . " :PublishBInfo ";
            self::$qu1 .= self::$qs1 . " publishBInfo = :PublishBInfo ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['BEthnicity']) && trim($_REQUEST['BEthnicity'])) {
            self::$params['BEthnicity'] = HTTP::escapeQuoteForPOST($_REQUEST['BEthnicity']);
            self::$qc1 .= self::$qs1 . ' ethnicity ';
            self::$qv1 .= self::$qs1 . " :BEthnicity ";
            self::$qu1 .= self::$qs1 . " ethnicity = :BEthnicity ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['BRace']) && trim($_REQUEST['BRace'])) {
            self::$params['BRace'] = HTTP::escapeQuoteForPOST($_REQUEST['BRace']);
            self::$qc1 .= self::$qs1 . ' race ';
            self::$qv1 .= self::$qs1 . " :BRace ";
            self::$qu1 .= self::$qs1 . " race = :BRace ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['BGender']) && trim($_REQUEST['BGender'])) {
            self::$params['BGender'] = HTTP::escapeQuoteForPOST($_REQUEST['BGender']);
            self::$qc1 .= self::$qs1 . ' gender ';
            self::$qv1 .= self::$qs1 . " :BGender ";
            self::$qu1 .= self::$qs1 . " gender = :BGender ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['BVeteran']) && trim($_REQUEST['BVeteran'])) {
            self::$params['BVeteran'] = HTTP::escapeQuoteForPOST($_REQUEST['BVeteran']);
            self::$qc1 .= self::$qs1 . ' veteran ';
            self::$qv1 .= self::$qs1 . " :BVeteran ";
            self::$qu1 .= self::$qs1 . " veteran = :BVeteran ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiEthnicity']) && trim($_REQUEST['bFiEthnicity'])) {
            self::$params['bFiEthnicity'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiEthnicity']);
            self::$qc1 .= self::$qs1 . ' FIEthnicity ';
            self::$qv1 .= self::$qs1 . " :bFiEthnicity ";
            self::$qu1 .= self::$qs1 . " FIEthnicity = :bFiEthnicity ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiEthnicitySub']) && trim($_REQUEST['bFiEthnicitySub'])) {
            self::$params['bFiEthnicitySub'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiEthnicitySub']);
            self::$qc1 .= self::$qs1 . ' FIEthnicitySub ';
            self::$qv1 .= self::$qs1 . " :bFiEthnicitySub ";
            self::$qu1 .= self::$qs1 . " FIEthnicitySub = :bFiEthnicitySub ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiEthnicitySubOther']) && trim($_REQUEST['bFiEthnicitySubOther'])) {
            self::$params['bFiEthnicitySubOther'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiEthnicitySubOther']);
            self::$qc1 .= self::$qs1 . ' FIEthnicitySubOther ';
            self::$qv1 .= self::$qs1 . " :bFiEthnicitySubOther ";
            self::$qu1 .= self::$qs1 . " FIEthnicitySubOther = :bFiEthnicitySubOther ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiSex']) && trim($_REQUEST['bFiSex'])) {
            self::$params['bFiSex'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiSex']);
            self::$qc1 .= self::$qs1 . ' FISex ';
            self::$qv1 .= self::$qs1 . " :bFiSex ";
            self::$qu1 .= self::$qs1 . " FISex = :bFiSex ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiRace']) && trim($_REQUEST['bFiRace'])) {
            self::$params['bFiRace'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiRace']);
            self::$qc1 .= self::$qs1 . ' FIRace ';
            self::$qv1 .= self::$qs1 . " :bFiRace ";
            self::$qu1 .= self::$qs1 . " FIRace = :bFiRace ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiRaceSub']) && trim($_REQUEST['bFiRaceSub'])) {
            self::$params['bFiRaceSub'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiRaceSub']);
            self::$qc1 .= self::$qs1 . ' FIRaceSub ';
            self::$qv1 .= self::$qs1 . " :bFiRaceSub ";
            self::$qu1 .= self::$qs1 . " FIRaceSub = :bFiRaceSub ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiRaceAsianOther']) && trim($_REQUEST['bFiRaceAsianOther'])) {
            self::$params['bFiRaceAsianOther'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiRaceAsianOther']);
            self::$qc1 .= self::$qs1 . ' FIRaceAsianOther ';
            self::$qv1 .= self::$qs1 . " :bFiRaceAsianOther ";
            self::$qu1 .= self::$qs1 . " FIRaceAsianOther = :bFiRaceAsianOther ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bFiRacePacificOther']) && trim($_REQUEST['bFiRacePacificOther'])) {
            self::$params['bFiRacePacificOther'] = HTTP::escapeQuoteForPOST($_REQUEST['bFiRacePacificOther']);
            self::$qc1 .= self::$qs1 . ' FIRacePacificOther ';
            self::$qv1 .= self::$qs1 . " :bFiRacePacificOther ";
            self::$qu1 .= self::$qs1 . " FIRacePacificOther = :bFiRacePacificOther ";
            self::$qs1 = ', ';
        }
        if (isset($_REQUEST['bDemoInfo']) && trim($_REQUEST['bDemoInfo'])) {
            self::$params['bDemoInfo'] = HTTP::escapeQuoteForPOST($_REQUEST['bDemoInfo']);
            self::$qc1 .= self::$qs1 . ' DemoInfo ';
            self::$qv1 .= self::$qs1 . " :bDemoInfo ";
            self::$qu1 .= self::$qs1 . " DemoInfo = :bDemoInfo ";
            self::$qs1 = ', ';
        }
        if ($CID > 0 && $qu1) {
            self::$params['CID'] = $CID;
            $clientqry = ' UPDATE tblClient SET ' . self::$qu1 . " WHERE CID = :CID ;";
            Database2::getInstance()->update($clientqry, self::$params);
        }*/

        /*deepthi code for borrower sync end*/

        if ($saveTab == 'CI'
            || $saveTab == 'FP'
            || $saveTab == 'LR'
            || $saveTab == 'QA'
            || $saveTab == 'SSREG'
            || $saveTab == 'HR'
            || $saveTab == 'SLM'
            || $saveTab == 'JA'
            || $saveTab == 'INT'
            || $saveTab == 'US'
            || $saveTab == 'FU'
            || $saveTab == 'LSR'
            || $saveTab == 'HMLO'
            || $saveTab == 'EQA'
            || $saveTab == 'SSS'
        ) {
            /** FP = Full one page SSREG= SS Registration/ iframe module **/

            if ($newLMRId > 0) {
                $rsArray = ['LMRId' => $newLMRId, 'upCnt' => $rsL, 'responseId' => $newResponseId, 'newLMRId' => $newLMRId, 'clientId' => $CID, 'brokerNumber' => $agentId, 'clientEmail' => $clientEmail];
            } elseif (Arrays::getArrayValue('upEmailInClientPro', $_REQUEST) == 'No') {
                $rsArray = ['LMRId' => $LMRId, 'upCnt' => $rsL, 'responseId' => $responseId, 'clientId' => $CID, 'clientEmail' => $clientEmail];
            } else {
                $rsArray = ['LMRId' => $LMRId, 'upCnt' => $rsL, 'responseId' => $responseId, 'clientId' => $CID, 'clientEmail' => $clientEmail];
            }

            return $rsArray;
        } else {
            return $ip['LMRId'];
        }
    }

}
