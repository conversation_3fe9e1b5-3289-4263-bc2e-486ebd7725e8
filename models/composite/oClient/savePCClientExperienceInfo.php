<?php

namespace models\composite\oClient;

use models\Database2;
use models\cypher;
use models\lendingwise\tblClientExp;
use models\lendingwise\tblPCClientExperience;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class savePCClientExperienceInfo extends strongType
{
    /**
     * @param $ip
     * @return int
     */
    public static function getReport($ip): int
    {
        $PCID = 0;
        $cnt = 0;
        $recordDate = Dates::Timestamp();
        $userGroup = '';
        $userNumber = 0;
        $userName = '';
        $sync = $ip['sync'] ?? '';

        if (array_key_exists('CID', $ip)) {
            $CID = intval(cypher::myDecryption(trim($ip['CID'])));
        } else {
            $CID = intval(cypher::myDecryption(trim($ip['encryptedPCID'])));
        }
        $activeTab = array_key_exists('activeTab', $ip) ? $ip['activeTab'] : '';
        if ($activeTab == 'LI' || $activeTab == 'QAPP' || $activeTab == 'CI' || $activeTab == 'BI') {
            if (array_key_exists('PCID', $ip)) {
                $PCID = intval($ip['PCID']);
            }
        } elseif (array_key_exists('PCID', $ip)) {
            $PCID = intval(cypher::myDecryption(trim($ip['PCID'])));
        }


        if (array_key_exists('userGroup', $ip)) {
            $userGroup = $ip['userGroup'];
        }

        if (array_key_exists('userNumber', $ip)) {
            $userNumber = $ip['userNumber'];
        }

        if (array_key_exists('userName', $ip)) {
            $userName = trim($ip['userName']);
        }

        if ($CID == 0) {
            return $cnt;
        }
        /**
         * Save Experience Information
         */
        $tblPCClientExperience = tblPCClientExperience::Get(['CID' => $CID]) ?? new tblPCClientExperience();

        $PCClientExperienceFieldsArray = [
            'haveBorREInvestmentExperience',
            'borNoOfREPropertiesCompleted',
            'haveBorRehabConstructionExperience',
            'borNoOfYearRehabExperience',
            'borRehabPropCompleted',
            'haveBorProjectCurrentlyInProgress',
            'borNoOfProjectCurrently',
            'haveBorOwnInvestmentProperties',
            'borNoOfOwnProp',
            'areBorMemberOfInvestmentClub',
            'borClubName',
            'borNoOfFlippingExperience',
            'liquidAssets',
            'haveBorProfLicences',
            'borProfLicence',
            'fullTimeRealEstateInvestor',
            'areBuilderDeveloper',
            'doYouHireGC',
            'borPrimaryInvestmentStrategyExplain',
            'amountOfFinancing',
            'amountOfFinancingTo',
            'typicalPurchasePrice',
            'typicalPurchasePriceTo',
            'typicalConstructionCosts',
            'typicalConstructionCostsTo',
            'typicalSalePrice',
            'typicalSalePriceTo',
            'constructionDrawsPerProject',
            'constructionDrawsPerProjectTo',
            'monthsPurchaseDateToFirstConst',
            'monthsPurchaseDateToFirstConstTo',
            'monthsPurchaseDateUntilConst',
            'monthsPurchaseDateUntilConstTo',
            'monthsPurchaseDateToSaleDate',
            'monthsPurchaseDateToSaleDateTo',
            'NoOfSuchProjects',
            'NoOfSuchProjectsTo',
            'haveBorSellPropertie',
            'borNoOfProSellExperience',
            'borNoOfProSellCompleted',
            'flipPropCompletedLifetime',
            'groundPropCompletedLifetime',
            'sellPropCompletedLifetime',
            'overallRealEstateInvesExp',
            'borLicenseNo',
            'trackRecord',
        ];

        $PCClientExperienceReplaceCommaValuesArray = [
            'amountOfFinancing'
            , 'amountOfFinancingTo'
            , 'typicalPurchasePrice'
            , 'typicalPurchasePriceTo'
            , 'typicalConstructionCosts'
            , 'typicalConstructionCostsTo'
            , 'typicalSalePrice'
            , 'typicalSalePriceTo'
            , 'liquidAssets'
        ];
        /** DIRECT Variables **/

        /* Multi Select Value */
        if (isset($_REQUEST['borPrimaryInvestmentStrategy'])) {
            $borPrimaryInvestmentStrategy = implode(',', $_REQUEST['borPrimaryInvestmentStrategy']);
            if ($sync == '' || ($ip['sync'] == 'sync' && trim($borPrimaryInvestmentStrategy))) {
                $tblPCClientExperience->borPrimaryInvestmentStrategy = trim($borPrimaryInvestmentStrategy);
            }
        }

        if (isset($_REQUEST['geographicAreas'])) {
            $geographicAreas = implode(',', $_REQUEST['geographicAreas']);
            if ($sync == '' || ($ip['sync'] == 'sync' && trim($geographicAreas))) {
                $tblPCClientExperience->geographicAreas = trim($geographicAreas);
            }
        }

        $checkArray = [
            'haveBorREInvestmentExperience'
            , 'haveBorRehabConstructionExperience'
            , 'haveBorSellPropertie'
            , 'haveBorProjectCurrentlyInProgress'
            , 'areBorMemberOfInvestmentClub'
            , 'fullTimeRealEstateInvestor'
            , 'haveBorOwnInvestmentProperties'
            , 'haveBorProfLicences'
            , 'areBorMemberOfInvestmentClub'
            , 'overallRealEstateInvesExp'
            , 'borPrimaryInvestmentStrategyExplain'
            , 'borProfLicence'
            , 'borLicenseNo'
        ];
        $checkIntColumns = [
            'borNoOfREPropertiesCompleted',
            'borNoOfFlippingExperience',
            'borNoOfYearRehabExperience',
            'borRehabPropCompleted'
        ];

        foreach ($PCClientExperienceFieldsArray as $column) {
            if (isset ($_REQUEST[$column])) {
                if (in_array($column, $PCClientExperienceReplaceCommaValuesArray)) {
                    $postVal = Strings::replaceCommaValues($_REQUEST[$column]);
                } elseif (in_array($column, $checkIntColumns)) {
                    $postVal = intval($_REQUEST[$column]);
                } else {
                    $postVal = HTTP::escapeQuoteForPOST($_REQUEST[$column]);
                }
                if ($sync == '' || ($ip['sync'] == 'sync' && $postVal && ($postVal != 0 || in_array($column, $checkArray)))) {
                    $tblPCClientExperience->safeSet($column, $postVal);
                }
            }
        }
        if (!$tblPCClientExperience->CEID) {
            $tblPCClientExperience->recordDate = $recordDate;
            $tblPCClientExperience->CID = $CID;
            $tblPCClientExperience->PCID = $PCID;
        }
        $tblPCClientExperience->Save();
        if ($tblPCClientExperience->CEID) {
            $cnt++;
        }

        /**
         * Save Flipping & Ground Up Experience Information
         */
        if ($CID > 0) {
            $qry = ' DELETE FROM tblClientExp WHERE CID = :CID; ';
            Database2::getInstance()->update($qry, ['CID' => $CID]);

            for ($ag = 0; $ag < 3; $ag++) {
                $tCnt = 0;
                $tblClientExp = new tblClientExp();
                if (isset($_REQUEST['borFlipPropType'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipPropType'][$ag]))) {
                        $tblClientExp->propertyType = trim($_REQUEST['borFlipPropType'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipPurchaseDate'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipPurchaseDate'][$ag]))) {
                        $tblClientExp->purchaseDate = Dates::formatDateWithRE($_REQUEST['borFlipPurchaseDate'][$ag], 'MDY', 'Y-m-d') ? Dates::Datestamp($_REQUEST['borFlipPurchaseDate'][$ag]) : null;
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipPurchasePrice'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipPurchasePrice'][$ag]))) {
                        $tblClientExp->purchasePrice = Strings::replaceCommaValues($_REQUEST['borFlipPurchasePrice'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipAmountFinanced'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipAmountFinanced'][$ag]))) {
                        $tblClientExp->amountFinanced = Strings::replaceCommaValues($_REQUEST['borFlipAmountFinanced'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipRehabBudget'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipRehabBudget'][$ag]))) {
                        $tblClientExp->rehabBudget = Strings::replaceCommaValues($_REQUEST['borFlipRehabBudget'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipEntityName'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipEntityName'][$ag]))) {
                        $tblClientExp->entityName = trim($_REQUEST['borFlipEntityName'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipOwnership'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipOwnership'][$ag]))) {
                        $tblClientExp->ownership = Strings::replaceCommaValues($_REQUEST['borFlipOwnership'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipExit'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipExit'][$ag]))) {
                        $tblClientExp->exitValues = trim($_REQUEST['borFlipExit'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipSalePrice'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipSalePrice'][$ag]))) {
                        $tblClientExp->salePrice = Strings::replaceCommaValues($_REQUEST['borFlipSalePrice'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipSaleDate'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipSaleDate'][$ag]))) {
                        $tblClientExp->saleDate = Dates::formatDateWithRE($_REQUEST['borFlipSaleDate'][$ag], 'MDY', 'Y-m-d') ? Dates::Datestamp($_REQUEST['borFlipSaleDate'][$ag]) : null;
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipMonthlyRent'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipMonthlyRent'][$ag]))) {
                        $tblClientExp->monthlyRent = Strings::replaceCommaValues($_REQUEST['borFlipMonthlyRent'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipAddress'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipAddress'][$ag]))) {
                        $tblClientExp->address = trim($_REQUEST['borFlipAddress'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipCity'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipCity'][$ag]))) {
                        $tblClientExp->city = trim($_REQUEST['borFlipCity'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipState'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipState'][$ag]))) {
                        $tblClientExp->state = trim($_REQUEST['borFlipState'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipZip'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipZip'][$ag]))) {
                        $tblClientExp->zip = substr(trim($_REQUEST['borFlipZip'][$ag]), 0, 10);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipUnit'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipUnit'][$ag]))) {
                        $tblClientExp->unit = trim($_REQUEST['borFlipUnit'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipOwnershipStatus'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borFlipOwnershipStatus'][$ag]))) {
                        $tblClientExp->ownershipStatus = trim($_REQUEST['borFlipOwnershipStatus'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borOutcomeRE'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borOutcomeRE'][$ag]))) {
                        $tblClientExp->Outcome = Strings::replaceCommaValues($_REQUEST['borOutcomeRE'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borFlipOwnershipStatus'])) {
                    $tblClientExp->ownershipStatus = $_REQUEST['borFlipOwnershipStatus'][$ag];
                    $tCnt++;
                }
                if (isset($_REQUEST['borFlipUnit'])) {
                    $tblClientExp->unit = trim($_REQUEST['borFlipUnit'][$ag]);
                    $tCnt++;
                }
                if ($userName) {
                    $tblClientExp->createdBy = trim($userName . ' ('  .  $userGroup . ')');
                }
                $tblClientExp->CID = $CID;
                $tblClientExp->userType = 'B';
                $tblClientExp->expType = 'Flip';
                $tblClientExp->recordDate = $recordDate;

                if ($tCnt > 0) {
                    $tblClientExp->Save();
                    $cnt++;
                }
            }

            /**
             * Save Ground Up Experience Information
             */

            for ($ag = 0; $ag < 3; $ag++) {
                $tCnt = 0;
                $tblClientExp = new tblClientExp();

                if (isset($_REQUEST['borGUPropType'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUPropType'][$ag]))) {
                        $tblClientExp->propertyType = trim($_REQUEST['borGUPropType'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUPurchaseDate'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUPurchaseDate'][$ag]))) {
                        $tblClientExp->purchaseDate = Dates::formatDateWithRE($_REQUEST['borGUPurchaseDate'][$ag], 'MDY', 'Y-m-d') ? Dates::Datestamp($_REQUEST['borGUPurchaseDate'][$ag]) : null;
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUPurchasePrice'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUPurchasePrice'][$ag]))) {
                        $tblClientExp->purchasePrice = Strings::replaceCommaValues($_REQUEST['borGUPurchasePrice'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUAmountFinanced'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUAmountFinanced'][$ag]))) {
                        $tblClientExp->amountFinanced = Strings::replaceCommaValues($_REQUEST['borGUAmountFinanced'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGURehabBudget'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGURehabBudget'][$ag]))) {
                        $tblClientExp->rehabBudget = Strings::replaceCommaValues($_REQUEST['borGURehabBudget'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUEntityName'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUEntityName'][$ag]))) {
                        $tblClientExp->entityName = trim($_REQUEST['borGUEntityName'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUOwnership'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUOwnership'][$ag]))) {
                        $tblClientExp->ownership = Strings::replaceCommaValues($_REQUEST['borGUOwnership'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUExit'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUExit'][$ag]))) {
                        $tblClientExp->exitValues = trim($_REQUEST['borGUExit'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUSalePrice'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUSalePrice'][$ag]))) {
                        $tblClientExp->salePrice = Strings::replaceCommaValues($_REQUEST['borGUSalePrice'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUSaleDate'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUSaleDate'][$ag]))) {
                        $tblClientExp->saleDate = Dates::formatDateWithRE($_REQUEST['borGUSaleDate'][$ag], 'MDY', 'Y-m-d') ? Dates::Datestamp($_REQUEST['borGUSaleDate'][$ag]) : null;
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUMonthlyRent'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUMonthlyRent'][$ag]))) {
                        $tblClientExp->monthlyRent = Strings::replaceCommaValues($_REQUEST['borGUMonthlyRent'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUAddress'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUAddress'][$ag]))) {
                        $tblClientExp->address = trim($_REQUEST['borGUAddress'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUCity'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUCity'][$ag]))) {
                        $tblClientExp->city = trim($_REQUEST['borGUCity'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUState'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUState'][$ag]))) {
                        $tblClientExp->state = trim($_REQUEST['borGUState'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUZip'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borGUZip'][$ag]))) {
                        $tblClientExp->zip = substr(trim($_REQUEST['borGUZip'][$ag]), 0, 10);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borOutcomeRC'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borOutcomeRC'][$ag]))) {
                        $tblClientExp->Outcome = Strings::replaceCommaValues($_REQUEST['borOutcomeRC'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borGUOwnershipStatus'])) {
                    $tblClientExp->ownershipStatus = $_REQUEST['borGUOwnershipStatus'][$ag];
                    $tCnt++;
                }
                if (isset($_REQUEST['borGUUnit'])) {
                    $tblClientExp->unit = trim($_REQUEST['borGUUnit'][$ag]);
                    $tCnt++;
                }
                if ($userName) {
                    $tblClientExp->createdBy = trim($userName . ' ('  .  $userGroup . ')');
                }
                $tblClientExp->CID = $CID;
                $tblClientExp->userType = 'B';
                $tblClientExp->expType = 'Gup';
                $tblClientExp->recordDate = $recordDate;

                if ($tCnt > 0) {
                    $tblClientExp->Save();
                    $cnt++;
                }
            }


            /**
             * Save Selling Properties Experience Information
             */
            for ($ag = 0; $ag < 3; $ag++) {
                $tCnt = 0;
                $tblClientExp = new tblClientExp();

                if (isset($_REQUEST['borSellPropType'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellPropType'][$ag]))) {
                        $tblClientExp->propertyType = trim($_REQUEST['borSellPropType'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellPurchaseDate'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellPurchaseDate'][$ag]))) {
                        $tblClientExp->purchaseDate = Dates::formatDateWithRE($_REQUEST['borSellPurchaseDate'][$ag], 'MDY', 'Y-m-d') ? Dates::Datestamp($_REQUEST['borSellPurchaseDate'][$ag]) : null;
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellPurchasePrice'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellPurchasePrice'][$ag]))) {
                        $tblClientExp->purchasePrice = Strings::replaceCommaValues($_REQUEST['borSellPurchasePrice'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellAmountFinanced'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellAmountFinanced'][$ag]))) {
                        $tblClientExp->amountFinanced = Strings::replaceCommaValues($_REQUEST['borSellAmountFinanced'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellRehabBudget'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellRehabBudget'][$ag]))) {
                        $tblClientExp->rehabBudget = Strings::replaceCommaValues($_REQUEST['borSellRehabBudget'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellEntityName'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellEntityName'][$ag]))) {
                        $tblClientExp->entityName = trim($_REQUEST['borSellEntityName'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellOwnership'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellOwnership'][$ag]))) {
                        $tblClientExp->ownership = Strings::replaceCommaValues($_REQUEST['borSellOwnership'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellExit'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellExit'][$ag]))) {
                        $tblClientExp->exitValues = trim($_REQUEST['borSellExit'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellSalePrice'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellSalePrice'][$ag]))) {
                        $tblClientExp->salePrice = Strings::replaceCommaValues($_REQUEST['borSellSalePrice'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellSaleDate'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellSaleDate'][$ag]))) {
                        $tblClientExp->saleDate = Dates::formatDateWithRE($_REQUEST['borSellSaleDate'][$ag], 'MDY', 'Y-m-d') ? Dates::Datestamp($_REQUEST['borSellSaleDate'][$ag]) : null;
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellMonthlyRent'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellMonthlyRent'][$ag]))) {
                        $tblClientExp->monthlyRent = Strings::replaceCommaValues($_REQUEST['borSellMonthlyRent'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellAddress'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellAddress'][$ag]))) {
                        $tblClientExp->address = trim($_REQUEST['borSellAddress'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellCity'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellCity'][$ag]))) {
                        $tblClientExp->city = trim($_REQUEST['borSellCity'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellState'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellState'][$ag]))) {
                        $tblClientExp->state = trim($_REQUEST['borSellState'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellZip'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellZip'][$ag]))) {
                        $tblClientExp->zip = substr(trim($_REQUEST['borSellZip'][$ag]), 0, 10);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellOutcomeRE'])) {
                    if ($sync == '' || ($ip['sync'] == 'sync' && trim($_REQUEST['borSellOutcomeRE'][$ag]))) {
                        $tblClientExp->Outcome = Strings::replaceCommaValues($_REQUEST['borSellOutcomeRE'][$ag]);
                        $tCnt++;
                    }
                }
                if (isset($_REQUEST['borSellOwnershipStatus'])) {
                    $tblClientExp->ownershipStatus = $_REQUEST['borSellOwnershipStatus'][$ag];
                    $tCnt++;
                }
                if (isset($_REQUEST['borSellUnit'])) {
                    $tblClientExp->unit = trim($_REQUEST['borSellUnit'][$ag]);
                    $tCnt++;
                }
                if ($userName) {
                    $tblClientExp->createdBy = trim($userName . ' ('  .  $userGroup . ')');
                }
                $tblClientExp->CID = $CID;
                $tblClientExp->userType = 'B';
                $tblClientExp->expType = 'Sell';
                $tblClientExp->recordDate = $recordDate;

                if ($tCnt > 0) {
                    $tblClientExp->Save();
                    $cnt++;
                }
            }

        }

        $nCnt = uploadClientDocs::getReport([
            'CID'        => $CID,
            'userGroup'  => $userGroup,
            'userName'   => $userName,
            'userNumber' => $userNumber,
            'PCID'       => $PCID,
            'p'          => $ip
        ]);   /*HUD Document Upload..*/

        if ($nCnt > 0) {
            $cnt++;
        }

        if ($nCnt > 0 && $PCID > 0 && $CID > 0) {
            $sql = 'CALL SP_InsUpdateHMLOExperience (
                    :CID,
                    :PCID
            );';
            Database2::getInstance()->executeQuery($sql, [
                'CID'  => $CID,
                'PCID' => $PCID,
            ]);
        }
        return $cnt;
    }
}
