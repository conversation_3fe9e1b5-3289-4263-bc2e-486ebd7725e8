<?php

namespace models\composite\oFile;

use models\composite\oFile\getFileInfo\fileCalculatedValues;
use models\composite\oFile\getFileInfo\Notes;
use models\composite\oHMLOInfo\fileExtensionOptions;
use models\Controllers\LMRequest\additionalGuarantors;
use models\Controllers\LMRequest\creditDecision;
use models\Controllers\LMRequest\HUDFundingClosingInfo;
use models\Database2;
use models\composite\oBroker\getBrokerInfo;
use models\composite\oFile\getFileInfo\ACHInfo;
use models\composite\oFile\getFileInfo\addedToFav;
use models\composite\oFile\getFileInfo\AdditionalInfo;
use models\composite\oFile\getFileInfo\AdditionalLienContactInfo;
use models\composite\oFile\getFileInfo\additionalStateBarInfo;
use models\composite\oFile\getFileInfo\adverseActionInfo;
use models\composite\oFile\getFileInfo\alowareContact;
use models\composite\oFile\getFileInfo\AssetsInfo;
use models\composite\oFile\getFileInfo\assignedStaffHistoryInfo;
use models\composite\oFile\getFileInfo\BillingFeeInfo;
use models\composite\oFile\getFileInfo\billingInfoArray;
use models\composite\oFile\getFileInfo\BillingPaymentInfo;
use models\composite\oFile\getFileInfo\billingUrlInfo;
use models\composite\oFile\getFileInfo\borEmploymentInfo;
use models\composite\oFile\getFileInfo\borrowerAlternateNamesArray;
use models\composite\oFile\getFileInfo\BorrowerMissingDoc;
use models\composite\oFile\getFileInfo\branchClientTypeInfo;
use models\composite\oFile\getFileInfo\BranchInfo;
use models\composite\oFile\getFileInfo\branchModuleInfo;
use models\composite\oFile\getFileInfo\BrokerInfo;
use models\composite\oFile\getFileInfo\budgetAndDrawsInfo;
use models\composite\oFile\getFileInfo\CCInfo;
use models\composite\oFile\getFileInfo\CFPBInfo;
use models\composite\oFile\getFileInfo\CFPBReport;
use models\composite\oFile\getFileInfo\cklistNotRequired;
use models\composite\oFile\getFileInfo\clientDocsArray;
use models\composite\oFile\getFileInfo\clientInfoArray;
use models\composite\oFile\getFileInfo\clientPaymentInfo;
use models\composite\oFile\getFileInfo\CMAInfo;
use models\composite\oFile\getFileInfo\coBEmploymentInfo;
use models\composite\oFile\getFileInfo\coBorrowerCountyInfo;
use models\composite\oFile\getFileInfo\collateralArray;
use models\composite\oFile\getFileInfo\commissionInfo;
use models\composite\oFile\getFileInfo\contingentLiabilities;
use models\composite\oFile\getFileInfo\CourtInfo;
use models\composite\oFile\getFileInfo\creditMemoArray;
use models\composite\oFile\getFileInfo\creditorInfo;
use models\composite\oFile\getFileInfo\creditorInfoStatus;
use models\composite\oFile\getFileInfo\dealAnalysisInfo;
use models\composite\oFile\getFileInfo\dummyBrokerId;
use models\composite\oFile\getFileInfo\emailHistoryInfo;
use models\composite\oFile\getFileInfo\EmployeeInfo;
use models\composite\oFile\getFileInfo\equipmentInfo;
use models\composite\oFile\getFileInfo\equipmentInformation;
use models\composite\oFile\getFileInfo\estimatedProjectCost;
use models\composite\oFile\getFileInfo\exteriorWorkInfo;
use models\composite\oFile\getFileInfo\Fax;
use models\composite\oFile\getFileInfo\feeScheduleInfo;
use models\composite\oFile\getFileInfo\file2Info;
use models\composite\oFile\getFileInfo\file3Info;
use models\composite\oFile\getFileInfo\fileAdditionalGuarantorsInfo;
use models\composite\oFile\getFileInfo\fileBudgetAndDrawDoc;
use models\composite\oFile\getFileInfo\fileCheckListInfo;
use models\composite\oFile\getFileInfo\fileExpFilpGroundUp;
use models\composite\oFile\getFileInfo\fileFUCreditEnhancementInfo;
use models\composite\oFile\getFileInfo\fileFUCreditInfo;
use models\composite\oFile\getFileInfo\fileFUEntityInfo;
use models\composite\oFile\getFileInfo\fileHMLOAssetsInfo;
use models\composite\oFile\getFileInfo\fileHMLOBackGroundInfo;
use models\composite\oFile\getFileInfo\fileHMLOChecklistUploadDocs;
use models\composite\oFile\getFileInfo\fileHMLOEntityInfo;
use models\composite\oFile\getFileInfo\fileHMLOEntityRefInfo;
use models\composite\oFile\getFileInfo\fileHMLOExperienceInfo;
use models\composite\oFile\getFileInfo\fileHMLOInfo;
use models\composite\oFile\getFileInfo\fileHMLOListOfRepairs;
use models\composite\oFile\getFileInfo\fileHMLONewLoanInfo;
use models\composite\oFile\getFileInfo\fileHMLOPropertyInfo;
use models\composite\oFile\getFileInfo\fileLoanOriginationInfo;
use models\composite\oFile\getFileInfo\fileLOAssetsInfo;
use models\composite\oFile\getFileInfo\fileLOChekingSavingInfo;
use models\composite\oFile\getFileInfo\fileLOExpensesInfo;
use models\composite\oFile\getFileInfo\fileLOPropInfo;
use models\composite\oFile\getFileInfo\fileLOScheduleRealInfo;
use models\composite\oFile\getFileInfo\fileLSSummaryInfo;
use models\composite\oFile\getFileInfo\fileMemberOfficerInfo;
use models\composite\oFile\getFileInfo\fileModuleInfo;
use models\composite\oFile\getFileInfo\FileProInfo;
use models\composite\oFile\getFileInfo\fileSubstatusInfo;
use models\composite\oFile\getFileInfo\fileVelocityInfo;
use models\composite\oFile\getFileInfo\filRentRollInfo;
use models\composite\oFile\getFileInfo\FUInfo;
use models\composite\oFile\getFileInfo\getInsuranceDtls;
use models\composite\oFile\getFileInfo\getInsuranceTypesGlobal;
use models\composite\oFile\getFileInfo\getLenderNotes;
use models\composite\oFile\getFileInfo\getValuationMethodsGlobal;
use models\composite\oFile\getFileInfo\gogArray;
use models\composite\oFile\getFileInfo\HAFADocInfo;
use models\composite\oFile\getFileInfo\hardshipDescInfo;
use models\composite\oFile\getFileInfo\hardshipInfo;
use models\composite\oFile\getFileInfo\History;
use models\composite\oFile\getFileInfo\HMLOPCReport;
use models\composite\oFile\getFileInfo\HRHistoryInfo;
use models\composite\oFile\getFileInfo\HRInfo;
use models\composite\oFile\getFileInfo\HRRequiredCheckListInfo;
use models\composite\oFile\getFileInfo\HUDAdditionalChargesInfo;
use models\composite\oFile\getFileInfo\HUDBasicInfoArray;
use models\composite\oFile\getFileInfo\HUDItemsPayableInfo;
use models\composite\oFile\getFileInfo\HUDLenderToPayInfo;
use models\composite\oFile\getFileInfo\HUDLoanTypeInfo;
use models\composite\oFile\getFileInfo\HUDReservesDepositInfo;
use models\composite\oFile\getFileInfo\HUDSettlementInfo;
use models\composite\oFile\getFileInfo\HUDTransactionInfo;
use models\composite\oFile\getFileInfo\incomeInfo;
use models\composite\oFile\getFileInfo\instypeNames;
use models\composite\oFile\getFileInfo\interiorWorkInfo;
use models\composite\oFile\getFileInfo\investorOtherInfo;
use models\composite\oFile\getFileInfo\JoinderAppInfo;
use models\composite\oFile\getFileInfo\legalBankRuptcyInfo;
use models\composite\oFile\getFileInfo\legalContractInfo;
use models\composite\oFile\getFileInfo\LenderInfo;
use models\composite\oFile\getFileInfo\liabilitiesInfo;
use models\composite\oFile\getFileInfo\listingInfo;
use models\composite\oFile\getFileInfo\listingPageArray;
use models\composite\oFile\getFileInfo\listingPrincipal;
use models\composite\oFile\getFileInfo\listingRealtorInfo;
use models\composite\oFile\getFileInfo\listingRealtorInfo2;
use models\composite\oFile\getFileInfo\LMRadditionalLoanprograms;
use models\composite\oFile\getFileInfo\LMRChecklist;
use models\composite\oFile\getFileInfo\LMRClientTypeInfo;
use models\composite\oFile\getFileInfo\LMRInfo;
use models\composite\oFile\getFileInfo\LMRInternalLoanprograms;
use models\composite\oFile\getFileInfo\LMRWFArray;
use models\composite\oFile\getFileInfo\LoanAudit;
use models\composite\oFile\getFileInfo\loanOriginatorInfo;
use models\composite\oFile\getFileInfo\LOExplanationInfo;
use models\composite\oFile\getFileInfo\lpArray;
use models\composite\oFile\getFileInfo\MFInfo;
use models\composite\oFile\getFileInfo\MFLoanInfo;
use models\composite\oFile\getFileInfo\MFLoanTermsInfo;
use models\composite\oFile\getFileInfo\missingDocInfoArray;
use models\composite\oFile\getFileInfo\moduleFileTabInfo;
use models\composite\oFile\getFileInfo\mortgagePropLoanArray;
use models\composite\oFile\getFileInfo\notesHistoryInfo;
use models\composite\oFile\getFileInfo\NotesInfo;
use models\composite\oFile\getFileInfo\offerArray;
use models\composite\oFile\getFileInfo\offerDocsArray;
use models\composite\oFile\getFileInfo\OtherCreditsInfo;
use models\composite\oFile\getFileInfo\otherWorkInfo;
use models\composite\oFile\getFileInfo\paydownInfo;
use models\composite\oFile\getFileInfo\payeeInfo;
use models\composite\oFile\getFileInfo\PCBasicLoanTabFileIdExists;
use models\composite\oFile\getFileInfo\PCCheckListInfo;
use models\composite\oFile\getFileInfo\PCFileDocsCategory;
use models\composite\oFile\getFileInfo\PCInfo;
use models\composite\oFile\getFileInfo\PCLegalContractInfo;
use models\composite\oFile\getFileInfo\PCquickAppFieldsInfo;
use models\composite\oFile\getFileInfo\PCServiceType;
use models\composite\oFile\getFileInfo\PCStatusInfo;
use models\composite\oFile\getFileInfo\PCSubStatusInfo;
use models\composite\oFile\getFileInfo\peerstreet;
use models\composite\oFile\getFileInfo\planInfoArray;
use models\composite\oFile\getFileInfo\preliminaryWorkInfo;
use models\composite\oFile\getFileInfo\primeStatusHistoryInfo;
use models\composite\oFile\getFileInfo\principalResInfo;
use models\composite\oFile\getFileInfo\proInsPolicyExpDate;
use models\composite\oFile\getFileInfo\propertyCountyInfo;
use models\composite\oFile\getFileInfo\propertyValuationDocs;
use models\composite\oFile\getFileInfo\propMgmtArray;
use models\composite\oFile\getFileInfo\propMgmtDocsArray;
use models\composite\oFile\getFileInfo\proposalInfo;
use models\composite\oFile\getFileInfo\proposalSummaryInfo;
use models\composite\oFile\getFileInfo\purchaseInfo;
use models\composite\oFile\getFileInfo\QAInfo;
use models\composite\oFile\getFileInfo\RAMReport;
use models\composite\oFile\getFileInfo\RealEstateInfo;
use models\composite\oFile\getFileInfo\REBrokerInfo;
use models\composite\oFile\getFileInfo\RecentInfo;
use models\composite\oFile\getFileInfo\rehabItemInfo;
use models\composite\oFile\getFileInfo\relatedDocs;
use models\composite\oFile\getFileInfo\reqValMethodNames;
use models\composite\oFile\getFileInfo\ResponseInfo;
use models\composite\oFile\getFileInfo\RESTInfo;
use models\composite\oFile\getFileInfo\RESTNotesInfo;
use models\composite\oFile\getFileInfo\salesMethodInfo;
use models\composite\oFile\getFileInfo\SBABackground;
use models\composite\oFile\getFileInfo\sbaOtherBusinessData;
use models\composite\oFile\getFileInfo\scheduledEmail;
use models\composite\oFile\getFileInfo\secondaryBrokerInfo;
use models\composite\oFile\getFileInfo\SellerInfoArray;
use models\composite\oFile\getFileInfo\SSInfo;
use models\composite\oFile\getFileInfo\SSProposalInfo;
use models\composite\oFile\getFileInfo\SSProposalSummaryInfo;
use models\composite\oFile\getFileInfo\SSRegn;
use models\composite\oFile\getFileInfo\Staff;
use models\composite\oFile\getFileInfo\studentLoanInfo;
use models\composite\oFile\getFileInfo\studentLoanQAInfo;
use models\composite\oFile\getFileInfo\studentReferenceInfo;
use models\composite\oFile\getFileInfo\substatusHistoryInfo;
use models\composite\oFile\getFileInfo\taskEmpInfo;
use models\composite\oFile\getFileInfo\taskInfo;
use models\composite\oFile\getFileInfo\thirdPartyServiceInfo;
use models\composite\oFile\getFileInfo\trustDocPositionsInfo;
use models\composite\oFile\getFileInfo\WFInfo;
use models\composite\oFile\getFileInfo\Workflow;
use models\Controllers\LMRequest\dealSizer;
use models\Controllers\LMRequest\loanSetting;
use models\Controllers\LMRequest\partnerShips;
use models\Controllers\LMRequest\Property;
use models\Controllers\LMRequest\refinanceMortgage;
use models\lendingwise\tblFileExtensionOptions;
use models\cypher;
use models\types\strongType;

/**
 *
 */
class getFileInfo extends strongType
{

    public static ?array $fileLoanPrograms = null;
    public static ?array $fileInternalLoanPrograms = null;
    public static ?array $fileInternalLoanProgramsName = null;
    public static ?array $fileAdditionalLoanPrograms = null;

    public static ?int $publicUser = null;

    public static ?array $fileHMLOExperienceInfo = null;

    public static ?int $viewPrivateNotes = null;
    public static ?int $viewPublicNotes = null;
    public static ?int $LMRId = null;
    public static ?int $notesId = null;
    public static ?array $infoArray = null;
    public static ?int $public = null;
    public static ?string $fetchTab = null;


    /**
     * @param $infoArray
     * @return array
     */
    public static function getReport($infoArray): array
    {
        $fileInfo = [];
        $brokerNumber = 0;
        $secondaryBrokerNumber = 0;
        $executiveId = 0;
        $lenderName1 = '';
        $lenderName2 = '';
        $REBrokerId = 0;
        $clientId = 0;
        $coBorrowerMailingState = '';
        $processingCompanyId = 0;
        $LMRId = intval($infoArray['LMRId']);
        $isCoBorrower = 0;

        $pkgID = intval($infoArray['pkgID'] ?? 0);
        $fetchTab = $infoArray['fetchTab'] ?? 'ALL';
        $commissionUserId = $infoArray['commissionUserId'] ?? 0;
        $commissionUserType = $infoArray['commissionUserType'] ?? '';
        $docID = $infoArray['docID'] ?? 0;
        $UType = trim($infoArray['userGroup'] ?? '');
        $CIID = intval($infoArray['CIID'] ?? 0);
        $public = $infoArray['public'] ?? 1;
        $viewPrivateNotes = $infoArray['viewPrivateNotes'] ?? 1;
        $viewPublicNotes = $infoArray['viewPublicNotes'] ?? 1;
        $linkedDocID = $infoArray['linkedDocID'] ?? 0;
        $saveOpt = $infoArray['saveOpt'] ?? '';
        $txnID = $infoArray['txnID'] ?? 0;
        $notesId = $infoArray['notesId'] ?? 0;
        $userGroup = $infoArray['userGroup'] ?? '';

        if (!$LMRId) {
            return [];
        }
        if (trim($fetchTab) == '') {
            $fetchTab = 'ALL';
        }

        $fileInfo[$LMRId] = [];

        /** LMR Info **/
        $LMRInfo = LMRInfo::getReport($LMRId); // refactored

        static::$viewPrivateNotes = $viewPrivateNotes;
        static::$viewPublicNotes = $viewPublicNotes;
        static::$LMRId = $LMRId;
        static::$notesId = $notesId;
        static::$infoArray = $infoArray;
        static::$public = $public;
        static::$fetchTab = $fetchTab;

        foreach ($LMRInfo as $row) {
            $clientId = intval($row['clientId']);
            $brokerNumber = intval($row['brokerNumber']);
            $secondaryBrokerNumber = intval($row['secondaryBrokerNumber']);
            $lenderName2 = trim($row['servicer2']);
            $lenderName1 = trim($row['servicer1']);
            $REBrokerId = intval($row['REBrokerId']);
            $processingCompanyId = intval($row['FPCID']);
            $isCoBorrower = intval($row['isCoBorrower']);
            $executiveId = intval($row['FBRID']);

            if (trim($REBrokerId) == '') {
                $REBrokerId = 0;
            }
            $LMRInfo = $row;

            $LMRInfo['mortgageNotes'] = rawurldecode(stripslashes($row['mortgageNotes']));
            $LMRInfo['borrowerFName'] = cypher::myDecryption(trim($row['enc_borrowerName']));
            $LMRInfo['borrowerName'] = cypher::myDecryption(trim($row['enc_borrowerName']));
            $LMRInfo['borrowerLName'] = cypher::myDecryption(trim($row['enc_borrowerLName']));
            $LMRInfo['borrowerEmail'] = cypher::myDecryption(trim($row['enc_borrowerEmail']));

            if ($isCoBorrower == 0) {
                $LMRInfo['coBorrowerFName'] = '';
                $LMRInfo['coBorrowerLName'] = '';
                $LMRInfo['coBSsnNumber'] = '';
                $LMRInfo['coBorrowerDOB'] = '';
                $LMRInfo['coborrowerPOB'] = '';
                $LMRInfo['coBPhoneNumber'] = '';
                $LMRInfo['coBorrowerTimeZone'] = '';
                $LMRInfo['coBAltPhoneNumber'] = '';
                $LMRInfo['coBCellNumber'] = '';
                $LMRInfo['coBServiceProvider'] = '';
                $LMRInfo['coBFax'] = '';
                $LMRInfo['coBorrowerEmail'] = '';
                $LMRInfo['coBorrowerWorkNumber'] = '';
                $LMRInfo['coBorrowerMailingAddress'] = '';
                $LMRInfo['coBorrowerMailingCity'] = '';
                $LMRInfo['coBorrowerMailingState'] = '';
                $LMRInfo['coBorrowerMailingZip'] = '';
                $LMRInfo['coBorPreviousAddress'] = '';
                $LMRInfo['coBorPreviousCity'] = '';
                $LMRInfo['coBorPreviousState'] = '';
                $LMRInfo['coBorPreviousZip'] = '';
            }
            $coBorrowerMailingState = trim($row['coBorrowerMailingState']);
        }

        Property::init($LMRId);

        $LMRInfo['propertyAddress'] = Property::$primaryPropertyInfo->propertyAddress;
        $LMRInfo['propertyState'] = Property::$primaryPropertyInfo->propertyState;
        $LMRInfo['propertyCity'] = Property::$primaryPropertyInfo->propertyCity;
        $LMRInfo['propertyZip'] = Property::$primaryPropertyInfo->propertyZipCode;
        $LMRInfo['propertyType'] = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType;

        $LMRInfo['enc_propertyAddress'] = cypher::myEncryption(Property::$primaryPropertyInfo->propertyAddress);
        $LMRInfo['enc_propertyState'] = cypher::myEncryption(Property::$primaryPropertyInfo->propertyState);
        $LMRInfo['enc_propertyCity'] = cypher::myEncryption(Property::$primaryPropertyInfo->propertyCity);
        $LMRInfo['enc_propertyZip'] = cypher::myEncryption(Property::$primaryPropertyInfo->propertyZipCode);
        $propertyState = Property::$primaryPropertyInfo->propertyState;

        ksort($LMRInfo);

        $addedToFav = addedToFav::getReport($LMRId); // refactored
        $file2Info = file2Info::getReport($fetchTab, $LMRId, $isCoBorrower); // refactored
        $listingRealtorInfo = listingRealtorInfo::getReport($fetchTab, $LMRId); // refactored
        $ResponseInfo = ResponseInfo::getReport($fetchTab, $LMRId); // refactored
        $BrokerInfo = BrokerInfo::getReport($fetchTab, $brokerNumber); // refactored
        $dummyBrokerId = dummyBrokerId::getReport($processingCompanyId); // refactored
        if ($secondaryBrokerNumber > '') {
            $SecondaryBrokerInfo = secondaryBrokerInfo::getReport($fetchTab, $secondaryBrokerNumber); // refactored
        }
        $BranchInfo = BranchInfo:: getReport($executiveId); // refactored
        $employeeInfo = EmployeeInfo::getReport($fetchTab, $processingCompanyId); // refactored
        $incomeInfo = incomeInfo::getReport($fetchTab, $LMRId, $isCoBorrower); // refactored
        $fileHMLONewLoanInfo = fileHMLONewLoanInfo::getReport($fetchTab, $LMRId); // refactored

        refinanceMortgage::init($LMRId); // refactored
        $refinanceMortgageInfo = refinanceMortgage::$refinanceMortgageInfo;
        $refinanceMortgageFirst = (array)$refinanceMortgageInfo[0];
        $refinanceMortgageFirst = (array_intersect_key($refinanceMortgageFirst, $fileHMLONewLoanInfo));
        $fileHMLONewLoanInfo = (array_merge($fileHMLONewLoanInfo, $refinanceMortgageFirst));

        $res = LenderInfo::getReport($fetchTab, $lenderName1, $lenderName2); // refactored
        $LenderInfo1 = $res['LenderInfo1'];
        $lenderArray = $res['lenderArray'];
        $LenderInfo2 = $res['LenderInfo2'];

        $lenderNotes = getLenderNotes::getReport($fetchTab, $lenderArray); // refactored

        $res = Staff::getReport($fetchTab, $LMRId); // refactored

        $tStaff = $res['tStaff']; // not used 8/27/2022
        $BOStaff = $res['BOStaff'];
        $BOStaffId = $res['BOStaffId'];

        $listingprincipalResInfo = listingPrincipal::getReport($fetchTab, $LMRId, $pkgID); // refactored
        $payeeInfo = payeeInfo::getReport($fetchTab, $LMRId); // refactored
        $SSInfo = SSInfo::getReport($fetchTab, $LMRId); // refactored
        $RealEstateInfo = RealEstateInfo::getReport($fetchTab, $LMRId); // refactored
        $BillingFeeInfo = BillingFeeInfo::getReport($fetchTab, $LMRId); // refactored
        $paydownInfo = paydownInfo::getReport($LMRId); // refactored
        $BillingPaymentInfo = BillingPaymentInfo::getReport($fetchTab, $LMRId); // refactored
        $commissionInfo = commissionInfo::getReport($fetchTab, $LMRId, $infoArray, $commissionUserId, $commissionUserType); // refactored
        $listingInfo = listingInfo::getReport($fetchTab, $LMRId); // refactored
        $RecentInfo = RecentInfo::getReport($fetchTab, $LMRId); // refactored
        $CMAInfo = CMAInfo::getReport($fetchTab, $LMRId); // refactored
        $SSRegn = SSRegn::getReport($fetchTab, $LMRId); // refactored
        $hardshipInfo = hardshipInfo::getReport($fetchTab, $LMRId); // refactored
        $PCInfo = PCInfo::getReport($processingCompanyId); // refactored

        $res = fileModuleInfo::getReport($LMRId); // refactored
        $fileModuleInfo = $res['fileModuleInfo'];
        $fileSelectedModuleCode = $res['fileSelectedModuleCode'];

        $fileHMLOPropertyInfo = fileHMLOPropertyInfo::getReport($LMRId); // refactored

        if ($fileHMLOPropertyInfo['proInsType']) {
            $instypeNames = instypeNames::getReport($fileHMLOPropertyInfo['proInsType']); // refactored
            $fileHMLOPropertyInfo['selInsNames'] = implode(', ', $instypeNames);
        }

        if ($fileHMLOPropertyInfo['reqValuationMethod']) {
            $reqValMethodNames = reqValMethodNames::getReport($fileHMLOPropertyInfo['reqValuationMethod']); // refactored
            $fileHMLOPropertyInfo['reqValMethodNames'] = implode(', ', $reqValMethodNames);
        }
        $fileHMLOPropertyInfo['proInsPolicyExpDate'] = proInsPolicyExpDate::getReport($LMRId); // refactored


        $resIncomeExpenses = calculateIncomeExpenses::getReport(
            $incomeInfo,
            $fileSelectedModuleCode,
            $LMRInfo,
            $fileHMLONewLoanInfo['isTaxesInsEscrowed'] ?? 0,
            $fileHMLOPropertyInfo['annualPremium'] ?? 0
        ); // refactored

        $incomeInfo['borrowerTotalMonthlyIncome'] = $resIncomeExpenses['borrowerTotalMonthlyIncome'];
        $incomeInfo['coborrowerTotalMonthlyIncome'] = $resIncomeExpenses['coborrowerTotalMonthlyIncome'];
        $incomeInfo['borrowerTotalMonthlyExpenses'] = $resIncomeExpenses['borrowerTotalMonthlyExpenses'];
        $incomeInfo['coborrowerTotalMonthlyExpenses'] = $resIncomeExpenses['coborrowerTotalMonthlyExpenses'];
        $incomeInfo['totalMonthlyExpensesBorrowerAndCoborrower'] = $resIncomeExpenses['totalMonthlyExpensesBorrowerAndCoborrower'];
        $incomeInfo['totalMonthlyIncomeBorrowerAndCoborrower'] = $resIncomeExpenses['totalMonthlyIncomeBorrowerAndCoborrower'];

        $LMRClientTypeInfoArray = LMRClientTypeInfo::getReport($fetchTab, $LMRId); // refactored
        $ClientTypes = $LMRClientTypeInfoArray['ClientTypes'];
        $LMRClientTypeInfo = $LMRClientTypeInfoArray['LMRClientTypeInfo'];
        self::$fileLoanPrograms = array_values($ClientTypes);

        $borrowerMissingDoc = BorrowerMissingDoc::getReport(
            $fetchTab,
            $pkgID,
            $fileSelectedModuleCode,
            $ClientTypes,
            $processingCompanyId,
            $LMRId,
            $executiveId
        ); // refactored

        $res = LMRInternalLoanprograms::getReport($LMRId); // refactored
        $LMRInternalLoanprogramsLong = $res['LMRInternalLoanprogramsLong'];
        $LMRInternalLoanprograms = $res['LMRInternalLoanprograms'];
        self::$fileInternalLoanPrograms = $LMRInternalLoanprograms;
        self::$fileInternalLoanProgramsName = $LMRInternalLoanprogramsLong;

        $LMRadditionalLoanprograms = LMRadditionalLoanprograms::getReport($LMRId); // refactored
        self::$fileAdditionalLoanPrograms = $LMRadditionalLoanprograms;

        $externalBroker = 0;
        if ($userGroup == 'Agent') {
            $brokerInfoArray = getBrokerInfo::getReport(['brokerNumber' => $infoArray['userNumber']]); // refactored
            $externalBroker = $brokerInfoArray['externalBroker'] ?? 0;
        }
        $PCCheckListInfo = PCCheckListInfo::getReport(
            $fetchTab,
            $UType,
            $fileSelectedModuleCode,
            $processingCompanyId,
            $ClientTypes,
            $externalBroker ?? 0
        ); // refactored
        $fileCheckListInfo = fileCheckListInfo::getReport($fetchTab, $UType, $LMRId, $externalBroker); // refactored

        $LMRChecklist = LMRChecklist::getReport($fetchTab, $LMRId); // refactored
        $LMRChecklistInfo = $LMRChecklist['LMRChecklistInfo'];
        $LMRChecklistInfoNew = $LMRChecklist['LMRChecklistInfoNew'];

        $QAInfo = QAInfo::getReport($fetchTab, $LMRId); // refactored
        $AssetsInfo = AssetsInfo::getReport($fetchTab, $LMRId); // refactored
        $CourtInfo = CourtInfo::getReport($fetchTab, $LMRId); // refactored

        $legalBankRuptcyInfo = legalBankRuptcyInfo::getReport($fetchTab, $LMRId); // refactored
        $ACHInfo = ACHInfo::getReport($fetchTab, $LMRId); // refactored

        $fileExtensionInfo = [];

        $temp = tblFileExtensionOptions::GetAll(['LMRId' => $LMRId, 'status' => 1]);
        foreach ($temp as $item) {
            $fileExtensionInfo[] = $item->toArray();
        }

        $PCFieldsInfo = []; // unused 8/27/2022

        $PCquickAppFieldsInfo = PCquickAppFieldsInfo::getReport($processingCompanyId); // refactored
        $CCInfo = CCInfo::getReport($fetchTab, $LMRId); // refactored
        $proposalInfo = proposalInfo::getReport($fetchTab, $LMRId); // refactored
        $hardshipDescInfo = hardshipDescInfo::getReport($fetchTab, $LMRId); // refactored
        $REBrokerInfo = REBrokerInfo::getReport($fetchTab, $REBrokerId); // refactored

        $clientInfoArray = clientInfoArray::getReport($fetchTab, $clientId); // refactored
        $PCStatusInfo = PCStatusInfo::getReport($fetchTab, $processingCompanyId, $fileSelectedModuleCode); // refactored
        $PCSubStatusInfo = PCSubStatusInfo::getReport($fetchTab, $processingCompanyId, $fileSelectedModuleCode); // refactored
        $fileSubstatusInfo = fileSubstatusInfo::getReport($fetchTab, $LMRId, $fileSelectedModuleCode); // refactored
        $billingUrlInfo = billingUrlInfo::getReport($fetchTab, $LMRId); // refactored

        $res = AdditionalInfo::getReport($fetchTab, $LMRId); // refactored
        $AdditionalInfo = $res['AdditionalInfo'];
        $LIDsArray = $res['LIDsArray'];
        $AdditionalLienContactInfo = [];
        if (count($LIDsArray) > 0) {
            $AdditionalLienContactInfo = AdditionalLienContactInfo::getReport($LIDsArray, $fetchTab); // refactored
        }


        $FileProInfo = FileProInfo::getReport($fetchTab, $LMRId); // refactored
        $FilePropInfo = $FileProInfo[0];
        $principalResInfo = principalResInfo::getReport($fetchTab, $LMRId); // refactored
        $propertyCountyInfo = propertyCountyInfo::getReport($fetchTab, $propertyState); // refactored
        $coBorrowerCountyInfo = coBorrowerCountyInfo::getReport($fetchTab, $coBorrowerMailingState); // refactored
        $branchClientTypeInfo = branchClientTypeInfo::getReport($fetchTab, $executiveId, $fileSelectedModuleCode); // refactored
        $branchModuleInfo = branchModuleInfo::getReport($fetchTab, $executiveId); // refactored
        $moduleFileTabInfo = moduleFileTabInfo::getReport($fetchTab, $fileSelectedModuleCode, $executiveId); // refactored

        $res = cklistNotRequired::getReport($fetchTab, $LMRId); // refactored
        $cklistNotRequiredArray = $res['cklistNotRequiredArray'] ?? [];
        $cklistNotRequiredNewArray = $res['cklistNotRequiredNewArray'] ?? [];
        $chkNotReqEmpInfo = $res['chkNotReqEmpInfo'] ?? [];
        $chkNotReqAgentInfo = $res['chkNotReqAgentInfo'] ?? [];
        $chkNotReqBranchInfo = $res['chkNotReqBranchInfo'] ?? [];

        $LMRWFArray = LMRWFArray::getReport($fetchTab, $LMRId); // refactored
        $HUDBasicInfoArray = HUDBasicInfoArray::getReport($fetchTab, $LMRId); // refactored
        $HUDLoanTypeInfo = HUDLoanTypeInfo::getReport($fetchTab, $LMRId); // refactored
        $HUDTransactionInfo = HUDTransactionInfo::getReport($fetchTab, $LMRId); // refactored
        $HUDSettlementInfo = HUDSettlementInfo::getReport($fetchTab, $LMRId); // refactored
        $HUDItemsPayableInfo = HUDItemsPayableInfo::getReport($fetchTab, $LMRId); // refactored
        $HUDLenderToPayInfo = HUDLenderToPayInfo::getReport($fetchTab, $LMRId); // refactored
        $HUDReservesDepositInfo = HUDReservesDepositInfo::getReport($fetchTab, $LMRId); // refactored
        $HUDAdditionalChargesInfo = HUDAdditionalChargesInfo::getReport($fetchTab, $LMRId); // refactored
        $creditorInfo = creditorInfo::getReport($fetchTab, $LMRId, $CIID); // refactored
        $creditorInfoStatus = creditorInfoStatus::getReport($fetchTab, $LMRId); // refactored
        $SSProposalInfo = SSProposalInfo::getReport($fetchTab, $LMRId); // refactored
        $RESTInfo = RESTInfo::getReport($fetchTab, $LMRId); // refactored
        $RESTNotesInfo = RESTNotesInfo::getReport($fetchTab, $LMRId); // refactored
        $proposalSummaryInfo = proposalSummaryInfo::getReport($fetchTab, $LMRId); // refactored
        $SSProposalSummaryInfo = SSProposalSummaryInfo::getReport($fetchTab, $LMRId); // refactored
        $HAFADocInfo = HAFADocInfo::getReport($fetchTab, $LMRId); // refactored
        $emailHistoryInfo = emailHistoryInfo::getReport($fetchTab, $LMRId); // refactored
        $substatusHistoryInfo = substatusHistoryInfo::getReport($fetchTab, $LMRId); // refactored
        $primeStatusHistoryInfo = primeStatusHistoryInfo::getReport($fetchTab, $LMRId); // refactored
        $assignedStaffHistoryInfo = assignedStaffHistoryInfo::getReport($fetchTab, $LMRId); // refactored

        $fax = Fax::getReport($fetchTab, $LMRId); // refactored
        $faxedDocHistoryInfo = $fax['faxedDocHistoryInfo'];
        $faxedDocRecipientInfo = $fax['faxedDocRecipientInfo'];
        $faxedSentDocInfo = $fax['faxedSentDocInfo'];


        $history = History::getReport(
            $fetchTab,
            $UType,
            $processingCompanyId,
            $userGroup,
            $LMRId,
            $ClientTypes,
            $LMRInternalLoanprograms,
            $LMRadditionalLoanprograms,
            $docID
        ); // refactored

        $PCWFServiceTypeArray = $history['PCWFServiceTypeArray'];
        $PCWFHistoryInfo = $history['PCWFHistoryInfo'];
        $LMRWFHistoryInfo = $history['LMRWFHistoryInfo'];
        $PCClientWFServiceTypeArray = $history['PCClientWFServiceTypeArray'];

        $workflow = Workflow::getReport($docID, $fetchTab, $LMRId); // refactored

        $empWFNInfoArray = $workflow['empWFNInfoArray'] ?? [];
        $agentWFNInfoArray = $workflow['agentWFNInfoArray'] ?? [];
        $branchWFNInfoArray = $workflow['branchWFNInfoArray'] ?? [];
        $agentCLInfoArray = $workflow['agentCLInfoArray'] ?? [];
        $branchCLInfoArray = $workflow['branchCLInfoArray'] ?? [];
        $empCLInfoArray = $workflow['empCLInfoArray'] ?? [];
        $fileWFNotesInfo = $workflow['fileWFNotesInfo'] ?? [];
        $fileChecklistNotesInfo = $workflow['fileChecklistNotesInfo'] ?? [];
        $fileChecklistNotesInfoNew = $workflow['fileChecklistNotesInfoNew'] ?? [];

        $res = WFInfo::getReport($fetchTab, $LMRId); // refactored
        $empWFInfo = $res['empWFInfo'];
        $agentWFInfo = $res['agentWFInfo'];
        $branchWFInfo = $res['branchWFInfo'];
        $WFListArray = $res['WFListArray'];

        $relatedDocs = relatedDocs::getReport($fetchTab, $LMRId, $linkedDocID); // refactored
        $docArray = $relatedDocs['docArray'];
        $propertyValuationDocInfo = $relatedDocs['propertyValuationDocInfo'];
        $HMLOPropValuationDocInfo = $relatedDocs['HMLOPropValuationDocInfo'];

        $propertyValuationDocs = propertyValuationDocs::getReport($fetchTab, $LMRId); // refactored
        $scheduledEmail = scheduledEmail::getReport($fetchTab, $clientId); // refactored
        $planInfoArray = planInfoArray::getReport($fetchTab, $processingCompanyId); // refactored
        $billingInfoArray = billingInfoArray::getReport($fetchTab, $LMRId); // refactored

        $eSignedDoc = []; // unused 8/27/2022

        $JoinderAppInfo = JoinderAppInfo::getReport($fetchTab, $LMRId); // refactored
        $purchaseInfo = purchaseInfo::getReport($fetchTab, $LMRId, $pkgID); // refactored
        $PCServiceType = PCServiceType::getReport($fetchTab, $processingCompanyId); // refactored

        $taskInfo = taskInfo::getReport($fetchTab, $LMRId); // refactored
        $taskIDArray = $taskInfo['taskIDArray'];
        $taskInfo = $taskInfo['taskInfo'];


        $clientPaymentInfo = clientPaymentInfo::getReport($fetchTab, $clientId); // refactored
        $legalContractInfo = legalContractInfo::getReport($fetchTab, $processingCompanyId, $executiveId); // refactored
        $PCLegalContractInfo = PCLegalContractInfo::getReport($fetchTab, $legalContractInfo, $processingCompanyId); // refactored
        $HRRequiredCheckListInfo = HRRequiredCheckListInfo::getReport($fetchTab, $LMRId); // refactored
        $HRInfo = HRInfo::getReport($fetchTab, $LMRId); // refactored
        $HRHistoryInfo = HRHistoryInfo::getReport($fetchTab, $LMRId); // refactored

        /** Loan Audit **/
        $res = LoanAudit::getReport($fetchTab, $LMRId, $UType); // refactored

        $loanAuditInfo = $res['loanAuditInfo'] ?? [];
        $loanAuditProductInfo = $res['loanAuditProductInfo'] ?? [];
        $loanAuditChecklistInfo = $res['loanAuditChecklistInfo'] ?? [];
        $loanAuditDocInfo = $res['loanAuditDocInfo'] ?? [];
        $loanAuditNotesInfo = $res['loanAuditNotesInfo'] ?? [];
        $empLAInfo = $res['empLAInfo'] ?? [];
        $branchLAInfo = $res['branchLAInfo'] ?? [];
        $agentLAInfo = $res['agentLAInfo'] ?? [];
        $empLADocInfo = $res['empLADocInfo'] ?? [];
        $branchLADocInfo = $res['branchLADocInfo'] ?? [];
        $agentLADocInfo = $res['agentLADocInfo'] ?? [];


        $studentLoanInfo = studentLoanInfo::getReport($fetchTab, $LMRId); // refactored
        $studentLoanQAInfo = studentLoanQAInfo::getReport($fetchTab, $LMRId); // refactored
        $studentReferenceInfo = studentReferenceInfo::getReport($fetchTab, $LMRId); // refactored
        $preliminaryWorkInfo = preliminaryWorkInfo::getReport($fetchTab, $LMRId); // refactored
        $interiorWorkInfo = interiorWorkInfo::getReport($fetchTab, $LMRId); // refactored
        $exteriorWorkInfo = exteriorWorkInfo::getReport($fetchTab, $LMRId); // refactored
        $otherWorkInfo = otherWorkInfo::getReport($fetchTab, $LMRId); // refactored
        $additionalStateBarInfo = additionalStateBarInfo::getReport($fetchTab, $BOStaffId); // refactored

        $fileSecondaryWFStatus = [];
        /* Hide the Workflow Actions custom functionality for all users as per Daniel Request. changed on Feb 11, 2016
            * Hide the fetch function - Viji on Jul 21, 2017 */

        if (count($QAInfo) > 0) {
            $QAInfo['condominiumOrHOAFeeAmtReceiver'] = '';
            $QAInfo['feeAmtReceiverAddress'] = '';
            $QAInfo['feeAmtReceiverCity'] = '';
            $QAInfo['feeAmtReceiverState'] = '';
            $QAInfo['feeAmtReceiverZip'] = '';
            $QAInfo['HOAOrCOAFeeAddress'] = '';
            $QAInfo['HOA2Email'] = '';
            $QAInfo['HOAOrCOAFeeCity'] = '';
            $QAInfo['HOAOrCOAFeeState'] = '';
            $QAInfo['HOAOrCOAFeeZip'] = '';
            $QAInfo['attorneyName'] = '';
            $QAInfo['attorneyFirmName'] = '';
            $QAInfo['attorneyPhone'] = '';
            $QAInfo['attorneyFax'] = '';
            $QAInfo['attorneyCell'] = '';
            $QAInfo['attorneyEmail'] = '';
            $QAInfo['attorneyAddress'] = '';
            $QAInfo['attorneyCity'] = '';
            $QAInfo['attorneyState'] = '';
            $QAInfo['attorneyZip'] = '';
            $QAInfo['creditCounselorName'] = '';
            $QAInfo['creditCounselorAgency'] = '';
            $QAInfo['creditCounselorPhone'] = '';
            $QAInfo['creditCounselorEmail'] = '';
        }

        if (count($listingRealtorInfo) > 0) {
            $listingRealtorInfo['HOContactName'] = '';
            $listingRealtorInfo['HOEmail'] = '';
            $listingRealtorInfo['HOPhone'] = '';
            $listingRealtorInfo['HOFax'] = '';
            $listingRealtorInfo['titleCo'] = '';
            $listingRealtorInfo['titleCompanyPhoneNumber'] = '';
            $listingRealtorInfo['sales2Fax'] = '';
            $listingRealtorInfo['titleCompanyEmail'] = '';
            $listingRealtorInfo['attorneyName'] = '';
            $listingRealtorInfo['firmName'] = '';
            $listingRealtorInfo['attorneyPhone'] = '';
            $listingRealtorInfo['attorneyFax'] = '';
            $listingRealtorInfo['attorneyCell'] = '';
            $listingRealtorInfo['attorneyEmail'] = '';
            $listingRealtorInfo['buyer1AttorneyName'] = '';
            $listingRealtorInfo['buyer1FirmName'] = '';
            $listingRealtorInfo['buyer1AttorneyPhone'] = '';
            $listingRealtorInfo['buyer1AttorneyFax'] = '';
            $listingRealtorInfo['buyer1AttorneyCell'] = '';
            $listingRealtorInfo['buyer1AttorneyEmail'] = '';
            $listingRealtorInfo['buyer1AttorneyAddress'] = '';
            $listingRealtorInfo['buyer1AttorneyCity'] = '';
            $listingRealtorInfo['buyer1AttorneyState'] = '';
            $listingRealtorInfo['buyer1AttorneyZip'] = '';
            $listingRealtorInfo['buyer2AttorneyName'] = '';
            $listingRealtorInfo['buyer2FirmName'] = '';
            $listingRealtorInfo['buyer2AttorneyPhone'] = '';
            $listingRealtorInfo['buyer2AttorneyFax'] = '';
            $listingRealtorInfo['buyer2AttorneyCell'] = '';
            $listingRealtorInfo['buyer2AttorneyEmail'] = '';
            $listingRealtorInfo['buyer2AttorneyAddress'] = '';
            $listingRealtorInfo['buyer2AttorneyCity'] = '';
            $listingRealtorInfo['buyer2AttorneyState'] = '';
            $listingRealtorInfo['buyer2AttorneyZip'] = '';
            $listingRealtorInfo['buyer3AttorneyName'] = '';
            $listingRealtorInfo['buyer3FirmName'] = '';
            $listingRealtorInfo['buyer3AttorneyPhone'] = '';
            $listingRealtorInfo['buyer3AttorneyFax'] = '';
            $listingRealtorInfo['buyer3AttorneyCell'] = '';
            $listingRealtorInfo['buyer3AttorneyEmail'] = '';
            $listingRealtorInfo['buyer3AttorneyAddress'] = '';
            $listingRealtorInfo['buyer3AttorneyCity'] = '';
            $listingRealtorInfo['buyer3AttorneyState'] = '';
            $listingRealtorInfo['buyer3AttorneyZip'] = '';
            $listingRealtorInfo['realtor'] = '';
            $listingRealtorInfo['agency'] = '';
            $listingRealtorInfo['realtorPhoneNumber'] = '';
            $listingRealtorInfo['sales1Fax'] = '';
            $listingRealtorInfo['sales1CellNo'] = '';
            $listingRealtorInfo['realtorEmail'] = '';
            $listingRealtorInfo['realtorAddress'] = '';
            $listingRealtorInfo['buyer1AgentName'] = '';
            $listingRealtorInfo['buyer1AgencyName'] = '';
            $listingRealtorInfo['buyer1Phone'] = '';
            $listingRealtorInfo['buyer1Cell'] = '';
            $listingRealtorInfo['buyer1Fax'] = '';
            $listingRealtorInfo['buyer1Email'] = '';
            $listingRealtorInfo['buyer2AgentName'] = '';
            $listingRealtorInfo['buyer2AgencyName'] = '';
            $listingRealtorInfo['buyer2Phone'] = '';
            $listingRealtorInfo['buyer2Cell'] = '';
            $listingRealtorInfo['buyer2Fax'] = '';
            $listingRealtorInfo['buyer2Email'] = '';
            $listingRealtorInfo['buyer3AgentName'] = '';
            $listingRealtorInfo['buyer3AgencyName'] = '';
            $listingRealtorInfo['buyer3Phone'] = '';
            $listingRealtorInfo['buyer3Cell'] = '';
            $listingRealtorInfo['buyer3Fax'] = '';
            $listingRealtorInfo['buyer3Email'] = '';
            $listingRealtorInfo['RELicenseNumber1'] = '';
            $listingRealtorInfo['RELicenseNumber2'] = '';
            $listingRealtorInfo['RELicenseNumber3'] = '';
        }

        $fileContacts = [];

        // always sort contacts by last, first to be consistent
        $qry = ' 
SELECT 
    * 
FROM tblFileContacts t1 
    join tblContacts t2 
WHERE 
    t1.CID = t2.CID 
  and t1.fileID = :fileID 
  and activeStatus = 1  
ORDER BY t1.sort_order, t2.contactLName, t2.contactName
';
        $rs = Database2::getInstance()->queryData($qry, ['fileID' => $LMRId]);

        foreach ($rs as $row) {
            if ($row['cRole'] == 'HOA1') {
                $QAInfo['condominiumOrHOAFeeAmtReceiver'] = $row['companyName'];
                $QAInfo['feeAmtReceiverAddress'] = $row['address'];
                $QAInfo['feeAmtReceiverCity'] = $row['city'];
                $QAInfo['feeAmtReceiverState'] = $row['state'];
                $QAInfo['feeAmtReceiverZip'] = $row['zip'];
                $QAInfo['HO1Notes'] = $row['description'];
                $listingRealtorInfo['HOContactName'] = $row['contactName'];
                $listingRealtorInfo['HOPhone'] = $row['phone'];
                $listingRealtorInfo['HOFax'] = $row['fax'];
                $listingRealtorInfo['HOEmail'] = $row['email'];
            }
            if (trim($row['cRole']) == 'HOA2') {
                $QAInfo['HOA2Email'] = $row['email'];
                $QAInfo['HOAOrCOAFeeAddress'] = $row['address'];
                $QAInfo['HOAOrCOAFeeCity'] = $row['city'];
                $QAInfo['HOAOrCOAFeeState'] = $row['state'];
                $QAInfo['HOAOrCOAFeeZip'] = $row['zip'];
                $QAInfo['HO2Notes'] = $row['description'];
            }
            if (trim($row['cRole']) == 'Title Rep') {
                $listingRealtorInfo['titleCo'] = $row['companyName'];
                $listingRealtorInfo['titleCompanyPhoneNumber'] = $row['phone'];
                $listingRealtorInfo['sales2Fax'] = $row['fax'];
                $listingRealtorInfo['titleCompanyEmail'] = $row['email'];
                $listingRealtorInfo['titleAddress'] = $row['address'];
                $listingRealtorInfo['titleCity'] = $row['city'];
                $listingRealtorInfo['titleState'] = $row['state'];
                $listingRealtorInfo['titleZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == 'Bank Attorney') {
                $QAInfo['attorneyName'] = $row['contactName'];
                $QAInfo['attorneyFirmName'] = $row['companyName'];
                $QAInfo['attorneyPhone'] = $row['phone'];
                $QAInfo['attorneyFax'] = $row['fax'];
                $QAInfo['attorneyCell'] = $row['cell'];
                $QAInfo['attorneyEmail'] = $row['email'];
                $QAInfo['attorneyAddress'] = $row['address'];
                $QAInfo['attorneyCity'] = $row['city'];
                $QAInfo['attorneyState'] = $row['state'];
                $QAInfo['attorneyZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == 'Counselor') {
                $QAInfo['creditCounselorName'] = $row['contactName'];
                $QAInfo['creditCounselorAgency'] = $row['companyName'];
                $QAInfo['creditCounselorPhone'] = $row['phone'];
                $QAInfo['creditCounselorEmail'] = $row['email'];
            }
            if (trim($row['cRole']) == 'PO Attorney') {
                $listingRealtorInfo['attorneyName'] = $row['contactName'];
                $listingRealtorInfo['firmName'] = $row['companyName'];
                $listingRealtorInfo['attorneyPhone'] = $row['phone'];
                $listingRealtorInfo['attorneyFax'] = $row['fax'];
                $listingRealtorInfo['attorneyCell'] = $row['cell'];
                $listingRealtorInfo['attorneyEmail'] = $row['email'];
            }
            if (trim($row['cRole']) == 'B1 Attorney') {
                $listingRealtorInfo['buyer1AttorneyName'] = $row['contactName'];
                $listingRealtorInfo['buyer1FirmName'] = $row['companyName'];
                $listingRealtorInfo['buyer1AttorneyPhone'] = $row['phone'];
                $listingRealtorInfo['buyer1AttorneyFax'] = $row['fax'];
                $listingRealtorInfo['buyer1AttorneyCell'] = $row['cell'];
                $listingRealtorInfo['buyer1AttorneyEmail'] = $row['email'];
                $listingRealtorInfo['buyer1AttorneyAddress'] = $row['address'];
                $listingRealtorInfo['buyer1AttorneyCity'] = $row['city'];
                $listingRealtorInfo['buyer1AttorneyState'] = $row['state'];
                $listingRealtorInfo['buyer1AttorneyZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == 'B2 Attorney') {
                $listingRealtorInfo['buyer2AttorneyName'] = $row['contactName'];
                $listingRealtorInfo['buyer2FirmName'] = $row['companyName'];
                $listingRealtorInfo['buyer2AttorneyPhone'] = $row['phone'];
                $listingRealtorInfo['buyer2AttorneyFax'] = $row['fax'];
                $listingRealtorInfo['buyer2AttorneyCell'] = $row['cell'];
                $listingRealtorInfo['buyer2AttorneyEmail'] = $row['email'];
                $listingRealtorInfo['buyer2AttorneyAddress'] = $row['address'];
                $listingRealtorInfo['buyer2AttorneyCity'] = $row['city'];
                $listingRealtorInfo['buyer2AttorneyState'] = $row['state'];
                $listingRealtorInfo['buyer2AttorneyZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == 'B3 Attorney') {
                $listingRealtorInfo['buyer3AttorneyName'] = $row['contactName'];
                $listingRealtorInfo['buyer3FirmName'] = $row['companyName'];
                $listingRealtorInfo['buyer3AttorneyPhone'] = $row['phone'];
                $listingRealtorInfo['buyer3AttorneyFax'] = $row['fax'];
                $listingRealtorInfo['buyer3AttorneyCell'] = $row['cell'];
                $listingRealtorInfo['buyer3AttorneyEmail'] = $row['email'];
                $listingRealtorInfo['buyer3AttorneyAddress'] = $row['address'];
                $listingRealtorInfo['buyer3AttorneyCity'] = $row['city'];
                $listingRealtorInfo['buyer3AttorneyState'] = $row['state'];
                $listingRealtorInfo['buyer3AttorneyZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == 'Realtor') {
                $listingRealtorInfo['realtor'] = $row['contactName'];
                $listingRealtorInfo['agency'] = $row['companyName'];
                $listingRealtorInfo['realtorPhoneNumber'] = $row['phone'];
                $listingRealtorInfo['sales1Fax'] = $row['fax'];
                $listingRealtorInfo['sales1CellNo'] = $row['cell'];
                $listingRealtorInfo['realtorEmail'] = $row['email'];
                $listingRealtorInfo['realtorAddress'] = $row['address'];
            }
            if (trim($row['cRole']) == 'B1 Agent') {
                $listingRealtorInfo['buyer1AgentName'] = $row['contactName'];
                $listingRealtorInfo['buyer1AgencyName'] = $row['companyName'];
                $listingRealtorInfo['buyer1Phone'] = $row['phone'];
                $listingRealtorInfo['buyer1Cell'] = $row['cell'];
                $listingRealtorInfo['buyer1Fax'] = $row['fax'];
                $listingRealtorInfo['buyer1Email'] = $row['email'];
                $listingRealtorInfo['RELicenseNumber1'] = $row['licenseNo'];
            }
            if (trim($row['cRole']) == 'B2 Agent') {
                $listingRealtorInfo['buyer2AgentName'] = $row['contactName'];
                $listingRealtorInfo['buyer2AgencyName'] = $row['companyName'];
                $listingRealtorInfo['buyer2Phone'] = $row['phone'];
                $listingRealtorInfo['buyer2Cell'] = $row['cell'];
                $listingRealtorInfo['buyer2Fax'] = $row['fax'];
                $listingRealtorInfo['buyer2Email'] = $row['email'];
                $listingRealtorInfo['RELicenseNumber2'] = $row['licenseNo'];
            }
            if (trim($row['cRole']) == 'B3 Agent') {
                $listingRealtorInfo['buyer3AgentName'] = $row['contactName'];
                $listingRealtorInfo['buyer3AgencyName'] = $row['companyName'];
                $listingRealtorInfo['buyer3Phone'] = $row['phone'];
                $listingRealtorInfo['buyer3Cell'] = $row['cell'];
                $listingRealtorInfo['buyer3Fax'] = $row['fax'];
                $listingRealtorInfo['buyer3Email'] = $row['email'];
                $listingRealtorInfo['RELicenseNumber3'] = $row['licenseNo'];
            }
            if (trim($row['cRole']) == 'Attorney') {
                $listingRealtorInfo['titleAttorneyName'] = $row['contactName'];
                $listingRealtorInfo['titleAttorneyFirmName'] = $row['companyName'];
                $listingRealtorInfo['titleAttorneyPhone'] = $row['phone'];
                $listingRealtorInfo['titleAttorneyEmail'] = $row['email'];
                $listingRealtorInfo['titleAttorneyAddress'] = $row['address'];
                $listingRealtorInfo['titleAttorneyCity'] = $row['city'];
                $listingRealtorInfo['titleAttorneyState'] = $row['state'];
                $listingRealtorInfo['titleAttorneyZip'] = $row['zip'];
                $listingRealtorInfo['titleAttorneyBarNo'] = $row['barNo'];
            }

            if (trim($row['cRole']) == 'Appraiser 1') {
                $listingRealtorInfo['appraiser1Company'] = $row['companyName'];
                $listingRealtorInfo['appraiser1'] = $row['contactName'];
                $listingRealtorInfo['appraiser1Phone'] = $row['phone'];
                $listingRealtorInfo['appraiser1Email'] = $row['email'];
            }
            if (trim($row['cRole']) == 'Appraiser 2') {
                $listingRealtorInfo['appraiser2Company'] = $row['companyName'];
                $listingRealtorInfo['appraiser2Phone'] = $row['phone'];
                $listingRealtorInfo['appraiser2'] = $row['contactName'];
                $listingRealtorInfo['appraiser2Email'] = $row['email'];
            }

            if (trim($row['cRole']) == 'BPO 1') {
                $listingRealtorInfo['realtor1Company'] = $row['companyName'];
                $listingRealtorInfo['BPO1'] = $row['contactName'];
                $listingRealtorInfo['realtorPhone'] = $row['phone'];
                $listingRealtorInfo['realtor1Email'] = $row['email'];
            }
            if (trim($row['cRole']) == 'BPO 2') {
                $listingRealtorInfo['realtor2Company'] = $row['companyName'];
                $listingRealtorInfo['realtor2Phone'] = $row['phone'];
                $listingRealtorInfo['BPO2'] = $row['contactName'];
                $listingRealtorInfo['realtor2Email'] = $row['email'];
            }
            if (trim($row['cRole']) == 'BPO 3') {
                $listingRealtorInfo['realtor3Company'] = $row['companyName'];
                $listingRealtorInfo['realtor3Phone'] = $row['phone'];
                $listingRealtorInfo['BPO3'] = $row['contactName'];
                $listingRealtorInfo['realtor3Email'] = $row['email'];
            }
            if (trim($row['cRole']) == 'Lender') {
                $listingRealtorInfo['serviceLenderName'] = $row['contactName'];
                $listingRealtorInfo['serviceLenderAddress'] = $row['address'];
                $listingRealtorInfo['serviceLenderCity'] = $row['city'];
                $listingRealtorInfo['serviceLenderState'] = $row['state'];
                $listingRealtorInfo['serviceLenderZip'] = $row['zip'];
            }
            if (trim($row['cRole']) == 'Trustee') {
                $listingRealtorInfo['trusteeName'] = $row['contactName'];
            }

            $fileContacts[$row['cRole']] = $row;
            $fileContacts['multiContact'][$row['cRole']][] = $row;
        }

        $file3Info = file3Info::getReport($fetchTab, $LMRId); // refactored

        $CFPBInfo = CFPBInfo::getReport($LMRId); // refactored
        $taskEmpInfo = taskEmpInfo::getReport($fetchTab, $taskIDArray); // refactored

        $res = CFPBReport::getReport($fetchTab, $LMRId, $UType, $infoArray); // refactored
        $CFPBDocsEmpIdArray = $res['CFPBDocsEmpIdArray'] ?? [];
        $CFPBDocsBranchIdArray = $res['CFPBDocsBranchIdArray'] ?? [];
        $CFPBDocsAgentIdArray = $res['CFPBDocsAgentIdArray'] ?? [];
        $CFPBAuditDocInfo = $res['CFPBAuditDocInfo'] ?? [];
        $CFPBAuditNotesInfo = $res['CFPBAuditNotesInfo'] ?? [];
        $empCFPBInfo = $res['empCFPBInfo'] ?? [];
        $branchCFPBInfo = $res['branchCFPBInfo'] ?? [];
        $agentCFPBInfo = $res['agentCFPBInfo'] ?? [];
        $clientCFPBInfo = $res['clientCFPBInfo'] ?? [];
        $CFPBAuditSearchSNHInfo = $res['CFPBAuditSearchSNHInfo'] ?? [];
        $CFPBAuditSearchSEHInfo = $res['CFPBAuditSearchSEHInfo'] ?? [];
        $CFPBAuditSearchSFHInfo = $res['CFPBAuditSearchSFHInfo'] ?? [];
        $CFPBAuditSearchSTHInfo = $res['CFPBAuditSearchSTHInfo'] ?? [];
        $CFPBAuditSearchSFHSentInfo = $res['CFPBAuditSearchSFHSentInfo'] ?? [];
        $CFPBDocsClientIdArray = $res['CFPBDocsClientIdArray'] ?? [];
        $CFPBAuditSearchSTHEmpInfo = $res['CFPBAuditSearchSTHEmpInfo'] ?? [];


        /* Search history */
        $taskHistoryInfo = []; // not used - 8/27/2022

        $notesHistoryInfo = notesHistoryInfo::getReport($fetchTab, $LMRId); // refactored

        $res = RAMReport::getReport($fetchTab, $processingCompanyId, $LMRId); // refactored
        $PCRAMAttorneyInfo = $res['PCRAMAttorneyInfo'] ?? [];
        $PCRAMAffiliateInfo = $res['PCRAMAffiliateInfo'] ?? [];
        $PCRAMFeeGroupInfo = $res['PCRAMFeeGroupInfo'] ?? [];
        $fileRAMClientInfo = $res['fileRAMClientInfo'] ?? [];
        $fileRAMPaymentInfo = $res['fileRAMPaymentInfo'] ?? [];
        $maxRowRAMPaymentInfo = $res['maxRowRAMPaymentInfo'] ?? [];


        $MFInfo = MFInfo::getReport($fetchTab, $LMRId); // refactored
        $FUInfo = FUInfo::getReport($fetchTab, $LMRId); // refactored

        $counselAttorney = [];

        if (isset($fileContacts['Counsel Attorney'])) {
            $counselAttorney = $fileContacts['Counsel Attorney'];
        }


        $LOExplanationInfo = LOExplanationInfo::getReport($fetchTab, $LMRId); // refactored
        $fileLoanOriginationInfo = fileLoanOriginationInfo::getReport($fetchTab, $LMRId); // refactored
        $fileLOAssetsInfo = fileLOAssetsInfo::getReport($fetchTab, $LMRId); // refactored
        $fileLOChekingSavingInfo = fileLOChekingSavingInfo::getReport($fetchTab, $LMRId); // refactored
        $liabilitiesInfo = liabilitiesInfo::getReport($fetchTab, $LMRId); // refactored
        $fileLOScheduleRealInfo = fileLOScheduleRealInfo::getReport($fetchTab, $LMRId); // refactored
        $fileLOPropInfo = fileLOPropInfo::getReport($fetchTab, $LMRId); // refactored
        $OtherCreditsInfo = OtherCreditsInfo::getReport($fetchTab, $LMRId); // refactored
        $borEmploymentInfo = borEmploymentInfo::getReport($fetchTab, $LMRId); // refactored
        $coBEmploymentInfo = coBEmploymentInfo::getReport($fetchTab, $LMRId); // refactored
        $fileLOExpensesInfo = fileLOExpensesInfo::getReport($fetchTab, $LMRId); // refactored
        $fileVelocityInfo = fileVelocityInfo::getReport($fetchTab, $LMRId); // refactored
        $fileLSSummaryInfo = fileLSSummaryInfo::getReport($fetchTab, $LMRId); // refactored
        $fileHMLOInfo = fileHMLOInfo::getReport($fetchTab, $saveOpt, $LMRId); // refactored
        $fileHMLOAssetsInfo = fileHMLOAssetsInfo::getReport($fetchTab, $LMRId); // refactored


        $rehabItemInfo = rehabItemInfo::getReport($fetchTab, $LMRId); // refactored
        $fileHMLOEntityInfo = fileHMLOEntityInfo::getReport($LMRId); // refactored
        $fileMemberOfficerInfo = fileMemberOfficerInfo::getReport($LMRId); // refactored
        $fileHMLOEntityRefInfo = fileHMLOEntityRefInfo::getReport($fetchTab, $LMRId); // refactored
        $fileHMLOBackGroundInfo = fileHMLOBackGroundInfo::getReport($fetchTab, $LMRId); // refactored
        $SBABackground = SBABackground::getReport($fetchTab, $LMRId); // refactored
        $sbaOtherBusinessData = sbaOtherBusinessData::getReport($LMRId); // refactored
        $fileHMLOExperienceInfo = fileHMLOExperienceInfo::getReport($fetchTab, $LMRId); // refactored
        self::$fileHMLOExperienceInfo = $fileHMLOExperienceInfo;
        $fileExpFilpGroundUp = fileExpFilpGroundUp::getReport($fetchTab, $LMRId); // refactored
        $fileHMLOListOfRepairs = fileHMLOListOfRepairs::getReport($fetchTab, $LMRId); // refactored

        $PCFileDocsCategory = PCFileDocsCategory::getReport($fetchTab, $processingCompanyId, $fileSelectedModuleCode); // refactored
        $PCFileDocsName = $PCFileDocsCategory['PCFileDocsName'];
        $PCFileDocsCategory = $PCFileDocsCategory['PCFileDocsCategory'];

        $fileHMLOChecklistUploadDocs = fileHMLOChecklistUploadDocs::getReport($fetchTab, $LMRId); // refactored
        $fileHMLOChecklistUploadDocsNew = $fileHMLOChecklistUploadDocs['fileHMLOChecklistUploadDocsNew'];
        $fileHMLOChecklistUploadDocs = $fileHMLOChecklistUploadDocs['fileHMLOChecklistUploadDocs'];

        $MFLoanInfo = MFLoanInfo::getReport($fetchTab, $LMRId); // refactored
        $MFLoanTermsInfo = MFLoanTermsInfo::getReport($LMRId); // refactored
        $dealAnalysisInfo = dealAnalysisInfo::getReport($fetchTab, $LMRId); // refactored
        $trustDocPositionsInfo = trustDocPositionsInfo::getReport($fetchTab, $pkgID, $txnID); // refactored

        $res = HMLOPCReport::getReport($fileSelectedModuleCode, $processingCompanyId, $ClientTypes); // refactored
        $HMLOPCTransactionType = $res['HMLOPCTransactionType'] ?? [];
        $HMLOPCPropertyType = $res['HMLOPCPropertyType'] ?? [];
        $HMLOPCBasicEntityType = $res['HMLOPCBasicEntityType'] ?? [];
        $HMLOPCExtnOption = $res['HMLOPCExtnOption'] ?? [];
        $HMLOPCLoanTerm = $res['HMLOPCLoanTerm'] ?? [];
        $HMLOPCOccupancy = $res['HMLOPCOccupancy'] ?? [];
        $HMLOPCState = $res['HMLOPCState'] ?? [];
        $HMLOPCNiches = $res['HMLOPCNiches'] ?? [];
        $HMLOPCAmortizationValInfo = $res['HMLOPCAmortizationValInfo'] ?? [];
        $HMLOPCBasicLoanInfo = $res['HMLOPCBasicLoanInfo'] ?? [];
        $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo = $res['HMLOPCBasicMinSeasoningPersonalBankruptcyInfo'] ?? [];
        $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo = $res['HMLOPCBasicMinSeasoningBusinessBankruptcyInfo'] ?? [];
        $HMLOPCBasicMinSeasoningForeclosureInfo = $res['HMLOPCBasicMinSeasoningForeclosureInfo'] ?? [];
        $HMLOPCBasicSBALoanProductInfo = $res['HMLOPCBasicSBALoanProductInfo'] ?? [];
        $HMLOPCBasicEquipmentTypeInfo = $res['HMLOPCBasicEquipmentTypeInfo'] ?? [];
        $HMLOPCBasicEntitityStateFormationInfo = $res['HMLOPCBasicEntitityStateFormationInfo'] ?? [];
        $HMLOPCBasicPaymentFrequencyInfo = $res['HMLOPCBasicPaymentFrequencyInfo'] ?? [];
        $HMLOPCBasicLoanPurposeInfo = $res['HMLOPCBasicLoanPurposeInfo'] ?? [];
        $HMLOPCBasicMinTimeInBusinessInfo = $res['HMLOPCBasicMinTimeInBusinessInfo'] ?? [];
        $HMLOPCBasicRateLockPeriodInfo = $res['HMLOPCBasicRateLockPeriodInfo'] ?? [];
        $HMLOPCBasicLoanExitStrategyInfo = $res['HMLOPCBasicLoanExitStrategyInfo'] ?? [];


        $PCBasicLoanTabFileIdExists = PCBasicLoanTabFileIdExists::getReport($fetchTab, $LMRId); // refactored
        $filRentRollInfo = filRentRollInfo::getReport($LMRId); // refactored
        $listingRealtorInfo2 = listingRealtorInfo2::getReport($LMRId); // refactored
        $fileFUEntityInfo = fileFUEntityInfo::getReport($fetchTab, $LMRId); // refactored
        $fileFUCreditEnhancementInfo = fileFUCreditEnhancementInfo::getReport($fetchTab, $LMRId); // refactored
        $fileFUCreditInfo = fileFUCreditInfo::getReport($fetchTab, $LMRId); // refactored
        $fileAdditionalGuarantorsInfo = fileAdditionalGuarantorsInfo::getReport($fetchTab, $LMRId); // refactored
        $equipmentInfo = equipmentInfo::getReport($fetchTab, $LMRId); // refactored
        $clientDocsArray = clientDocsArray::getReport($fetchTab, $clientId); // refactored
        $SellerInfoArray = SellerInfoArray::getReport($fetchTab, $LMRId); // refactored
        $budgetAndDrawsInfo = budgetAndDrawsInfo::getReport($fetchTab, $LMRId); // refactored
        $investorOtherInfo = investorOtherInfo::getReport($fetchTab, $LMRId); // refactored
        $fileBudgetAndDrawDoc = fileBudgetAndDrawDoc::getReport($fetchTab, $LMRId); // refactored
        $getInsuranceTypesGlobal = getInsuranceTypesGlobal::getReport($fetchTab); // refactored
        $getValuationMethodsGlobal = getValuationMethodsGlobal::getReport($fetchTab); // refactored
        $getInsuranceDtls = getInsuranceDtls::getReport($LMRId); // refactored


        $peerstreet = peerstreet::getReport($fetchTab, $LMRId); // refactored
        $alowareContact = alowareContact::getReport($LMRId, $fetchTab, $LMRInfo, $PCInfo, $file2Info); // refactored
        $thirdPartyServiceInfo = thirdPartyServiceInfo::getReport($fetchTab, $LMRId); // refactored
        $estimatedProjectCost = estimatedProjectCost::getReport($LMRId); // refactored
        $contingentLiabilities = contingentLiabilities::getReport($LMRId); // refactored
        $collateralArray = collateralArray::getReport($LMRId); // refactored
        $collateralValuesArray = collateralArray::getCollateralValuesReport($LMRId); // refactored
        $offerArray = offerArray::getReport($LMRId); // refactored
        $offerDocsArray = offerDocsArray::getReport($LMRId); // refactored
        $listingPageArray = listingPageArray::getReport($LMRId); // refactored
        $lpArray = lpArray::getReport($LMRId); // refactored
        $propMgmtArray = propMgmtArray::getReport($LMRId); // refactored
        $propMgmtDocsArray = propMgmtDocsArray::getReport($LMRId); // refactored
        $creditMemoArray = creditMemoArray::getReport($LMRId); // refactored
        $missingDocInfoArray = missingDocInfoArray::getReport($LMRId); // refactored
        $borrowerAlternateNamesArray = borrowerAlternateNamesArray::getReport($LMRId); // refactored
        $mortgagePropLoanArray = mortgagePropLoanArray::getReport($LMRId); // refactored
        $gogArray = gogArray::getReport($LMRId); // refactored
        $loanOriginatorInfo = loanOriginatorInfo::getReport($LMRId); // refactored
        $salesMethodInfo = salesMethodInfo::getReport($LMRId); // refactored
        $feeScheduleInfo = feeScheduleInfo::getReport($LMRId); // refactored
        $equipmentInformation = equipmentInformation::getReport($LMRId); // refactored
        $adverseActionInfo = adverseActionInfo::getReport($LMRId); // refactored

        $fileCalculatedValues = fileCalculatedValues::getReport($LMRId); // refactored
        loanSetting::init($LMRId);
        $loanSetting = loanSetting::$loanSetting;
        partnerShips::init($LMRId);
        dealSizer::init($LMRId);
        HUDFundingClosingInfo::init($LMRId);
        $HUDFundingClosingInfo = HUDFundingClosingInfo::$tblFundingClosing;
        creditDecision::init($LMRId);
        additionalGuarantors::init($LMRId);
        fileExtensionOptions::init($LMRId);

        $fileInfo[$LMRId] = [
            'processorComments' => function () {
                return getFileInfo::GetNotesCached()->processorCommentsArray;
            },
            'notesEmpInfo'      => function () {
                return getFileInfo::GetNotesCached()->notesEmpInfo;
            },
            'notesAgentInfo'    => function () {
                return getFileInfo::GetNotesCached()->notesAgentInfo;
            },
            'notesBranchInfo'   => function () {
                return getFileInfo::GetNotesCached()->notesBranchInfo;
            },
            'notesClientInfo'   => function () {
                return getFileInfo::GetNotesCached()->notesClientInfo;
            },
            'billableMinutes'   => function () {
                return getFileInfo::GetNotesCached()->billableMinutesInfo;
            },

            'LMRInfo'                   => $LMRInfo,
            'addedToFav'                => $addedToFav,
            'file2Info'                 => $file2Info,
            'ResponseInfo'              => $ResponseInfo,
            'BrokerInfo'                => $BrokerInfo,
            'dummyBrokerId'             => $dummyBrokerId,
            'SecondaryBrokerInfo'       => $SecondaryBrokerInfo,
            'BranchInfo'                => $BranchInfo,
            'employeeInfo'              => $employeeInfo,
            'AssignedStaffInfo'         => $tStaff,
            'AssignedBOStaffInfo'       => $BOStaff,
            'listingRealtorInfo'        => $listingRealtorInfo,
            'BillingPaymentInfo'        => $BillingPaymentInfo,
            'BillingFeeInfo'            => $BillingFeeInfo,
            'listingInfo'               => $listingInfo,
            'SSRegnInfo'                => $SSRegn,
            'hardshipInfo'              => $hardshipInfo,
            'PCInfo'                    => $PCInfo,
            'PCCheckListInfo'           => $PCCheckListInfo,
            'LMRClientTypeInfo'         => $LMRClientTypeInfo,
            'LMRChecklistInfo'          => $LMRChecklistInfo,
            'incomeInfo'                => $incomeInfo,
            'QAInfo'                    => $QAInfo,
            'AssetsInfo'                => $AssetsInfo,
            'ACHInfo'                   => $ACHInfo,
            'CCInfo'                    => $CCInfo,
            'LenderInfo1'               => $LenderInfo1,
            'LenderInfo2'               => $LenderInfo2,
            'proposalInfo'              => $proposalInfo,
            'hardshipDescInfo'          => $hardshipDescInfo,
            'RealEstateInfo'            => $RealEstateInfo,
            'REBrokerInfo'              => $REBrokerInfo,
            'clientInfo'                => $clientInfoArray,
            'PCStatusInfo'              => $PCStatusInfo,
            'PCSubStatusInfo'           => $PCSubStatusInfo,
            'fileSubstatusInfo'         => $fileSubstatusInfo,
            'lenderNotes'               => $lenderNotes,
            'billingUrlInfo'            => $billingUrlInfo,
            'AdditionalInfo'            => $AdditionalInfo,
            'FileProInfo'               => $FileProInfo,
            'FilePropInfo'              => $FilePropInfo,
            'principalResInfo'          => $principalResInfo,
            'propertyCountyInfo'        => $propertyCountyInfo,
            'coBorrowerCountyInfo'      => $coBorrowerCountyInfo,
            'branchClientTypeInfo'      => $branchClientTypeInfo,
            'cklistNotRequiredArray'    => $cklistNotRequiredArray,
            'chkNotReqEmpInfo'          => $chkNotReqEmpInfo,
            'chkNotReqAgentInfo'        => $chkNotReqAgentInfo,
            'chkNotReqBranchInfo'       => $chkNotReqBranchInfo,
            'LMRWFArray'                => $LMRWFArray,
            'HUDBasicInfo'              => $HUDBasicInfoArray,
            'HUDLoanTypeInfo'           => $HUDLoanTypeInfo,
            'HUDTransactionInfo'        => $HUDTransactionInfo,
            'HUDSettlementInfo'         => $HUDSettlementInfo,
            'HUDItemsPayableInfo'       => $HUDItemsPayableInfo,
            'HUDLenderToPayInfo'        => $HUDLenderToPayInfo,
            'HUDReservesDepositInfo'    => $HUDReservesDepositInfo,
            'HUDAdditionalChargesInfo'  => $HUDAdditionalChargesInfo,
            'RecentInfo'                => $RecentInfo,
            'CMAInfo'                   => $CMAInfo,
            'commissionInfo'            => $commissionInfo,
            'creditorInfo'              => $creditorInfo,
            'creditorInfoStatus'        => $creditorInfoStatus,
            'RESTInfo'                  => $RESTInfo,
            'RESTNotesInfo'             => $RESTNotesInfo,
            'SSProposalInfo'            => $SSProposalInfo,
            'emailHistoryInfo'          => $emailHistoryInfo,
            'substatusHistoryInfo'      => $substatusHistoryInfo,
            'primeStatusHistoryInfo'    => $primeStatusHistoryInfo,
            'assignedStaffHistoryInfo'  => $assignedStaffHistoryInfo,
            'proposalSummaryInfo'       => $proposalSummaryInfo,
            'SSInfo'                    => $SSInfo,
            'SSProposalSummaryInfo'     => $SSProposalSummaryInfo,
            'HAFADocInfo'               => $HAFADocInfo,
            'faxedDocHistoryInfo'       => $faxedDocHistoryInfo,
            'faxedDocRecipientInfo'     => $faxedDocRecipientInfo,
            'PCWFHistoryInfo'           => $PCWFHistoryInfo,
            'LMRWFHistoryInfo'          => $LMRWFHistoryInfo,
            'WFListArray'               => $WFListArray,
            'AdditionalLienContactInfo' => $AdditionalLienContactInfo,
            'docArray'                  => $docArray,
            'propertyValuation'         => $propertyValuationDocInfo,
            'scheduledEmail'            => $scheduledEmail,
            'planInfo'                  => $planInfoArray,
            'billingInfo'               => $billingInfoArray,
            'eSignedDoc'                => $eSignedDoc,
            'empWFInfo'                 => $empWFInfo,
            'branchWFInfo'              => $branchWFInfo,
            'agentWFInfo'               => $agentWFInfo,
            'JoinderAppInfo'            => $JoinderAppInfo,
            'PCServiceType'             => $PCServiceType,
            'purchaseInfo'              => $purchaseInfo,
            'taskInfo'                  => $taskInfo,
            'clientPaymentInfo'         => $clientPaymentInfo,
            'legalContractInfo'         => $legalContractInfo,
            'PCLegalContractInfo'       => $PCLegalContractInfo,
            'fileWFNotesInfo'           => $fileWFNotesInfo,
            'fileChecklistNotesInfo'    => $fileChecklistNotesInfo,
            'HRRequiredCheckListInfo'   => $HRRequiredCheckListInfo,
            'HRInfo'                    => $HRInfo,
            'HRHistoryInfo'             => $HRHistoryInfo,
            'loanAuditInfo'             => $loanAuditInfo,
            'loanAuditProductInfo'      => $loanAuditProductInfo,
            'loanAuditChecklistInfo'    => $loanAuditChecklistInfo,
            'loanAuditDocInfo'          => $loanAuditDocInfo,
            'loanAuditNotesInfo'        => $loanAuditNotesInfo,
            'empLAInfo'                 => $empLAInfo,
            'branchLAInfo'              => $branchLAInfo,
            'agentLAInfo'               => $agentLAInfo,
            'empLADocInfo'              => $empLADocInfo,
            'branchLADocInfo'           => $branchLADocInfo,
            'agentLADocInfo'            => $agentLADocInfo,
            'listingprincipalResInfo'   => $listingprincipalResInfo,
            'studentLoanInfo'           => $studentLoanInfo,
            'studentLoanQAInfo'         => $studentLoanQAInfo,
            'PCWFServiceType'           => $PCWFServiceTypeArray,
            'PCClientWFServiceType'     => $PCClientWFServiceTypeArray,
            'fileContacts'              => $fileContacts,
            'studentReferenceInfo'      => $studentReferenceInfo,
            'CourtInfo'                 => $CourtInfo,
            'legalBankRuptcyInfo'       => $legalBankRuptcyInfo,
            'preliminaryWorkInfo'       => $preliminaryWorkInfo,
            'interiorWorkInfo'          => $interiorWorkInfo,
            'exteriorWorkInfo'          => $exteriorWorkInfo,
            'otherWorkInfo'             => $otherWorkInfo,
            'fileSecondaryWFStatus'     => $fileSecondaryWFStatus,
            'counselAttorney'           => $counselAttorney,
            'file3Info'                 => $file3Info,
            'payeeInfo'                 => $payeeInfo,

            'CFPBInfo'              => $CFPBInfo,
            'CFPBAuditDocInfo'      => $CFPBAuditDocInfo,
            'CFPBDocsEmpIdArray'    => $CFPBDocsEmpIdArray,
            'CFPBDocsBranchIdArray' => $CFPBDocsBranchIdArray,
            'CFPBDocsAgentIdArray'  => $CFPBDocsAgentIdArray,
            'CFPBDocsClientIdArray' => $CFPBDocsClientIdArray,

            'CFPBAuditNotesInfo'         => $CFPBAuditNotesInfo,
            'empCFPBInfo'                => $empCFPBInfo,
            'branchCFPBInfo'             => $branchCFPBInfo,
            'agentCFPBInfo'              => $agentCFPBInfo,
            'clientCFPBInfo'             => $clientCFPBInfo,
            'notesHistoryInfo'           => $notesHistoryInfo,
            'taskHistoryInfo'            => $taskHistoryInfo,
            'taskEmpInfo'                => $taskEmpInfo,
            'CFPBAuditSearchSNHInfo'     => $CFPBAuditSearchSNHInfo,
            'CFPBAuditSearchSEHInfo'     => $CFPBAuditSearchSEHInfo,
            'CFPBAuditSearchSFHInfo'     => $CFPBAuditSearchSFHInfo,
            'CFPBAuditSearchSTHInfo'     => $CFPBAuditSearchSTHInfo,
            'faxedSentDocInfo'           => $faxedSentDocInfo,
            'CFPBAuditSearchSFHSentInfo' => $CFPBAuditSearchSFHSentInfo,
            'CFPBAuditSearchSTHEmpInfo'  => $CFPBAuditSearchSTHEmpInfo,

            'additionalStateBarInfo' => $additionalStateBarInfo,
            'branchModuleInfo'       => $branchModuleInfo,
            'fileModuleInfo'         => $fileModuleInfo,
            'moduleFileTabInfo'      => $moduleFileTabInfo,
            'PCRAMAttorneyInfo'      => $PCRAMAttorneyInfo,
            'PCRAMAffiliateInfo'     => $PCRAMAffiliateInfo,
            'PCRAMFeeGroupInfo'      => $PCRAMFeeGroupInfo,
            'fileRAMClientInfo'      => $fileRAMClientInfo,
            'fileRAMPaymentInfo'     => $fileRAMPaymentInfo,
            'maxRowRAMPaymentInfo'   => $maxRowRAMPaymentInfo,
            'MFInfo'                 => $MFInfo,

            'LOExplanationInfo'       => $LOExplanationInfo,
            'fileLoanOriginationInfo' => $fileLoanOriginationInfo,
            'fileLOAssetsInfo'        => $fileLOAssetsInfo,
            'fileLOChekingSavingInfo' => $fileLOChekingSavingInfo,
            'liabilitiesInfo'         => $liabilitiesInfo,
            'fileLOScheduleRealInfo'  => $fileLOScheduleRealInfo,
            'fileLOPropInfo'          => $fileLOPropInfo,

            'OtherCreditsInfo'  => $OtherCreditsInfo,
            'borEmploymentInfo' => $borEmploymentInfo,
            'coBEmploymentInfo' => $coBEmploymentInfo,

            'fileLOExpensesInfo' => $fileLOExpensesInfo,

            'FUInfo'                         => $FUInfo,
            'CLEmpInfo'                      => $empCLInfoArray,
            'CLBranchInfo'                   => $branchCLInfoArray,
            'CLAgentInfo'                    => $agentCLInfoArray,
            'fileVelocityInfo'               => $fileVelocityInfo,
            'fileStatusHistory'              => $fileStatusHistory,
            'fileLSSummaryInfo'              => $fileLSSummaryInfo,
            'fileHMLOInfo'                   => $fileHMLOInfo,
            'collateralPropertyInfo'         => $collateralPropertyInfo,
            'fileHMLOAssetsInfo'             => $fileHMLOAssetsInfo,
            'fileHMLOPropertyInfo'           => $fileHMLOPropertyInfo,
            'rehabItemInfo'                  => $rehabItemInfo,
            'fileHMLOEntityInfo'             => $fileHMLOEntityInfo,
            'fileMemberOfficerInfo'          => $fileMemberOfficerInfo,
            'fileHMLOEntityRefInfo'          => $fileHMLOEntityRefInfo,
            'fileHMLOBackGroundInfo'         => $fileHMLOBackGroundInfo,
            'SBABackground'                  => $SBABackground,
            'fileHMLOExperienceInfo'         => $fileHMLOExperienceInfo,
            'HMLOPropValuationDocInfo'       => $HMLOPropValuationDocInfo,
            'fileHMLOListOfRepairs'          => $fileHMLOListOfRepairs,
            'PCFileDocsCategory'             => $PCFileDocsCategory,
            'PCFileDocsName'                 => $PCFileDocsName,
            'fileHMLOChecklistUploadDocs'    => $fileHMLOChecklistUploadDocs,
            'MFLoanInfo'                     => $MFLoanInfo,
            'fileCheckListInfo'              => $fileCheckListInfo,
            'LMRChecklistInfoNew'            => $LMRChecklistInfoNew,
            'fileChecklistNotesInfoNew'      => $fileChecklistNotesInfoNew,
            'cklistNotRequiredNewArray'      => $cklistNotRequiredNewArray,
            'fileHMLOChecklistUploadDocsNew' => $fileHMLOChecklistUploadDocsNew,
            'borrowerMissingDoc'             => $borrowerMissingDoc,
            'fileHMLONewLoanInfo'            => $fileHMLONewLoanInfo,
            'MFLoanTermsInfo'                => $MFLoanTermsInfo,
            'dealAnalysisInfo'               => $dealAnalysisInfo,
            'trustDocPositionsInfo'          => $trustDocPositionsInfo,
            'notesArray'                     => $notesArray,

            'HMLOPCTransactionType'                         => $HMLOPCTransactionType,
            'HMLOPCPropertyType'                            => $HMLOPCPropertyType,
            'HMLOPCBasicEntityType'                         => $HMLOPCBasicEntityType,
            'HMLOPCExtnOption'                              => $HMLOPCExtnOption,
            'HMLOPCLoanTerm'                                => $HMLOPCLoanTerm,
            'HMLOPCOccupancy'                               => $HMLOPCOccupancy,
            'HMLOPCState'                                   => $HMLOPCState,
            'HMLOPCNiches'                                  => $HMLOPCNiches,
            'HMLOPCAmortizationValInfo'                     => $HMLOPCAmortizationValInfo,
            'HMLOPCBasicRateLockPeriodInfo'                 => $HMLOPCBasicRateLockPeriodInfo,
            'HMLOPCBasicLoanExitStrategyInfo'                 => $HMLOPCBasicLoanExitStrategyInfo,
            'HMLOPCBasicLoanInfo'                           => $HMLOPCBasicLoanInfo,
            'PCBasicLoanTabFileIdExists'                    => $PCBasicLoanTabFileIdExists,
            'filRentRollInfo'                               => $filRentRollInfo,
            'listingRealtorInfo2'                           => $listingRealtorInfo2,
            'fileFUEntityInfo'                              => $fileFUEntityInfo,
            'fileFUCreditEnhancementInfo'                   => $fileFUCreditEnhancementInfo,
            'fileFUCreditInfo'                              => $fileFUCreditInfo,
            'WFNEmpInfo'                                    => $empWFNInfoArray,
            'WFNBranchInfo'                                 => $branchWFNInfoArray,
            'WFNAgentInfo'                                  => $agentWFNInfoArray,
            'PCFieldsInfo'                                  => $PCFieldsInfo,
            'equipmentInfo'                                 => $equipmentInfo,
            'PCquickAppFieldsInfo'                          => $PCquickAppFieldsInfo,
            'propertyValuationDocs'                         => $propertyValuationDocs,
            'AddGuarantorsInfo'                             => $fileAdditionalGuarantorsInfo,
            'fileExpFilpGroundUp'                           => $fileExpFilpGroundUp,
            'clientDocsArray'                               => $clientDocsArray,
            'sellerInfo'                                    => $SellerInfoArray,
            'budgetAndDrawsInfo'                            => $budgetAndDrawsInfo,
            'fileBudgetAndDrawDoc'                          => $fileBudgetAndDrawDoc,
            'investorOtherInfo'                             => $investorOtherInfo,
            'getInsuranceTypesGlobal'                       => $getInsuranceTypesGlobal,
            'getValuationMethodsGlobal'                     => $getValuationMethodsGlobal,
            'getInsuranceDtls'                              => $getInsuranceDtls,
            'peerStreet'                                    => $peerstreet,
            'paydownInfo'                                   => $paydownInfo,
            'alowareContact'                                => $alowareContact,
            'thirdPartyServiceInfo'                         => $thirdPartyServiceInfo,
            'LMRInternalLoanprograms'                       => $LMRInternalLoanprograms,
            'LMRInternalLoanprogramsLong'                   => $LMRInternalLoanprogramsLong,
            'LMRadditionalLoanprograms'                     => $LMRadditionalLoanprograms,
            'sbaOtherBusinessData'                          => $sbaOtherBusinessData,
            'contingentLiabilities'                         => $contingentLiabilities,
            'estimatedProjectCost'                          => $estimatedProjectCost,
            'collateralArray'                               => $collateralArray,
            'collateralValuesArray'                         => $collateralValuesArray,
            'offerArray'                                    => $offerArray,
            'listingPageArray'                              => $listingPageArray,
            'lpArray'                                       => $lpArray,
            'offerDocsArray'                                => $offerDocsArray,
            'propMgmtArray'                                 => $propMgmtArray,
            'propMgmtDocsArray'                             => $propMgmtDocsArray,
            'creditMemoArray'                               => $creditMemoArray,
            'missingDocInfo'                                => $missingDocInfoArray,
            'HMLOPCBasicMinSeasoningPersonalBankruptcyInfo' => $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo,
            'HMLOPCBasicMinSeasoningBusinessBankruptcyInfo' => $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo,
            'HMLOPCBasicMinSeasoningForeclosureInfo'        => $HMLOPCBasicMinSeasoningForeclosureInfo,
            'HMLOPCBasicSBALoanProductInfo'                 => $HMLOPCBasicSBALoanProductInfo,
            'HMLOPCBasicEquipmentTypeInfo'                  => $HMLOPCBasicEquipmentTypeInfo,
            'HMLOPCBasicEntitityStateFormationInfo'         => $HMLOPCBasicEntitityStateFormationInfo,
            'HMLOPCBasicPaymentFrequencyInfo'               => $HMLOPCBasicPaymentFrequencyInfo,
            'HMLOPCBasicLoanPurposeInfo'                    => $HMLOPCBasicLoanPurposeInfo,
            'HMLOPCBasicMinTimeInBusinessInfo'              => $HMLOPCBasicMinTimeInBusinessInfo,
            'borrowerAlternateNames'                        => $borrowerAlternateNamesArray,
            'mortgagePropLoanInfo'                          => $mortgagePropLoanArray,
            'gogInfo'                                       => $gogArray,
            'loanOriginatorInfo'                            => $loanOriginatorInfo,
            'salesMethodInfo'                               => $salesMethodInfo,
            'feeScheduleInfo'                               => $feeScheduleInfo,
            'equipmentInformation'                          => $equipmentInformation,
            'adverseActionInfo'                             => $adverseActionInfo,
            'refinanceMortgageInfo'                         => $refinanceMortgageInfo,
            'loanSetting'                                   => $loanSetting,
            'HUDFundingClosingInfo'                         => $HUDFundingClosingInfo,
            'fileExtensionInfo'                             => $fileExtensionInfo,
            'fileCalculatedValues'                          => $fileCalculatedValues,
        ];
        return $fileInfo;
    }

    /* @var Notes[] $_GetNotesCached */
    private static ?array $_GetNotesCached = null;

    public static function GetNotesCached(): ?Notes
    {
        if (!self::$_GetNotesCached[self::$LMRId]) {
            $res = NotesInfo::getReport(
                self::$viewPrivateNotes,
                self::$viewPublicNotes,
                self::$LMRId,
                self::$notesId,
                self::$infoArray,
                self::$public,
                self::$fetchTab
            ); // refactored

            self::$_GetNotesCached[self::$LMRId] = new Notes($res);
        }

        return self::$_GetNotesCached[self::$LMRId];
    }
}
