<?php

namespace models\composite\oFile\getFileInfo;

use models\Database2;
use models\types\strongType;

/**
 *
 */
class HMLOPCReport extends strongType
{
    public static function getObjects(
        array $fileSelectedModuleCode,
        int $processingCompanyId,
        array $ClientTypes
    ): HMLOPCReport
    {
       $res = self::getReport($fileSelectedModuleCode, $processingCompanyId, $ClientTypes);
       return new self($res);
    }
    /**
     * @param array $fileSelectedModuleCode
     * @param int $processingCompanyId
     * @param array $ClientTypes
     * @return array
     */
    public static function getReport(
        array $fileSelectedModuleCode,
        int $processingCompanyId,
        array $ClientTypes
    ): array
    {
        $result = [];
        if (sizeof($ClientTypes) && (in_array('HMLO', $fileSelectedModuleCode) || in_array('loc', $fileSelectedModuleCode))) {

            $params = [];
            $params['PCID'] = $processingCompanyId;
            foreach($ClientTypes as $i => $item) {
                $params['loanPgm' . $i] = $item;
            }


            $qry = "SELECT BLID 
                FROM tblPCHMLOBasicLoanPgmInfo 
            WHERE loanPgm IN (" . Database2::GetPlaceholders(sizeof($ClientTypes), ':loanPgm', true) . ")
				AND PCID = :PCID 
				;";
            $row = Database2::getInstance()->queryData($qry, $params)[0] ?? [];
            $BLID = $row['BLID'] ?? 0;

            if ($BLID > 0) {
                $qry_multiple = " SELECT transactionType, 'HMLOPCTransactionType' AS myOpt FROM tblPCHMLOBasicLoanTransactionType WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT *, 'HMLOPCBasicLoanInfo' AS myOpt FROM tblPCHMLOBasicLoanInfo WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT propertyType, 'HMLOPCPropertyType' AS myOpt FROM tblPCHMLOBasicLoanPropertyType WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT entityType, 'HMLOPCBasicEntityType' AS myOpt FROM tblPCHMLOBasicLoanEntityType WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT extnOption, 'HMLOPCExtnOption' AS myOpt FROM tblPCHMLOBasicLoanExtensionOption WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT loanTerm, 'HMLOPCLoanTerm' AS myOpt FROM tblPCHMLOBasicLoanTermInfo WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT occupancy, 'HMLOPCOccupancy' AS myOpt FROM tblPCHMLOBasicLoanOccupancy WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT t1.stateCode, t2.stateName, 'HMLOPCState' AS myOpt FROM tblPCHMLOBasicLoanStateInfo t1, tblStates t2 WHERE t1.stateCode = t2.stateCode AND BLID = :BLID ; ";
                $qry_multiple .= " SELECT nichesID, 'HMLOPCNiches' AS myOpt FROM tblPCHMLOBasicLoanNichesInfo WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT AmortizationVal, 'HMLOPCAmortizationValInfo' AS myOpt FROM tblPCHMLOBasicLoanAmortizationInfo WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT MinSeasoningPersonalBankruptcyVal, 'HMLOPCBasicMinSeasoningPersonalBankruptcyInfo' AS myOpt FROM tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT MinSeasoningBusinessBankruptcyVal, 'HMLOPCBasicMinSeasoningBusinessBankruptcyInfo' AS myOpt FROM tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT MinSeasoningForeclosureVal, 'HMLOPCBasicMinSeasoningForeclosureInfo' AS myOpt FROM tblPCHMLOBasicLoanMinSeasoningForeclosure WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT SBALoanProductVal, 'HMLOPCBasicSBALoanProductInfo' AS myOpt FROM tblPCHMLOBasicLoanSBALoanProduct WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT equipmentTypeVal, 'HMLOPCBasicEquipmentTypeInfo' AS myOpt FROM tblPCHMLOBasicLoanEquipmentType WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT sateCode, 'HMLOPCBasicEntitityStateFormationInfo' AS myOpt FROM tblPCHMLOBasicLoanEntityStateOfFormation WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT paymentFrequencyVal, 'HMLOPCBasicPaymentFrequencyInfo' AS myOpt FROM tblPCHMLOBasicLoanPaymentFrequency WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT purposeName, 'HMLOPCBasicLoanPurposeInfo' AS myOpt FROM tblPCHMLOBasicLoanLoanPurpose WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT minTimeVal, 'HMLOPCBasicMinTimeInBusinessInfo' AS myOpt FROM tblPCHMLOBasicLoanMinTimeInBusiness WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT rateLockPeriod, 'HMLOPCBasicRateLockPeriodInfo' AS myOpt FROM tblPCHMLOBasicLoanRateLockPeriod WHERE BLID = :BLID ; ";
                $qry_multiple .= " SELECT exitStrategy, 'HMLOPCBasicLoanExitStrategyInfo' AS myOpt FROM tblPCHMLOBasicLoanExitStrategy WHERE BLID = :BLID ; ";

                $result = Database2::getInstance()->multiQueryData($qry_multiple, 'myOpt', [
                    'BLID' => $BLID,
                ]);
            }
        }
        // we could just return $result, but we're not because we want to be explicit for future debugging 8/28/2022
        return [
            'HMLOPCTransactionType' => $result['HMLOPCTransactionType'] ?? [],
            'HMLOPCBasicLoanInfo' => $result['HMLOPCBasicLoanInfo'] ?? [],
            'HMLOPCPropertyType' => $result['HMLOPCPropertyType'] ?? [],
            'HMLOPCBasicEntityType' => $result['HMLOPCBasicEntityType'] ?? [],
            'HMLOPCExtnOption' => $result['HMLOPCExtnOption'] ?? [],
            'HMLOPCLoanTerm' => $result['HMLOPCLoanTerm'] ?? [],
            'HMLOPCOccupancy' => $result['HMLOPCOccupancy'] ?? [],
            'HMLOPCState' => $result['HMLOPCState'] ?? [],
            'HMLOPCNiches' => $result['HMLOPCNiches'] ?? [],
            'HMLOPCAmortizationValInfo' => $result['HMLOPCAmortizationValInfo'] ?? [],
            'HMLOPCBasicMinSeasoningPersonalBankruptcyInfo' => $result['HMLOPCBasicMinSeasoningPersonalBankruptcyInfo'] ?? [],
            'HMLOPCBasicMinSeasoningBusinessBankruptcyInfo' => $result['HMLOPCBasicMinSeasoningBusinessBankruptcyInfo'] ?? [],
            'HMLOPCBasicMinSeasoningForeclosureInfo' => $result['HMLOPCBasicMinSeasoningForeclosureInfo'] ?? [],
            'HMLOPCBasicSBALoanProductInfo' => $result['HMLOPCBasicSBALoanProductInfo'] ?? [],
            'HMLOPCBasicEquipmentTypeInfo' => $result['HMLOPCBasicEquipmentTypeInfo'] ?? [],
            'HMLOPCBasicEntitityStateFormationInfo' => $result['HMLOPCBasicEntitityStateFormationInfo'] ?? [],
            'HMLOPCBasicPaymentFrequencyInfo' => $result['HMLOPCBasicPaymentFrequencyInfo'] ?? [],
            'HMLOPCBasicLoanPurposeInfo' => $result['HMLOPCBasicLoanPurposeInfo'] ?? [],
            'HMLOPCBasicMinTimeInBusinessInfo' => $result['HMLOPCBasicMinTimeInBusinessInfo'] ?? [],
            'HMLOPCBasicRateLockPeriodInfo' => $result['HMLOPCBasicRateLockPeriodInfo'] ?? [],
            'HMLOPCBasicLoanExitStrategyInfo' => $result['HMLOPCBasicLoanExitStrategyInfo'] ?? [],
        ];
    }
}
