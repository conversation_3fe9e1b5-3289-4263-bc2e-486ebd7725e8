<?php

namespace models\composite\oFile\getFileInfo;

use models\Database2;
use models\types\strongType;
use models\constants\gl\glThirdPartyServicesCRA;

class thirdPartyServiceInfo extends strongType
{
    public ?string $tID = null;
    public ?string $cra = null;
    public ?string $service = null;
    public ?string $paymentThrough = null;
    public ?string $request_type = null;
    public ?string $statusCode = null;
    public ?string $vendorOrderIdentifier = null;
    public ?string $notificationEmail = null;
    public ?string $created_at = null;
    public ?string $updated_at = null;
    public ?string $created_by = null;
    public ?string $loanIdentifier = null;
    public ?string $errors = null;
    public ?string $valuationModel = null;
    public ?string $response_xml = null;
    public ?int $created_at_sec = null;

    /**
     * @param string $fetchTab
     * @param int $LMRId
     * @return self[]
     */
    public static function getReport(string $fetchTab, int $LMRId): array
    {
        $thirdPartyServiceInfo = [];
        if ($fetchTab == 'TPS') {
            $qry = '
                SELECT tps.tID
                     , tps.cra                                          AS cra
                     , tps.service                                      AS service
                     , tps.paymentThrough
                     , tps.request_type
                     , tps.statusCode
                     , tps.vendorOrderIdentifier
                     , tps.notificationEmail
                     , tps.created_at
                     , tps.updated_at
                     , tps.created_by
                     , tps.loanIdentifier
                     , tps.errors
                     , tps.valuationModel
                     , tps.response_xml
                     , TIME_TO_SEC( TIMEDIFF(NOW(), tps.created_at) )   AS created_at_sec
                FROM tblThirdPartyService tps
                WHERE LMRId = :LMRId 
                ORDER BY tID DESC
            ';
            $thirdPartyServiceInfo = Database2::getInstance()->queryData($qry, [
                'LMRId' => $LMRId,
            ], function($row) {
                return new self($row);
            });

            $glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
            foreach ($thirdPartyServiceInfo as $row) {
                $row->service = $glThirdPartyServicesCRA[$row->cra]->Services[$row->service]['Name'] ?? $row->service;
                $row->cra = $glThirdPartyServicesCRA[$row->cra]->Name ?? $row->cra;
            }
        }
        return $thirdPartyServiceInfo;
    }
}