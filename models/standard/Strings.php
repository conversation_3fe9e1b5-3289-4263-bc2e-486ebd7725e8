<?php

namespace models\standard;

use Exception;
use models\composite\oEmail\saveMailsToQueue;
use models\composite\proposalFormula;
use models\constants\disAllowedChars;
use models\constants\gl\glAllowOthersToUpdatePLStatusArray;
use models\constants\gl\glDisplayOrderPLStatusArray;
use models\constants\gl\glHMLOPLStatusArray;
use models\constants\gl\glPCFilePrimaryStatusModuleArray;
use models\constants\gl\glPrimaryLoanModStatusArray;
use models\constants\newCreditCardTypeArray;
use models\constants\newCreditCardTypeCodeArray;
use models\Database2;
use models\cypher;
use NumberFormatter;
use SimpleXMLElement;


/**
 *
 */
class Strings
{
    public static function htmlentities(?string $string)
    {
        if (!$string) {
            return '';
        }

        $string = htmlentities($string);
        return str_replace('&amp;', '&', $string);
    }

    /**
     * @param $string
     * @param $ends_with
     * @param bool $case_sensitive
     * @return bool
     */
    public static function EndsWith($string, $ends_with, bool $case_sensitive = true): bool
    {
        if (!$case_sensitive) {
            return strcasecmp(substr($string, -strlen($ends_with), strlen($ends_with)), $ends_with) == 0;
        }
        return substr($string, -strlen($ends_with), strlen($ends_with)) === $ends_with;
    }

    /**
     * @param $hex
     * @return string
     */
    public static function Base16to10($hex): string
    {
        return base_convert($hex, 16, 10);
    }

    public static function ZeroPad(?string $str, int $len): string
    {
        return str_pad($str, $len, '0', STR_PAD_LEFT);
    }

    public static function hexToRgb($hex, $alpha = false): array
    {
        // https://stackoverflow.com/questions/15202079/convert-hex-color-to-rgb-values-in-php
        $hex = str_replace('#', '', $hex);
        $length = strlen($hex);
        $rgb['r'] = hexdec($length == 6 ? substr($hex, 0, 2) : ($length == 3 ? str_repeat(substr($hex, 0, 1), 2) : 0));
        $rgb['g'] = hexdec($length == 6 ? substr($hex, 2, 2) : ($length == 3 ? str_repeat(substr($hex, 1, 1), 2) : 0));
        $rgb['b'] = hexdec($length == 6 ? substr($hex, 4, 2) : ($length == 3 ? str_repeat(substr($hex, 2, 1), 2) : 0));
        if ($alpha) {
            $rgb['a'] = $alpha;
        }
        return $rgb;
    }

    public static function StringRepeatCS(string $pattern, int $multiplier, string $separator = ','): string
    {
        $t = [];
        for ($j = 0; $j < $multiplier; $j++) {
            $t[] = $pattern;
        }
        return implode($separator, $t);
    }

    public static function isBinary(string $check): bool
    {
        return preg_match('~[^\x20-\x7E\t\r\n]~', $check) > 0;
    }

    // https://stackoverflow.com/questions/4278106/how-to-check-if-a-string-is-base64-valid-in-php
    public static function is_base64($s): bool
    {
        return (bool)preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $s);
    }

    /**
     * @param $remove
     * @param $string
     * @return string
     */
    public static function RemoveFromStart($remove, $string): string
    {
        $remove_length = strlen($remove);

        return substr($string, $remove_length, strlen($string) - $remove_length);
    }

    /**
     * @param string|null $str
     * @return string
     */
    public static function AlphaNumericOnly(?string $str): string
    {
        if (!$str) {
            return '';
        }
        return trim(preg_replace('/\s+\.,/', ' ', preg_replace('/[^a-z\d\s]/i', ' ', $str)));
    }

    /**
     * @param $str
     * @return null|string|string[]
     */
    public static function KeyboardOnly($str)
    {
        $str = preg_replace('/[^a-z0-9\!\@\#\$\%\^\&\*\(\)\-\=\_\+\[\]\\\{\}\|\;\'\:\"\,\.\/\<\>\\\?\ \r\n]/si', '', $str);
        return preg_replace('/\s+/si', ' ', $str);
    }


    /**
     * @param string $str
     * @return string
     */
    public static function removeDisAllowedCharacters(string $str): string
    {
        $disAllowedChars = [
            '%', '+', '&', '@', '?',
            '#', '*', '$', '^', '!', '?', "'", "\"", '?',
            "\\", '??', ':', ';', '>', '<', '{', '}',
            '`', '=', '~', '.', '(', ')', '[', ']',
        ];

        $spaces = [' ', '  ', '   ', '    ', '     ', '/', ':'];
        $str = str_replace($spaces, '_', $str);
        return str_replace($disAllowedChars, '', $str);
    }

    /**
     * @param string|null $val
     * @param bool $return_orig_on_zero
     * @return float|string
     */
    public static function NumbersOnly(
        ?string $val,
        bool    $return_orig_on_zero = true
    )
    {
        $res = trim(preg_replace('/[^0-9]/si', '', $val));
        if (!$res) {
            return $return_orig_on_zero ? $val : 0;
        }
        return $res;
    }

    /**
     * @param string|null $str
     * @return float
     */
    public static function toNumber(?string $str): float
    {
        if (!$str) {
            return 0;
        }
        return floatval(str_replace(',', '', $str));
    }

    /**
     * @param array $arr
     */
    public static function SortArrayByValueLength(array &$arr)
    {
        usort($arr, function ($a, $b) {
            return strlen($b) - strlen($a);
        });
    }

    /**
     * @param $val
     * @return float
     */
    public static function Numeric($val): float
    {
        // handle scientific notation, force into decimal format
        if (stristr($val, 'E')) {
            $temp = explode('E', $val);
            if (sizeof($temp) == 2) {
                // https://stackoverflow.com/questions/1471674/why-is-php-printing-my-number-in-scientific-notation-when-i-specified-it-as-00
                return rtrim(rtrim(sprintf('%.8F', $temp[0] * pow(10, $temp[1])), '0'), '.') * 1.0;
            }
        }
        // handle basic numbers
        $val = preg_replace('/[^0-9\.-]/si', '', $val);
        if (is_numeric($val)) {
            $res = trim($val * 1.0);
            if ($res) {
                return $res;
            }
        }
        return 0;
    }

    public static function CurrencyForm(
        $val,
        int $sig_figs = 2
    ): string
    {
        return self::Currency($val, false, $sig_figs, '0.00');
    }

    /**
     * @param      $val
     * @param bool $dollar_sign
     * @param int $sig_figs
     * @param string $placeholder
     * @return string
     */
    public static function Currency(
        $val,
        bool $dollar_sign = true,
        int $sig_figs = 2,
        string $placeholder = '--'
    ): string
    {
        $val = self::Numeric($val);

        if (!is_numeric($val)) {
            return $placeholder;
        }

        if ($val * 1.0 == 0) {
            return $placeholder;
        }

        $res = number_format($val * 1.0, $sig_figs);
        if ($dollar_sign) {
            return '$' . $res;
        }
        return $res;
    }


    /**
     * @param $inParam
     * @return string
     */
    public static function booleanTextVal($inParam): string
    {
        if ($inParam == 1) return 'Yes';
        if ($inParam == 0) return 'No';
        if ($inParam == '') return 'NA';
        return '';
    }




    /**
     * @param string $number
     * @return string
     */
    public static function PhoneNumber(?string $number): string
    {
        $number = self::NumbersOnly($number);

        if (preg_match('/^\+?\d?(\d{3})(\d{3})(\d{4})$/', $number, $matches)) {
            return $matches[1] . '-' . $matches[2] . '-' . $matches[3];
        }
        return $number;
    }

    /**
     * @param $input
     * @return mixed
     */
    public static function stripSlashesFull($input)
    {
        if (is_array($input)) {
            $input = array_map('self::stripSlashesFull', $input);
        } elseif (is_object($input)) {
            $vars = get_object_vars($input);
            foreach ($vars as $k => $v) {
                $input->{$k} = self::stripSlashesFull($v);
            }
        } else {
            $input = addslashes(stripslashes($input));
        }
        return $input;
    }

    public static function REQUEST_URI(): string
    {
        $res = $_SERVER['REQUEST_URI'] ?? '';
        if ($res[strlen($res) - 1] == '/') {
            $res = substr($res, 0, -1);
        }
        return $res;
    }

    /**
     * @return string
     */
    public static function getCurrentURLFull(): string
    {
        return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    }

    /**
     * @return string
     */
    public static function getCurrentPageFromURL(): string
    {
        return basename($_SERVER['SCRIPT_FILENAME']);
    }

    /**
     * @return mixed
     */
    public static function getCurrentQUERY_STRING()
    {
        return $_SERVER['QUERY_STRING'] ?? '';
    }

    /**
     * @return string
     */
    public static function is_connected(): string
    {

        $st = (bool)@fsockopen('www.google.com', '80', $err_no, $err_str, 10);
        if ($st) {
            return '1';
        } else {
            return '0';
        }
    }
    //  Set a session variable

    /**
     * @param $field
     * @param $value
     */
    public static function SetSess($field, $value)
    {
        //        session_register ($field);
        $_SESSION[$field] = $value;
    }

    /**
     * @param $field
     * @return mixed|string
     */
    public static function GetSess($field)
    {
        $value = '';
        if (isset($_SESSION[$field])) {
            $value = $_SESSION[$field];
        }
        return $value;
    }

    /**
     * @param $val
     * @return string
     */
    public static function DisplayMessage($val): string
    {
        $script = '';

        if (trim($val)) {
            if (Arrays::getArrayValue('tabOpt', $_REQUEST) == 'PE') {
                $script = "toastrNotification('" . htmlentities($val) . "', 'success', 8000);";
            } elseif (strpos($val, 'Error') !== false) {
                $script = "toastrNotification('" . htmlentities($val) . "', 'error');";
            } else {
                $script = "toastrNotification('" . htmlentities($val) . "', 'success');";
            }
        }

        return '<script>
$(function() {
    ' . $script . '
});
</script>';

    }

    /**
     * @return string
     */
    public static function getNotifications(): string
    {
        $notificationList = '';
        if (isset($_SESSION['Notifications'])) {
            $notificationList = '<ul class="list-group">';
            foreach ($_SESSION['Notifications']['ErrorMsgs'] as $eachError) {
                $notificationList .= "<li  class='list-group-item text-danger'>" . $eachError . '</li>';
            }
            foreach ($_SESSION['Notifications']['successMsgs'] as $eachMsg) {
                $notificationList .= "<li  class='list-group-item text-success'>" . $eachMsg . '</li>';
            }
            $notificationList .= '</ul>';
            unset($_SESSION['Notifications']);
        }
        return $notificationList;
    }

    /**
     * @param $typeAE
     * @param $selValue
     * @return string
     */
    public static function isCheckedArray($typeAE, $selValue): string
    {
        $opStr = '';
        if (count($typeAE ?? []) > 0) {
            foreach ($typeAE as $val) {
                if (trim($val) == trim($selValue)) {
                    $opStr = ' checked ';
                }
            }
        }
        return $opStr;
    }

    /**
     *
     */
    public static function initSessVars()
    {
//        ini_set('session.cookie_lifetime', 0);
        self::SetSess('msg', '');
        self::SetSess('allow', false);
        self::SetSess('firstName', '');
        self::SetSess('lastName', '');
        self::SetSess('userNumber', 0);
        self::SetSess('processorAssignedCompany', '');
        self::SetSess('LMRAEId', 0);
        self::SetSess('assignedPCID', 0);
        self::SetSess('clientRFCode', 0);
        self::SetSess('allowExcelDownload', false);
        self::SetSess("allowPageView['Employee']['dashboard']", false);
        self::SetSess("allowPageView['Employee']['createfiles']", false);
        self::SetSess("allowPageView['Employee']['createtasks']", false);
        self::SetSess("allowPageView['Employee']['accessRestricted']", true);
        self::SetSess("allowPageView['Branch']['dashboard']", false);
        self::SetSess("allowPageView['Branch']['createfiles']", false);
        self::SetSess("allowPageView['Branch']['createtasks']", false);
        self::SetSess("allowPageView['Branch']['accessRestricted']", true);
        self::SetSess('allowedToEditOwnNotes', 1);
        self::SetSess('empSeeBilling', 1);
        self::SetSess('isPLO', 0);
        self::SetSess('allowEmpToViewAllFiles', 1);
        self::SetSess('loginOk', false);
        self::SetSess('isPCActive', false);
        self::SetSess('simLogin', 0);
        self::SetSess('userLogo', '');
        self::SetSess('userNo', '');
        self::SetSess('userRole', '');
        self::SetSess('userGroup', '');
        self::SetSess('lastActivity', 0);
        self::SetSess('allowToCreateBranch', 0);
        self::SetSess('viewPrivateNotes', 1);
        self::SetSess('branchSeePrivate', 0);
        self::SetSess('AEUserType', '');
        self::SetSess('allowLMRAEToEditCommission', 0);
        self::SetSess('allowedToDeleteUploadedDocs', 1);
        self::SetSess('permissionToREST', 1);
        self::SetSess('allowLMRToEditAgentProfile', 0);
        self::SetSess('allowToViewAllFiles', 0);
        self::SetSess('branchIDs', '');
        self::SetSess('branchesName', '');
        self::SetSess('agentLMRClientType', '');
        self::SetSess('seeBilling', '');
        self::SetSess('userCell', '');
        self::SetSess('userPhone', '');
        self::SetSess('private', 0);
        self::SetSess('lien1DispOpt', 'Close');
        self::SetSess('lien2DispOpt', 'Close');
        self::SetSess('allowClientToCreateHMLOFile', 0);
    }

    /**
     * @param $strVal
     * @param $selValue
     * @return string
     */
    public static function isChecked($strVal, $selValue): string
    {
        $opStr = '';
        if (trim($strVal) == trim($selValue)) {
            $opStr = ' checked ';
        }
        return $opStr;
    }

//    /**
//     * @param $encVal
//     * @return string
//     * @deprecated use cypher
//     */
//    public static function myDecryption($encVal): string
//    {
//        return cypher::myDecryption($encVal);
//    }

    /**
     * @param $val
     * @return float|int|string
     */
    public static function return_bytes($val)
    {
        $val = trim($val);
        $last = $val[strlen($val) - 1];
        switch ($last) {
            case 'k':
            case 'K':
                return (int)$val * 1024;
            case 'm':
            case 'M':
                return (int)$val * 1048576;
            default:
                return $val;
        }
    }

//    /**
//     * @param $decVal
//     * @return string
//     * @deprecated use cypher
//     */
//    public static function myEncryption($decVal): ?string
//    {
//        return cypher::myEncryption($decVal);
//    }

    /**
     * @param $pn
     * @return string
     */
    public static function cleanPhoneNo($pn): string
    {
        //$processedString = "";
        $processedString = str_replace('-', '', $pn);
        $processedString = str_replace(')', '', $processedString);
        $processedString = str_replace(' ', '', $processedString);
        $processedString = str_replace('(', '', $processedString);
        $processedString = str_replace('*', '', $processedString);
        $processedString = str_replace('-', '', $processedString);
        $processedString = str_replace(':', '', $processedString);
        $processedString = str_replace('x', '', $processedString);
        $processedString = str_replace('E', '', $processedString);
        $processedString = str_replace('t', '', $processedString);
        $processedString = str_replace('.', '', $processedString);
        $processedString = str_replace('_', '', $processedString);
        return trim($processedString);
    }

    /**
     * @param $ph
     * @return array|string|string[]|null
     */
    public static function formatPhoneNumber($ph)
    {
        //      $ph = preg_replace("[_-()*:a-zA-Z ]","",$ph);
        $ph = preg_replace('/[^0-9]/si', '', $ph);

        try {
            $p1 = substr($ph, 0, 3);
            $p2 = substr($ph, 3, 3);
            $p3 = substr($ph, 6, 4);
            $ex = substr($ph, 10, 6);
            if ($ph != '') {
                $ph = '(' . $p1 . ') ' . $p2 . '-' . $p3;
            }
            if ($ex != '') {
                $ph .= ' Ext ' . $ex;
            }
        } catch (Exception $e) {
        }
        return $ph;
    }

    /**
     * @param string $ccNo
     * @return string
     */
    public static function formatCCNumber(string $ccNo = ''): string
    {
        $maskCCNo = '';
        if ($ccNo != '') {
            $maskCCNo = str_replace(range(0, 9), '*', substr($ccNo, 0, -4)) . substr($ccNo, -4);
        }
        return $maskCCNo;
    }

    /**
     * @param string $cvvNo
     * @return string
     */
    public static function formatCVVNumber(string $cvvNo = ''): string
    {
        $maskCVVNo = '';
        if ($cvvNo != '') {
            $maskCVVNo = '****';
        }
        return $maskCVVNo;
    }

    /**
     * @param $phone
     * @return array
     */
    public static function splitPhoneNumber($phone): array
    {
        $phoneArray = [];
        //       $phone = preg_replace("[_-()*:a-zA-Z ]","",$phone);
        $phone = preg_replace_callback(
            '/[-_()*:a-z A-Z]/',
            function () {
                return '';
            },
            $phone
        );
        if (($phone != '') && ($phone != NULL) && ($phone != 'NULL')) {
            $phone1 = substr($phone, 0, 3);
            $phone2 = substr($phone, 3, 3);
            $phone3 = substr($phone, 6, 4);
            $ext = substr($phone, 10);
            $phoneArray = [
                'No1' => $phone1,
                'No2' => $phone2,
                'No3' => $phone3,
                'Ext' => $ext,
            ];
        }
        return $phoneArray;
    }

    /**
     * @param $phone
     * @return array|string|string[]|null
     */
    public static function cleanInputData($phone)
    {
        //       return preg_replace("[_-()*:a-zA-Z ]","",$phone);
        return preg_replace_callback(
            '/[-_()*:a-z A-Z]/',
            function () {
                return '';
            },
            $phone
        );
    }
    /*
 * Format SSN Number.
 */
    /**
     * @param string|null $ssn
     * @param bool $spaces
     * @return string
     */
    public static function formatSSNNumber(?string $ssn, bool $spaces = true): string
    {
        $ssn = self::numberOnly($ssn);
        if ($ssn && $ssn != 'NULL') {
            $ssn1 = substr($ssn, 0, 3);
            $ssn2 = substr($ssn, 3, 2);
            $ssn3 = substr($ssn, 5, 5);
            $ssn = $ssn1 . ' - ' . $ssn2 . ' - ' . $ssn3;
        } else {
            $ssn = '';
        }
        return $spaces ? $ssn : str_replace(' ', '', $ssn);
    }

    /*
 * split SSN Number.
 */
    /*
     * Format Cell Number.
     */
    /**
     * @param $phone
     * @return string
     */
    public static function formatCellNumber($phone): string
    {
        $phone = self::cleanPhoneNo($phone);
        if (($phone != '') && ($phone != NULL) && ($phone != 'NULL')) {
            $phone1 = substr($phone, 0, 3);
            $phone2 = substr($phone, 3, 3);
            $phone3 = substr($phone, 6, 4);
            $phone = '(' . $phone1 . ')-' . $phone2 . ' - ' . $phone3;
        } else {
            $phone = '';
        }
        return $phone;
    }

    /**
     * @param $phone
     * @return string
     */
    public static function formatCellNumberWithOut($phone): string
    {
        $phone = self::cleanPhoneNo($phone);
        if (($phone != '') && ($phone != NULL) && ($phone != 'NULL')) {
            $phone1 = substr($phone, 0, 3);
            $phone2 = substr($phone, 3, 3);
            $phone3 = substr($phone, 6, 4);
            $phone = $phone1 . '-' . $phone2 . '-' . $phone3;
        } else {
            $phone = '';
        }
        return $phone;
    }

    /**
     * @param $phone
     * @return string
     */
    public static function formatCellNumberWithOutHide($phone): string
    {
        $phone = self::cleanPhoneNo($phone);
        if (($phone != '') && ($phone != NULL) && ($phone != 'NULL')) {
//        $phone1 = substr($phone, 0, 3);
//        $phone2 = substr($phone, 3, 3);
            $phone3 = substr($phone, 6, 4);
            $phone = '*** - *** - ' . $phone3;
        } else {
            $phone = '';
        }
        return $phone;
    }

    /*
 * split CELL Number.
 */
    /**
     * @param $ssnNumber
     * @return array
     */
    public static function splitSSNNumber($ssnNumber): array
    {
        $ssnNumberArray = [];
        //       $ssnNumber = preg_replace("[_-()*:a-zA-Z ]","", $ssnNumber);
        $ssnNumber = preg_replace_callback(
            '/[-_()*:a-z A-Z]/',
            function () {
                return '';
            },
            $ssnNumber
        );
        if ($ssnNumber != '' && $ssnNumber != NULL && $ssnNumber != 'NULL') {
            $ssnNumberArray = [
                'No1' => substr($ssnNumber, 0, 3),
                'No2' => substr($ssnNumber, 3, 2),
                'No3' => substr($ssnNumber, 5, 4),
            ];
        }
        return $ssnNumberArray;
    }

    /*
 * Replace Special characters used in Highlights.
 */
    /**
     * @param $inputString
     * @return array|string|string[]
     */
    public static function processString($inputString)
    {
        //$processedString = "";
        $processedString = str_replace("\"", '&ldquo;', $inputString);
        $processedString = str_replace('&', '&amp;', $processedString);
        $processedString = str_replace('<', '&#60;', $processedString);
        $processedString = str_replace('>', '&#62;', $processedString);
        return str_replace("'", '&#39;', $processedString);
    }

    /**
     * @param $inputString
     * @return array|string|string[]
     */
    public static function processStringForPkg($inputString)
    {
        //$processedString = "";
        $spl_array = [
            '&#65533;' => "'",
            '?'        => "'",
            "\""       => '&ldquo;',
            '&'        => '&amp;',
            '<'        => '&#60;',
            '>'        => '&#62;',
            "'"        => '&#39;',
            ''        => '&#8220;',
            ''        => '&#8221;',
            ''        => '&#8364;',
            ''        => '&#8482;',
            '´'        => '&#180;',
            '£'        => '&#163;',
            '¢'        => '&#162;',
            '§'        => '&#167;',
            ''        => '&#8230;',
            ''        => '&#8211;',
            ''        => '&#8216;', '' => '&#8217;',
            ''        => '&#8226;', '·' => '&#183;', '' => '&#8212;',
            ''        => '&#8211;', '' => '&#8212;', '' => '&#8216;', '' => '&#8217;',
            ''        => '&#8218;', '' => '&#8220;', '' => '&#8221;', '' => '&#8222;',
            ''        => '&#8224;', '' => '&#8225;', '' => '&#8226;', '' => '&#8230;',
            ''        => '&#8240;', '' => '&#8364;', '' => '&#8482;', '°' => '&#176;',
            '±'        => '&#177;', '²' => '&#178;', '³' => '&#179;', '´' => '&#180;',
            'µ'        => '&#181;', '¶' => '&#182;', '·' => '&#183;', '¸' => '&#184;',
            '¹'        => '&#185;', 'º' => '&#186;', '»' => '&#187;', '¼' => '&#188;',
            '½'        => '&#189;', '¾' => '&#190;', '¿' => '&#191;', '¡' => '&#161;',
            '¢'        => '&#162;', '£' => '&#163;', '¤' => '&#164;', '¥' => '&#165;',
            '¦'        => '&#166;', '§' => '&#167;', '¨' => '&#168;', '©' => '&#169;',
            'ª'        => '&#170;', '«' => '&#171;', '¬' => '&#172;', '®' => '&#174;',
            '¯'        => '&#175;', '?' => '&spades;', '?' => '&clubs;', '?' => '&diams;',
            '?'        => '&hearts;', '?' => '&oline;', '?' => '&larr;', '?' => '&rarr;',
            '?'        => '&uarr;', '?' => '&darr;', '?' => '&#63;', 'À' => '&#192;',
            'Á'        => '&#193;', 'Â' => '&#194;', 'Ã' => '&#195;', 'Ä' => '&#196;',
            'Å'        => '&#197;', 'Æ' => '&#198;', 'Ç' => '&#199;', 'È' => '&#200;',
            'É'        => '&#201;', 'Ê' => '&#202;', 'Ë' => '&#203;', 'Ì' => '&#204;',
            'Í'        => '&#205;', 'Î' => '&#206;', 'Ï' => '&#207;', 'Ð' => '&#208;',
            'Ñ'        => '&#209;', 'Ò' => '&#210;', 'Ó' => '&#211;', 'Ô' => '&#212;',
            'Õ'        => '&#213;', 'Ö' => '&#214;', '×' => '&#215;', 'Ø' => '&#216;',
            'Ù'        => '&#217;', 'Ú' => '&#218;', 'Û' => '&#219;', 'Ü' => '&#220;',
            'Ý'        => '&#221;', 'Þ' => '&#222;', 'ß' => '&#223;', 'à' => '&#224;',
            'á'        => '&#225;', 'â' => '&#226;', 'ã' => '&#227;', 'ä' => '&#228;',
            'å'        => '&#229;', 'æ' => '&#230;', 'ç' => '&#231;', 'è' => '&#232;',
            'é'        => '&#233;', 'ê' => '&#234;', 'ë' => '&#235;', 'ì' => '&#236;',
            'í'        => '&#237;', 'î' => '&#238;', 'ï' => '&#239;', 'ð' => '&#240;',
            'ñ'        => '&#241;', 'ò' => '&#242;', 'ó' => '&#243;', 'ô' => '&#244;',
            'õ'        => '&#245;', 'ö' => '&#246;', '÷' => '&#247;', 'ø' => '&#248;',
            'ù'        => '&#249;', 'ú' => '&#250;', 'û' => '&#251;', 'ü' => '&#252;',
            'ý'        => '&#253;', 'þ' => '&#254;', 'ÿ' => '&#255;', '' => '&#338;',
            ''        => '&#339;', '' => '&#352;', '' => '&#353;', '' => '&#376;',
            ''        => '&#402;', '' => '&#127;', '' => '&#136;', '' => '&#139;',
            ''        => '&#142;', '' => '&#152;', '' => '&#155;', ' ' => '&#160;',
            ''        => '&#158;',
        ];
        $sp_chr_array = array_keys($spl_array);
        $html_chr_array = array_values($spl_array);
        return str_replace($sp_chr_array, $html_chr_array, $inputString);
    }

    /*
 * Replace Special characters used in Highlights.
 */
    /**
     * @param $str
     * @return array|string|string[]
     */
    public static function processStringForExport($str)
    {
        $str = str_replace('', "'", $str);
        $str = str_replace('&lsquo;', "'", $str);
        return str_replace('&rsquo;', "'", $str);
    }
    /* Remove space from the string and convert it into lowercase */
    /**
     * @param $strInput
     * @return array|mixed|string|string[]|null
     */
    public static function removeSpaceWithSpecialChars($strInput)
    {
        if ($strInput != '') {
            $strInput = strtolower(preg_replace('/[^A-Za-z0-9\-]/s', '', $strInput));    // Replaces all spaces and special characters.
            $strInput = preg_replace('/-+/', '', $strInput); // Replaces all hyphens.
        }
        return $strInput;
    }
    /*
 * Replace Special characters used in Highlights.
 */
    /**
     * @param $inputString
     * @return array|string|string[]
     */
    public static function replaceProcessString($inputString)
    {
//    $processedString = "";
        $processedString = str_replace('&ldquo;', "\"", $inputString);
        $processedString = str_replace('&amp;', '&', $processedString);
        $processedString = str_replace('&amp', '&', $processedString);
        $processedString = str_replace('&#60;', '<', $processedString);
        $processedString = str_replace('&#62;', '>', $processedString);
        $processedString = str_replace('&lt;', '<', $processedString);
        $processedString = str_replace('&gt;', '>', $processedString);
        $processedString = str_replace('&#39;', "'", $processedString);
        return str_replace('&#39', "'", $processedString);
    }

    /**
     * @param $urlQString
     * @return string
     */
    public static function processWebString($urlQString): string
    {
        $matchesArray = [];
        // $processedUrl = "";
        $urlQString = trim($urlQString);
        preg_match('/https:\/\//i', $urlQString, $matchesArray);
        if (count($matchesArray) > 0) {
            $processedUrl = $urlQString;
        } else {
            //            $processedUrl = preg_replace("/http:\/\//i","",$urlQString);
            $processedUrl = preg_replace_callback(
                '#http://#i',
                function () {
                    return '';
                },
                $urlQString
            );
            $processedUrl = 'http://' . $processedUrl;
        }
        return $processedUrl;
    }

    /**
     * @param $inputString
     * @return array|string|string[]
     */
    public static function replaceHtmlTags($inputString)
    {
        //$replacedText = "";
        /*
            $search = array ("'<script[^>]*?>.*?</script>'si",
                             "'<[\/\!]*?[^<>]*?>'si",
                             "'([\r\n])[\s]+'",
                             "'&(quot|#34);'i",
                             "'&(amp|#38);'i",
                             "'&(lt|#60);'i",
                             "'&(gt|#62);'i",
                             "'&(nbsp|#160);'i",
                             "'&(iexcl|#161);'i",
                             "'&(cent|#162);'i",
                             "'&(pound|#163);'i",
                             "'&(copy|#169);'i",
                             "'&#(\d+);'e");
    */
        $search = [
            '/<[\/\!]*?[^<>]*?>/i',
            "/([\r\n])[\s]+/",
            '/&(quot|#34);/i',
            '/&(amp|#38);/i',
            '/&(lt|#60);/i',
            '/&(gt|#62);/i',
            '/&(nbsp|#160);/i',
            '/&(iexcl|#161);/i',
            '/&(cent|#162);/i',
            '/&(pound|#163);/i',
            '/&(copy|#169);/i',
            '/&#(\d+);/',
        ];
        //$replace = array(" ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ");
        //        $replacedText = preg_replace($search, $replace, $inputString);
        $replacedText = $inputString;
        foreach ($search as $searchText) {
            $replacedText = preg_replace_callback(
                $searchText,
                function () {
                    return '';
                },
                $replacedText
            );
        }
        $replacedText = str_replace("'", "\'", $replacedText);
        return str_replace("\"", '&ldquo;', $replacedText);
    }

    /**
     * @param $inputString
     * @return float
     */
    public static function replaceCommaValues($inputString): float
    {
        if (!$inputString) {
            return 0.0;
        }

        $search = [
            '/<[\/\!]*?[^<>]*?>/i',
            "/([\r\n])[\s]+/",
            '/&(quot|#34);/i',
            '/&(amp|#38);/i',
            '/&(lt|#60);/i',
            '/&(gt|#62);/i',
            '/&(nbsp|#160);/i',
            '/&(iexcl|#161);/i',
            '/&(cent|#162);/i',
            '/&(pound|#163);/i',
            '/&(copy|#169);/i',
            '/&#(\d+);/',
            '/[a-zA-Z]/',
        ];
        $processedString = str_replace(' ', '', $inputString);
        $processedString = str_replace('$', '', $processedString);
        $processedString = str_replace(',', '', $processedString);
        foreach ($search as $searchText) {
            $processedString = preg_replace_callback(
                $searchText,
                function () {
                    return '';
                },
                $processedString
            );
        }
        return self::Numeric($processedString);
    }

    /**** Remove DisAllowed Chars *****/
    public static function removeDisAllowedChars($str)
    {
        $disAllowedChars = disAllowedChars::$disAllowedChars;
        $spaces = [' ', '  ', '   ', '    ', '     ', '/', ':'];
        $str = str_replace($spaces, '_', $str);
        return str_replace($disAllowedChars, '', $str);
    }

    /*
 * Clear headers in email
 */
    /**
     * @param $inputString
     * @return array|string|string[]
     */
    public static function processedHeader($inputString)
    {
        $replacedText = $inputString;
        $replacedText = str_replace("/'/i", '&lsquo;', $replacedText);
        $replacedText = str_replace("/\"/i", '&ldquo;', $replacedText);
        $replacedText = str_replace('/&/i', '&amp;', $replacedText);
        $replacedText = str_replace('/</i', '&#60;', $replacedText);
        //            $replacedText = preg_replace($search, $replace, $inputString);
        return str_replace('/>/i', '&#62;', $replacedText);
    }

    /**
     * @param $inputString
     * @return array|string|string[]
     */
    public static function replaceProcessedHeader($inputString)
    {
//    $replacedText = "";
//    $search = array(
//        "/\&amp;lsquo;/i",
//        "/&amp;lsquo;/i",
//        "/&amp;ldquo;/i",
//        "/&amp;/i",
//        "/&#60;/i",
//        "/&#62;/i",
//    );
//    $replace = array("'", "'", "\"", "&", "<", ">");
        //            $replacedText = preg_replace($search, $replace, $inputString);
        $replacedText = $inputString;
        $replacedText = preg_replace_callback(
            '/&amp;lsquo;/i',
            function () {
                return '\'';
            },
            $replacedText
        );
        $replacedText = preg_replace_callback(
            '/&amp;lsquo;/i',
            function () {
                return '\'';
            },
            $replacedText
        );
        $replacedText = preg_replace_callback(
            '/&amp;ldquo;/i',
            function () {
                return '"';
            },
            $replacedText
        );
        $replacedText = preg_replace_callback(
            '/&amp;/i',
            function () {
                return '&';
            },
            $replacedText
        );
        $replacedText = preg_replace_callback(
            '/&#60;/i',
            function () {
                return '<';
            },
            $replacedText
        );
        $replacedText = preg_replace_callback(
            '/&#62;/i',
            function () {
                return '>';
            },
            $replacedText
        );
        $replacedText = str_replace("\r\n", '<br>', $replacedText);
        return str_replace("\n", '<br>', $replacedText);
    }
    /*
 * Replace characters for REST.
 * Subtitute the special characters into its ascii values.
 */
    /**
     * @param $str
     * @return array|string|string[]
     */
    public static function RESTProcessString($str)
    {
        //$result = "";
        //      $result = preg_replace("/[\"^!<>'@&\/$#_,%*]/","", $str);
        $spl_array = [
            '&#65533;' => "'", '?' => "'", "\"" => '&ldquo;', '&' => '&amp;', '<' => '&#60;',
            '>'        => '&#62;', "'" => '&#39;', '' => '&#8220;', '' => '&#8221;', '' => '&#8364;',
            ''        => '&#8482;', '´' => '&#180;', '£' => '&#163;', '¢' => '&#162;', '§' => '&#167;',
            ''        => '&#8230;', '' => '&#8211;', '' => '&#8216;', '' => '&#8217;',
            ''        => '&#8226;', '·' => '&#183;', '' => '&#8212;', '' => '&#8211;',
            ''        => '&#8212;', '' => '&#8216;', '' => '&#8217;', '' => '&#8218;', '' => '&#8220;',
            ''        => '&#8221;', '' => '&#8222;', '' => '&#8224;', '' => '&#8225;', '' => '&#8226;',
            ''        => '&#8230;', '' => '&#8240;', '' => '&#8364;', '' => '&#8482;', '°' => '&#176;',
            '±'        => '&#177;', '²' => '&#178;', '³' => '&#179;', '´' => '&#180;', 'µ' => '&#181;',
            '¶'        => '&#182;', '·' => '&#183;', '¸' => '&#184;', '¹' => '&#185;', 'º' => '&#186;',
            '»'        => '&#187;', '¼' => '&#188;', '½' => '&#189;', '¾' => '&#190;', '¿' => '&#191;',
            '¡'        => '&#161;', '¢' => '&#162;', '£' => '&#163;', '¤' => '&#164;', '¥' => '&#165;',
            '¦'        => '&#166;', '§' => '&#167;', '¨' => '&#168;', '©' => '&#169;', 'ª' => '&#170;',
            '«'        => '&#171;', '¬' => '&#172;', '®' => '&#174;', '¯' => '&#175;', '?' => '&spades;',
            '?'        => '&clubs;', '?' => '&diams;', '?' => '&hearts;', '?' => '&oline;', '?' => '&larr;',
            '?'        => '&rarr;', '?' => '&uarr;', '?' => '&darr;', '?' => '&#63;', 'À' => '&#192;',
            'Á'        => '&#193;', 'Â' => '&#194;', 'Ã' => '&#195;', 'Ä' => '&#196;', 'Å' => '&#197;',
            'Æ'        => '&#198;', 'Ç' => '&#199;', 'È' => '&#200;', 'É' => '&#201;', 'Ê' => '&#202;',
            'Ë'        => '&#203;', 'Ì' => '&#204;', 'Í' => '&#205;', 'Î' => '&#206;', 'Ï' => '&#207;',
            'Ð'        => '&#208;', 'Ñ' => '&#209;', 'Ò' => '&#210;', 'Ó' => '&#211;', 'Ô' => '&#212;',
            'Õ'        => '&#213;', 'Ö' => '&#214;', '×' => '&#215;', 'Ø' => '&#216;', 'Ù' => '&#217;',
            'Ú'        => '&#218;', 'Û' => '&#219;', 'Ü' => '&#220;', 'Ý' => '&#221;', 'Þ' => '&#222;',
            'ß'        => '&#223;', 'à' => '&#224;', 'á' => '&#225;', 'â' => '&#226;', 'ã' => '&#227;',
            'ä'        => '&#228;', 'å' => '&#229;', 'æ' => '&#230;', 'ç' => '&#231;', 'è' => '&#232;',
            'é'        => '&#233;', 'ê' => '&#234;', 'ë' => '&#235;', 'ì' => '&#236;', 'í' => '&#237;',
            'î'        => '&#238;', 'ï' => '&#239;', 'ð' => '&#240;', 'ñ' => '&#241;', 'ò' => '&#242;',
            'ó'        => '&#243;', 'ô' => '&#244;', 'õ' => '&#245;', 'ö' => '&#246;', '÷' => '&#247;',
            'ø'        => '&#248;', 'ù' => '&#249;', 'ú' => '&#250;', 'û' => '&#251;', 'ü' => '&#252;',
            'ý'        => '&#253;', 'þ' => '&#254;', 'ÿ' => '&#255;', '' => '&#338;', '' => '&#339;',
            ''        => '&#352;', '' => '&#353;', '' => '&#376;', '' => '&#402;',
            ''        => '&#127;', '' => '&#136;', '' => '&#139;', '' => '&#142;',
            ''        => '&#152;', '' => '&#155;', ' ' => '&#173;', '' => '&#158;', '/' => '&#47;',
            '^'        => '&#94;', '{' => '&#123;', '}' => '&#125;', '!' => '&#33;',
            '@'        => '&#64;', '$' => '&#36;', '_' => '&#95;', ',' => '&#44;', '%' => '&#37;',
            '['        => '&#91;', ']' => '&#93;', '*' => '&#42;'
        ];
        $sp_chr_array = array_keys($spl_array);
        $html_chr_array = array_values($spl_array);
        return str_replace($sp_chr_array, $html_chr_array, $str);
    }
    /**
     * Single quote not accepted in tcpdf
     * Convert single quote to accept in PDF.
     */

    /**
     * @param string|null $str
     * @return string
     */
    public static function undoHTMLEntitiesForPDF(?string $str): string
    {
        if (!$str) {
            return '';
        }
        return str_replace('&#039;', '&#39;', $str);
    }

    /**
     * @param $infoArray
     * @param $key
     * @param $selValue
     * @return string
     */
    public static function isKeyChecked($infoArray, $key, $selValue): string
    {
        $opStr = '';
        if (count($infoArray ?? []) > 0) {
            foreach ($infoArray as $item) {
                if (is_object($item)) {
                    if (trim($item->$key) == trim($selValue)) {
                        $opStr = ' checked ';
                    }
                    continue;
                }
                if (trim($item[$key]) == trim($selValue)) {
                    $opStr = ' checked ';
                }
            }
        }
        return $opStr;
    }


    public static function implode2dServiceTypeArray($sepParameter, $array, $dimension = 0): string
    {
        global $glLMRClientTypeArray;
        $rtn = [];
        foreach ($array as $value) {
            if (isset($value[$dimension])) {
                if (array_key_exists($value[$dimension], $glLMRClientTypeArray)) {
                    $rtn[] = $glLMRClientTypeArray[$value[$dimension]];
                }
            }
        }
        return join($sepParameter, $rtn);
    }

    /**
     * @param $stateArray
     * @param $stateKey
     * @return mixed|string
     */
    public static function getStateFullName($stateArray, $stateKey)
    {
        $fullStateName = '';

        foreach ($stateArray as $s => $item) {
            $stateCode = $item['stateCode'];
            if (trim($stateKey) == trim($stateCode)) {
                $fullStateName = $item['stateName'];
            }
        }
        return $fullStateName;
    }

    /**
     * @param $name
     * @return false|int|string
     */

    public static function convertState($name)
    {
        $states = [
            'Alaska'         => 'AK',
            'Alabama'        => 'AL',
            'Arizona'        => 'AZ',
            'Arkansas'       => 'AR',
            'California'     => 'CA',
            'Colorado'       => 'CO',
            'Connecticut'    => 'CT',
            'Delaware'       => 'DE',
            'Florida'        => 'FL',
            'Georgia'        => 'GA',
            'Hawaii'         => 'HI',
            'Idaho'          => 'ID',
            'Illinois'       => 'IL',
            'Indiana'        => 'IN',
            'Iowa'           => 'IA',
            'Kansas'         => 'KS',
            'Kentucky'       => 'KY',
            'Louisiana'      => 'LA',
            'Maine'          => 'ME',
            'Maryland'       => 'MD',
            'Massachusetts'  => 'MA',
            'Michigan'       => 'MI',
            'Minnesota'      => 'MN',
            'Mississippi'    => 'MS',
            'Missouri'       => 'MO',
            'Montana'        => 'MT',
            'Nebraska'       => 'NE',
            'Nevada'         => 'NV',
            'New Hampshire'  => 'NH',
            'New Jersey'     => 'NJ',
            'New Mexico'     => 'NM',
            'New York'       => 'NY',
            'North Carolina' => 'NC',
            'North Dakota'   => 'ND',
            'Ohio'           => 'OH',
            'Oklahoma'       => 'OK',
            'Oregon'         => 'OR',
            'Pennsylvania'   => 'PA',
            'Rhode Island'   => 'RI',
            'South Carolina' => 'SC',
            'South Dakota'   => 'SD',
            'Tennessee'      => 'TN',
            'Texas'          => 'TX',
            'Utah'           => 'UT',
            'Vermont'        => 'VT',
            'Virginia'       => 'VA',
            'Washington'     => 'WA',
            'West Virginia'  => 'WV',
            'Wisconsin'      => 'WI',
            'Wyoming'        => 'WY',
            'Virgin Islands' => 'V.I.',
            'Guam'           => 'GU',
            'Puerto Rico'    => 'PR',
        ];
        // detect type name or abbr
        if (strlen($name) > 2) {
            $name = strtolower($name); //normalize data
            $name = ucwords($name);  //normalize data
            return $states[$name];
        } else {
            return array_search(strtoupper($name), $states);
        }
    }

    /**
     * @param $emailAddress
     * @return string
     */
    public static function hideDummyEmail($emailAddress): string
    {
        $emailAddress = trim($emailAddress);
        if (preg_match('/@dummyemail.com/i', $emailAddress)) {
            $returnedEmail = '';
        } else {
            $returnedEmail = $emailAddress;
        }
        return $returnedEmail;
    }

    /**
     * @param $inputArray
     * @return string
     */

    public static function getDefaultTAC($inputArray): string
    {
        $DIYBranchCompanyName = trim($inputArray['companyName']);
        $DIYBranchWebsite = trim($inputArray['website']);
        $DIYBranchState = trim($inputArray['state']);
        return "
        DESKTOP CLICK-THROUGH AGREEMENT\n\n
        
        THIS CLICK-THROUGH AGREEMENT (THIS \"AGREEMENT\") IS BETWEEN " . $DIYBranchCompanyName . " AND YOU. 
        BY CLICKING ON THE \"ACCEPT\" BUTTON BELOW, YOU ACKNOWLEDGE THAT YOU HAVE READ ALL OF THE TERMS AND 
        CONDITIONS SET FORTH BELOW, UNDERSTAND ALL  OF THE TERMS AND CONDITIONS OF THIS AGREEMENT, AND AGREE
        TO BE BOUND BY ALL OF THE TERMS AND CONDITIONS OF THIS AGREEMENT. IF YOU DO NOT AGREE TO ANY OF THE 
        TERMS AND CONDITIONS OF THIS AGREEMENT, " . $DIYBranchCompanyName . " IS UNWILLING TO LICENSE THE 
        SOFTWARE (AS DEFINED BELOW) TO YOU, AND YOU MUST CLICK ON THE \"DO NOT ACCEPT\" BUTTON BELOW. THE 
        \"EFFECTIVE DATE\" OF THIS AGREEMENT IS THE DATE UPON WHICH YOU CLICK THE \"ACCEPT\" BUTTON BELOW. 
        FOR THE PURPOSE OF THIS AGREEMENT, YOU SHALL HEREAFTER BE REFERRED TO AS \"CUSTOMER\".\n\n
        
        1. Background; Terms of Service.\n\n
        
        " . $DIYBranchCompanyName . "
        maintains and provides access to a certain software solution in object and source code form, called TheModPost 
        (the \"Software\"), which is available at " . $DIYBranchCompanyName . "'s site identified by 
        the URL " . $DIYBranchWebsite . " (\"Website\"). Customer wishes to use the Software to assist them 
        in preparing a loss mitigation mortgage modification package for presentation to Customer's lender.\n\n
        
        2. License.\n\n
        
        " . $DIYBranchCompanyName . " grants to Customer a nonexclusive, nontransferable, non-sublicensable, 
        revocable and limited license to access and use the Software solely for Customer's personal purposes to aid in
        Customer's attempt to modify a mortgage on Customer's personal real property. The Software may not be 
        downloaded from the Website or used for any other purpose.\n\n
        
        3. License Restrictions.\n\n
        
        Unless expressly
        otherwise set forth in this Agreement, Customer will not: (a) sell, assign, sublicense, rent, lease, 
        loan, provide, distribute or otherwise transfer all or any portion of the Software; (b) remove or alter
        any trademark, logo, copyright or other proprietary notices associated with the Software; or (c) cause
        or permit any other party to do any of the foregoing.\n\n
        
        4. Ownership.\n\n
        
        As between the parties, 
        " . $DIYBranchCompanyName . ' owns all right, title and interest in and to the Software. There are no
        implied licenses in this Agreement, and ' . $DIYBranchCompanyName . " reserves all rights not 
        expressly granted under this Agreement.\n\n
        
        5. Warranty; Disclaimer.\n\n
        
        Customer represents warrants and 
        covenants that: (a) Customer will abide by the terms of this Agreement; and (b) will comply with all 
        applicable laws, regulations, rules, orders and other requirements, now or hereafter in effect, of any 
        applicable governmental authority, in his/her performance of this Agreement. Notwithstanding any terms 
        to the contrary in this Agreement, Customer will remain solely and personally responsible for his/her 
        acts or omissions. THE SOFTWARE IS PROVIDED ON AN \"AS IS\" OR \"AS AVAILABLE\" BASIS WITHOUT ANY
        REPRESENTATIONS, WARRANTIES, COVENANTS OR CONDITIONS OF ANY KIND. " . $DIYBranchCompanyName . ' 
        AND ITS SUPPLIERS DO NOT WARRANT THAT ANY PART OF THE SOFTWARE WILL BE FREE FROM ALL BUGS, ERRORS,
        OR OMISSIONS. ' . $DIYBranchCompanyName . ' AND ITS SUPPLIERS DISCLAIM ANY AND ALL OTHER WARRANTIES 
        AND REPRESENTATIONS (EXPRESS OR IMPLIED, ORAL OR WRITTEN) WITH RESPECT TO THE SOFTWARE WHETHER 
        ALLEGED TO ARISE BY OPERATION OF LAW, BY REASON OF CUSTOM OR USAGE IN THE TRADE, BY COURSE OF
        DEALING OR OTHERWISE, INCLUDING ANY AND ALL (I) WARRANTIES OF MERCHANTABILITY, (II) WARRANTIES OF
        FITNESS OR SUITABILITY FOR ANY PURPOSE (WHETHER OR NOT ' . $DIYBranchCompanyName . " KNOWS, HAS REASON TO KNOW,
        HAS BEEN ADVISED, OR IS OTHERWISE AWARE OF ANY SUCH PURPOSE), AND (III) WARRANTIES OF NONINFRINGEMENT OR
        CONDITION OF TITLE. CUSTOMER ACKNOWLEDGES AND AGREES THAT CUSTOMER HAS RELIED ON NO WARRANTIES.\n\n
        
        6. Limitation of Liability.\n\n
        
        EXCEPT FOR ANY ACTS OF FRAUD, GROSS NEGLIGENCE, OR WILLFUL MISCONDUCT, IN
        NO EVENT WILL " . $DIYBranchCompanyName . ' BE LIABLE TO CUSTOMER OR ANY THIRD PARTY FOR ANY INDIRECT,
        SPECIAL, INCIDENTAL, EXEMPLARY, PUNITIVE OR CONSEQUENTIAL DAMAGES OF ANY KIND ARISING OUT OF OR IN
        CONNECTION WITH THIS AGREEMENT OR THE SOFTWARE, REGARDLESS OF THE FORM OF ACTION, WHETHER IN CONTRACT,
        TORT, STRICT LIABILITY OR OTHERWISE, EVEN IF ' . $DIYBranchCompanyName . ' HAS BEEN ADVISED OR IS
        OTHERWISE AWARE OF THE POSSIBILITY OF SUCH DAMAGES. EXCEPT FOR ANY ACTS OF FRAUD, GROSS NEGLIGENCE,
        OR WILLFUL MISCONDUCT, IN NO EVENT WILL ' . $DIYBranchCompanyName . "'S TOTAL LIABILITY ARISING 
        OUT OF OR RELATED TO THIS AGREEMENT EXCEED THE AMOUNT PAID BY CUSTOMER TO " . $DIYBranchCompanyName . ", IF 
        ANY, UNDER THIS AGREEMENT. MULTIPLE CLAIMS WILL NOT EXPAND THIS LIMITATION.\n\n
        
        7. Third Party Suppliers.\n\n
        
        The Software may include software or other code distributed under license from third
        party suppliers. Customer acknowledges that such third party suppliers disclaim and make no 
        representation or warranty with respect to the Software or any portion thereof and assume no 
        liability for any claim that may arise with respect to the Software or Customer's use or inability to use 
        the same.\n\n
        
        8. Export Controls.\n\n
        
        The Software and the underlying information and technology may not 
        be downloaded or otherwise exported or re-exported (i) into (or to a national or resident of) Cuba, Iraq, 
        Libya, North Korea, Iran, Syria or any other country to which the U.S. 
        has embargoed goods; or (ii) to anyone on the U.S. Treasury Departments list of 
        Specially Designated Nationals or the U.S. Commerce Departments Table of Deny Orders. 
        By using the Software, you are agreeing to the foregoing and you represent and warrant that you are not 
        located in, under the control of, or a national or resident of any such country or on any such list.\n\n
        
        9. Termination.\n\n
        
        The term of this Agreement will commence on the Effective Date and will remain in effect 
        indefinetely, unless otherwise terminated in accordance with the terms of this Agreement. Without prejudice to 
        any other rights, in the event of a breach of the Agreement by Customer, " . $DIYBranchCompanyName . " may immediately 
        terminate this Agreement. Upon expiration or termination of this Agreement, all rights granted to Customer under 
        this Agreement will immediately cease. In addition to all definitions and this sentence, the following sections 
        will survive any termination or expiration of this Agreement: 3, 4, 5, 6, 7, 8, 9, 10, and 11.\n\n
        
        10. Use Prohibited Where Prohibited by Law.\n\n
        
        USE OF THIS SOFTWARE IS STRICTLY PROHIBITED IN ANY JURISDICTION IN
        WHICH THE SALE OR USE OF THE SAME IS RESTIRCTED OR PROHIBITED BY LAW AND CUSTOMER ACKNOWLEDGES THAT CUSTOMER 
        SHALL CONFIRM THAT SUCH USE IS NOT PROHIBITED IN CUSTOMER'S STATE, PRIOR TO CUSTOMER'S USE OF THIS SOFTWARE.\n\n
        
        11. Miscellaneous.\n\n
        
        This Agreement together with any exhibits attached hereto, are the entire agreement of the parties 
        regarding the subject matter hereof, superseding all other agreements between them, whether oral or written, 
        regarding the subject matter hereof. This Agreement will be governed by and construed in accordance with the laws 
        of the State of " . $DIYBranchState . ' without resort to its conflict of law provisions. Any actions concerning this 
        Agreement or any aspect thereof must be brought in the Superior Court of the State of ' . $DIYBranchState . ' or in 
        the Federal District Court for the District of ' . $DIYBranchState . ". Customer may not transfer Customer's rights 
        under this Agreement to any third party. If any provision of this Agreement is invalid, illegal, or incapable of
        being enforced by any rule of law or public policy, all other provisions of this Agreement will nonetheless 
        remain in full force and effect.
        ";
    }

    /**
     * @param $ccType
     * @return string
     */

    public static function listCreditCardType($ccType): string
    {
        $newCreditCardTypeArray = newCreditCardTypeArray::$newCreditCardTypeArray;
        $newCreditCardTypeCodeArray = newCreditCardTypeCodeArray::$newCreditCardTypeCodeArray;

        $ccTypes = '';
        for ($i = 0; $i < count($newCreditCardTypeArray); $i++) {
            $cardType = $newCreditCardTypeArray[$i];
            $cardTypeCode = $newCreditCardTypeCodeArray[$i];
            $ccTypes .= "<option value=\"" . $cardTypeCode . "\"";
            if ($ccType == $cardTypeCode) {
                $ccTypes .= ' selected';
            }
            $ccTypes .= '>' . $cardType . '</option>';
        }
        return $ccTypes;
    }

    /**
     * @param $sm
     * @return string
     */

    public static function listMonths($sm): string
    {
        $months = [1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'May', 6 => 'Jun', 7 => 'Jul', 8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec'];
        $calendarMonth = '';
        for ($i = 1; $i <= count($months); $i++) {
            $calendarMonth .= "<option value=\"" . $i . "\"";
            if ($sm == $i) {
                $calendarMonth .= ' selected';
            }
            $calendarMonth .= '>' . $months[$i] . '</option>';
        }
        return $calendarMonth;
    }

    /**
     * @param $sy
     * @return string
     */
    public static function listYears($sy): string
    {
        $calendarYear = '';
        $cntDate = date('Y') + 10;
        for ($i = date('Y'); $i <= $cntDate; $i++) {
            $calendarYear .= "<option value=\"" . $i . "\"";
            if ($sy == $i) {
                $calendarYear .= ' selected';
            }
            $calendarYear .= '>' . $i . '</option>';
        }
        return $calendarYear;
    }

    /**
     * @param $fldName
     * @param $rsArray
     * @param null $inputArray
     * @return string
     */
    public static function showField($fldName, $rsArray, $inputArray = null): string
    {
        global $myFileInfo;

        if (!$inputArray) {
            $inputArray = $myFileInfo;
        }

        $inputArray = $inputArray ?? [];
        if (count($inputArray) > 0) {
            if (isset($inputArray[$rsArray][$fldName])) {
                return trim($inputArray[$rsArray][$fldName]);
            }
        }
        return '';
    }

    /**
     * @param array|null $js_file_array
     */

    public static function includeMyScript(?array $js_file_array)
    {
        if (!$js_file_array) {
            return;
        }

        foreach ($js_file_array as $js_file) {
            if (stristr($js_file, 'http') === false) {
                if (!file_exists(__DIR__ . '/../../public' . $js_file)) {
                    Debug(['error' => '$js_file does not exist', 'js_file' => $js_file]);
                }
            }
            echo '<script type="text/javascript" src="' . $js_file . '?' . CONST_JS_VERSION . '"></script>' . PHP_EOL;
        }
    }

    /**
     * @param array|null $css_file_array
     */
    public static function includeMyCSS(?array $css_file_array)
    {
        if (!$css_file_array) {
            return;
        }
        foreach ($css_file_array as $css_file) {
            if (stristr($css_file, 'http') === false) {
                if (!file_exists(__DIR__ . '/../../public' . $css_file)) {
                    Debug(['error' => '$css_file does not exist', 'css_file' => __DIR__ . '/../../public' . $css_file]);
                }
            }
            echo '<link rel="stylesheet" type="text/css" href="' . $css_file . '?' . CONST_CSS_VERSION . '">' . "\n";
        }
    }

    public static function tooltip(string $tooltip): string
    {
        return '<span class="cursor-pointer btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass"  title="' . htmlentities($tooltip) . '"> <i class="fa fa-info-circle text-primary tooltipClass" ></i></span>';
    }

    /**
     * @param $inputArray
     * @return string
     */

    public static function getLMPSummaryDefaultContent($inputArray): string
    {

        $lien2ProposalPrincipalReductionAmt = trim($inputArray['lien2ProposalPrincipalReductionAmt']);
        $signature = trim($inputArray['signature']);
        $lien2ProposalPrincipalReductionAmt = self::replaceCommaValues($lien2ProposalPrincipalReductionAmt);
        $lien1ProposalPrincipalReductionAmt = self::replaceCommaValues($lien2ProposalPrincipalReductionAmt);
        if (($lien1ProposalPrincipalReductionAmt > 0) && ($lien2ProposalPrincipalReductionAmt > 0)) {
            $prinicpalReductionContent = 'Requested Principal Reduction to 100% LTV. (1st Lien Principal Reduction Request: ' . proposalFormula::convertToAbsoluteValueForPDF($lien1ProposalPrincipalReductionAmt) . ', 2nd Lien Principal Reduction Request: ' . proposalFormula::convertToAbsoluteValueForPDF($lien2ProposalPrincipalReductionAmt) . ')';
        } elseif ($lien1ProposalPrincipalReductionAmt > 0) {
            $prinicpalReductionContent = 'Requested Principal Reduction to 100% LTV. 1st Lien Principal Reduction Request: ' . proposalFormula::convertToAbsoluteValueForPDF($lien1ProposalPrincipalReductionAmt) . ')';
        } else {
            $prinicpalReductionContent = 'Requested Principal Reduction to 100% LTV.';
        }

        $glLMProposalSummary = '<ol><li>Per HUD Guidelines all Late Fees and associated Administrative costs should be waived.</li>
<li>All current and delinquent principal, interest, and escrow items will be capitalized into the modified principal balance.</li>
<li>The first payment shall be due on the following full month from the date of the modification approval.</li>
<li>' . $prinicpalReductionContent . '</li></ol>';
        if (trim($signature) != '') {
            $glLMProposalSummary .= '<br><br>Sincerely, <br><br><b>' . $signature . '</b>';
        }
        return $glLMProposalSummary;
    }

    /**
     * @param $inputArray
     * @return string
     */

    public static function getSSPSummaryDefaultContent($inputArray): string
    {
        $offer1 = trim($inputArray['offer1']);
        $propAddressInfo = trim($inputArray['propAddressInfo']);
        $SS_InvestorBenefit = trim($inputArray['SS_InvestorBenefit']);
        $listingDate = trim($inputArray['listingDate']);
        $attorneySignature = trim($inputArray['attorneySignature']);
        return 'We have a buyer offering <b>$' . $offer1 . "</b>,
 closing in <b>45 days</b>, and asking for no inspection period. 
  Based on the comparables and the amount of damage to the property,
   you can see that the buyer's offer is more than fair. 
   Upon our inspection of <b>" . $propAddressInfo . '</b>, 
   we found that the property needs major work to bring it up to 
   current market standards.  Our buyer has based the offer on
    the market values as well as the necessary repairs. In addition, 
    after inputting the variables on our internal model, which corresponds 
    to FDIC Guidelines, it would be beneficial for the investor to accept
     the proposed short sale offer due to the <b>$' . $SS_InvestorBenefit . '</b>
      in savings over a foreclosure. The borrower has experienced a 
      legitimate hardship which is permanent. Given the income and
       expenses, there is no question the borrower <b>will continue to 
       default</b>. The property <b>was listed with a realtor since ' . $listingDate . "</b>;
        however there have been no offers to cover the amount the borrower owes.
         Please review the attached documents and respond at your earliest convenience.  
         I urge you to accept our buyer's proposal and move for a quick closing on 
         this property. Thank you for your cooperation and we look forward to 
         working with you. <br><br>Sincerely, <br><br><b>" . $attorneySignature . '</b>';
    }

    /**
     * @param $inputArray
     * @return string
     */

    public static function getHRSummaryDefaultContent($inputArray): string
    {
        $signature = trim($inputArray['signature']);
        return '<p>Attention Loss Mitigation Department</p><p>Based upon the information presented, the requested Loan 
        Modification and reduced monthly payment would allow the homeowners to meet the monthly payment obligation and remain in
        their home. The borrower(s) financial circumstances, mortgage history, and hardship have been reviewed against the HAMP
        program. Not only does it show that they are qualified, but you will also see in this report that a reduction in the
        mortgage payment significantly and positively impacts their ability to make mortgage payments consistently and on 
        time.</p>
        <p>Included in this report you will find the required forms- RMA, 710 Form, 4506, and Dodd Frank form. Please
        indicate if anything else is required.</p>
        <p>I am expecting a prompt reply and resolution based on the latest, mandated compliance requirements centered 
        around communication time lines,  issuing a representative as a point of contact, acknowledging receipt of 
        borrower requests for workouts, and providing review analysis results promptly.</p>
        <p>Thank-you in advance for your time in reviewing this delicate matter. We look forward to a prompt and 
        mutually beneficial resolution.</p><br><br>Sincerely, <br><br><b>' . $signature . '</b>';
    }

    /**
     * @param $inputStr
     * @return array|string|string[]
     */
    public static function processHardshipLetter($inputStr)
    {
        return self::replaceHardshipTemplate(strip_tags($inputStr));
    }

    /**
     * @param $hardshipDesc
     * @return array|string|string[]
     */

    public static function replaceHardshipTemplate($hardshipDesc)
    {
        $hardshipDesc = str_replace('?', "'", $hardshipDesc);
        $hardshipDesc = str_replace('', "'", $hardshipDesc);
        $hardshipDesc = str_replace('â', "\"", $hardshipDesc);
        $hardshipDesc = str_replace('â¢', '', $hardshipDesc);
        $hardshipDesc = str_replace('â', "'", $hardshipDesc);
        $hardshipDesc = str_replace('Ã¢â¬Â¢', '', $hardshipDesc);
        $hardshipDesc = str_replace('Ã¢â¬â¢', "'", $hardshipDesc);
        return str_replace("\'", "'", $hardshipDesc);
    }

    /**
     * @param $inArray
     * @return string
     */

    public static function generatePwdToOpenPdf($inArray): string
    {
        $SSN = trim($inArray['SSN']);
        $clientPwd = trim(substr($SSN, -4));
        if (trim($clientPwd) == '') {
            $clientPwd = '0000';
        }
        return $clientPwd;
    }

    /**
     * @param $urlString
     * @param $title
     */

    public static function openHTMLForESign($urlString, $title)
    {
        global $keywords;

        $url = CONST_SITE_URL;

        $str = <<<EOT
            <!doctype html>
            <!--[if lt IE 8 ]><html lang="en" class="no-js ie ie7 dark"><![endif]-->
            <!--[if IE 8 ]><html lang="en" class="no-js ie dark"><![endif]-->
            <!--[if (gt IE 8)|!(IE)]><!--><html lang="en" class="no-js dark"><!--<![endif]-->
            <head>
            <meta charset="UTF-8" />
            <title>$title</title>
            <meta name="Description" content="$keywords"/>
            <meta name="keywords" content="$keywords"/>
            <meta name="dcterms.audience" content="Global"/>
            <meta name="robots" content="noindex"/>
            <meta name="dc.language" content="en"/>
            <meta name="author" content="The Loan post"/>
            <meta http-equiv="Cache-Control" Content="no-cache">
            <meta http-equiv="Pragma" Content="no-cache">
            <meta http-equiv="Expires" Content="0">
            <meta http-equiv="Pragma-directive: no-cache">
            <meta http-equiv="Cache-directive: no-cache">

EOT;
        $str .= BaseHTMLOGCust::OGLinks();
        $str .= "<script src=\"" . $urlString . 'assets/js/XmlUtil.js?' . CONST_JS_VERSION . "\"  type=\"text/javascript\"></script>";
        $str .= "<link rel=\"stylesheet\" href=\"" . $url . 'assets/js/3rdParty/Contact-Pop/css/contact-pop.css?' . CONST_CSS_VERSION . "\" type=\"text/css\">\n";
        $str .= "<link rel=\"stylesheet\" href=\"" . $url . 'assets/styles/jquery-ui.css?' . CONST_CSS_VERSION . "\" type=\"text/css\">\n";
        $str .= "<link rel=\"stylesheet\" href=\"" . $url . 'assets/styles/classes.css?' . CONST_CSS_VERSION . "\" type=\"text/css\">\n";
        $str .= "<script src=\"" . $url . 'assets/js/jsconfig.php?' . CONST_JS_VERSION . "\"  type=\"text/javascript\"></script>\n";
        $str .= "<script src=\"" . $url . 'assets/js/3rdParty/jquery-legacy/jquery.min.js?' . CONST_JS_VERSION . "\"  type=\"text/javascript\"></script>\n";
        $str .= "<script src=\"" . $url . 'assets/js/3rdParty/jquery-ui-legacy/jquery-ui.min.js?' . CONST_JS_VERSION . "\"  type=\"text/javascript\"></script>\n";
        $str .= "<script src=\"" . $url . 'assets/js/3rdParty/Contact-Pop/js/contact-pop_new_esign.js?' . CONST_JS_VERSION . "\"  type=\"text/javascript\"></script>\n";
        $str .= "<script src=\"" . $url . 'assets/js/3rdParty/jquery-tools/jquery.tools.min.js?' . CONST_JS_VERSION . "\"  type=\"text/javascript\"></script>\n";
        $str .= "<script src=\"" . $url . 'assets/js/validation.js?' . CONST_JS_VERSION . "\"  type=\"text/javascript\"></script>\n";
        echo $str;
    }
    public static function openTrustDocument($urlString,$title)
    {
        global $keywords;

        $CONST_JS_VERSION = CONST_JS_VERSION;

        $str = <<<EOT
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>$title</title>
    <meta name="description" content="$keywords"/>
    <meta name="keywords" content="$keywords"/>
    <meta name="dcterms.audience" content="Global"/>
    <meta name="robots" content="noindex"/>
    <meta name="dc.language" content="en"/>
    <meta name="author" content="The Loan Post"/>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>

    <!--begin::Fonts-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700"/>
    <!--end::Fonts-->

    <!--begin::Page Custom Styles (used by this page)-->
    <link href="/assetsNew/css/pages/login/classic/login-5.css" rel="stylesheet" type="text/css"/>
    <!--end::Page Custom Styles-->

    <!--begin::Global Theme Styles (used by all pages)-->
    <link href="/assets/js/3rdParty/plugins/plugins.bundle.css" rel="stylesheet" type="text/css"/>
    <link href="/assets/js/3rdParty/prismjs/prismjs.bundle.css" rel="stylesheet" type="text/css"/>
    <link href="/assetsNew/css/style.bundle.css" rel="stylesheet" type="text/css"/>
    <!--end::Global Theme Styles-->

    <!--begin::Layout Themes (used by all pages)-->
    <link href="/assetsNew/css/themes/layout/header/base/light.css" rel="stylesheet" type="text/css"/>
    <link href="/assetsNew/css/themes/layout/header/menu/light.css" rel="stylesheet" type="text/css"/>
    <link href="/assetsNew/css/themes/layout/brand/dark.css" rel="stylesheet" type="text/css"/>
    <link href="/assetsNew/css/themes/layout/aside/dark.css" rel="stylesheet" type="text/css"/>
    <link href="/assets/styles/toastr.css" rel="stylesheet" type="text/css"/>
    <!--end::Layout Themes-->

    <!-- Scripts -->
    <script src="/login/js/backoffice/KTAppSettings.js?{$CONST_JS_VERSION}"></script>
    <script src="/assets/js/3rdParty/plugins/plugins.bundle.js?{$CONST_JS_VERSION}"></script>
    <script src="/assets/js/3rdParty/prismjs/prismjs.bundle.js?{$CONST_JS_VERSION}"></script>
    <script src="/assets/js/3rdParty/KTApp.js?{$CONST_JS_VERSION}"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js?{$CONST_JS_VERSION}"></script>
    <script src="/assetsNew/js/http.js" type="application/javascript"></script>
EOT;
        $str .= BaseHTMLOGCust::OGLinks().'</head>';
        echo $str;
    }

    /**
     * @param $typeAE
     * @param $selValue
     * @return string
     */

    public static function isNicheTypeChecked($typeAE, $selValue): string
    {
        $opStr = '';
        if (count($typeAE) > 0) {
            for ($i = 0; $i < count($typeAE); $i++) {
                if ($typeAE[$i] == $selValue) {
                    $opStr = ' checked ';
                }
            }
        }
        return $opStr;
    }

    /**
     * @param $inArray
     */

    public static function alertInvalidFileAccess($inArray)
    {
        $attorneyEmail = '';
        $mailInpArray = [];
        $clientName = '';
        $fileID = 0;
        $attachmentArray = [];
        $PCName = '';
        $accessIP = $_SERVER['REMOTE_ADDR'];
        $page = $_SERVER['PHP_SELF'];
        $time = date('m/d/Y H:i:s');
        if (array_key_exists('clientName', $inArray)) $clientName = $inArray['clientName'];
        if (array_key_exists('LMRId', $inArray)) $fileID = $inArray['LMRId'];
        if (array_key_exists('attorneyEmail', $inArray)) $attorneyEmail = $inArray['attorneyEmail'];
        if (array_key_exists('PCName', $inArray)) $PCName = $inArray['PCName'];
        $userNumber = self::GetSess('userNumber');
        $userGroup = self::GetSess('userGroup');
        $userName = self::GetSess('lastName');
        if ($fileID > 0) {
            $subj = 'INVALID FILE ACCESS - ' . date('Y-m-d');
        } else {
            $subj = 'INVALID PAGE ACCESS - ' . date('Y-m-d');
        }
        $alertMsg = 'By ' . ucwords($userName) . '<br> At = ' . $time;
        if ($fileID > 0) {
            $alertMsg .= '<br> Homeowner = ' . ucwords($clientName);
        }
        $alertMsg .= '<br> IP = ' . $accessIP . '<br> Page = ' . $page;
        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-type: text/html; charset=iso-8859-1\r\n";
        $inpArray = [
            'fromName'           => CONST_EMAIL_FROM_NAME,
            'fromEmailActual'    => CONST_EMAIL_MARKETING,
            'toName'             => $PCName,
            'subject'            => $subj,
            'mailType'           => 'invalidAccess',
            'sendAsUser'         => 1,
            'sendServerUserId'   => 0,
            'sendServerUserType' => '',
            'LMRId'              => $fileID,
            'fromEmail'          => CONST_EMAIL_FROM,
            'toEmail'            => $attorneyEmail,
            'msg'                => $alertMsg,
            'attachmentArray'    => $attachmentArray,
        ];
        $mailInpArray[] = $inpArray;

        saveMailsToQueue::getReport(['info' => $mailInpArray]);
        $qry = " 
INSERT INTO 
tblInvalidAccess (
                  IPNo
                  , accessUrl
                  , recDate
                  , userID
                  , userGroup
                  , remarks
                  , fileID
                  )
                 VALUES (
                  :IPNo
                  , :accessUrl
                  , :recDate
                  , :userID
                  , :userGroup
                  , :remarks
                  , :fileID
                 )
               ";
        Database2::getInstance()->executeQuery($qry, [
            'IPNo'      => $accessIP,
            'accessUrl' => $page,
            'recDate'   => Dates::Timestamp(),
            'userID'    => $userNumber,
            'userGroup' => $userGroup,
            'remarks'   => $subj . $alertMsg,
            'fileID'    => $fileID,
        ]);
    }

    /**
     * @param array $ip
     * @return string
     */

    public static function alertInvalidAccess(array $ip = []): string
    {
        $nAttempts = 0;
        $msg = '';
        $userEmail = '';
        $accessIP = $_SERVER['REMOTE_ADDR'];
        $page = $_SERVER['PHP_SELF'];
        if (array_key_exists('userID', $ip)) $userEmail = trim($ip['userID']);
        if (isset($_SESSION['nAttempts'])) {
            $nAttempts = self::GetSess('nAttempts');
        } else {
            self::SetSess('nAttempts', $nAttempts);
        }
        $crntPg = $page;
        $endPos = strripos($crntPg, '/');
        if ($endPos > -1) {
            $crntPg = substr($crntPg, $endPos + 1);
        }
        $userNumber = self::GetSess('userNumber');
        $userGroup = self::GetSess('userGroup');
        if ($nAttempts >= 2) $msg = 'To retrieve your password, click the lost password option. In case your attempts are genuine, please contact your administrator.';
        self::SetSess('nAttempts', ($nAttempts + 1));
        $qry = " 
INSERT INTO 
    tblInvalidAccess 
    (
      IPNo
      , accessUrl
      , recDate
      , userEmail
      , userID
      , userGroup
      , fileID
  )
  VALUES
      (
          :IPNo
          , :accessUrl
          , :recDate
          , :userEmail
          , :userID
          , :userGroup
        , 0
        )
   ";
        Database2::getInstance()->executeQuery($qry, [
            'IPNo'      => $accessIP,
            'accessUrl' => $page,
            'recDate'   => Dates::Timestamp(),
            'userEmail' => $userEmail,
            'userID'    => $userNumber,
            'userGroup' => $userGroup,
        ]);
        return $msg;
    }

    /**
     * @param $inArray
     */

    public static function addDefaultPrimaryStatusForPC($inArray)
    {
        $PCID = 0;
        $qry1 = '';
        $qry2 = '';
        $displayOrder = 0;
        $isHMLO = 0;
        $PCSelectedModuleCode = [];

        $glAllowOthersToUpdatePLStatusArray = glAllowOthersToUpdatePLStatusArray::$glAllowOthersToUpdatePLStatusArray;
        $glPrimaryLoanModStatusArray = glPrimaryLoanModStatusArray::$glPrimaryLoanModStatusArray;
        $glPCFilePrimaryStatusModuleArray = glPCFilePrimaryStatusModuleArray::$glPCFilePrimaryStatusModuleArray;
        $glDisplayOrderPLStatusArray = glDisplayOrderPLStatusArray::$glDisplayOrderPLStatusArray;
        $glHMLOPLStatusArray = glHMLOPLStatusArray::$glHMLOPLStatusArray;

        if (array_key_exists('PCID', $inArray)) $PCID = trim($inArray['PCID']);
        /**
         * Description : Allowed only HMLO Modules Default Sub-status
         * Developer   : Viji, Venkatesh
         * Date        : Feb 13, 2017
         **/
        if (array_key_exists('PCSelectedModuleCode', $inArray)) $PCSelectedModuleCode = $inArray['PCSelectedModuleCode'];
        if (count($PCSelectedModuleCode) > 0) {
            if (in_array('HMLO', $PCSelectedModuleCode)) {
                $isHMLO = 1;
            }
        }

        $tempPrimaryArray = array_keys($glPCFilePrimaryStatusModuleArray);
        for ($s = 0; $s < count($glPrimaryLoanModStatusArray); $s++) {
            if ($s > 0) {
                $qry1 .= ', ';
            }
            if ($isHMLO == 1) {
                if (array_key_exists($s, $glDisplayOrderPLStatusArray)) {
                    $displayOrder = trim($glDisplayOrderPLStatusArray[$s]);
                }
            }

            /**
             *
             * Description : Allow to update each login edit the file status in HMLO modules
             * Date        : April 07, 2017
             * Author      : Viji & Venkatesh
             **/
            if ($isHMLO == 1) {
                $qry1 .= " ('" . $PCID . "', '" . addslashes($glPrimaryLoanModStatusArray[$s]) . "', 
                '" . $glHMLOPLStatusArray['BR'][$s] . "', '" . $glHMLOPLStatusArray['AG'][$s] . "', 
                '" . $glHMLOPLStatusArray['CL'][$s] . "', '" . $glHMLOPLStatusArray['BO'][$s] . "', 
                '" . $displayOrder . "') ";
            } else {
                $qry1 .= " ('" . $PCID . "', '" . addslashes($glPrimaryLoanModStatusArray[$s]) . "', 
                '" . $glAllowOthersToUpdatePLStatusArray[$s] . "', 1, '" . $glAllowOthersToUpdatePLStatusArray[$s] . "',
                 '" . $glAllowOthersToUpdatePLStatusArray[$s] . "', '" . $displayOrder . "') ";
            }
        }
        $m = 0;
        for ($j = 0; $j < count($tempPrimaryArray); $j++) {
            $primaryStatusVal = trim($tempPrimaryArray[$j]);
            $tempModulesArr = $glPCFilePrimaryStatusModuleArray[$primaryStatusVal];
            for ($b = 0; $b < count($tempModulesArr); $b++) {
                $moduleCode = trim($tempModulesArr[$b]);
                if ($m > 0) {
                    $qry2 .= ', ';
                }
                $qry2 .= " ('" . $primaryStatusVal . "', '" . $moduleCode . "') ";
                $m++;
            }
        }
        if ($PCID > 0) {
            $qry = 'CALL SP_DefaultPCFileStatus(' . $PCID . ", \"" . $qry1 . "\", \"" . $qry2 . "\");";
            Database2::getInstance()->fetchRecords(['qry' => $qry]);
        }
    }

    /**
     * @param $urlQString
     * @return string
     */

    public static function processGivenURLString($urlQString): string
    {
        $matchesArray = [];
        $urlQString = trim($urlQString);
        preg_match('/https:\/\//i', $urlQString, $matchesArray);
        if (count($matchesArray) > 0) {
            $processedUrl = $urlQString;
        } else {
            $processedUrl = preg_replace_callback(
                '#http://#i',
                function () {
                    return '';
                },
                $urlQString
            );
            $processedUrl = 'http://' . $processedUrl;
        }
        return $processedUrl;
    }

    /**
     * @param $numval
     * @param int $cents
     * @return string
     */

    public static function makewords($numval, int $cents = 1): string
    {
        $moneystr = '';
        // handle the millions
        $milval = (int)($numval / 1000000);
        if ($milval > 0) {
            $moneystr = self::getWords($milval) . ' Million';
        }
        // handle the thousands
        $workval = $numval
            - ($milval * 1000000); // get rid of millions
        $thouval = (int)($workval / 1000);
        if ($thouval > 0) {
            $workword = self::getWords($thouval);
            if ($moneystr == '') {
                $moneystr = $workword . ' Thousand';
            } else {
                $moneystr .= ' ' . $workword . ' Thousand';
            }
        }
        // handle all the rest of the dollars
        $workval = $workval - ($thouval * 1000); // get rid of thousands
        $tensval = (int)($workval);
        if ($moneystr == '') {
            if ($tensval > 0) {
                $moneystr = self::getWords($tensval);
            } else {
                $moneystr = 'Zero';
            }
        } else // non zero values in hundreds and up
        {
            $workword = self::getWords($tensval);
            if ($workword > '') {
                $moneystr .= ' ' . $workword;
            }
        }
        // plural or singular 'dollar'
        $workval = (int)($numval);
        if ($cents == 1) {
            if ($workval == 1) {
                $moneystr .= ' Dollar And ';
            } else {
                $moneystr .= ' Dollars And ';
            }
        } else {
            if ($workval == 1) {
                $moneystr .= ' Dollar';
            } else {
                $moneystr .= ' Dollars';
            }
        }
        // do the cents - use printf so that we get the
        // same rounding as printf
        $workstr = sprintf('%3.2f', $numval); // convert to a string
        $intstr = substr($workstr, strlen($workstr) - 2, 2);
        $workint = (int)($intstr);
        if ($cents == 1) {
            if ($workint == 0) {
                $moneystr .= 'Zero';
            } else {
                $moneystr .= self::getWords($workint);
            }
            if ($workint == 1) {
                $moneystr .=
                    ' Cent';
            } else {
                $moneystr .= ' Cents';
            }
        }
        // done
        return $moneystr;
    }

    /**
     * @param $input
     * @param $callback
     * @param bool $recurse
     * @return array|false|mixed|SimpleXMLElement
     */
    public static function xmlToArray($input, $callback = null, bool $recurse = false)
    {
        $data = ((!$recurse) && is_string($input)) ? simplexml_load_string($input, 'SimpleXMLElement', LIBXML_NOCDATA) : $input;
        if ($data instanceof SimpleXMLElement) $data = (array)$data;
        if (is_array($data)) foreach ($data as &$item) $item = self::xmlToArray($item, $callback, true);
        return (!is_array($data) && is_callable($callback)) ? call_user_func($callback, $data) : $data;
    }

    /**
     * @param $inParam
     * @return string
     */
    /*public static function booleanTextVal($inParam): string
    {
        if ($inParam == 1) return 'Yes';
        if ($inParam == 0) return 'No';
        if ($inParam == '') return 'NA';
        return '';
    }*/

    /**
     * @param $numString
     * @return array|string|string[]|null
     */
    public static function numberOnly($numString)
    {
        return preg_replace('/[^0-9]/', '', $numString);
    }

    /**
     * @param $key
     * @return array
     */
    public static function splitRAMVendorKey($key): array
    {
        $keyArray = [];
        //       $key = preg_replace("[_-()*:a-zA-Z ]","", $key);
        $key = preg_replace_callback(
            '/[-_()*:a-z A-Z]/',
            function () {
                return '';
            },
            $key
        );
        if ($key != '' && $key != NULL && $key != 'NULL') {
            $keyArray = [
                'No1' => substr($key, 0, 8),
                'No2' => substr($key, 8, 4),
                'No3' => substr($key, 12, 4),
                'No4' => substr($key, 16, 4),
                'No5' => substr($key, 20, 12),
            ];
        }
        return $keyArray;
    }


    /**
     * @param array $mainArray
     * @param array $commaToBeRemoved
     * @return array
     */
    public static function removeCommasInArrayValues(array $mainArray, array $commaToBeRemoved): array
    {
        foreach ($commaToBeRemoved as $val) {
            if (!empty($mainArray[$val])) {
                $mainArray[$val] = self::replaceCommaValues($mainArray[$val]);
            }
        }
        return $mainArray;
    }

    /**
     * @param $item
     * @return void
     */
    public static function prepareDbValues(&$item)
    {
        $item = "'" . addslashes($item) . "'";
    }

    /**
     * @param $string
     * @return string
     */
    public static function cleanString($string): string
    {
        return strtolower(preg_replace('/[^A-Za-z0-9\-]/', '', $string)); // Removes special chars.
    }

    /**
     * @param $number
     * @return string
     */
    public static function ordinal($number): string
    {
        $ends = ['th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th'];
        if ((($number % 100) >= 11) && (($number % 100) <= 13))
            return $number . 'th';
        else
            return $number . $ends[$number % 10];
    }

    /**
     * @param $number
     * @return string
     */
    public static function ccMasking($number): string
    {
        $number = self::getNumberValue($number);
        $cnt = strlen($number);

        if ($cnt > 0) {
            return 'XXXX XXXX XXXX ' . substr($number, ($cnt - 4), $cnt);
        } else {
            return '';
        }
    }

    /**
     * @param $number
     * @return string
     */
    public static function ssnMasking($number): string
    {
        $number = self::getNumberValue($number);
        $cnt = strlen($number);

        if ($cnt > 0) {
            return '###-##-' . substr($number, ($cnt - 4), $cnt);
        } else {
            return '';
        }
    }

    /**
     * @param string $number
     * @return false|string
     */
    public static function convertNumberToWord(string $number)
    {
        $f = new NumberFormatter("en", NumberFormatter::SPELLOUT);
        return $f->format(Strings::Numeric($number));
    }

    /**
     * @param $inVal
     * @return array|string|string[]|null
     */
    public static function getNumberValue($inVal)
    {
        if ($inVal == '') {
            return '';
        }

        return preg_replace('/[^0-9]/', '', trim($inVal));
    }

    public static function PhoneNumberNoExt($value)
    {
        return substr(self::getNumberValue($value), 0, 10);
    }

    /**
     * @param $inputString
     * @return array|string|string[]
     */
    public static function processEmailString($inputString)
    {
        //$processedString = "";
        $processedString = str_replace("\r\n", '', $inputString);
        $processedString = str_replace("\n", '', $processedString);
        $processedString = str_replace("\r", '', $processedString);
        return str_replace('
', '', $processedString);
    }

    /**
     * @param $ph
     * @return array|string|string[]|null
     */
    public static function formatPhoneNumberWithoutSpace($ph)
    {
        $ph = preg_replace_callback(
            '/[-_()*:a-z A-Z]/',
            function () {
                return '';
            },
            $ph
        );
        try {
            $p1 = substr($ph, 0, 3);
            $p2 = substr($ph, 3, 3);
            $p3 = substr($ph, 6, 4);
            $ex = substr($ph, 10, 6);
            if ($ph != '') {
                $ph = '(' . $p1 . ')' . $p2 . '-' . $p3;
            }
            if ($ex != '') {
                $ph .= ' Ext ' . $ex;
            }
        } catch (Exception $e) {
        }
        return $ph;
    }

    /**
     * @param $workval
     * @return string
     */
    public static function getWords($workval): string
    {
        $numwords = [
            1  => 'One',
            2  => 'Two',
            3  => 'Three',
            4  => 'Four',
            5  => 'Five',
            6  => 'Six',
            7  => 'Seven',
            8  => 'Eight',
            9  =>
                'Nine',
            10 => 'Ten',
            11 => 'Eleven',
            12 => 'Twelve',
            13 => 'Thirteen',
            14 => 'Fourteen',
            15 => 'Fifteen',
            16 => 'Sixteen',
            17 => 'Seventeen',
            18 => 'Eighteen',
            19 => 'Nineteen',
            20 => 'Twenty',
            30 => 'Thirty',
            40 => 'Forty',
            50 => 'Fifty',
            60 => 'Sixty',
            70 => 'Seventy',
            80 => 'Eighty',
            90 => 'Ninety',
        ];
        // handle the 100's
        $retstr = '';
        $hundval = (int)($workval / 100);
        if ($hundval > 0) {
            $retstr = $numwords[$hundval] . ' Hundred';
        }
        // handle units and teens
        $workstr = '';
        $tensval = $workval - ($hundval * 100); // dump the 100's
        // do the teens
        if (($tensval < 20) && ($tensval > 0)) {
            $workstr = $numwords[$tensval];
            // got to break out the units and tens
        } else {
            $tempval = ((int)($tensval / 10)) * 10; // dump the units
            if ($tempval > 0) $workstr = $numwords[$tempval]; // get the tens
            $unitval =
                $tensval - $tempval; // get the unit value
            if ($unitval > 0) {
                $workstr .= ' ' . $numwords[$unitval];
            }
        }
        // join the parts together
        if ($workstr != '') {
            if ($retstr != '') {
                $retstr .= ' ' . $workstr;
            } else {
                $retstr = $workstr;
            }
        }
        return $retstr;
    }


    /**
     * @param string $str
     * @return string
     */
    public static function escapeQuote(?string $str = ''): string
    {
        return addslashes(trim($str));
    }

    /**
     * @param string $str
     * @return array|string|string[]
     */
    public static function escapeQuoteNew(?string $str = '')
    {
        return is_null($str) ? null : str_replace('"', '', str_replace("'", '', trim($str)));
    }

    /**
     * @param string $str
     * @return string
     */
    public static function stripQuote(?string $str = ''): string
    {
        return stripslashes(trim($str));
    }

    /**
     * @param string $str
     * @return array|string|string[]
     */
    public static function escapeQuoteForDB(string $str = '')
    {
        return str_replace("'", "\\''", stripslashes(trim($str)));
    }

    /**
     * @param $oldVal
     * @param $newVal
     * @return mixed
     */
    public static function checkAndUpdateValue($oldVal, $newVal)
    {
        if ($oldVal > 0) {
            $assVal = $oldVal;
        } else {
            $assVal = $newVal;
        }
        return $assVal;
    }

    public static function revstrpos(string $haystack, string $needle)
    {
        $haystack = strrev($haystack);
        $index = strpos($haystack, $needle);
        if ($index === false) {
            return false;
        }
        return strlen($haystack) - $index - 1;
    }

    public static function preg_revstrpos(string $haystack, string $regex)
    {
        $haystack = strrev($haystack);
        $m = strlen($haystack);
        for ($j = 0; $j < $m; $j++) {
            if (preg_match($regex, substr($haystack, 0, $j))) {
                return $m - $j;
            }
        }
        return false;
    }

    public static function fileExtensionFilter($displayDocName, $fileType): ?string
    {
        $fileInfo = pathinfo(trim($displayDocName));
        if (is_array($fileInfo)) {
            if ($fileInfo['extension'] == trim($fileType)) {
                return $displayDocName;
            }
        }
        return $displayDocName . '.' . $fileType;
    }

    public static function arrayToString(array $inputArray): ?string
    {
        return implode(', ', array_filter($inputArray)) ?? null;
    }

    public static function ensureHTTPSURL($url): ?string
    {
        if (strpos($url, 'http://') === 0) {
            $url = 'https://' . substr($url, 7);
        } elseif (strpos($url, 'https://') !== 0) {
            $url = 'https://' . $url;
        }
        return $url;
    }

    public static function recursiveXSSSanitize(?array $array): ?array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = self::recursiveXSSSanitize($value); // Recursively sanitize sub-array
            } else {
                $array[$key] = htmlspecialchars($value); // Sanitize individual value
            }
        }
        return $array;
    }

    public static function checkStringInArray($haystack, $needles, $dirSeparator = ''): bool
    {
        foreach ($needles as $needle) {
            if (str_contains($haystack, $dirSeparator . $needle)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $string
     * @param int $limit
     * @return string
     */
    public static function cleanAndTruncate($string, int $limit = 160): string
    {
        // Decode HTML entities (e.g., &amp; => &, &lt; => <)
        $decodedString = html_entity_decode($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove HTML tags
        $cleanString = strip_tags($decodedString);

        // Truncate to 160 characters
        return mb_substr($cleanString, 0, $limit, 'UTF-8');
    }

    /**
     * @param $vhost
     * @return string
     */
    public static function validateVhost($vhost): string
    {
        // Convert to lowercase and trim spaces
        $vhost = strtolower(trim($vhost));

        // Remove protocol (http:// or https://)
        $vhost = preg_replace('/^https?:\/\//', '', $vhost);

        // Remove invalid characters (allow only letters, numbers, dots, and hyphens)
        $vhost = preg_replace('/[^a-z0-9.-]/', '', $vhost);

        // Ensure it does not start or end with a hyphen (-) or dot (.)
        $vhost = trim($vhost, '-.');

        // Validate format using regex (basic domain validation)
        if (!preg_match('/^(?:[a-z0-9][a-z0-9-]{0,62}\.)+[a-z]{2,}$/', $vhost)) {
            return ''; // Invalid vhost
        }

        return $vhost; // Return cleaned vhost
    }

    public static function isRandomString($string) {
        // Allow fully uppercase or fully lowercase names (e.g., "JAMES", "alice")
        if (ctype_upper($string) || ctype_lower($string)) {
            return false; // Valid name
        }

        // Block names with numbers mixed into letters (e.g., "alieDkw3s53laDKsld")
        if (preg_match('/[0-9]/', $string)) {
            return true; // Suspicious if name contains numbers
        }

        // Detect alternating uppercase/lowercase patterns (e.g., "aBcDeF", "xYzAbC")
        if (preg_match('/(?:[A-Z][a-z]){2,}|(?:[a-z][A-Z]){2,}/', $string)) {
            return true; // Suspicious alternating pattern
        }

        // Detect long mixed-case sequences (e.g., "aexeFgjcqDN", "aqDFxZZUkrWWS")
        if (preg_match('/[A-Z]{2,}[a-z]{2,}[A-Z]{2,}/', $string) || preg_match('/[a-z]{2,}[A-Z]{2,}[a-z]{2,}/', $string)) {
            return true; // Suspicious mixed case pattern
        }

        // Detect if at least 40% of the name is a mix of uppercase/lowercase
        $upper = preg_match_all('/[A-Z]/', $string);
        $lower = preg_match_all('/[a-z]/', $string);
        $length = strlen($string);

        if ($length > 6 && min($upper, $lower) / $length > 0.4) { // More than 40% mix of cases
            return true; // Suspicious pattern
        }

        return false; // Otherwise, accept the name
    }

    /**
     * @param $fileName
     * @return string
     */
    public static function removeFileExtension($fileName): string
    {
        if (!$fileName) {
            return '';
        }
        return pathinfo($fileName, PATHINFO_FILENAME);
    }

    public static function formatNumber($val,$decimals): string
    {
        $val = rtrim(rtrim(number_format((float)$val, $decimals, '.', ''), '0'), '.');

        // If it has no decimal or only one digit after decimal, add trailing zero(s)
        if (!str_contains($val, '.')) {
            return $val . '.00';
        }

        $parts = explode('.', $val);
        if (strlen($parts[1]) === 1) {
            return $val . '0';
        }
        return $val;
    }

}
