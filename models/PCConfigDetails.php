<?php

namespace models;

use models\composite\oFileTabs\getPCFileTabsDispOrder;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\types\strongType;

class PCConfigDetails extends strongType
{
    /**
     * @param int $PCID
     * @return array
     */
    public static function getReport(int $PCID): array
    {
        $PCConfigDetails = [];
        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/enabledLPDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['enabledInternalLoanProgramsCount'] = $res[0]['enabledInternalLoanProgramsCount'] ?? 0;
        $PCConfigDetails['enabledLoanProgramsCount'] = $res[0]['enabledLoanProgramsCount'] ?? 0;


        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/PCCreatedLPDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['PCCreatedInternalLoanProgramsCount'] = $res[0]['PCCreatedInternalLoanProgramsCount'] ?? 0;
        $PCConfigDetails['PCCreatedLoanProgramsCount'] = $res[0]['PCCreatedLoanProgramsCount'] ?? 0;

        $PCServiceTypeInfoArray = getPCServiceType::getReport(['PCID' => $PCID]);
        $PCServiceTypeInfo = $PCServiceTypeInfoArray[$PCID];
        $additionalLPArray = [];
        $defaultLPArray = [];
        foreach ($PCServiceTypeInfo as $item) {
            if (!in_array($item['LMRClientType'], ['BPP', 'BRL', 'CB', 'FAF', 'CONS', 'RBH'])) {
                $additionalLPArray[] = $item['serviceType'];
            } else {
                $defaultLPArray[] = $item['LMRClientType'];
            }
        }
        $disabledDefaultLP = implode(',', (array_diff(['BPP', 'BRL', 'CB', 'FAF', 'CONS', 'RBH'], $defaultLPArray))) ?? null;
        $additionalLPAssigned = implode(",", $additionalLPArray) ?? null;
        $PCConfigDetails['disabledDefaultLP'] = $disabledDefaultLP;
        $PCConfigDetails['additionalLPAssigned'] = $additionalLPAssigned;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/formFieldsEnabledDisabledDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['FAEnabledCount'] = $res[0]['FAEnabledCount'] ?? 0;
        $PCConfigDetails['FADisabledCount'] = $res[0]['FADisabledCount'] ?? 0;
        $PCConfigDetails['QAEnabledCount'] = $res[0]['QAEnabledCount'] ?? 0;
        $PCConfigDetails['QADisabledCount'] = $res[0]['QADisabledCount'] ?? 0;
        $PCConfigDetails['BOEnabledCount'] = $res[0]['BOEnabledCount'] ?? 0;
        $PCConfigDetails['BODisabledCount'] = $res[0]['BODisabledCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/docWizardDocumentsDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['activeDocWizardDocCount'] = $res[0]['activeDocWizardDocCount'] ?? 0;
        $PCConfigDetails['inactiveDocWizardDocCount'] = $res[0]['inactiveDocWizardDocCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/PCCreatedFileStatusDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['activePCCreatedFileStatusCount'] = $res[0]['activePCCreatedFileStatusCount'] ?? 0;
        $PCConfigDetails['inactivePCCreatedFileStatusCount'] = $res[0]['inactivePCCreatedFileStatusCount'] ?? 0;
        $PCConfigDetails['inactiveFileStatusCount'] = $res[0]['inactiveFileStatusCount'] ?? 0;
        $PCConfigDetails['totalInactiveFileStatusCount'] = $res[0]['totalInactiveFileStatusCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/PCCreatedFileSubStatusDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['activePCCreatedFileSubStatusCount'] = $res[0]['activePCCreatedFileSubStatusCount'] ?? 0;
        $PCConfigDetails['inactivePCCreatedFileSubStatusCount'] = $res[0]['inactivePCCreatedFileSubStatusCount'] ?? 0;
        $PCConfigDetails['inactiveFileSubStatusCount'] = $res[0]['inactiveFileSubStatusCount'] ?? 0;
        $PCConfigDetails['totaInactiveFileSubStatusCount'] = $res[0]['totaInactiveFileSubStatusCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/PCCreatedRequiredDocsDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['PCCreatedRequiredDocsCount'] = $res[0]['PCCreatedRequiredDocsCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/automationsDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['tasksAutomationCount'] = $res[0]['tasksAutomationCount'] ?? 0;
        $PCConfigDetails['webhookAutomationCount'] = $res[0]['webhookAutomationCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/automationTriggerDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['automationPrimaryStatusTriggerCount'] = $res[0]['automationPrimaryStatusTriggerCount'] ?? 0;
        $PCConfigDetails['automationWorkflowTriggerCount'] = $res[0]['automationWorkflowTriggerCount'] ?? 0;
        $PCConfigDetails['automationNewLoanTriggerCount'] = $res[0]['automationNewLoanTriggerCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/monthlyLoanFileCreationDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['monthlyLoanFileCreationCount'] = $res[0]['monthlyLoanFileCreationCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/PCCreatedWorkflowStepsDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['activePCCreatedWorkflowStepsCount'] = $res[0]['activePCCreatedWorkflowStepsCount'] ?? 0;
        $PCConfigDetails['inactivePCCreatedWorkflowStepsCount'] = $res[0]['inactivePCCreatedWorkflowStepsCount'] ?? 0;
        $PCConfigDetails['inactiveWorkflowStepsCount'] = $res[0]['inactiveWorkflowStepsCount'] ?? 0;
        $PCConfigDetails['totalInactiveWorkflowStepsCount'] = $res[0]['totalInactiveWorkflowStepsCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/stickyNotesCretionDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['stickyNotesCreatedCount'] = $res[0]['stickyNotesCreatedCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/taskDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['taskCreatedCount'] = $res[0]['taskCreatedCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/serviceReportRequested.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['serviceReportRequestedCount'] = $res[0]['serviceReportRequestedCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/customEmailTemplatesDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['customEmailTemplatesCount'] = $res[0]['customEmailTemplatesCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/autogeneratedDocDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['autogeneratedDocsAccessCount'] = $res[0]['autogeneratedDocsAccessCount'] ?? 0;
        $PCConfigDetails['esignedDocsCount'] = $res[0]['esignedDocsCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/formfieldsEditDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['ffEditCount'] = $res[0]['ffEditCount'] ?? 0;
        $PCConfigDetails['QAEditCount'] = $res[0]['QAEditCount'] ?? 0;
        $PCConfigDetails['FAEditCount'] = $res[0]['FAEditCount'] ?? 0;
        $PCConfigDetails['BOEditCount'] = $res[0]['BOEditCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/customFormFieldsDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['customFFCount'] = $res[0]['customFFCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/softpullCreditDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['creditPullCount'] = $res[0]['creditPullCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/filesCreationDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['fileCreatedViaWebformCount'] = $res[0]['fileCreatedViaWebformCount'] ?? 0;
        $PCConfigDetails['fileCreatedViaBOCount'] = $res[0]['fileCreatedViaBOCount'] ?? 0;
        $PCConfigDetails['fileCreatedViaBranchCount'] = $res[0]['fileCreatedViaBranchCount'] ?? 0;
        $PCConfigDetails['fileCreatedViaBrokerCount'] = $res[0]['fileCreatedViaBrokerCount'] ?? 0;
        $PCConfigDetails['fileCreatedViaBorrowerCount'] = $res[0]['fileCreatedViaBorrowerCount'] ?? 0;
        $PCConfigDetails['fileCreatedViaLoanOfficerCount'] = $res[0]['fileCreatedViaLoanOfficerCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/geraciSubmissionDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['geraciSubmissionCount'] = $res[0]['geraciSubmissionCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/fileEmailsSentDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['fileEmailsSentCount'] = $res[0]['fileEmailsSentCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/bulkEmailsSentDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['bulkEmailsSentCount'] = $res[0]['bulkEmailsSentCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/borrowerProfileRequiredDocsDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['borrowerDocsCount'] = $res[0]['borrowerDocsCount'] ?? 0;
        $PCConfigDetails['entityDocsCount'] = $res[0]['entityDocsCount'] ?? 0;
        $PCConfigDetails['membersDocsCount'] = $res[0]['membersDocsCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/fileTabOrderEditDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['fileTabOrderEditCount'] = $res[0]['fileTabOrderEditCount'] ?? 0;

        $ip = ['PCID' => $PCID];
        $ip['keyNeeded'] = 'n';
        $modulesArray = getPCModules::getReport($ip);
        $resArray = getPCFileTabsDispOrder::getReport([
            'PCID'        => $PCID,
            'moduleCode'  => implode(',', array_column($modulesArray, 'moduleCode')),
            'tabOrderFor' => 'PC',
        ]);
        $fileTabsResArray = $resArray['PCFileTabsDispOrder'];
        foreach ($fileTabsResArray as $tabKey => $tabItem) {
            if ($tabItem['tabName'] == 'MP') {
                if (PageVariables::$allowPCUsersToMarketPlace == 0) {
                    unset($fileTabsResArray[$tabKey]);
                }
            }
            if ($tabItem['tabName'] == 'HR') {
                if (PageVariables::$subscribePCToHOME == 0) {
                    unset($fileTabsResArray[$tabKey]);
                }
            }
            if ($tabItem['tabName'] == 'AW') {
                if (PageVariables::$isPCActiveAloware == 0) {
                    unset($fileTabsResArray[$tabKey]);
                }
            }
            if ($tabItem['tabName'] == 'PE') {
                if (PageVariables::$allowPeerstreet == 0) {
                    unset($fileTabsResArray[$tabKey]);
                }
            }
            if ($tabItem['tabName'] == 'SER2') {
                if (PageVariables::$allowServicing == 0) {
                    unset($fileTabsResArray[$tabKey]);
                }
            }
            if ($tabItem['tabName'] == 'DS' && !in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                unset($fileTabsResArray[$tabKey]);
            }
            if (!glCustomJobForProcessingCompany::showLoanTermsToBusinessFunding($PCID) && $tabItem['tabName'] == 'LT') {
                unset($fileTabsResArray[$tabKey]);
            }

            if (!glCustomJobForProcessingCompany::showLoanInfoV2($PCID) && $tabItem['tabName'] == 'LIV2') {
                unset($fileTabsResArray[$tabKey]);
            }
        }
        $enabledTabsCount = count(array_keys(array_column($fileTabsResArray, 'showTabs'), 1)) ?? 0;
        $disabledTabsCount = count(array_keys(array_column($fileTabsResArray, 'showTabs'), 0)) ?? 0;

        $PCConfigDetails['enabledTabsCount'] = $enabledTabsCount;
        $PCConfigDetails['disabledTabsCount'] = $disabledTabsCount;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/userLoginDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['clientLoginCount'] = $res[0]['clientLoginCount'] ?? 0;
        $PCConfigDetails['agentLoginCount'] = $res[0]['agentLoginCount'] ?? 0;
        $PCConfigDetails['branchLoginCount'] = $res[0]['branchLoginCount'] ?? 0;
        $PCConfigDetails['employeeLoginCount'] = $res[0]['employeeLoginCount'] ?? 0;
//        $PCConfigDetails['superLoginCount'] = $res[0]['superLoginCount'] ?? 0;
//        $PCConfigDetails['auditorLoginCount'] = $res[0]['auditorLoginCount'] ?? 0;
//        $PCConfigDetails['DIYClientLoginCount'] = $res[0]['DIYClientLoginCount'] ?? 0;


        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/defaultAuogeneratedDocsEnabledDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['defaultAuogeneratedDocsEnabledCount'] = $res[0]['defaultAuogeneratedDocsEnabledCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/PCAssignedFileTypeDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['fileTypeName'] = $res[0]['fileTypeName'] ?? 0;


        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/PCWorkflowDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $PCConfigDetails['activePCCreatedWorkflowCount'] = $res[0]['activePCCreatedWorkflowCount'] ?? 0;
        $PCConfigDetails['inactivePCCreatedWorkflowCount'] = $res[0]['inactivePCCreatedWorkflowCount'] ?? 0;
        $PCConfigDetails['inactiveWorkflowCount'] = $res[0]['inactiveWorkflowCount'] ?? 0;
        $PCConfigDetails['totalInactiveWorkflowCount'] = $res[0]['totalInactiveWorkflowCount'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/PCConfigDetails/sql/deletedRequiredDocsDetails.sql');
        $res = Database2::getInstance()->queryData($sql, [
            'PCID' => $PCID,
        ]);
        $deletedRequiredDocsCount = count(array_keys(array_column($res, 'doc_status'), 'Deleted')) ?? 0;
        $PCConfigDetails['deletedRequiredDocsCount'] = $deletedRequiredDocsCount;


        return $PCConfigDetails;
    }
}