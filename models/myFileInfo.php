<?php

namespace models;

use models\composite\oBinder\getBinderDocument;
use models\composite\oBranch\getBranchAssignedEmployees;
use models\composite\oFile\getFileInfo\addedToFav;
use models\composite\oFile\getFileInfo\adverseActionInfo;
use models\composite\oFile\getFileInfo\BillingFeeInfo;
use models\composite\oFile\getFileInfo\BillingPaymentInfo;
use models\composite\oFile\getFileInfo\clientDocsArray;
use models\composite\oFile\getFileInfo\collateralArray;
use models\composite\oFile\getFileInfo\creditMemoArray;
use models\composite\oFile\getFileInfo\creditorInfoStatus;
use models\composite\oFile\getFileInfo\fileBudgetAndDrawDoc;
use models\composite\oFile\getFileInfo\fileExpFilpGroundUp;
use models\composite\oFile\getFileInfo\fileSubstatusInfo;
use models\composite\oFile\getFileInfo\HMLOPCReport;
use models\composite\oFile\getFileInfo\lpArray;
use models\composite\oFile\getFileInfo\NotesInfo;
use models\composite\oFile\getFileInfo\PCBasicLoanTabFileIdExists;
use models\composite\oFile\getFileInfo\PCStatusInfo;
use models\composite\oFile\getFileInfo\PCSubStatusInfo;
use models\composite\oFile\getFileInfo\propertyCountyInfo;
use models\composite\oFile\getFileInfo\propertyValuationDocs;
use models\constants\gl\glFUModulesNotesTypeArray;
use models\lendingwise\collateral;
use models\lendingwise\db\collateral_db;
use models\lendingwise\db\contingentLiabilities_db;
use models\lendingwise\db\estimatedProjectCost_db;
use models\lendingwise\db\tblBorrowerAlternateNames_db;
use models\lendingwise\db\tblFileHMLOAssetsInfo_db;
use models\lendingwise\db\tblFileHMLOBusinessEntityRef_db;
use models\lendingwise\db\tblFileLoanOrigination_db;
use models\lendingwise\db\tblFileLOCoBEmploymentInfo_db;
use models\lendingwise\db\tblFileLOExplanation_db;
use models\lendingwise\db\tblFileLOScheduleRealInfo_db;
use models\lendingwise\db\tblGiftsOrGrants_db;
use models\lendingwise\db\tblMemberOfficerInfo_db;
use models\lendingwise\db\tblOffers_db;
use models\lendingwise\db\tblOtherNewMortgageLoansOnProperty_db;
use models\lendingwise\db\tblPropertyManagement_db;
use models\lendingwise\db\tblPropertyManagementDocs_db;
use models\lendingwise\db\tblRefinanceMortgage_db;
use models\lendingwise\db\tblRestInfo_db;
use models\lendingwise\estimatedProjectCost;
use models\lendingwise\tblAdverseAction;
use models\lendingwise\tblAgent;
use models\lendingwise\tblAssetsInfo;
use models\lendingwise\tblBorrowerAlternateNames;
use models\lendingwise\tblBranch;
use models\lendingwise\tblChecklistFlatNotes;
use models\lendingwise\tblClient;
use models\lendingwise\tblClientDocs;
use models\lendingwise\tblCustomFieldValue;
use models\lendingwise\tblCreditMemo;
use models\lendingwise\tblFile;
use models\lendingwise\tblFile2;
use models\lendingwise\tblFileAdditionalGuarantors;
use models\lendingwise\tblFileAdminInfo;
use models\lendingwise\tblFileCalculatedValues;
use models\lendingwise\tblFileCFPB;
use models\lendingwise\tblFileExpFilpGroundUp;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFileHMLOAssetsInfo;
use models\lendingwise\tblFileHMLOBackGround;
use models\lendingwise\tblFileHMLOBusinessEntity;
use models\lendingwise\tblFileHMLOBusinessEntityRef;
use models\lendingwise\tblFileHMLOExperience;
use models\lendingwise\tblFileHMLONewLoanInfo;
use models\lendingwise\tblFileHMLOPropInfo;
use models\lendingwise\tblFileHUDBasicInfo;
use models\lendingwise\tblFileLoanOrigination;
use models\lendingwise\tblFileLOAssetsInfo;
use models\lendingwise\tblFileLOBorEmploymentInfo;
use models\lendingwise\tblFileLOChekingSavingInfo;
use models\lendingwise\tblFileLOCoBEmploymentInfo;
use models\lendingwise\tblFileLOExplanation;
use models\lendingwise\tblFileLOLiabilitiesInfo;
use models\lendingwise\tblFileLOScheduleRealInfo;
use models\lendingwise\tblFileMFLoanTerms;
use models\lendingwise\tblFilePropertyInfo;
use models\lendingwise\tblFileResponse;
use models\lendingwise\tblFileSBAQuestions;
use models\lendingwise\tblGiftsOrGrants;
use models\lendingwise\tblIncomeInfo;
use models\lendingwise\tblListingPageData;
use models\lendingwise\tblLMRClientType;
use models\lendingwise\tblLMRCreditorInfo;
use models\lendingwise\tblLMRHUDItemsPayableLoan;
use models\lendingwise\tblLoanPropertySummary;
use models\lendingwise\tblMemberOfficerInfo;
use models\lendingwise\tblMissingDocuments;
use models\lendingwise\tblOffers;
use models\lendingwise\tblOtherNewMortgageLoansOnProperty;
use models\lendingwise\tblPartnerShips;
use models\lendingwise\tblPCHMLOBasicLoanAmortizationInfo;
use models\lendingwise\tblPCHMLOBasicLoanEntityStateOfFormation;
use models\lendingwise\tblPCHMLOBasicLoanEntityType;
use models\lendingwise\tblPCHMLOBasicLoanEquipmentType;
use models\lendingwise\tblPCHMLOBasicLoanExitStrategy;
use models\lendingwise\tblPCHMLOBasicLoanExtensionOption;
use models\lendingwise\tblPCHMLOBasicLoanInfo;
use models\lendingwise\tblPCHMLOBasicLoanLoanPurpose;
use models\lendingwise\tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy;
use models\lendingwise\tblPCHMLOBasicLoanMinSeasoningForeclosure;
use models\lendingwise\tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy;
use models\lendingwise\tblPCHMLOBasicLoanMinTimeInBusiness;
use models\lendingwise\tblPCHMLOBasicLoanNichesInfo;
use models\lendingwise\tblPCHMLOBasicLoanOccupancy;
use models\lendingwise\tblPCHMLOBasicLoanPaymentFrequency;
use models\lendingwise\tblPCHMLOBasicLoanPropertyType;
use models\lendingwise\tblPCHMLOBasicLoanRateLockPeriod;
use models\lendingwise\tblPCHMLOBasicLoanSBALoanProduct;
use models\lendingwise\tblPCHMLOBasicLoanTermInfo;
use models\lendingwise\tblPCHMLOBasicLoanTransactionType;
use models\lendingwise\tblPreBoarding;
use models\lendingwise\tblPrincipalPayDown;
use models\lendingwise\tblProcessingCompany;
use models\lendingwise\tblProperties;
use models\lendingwise\tblPropertyManagement;
use models\lendingwise\tblPropertyManagementDocs;
use models\lendingwise\tblProposalInfo;
use models\lendingwise\tblPropSellerInfo;
use models\lendingwise\tblQAInfo;
use models\lendingwise\tblRefinanceMortgage;
use models\lendingwise\tblRestInfo;
use models\lendingwise\tblShortSale;
use models\myFileInfo\AddGuarantorsInfo;
use models\myFileInfo\assetInfo;
use models\myFileInfo\AssignedBOStaffInfo;
use models\myFileInfo\branchClientTypeInfo;
use models\myFileInfo\BranchInfo;
use models\myFileInfo\branchModuleInfo;
use models\myFileInfo\BrokerInfo;
use models\myFileInfo\budgetAndDrawsInfo;
use models\myFileInfo\ChecklistFlatNotes;
use models\myFileInfo\cklistNotRequired;
use models\myFileInfo\creditorInfo;
use models\myFileInfo\employeeInfo;
use models\myFileInfo\employeeInfo\AdminUsers;
use models\myFileInfo\fileCheckListInfo;
use models\myFileInfo\FileContacts;
use models\myFileInfo\fileHMLOBackGroundInfo;
use models\myFileInfo\fileHMLOChecklistUploadDocs;
use models\myFileInfo\fileHMLOEntityInfo;
use models\myFileInfo\fileHMLOInfo;
use models\myFileInfo\fileHMLOPropertyInfo;
use models\myFileInfo\FileHUD;
use models\myFileInfo\fileMFLoanTermsSummary;
use models\myFileInfo\fileModuleInfo;
use models\myFileInfo\FileProInfo;
use models\myFileInfo\listingRealtorInfo;
use models\myFileInfo\LMRadditionalLoanprograms;
use models\myFileInfo\LMRChecklist;
use models\myFileInfo\LMRClientTypeInfo;
use models\myFileInfo\LMRData;
use models\myFileInfo\LMRInfo;
use models\myFileInfo\LMRInternalLoanprograms;
use models\myFileInfo\missingDocInfo;
use models\myFileInfo\netOperatingIncome;
use models\myFileInfo\PCCheckListInfo;
use models\myFileInfo\PCClientWFServiceType;
use models\myFileInfo\PCFileDocsName;
use models\myFileInfo\PCInfo;
use models\myFileInfo\relatedDocs;
use models\myFileInfo\SecondaryBrokerInfo;
use models\myFileInfo\WFIDs;
use models\myFileInfo\Workflow;
use models\myFileInfo\Workflow\agentCLInfo;
use models\myFileInfo\Workflow\branchCLInfo;
use models\myFileInfo\Workflow\empCLInfo;
use models\types\strongType;

class myFileInfo extends strongType
{
    public ?int $LMRId = null;

    /**
     * @var tblFileAdditionalGuarantors[]
     */
    private ?array $AddGuarantorsInfo = null;

    /**
     * @var AssignedBOStaffInfo[]
     */
    private ?array $AssignedBOStaffInfo = null;

    /**
     * @var fileModuleInfo[]
     */
    private ?array $fileModuleInfo = null;

    private ?tblFile $tblFile = null;
    /**
     * @var branchClientTypeInfo[]
     */
    private ?array $branchClientTypeInfo = null;

    private ?tblBranch $BranchInfo = null;
    /**
     * @var branchModuleInfo[]
     */
    private ?array $branchModuleInfo = null;
    private ?tblAgent $BrokerInfo = null;

    private ?cklistNotRequired $cklistNotRequired = null;

    /**
     * @var void
     */
    private $chkNotReqBranchInfo = null;

    /**
     * @var void
     */
    private $cklistNotRequiredArray = null;

    /**
     * @var void
     */
    private $cklistNotRequiredNewArray = null;

    /**
     * @var void
     */
    private $chkNotReqEmpInfo = null;

    /**
     * @var void
     */
    private $chkNotReqAgentInfo = null;

    /**
     * @var relatedDocs[]
     */
    private ?array $relatedDocs = null;

    /**
     * @var relatedDocs[]
     */
    private ?array $docArray = null;

    /**
     * @var relatedDocs[]
     */
    private ?array $HMLOPropValuationDocInfo = null;

    /**
     * @var relatedDocs[]
     */
    private ?array $propertyValuationDocInfo = null;

    private ?array $PCFileDocsCategory = null;
    /**
     * @var PCFileDocsName[]
     */
    private ?array $PCFileDocsName = null;
    /**
     * @var tblLMRClientType[]
     */
    private ?array $LMRClientTypeInfo = null;
    private ?array $ClientTypes = null;
    private ?string $LMRClientType = null;
    /**
     * @var PCCheckListInfo[]
     */
    private ?array $PCCheckListInfo = null;
    private ?LMRInfo $LMRInfo = null;

    /* @var fileCheckListInfo[][] */
    private ?array $fileCheckListInfo = null;
    /**
     * @var missingDocInfo[][]
     */
    private ?array $missingDocInfo = null;
    private ?Workflow $Workflow = null;
    private ?array $fileChecklistNotesInfoNew = null;
    private ?fileHMLOChecklistUploadDocs $fileHMLOChecklistUploadDocs = null;
    /**
     * @var fileHMLOChecklistUploadDocs[][] $fileHMLOChecklistUploadDocsNew
     */
    private ?array $fileHMLOChecklistUploadDocsNew = null;
    private ?tblFileHMLOPropInfo $fileHMLOPropertyInfo = null;
    private ?tblFilePropertyInfo $FileProInfo = null;
    private ?tblFileHMLOBusinessEntity $fileHMLOEntityInfo = null;
    private ?tblFileHMLO $fileHMLOInfo = null;
    private ?tblFileHMLOBackGround $fileHMLOBackGroundInfo = null;
    private ?array $CLEmpInfo = null;
    private ?array $CLBranchInfo = null;
    private ?array $CLAgentInfo = null;
    private ?tblProcessingCompany $PCInfo = null;

    /* @var AdminUsers[] $employeeInfo */
    private ?array $employeeInfo = null;
    /**
     * @var tblLMRCreditorInfo[]
     */
    private ?array $creditorInfo = null;
    private ?LMRInternalLoanprograms $LMRInternalLoanprogramsReport = null;
    private ?array $LMRInternalLoanprogramsLong = null;
    private ?array $LMRInternalLoanprograms = null;
    private ?array $LMRadditionalLoanprograms = null;
    private ?tblAgent $SecondaryBrokerInfo = null;
    private ?LMRChecklist $LMRChecklist = null;
    /**
     * @var tblMissingDocuments[]
     */
    private ?array $LMRChecklistInfo = null;
    /**
     * @var tblMissingDocuments[][]
     */
    private ?array $LMRChecklistInfoNew = null;

    private ?FileContacts $FileContacts = null;
    private ?array $fileModuleCodes = null;
    /**
     * @var composite\oBranch\getBranchAssignedEmployees[][]
     */
    private ?array $BranchAssignedEmployees = null;

    /* @var tblChecklistFlatNotes[] $ChecklistFlatNotes */
    private ?array $ChecklistFlatNotes = null;
    private ?array $ChecklistFlatNotesByName = null;

    /* @var int[][] */
    private ?array $ChecklistFlatNotesCount = null;
    private ?tblShortSale $listingRealtorInfo = null;
    private ?LMRData $LMRData = null;
    /**
     * @var budgetAndDrawsInfo[]
     */
    private ?array $budgetAndDrawsInfo = null;
    /**
     * @var tblPrincipalPayDown[]
     */
    private ?array $paydownInfo = null;
    private ?tblIncomeInfo $incomeInfo = null;
    private ?assetInfo $assetInfo = null;

    private ?tblFileAdminInfo $fileAdminInfo = null;
    private ?tblFileHMLONewLoanInfo $fileHMLONewLoanInfo = null;
    private ?fileMFLoanTermsSummary $fileMFLoanTermsSummary = null;
    private ?netOperatingIncome $netOperatingIncome = null;

    private ?array $HUDItemsPayableLoan = null;
    private ?tblLoanPropertySummary $loanPropertySummary = null;
    private ?tblFileCalculatedValues $fileCalculatedValues = null;

    private ?tblFileHUDBasicInfo $fileHUDBasicInfo = null;

    private ?tblFileResponse $fileResponse = null;
    private ?string $creditDecisionClearToCloseBy = null;
    private ?string $requiredInsuranceInfo = null;
    private ?tblAdverseAction $adverseActionInfo = null;
    private ?tblFileHMLOExperience $fileHMLOExperienceInfo = null;
    private ?tblFile2 $file2Info = null;
    /**
     * @var tblCreditMemo[]
     */
    private ?array $creditMemoArray = null;
    private ?tblAssetsInfo $AssetsInfo = null;
    private ?tblFileLOAssetsInfo $fileLOAssetsInfo = null;

    /**
     * @var tblCustomFieldValue[]
     */
    private ?array $customFields = null;
    private ?string $createdDateWithTimestamp = null;
    private ?tblPreBoarding $preBoardingInfo;

    /**
     * @return tblFileAdditionalGuarantors[]
     */
    public function AddGuarantorsInfo(): array
    {
        if (is_null($this->AddGuarantorsInfo)) {
            $this->AddGuarantorsInfo = AddGuarantorsInfo::getReport($this->LMRId);
        }
        return $this->AddGuarantorsInfo;
    }

    public function AssignedBOStaffInfo(): array
    {
        if (is_null($this->AssignedBOStaffInfo)) {
            $this->AssignedBOStaffInfo = AssignedBOStaffInfo::getReport($this->LMRId);
        }
        return $this->AssignedBOStaffInfo;
    }

    public function fileModuleInfo(): ?array
    {
        if (is_null($this->fileModuleInfo) && $this->LMRId) {
            $this->fileModuleInfo = fileModuleInfo::getReport($this->LMRId);
        }
        return $this->fileModuleInfo;
    }

    public function getLoanPrograms(): array
    {
        // LMRClientType_chosen
        // LMRInternalLoanProgram_chosen

        $clientTypes = $this->ClientTypes();
        $internal = $this->LMRInternalLoanprograms();
        $list = [];
        foreach ($clientTypes as $type) {
            $list[] = $type;
        }
        foreach ($internal as $item) {
            $list[] = $item;
        }
        return $list;
    }

    public function getFileTypes(): ?array
    {
        return $this->getFileModuleCodes();
    }

    public function getFileModuleCodes(): ?array
    {
        if (is_null($this->fileModuleCodes)) {
            $this->fileModuleCodes = [];
            foreach ($this->fileModuleInfo() as $item) {
                $this->fileModuleCodes[] = $item->moduleCode;
            }
        }
        return $this->fileModuleCodes;
    }

    public function isHMLO(): bool
    {
        return in_array('HMLO', $this->getFileModuleCodes());
    }

    public function isLO(): bool
    {
        return in_array('LO', $this->getFileModuleCodes());
    }

    public function tblFile(): ?tblFile
    {
        if (is_null($this->tblFile)) {
            $this->tblFile = tblFile::Get(['LMRId' => $this->LMRId]);
        }
        return $this->tblFile;
    }

    public function file2Info(): ?tblFile2
    {
        if (is_null($this->file2Info) && $this->LMRId) {
            $this->file2Info = tblFile2::Get(['LMRID' => $this->LMRId]);
        }
        return $this->file2Info;
    }

    public function branchClientTypeInfo(): ?array
    {
        if (is_null($this->branchClientTypeInfo)) {
            $this->branchClientTypeInfo = branchClientTypeInfo::getReport($this->tblFile()->FBRID, $this->fileModuleInfo());
        }
        return $this->branchClientTypeInfo;
    }

    public function BranchInfo(): ?tblBranch
    {
        if (is_null($this->BranchInfo)) {
            $this->BranchInfo = BranchInfo::getReport($this->tblFile()->FBRID);
        }
        return $this->BranchInfo;
    }

    public function branchModuleInfo(): ?array
    {
        if (is_null($this->branchModuleInfo)) {
            $this->branchModuleInfo = branchModuleInfo::getReport($this->tblFile()->FBRID);
        }
        return $this->branchModuleInfo;
    }

    private ?tblProposalInfo $proposalInfo = null;

    public function proposalInfo(): ?tblProposalInfo
    {
        if (is_null($this->proposalInfo)) {
            $this->proposalInfo = tblProposalInfo::Get(['LMRId' => $this->LMRId]);
        }
        return $this->proposalInfo;
    }

    public function BrokerInfo(): ?tblAgent
    {
        if (is_null($this->BrokerInfo)) {
            $this->BrokerInfo = BrokerInfo::getReport($this->tblFile()->brokerNumber);
        }
        return $this->BrokerInfo;
    }

    public function cklistNotRequired(): ?cklistNotRequired
    {
        if (is_null($this->cklistNotRequired)) {
            $this->cklistNotRequired = cklistNotRequired::getReport($this->LMRId);
        }
        return $this->cklistNotRequired;
    }

    public function cklistNotRequiredArray()
    {
        if (is_null($this->cklistNotRequiredArray)) {
            $this->cklistNotRequiredArray = $this->cklistNotRequired()->cklistNotRequiredArray;
        }
        return $this->cklistNotRequiredArray;
    }

    public function cklistNotRequiredNewArray()
    {
        if (is_null($this->cklistNotRequiredNewArray)) {
            $this->cklistNotRequiredNewArray = $this->cklistNotRequired()->cklistNotRequiredNewArray;
        }
        return $this->cklistNotRequiredNewArray;
    }

    public function chkNotReqEmpInfo()
    {
        if (is_null($this->chkNotReqEmpInfo)) {
            $this->chkNotReqEmpInfo = $this->cklistNotRequired()->chkNotReqEmpInfo;
        }
        return $this->chkNotReqEmpInfo;
    }

    public function chkNotReqAgentInfo()
    {
        if (is_null($this->chkNotReqAgentInfo)) {
            $this->chkNotReqAgentInfo = $this->cklistNotRequired()->chkNotReqAgentInfo;
        }
        return $this->chkNotReqAgentInfo;
    }

    public function chkNotReqBranchInfo()
    {
        if (is_null($this->chkNotReqBranchInfo)) {
            $this->chkNotReqBranchInfo = $this->cklistNotRequired()->chkNotReqBranchInfo;
        }
        return $this->chkNotReqBranchInfo;
    }

    public function relatedDocs(): ?array
    {
        if (is_null($this->relatedDocs) && $this->LMRId) {
            $this->relatedDocs = relatedDocs::getReport($this->LMRId);
        }
        return $this->relatedDocs;
    }

    /* @var composite\oBinder\getBinderDocument[][] $binderDocs */
    private ?array $binderDocs = null;

    /**
     * @return composite\oBinder\getBinderDocument[][]|null
     */
    public function binderDocs(): ?array
    {
        if (is_null($this->binderDocs) && $this->LMRId) {
            $this->binderDocs = getBinderDocument::getReportObjects(
                $this->LMRId,
                'creDate',
                'desc',
                1
            );
        }
        return $this->binderDocs;
    }

    public function docArray(): array
    {
        if (is_null($this->docArray)) {
            $this->docArray = [];
            foreach ($this->relatedDocs() as $doc) {
                if (!$doc->isPropertyValuationDocInfo() && !$doc->isHMLOPropValuationDocInfo()) {
                    $this->docArray[] = $doc;
                }
            }
        }
        return $this->docArray;
    }

    public function propertyValuationDocInfo(): ?array
    {
        if (is_null($this->propertyValuationDocInfo)) {
            $this->propertyValuationDocInfo = [];
            foreach ($this->relatedDocs() as $doc) {
                if ($doc->isPropertyValuationDocInfo()) {
                    $this->propertyValuationDocInfo[] = $doc;
                }
            }
        }
        return $this->docArray;
    }

    public function HMLOPropValuationDocInfo(): array
    {
        if (is_null($this->HMLOPropValuationDocInfo)) {
            $this->HMLOPropValuationDocInfo = [];
            foreach ($this->relatedDocs() as $doc) {
                if ($doc->isHMLOPropValuationDocInfo()) {
                    $this->HMLOPropValuationDocInfo[] = $doc;
                }
            }
        }
        return $this->HMLOPropValuationDocInfo;
    }


    public function PCFileDocsCategory(): ?array
    {
        if (is_null($this->PCFileDocsCategory)) {
            $this->PCFileDocsCategory = [];
            foreach ($this->PCFileDocsName() as $item) {
                $this->PCFileDocsCategory[$item->categoryId] = $item->category;
            }
        }
        return $this->PCFileDocsCategory;
    }

    public function PCFileDocsName(): ?array
    {
        if (is_null($this->PCFileDocsName)) {
            $this->PCFileDocsName = PCFileDocsName::getReport(
                $this->tblFile()->FPCID,
                $this->fileModuleInfo()
            );
        }
        return $this->PCFileDocsName;
    }

    public function LMRClientTypeInfo(bool $refresh = false): ?array
    {
        if($this->LMRId) {
            if ($refresh || is_null($this->LMRClientTypeInfo)) {
                $this->LMRClientTypeInfo = LMRClientTypeInfo::getReport($this->LMRId);
            }
        }
        return $this->LMRClientTypeInfo;
    }

    public function ClientTypes(): ?array
    {
        if (is_null($this->ClientTypes)) {
            $this->ClientTypes = [];
            foreach ($this->LMRClientTypeInfo() as $item) {
                $this->ClientTypes[] = $item->ClientType;
            }
        }
        return $this->ClientTypes;
    }

    public function getLMRClientType(): ?string
    {

        if (is_null($this->LMRClientType)) {
            $this->LMRClientType = implode(',', $this->ClientTypes());
        }
        return $this->LMRClientType;
    }

    public function PCCheckListInfo(string $userGroup, int $externalBroker): ?array
    {
        if (is_null($this->PCCheckListInfo)) {
            $this->PCCheckListInfo = PCCheckListInfo::getReport(
                $this->tblFile()->FPCID,
                $this->fileModuleInfo(),
                $this->ClientTypes(),
                $externalBroker,
                $userGroup
            );
        }
        return $this->PCCheckListInfo;
    }

    public function LMRInfo(): ?LMRInfo
    {
        if (is_null($this->LMRInfo)) {
            $this->LMRInfo = LMRInfo::getReport($this->LMRId);
        }
        return $this->LMRInfo;
    }

    /**
     * @param string $userGroup
     * @param int $externalBroker
     * @return fileCheckListInfo[][]|null
     */
    public function fileCheckListInfo(string $userGroup, int $externalBroker): ?array
    {
        if (is_null($this->fileCheckListInfo)) {
            $this->fileCheckListInfo = fileCheckListInfo::getReport($userGroup, $this->LMRId, $externalBroker);
        }
        return $this->fileCheckListInfo;
    }

    public function missingDocInfo(): ?array
    {
        if (is_null($this->missingDocInfo)) {
            $this->missingDocInfo = missingDocInfo::getReport($this->LMRId);
        }
        return $this->missingDocInfo;
    }

    public function Workflow(): ?Workflow
    {
        if (is_null($this->Workflow)) {
            $this->Workflow = Workflow::getReport($this->LMRId);
        }
        return $this->Workflow;
    }

    /**
     * @return ChecklistFlatNotes[][]|null
     */
    public function fileChecklistNotesInfoNew(): ?array
    {
        if (is_null($this->fileChecklistNotesInfoNew)) {
            $this->fileChecklistNotesInfoNew = $this->Workflow()->fileChecklistNotesInfoNew;
        }
        return $this->fileChecklistNotesInfoNew;
    }

    public function fileHMLOChecklistUploadDocs(): ?fileHMLOChecklistUploadDocs
    {
        if (is_null($this->fileHMLOChecklistUploadDocs)) {
            $this->fileHMLOChecklistUploadDocs = fileHMLOChecklistUploadDocs::getReport($this->LMRId);
        }
        return $this->fileHMLOChecklistUploadDocs;
    }

    public function fileHMLOChecklistUploadDocsNew(): ?array
    {
        if (is_null($this->fileHMLOChecklistUploadDocsNew)) {
            $this->fileHMLOChecklistUploadDocsNew = $this->fileHMLOChecklistUploadDocs()->fileHMLOChecklistUploadDocsNew;
        }
        return $this->fileHMLOChecklistUploadDocsNew;
    }

    public function fileHMLOPropertyInfo(): ?tblFileHMLOPropInfo
    {
        if (is_null($this->fileHMLOPropertyInfo) && $this->LMRId) {
            $this->fileHMLOPropertyInfo = fileHMLOPropertyInfo::getReport($this->LMRId);
        }
        return $this->fileHMLOPropertyInfo;
    }

    public function FileProInfo(): ?lendingwise\tblFilePropertyInfo
    {
        if (is_null($this->FileProInfo) && $this->LMRId) {
            $this->FileProInfo = FileProInfo::getReport($this->LMRId);
        }
        return $this->FileProInfo;
    }

    /* @var tblProperties[] $getProperties */
    private ?array $getProperties = null;

    /**
     * @return tblProperties[]|null
     */
    public function getProperties(): ?array
    {
        if (is_null($this->getProperties) && $this->LMRId) {
            $this->getProperties = tblProperties::GetAll(['LMRId' => $this->LMRId]);
        }
        return $this->getProperties;
    }

    private ?tblProperties $getPrimaryProperty = null;

    /**
     * @return tblProperties|null
     */
    public function getPrimaryProperty(): ?tblProperties
    {
        if (is_null($this->getPrimaryProperty)) {
            $this->getPrimaryProperty = new tblProperties();
            foreach ($this->getProperties() as $propertyInfo) {
                if ($propertyInfo->isPrimary) {
                    $this->getPrimaryProperty = $propertyInfo;
                    break;
                }
            }
        }
        return $this->getPrimaryProperty;
    }

    public function fileHMLOEntityInfo(): ?tblFileHMLOBusinessEntity
    {
        if (is_null($this->fileHMLOEntityInfo) && $this->LMRId) {
            $this->fileHMLOEntityInfo = fileHMLOEntityInfo::getReport($this->LMRId);
        }
        return $this->fileHMLOEntityInfo;
    }

    public function fileHMLOInfo(): ?tblFileHMLO
    {
        if (is_null($this->fileHMLOInfo) && $this->LMRId) {
            $this->fileHMLOInfo = fileHMLOInfo::getReport($this->LMRId);
        }
        return $this->fileHMLOInfo;
    }

    // fileHMLOBackGroundInfo

    public function fileHMLOBackGroundInfo(): ?tblFileHMLOBackGround
    {
        if (is_null($this->fileHMLOBackGroundInfo) && $this->LMRId) {
            $this->fileHMLOBackGroundInfo = fileHMLOBackGroundInfo::getReport($this->LMRId);
        }
        return $this->fileHMLOBackGroundInfo;
    }

    /**
     * @return empCLInfo[]|null
     */
    public function CLEmpInfo(): ?array
    {
        if (is_null($this->CLEmpInfo)) {
            $this->CLEmpInfo = $this->Workflow()->empCLInfoArray;
        }
        return $this->CLEmpInfo;
    }

    /**
     * @return branchCLInfo[]|null
     */
    public function CLBranchInfo(): ?array
    {
        if (is_null($this->CLBranchInfo)) {
            $this->CLBranchInfo = $this->Workflow()->branchCLInfoArray;
        }
        return $this->CLBranchInfo;
    }

    /**
     * @return agentCLInfo[]|null
     */
    public function CLAgentInfo(): ?array
    {
        if (is_null($this->CLAgentInfo)) {
            $this->CLAgentInfo = $this->Workflow()->agentCLInfoArray;
        }
        return $this->CLAgentInfo;
    }

    public function PCInfo(?int $PCID = null): ?tblProcessingCompany
    {
        // Pass PCID in when using on Public Pages without an existing Loan Record
        if (is_null($this->PCInfo)) {
            $this->PCInfo = PCInfo::getReport($PCID ?: $this->tblFile()->FPCID);
        }
        return $this->PCInfo;
    }

    /**
     * @return AdminUsers[]|null
     */
    public function employeeInfo(): ?array
    {
        if (is_null($this->employeeInfo)) {
            $this->employeeInfo = employeeInfo::getReport($this->tblFile()->FPCID);
        }
        return $this->employeeInfo;
    }

    /**
     * @return tblLMRCreditorInfo[]
     */
    public function creditorInfo(): ?array
    {
        if (is_null($this->creditorInfo) && $this->LMRId) {
            $this->creditorInfo = creditorInfo::getReport($this->LMRId);
        }
        return $this->creditorInfo;
    }

    /**
     * @return LMRInternalLoanprograms|null
     */
    public function LMRInternalLoanprogramsReport(): ?LMRInternalLoanprograms
    {
        if (is_null($this->LMRInternalLoanprogramsReport) && $this->LMRId) {
            $this->LMRInternalLoanprogramsReport = LMRInternalLoanprograms::getReport($this->LMRId);
        }
        return $this->LMRInternalLoanprogramsReport;
    }

    /**
     * @return array|null
     */
    public function LMRInternalLoanprograms(): ?array
    {
        if (is_null($this->LMRInternalLoanprograms)) {
            $this->LMRInternalLoanprograms = $this->LMRInternalLoanprogramsReport()->LMRInternalLoanprograms;
        }
        return $this->LMRInternalLoanprograms;
    }

    public function LMRInternalLoanprogramsLong(): ?array
    {
        if (is_null($this->LMRInternalLoanprogramsLong)) {
            $this->LMRInternalLoanprogramsLong = $this->LMRInternalLoanprogramsReport()->LMRInternalLoanprogramsLong;
        }
        return $this->LMRInternalLoanprogramsLong;
    }

    /**
     * @return array|null
     */
    public function LMRadditionalLoanprograms(): ?array
    {
        if (is_null($this->LMRadditionalLoanprograms) && $this->LMRId) {
            $this->LMRadditionalLoanprograms = LMRadditionalLoanprograms::getReport($this->LMRId);
        }
        return $this->LMRadditionalLoanprograms;
    }

    public function SecondaryBrokerInfo(): ?tblAgent
    {
        if (is_null($this->SecondaryBrokerInfo) && $this->tblFile()->secondaryBrokerNumber) {
            $this->SecondaryBrokerInfo = SecondaryBrokerInfo::getReport($this->tblFile()->secondaryBrokerNumber);
        }
        return $this->SecondaryBrokerInfo;
    }

    public function LMRChecklist(): LMRChecklist
    {
        if (is_null($this->LMRChecklist) && $this->LMRId) {
            $this->LMRChecklist = LMRChecklist::getReport($this->LMRId);
        }
        return $this->LMRChecklist;
    }

    /**
     * @return tblMissingDocuments[]|null
     */
    public function LMRChecklistInfo(): ?array
    {
        if (is_null($this->LMRChecklistInfo)) {
            $this->LMRChecklistInfo = $this->LMRChecklist()->LMRChecklistInfo;
        }
        return $this->LMRChecklistInfo;
    }

    /**
     * @return lendingwise\tblMissingDocuments[][]|null
     */
    public function LMRChecklistInfoNew(): ?array
    {
        if (is_null($this->LMRChecklistInfoNew)) {
            $this->LMRChecklistInfoNew = $this->LMRChecklist()->LMRChecklistInfoNew;
        }
        return $this->LMRChecklistInfoNew;
    }

    public function FileContacts(): ?FileContacts
    {
        if (is_null($this->FileContacts) && $this->LMRId) {
            $this->FileContacts = FileContacts::getReport($this->LMRId);
        }
        return $this->FileContacts;
    }

    public function BranchAssignedEmployees(): array
    {
        if (is_null($this->BranchAssignedEmployees) && $this->LMRId) {
            $this->BranchAssignedEmployees = getBranchAssignedEmployees::getReportObjects(
                $this->tblFile()->FPCID,
                $this->tblFile()->FBRID
            );
        }
        return $this->BranchAssignedEmployees;
    }

    /**
     * @return ChecklistFlatNotes[][][]|null
     */
    public function ChecklistFlatNotes(): ?array
    {
        if (is_null($this->ChecklistFlatNotes) && $this->LMRId) {
            $res = ChecklistFlatNotes::getReport($this->LMRId);
            $this->ChecklistFlatNotes = [];
            foreach ($res as $item) {
                if (!$item->notesType || $item->notesType == 'checklist') {
                    $item->notesType = glFUModulesNotesTypeArray::GENERAL;
                }
                $this->ChecklistFlatNotes[$item->CID][$item->notesType][] = $item;
            }
        }
        return $this->ChecklistFlatNotes;
    }

    public function ChecklistFlatNoteCount(): ?array
    {
        if (is_null($this->ChecklistFlatNotesCount)) {
            $sql = '
                SELECT IF(ISNULL(notesType) OR notesType = \'checklist\', \'GE\', notesType) AS notesType
                     , CID
                     , COUNT(*) AS cnt
                FROM tblChecklistFlatNotes 
                WHERE fileID = @fileId
               GROUP BY notesType, CID
            ';
            $res = Database2::getInstance()->queryData($sql, ['fileId' => $this->LMRId]);
            $this->ChecklistFlatNotesCount = [];
            foreach ($res as $item) {
                $this->ChecklistFlatNotesCount[$item['CID']][$item['notesType']] = $item['cnt'];
            }
        }
        return $this->ChecklistFlatNotesCount;
    }


    public function listingRealtorInfo(): ?tblShortSale
    {
        if (is_null($this->listingRealtorInfo)) {
            $this->listingRealtorInfo = listingRealtorInfo::getReport($this->LMRId);
        }
        return $this->listingRealtorInfo;
    }

    public function LMRData(): ?LMRData
    {
        if (is_null($this->LMRData) && $this->LMRId) {
            $this->LMRData = LMRData::getReport($this->LMRId);
        }
        return $this->LMRData;
    }

    /**
     * @return budgetAndDrawsInfo[]
     */
    public function budgetAndDrawsInfo(): ?array
    {
        if (is_null($this->budgetAndDrawsInfo) && $this->LMRId) {
            $this->budgetAndDrawsInfo = budgetAndDrawsInfo::getReport($this->LMRId);
        }
        return $this->budgetAndDrawsInfo;
    }

    public function paydownInfo(): ?array
    {
        if (is_null($this->paydownInfo) && $this->LMRId) {
            $this->paydownInfo = tblPrincipalPayDown::GetAll(['LMRId' => $this->LMRId]);
        }
        return $this->paydownInfo;
    }

    public function incomeInfo(): ?tblIncomeInfo
    {
        if (is_null($this->incomeInfo) && $this->LMRId) {
            $this->incomeInfo = tblIncomeInfo::Get(['LMRId' => $this->LMRId]);
        }
        return $this->incomeInfo;
    }

    public function assetInfo(): ?assetInfo
    {
        if (is_null($this->assetInfo) && $this->LMRId) {
            $this->assetInfo = assetInfo::getReport($this->LMRId);
        }
        return $this->assetInfo;
    }

    public function getFileAdminInfo(): ?tblFileAdminInfo
    {
        if (is_null($this->fileAdminInfo) && $this->LMRId) {
            $this->fileAdminInfo = tblFileAdminInfo::Get(['LMRId' => $this->LMRId]);
        }
        return $this->fileAdminInfo;
    }

    public function getFileHMLONewLoanInfo(): ?tblFileHMLONewLoanInfo
    {
        if (is_null($this->fileHMLONewLoanInfo) && $this->LMRId) {
            $this->fileHMLONewLoanInfo = tblFileHMLONewLoanInfo::Get(['fileID' => $this->LMRId]);
        }
        return $this->fileHMLONewLoanInfo;
    }

    public function fileMFLoanTermsSummary(): ?fileMFLoanTermsSummary
    {
        if (is_null($this->fileMFLoanTermsSummary) && $this->LMRId) {
            $this->fileMFLoanTermsSummary = fileMFLoanTermsSummary::getReport($this->LMRId);
        }
        return $this->fileMFLoanTermsSummary;
    }

    public function netOperatingIncome(): ?netOperatingIncome
    {
        if (is_null($this->netOperatingIncome) && $this->LMRId) {
            $this->netOperatingIncome = netOperatingIncome::getReport($this->LMRId);
        }
        return $this->netOperatingIncome;
    }

    public function ChecklistFlatNotesByName(): ?array
    {
        if (is_null($this->ChecklistFlatNotesByName) && $this->LMRId) {
            $res = ChecklistFlatNotes::getReport($this->LMRId);
            $this->ChecklistFlatNotesByName = [];
            foreach ($res as $item) {
                if (!$item->notesType || $item->notesType == 'checklist') {
                    $item->notesType = glFUModulesNotesTypeArray::GENERAL;
                }
                $this->ChecklistFlatNotesByName[$item->docName][$item->notesType][] = $item;
            }
        }
        return $this->ChecklistFlatNotesByName;
    }

    private ?int $addedToFav = null;

    public function addedToFav(): ?int
    {
        if (is_null($this->addedToFav) && $this->LMRId) {
            $this->addedToFav = addedToFav::getReport($this->LMRId);
        }
        return $this->addedToFav;
    }

    private ?tblFileResponse $ResponseInfo = null;

    public function ResponseInfo(): ?tblFileResponse
    {
        if (is_null($this->ResponseInfo) && $this->LMRId) {
            $this->ResponseInfo = tblFileResponse::Get(['LMRId' => $this->LMRId]);
        }
        return $this->ResponseInfo;
    }

    /* @var fileSubstatusInfo[] $fileSubstatusInfo */
    private ?array $fileSubstatusInfo = null;

    /**
     * @param branchModuleInfo[] $branchModules
     * @return fileSubstatusInfo[]
     */
    public function fileSubstatusInfo(array $branchModules): ?array
    {
        if (is_null($this->fileSubstatusInfo) && $this->LMRId) {
            $fileSelectedModuleCode = [];
            foreach ($branchModules as $module) {
                $fileSelectedModuleCode[] = $module->moduleCode;
            }
            $this->fileSubstatusInfo = fileSubstatusInfo::getObjects('', $this->LMRId, $fileSelectedModuleCode);
        }
        return $this->fileSubstatusInfo;
    }

    private ?array $getNotes = null;

    /**
     * @param int $viewPrivateNotes
     * @param int $viewPublicNotes
     * @return array[]
     */
    public function getNotes(
        int $viewPrivateNotes,
        int $viewPublicNotes
    ): ?array
    {
        if (is_null($this->getNotes)) {
            $this->getNotes = NotesInfo::getReport(
                $viewPrivateNotes,
                $viewPublicNotes,
                $this->LMRId,
                0,
                [],
                0,
                'NOTES',
                false
            ); // refactored
        }

        return $this->getNotes;
    }

    /**
     * @param int|null $viewPrivateNotes
     * @param int|null $viewPublicNotes
     * @return array
     */
    public function processorComments(
        ?int $viewPrivateNotes,
        ?int $viewPublicNotes
    ): array
    {
        return $this->getNotes(
            $viewPrivateNotes ?? 0,
                $viewPublicNotes ?? 0
        )['processorCommentsArray'] ?? [];
    }

    /**
     * @param int|null $viewPrivateNotes
     * @param int|null $viewPublicNotes
     * @return array
     */
    public function notesEmpInfo(
        ?int $viewPrivateNotes,
        ?int $viewPublicNotes
    ): array
    {
        return $this->getNotes(
            $viewPrivateNotes ?? 0,
            $viewPublicNotes ?? 0
        )['notesEmpInfo'] ?? [];
    }

    /**
     * @param int|null $viewPrivateNotes
     * @param int|null $viewPublicNotes
     * @return array
     */
    public function notesAgentInfo(
        ?int $viewPrivateNotes,
        ?int $viewPublicNotes
    ): array
    {
        return $this->getNotes(
            $viewPrivateNotes ?? 0,
            $viewPublicNotes ?? 0
        )['notesAgentInfo'] ?? [];
    }

    /**
     * @param int $viewPrivateNotes
     * @param int $viewPublicNotes
     * @return array
     */
    public function notesBranchInfo(
        ?int $viewPrivateNotes,
        ?int $viewPublicNotes
    ): array
    {
        return $this->getNotes(
            $viewPrivateNotes ?? 0,
            $viewPublicNotes ?? 0
        )['notesBranchInfo'] ?? [];
    }

    /**
     * @param int|null $viewPrivateNotes
     * @param int|null $viewPublicNotes
     * @return array
     */
    public function notesClientInfo(
        ?int $viewPrivateNotes,
        ?int $viewPublicNotes
    ): array
    {
        return $this->getNotes(
            $viewPrivateNotes ?? 0,
            $viewPublicNotes ?? 0
        )['notesClientInfo'] ?? [];
    }

    private ?int $BLID = null;

    public function BLID(array $ClientTypes): ?int
    {
        if (!sizeof($ClientTypes)) {
            return null;
        }

        if (is_null($this->BLID)) {
            $params = [];
            $params['PCID'] = $this->tblFile()->FPCID;
            foreach ($ClientTypes as $i => $item) {
                $params['loanPgm' . $i] = $item;
            }


            $qry = "SELECT BLID 
                FROM tblPCHMLOBasicLoanPgmInfo 
            WHERE loanPgm IN (" . Database2::GetPlaceholders(sizeof($ClientTypes), ':loanPgm', true) . ")
				AND PCID = :PCID 
				LIMIT 1
				;";
            $row = Database2::getInstance()->queryData($qry, $params)[0] ?? [];
            $this->BLID = intval($row['BLID'] ?? 0);
        }
        return $this->BLID;
    }

    /* @var tblPCHMLOBasicLoanPropertyType[] $HMLOPCPropertyType */
    private ?array $HMLOPCPropertyType = null;

    public function HMLOPCPropertyType(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCPropertyType)) {
            $this->HMLOPCPropertyType = tblPCHMLOBasicLoanPropertyType::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCPropertyType;
    }

    /* @var tblPCHMLOBasicLoanPropertyType[] HMLOPCLoanTerm */
    private ?array $HMLOPCLoanTerm = null;

    public function HMLOPCLoanTerm(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCLoanTerm)) {
            $this->HMLOPCLoanTerm = tblPCHMLOBasicLoanTermInfo::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCLoanTerm;
    }

    /* @var tblPCHMLOBasicLoanOccupancy[] HMLOPCOccupancy */
    private ?array $HMLOPCOccupancy = null;

    public function HMLOPCOccupancy(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCOccupancy)) {
            $this->HMLOPCOccupancy = tblPCHMLOBasicLoanOccupancy::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCOccupancy;
    }

    /* @var State[] HMLOPCState */
    private ?array $HMLOPCState = null;

    public function HMLOPCState(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCState)) {
            $sql = '
SELECT t1.stateCode
     , t2.stateName 
FROM tblPCHMLOBasicLoanStateInfo t1, tblStates t2 
WHERE t1.stateCode = t2.stateCode 
  AND BLID = :BLID';
            $this->HMLOPCState = Database2::getInstance()->queryData($sql, [
                'BLID' => $this->BLID($ClientTypes),
            ], function ($row) {
                return new State($row);
            });
        }
        return $this->HMLOPCState;
    }

    private ?tblPCHMLOBasicLoanInfo $HMLOPCBasicLoanInfo = null;

    public function HMLOPCBasicLoanInfo(array $ClientTypes): ?tblPCHMLOBasicLoanInfo
    {
        if (is_null($this->HMLOPCBasicLoanInfo)) {
            $this->HMLOPCBasicLoanInfo = tblPCHMLOBasicLoanInfo::Get(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicLoanInfo;
    }

    private ?tblFileCFPB $CFPBInfo = null;

    public function CFPBInfo(): ?tblFileCFPB
    {
        if (is_null($this->CFPBInfo)) {
            $this->CFPBInfo = tblFileCFPB::Get(['LMRId' => $this->LMRId, 'activeStatus' => 1]);
        }
        return $this->CFPBInfo;
    }

    private ?tblQAInfo $QAInfo = null;

    public function QAInfo(): ?tblQAInfo
    {
        // select * from tblQAInfo where LMRId = :LMRId
        if (is_null($this->QAInfo)) {
            $this->QAInfo = tblQAInfo::Get(['LMRId' => $this->LMRId]);
        }
        return $this->QAInfo;
    }

    /* @var PCStatusInfo[] $PCStatusInfo */
    private ?array $PCStatusInfo = null;

    /**
     * @param branchModuleInfo[] $branchModules
     * @return PCStatusInfo[]|null
     */
    public function PCStatusInfo(array $branchModules): ?array
    {
        // select * from tblQAInfo where LMRId = :LMRId
        if (is_null($this->PCStatusInfo)) {
            $fileSelectedModuleCode = [];
            foreach ($branchModules as $module) {
                $fileSelectedModuleCode[] = $module->moduleCode;
            }
            $res = PCStatusInfo::getReport('', $this->tblFile()->FPCID, $fileSelectedModuleCode);
            $this->PCStatusInfo = [];
            foreach ($res as $row) {
                $this->PCStatusInfo[] = new PCStatusInfo($row);
            }
        }
        return $this->PCStatusInfo;
    }

    /* @var PCSubStatusInfo[] */
    private ?array $PCSubStatusInfo = null;

    /**
     * @param branchModuleInfo[] $branchModules
     * @return PCSubStatusInfo[]|null
     */
    public function PCSubStatusInfo(array $branchModules): ?array
    {
        if (is_null($this->PCSubStatusInfo)) {
            $fileSelectedModuleCode = [];
            foreach ($branchModules as $module) {
                $fileSelectedModuleCode[] = $module->moduleCode;
            }
            $res = PCSubStatusInfo::getReport('', $this->tblFile()->FPCID, $fileSelectedModuleCode); // refactored
            $this->PCSubStatusInfo = [];
            foreach ($res as $row) {
                $this->PCSubStatusInfo[] = new PCSubStatusInfo($row);
            }
        }

        return $this->PCSubStatusInfo;
    }

    /* @var BillingPaymentInfo[][] $BillingPaymentInfo */
    private ?array $BillingPaymentInfo = null;

    public function BillingPaymentInfo(): ?array
    {
        if (is_null($this->BillingPaymentInfo)) {
            $res = BillingPaymentInfo::getReport('', $this->LMRId);
            foreach ($res as $phase => $row) {
                $this->BillingPaymentInfo[$phase] = new BillingPaymentInfo($row);
            }
        }
        return $this->BillingPaymentInfo;
    }

    /* @var BillingFeeInfo[][] $BillingFeeInfo */
    private ?array $BillingFeeInfo = null;

    /**
     * @return BillingFeeInfo[][]|null
     */
    public function BillingFeeInfo(): ?array
    {
        if (is_null($this->BillingFeeInfo)) {
            $res = BillingFeeInfo::getReport('', $this->LMRId);
            foreach ($res as $feeCode => $rows) {
                foreach ($rows as $row) {
                    $this->BillingFeeInfo[$feeCode][] = new BillingFeeInfo($row);
                }
            }
        }
        return $this->BillingFeeInfo;
    }

    /* @var tblFileMFLoanTerms[] $MFLoanTermsInfo */
    private ?array $MFLoanTermsInfo = null;

    /**
     * @return tblFileMFLoanTerms[]
     */
    public function MFLoanTermsInfo(): array
    {
        if (is_null($this->MFLoanTermsInfo)) {
            // SELECT * FROM tblFileMFLoanTerms WHERE fileID = :fileID AND activeStatus = 1
            $this->MFLoanTermsInfo = tblFileMFLoanTerms::GetAll(['fileID' => $this->LMRId, 'activeStatus' => 1]);
        }
        return $this->MFLoanTermsInfo;
    }

    /* @var WFIDs[] $WFIDs */
    private ?array $WFIDs = null;

    /**
     * @return WFIDs[]|null
     */
    public function WFIDs(): ?array
    {
        if (is_null($this->WFIDs)) {
            $this->WFIDs = WFIDs::getReport($this->LMRId, $this->LMRInfo()->FPCID);
        }
        return $this->WFIDs;
    }

    /* @var PCClientWFServiceType[] PCClientWFServiceType */
    private ?array $PCClientWFServiceType = null;

    /**
     * @param array $branchModules
     * @return PCClientWFServiceType[]|null
     */
    public function PCClientWFServiceType(array $branchModules): ?array
    {
        if (is_null($this->PCClientWFServiceType)) {
            $fileSelectedModuleCode = [];
            foreach ($branchModules as $module) {
                $fileSelectedModuleCode[] = $module->moduleCode;
            }
            $this->PCClientWFServiceType = PCClientWFServiceType::getReport($this->WFIDs(), $fileSelectedModuleCode);
        }
        return $this->PCClientWFServiceType;

    }

    public function HUDItemsPayableLoan(): array
    {
        if (is_null($this->HUDItemsPayableLoan)) {
            $this->HUDItemsPayableLoan = tblLMRHUDItemsPayableLoan::GetAll(['LMRId' => $this->LMRId]) ?? new tblLMRHUDItemsPayableLoan();
        }
        return $this->HUDItemsPayableLoan;
    }

    public function getLoanPropertySummary(): tblLoanPropertySummary
    {
        if (is_null($this->loanPropertySummary)) {
            $this->loanPropertySummary = tblLoanPropertySummary::Get(['LMRId' => $this->LMRId]) ?? new tblLoanPropertySummary();
        }
        return $this->loanPropertySummary;
    }

    public function getFileCalculatedValues(): tblFileCalculatedValues
    {
        if (is_null($this->fileCalculatedValues)) {
            $this->fileCalculatedValues = tblFileCalculatedValues::Get(['LMRId' => $this->LMRId]) ?? new tblFileCalculatedValues();
        }
        return $this->fileCalculatedValues;
    }

    /**
     * @return tblFileHUDBasicInfo
     */
    public function getFileHUDBasicInfo(): tblFileHUDBasicInfo
    {
        if (is_null($this->fileHUDBasicInfo)) {
            $this->fileHUDBasicInfo = tblFileHUDBasicInfo::Get(['LMRId' => $this->LMRId]) ?? new tblFileHUDBasicInfo();
        }
        return $this->fileHUDBasicInfo;
    }

    public function fileResponse(): ?tblFileResponse
    {
        if (is_null($this->fileResponse)) {
            $this->fileResponse = tblFileResponse::Get([
                'LMRId' => $this->LMRId,
            ]);
        }
        return $this->fileResponse;
    }

    public function creditDecisionClearToCloseBy(): ?string
    {
        if (is_null($this->creditDecisionClearToCloseBy)) {
            $sql = '
                SELECT CONCAT(tblAdminUsers.processorName,\' \',tblAdminUsers.processorLName) as clearToCloseBy 
                FROM tblCreditDecisionForm 
                INNER JOIN tblAdminUsers ON tblAdminUsers.AID = tblCreditDecisionForm.clearToCloseBy
                WHERE LMRId = @LMRId
            ';
            $res = Database2::getInstance()->queryData($sql, ['LMRId' => $this->LMRId]);
            $this->creditDecisionClearToCloseBy = $res[0]['clearToCloseBy'] ?? null;
        }
        return $this->creditDecisionClearToCloseBy;
    }

    public function requiredInsuranceInfo(?string $proInsType): ?string
    {
        $proInsTypeArray = explode(',', $proInsType) ?? [];
        $sqlParams = [];
        if (is_null($this->requiredInsuranceInfo)) {
            $sql = '
                SELECT group_concat(insuranceName SEPARATOR ", ") as insuranceType
                FROM tblInsurance_types 
                WHERE id IN ( ' . Database2::GetPlaceholders(sizeof($proInsTypeArray), ':id', true) . ' )
            ';
            foreach ($proInsTypeArray as $i => $paVal) {
                $sqlParams['id' . $i] = trim($paVal);
            }
            $res = Database2::getInstance()->queryData($sql, $sqlParams);
            $this->requiredInsuranceInfo = $res[0]['insuranceType'] ?? null;
        }
        return $this->requiredInsuranceInfo;
    }

    public function getAdverseAction(): ?tblAdverseAction
    {
        if (is_null($this->adverseActionInfo)) {
            $this->adverseActionInfo = tblAdverseAction::Get(['LMRId' => $this->LMRId]);
        }
        return $this->adverseActionInfo;
    }

    public function getFileHMLOExperience(): ?tblFileHMLOExperience
    {
        if (is_null($this->fileHMLOExperienceInfo)) {
            $this->fileHMLOExperienceInfo = tblFileHMLOExperience::Get(['fileID' => $this->LMRId]);
        }
        return $this->fileHMLOExperienceInfo;
    }

    private ?NotesInfo $NotesInfo = null;

    public function NotesInfo(
        int  $viewPrivateNotes,
        int  $viewPublicNotes,
        int  $public = 0,
        ?int $limit = null
    ): ?NotesInfo
    {
        if (is_null($this->NotesInfo)) {
            $this->NotesInfo = NotesInfo::getObjects(
                $viewPrivateNotes,
                $viewPublicNotes,
                $this->LMRId,
                $public,
                'NOTES',
                $limit
            );
        }
        return $this->NotesInfo;
    }

    /**
     * @return tblCustomFieldValue[]|null
     */
    public function customFields(): ?array
    {
        if (is_null($this->customFields)) {
            $items = tblCustomFieldValue::GetAll([
                'objectType' => 'models\lendingwise\tblFile',
                'primaryKey' => $this->LMRId,
            ]);
            foreach ($items as $item) {
                $this->customFields[$item->tblCustomFieldId] = $item;
            }
        }
        return $this->customFields;
    }

    public function customField(int $id): ?tblCustomFieldValue
    {
        return $this->customFields() ? ($this->customFields[$id] ?? null) : null;
    }

    /**
     * @return tblCreditMemo[]
     */
    public function creditMemoArray(): array
    {
        if (is_null($this->creditMemoArray) && $this->LMRId) {
            $this->creditMemoArray = creditMemoArray::getObjects($this->LMRId);
        }
        return $this->creditMemoArray;
    }

    public function adverseActionInfo(): ?tblAdverseAction
    {
        if (is_null($this->adverseActionInfo) && $this->LMRId) {
            $this->adverseActionInfo = adverseActionInfo::getObject($this->LMRId);
        }
        return $this->adverseActionInfo;

    }

    public function AssetsInfo(): ?tblAssetsInfo
    {
        if (is_null($this->AssetsInfo) && $this->LMRId) {
            $this->AssetsInfo = tblAssetsInfo::Get(['LMRId' => $this->LMRId]);
        }
        return $this->AssetsInfo;
    }

    public function fileLOAssetsInfo(): ?tblFileLOAssetsInfo
    {
        if (is_null($this->fileLOAssetsInfo) && $this->LMRId) {
            $this->fileLOAssetsInfo = tblFileLOAssetsInfo::Get(['fileID' => $this->LMRId]);
        }
        return $this->fileLOAssetsInfo;
    }

    /* @var tblFileLOBorEmploymentInfo[] borEmploymentInfo */
    private ?array $borEmploymentInfo = null;

    /**
     * @return tblFileLOBorEmploymentInfo[]|null
     */
    public function borEmploymentInfo(): ?array
    {
        if (is_null($this->borEmploymentInfo) && $this->LMRId) {
            $this->borEmploymentInfo = tblFileLOBorEmploymentInfo::GetAll(['fileID' => $this->LMRId]);
        }
        return $this->borEmploymentInfo;
    }

    /* @var tblFileLOCoBEmploymentInfo[] _borEmploymentInfo */
    private ?array $coBEmploymentInfo = null;

    /**
     * @return tblFileLOCoBEmploymentInfo[]|null
     */
    public function coBEmploymentInfo(): ?array
    {
        if (is_null($this->coBEmploymentInfo) && $this->LMRId) {
            $this->coBEmploymentInfo = tblFileLOCoBEmploymentInfo::GetAll([
                tblFileLOCoBEmploymentInfo_db::COLUMN_FILEID => $this->LMRId,
            ]);
        }
        return $this->coBEmploymentInfo;
    }

    /* @var tblFileLOChekingSavingInfo[] $_fileLOChekingSavingInfo */
    private ?array $_fileLOChekingSavingInfo = null;

    public function fileLOChekingSavingInfo(): ?array
    {
        if (is_null($this->_fileLOChekingSavingInfo) && $this->LMRId) {
            $this->_fileLOChekingSavingInfo = tblFileLOChekingSavingInfo::GetAll(['fileID' => $this->LMRId]);
        }
        return $this->_fileLOChekingSavingInfo;
    }

    /* @var tblFileLOLiabilitiesInfo[] $_liabilitiesInfo */
    private ?array $_liabilitiesInfo = null;

    public function liabilitiesInfo(): ?array
    {
        if (is_null($this->_liabilitiesInfo) && $this->LMRId) {
            $this->_liabilitiesInfo = tblFileLOLiabilitiesInfo::GetAll(['fileID' => $this->LMRId]);
        }
        return $this->_liabilitiesInfo;
    }

    private ?tblListingPageData $_lpArray = null;

    public function lpArray(): ?tblListingPageData
    {
        if (is_null($this->_lpArray) && $this->LMRId) {
            $this->_lpArray = lpArray::getObjects($this->LMRId);
        }
        return $this->_lpArray;
    }

    private ?tblFileSBAQuestions $_SBABackground = null;

    public function SBABackground(): ?tblFileSBAQuestions
    {
        if (is_null($this->_SBABackground)) {
            $this->_SBABackground = tblFileSBAQuestions::Get(['fileID' => $this->LMRId]);
        }
        return $this->_SBABackground;
    }

    /* @var tblOffers[] $_offerArray */
    public ?array $_offerArray = null;

    /**
     * @return tblOffers[]|null
     */
    public function offerArray(): ?array
    {
        if (is_null($this->_offerArray)) {
            $this->_offerArray = tblOffers::GetAll([
                tblOffers_db::COLUMN_LMRID        => $this->LMRId,
                tblOffers_db::COLUMN_ACTIVESTATUS => 1
            ], [
                tblOffers_db::COLUMN_ID => 'desc'
            ]);
        }
        return $this->_offerArray;
    }

    public function listingPageArray(): ?tblListingPageData
    {
        return $this->lpArray();
    }

    private ?HMLOPCReport $_HMLOPCReport = null;

    private function HMLOPCReport(?int $PCID = null): HMLOPCReport
    {
        if (is_null($this->_HMLOPCReport)) {
            $this->_HMLOPCReport = HMLOPCReport::getObjects(
                $this->getFileModuleCodes(),
                $PCID ?: $this->tblFile()->FPCID,
                $this->ClientTypes()
            );
        }
        return $this->_HMLOPCReport;
    }

    /* @var tblPCHMLOBasicLoanTransactionType[] $HMLOPCTransactionType */
    private ?array $HMLOPCTransactionType = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanTransactionType[]|null
     */
    public function HMLOPCTransactionType(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCTransactionType)) {
            $this->HMLOPCTransactionType = tblPCHMLOBasicLoanTransactionType::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCTransactionType;
    }

    /* @var tblPCHMLOBasicLoanEntityType[] $HMLOPCBasicEntityType */
    private ?array $HMLOPCBasicEntityType = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanEntityType[]|null
     */
    public function HMLOPCBasicEntityType(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicEntityType)) {
            $this->HMLOPCBasicEntityType = tblPCHMLOBasicLoanEntityType::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicEntityType;
    }

    /* @var tblPCHMLOBasicLoanExtensionOption[] $HMLOPCExtnOption */
    private ?array $HMLOPCExtnOption = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanExtensionOption[]|null
     */
    public function HMLOPCExtnOption(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCExtnOption)) {
            $this->HMLOPCExtnOption = tblPCHMLOBasicLoanExtensionOption::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCExtnOption;
    }

    /* @var tblPCHMLOBasicLoanNichesInfo[] $HMLOPCNiches */
    private ?array $HMLOPCNiches = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanNichesInfo[]|null
     */
    public function HMLOPCNiches(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCNiches)) {
            $this->HMLOPCNiches = tblPCHMLOBasicLoanNichesInfo::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCNiches;
    }

    /* @var tblPCHMLOBasicLoanNichesInfo[] $PCBasicLoanTabFileIdExists */
    private ?array $PCBasicLoanTabFileIdExists = null;

    /**
     * @return array|null
     */
    public function PCBasicLoanTabFileIdExists(): ?array
    {
        if (is_null($this->PCBasicLoanTabFileIdExists) && $this->LMRId) {
            $this->PCBasicLoanTabFileIdExists = PCBasicLoanTabFileIdExists::getReport('MP', $this->LMRId);
        }
        return $this->PCBasicLoanTabFileIdExists;
    }

    /* @var tblPCHMLOBasicLoanAmortizationInfo[] $HMLOPCAmortizationValInfo */
    private ?array $HMLOPCAmortizationValInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanAmortizationInfo[]|null
     */
    public function HMLOPCAmortizationValInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCAmortizationValInfo)) {
            $this->HMLOPCAmortizationValInfo = tblPCHMLOBasicLoanAmortizationInfo::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCAmortizationValInfo;
    }

    /* @var tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy[] $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo */
    private ?array $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy[]|null
     */
    public function HMLOPCBasicMinSeasoningPersonalBankruptcyInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicMinSeasoningPersonalBankruptcyInfo)) {
            $this->HMLOPCBasicMinSeasoningPersonalBankruptcyInfo = tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicMinSeasoningPersonalBankruptcyInfo;
    }


    /* @var tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy[] $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo */
    private ?array $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy[]|null
     */
    public function HMLOPCBasicMinSeasoningBusinessBankruptcyInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicMinSeasoningBusinessBankruptcyInfo)) {
            $this->HMLOPCBasicMinSeasoningBusinessBankruptcyInfo = tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicMinSeasoningBusinessBankruptcyInfo;
    }

    /* @var tblPCHMLOBasicLoanMinSeasoningForeclosure[] $HMLOPCBasicMinSeasoningForeclosureInfo */
    private ?array $HMLOPCBasicMinSeasoningForeclosureInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanMinSeasoningForeclosure[]|null
     */
    public function HMLOPCBasicMinSeasoningForeclosureInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicMinSeasoningForeclosureInfo)) {
            $this->HMLOPCBasicMinSeasoningForeclosureInfo = tblPCHMLOBasicLoanMinSeasoningForeclosure::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicMinSeasoningForeclosureInfo;
    }

    /* @var tblPCHMLOBasicLoanSBALoanProduct[] $HMLOPCBasicSBALoanProductInfo */
    private ?array $HMLOPCBasicSBALoanProductInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanSBALoanProduct[]|null
     */
    public function HMLOPCBasicSBALoanProductInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicSBALoanProductInfo)) {
            $this->HMLOPCBasicSBALoanProductInfo = tblPCHMLOBasicLoanSBALoanProduct::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicSBALoanProductInfo;
    }

    /* @var tblPCHMLOBasicLoanEquipmentType[] $HMLOPCTransactionType */
    private ?array $HMLOPCBasicEquipmentTypeInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanEquipmentType[]|null
     */
    public function HMLOPCBasicEquipmentTypeInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicEquipmentTypeInfo)) {
            $this->HMLOPCBasicEquipmentTypeInfo = tblPCHMLOBasicLoanEquipmentType::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicEquipmentTypeInfo;
    }

    /* @var tblPCHMLOBasicLoanEntityStateOfFormation[] $HMLOPCBasicEntitityStateFormationInfo */
    private ?array $HMLOPCBasicEntitityStateFormationInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanEntityStateOfFormation[]|null
     */
    public function HMLOPCBasicEntitityStateFormationInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicEntitityStateFormationInfo)) {
            $this->HMLOPCBasicEntitityStateFormationInfo = tblPCHMLOBasicLoanEntityStateOfFormation::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicEntitityStateFormationInfo;
    }

    /* @var tblPCHMLOBasicLoanPaymentFrequency[] $HMLOPCBasicPaymentFrequencyInfo */
    private ?array $HMLOPCBasicPaymentFrequencyInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanPaymentFrequency[]|null
     */
    public function HMLOPCBasicPaymentFrequencyInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicPaymentFrequencyInfo)) {
            $this->HMLOPCBasicPaymentFrequencyInfo = tblPCHMLOBasicLoanPaymentFrequency::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicPaymentFrequencyInfo;
    }

    /* @var tblPCHMLOBasicLoanLoanPurpose[] $HMLOPCBasicLoanPurposeInfo */
    private ?array $HMLOPCBasicLoanPurposeInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanLoanPurpose[]|null
     */
    public function HMLOPCBasicLoanPurposeInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicLoanPurposeInfo)) {
            $this->HMLOPCBasicLoanPurposeInfo = tblPCHMLOBasicLoanLoanPurpose::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicLoanPurposeInfo;
    }

    /* @var tblPCHMLOBasicLoanMinTimeInBusiness[] $HMLOPCBasicMinTimeInBusinessInfo */
    private ?array $HMLOPCBasicMinTimeInBusinessInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanMinTimeInBusiness[]|null
     */
    public function HMLOPCBasicMinTimeInBusinessInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicMinTimeInBusinessInfo)) {
            $this->HMLOPCBasicMinTimeInBusinessInfo = tblPCHMLOBasicLoanMinTimeInBusiness::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicMinTimeInBusinessInfo;
    }

    /* @var tblPCHMLOBasicLoanRateLockPeriod[] $HMLOPCBasicRateLockPeriodInfo */
    private ?array $HMLOPCBasicRateLockPeriodInfo = null;

    /**
     * @param array $ClientTypes
     * @return tblPCHMLOBasicLoanRateLockPeriod[]|null
     */
    public function HMLOPCBasicRateLockPeriodInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicRateLockPeriodInfo)) {
            $this->HMLOPCBasicRateLockPeriodInfo = tblPCHMLOBasicLoanRateLockPeriod::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicRateLockPeriodInfo;
    }

    private ?array $HMLOPCBasicLoanExitStrategyInfo = null;

    public function HMLOPCBasicLoanExitStrategyInfo(array $ClientTypes): ?array
    {
        if (is_null($this->HMLOPCBasicLoanExitStrategyInfo)) {
            $this->HMLOPCBasicLoanExitStrategyInfo = tblPCHMLOBasicLoanExitStrategy::GetAll(['BLID' => $this->BLID($ClientTypes)]);
        }
        return $this->HMLOPCBasicLoanExitStrategyInfo;
    }

    public function fileHMLONewLoanInfo(): ?tblFileHMLONewLoanInfo
    {
        return $this->getFileHMLONewLoanInfo();
    }

    private ?tblFilePropertyInfo $FilePropInfo = null;

    public function FilePropInfo(): ?tblFilePropertyInfo
    {
        if (is_null($this->FilePropInfo)) {
            $this->FilePropInfo = tblFilePropertyInfo::Get(['LMRId' => $this->LMRId]);
        }
        return $this->FilePropInfo;
    }

    private ?tblClient $clientInfo = null;

    public function clientInfo(): ?tblClient
    {
        if (is_null($this->clientInfo)) {
            $this->clientInfo = tblClient::Get(['CID' => $this->LMRInfo()->clientId]);
        }
        return $this->clientInfo;
    }

    private ?tblFileHMLOAssetsInfo $fileHMLOAssetsInfo = null;

    public function fileHMLOAssetsInfo(): ?tblFileHMLOAssetsInfo
    {
        if (is_null($this->fileHMLOAssetsInfo) && $this->LMRId) {
            $this->fileHMLOAssetsInfo = tblFileHMLOAssetsInfo::Get([
                tblFileHMLOAssetsInfo_db::COLUMN_FILEID => $this->LMRId,
            ]); // refactored
        }
        return $this->fileHMLOAssetsInfo;
    }

    /* @var tblFileExpFilpGroundUp[][] $fileExpFilpGroundUp */
    private ?array $fileExpFilpGroundUp = null;

    /**
     * @return tblFileExpFilpGroundUp[][]
     */
    public function fileExpFilpGroundUp(): ?array
    {
        if (is_null($this->fileExpFilpGroundUp) && $this->LMRId) {
            $this->fileExpFilpGroundUp = fileExpFilpGroundUp::getObjects($this->LMRId); // refactored
        }
        return $this->fileExpFilpGroundUp;
    }

    /* @var tblClientDocs[] $clientDocsArray */
    private ?array $clientDocsArray = null;

    /**
     * @return tblClientDocs[]
     */
    public function clientDocsArray(): ?array
    {
        if (is_null($this->clientDocsArray) && $this->LMRInfo()) {
            $this->clientDocsArray = clientDocsArray::getObjects($this->LMRInfo()->clientId); // refactored
        }
        return $this->clientDocsArray;
    }

    private ?tblPropSellerInfo $sellerInfo = null;

    public function sellerInfo(): ?tblPropSellerInfo
    {
        if (is_null($this->sellerInfo) && $this->LMRId) {
            $this->sellerInfo = tblPropSellerInfo::Get(['LMRID' => $this->LMRId]);
        }
        return $this->sellerInfo;
    }


    /* @var tblMemberOfficerInfo[] $fileMemberOfficerInfo */
    private ?array $fileMemberOfficerInfo = null;

    /**
     * @return tblMemberOfficerInfo[]
     */
    public function fileMemberOfficerInfo(): array
    {
        if (is_null($this->fileMemberOfficerInfo) && $this->LMRId) {
            $this->fileMemberOfficerInfo = tblMemberOfficerInfo::GetAll([
                tblMemberOfficerInfo_db::COLUMN_LMRID => $this->LMRId,
            ]);
        }
        return $this->fileMemberOfficerInfo;
    }

    private ?composite\oFile\getFileInfo\relatedDocs $relatedDocsByType = null;

    /**
     * @return composite\oFile\getFileInfo\relatedDocs|null
     */
    private function relatedDocsByType(): ?composite\oFile\getFileInfo\relatedDocs
    {
        if (is_null($this->relatedDocsByType)) {
            $this->relatedDocsByType = composite\oFile\getFileInfo\relatedDocs::getObjects($this->LMRId);
        }
        return $this->relatedDocsByType;
    }

    /**
     * @return relatedDocs[]|null
     */
    public function propertyValuation(): ?array
    {
        return $this->relatedDocsByType()->propertyValuationDocInfo;
    }

    /* @var propertyValuationDocs[] $propertyValuationDocs */
    private ?array $propertyValuationDocs = null;

    /**
     * @return propertyValuationDocs[]
     */
    public function propertyValuationDocs(): array
    {
        if (is_null($this->propertyValuationDocs)) {
            $this->propertyValuationDocs = propertyValuationDocs::getObjects($this->LMRId); // refactored
        }
        return $this->propertyValuationDocs;
    }

    /* @var tblFileLOScheduleRealInfo[] $fileLOScheduleRealInfo */
    private ?array $fileLOScheduleRealInfo = null;

    /**
     * @return tblFileLOScheduleRealInfo[]|null
     */
    public function fileLOScheduleRealInfo(): ?array
    {
        if (is_null($this->fileLOScheduleRealInfo) && $this->LMRId) {
            $this->fileLOScheduleRealInfo = tblFileLOScheduleRealInfo::GetAll([
                tblFileLOScheduleRealInfo_db::COLUMN_FILEID => $this->LMRId,
            ]);
        }
        return $this->fileLOScheduleRealInfo;
    }

    /* @var propertyCountyInfo[] $propertyCountyInfo */
    private ?array $propertyCountyInfo = null;

    /**
     * @return propertyCountyInfo[]
     */
    public function propertyCountyInfo(): array
    {
        if (is_null($this->propertyCountyInfo) && $this->getPrimaryProperty()) {
            $this->propertyCountyInfo = propertyCountyInfo::getObjects($this->getPrimaryProperty()->propertyState); // refactored
        }
        return $this->propertyCountyInfo;
    }

    /* @var fileBudgetAndDrawDoc[][] $fileBudgetAndDrawDoc */
    private ?array $fileBudgetAndDrawDoc = null;

    /**
     * @return fileBudgetAndDrawDoc[][]
     */
    public function fileBudgetAndDrawDoc(): array
    {
        if (is_null($this->fileBudgetAndDrawDoc) && $this->LMRId) {
            $this->fileBudgetAndDrawDoc = fileBudgetAndDrawDoc::getObjects($this->LMRId); // refactored
        }
        return $this->fileBudgetAndDrawDoc;
    }

    private ?estimatedProjectCost $estimatedProjectCost = null;

    public function estimatedProjectCost(): ?estimatedProjectCost
    {
        if (is_null($this->estimatedProjectCost) && $this->LMRId) {
            $this->estimatedProjectCost = estimatedProjectCost::Get([
                estimatedProjectCost_db::COLUMN_LMRID => $this->LMRId,
            ]);
        }
        return $this->estimatedProjectCost;
    }

    /* @var creditorInfoStatus[] $creditorInfoStatus */
    private ?array $creditorInfoStatus = null;

    /**
     * @return creditorInfoStatus[]
     */
    public function creditorInfoStatus(): array
    {
        if (is_null($this->creditorInfoStatus)) {
            $this->creditorInfoStatus = creditorInfoStatus::getObjects($this->LMRId); // refactored
        }
        return $this->creditorInfoStatus;
    }

    private ?collateral $collateralArray = null;

    /**
     * @return collateral
     */
    public function collateralArray(): ?collateral
    {
        if (is_null($this->collateralArray)) {
            $this->collateralArray = collateral::Get([
                collateral_db::COLUMN_LMRID => $this->LMRId,
            ]); // refactored
        }
        return $this->collateralArray;
    }

    /* @var collateralArray[] collateralValuesArray */
    private ?array $collateralValuesArray = null;

    /**
     * @return collateralArray[]
     */
    public function collateralValuesArray(): array
    {
        if (is_null($this->collateralValuesArray) && $this->LMRId) {
            $this->collateralValuesArray = collateralArray::getCollateralValuesObjects($this->LMRId); // refactored
        }
        return $this->collateralValuesArray;
    }

    private ?tblPropertyManagement $propMgmtArray = null;

    public function propMgmtArray(): ?tblPropertyManagement
    {
        if (is_null($this->propMgmtArray) && $this->LMRId) {
            $this->propMgmtArray = tblPropertyManagement::Get([
                tblPropertyManagement_db::COLUMN_LMRID => $this->LMRId,
            ]); // refactored
        }
        return $this->propMgmtArray;
    }

    /* @var tblPropertyManagementDocs[] $propMgmtDocsArray */
    private ?array $propMgmtDocsArray = null;

    /**
     * @return tblPropertyManagementDocs[]
     */
    public function propMgmtDocsArray(): array
    {
        if (is_null($this->propMgmtDocsArray) && $this->LMRId) {
            $this->propMgmtDocsArray = tblPropertyManagementDocs::GetAll([
                tblPropertyManagementDocs_db::COLUMN_LMRID        => $this->LMRId,
                tblPropertyManagementDocs_db::COLUMN_ACTIVESTATUS => 1,
            ]); // refactored
        }
        return $this->propMgmtDocsArray;
    }

    /**
     * @var tblFileHMLOBusinessEntityRef[]|null $fileHMLOEntityRefInfo
     */
    private ?array $fileHMLOEntityRefInfo = null;

    /**
     * @return tblFileHMLOBusinessEntityRef[]
     */
    public function fileHMLOEntityRefInfo(): array
    {
        if (is_null($this->fileHMLOEntityRefInfo) && $this->LMRId) {
            $this->fileHMLOEntityRefInfo = tblFileHMLOBusinessEntityRef::GetAll([
                tblFileHMLOBusinessEntityRef_db::COLUMN_FILEID       => $this->LMRId,
                tblFileHMLOBusinessEntityRef_db::COLUMN_ACTIVESTATUS => 1,
            ]);
        }
        return $this->fileHMLOEntityRefInfo;
    }

    /**
     * @var tblBorrowerAlternateNames[]|null $borrowerAlternateNames
     */
    public ?array $borrowerAlternateNames = null;

    /**
     * @return tblBorrowerAlternateNames[]
     */
    public function borrowerAlternateNames(): ?array
    {
        if (is_null($this->borrowerAlternateNames) && $this->LMRId) {
            $this->borrowerAlternateNames = tblBorrowerAlternateNames::GetAll([
                tblBorrowerAlternateNames_db::COLUMN_LMRID => $this->LMRId,
            ]);
        }
        return $this->borrowerAlternateNames;
    }

    public function fileHMLOExperienceInfo(): ?tblFileHMLOExperience
    {
        return $this->getFileHMLOExperience();
    }

    private ?tblRestInfo $RESTInfo = null;

    public function RESTInfo(): ?tblRestInfo
    {
        if (is_null($this->RESTInfo) && $this->LMRId) {
            $this->RESTInfo = tblRestInfo::Get([
                tblRestInfo_db::COLUMN_LMRID => $this->LMRId,
            ]); // refactored
        }
        return $this->RESTInfo;
    }

    public ?tblFileLoanOrigination $fileLoanOriginationInfo = null;

    public function fileLoanOriginationInfo(): ?tblFileLoanOrigination
    {
        if (is_null($this->fileLoanOriginationInfo) && $this->LMRId) {
            $this->fileLoanOriginationInfo = tblFileLoanOrigination::Get([
                tblFileLoanOrigination_db::COLUMN_FILEID => $this->LMRId,
            ]); // refactored
        }
        return $this->fileLoanOriginationInfo;
    }

    private ?tblFileLOExplanation $LOExplanationInfo = null;

    public function LOExplanationInfo(): ?tblFileLOExplanation
    {
        if (is_null($this->LOExplanationInfo) && $this->LMRId) {
            $this->LOExplanationInfo = tblFileLOExplanation::Get([
                tblFileLOExplanation_db::COLUMN_FILEID => $this->LMRId,
            ]); // refactored
        }
        return $this->LOExplanationInfo;
    }

    /* @var tblGiftsOrGrants[] $goginfo */
    private ?array $goginfo = null;

    /**
     * @return tblGiftsOrGrants[]
     */
    public function goginfo(): ?array
    {
        if (is_null($this->goginfo) && $this->LMRId) {
            $this->goginfo = tblGiftsOrGrants::GetAll([
                tblGiftsOrGrants_db::COLUMN_LMRID => $this->LMRId,
            ]);
        }
        return $this->goginfo;
    }

    /* @var lendingwise\contingentLiabilities[] $goginfo */
    private ?array $contingentLiabilities = null;

    /**
     * @return lendingwise\contingentLiabilities[]
     */
    public function contingentLiabilities(): ?array
    {
        if (is_null($this->contingentLiabilities) && $this->LMRId) {
            $this->contingentLiabilities = lendingwise\contingentLiabilities::GetAll([
                contingentLiabilities_db::COLUMN_LMRID => $this->LMRId,
            ]);
        }
        return $this->contingentLiabilities;
    }

    /* @var tblPartnerShips[] $partnerShipsInfo */
    private ?array $partnerShipsInfo = null;

    /**
     * @return tblPartnerShips[]
     */
    public function partnerShipsInfo(): ?array
    {
        if (is_null($this->partnerShipsInfo) && $this->LMRId) {
            $this->partnerShipsInfo = tblPartnerShips::GetAll([
                'LMRId' => $this->LMRId
            ]);
        }
        return $this->partnerShipsInfo;
    }

    /* @var tblOtherNewMortgageLoansOnProperty[] $mortgagePropLoanInfo */
    private ?array $mortgagePropLoanInfo = null;

    /**
     * @return tblOtherNewMortgageLoansOnProperty[]
     */
    public function mortgagePropLoanInfo(): ?array
    {
        if (is_null($this->mortgagePropLoanInfo) && $this->LMRId) {
            $this->mortgagePropLoanInfo = tblOtherNewMortgageLoansOnProperty::GetAll([
                tblOtherNewMortgageLoansOnProperty_db::COLUMN_LMRID => $this->LMRId
            ]);
        }
        return $this->mortgagePropLoanInfo;
    }

    /* @var tblRefinanceMortgage[] $refinanceMortgageInfo */
    private ?array $refinanceMortgageInfo = null;

    /**
     * @return tblRefinanceMortgage[]
     */
    public function refinanceMortgageInfo(): ?array
    {
        if (is_null($this->refinanceMortgageInfo) && $this->LMRId) {
            $this->refinanceMortgageInfo = tblRefinanceMortgage::GetAll([
                tblRefinanceMortgage_db::COLUMN_LMRID => $this->LMRId,
            ]);
        }
        return $this->refinanceMortgageInfo;
    }

    public function getCreatedDateWithTimestamp(): ?string
    {
        if (is_null($this->createdDateWithTimestamp) && $this->LMRId) {
            $sql = '
                SELECT min(FUAID) as FUAID,fileID AS LMRId ,recordDate
                FROM tblFileUpdateAudit 
                WHERE fileID = :fileID      ';
            $res = Database2::getInstance()->queryData($sql, ['fileID' => $this->LMRId]);
            $this->createdDateWithTimestamp = $res[0]['recordDate'] ?? null;
        }
        return $this->createdDateWithTimestamp;
    }

    private ?FileHUD $HUD = null;

    public function getHUD(): ?FileHUD
    {
        if(is_null($this->HUD) && $this->LMRId) {
            $this->HUD = FileHUD::getReport($this->LMRId);
        }
        return $this->HUD;
    }

    public function getPreBoarding(): tblPreBoarding
    {
        if (is_null($this->preBoardingInfo)) {
            $this->preBoardingInfo = tblPreBoarding::Get(['LMRId' => $this->LMRId]) ?? new tblPreBoarding();
        }
        return $this->preBoardingInfo;
    }

}

