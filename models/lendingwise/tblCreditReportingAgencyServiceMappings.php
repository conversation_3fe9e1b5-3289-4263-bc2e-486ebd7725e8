<?php

namespace models\lendingwise;

use models\Database2;
use models\lendingwise\db\tblCreditReportingAgencyServiceMappings_db;

/**
 *
 */
class tblCreditReportingAgencyServiceMappings extends tblCreditReportingAgencyServiceMappings_db
{
    public static function getCRAs(): array
    {
        $sql = "
            SELECT 
                cra.key                                 AS craKey,
                cra.name                                AS craName, 
                crs.key                                 AS crsKey, 
                COALESCE(crasm.displayName, crs.name)   AS crsName,
                crasm.URL                               AS URL
            FROM tblCreditReportingAgencyServiceMappings crasm 
                LEFT JOIN tblCreditReportingAgencies cra ON crasm.agencyKey = cra.key 
                LEFT JOIN tblCreditReportingServices crs ON crasm.serviceKey = crs.key
            WHERE cra.status = 1 
                AND crs.status = 1
            ORDER BY crs.sortOrder";

        $res = Database2::getInstance()->queryData($sql);
        foreach ($res as $row) {
            $data[$row['craKey']]['Name'] = $row['craName'];
            $data[$row['craKey']]['Services'][$row['crsKey']] = ['Name' => $row['crsName'], 'link' => $row['URL']];
        }

        return $data ?? [];
    }
}