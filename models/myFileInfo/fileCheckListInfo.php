<?php

namespace models\myFileInfo;

use models\Database2;
use models\types\strongType;

class fileCheckListInfo extends strongType
{
    public ?int $FMID = null;
    public ?int $fileID = null;
    public ?string $docName = null;
    public ?int $dStatus = null;
    public ?string $serviceType = null;
    public ?string $moduleType = null;
    public ?string $createdDate = null;
    public ?int $displayOrder = null;
    public ?string $description = null;
    public ?string $refDocName = null;
    public ?string $refDocUrl = null;
    public ?int $FRID = null;
    public ?string $requiredBy = null;
    public ?int $UID = null;
    public ?string $URole = null;
    public ?string $recordDate = null;
    public ?int $activeStatus = null;
    public ?int $categoryId = null;

    /**
     * @param string $UType
     * @param int $LMRId
     * @param int $externalBroker
     * @return self[][]
     */
    public static function getReport(string $UType, int $LMRId, int $externalBroker): array
    {
        $fileCheckListInfo = [];

        $qry = 'SELECT *,"99" as displayOrder FROM tblFileChecklistModules t1';
        if ($UType == 'Agent' && $externalBroker == 0) {
            $qry .= ' , tblFileChecklistRequiredBy t2 WHERE t1.FMID = t2.FMID AND t2.requiredBy = \'Broker\' ';
        } else if ($UType == 'Agent' && $externalBroker == 1) {
            $qry .= ' , tblFileChecklistRequiredBy t2 WHERE t1.FMID = t2.FMID AND t2.requiredBy = \'Loan Officer\' ';
        } elseif ($UType == 'Branch') {
            $qry .= ' , tblFileChecklistRequiredBy t2 WHERE t1.FMID = t2.FMID AND t2.requiredBy = \'Branch\' ';
        } elseif ($UType == 'Client') {
            $qry .= ' , tblFileChecklistRequiredBy t2 WHERE t1.FMID = t2.FMID AND t2.requiredBy = \'Borrower\' ';
        } else {
            $qry .= ' , tblFileChecklistRequiredBy t2 WHERE t1.FMID = t2.FMID ';
        }
        $qryWhr = '';
        $qryAnd = ' AND ';

        $qry .= ' ' . $qryWhr . $qryAnd . '  t1.dStatus = 1 AND t1.fileID = :LMRId ';

        if ($UType != 'Agent' && $UType != 'Branch' && $UType != 'Client') {
            $qry .= ' GROUP BY t2.`FMID` ';
        }

        $qry .= ' ORDER BY t1.moduleType, t1.serviceType, t1.displayOrder, t1.createdDate ;  ';

        /* @var self[] $rs */
        $rs = Database2::getInstance()->queryData($qry, [
            'LMRId' => $LMRId,
        ], function($row) {
            return new self($row);
        });

        foreach ($rs as $row) {
            $fileCheckListInfo[$row->moduleType][] = $row;
        }
        return $fileCheckListInfo;
    }
}