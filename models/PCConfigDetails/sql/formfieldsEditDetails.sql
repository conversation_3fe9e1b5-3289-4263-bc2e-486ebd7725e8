SELECT SUM(CASE WHEN pc.QADisplay != m.QADefaultShow THEN 1 ELSE 0 END)                                                                         AS QAEditCount,
       SUM(CASE WHEN pc.FADisplay != m.FADefaultShow THEN 1 ELSE 0 END)                                                                         AS FAEditCount,
       SUM(CASE WHEN pc.BODisplay != m.BODefaultShow THEN 1 ELSE 0 END)                                                                         AS BOEditCount,
       SUM(CASE WHEN (pc.QADisplay != m.QADefaultShow OR pc.FADisplay != m.FADefaultShow OR pc.BODisplay != m.BODefaultShow) THEN 1 ELSE 0 END) AS ffEditCount
FROM tblFieldsQuickApp pc
         JOIN tblFormFieldsMaster m ON pc.fieldID = m.fieldID
WHERE pc.PCID = @PCID
