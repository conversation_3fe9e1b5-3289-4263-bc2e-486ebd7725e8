SELECT SUM(CASE
               WHEN (processorComments = 'Created via Quick App Webform'
                   OR processorComments = 'Created via Full App Webform'
                   OR processorComments = 'Created via Webform'
                   OR processorComments = 'Auto Saved - Submission Not Completed - Via Multi Step Quick App') THEN 1
               ELSE 0 END)                                                                AS fileCreatedViaWebformCount,
       SUM(CASE WHEN processorComments = 'Created via Back Office' THEN 1 ELSE 0 END)     AS fileCreatedViaBOCount,
       SUM(CASE WHEN processorComments = 'Created via Branch' THEN 1 ELSE 0 END)          AS fileCreatedViaBranchCount,
       SUM(CASE WHEN processorComments = 'Created via Broker' THEN 1 ELSE 0 END)          AS fileCreatedViaBranchCount,
       SUM(CASE WHEN processorComments = 'Created via Loan Officer' THEN 1 ELSE 0 END)    AS fileCreatedViaLoanOfficerCount,
       SUM(CASE WHEN processorComments = 'Created via Borrower Portal' THEN 1 ELSE 0 END) AS fileCreatedViaBorrowerCount
FROM `tblLMRProcessorComments` t100
         INNER JOIN tblFile t200 ON t100.fileID = t200.LMRId
WHERE t200.FPCID = @PCID AND DATE (notesDate) >= CURDATE() - INTERVAL 30 DAY