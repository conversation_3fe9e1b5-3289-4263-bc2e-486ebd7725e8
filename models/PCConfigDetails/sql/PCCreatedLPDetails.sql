SELECT SUM(CASE
               WHEN (tm.activeStatus = 1 AND ts.activeStatus = 1 AND tms.internalLoanProgram = 1)
                   THEN 1
               ELSE 0 END) AS PCCreatednternalLoanProgramsCount,
       SUM(CASE
               WHEN (tm.activeStatus = 1 AND ts.activeStatus = 1 AND tms.internalLoanProgram = 0)
                   THEN 1
               ELSE 0 END) AS PCCreatedLoanProgramsCount
FROM tblModules tm
         INNER JOIN tblModuleServices tms ON tms.MID = tm.MID
         INNER JOIN tblLibServiceTypes ts ON ts.STCode = tms.STCode
WHERE ts.PCID = @PCID
