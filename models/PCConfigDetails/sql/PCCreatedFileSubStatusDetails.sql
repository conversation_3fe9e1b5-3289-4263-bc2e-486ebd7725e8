SELECT SUM(CASE
               WHEN activeStatus = 1 AND substatus NOT IN (SELECT substatus FROM `tblPCFileSubstatusMaster`) THEN 1
               ELSE 0 END) AS activePCCreatedFileSubStatusCount,
       SUM(CASE
               WHEN activeStatus = 0 AND substatus NOT IN (SELECT substatus FROM `tblPCFileSubstatusMaster`) THEN 1
               ELSE 0 END) AS inactivePCCreatedFileSubStatusCount,
       SUM(CASE
               WHEN activeStatus = 0 AND
                    substatus IN (SELECT substatus FROM tblPCFileSubstatusMaster WHERE activeStatus = 1) THEN 1
               ELSE 0 END) AS inactiveFileSubStatusCount,
       SUM(CASE
               WHEN activeStatus = 0 AND
                    substatus IN (SELECT substatus FROM tblPCFileSubstatusMaster WHERE activeStatus = 1) THEN 1
               ELSE 0 END) +
       SUM(CASE
               WHEN activeStatus = 0 AND substatus NOT IN (SELECT substatus FROM `tblPCFileSubstatusMaster`) THEN 1
               ELSE 0 END)    totaInactiveFileSubStatusCount
FROM tblPCFileSubstatus
WHERE PCID = @PCID