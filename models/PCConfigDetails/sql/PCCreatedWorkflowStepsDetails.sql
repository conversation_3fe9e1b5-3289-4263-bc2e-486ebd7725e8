SELECT SUM(CASE
               WHEN tblPCWorkflowSteps.dStatus = 1
                   AND steps NOT IN (SELECT DISTINCT steps
                                     FROM `tblWorkflowMaster`
                                              INNER JOIN `tblWorkflowStepsMaster`
                                                         ON tblWorkflowStepsMaster.WFID = tblWorkflowMaster.WFID)
                   THEN 1
               ELSE 0 END) AS activePCCreatedWorkflowStepsCount,

       SUM(CASE
               WHEN tblPCWorkflowSteps.dStatus = 0
                   AND steps NOT IN (SELECT DISTINCT steps
                                     FROM `tblWorkflowMaster`
                                              INNER JOIN `tblWorkflowStepsMaster`
                                                         ON tblWorkflowStepsMaster.WFID = tblWorkflowMaster.WFID)
                   THEN 1
               ELSE 0 END) AS inactivePCCreatedWorkflowStepsCount,

       SUM(CASE
               WHEN tblPCWorkflowSteps.dStatus = 0
                   AND steps IN (SELECT DISTINCT steps
                                 FROM `tblWorkflowMaster`
                                          INNER JOIN `tblWorkflowStepsMaster`
                                                     ON tblWorkflowStepsMaster.WFID = tblWorkflowMaster.WFID
                                 WHERE tblWorkflowStepsMaster.dStatus = 1) THEN 1
               ELSE 0 END) AS inactiveWorkflowStepsCount,

       SUM(CASE
               WHEN tblPCWorkflowSteps.dStatus = 0
                   AND steps NOT IN (SELECT DISTINCT steps
                                     FROM `tblWorkflowMaster`
                                              INNER JOIN `tblWorkflowStepsMaster`
                                                         ON tblWorkflowStepsMaster.WFID = tblWorkflowMaster.WFID)
                   THEN 1
               ELSE 0 END) +
       SUM(CASE
               WHEN tblPCWorkflowSteps.dStatus = 0
                   AND steps IN (SELECT DISTINCT steps
                                 FROM `tblWorkflowMaster`
                                          INNER JOIN `tblWorkflowStepsMaster`
                                                     ON tblWorkflowStepsMaster.WFID = tblWorkflowMaster.WFID
                                 WHERE tblWorkflowStepsMaster.dStatus = 1) THEN 1
               ELSE 0 END) AS totalInactiveWorkflowStepsCount


FROM tblPCWorkflowSteps
         INNER JOIN tblPCWorkflow ON tblPCWorkflowSteps.WFID = tblPCWorkflow.WFID
WHERE pcid = @PCID
