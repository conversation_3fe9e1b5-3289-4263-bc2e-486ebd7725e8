SELECT SUM(CASE WHEN t3.internalLoanProgram = 0 THEN 1 ELSE 0 END) AS enabledLoanProgramsCount,
       SUM(CASE WHEN t3.internalLoanProgram = 1 THEN 1 ELSE 0 END) AS enabledInternalLoanProgramsCount
FROM tblPCModules t1
         JOIN tblModules t2 ON t1.moduleCode = t2.moduleCode
         JOIN tblModuleServices t3 ON t2.MID = t3.MID
         JOIN tblLibServiceTypes t4 ON t3.STCode = t4.STCode
         JOIN tblProcessingCompanyLMRClientType t5 ON t1.PCID = t5.PCID AND t3.STCode = t5.LMRClientType
WHERE t1.PCID IN (@PCID)
  AND t1.subscribedStatus = 1
  AND t1.activeStatus = 1
  AND t2.activeStatus = 1
  AND t4.activeStatus = 1
  AND t1.activeStatus = t2.activeStatus
  AND t5.moduleCode = t1.moduleCode
