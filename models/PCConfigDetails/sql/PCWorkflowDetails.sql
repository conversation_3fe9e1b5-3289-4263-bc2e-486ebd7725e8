SELECT SUM(CASE
               WHEN dStatus = 1 AND WFName NOT IN (SELECT WFName FROM tblWorkflowMaster) THEN 1
               ELSE 0 END) AS activePCCreatedWorkflowCount,
       SUM(CASE
               WHEN dStatus = 0 AND WFName NOT IN (SELECT WFName FROM tblWorkflowMaster) THEN 1
               ELSE 0 END) AS inactivePCCreatedWorkflowCount,
       SUM(CASE
               WHEN dStatus = 0 AND WFName IN (SELECT WFName FROM tblWorkflowMaster WHERE dstatus = 1) THEN 1
               ELSE 0 END) AS inactiveWorkflowCount,
       SUM(CASE
               WHEN dStatus = 0 AND WFName NOT IN (SELECT WFName FROM tblWorkflowMaster) THEN 1
               ELSE 0 END) +
       SUM(CASE
               WHEN dStatus = 0 AND WFName IN (SELECT WFName FROM tblWorkflowMaster  WHERE dstatus = 1) THEN 1
               ELSE 0 END)    totalInactiveWorkflowCount
FROM tblPCWorkflow
WHERE pcid = @PCID;
