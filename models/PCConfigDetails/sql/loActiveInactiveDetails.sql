SELECT SUM(CASE WHEN (tl.activeStatus = 1 AND tb.status = 1) THEN 1 ELSE 0 END)                              AS activeLoanOfficerCount,
       SUM(CASE WHEN (tl.activeStatus = 0 AND tb.status = 1) THEN 1 ELSE 0 END)                              AS inactiveLoanOfficerCount,
       SUM(CASE
               WHEN (tl.activeStatus = 1 AND tb.status = 1 AND tb.allowAgentToLogin = 1) THEN 1
               ELSE 0 END)                                                                                   AS loginRightLoanOfficerCount

FROM tblAgent tb
         JOIN tblAgentPC tl
              ON tl.AID = tb.userNumber
WHERE tl.PCID = @PCID
  AND tb.externalBroker = 1
  AND tb.email NOT LIKE '%@dummyagentemail.com'