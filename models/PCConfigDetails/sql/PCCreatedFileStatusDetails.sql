SELECT SUM(CASE
               WHEN activeStatus = 1 AND primaryStatus NOT IN (SELECT primaryStatus FROM tblPrimaryStatusMaster) THEN 1
               ELSE 0 END) AS activePCCreatedFileStatusCount,
       SUM(CASE
               WHEN activeStatus = 0 AND primaryStatus NOT IN (SELECT primaryStatus FROM tblPrimaryStatusMaster) THEN 1
               ELSE 0 END) AS inactivePCCreatedFileStatusCount,
       SUM(CASE
               WHEN activeStatus = 0 AND primaryStatus IN (SELECT primaryStatus FROM tblPrimaryStatusMaster) THEN 1
               ELSE 0 END) AS inactiveFileStatusCount,
       SUM(CASE
               WHEN activeStatus = 0 AND primaryStatus NOT IN (SELECT primaryStatus FROM tblPrimaryStatusMaster) THEN 1
               ELSE 0 END) +
       SUM(CASE
               WHEN activeStatus = 0 AND primaryStatus IN (SELECT primaryStatus FROM tblPrimaryStatusMaster) THEN 1
               ELSE 0 END)    totalInactiveFileStatusCount
FROM tblPCPrimeStatus
WHERE pcid = @PCID;