<?php

namespace models\pops;

use models\constants\gl\glAllowMassPrintingPC;
use models\PageVariables;

/**
 *
 */
class massUpdateForm
{

    public ?string $thankYouMessage = '';
    public ?string $error = '';

    /**
     * @param int $ajax
     * @param array $inArray
     * @return string
     */
    public function getFormHtml(int $ajax = 0, array $inArray = []): string
    {
        $glAllowMassPrintingPC = glAllowMassPrintingPC::$glAllowMassPrintingPC;

        $formHtml = '';
        if (isset($_POST['httpReferer']) && !$this->error) {
            $out = '<p id="contact-pop-error" class="formItem">' . $this->thankYouMessage . '</p>';
            if ($ajax) $out .= '<a href="#" class="close-overlay">Close</a>';

            return $out;
        }
        $saveUrl = CONST_URL_POPS . 'updateFileEnmass.php';
        $userRole = trim($inArray['userRole']);
        $userGroup = trim($inArray['userGroup']);
        $userNumber = trim($inArray['userNumber']);
        $PCID = trim($inArray['PCID']);
        $activeFile = trim($inArray['activeFile']);
        $fileType = trim($inArray['fileType']);
        $isSysNotesPrivate = trim($inArray['isSysNotesPrivate']);

        $formHtml .= <<<EOT
<script type="text/javascript" src="/assets/js/popup.js"></script>

<div class="container-fluid">
<form name="massUpdateForm" id="massUpdateForm" method="POST" action="$saveUrl" >
<input type="hidden" name="LMRResponseIds" value="">
<input type="hidden" name="noOfFiles" value="">

<input type="hidden" name="selMassOpt" value="">
<input type="hidden" name="massStatusId" value="">
<input type="hidden" name="selectedBranchID" value="">
<input type="hidden" name="selectedAgentID" value="">
<input type="hidden" name="startDate" value="">
<input type="hidden" name="eDate" value="">
<input type="hidden" name="lenName" value="">
<input type="hidden" name="selectedEmpID" value="">
<input type="hidden" name="selectedSecondaryBrokerID" value="">
<input type="hidden" name="selectedBrokerID" value="">
<input type="hidden" name="selectedPCID" value="">
<input type="hidden" name="selectedPriLevel" value="">
<input type="hidden" name="selectedStaleDay" value="">
<input type="hidden" name="selectedPropState" value="">
<input type="hidden" name="selectedLeadSource" value="">
<input type="hidden" name="selectedLMRCTypes" value="">
<input type="hidden" name="selectedStatusOpt" value="">
<input type="hidden" name="searchTerm" value="">
<input type="hidden" name="searchFields" value="">
<input type="hidden" name="notesType" value="">
<input type="hidden" name="bDD" value="">
<input type="hidden" name="newbranchName" value="">
<input type="hidden" name="fileType" id="fileType" value="$fileType">
<input type="hidden" name="updatedStartDate" value="">
<input type="hidden" name="updatedEndDate" value="">
<input type="hidden" name="multipleFileStatus" value="">
<input type="hidden" name="WFStepIDs" value="">
<input type="hidden" name="isSysNotesPrivate" value="$isSysNotesPrivate">
<input type="hidden" name="multipleModuleCode" value="">
<input type="hidden" name="closingStartDate" value="">
<input type="hidden" name="closingEndDate" value="">
<input type="hidden" name="maturityStartDate" value="">
<input type="hidden" name="maturityEndDate" value="">
<input type="hidden" name="appraisalStartDate" value="">
<input type="hidden" name="appraisalEndDate" value="">
<input type="hidden" name="receivedStartDate" value="">
<input type="hidden" name="receivedEndDate" value="">
<input type="hidden" name="disclosureSentDateStart" value="">
<input type="hidden" name="disclosureSentDateEnd" value="">
<input type="hidden" name="selectedLoanOfficerID" value="">
<input type="hidden" name="selectedWFID" value="">
<input type="hidden" name="selectedWFSID" value="">
<input type="hidden" name="selectedWFSNotCompletedId" value="">
<input type="hidden" name="selectedInternalLoanPrograms" value="">
<input type="hidden" name="selectedServicingStatusCodes" value="">
<input type="hidden" name="referringPartySearchValue" value="">
<input type="hidden" name="paymentBasedAmt" value="">
<input type="hidden" name="selectedContactsIds" value="">
		   
<script>
    var subStDate = '', subEndDate = '';
	var modStartDate = '', modEndDate = '', multipleStatus = '', WFStepIDs = ''; 
	var multipleModuleCode = '';

    var responseIds         = document.LMRReport.selVal.value;
    var nf                  = document.LMRReport.noOfFiles.value;
    var selMassOpt          = document.LMRReport.selMassOpt.value;
    var massStatusId        = document.LMRReport.massStatusId.value;
    var massStatusValue     = document.LMRReport.massStatusValue.value;
    var massStatusCnt       = document.LMRReport.MassFiles.value;
    var massStatusFileCnt   = document.LMRReport.massStatusCnt.value;
    var UnselectedMassFiles = document.LMRReport.UnselectedMassFiles.value;
    var allCount            = document.LMRReport.allCount.value;
    var executiveId         = document.LMRReport.executiveId.value;
    var brokerNumb          = document.LMRReport.brokerNumb.value;
    var startDate           = document.LMRReport.startDate.value;
    var eDate               = document.LMRReport.eDate.value;
    var lenName             = document.LMRReport.lenName.value;
    var selectedEmpID       = document.LMRReport.selectedEmpID.value;
    var selectedSecondaryBrokerID = document.LMRReport.selectedSecondaryBrokerID.value;
    var selectedBrokerID    = document.LMRReport.selectedBrokerID.value;
    var selectedPCID        = document.LMRReport.PCID.value;
    var selectedStaleDay    = document.LMRReport.myStaleDay.value;
    var selectedPriLevel    = document.LMRReport.priLevel.value;
    var selectedPropState   = document.LMRReport.propState.value;
    var selectedLeadSource  = document.LMRReport.massLeadSource.value;
    var selectedLMRCTypes   = document.LMRReport.LMRCTypes.value;
    var selectedStatusOpt   = document.LMRReport.subStatus.value;
    var searchTerm          = document.LMRReport.massSearch.value;
    var searchFields        = document.LMRReport.searchFields.value;
    var notesType           = document.LMRReport.ntsType.value;
    var bDD                 = document.LMRReport.bDD.value;
    let closingStartDate                 = document.LMRReport.closingStartDate.value;
    let closingEndDate                 = document.LMRReport.closingEndDate.value;
    let maturityStartDate                 = document.LMRReport.maturityStartDate.value;
    let maturityEndDate                 = document.LMRReport.maturityEndDate.value;
    let appraisalStartDate                 = document.LMRReport.appraisalStartDate.value;
    let appraisalEndDate                 = document.LMRReport.appraisalEndDate.value;
    let receivedStartDate                 = document.LMRReport.receivedStartDate.value;
    let receivedEndDate                 = document.LMRReport.receivedEndDate.value;
    let disclosureSentDateStart                 = document.LMRReport.disclosureSentDateStart.value;
    let disclosureSentDateEnd                 = document.LMRReport.disclosureSentDateEnd.value;
    let selectedLoanOfficerID                 = document.LMRReport.loanOfficerIds.value;
    let selectedWFID                 = document.LMRReport.selectedWFID.value;
    let selectedWFSID                 = document.LMRReport.selectedWFSID.value;
    let selectedWFSNotCompletedId                 = document.LMRReport.selectedWFSNotCompletedId.value;
    let selectedInternalLoanPrograms                 = document.LMRReport.selectedInternalLoanPrograms.value;
    let selectedServicingStatusCodes                 = document.LMRReport.selectedServicingStatusCodes.value;
    let referringPartySearchValue                 = document.LMRReport.referringPartySearchValue.value;
    let paymentBasedAmt                 = document.LMRReport.paymentBasedAmt.value;
    let selectedContactsIds                 = document.LMRReport.selectedContactsIds.value;

    try {
      multipleModuleCode= document.LMRReport.multipleModuleCode.value;
    } catch(e) {}
  	try {
  		paymentStatus     = document.LMRReport.paymentStatus.value;
  	} catch(e) {}
  	try {
  		subStDate         = document.LMRReport.subStDate.value;
  	} catch(e) {}
  	try {
  		subEndDate        = document.LMRReport.subEndDate.value;
  	} catch(e) {}
  	try {
  		modStartDate      = document.LMRReport.modStartDate.value;
  	} catch(e) {}
  	try {
  		modEndDate        = document.LMRReport.modEndDate.value;
  	} catch(e) {}
  	try {
  		multipleStatus    = document.LMRReport.multipleStatus.value;
  	} catch(e) {}
  	try {
  		WFStepIDs         = document.LMRReport.WFStepIDs.value;
  	} catch(e) {}


	document.massUpdateForm.LMRResponseIds.value     = responseIds;
	document.massUpdateForm.noOfFiles.value          = nf;

	document.massUpdateForm.selMassOpt.value         = selMassOpt;
	document.massUpdateForm.massStatusId.value       = massStatusId;
	document.massUpdateForm.selectedBranchID.value   = executiveId;
	document.massUpdateForm.selectedAgentID.value    = brokerNumb;
	document.massUpdateForm.selectedEmpID.value      = selectedEmpID;
	document.massUpdateForm.selectedBrokerID.value      = selectedBrokerID;
	document.massUpdateForm.selectedSecondaryBrokerID.value      = selectedSecondaryBrokerID;
	document.massUpdateForm.selectedPCID.value       = selectedPCID;
	document.massUpdateForm.selectedPriLevel.value   = selectedPriLevel;
	document.massUpdateForm.selectedStaleDay.value   = selectedStaleDay;
	document.massUpdateForm.selectedPropState.value  = selectedPropState;
	document.massUpdateForm.selectedLeadSource.value = selectedLeadSource;
	document.massUpdateForm.selectedLMRCTypes.value  = selectedLMRCTypes;
	document.massUpdateForm.selectedStatusOpt.value  = selectedStatusOpt;
	document.massUpdateForm.searchTerm.value         = searchTerm;
	document.massUpdateForm.searchFields.value       = searchFields;
	document.massUpdateForm.notesType.value          = notesType;
	document.massUpdateForm.bDD.value                = bDD;

	document.massUpdateForm.startDate.value          = startDate;
	document.massUpdateForm.eDate.value              = eDate;
	document.massUpdateForm.lenName.value            = lenName;
	document.massUpdateForm.subEndDate.value         = subEndDate;
	document.massUpdateForm.subStDate.value          = subStDate;
	document.massUpdateForm.updatedStartDate.value   = modStartDate;
	document.massUpdateForm.updatedEndDate.value     = modEndDate;
	document.massUpdateForm.multipleFileStatus.value = multipleStatus;
  document.massUpdateForm.multipleModuleCode.value = multipleModuleCode;
  document.massUpdateForm.closingStartDate.value = closingStartDate;
  document.massUpdateForm.closingEndDate.value = closingEndDate;
  document.massUpdateForm.maturityStartDate.value = maturityStartDate;
  document.massUpdateForm.maturityEndDate.value = maturityEndDate;
  document.massUpdateForm.appraisalStartDate.value = appraisalStartDate;
  document.massUpdateForm.appraisalEndDate.value = appraisalEndDate;
  document.massUpdateForm.receivedStartDate.value = receivedStartDate;
  document.massUpdateForm.receivedEndDate.value = receivedEndDate;
  document.massUpdateForm.disclosureSentDateStart.value = disclosureSentDateStart;
  document.massUpdateForm.disclosureSentDateEnd.value = disclosureSentDateEnd;
  document.massUpdateForm.selectedLoanOfficerID.value = selectedLoanOfficerID;
  document.massUpdateForm.selectedWFID.value = selectedWFID;
  document.massUpdateForm.selectedWFSID.value = selectedWFSID;
  document.massUpdateForm.selectedWFSNotCompletedId.value = selectedWFSNotCompletedId;
  document.massUpdateForm.selectedInternalLoanPrograms.value = selectedInternalLoanPrograms;
  document.massUpdateForm.selectedServicingStatusCodes.value = selectedServicingStatusCodes;
  document.massUpdateForm.referringPartySearchValue.value = referringPartySearchValue;
  document.massUpdateForm.paymentBasedAmt.value = paymentBasedAmt;
  document.massUpdateForm.selectedContactsIds.value = selectedContactsIds;

	document.massUpdateForm.WFStepIDs.value	= WFStepIDs;
 if(massStatusCnt > 0 && selMassOpt !== 'All' && (massStatusId >0 || massStatusId !== '')) { 
	document.getElementById('noOFFilesDiv').innerHTML = "<h3>You have selected  <font color=\"#ff0000\">("+massStatusCnt+")</font> files.</h3>";
} else if(selMassOpt === 'All' && massStatusValue !== '') {
	document.getElementById('noOFFilesDiv').innerHTML = "<h3>You have selected  <font color=\"#ff0000\">("+massStatusFileCnt+")</font>  "+massStatusValue+" status files.</h3>";
} else if (massStatusValue !== '') { 
	document.getElementById('noOFFilesDiv').innerHTML = "<h3>You have selected  <font color=\"#ff0000\">("+massStatusCnt+")</font>  "+massStatusValue+" files.</h3>";
}else if(selMassOpt !== 'All' && massStatusId === 'All' ) {
	document.getElementById('noOFFilesDiv').innerHTML = "<h3>You have selected  <font color=\"#ff0000\">("+massStatusCnt+")</font>  files.</h3>";
}else {
	document.getElementById('noOFFilesDiv').innerHTML = "<h3>You have selected  <font color=\"#ff0000\">("+allCount+")</font>  files.</h3>";
}
</script>
  <input type="hidden" name="selectedPaymentStatus" value="">
  <input type="hidden" name="subStDate" value="">
  <input type="hidden" name="subEndDate" value="">
  <!-- automation -->
  <input type="hidden" name="lastUpdatedParam" id="lastUpdatedParam" value="">
  <input type="hidden" name="lastUpdatedFss" id="lastUpdatedFss" value="">
  <input type="hidden" name="triggerRule" id="triggerRule" value="No">
  <!-- // automation // -->
<div class="col-md-12" style="text-align:center;" id="noOFFilesDiv"></div>
 <div class="container-fluid p-0 m-0">
<input type="hidden" id="activeFileStatus" value="$activeFile">
EOT;
        if ($activeFile == 0) {
            $formHtml .= <<<EOT
            <div class="form-group row">
        <div class="col-md-4 ">
         <div class="radio-list">
          <label for="activateFiles" class="radio"><input class="newCheck" type="radio" name="opn" id="activateFiles" value="Activate" onclick="checkMassUpdate('Activate');"><span></span>Activate Files</label>
          </div>
        </div>
      </div>

EOT;

        } else {
            if ($fileType == '4' || $fileType == 'LA') {
                $formHtml .= <<<EOT

      <div class="col-md-1"><input type="radio" name="opn" id="reportStatusID" value="CPS" onclick="checkMassUpdate('CPS');" />Payment Status</div>

	    <div class="col-md-2" id="paymentStatusDiv" style="display:none;">
		   <select name="paymentStatus" id="reportStatus" class="mandatory">
			 <option value="" > All / Payment Status </option>
			 <option value="Payment Due"> Payment Due </option>
			 <option value="Paid"> Paid </option>
			 <option value="Delinquent"> Delinquent </option>
		   </select>
	    </div>


EOT;
            } else {
                if ($userRole == 'Super' || PageVariables::$allowToMassUpdate) {        /* Hide excel download feature for Awata Employe & Dave Maresca employee*/
                    $formHtml .= <<<EOT
    
      <div class="form-group row">
        <div class="col-md-4">
           <div class="radio-list">
          <label for="delFiles" class="radio"><input class="newCheck" type="radio" name="opn" id="delFiles" value="Delete" onclick="checkMassUpdate('Delete');"><span></span>Deactivate Files</label>
          </div>
        </div>
      </div>
EOT;

                }


                if ($userRole == 'Manager' || $userRole == 'Super') {
                    if ($userRole !== 'Super') {
                        $formHtml .= <<<EOT
         
    <div class="form-group row">
      <div class="col-md-4">
         <div class="radio-list">
            <label for="updateStatus" class="radio"><input class="newCheck" type="radio" name="opn" id="updateStatus" value="US" onclick="checkMassUpdate('US');"><span></span>Update Status</label>
            </div>
       </div>
	  <div id="usID" class="col-md-6" style="display:none"></div>
    </div>
    
    <div class="form-group row">
      <div class="col-md-4 ">
               <div class="radio-list">

            <label for="uSubStatus" class="radio" ><input  class="newCheck" type="radio" name="opn" id="uSubStatus" value="USS" onclick="checkMassUpdate('USS');"><span></span>Add Substatus</label>
            </div>
           </div>
	  <div id="ussID" class="col-md-6 left" style="display:none"></div>
    </div>
EOT;
                    }
                    $formHtml .= <<<EOT
	<div class="form-group row">
      <div class="col-md-4">
                     <div class="radio-list">
            <label for="updatebranch" class="radio"><input class="newCheck"  type="radio" name="opn" id="updatebranch" value="UB" onclick="checkMassUpdate('UB');"><span></span>Update Branch</label>
EOT;

                    if ($userRole == 'Super') {
                        $formHtml .= <<<EOT
		  <span class="text-muted text-danger">(Same PC)</span>
EOT;
                    }
                    $formHtml .= <<<EOT
            </div>
       </div>    
EOT;

                    $formHtml .= <<<EOT
	 
	  <div id="ubID" class="col-md-6" style="display:none"></div>
    </div>

    <div class="form-group row">
      <div class="col-md-4 ">
      <div class="radio-list">
           <label for="updateAgent" class="radio"><input  class="newCheck"  type="radio" name="opn" id="updateAgent" value="UA" onclick="checkMassUpdate('UA');"><span></span> Update Broker</label>
EOT;
                    if ($userRole == 'Super') {
                        $formHtml .= <<<EOT
		  <span class="text-muted text-danger">(Same PC)</span>
EOT;
                    }
                    $formHtml .= <<<EOT
           </div>
           </div>

	  <div id="uaID" class="col-md-6" style="display:none"></div>
    </div>
    
    
    <div class="form-group row">
      <div class="col-md-4 ">
          <div class="radio-list">
               <label for="updateSecondaryAgent"  class="radio"><input  class="newCheck"  type="radio" name="opn" id="updateSecondaryAgent" value="UL" onclick="checkMassUpdate('UL');"><span></span>Update Loan Officer</label>
EOT;

                    if ($userRole == 'Super') {
                        $formHtml .= <<<EOT
		  <span class="text-muted text-danger">(Same PC)</span>
EOT;
                    }
                    $formHtml .= <<<EOT
          </div>
      </div>

	  <div id="ulID" class="col-md-6 " style="display:none"></div>
    </div>
    
    <div class="form-group row">
      <div class="col-md-6">
            <div class="radio-list">
                 <label for="updateBranchAgent" class="radio"><input class="newCheck"  type="radio" name="opn" id="updateBranchAgent" value="UBA" onclick="checkMassUpdate('UBA');"><span></span>Update Branch/Loan Officer/Broker</label>
EOT;

                    if ($userRole == 'Super') {
                        $formHtml .= <<<EOT
		  <span class="text-muted text-danger">(Same PC)</span>
EOT;
                    }
                    $formHtml .= <<<EOT
            </div>
      </div>

	
	  <div id="ubaID" class="col-md-6 " style="display:none"></div>
    </div>
EOT;
                }
                if ($userRole == 'Manager' || $userRole == 'Super') {
                    $formHtml .= <<<EOT
    <div class="form-group row">
      <div class="col-md-4">
      <div class="radio-list">
         <label for="massAssignedEmp" class="radio"><input class="newCheck"  type="radio" name="opn" id="massAssignedEmp" value="MAE" onclick="checkMassUpdate('MAE');"><span></span>Assign employees</label>
         </div>
       </div>
	  <div id="maeID" class="col-md-6 left" style="display:none"></div>
    </div>
    
       
    
    <div class="form-group row">
      <div class="col-md-4">
      <div class="radio-list">
         <label for="massUnassignedEmp" class="radio"><input class="newCheck"  type="radio" name="opn" id="massUnassignedEmp" value="MRAE" onclick="checkMassUpdate('MRAE');"><span></span>Remove Assigned employees</label>
         </div>
       </div>
	  <div id="mraeID" class="col-md-6 left" style="display:none"></div>
    </div>
EOT;
                }
                if ($userRole == 'Super') {
                    $formHtml .= <<<EOT
	  <div class="form-group row">
          <div class="col-md-4">
              <div class="radio-list">
                 <label for="massCopyFile" class="radio"><input class="newCheck" type="radio" name="opn" id="massCopyFile" onclick="checkMassUpdate('MCF');" value="MCF"><span></span>Copy File</label>
              </div>
          </div>
          <div id="MCFID" class="col-md-8" style="display:none"></div>
    </div>
EOT;
                } else {
                    $formHtml .= <<<EOT
                   <label for="massCopyFile" class="radio d-none"><input class="newCheck" type="radio" name="opn" id="massCopyFile" onclick="checkMassUpdate('MCF');" value="MCF"><span></span>Copy File</label>   
                
EOT;
                }
                if (array_key_exists($PCID, $glAllowMassPrintingPC)) {
                    $formHtml .= <<<EOT
     <div class="form-group row">
      <div class="col-md-4">
      <div class="radio-list">
         <label for="allowMassPrint" class="radio"><input class="newCheck" onclick="checkMassUpdate('MPF');" type="radio" name="opn" id="allowMassPrint" value="MPF"><span></span> Mass print </label>
         </div>
       </div>
    <div id="MCFID" class="col-md-6 left" style="display:none"></div>
    </div>

EOT;
                }
                if ($userRole == 'Super' || $userRole == 'Manager') {
                    $formHtml .= <<<EOT

    <div class="form-group row">
      <div class="col-md-4">
      <div class="radio-list">
         <label for="allowEmailDocument" class="radio"><input class="newCheck" onclick="checkMassUpdate('MED');" type="radio" name="opn" id="allowEmailDocument" value="MED"><span></span> Email document</label>
         </div>
       </div>
      <div class="col-md-2">
      <div class="loaderNDiv" style="display:none;"><img src="/assets/js/3rdParty/Contact-Pop/img/ajax-loader.gif" alt=""><br>LOADING...</div>
      </div>

    </div>
          <div id="MEDID" class="form-group row " style="display:none"></div>

    
    <div class="form-group row">
      <div class="col-md-4">
       <div class="radio-list">
            <label for="updateLoanProgram" class="radio"><input class="newCheck" type="radio" name="opn" id="updateLoanProgram" value="ULP" onclick="checkMassUpdate('ULP');"><span></span>Update Loan Program</label>
            </div>
       </div>
	  <div id="ulpID" class="col-md-6" style="display:none"></div>
    </div>
EOT;

                }
            }
        }
        $formHtml .= <<<EOT
    <div class="form-group row">
        <div class="col-md-4">
            <div id="loaderDiv" style="display:none;text-align:center;"><img src="/assets/js/3rdParty/Contact-Pop/img/ajax-loader.gif" alt=""></div>
        </div>
    </div>
    
    <div class="form-group row">
        <div class="col-md-12">
            <input class="button" style="display:none;" type="submit" name="submit" id="submit" value="Save">
        </div>
    </div>
 
	<div class="form-group row">
	  <div class="col-md-12">
	     <span style="color:#ff0000"><b>Note: You are about to perform a mass update! Please choose any one of the option above.</b></span>
	  </div>
	</div>
    <input type="hidden" id="validationField" name="validationField">
</form>
</div>
EOT;
        return $formHtml;
    }
}
