<?php

namespace models\pops;


use models\composite\oBranch\getBranches;
use models\composite\oBroker\getPCAgents;
use models\composite\oContacts\getContactList;
use models\composite\oContacts\getMyContactType;
use models\composite\oEmployee\getPCEmployeeList;
use models\composite\oMarketPlace\getMarketPlaceLoanPrograms;
use models\composite\oMarketPlace\getPCNiches;
use models\composite\oPC\getPCBasicLoanInfo;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\constants\gl\glbusinessCategoryArray;
use models\constants\gl\glEntityTypeArray;
use models\constants\gl\glEquipmentType;
use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOExitStrategy;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLienPosition;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\globalBankruptcyCat;
use models\constants\gl\globalBusinessBankruptcyCat;
use models\constants\gl\globalMinTimeInBusinessCat;
use models\constants\gl\globalRecentNSFsCat;
use models\constants\gl\globalSBALoanProductsCat;
use models\constants\gl\glpaymentFrequency;
use models\constants\gl\glRateLockPeriod;
use models\constants\gl\gltypeOfHMLOLoanRequesting;
use models\constants\GpropertyTypeNumbArray;
use models\constants\purposeOfLoanArray;
use models\Controllers\LMRequest\Property;
use models\cypher;
use models\lendingwise\tblPCHMLOBasicLoanBusinessCategories;
use models\lendingwise\tblPCHMLOBasicLoanLienPositions;
use models\lendingwise\tblProcessingCompany;
use models\servicing\LoanTerms;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Strings;

/**
 *
 */
class addBasicLoanTerms
{

    public function getFormHtml($inArray): string
    {

        global $allowPCUsersToMarketPlace,
               $allowPCUsersToMarketPlacePublic, $userRole,
               $userGroup;

        $formHtml1 = '';

        $siteSSLUrl = CONST_SITE_URL;
        $glEquipmentType = glEquipmentType::$glEquipmentType;
        $globalSBALoanProductsCat = globalSBALoanProductsCat::$globalSBALoanProductsCat;
        $globalMinTimeInBusinessCat = globalMinTimeInBusinessCat::$globalMinTimeInBusinessCat;
        $globalRecentNSFsCat = globalRecentNSFsCat::$globalRecentNSFsCat;
        $globalBankruptcyCat = globalBankruptcyCat::$globalBankruptcyCat;
        $globalBusinessBankruptcyCat = globalBusinessBankruptcyCat::$globalBusinessBankruptcyCat;
        $purposeOfLoanArray = purposeOfLoanArray::$purposeOfLoanArray;
        $gltypeOfHMLOLoanRequesting = gltypeOfHMLOLoanRequesting::$gltypeOfHMLOLoanRequesting;
        $glHMLOHouseType = glHMLOHouseType::$glHMLOHouseType;
        $glHMLOCreditScoreRange = glHMLOCreditScoreRange::$glHMLOCreditScoreRange;
        $glHMLOLoanTerms = glHMLOLoanTerms::$glHMLOLoanTerms;
        $glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;
        $glpaymentFrequency = glpaymentFrequency::$glpaymentFrequency;
        $glEntityTypeArray = glEntityTypeArray::$glEntityTypeArray;
        $glRateLockPeriod = glRateLockPeriod::$glRateLockPeriod;
        $loanPgm = [];
        $transactionType = [];
        $maxLoanAmount = '';
        $maxRate = '';
        $maxPoints = '';
        $propertyType = [];
        $futurePropertyType = [];
        $extnOption = [];
        $loanTerm = [];
        $occupancy = [];
        $state = [];
        $niches = [];
        $BLID = '';
        $UType = '';
        $UID = 0;
        $PCID = 0;
        $originationPointsRate = 0;
        $originationPointsValue = 0;
        $minLoanAmount = '';
        $minRate = '';
        $minPoints = '';
        $brokerPointsRate = 0;
        $brokerPointsValue = 0;
        $applicationFee = 0;
        $drawsFee = 0;
        $encryptedPCID = 0;
        $estdTitleClosingFee = 0;
        $processingFee = 0;
        $appraisalFee = 0;
        $drawsSetUpFee = 0;
        $encryptedBLID = 0;
        $miscellaneousFee = '';
        $closingCostFinanced = '';
        $resultArray = [];
        $maxCredit = '';
        $minCredit = '';
        $purchase = 0;
        $ratetermrefinance = 0;
        $cashoutrefinance = 0;
        $transactional = 0;
        $blanketloan = 0;
        $commercialpurchase = 0;
        $commercialrateterm = 0;
        $commercialrefinance = 0;
        $heloc = 0;
        $marketPlaceCompanyDetails = '';
        $tempTransactionType = [];
        $basicInfoArray = [];
        $loanPgmInfoArray = [];
        $transactionTypeInfoArray = [];
        $extnOptionInfoArray = [];
        $loanTermInfoArray = [];
        $loanOccupancyInfoArray = [];
        $loanStateInfoArray = [];
        $loanNichesInfoArray = [];
        $loanAmortizationInfoArray = [];
        $usedLoanPgmInfoArray = [];
        $PCNichesArray = $PCNichesArrayNewArray = [];
        $tempUsedLoanPgm = [];
        $loanPgmDetails = '';
        $PCBorrCreditScoreRangeArray = [];
        $PCBorrCreditScoreRange = '';
        $valuationBPOFee = $valuationAVMFee = $creditReportFee = $backgroundCheckFee = 0;
        $floodCertificateFee = $documentPreparationFee = $wireFee = $servicingSetUpFee = $taxServiceFee = $floodServiceFee = $inspectionFees = $projectFeasibility = 0;
        $dueDiligence = $UccLienSearch = $otherFee = $taxImpoundsMonth = $taxImpoundsMonthAmt = $taxImpoundsFee = $insImpoundsMonthAmt = $insImpoundsFee = 0;
        $closingCostFinancingFee = $attorneyFee = 0;
        $insImpoundsMonth = 0;
        $thirdPartyFees = 0;
        $payOffLiensCreditors = 0;
        $downPaymentPercentage = '';
        $rehabCostPercentageFinanced = '';
        $reqForLoanProUnderwriting = '';
        $marketPlaceContactInfoDetails = '';
        $recordingFee = 0;
        $escrowFees = 0;
        $propertyTax = 0;
        $underwritingFees = 0;
        $bufferAndMessengerFee = 0;
        $travelNotaryFee = 0;
        $prePaidInterest = $realEstateTaxes = $insurancePremium = $wireTransferFeeToTitle = $wireTransferFeeToEscrow = $pastDuePropertyTaxes = 0;
        $survey = 0;
        $wholeSaleAdminFee = 0;
        $marketPlaceLoanProgramInfoArray = [];
        $marketPlaceLoanProgranNotificationArray = [];
        $executiveInfoArray = [];
        $agentList = [];
        $employeeListArray = [];
        //Edit BLT hide
        $hideIfMarketPlaceYes = '';
        $enablePrivateMarketPlaceOnly = 0;

        $minMidFico = '0';
        $maxMidFico = '850';
        $minPropertyForFixFlop = '0';
        $minPropertyForGrndConst = '0';
        $marketPlaceFilesArray = [];
        $marketPlaceLinksArray = [];
        $BLModuleCode = '';
        $marketAnnualGrossSales = '';
        $marketMaxNSFsAllowed = '';
        $marketAverageBankBalance = '';
        $marketAvgTotalMonthlySale = '';
        $marketIsTherePrePaymentPenalty = '';
        $selRateLockPeriodVal = [];

        $PCID = $inArray['PCID'];
        $BLID = $inArray['BLID'];
        $UID = $inArray['UID'];
        $UType = $inArray['UType'];
        $randomNum = $inArray['randomNum'];

        if ($userGroup == 'Super') {
            $pcInfo = tblProcessingCompany::Get(['PCID' => $PCID]);
            $allowPCUsersToMarketPlacePublic = $pcInfo->allowPCToMarketPlacePublic;
            $allowPCUsersToMarketPlace = $pcInfo->allowPCToMarketPlace;
        }

        $ip = ['PCID' => $PCID];
        $glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange($PCID);

        $executiveInfo = getBranches::getReport($ip);
        $ip['externalBroker'] = 1;
        $agentList = getPCAgents::getReport($ip);
        $oContactsListArray = $oContactsList = $contactTypeArray = [];
        $ip['role'] = '22,5';
        $ip['userRole'] = $_SESSION['userRole'];
        $ip['userNumber'] = $_SESSION['userNumber'];
        $ip['userGroup'] = $_SESSION['userGroup'];
        $oContactsListArray = getContactList::getReport($ip);
        $oContactsList = $oContactsListArray['contacts'];

        $contactTypeArray = getMyContactType::getReport(['PCID' => $PCID]);
        $ip['activeStatus'] = 1;
        unset($ip['role']);
        $employeeListArray = getPCEmployeeList::getReport($ip);
        if ($PCID > 0) {
            $executiveInfoArray = $executiveInfo['branchList'];
        }

        $enableRehabConstruction = false;
        $businessCategories = [];
        $lienPositions = [];
        $exitStrategyInfo = [];

        if ($PCID > 0) {
            $ip['PCID'] = $PCID;
            $ip['BLID'] = $BLID;
            $resultArray = getPCBasicLoanInfo::getReport($ip);

            if (count($resultArray) > 0) {
                if (array_key_exists('basicInfo', $resultArray)) {
                    $basicInfoArray = $resultArray['basicInfo'];
                }
                if (array_key_exists('usedLoanPgm', $resultArray)) {
                    $usedLoanPgmInfoArray = $resultArray['usedLoanPgm'];
                    foreach ($usedLoanPgmInfoArray as $eachLoanProgram) {
                        $tempUsedLoanPgm[] = $eachLoanProgram['loanPgm'];
                    }
                }

                $loanPgmInfoArray = $resultArray['loanPgmInfo'];
                $transactionTypeInfoArray = $resultArray['transactionTypeInfo'];
                $loanPropertyTypeInfoArray = $resultArray['loanPropertyTypeInfo'] ?? [];
                $loanFuturePropertyTypeInfoArray = $resultArray['loanFuturePropertyTypeInfo'] ?? [];
                $extnOptionInfoArray = $resultArray['extnOptionInfo'];
                $loanTermInfoArray = $resultArray['loanTermInfo'];
                $loanOccupancyInfoArray = $resultArray['loanOccupancyInfo'];
                $loanStateInfoArray = $resultArray['loanStateInfo'];
                $loanNichesInfoArray = $resultArray['loanNichesInfo'];
                $PCNichesArray = $resultArray['glNiches'];
                $entityTypeInfoArray = $resultArray['loanEntityTypeInfo'];

                $marketPlaceLoanProgramInfoArray = $resultArray['marketPlaceLoanProgramInfo'];
                $marketPlaceLoanProgranNotificationArray = $resultArray['marketPlaceLoanProgranNotification'];
                $marketPlaceLoanProgranCustomNotificationArray = $resultArray['marketPlaceLoanProgranCustomNotification'];
                $loanAmortizationInfoArray = $resultArray['loanAmortizationInfo'];


                $marketSBALoanProductArray = $resultArray['marketSBALoanProduct'];
                $marketEquipmentTypeArray = $resultArray['marketEquipmentType'];
                $marketEntityStateOfFormationArray = $resultArray['marketEntityStateOfFormation'];
                $marketeElgibleStateArray = $resultArray['marketeElgibleState'];
                $marketPaymentFrequencyArray = $resultArray['marketPaymentFrequency'];
                $marketplaceMinSeasoningPersonalBankruptcyArray = $resultArray['marketplaceMinSeasoningPersonalBankruptcy'];
                $marketplaceMinSeasoningBusinessBankruptcyArray = $resultArray['marketplaceMinSeasoningBusinessBankruptcy'];
                $marketplaceMinSeasoningForeclosureArray = $resultArray['marketplaceMinSeasoningForeclosure'];
                $marketplaceLoanPurposeArray = $resultArray['marketplaceLoanPurpose'];
                $marketMinTimeInBusinessArray = $resultArray['marketMinTimeInBusiness'];


                $marketPlaceFilesArray = $resultArray['marketPlaceFiles'][$BLID];
                $marketPlaceLinksArray = $resultArray['marketPlaceLinks'][$BLID];
                $exitStrategyInfo = $resultArray['exitStrategyInfo'][$BLID];

                $rateLockPeriodInfoArray = $resultArray['rateLockPeriodInfo'];

                $minDSCR = null;

                if ($BLID > 0) {
                    foreach ($basicInfoArray as $eachBasicInfo) {
                        $loanPgm = [];
                        $transactionType = [];
                        $minLoanAmount = '';
                        $minRate = '';
                        $minPoints = '';
                        $propertyType = [];
                        $futurePropertyType = [];
                        $extnOption = [];
                        $loanTerm = [];
                        $occupancy = [];
                        $state = [];
                        $niches = [];
                        $Amortization = [];
                        $originationPointsRate = 0;
                        $originationPointsValue = 0;
                        $tempPropertyType = [];
                        $brokerPointsRate = 0;
                        $brokerPointsValue = 0;
                        $applicationFee = 0;
                        $drawsFee = 0;
                        $tempExtnOption = [];
                        $estdTitleClosingFee = 0;
                        $processingFee = 0;
                        $appraisalFee = 0;
                        $drawsSetUpFee = 0;
                        $tempNiches = [];
                        $miscellaneousFee = 0;
                        $closingCostFinanced = 0;
                        $loanProgram = [];
                        $tempTransactionType = [];
                        $loanPgmDetails = '';
                        $loanTermInfo = $loanOccupancyInfo = [];
                        $tempState = [];
                        $maxLoanAmount = '';
                        $maxRate = '';
                        $maxPoints = '';
                        $PCBorrCreditScoreRange = '';
                        $PCBorrCreditScoreRangeArray = [];
                        $valuationBPOFee = $valuationAVMFee = $creditReportFee = $backgroundCheckFee = 0;
                        $floodCertificateFee = $documentPreparationFee = $wireFee = $servicingSetUpFee = $taxServiceFee = $floodServiceFee = $inspectionFees = $projectFeasibility = 0;
                        $dueDiligence = $UccLienSearch = $otherFee = $taxImpoundsMonth = $taxImpoundsMonthAmt = $taxImpoundsFee = $insImpoundsMonthAmt = $insImpoundsFee = 0;
                        $insImpoundsMonth = 0;
                        $thirdPartyFees = 0;
                        $closingCostFinancingFee = $attorneyFee = 0;
                        $payOffLiensCreditors = 0;
                        $minMidFico = '0';
                        $minDSCR = null;
                        $maxMidFico = '850';
                        $minPropertyForFixFlop = '0';
                        $minPropertyForGrndConst = '0';
                        $maxPropertyForGrndConst = '';
                        $totalLTC = '';
                        $rateLockPeriod = [];
                        $marketLoanProgramSelected = [];

                        $enableRehabConstruction = $eachBasicInfo['enableRehabConstruction'];
                        $minLoanAmount = Currency::formatDollarAmountWithDecimal($eachBasicInfo['minLoanAmount']);
                        $minRate = Currency::formatDollarAmountWithDecimal($eachBasicInfo['minRate']);
                        $minPoints = ($eachBasicInfo['minPoints']);
                        $maxLoanAmount = trim(Currency::formatDollarAmountWithDecimal($eachBasicInfo['maxLoanAmount']));
                        $maxRate = Currency::formatDollarAmountWithDecimal($eachBasicInfo['maxRate']);
                        $maxPoints = ($eachBasicInfo['maxPoints']);
                        $originationPointsRate = $eachBasicInfo['originationPointsRate'];
                        $originationPointsValue = Currency::formatDollarAmountWithDecimal($eachBasicInfo['originationPointsValue']);
                        $brokerPointsRate = $eachBasicInfo['brokerPointsRate'];
                        $brokerPointsValue = Currency::formatDollarAmountWithDecimal($eachBasicInfo['brokerPointsValue']);
                        $applicationFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['applicationFee']);
                        $estdTitleClosingFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['estdTitleClosingFee']);
                        $processingFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['processingFee']);
                        $appraisalFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['appraisalFee']);
                        $drawsSetUpFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['drawsSetUpFee']);
                        $drawsFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['drawsFee']);
                        $miscellaneousFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['miscellaneousFee']);
                        $closingCostFinanced = Currency::formatDollarAmountWithDecimal($eachBasicInfo['closingCostFinanced']);
                        /** New Fees Added PT# - 156793877 **/
                        $valuationBPOFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['valuationBPOFee']);
                        $valuationAVMFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['valuationAVMFee']);
                        $creditReportFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['creditReportFee']);
                        $backgroundCheckFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['backgroundCheckFee']);
                        $floodCertificateFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['floodCertificateFee']);
                        $documentPreparationFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['documentPreparationFee']);
                        $wireFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['wireFee']);
                        $servicingSetUpFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['servicingSetUpFee']);
                        $taxServiceFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['taxServiceFee']);
                        $floodServiceFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['floodServiceFee']);
                        $inspectionFees = Currency::formatDollarAmountWithDecimal($eachBasicInfo['inspectionFees']);
                        $projectFeasibility = Currency::formatDollarAmountWithDecimal($eachBasicInfo['projectFeasibility']);
                        $dueDiligence = Currency::formatDollarAmountWithDecimal($eachBasicInfo['dueDiligence']);
                        $UccLienSearch = Currency::formatDollarAmountWithDecimal($eachBasicInfo['UccLienSearch']);
                        $otherFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['otherFee']);
                        $taxImpoundsMonth = Currency::formatDollarAmountWithDecimal($eachBasicInfo['taxImpoundsMonth']);
                        $taxImpoundsMonthAmt = Currency::formatDollarAmountWithDecimal($eachBasicInfo['taxImpoundsMonthAmt']);
                        $taxImpoundsFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['taxImpoundsFee']);
                        $insImpoundsMonthAmt = Currency::formatDollarAmountWithDecimal($eachBasicInfo['insImpoundsMonthAmt']);
                        $insImpoundsFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['insImpoundsFee']);
                        $thirdPartyFees = Currency::formatDollarAmountWithDecimal($eachBasicInfo['thirdPartyFees']);
                        $insImpoundsMonth = Currency::formatDollarAmountWithDecimal($eachBasicInfo['insImpoundsMonth']);
                        $closingCostFinancingFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['closingCostFinancingFee']);
                        $attorneyFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['attorneyFee']);
                        $escrowFees = Currency::formatDollarAmountWithDecimal($eachBasicInfo['escrowFees']);
                        $recordingFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['recordingFee']);
                        $underwritingFees = Currency::formatDollarAmountWithDecimal($eachBasicInfo['underwritingFees']);
                        $propertyTax = Currency::formatDollarAmountWithDecimal($eachBasicInfo['propertyTax']);
                        $prePaidInterest = Currency::formatDollarAmountWithDecimal($eachBasicInfo['prePaidInterest']);
                        $realEstateTaxes = Currency::formatDollarAmountWithDecimal($eachBasicInfo['realEstateTaxes']);
                        $insurancePremium = Currency::formatDollarAmountWithDecimal($eachBasicInfo['insurancePremium']);
                        $payOffLiensCreditors = Currency::formatDollarAmountWithDecimal($eachBasicInfo['payOffLiensCreditors']);
                        $wireTransferFeeToTitle = Currency::formatDollarAmountWithDecimal($eachBasicInfo['wireTransferFeeToTitle']);
                        $wireTransferFeeToEscrow = Currency::formatDollarAmountWithDecimal($eachBasicInfo['wireTransferFeeToEscrow']);
                        $titleInsuranceFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['titleInsuranceFee']);
                        $pastDuePropertyTaxes = Currency::formatDollarAmountWithDecimal($eachBasicInfo['pastDuePropertyTaxes']);
                        $bufferAndMessengerFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['bufferAndMessengerFee']);
                        $travelNotaryFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['travelNotaryFee']);
                        $survey = Currency::formatDollarAmountWithDecimal($eachBasicInfo['survey']);
                        $wholeSaleAdminFee = Currency::formatDollarAmountWithDecimal($eachBasicInfo['wholeSaleAdminFee']);


                        $BLModuleCode = ($eachBasicInfo['moduleCode']);
                        $marketAnnualGrossSales = Currency::formatDollarAmountWithDecimal($eachBasicInfo['marketAnnualGrossSales']);
                        $marketMaxNSFsAllowed = ($eachBasicInfo['marketMaxNSFsAllowed']);
                        $marketAverageBankBalance = Currency::formatDollarAmountWithDecimal($eachBasicInfo['marketAverageBankBalance']);
                        $marketAvgTotalMonthlySale = Currency::formatDollarAmountWithDecimal($eachBasicInfo['marketAvgTotalMonthlySale']);
                        $marketIsTherePrePaymentPenalty = ($eachBasicInfo['marketIsTherePrePaymentPenalty']);
                        $marketPlaceCompanyDetails = trim(urldecode($eachBasicInfo['marketPlaceCompanyDetails']));
                        $enablePrivateMarketPlaceOnly = ($eachBasicInfo['enablePrivateMarketPlaceOnly']);

                        $downPaymentPercentage = $eachBasicInfo['downPaymentPercentage'];
                        $rehabCostPercentageFinanced = $eachBasicInfo['rehabCostPercentageFinanced'];
                        $loanPgmDetails = trim(urldecode($eachBasicInfo['loanPgmDetails']));
                        $reqForLoanProUnderwriting = urldecode((trim($eachBasicInfo['reqForLoanProUnderwriting'])));
                        $marketPlaceContactInfoDetails = urldecode((trim($eachBasicInfo['marketPlaceContactInfoDetails'])));
                        $BLID = trim($eachBasicInfo['BLID']);
                        $PCBorrCreditScoreRange = trim($eachBasicInfo['PCBorrCreditScoreRange']);
                        $totalLTC = $eachBasicInfo['totalLTC'];
                        $minMidFico = trim($eachBasicInfo['minMidFico']);
                        $minDSCR = trim($eachBasicInfo['minDSCR']);
                        $maxMidFico = trim($eachBasicInfo['maxMidFico']);
                        $minPropertyForFixFlop = trim($eachBasicInfo['minPropertyForFixFlop']);
                        $maxPropertyForFixFlop = trim($eachBasicInfo['maxPropertyForFixFlop']);
                        $minPropertyForGrndConst = trim($eachBasicInfo['minPropertyForGrndConst']);
                        $maxPropertyForGrndConst = trim($eachBasicInfo['maxPropertyForGrndConst']);
                        if ($PCBorrCreditScoreRange != '') {
                            $PCBorrCreditScoreRangeArray = explode(',', $PCBorrCreditScoreRange);
                        }
                        if (count($loanPgmInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanPgmInfoArray)) $loanProgram = $loanPgmInfoArray[$BLID];
                            for ($l = 0; $l < count($loanProgram); $l++) {
                                $loanPgm[] = trim($loanProgram[$l]['loanPgm']);
                            }
                        }
                        if (count($loanPropertyTypeInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanPropertyTypeInfoArray)) $tempPropertyType = $loanPropertyTypeInfoArray[$BLID];
                            foreach ($tempPropertyType as $tempPropertyTypeVal) {
                                $propertyType[] = trim($tempPropertyTypeVal['propertyType']);
                            }
                        }
                        if (count($loanFuturePropertyTypeInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanFuturePropertyTypeInfoArray)) $tempPropertyType = $loanFuturePropertyTypeInfoArray[$BLID];
                            foreach ($tempPropertyType as $tempPropertyTypeVal) {
                                $futurePropertyType[] = trim($tempPropertyTypeVal['futurePropertyType']);
                            }
                        }
                        if (count($entityTypeInfoArray ?? []) > 0) {
                            foreach ($entityTypeInfoArray as $etVal) {
                                $entityTypeData[] = $etVal['entityType'];
                            }
                        }
                        if (count($extnOptionInfoArray) > 0) {
                            if (array_key_exists($BLID, $extnOptionInfoArray)) $tempExtnOption = $extnOptionInfoArray[$BLID];
                            for ($l = 0; $l < count($tempExtnOption); $l++) {
                                $extnOption[] = trim($tempExtnOption[$l]['extnOption']);
                            }
                        }
                        if (count($transactionTypeInfoArray) > 0) {
                            if (array_key_exists($BLID, $transactionTypeInfoArray)) $tempTransactionType = $transactionTypeInfoArray[$BLID];
                            for ($l = 0; $l < count($tempTransactionType); $l++) {
                                $transactionType[] = trim($tempTransactionType[$l]['transactionType']);
                            }
                        }
                        /*Marketplace Loan Program Array	*/
                        if (count($marketPlaceLoanProgramInfoArray) > 0) {
                            if (array_key_exists($BLID, $marketPlaceLoanProgramInfoArray)) $tempMarketPlaceLoan = $marketPlaceLoanProgramInfoArray[$BLID];
                            for ($l = 0; $l < count($tempMarketPlaceLoan); $l++) {
                                $marketLoanProgramSelected[] = trim($tempMarketPlaceLoan[$l]['MPLID']);
                            }
                        }
                        /* */
                        /** @var  $marketLoanProgramNotificationSelected */
                        $marketLoanProgramNotificationSelected = [];
                        $tempMarketPlaceLoanProgranNotification = [];
                        if (count($marketPlaceLoanProgranNotificationArray) > 0) {
                            if (array_key_exists($BLID, $marketPlaceLoanProgranNotificationArray)) $tempMarketPlaceLoanProgranNotification = $marketPlaceLoanProgranNotificationArray[$BLID];
                            for ($l = 0; $l < count($tempMarketPlaceLoanProgranNotification); $l++) {
                                $marketLoanProgramNotificationSelected[trim($tempMarketPlaceLoanProgranNotification[$l]['NotificationuserGroup'])][] = trim($tempMarketPlaceLoanProgranNotification[$l]['NotificationUserId']);
                            }
                        }
                        /*** end**/
                        $marketLoanProgramCustomNotificationSelected = [];
                        $tempMarketPlaceLoanProgranCustomNotification = [];
                        $customEmailIdsNotify = '';
                        if (count($marketPlaceLoanProgranCustomNotificationArray) > 0) {
                            if (array_key_exists($BLID, $marketPlaceLoanProgranCustomNotificationArray)) $tempMarketPlaceLoanProgranCustomNotification = $marketPlaceLoanProgranCustomNotificationArray[$BLID];
                            for ($l = 0; $l < count($tempMarketPlaceLoanProgranCustomNotification); $l++) {
                                $customEmailIdsNotify = trim($tempMarketPlaceLoanProgranCustomNotification[$l]['customEmailIdsNotify']);
                            }
                        }
                        if (count($loanTermInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanTermInfoArray)) $loanTermInfo = $loanTermInfoArray[$BLID];
                            for ($l = 0; $l < count($loanTermInfo); $l++) {
                                $loanTerm[] = trim($loanTermInfo[$l]['loanTerm']);
                            }
                        }
                        if (count($loanOccupancyInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanOccupancyInfoArray)) $loanOccupancyInfo = $loanOccupancyInfoArray[$BLID];
                            for ($l = 0; $l < count($loanOccupancyInfo); $l++) {
                                $occupancy[] = trim($loanOccupancyInfo[$l]['occupancy']);
                            }
                        }
                        if (count($loanNichesInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanNichesInfoArray)) $tempNiches = $loanNichesInfoArray[$BLID];
                            for ($l = 0; $l < count($tempNiches); $l++) {
                                $niches[] = trim($tempNiches[$l]['nichesID']);
                            }
                        }
                        if (count($loanAmortizationInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanAmortizationInfoArray)) $tempAmortization = $loanAmortizationInfoArray[$BLID];
                            for ($l = 0; $l < count($tempAmortization); $l++) {
                                $Amortization[] = trim($tempAmortization[$l]['AmortizationVal']);
                            }
                        }
                        if (count($loanStateInfoArray) > 0) {
                            if (array_key_exists($BLID, $loanStateInfoArray)) $tempState = $loanStateInfoArray[$BLID];
                            for ($l = 0; $l < count($tempState); $l++) {
                                $state[] = trim($tempState[$l]['stateCode']);
                            }
                        }
                        if (count($marketSBALoanProductArray) > 0) {
                            if (array_key_exists($BLID, $marketSBALoanProductArray)) $tempSBALoanProduct = $marketSBALoanProductArray[$BLID];
                            for ($l = 0; $l < count($tempSBALoanProduct); $l++) {
                                $selectedSBALoanProducts[] = trim($tempSBALoanProduct[$l]['SBALoanProductVal']);
                            }
                        }
                        if (count($marketEquipmentTypeArray) > 0) {
                            if (array_key_exists($BLID, $marketEquipmentTypeArray)) $tempEquipmentType = $marketEquipmentTypeArray[$BLID];
                            for ($l = 0; $l < count($tempEquipmentType); $l++) {
                                $selectedEquipmentTypeVals[] = trim($tempEquipmentType[$l]['equipmentTypeVal']);
                            }
                        }
                        if (count($marketEntityStateOfFormationArray) > 0) {
                            if (array_key_exists($BLID, $marketEntityStateOfFormationArray)) $tempEntityState = $marketEntityStateOfFormationArray[$BLID];
                            for ($l = 0; $l < count($tempEntityState); $l++) {
                                $selectedEntityStateFormation[] = trim($tempEntityState[$l]['sateCode']);
                            }
                        }
                        if (count($marketeElgibleStateArray) > 0) {
                            if (array_key_exists($BLID, $marketeElgibleStateArray)) $tempElgibleState = $marketeElgibleStateArray[$BLID];
                            for ($l = 0; $l < count($tempElgibleState); $l++) {
                                $selectedElgibleStates[] = trim($tempElgibleState[$l]['stateCode']);
                            }
                        }
                        if (count($marketPaymentFrequencyArray) > 0) {
                            if (array_key_exists($BLID, $marketPaymentFrequencyArray)) $tempPaymentFreq = $marketPaymentFrequencyArray[$BLID];
                            for ($l = 0; $l < count($tempPaymentFreq); $l++) {
                                $selectedPaymentFrequency[] = trim($tempPaymentFreq[$l]['paymentFrequencyVal']);
                            }
                        }
                        if (count($marketplaceMinSeasoningPersonalBankruptcyArray) > 0) {
                            if (array_key_exists($BLID, $marketplaceMinSeasoningPersonalBankruptcyArray)) $tempMinSePer = $marketplaceMinSeasoningPersonalBankruptcyArray[$BLID];
                            for ($l = 0; $l < count($tempMinSePer); $l++) {
                                $selectedMinSesPersonalBan[] = trim($tempMinSePer[$l]['MinSeasoningPersonalBankruptcyVal']);
                            }
                        }
                        if (count($marketplaceMinSeasoningBusinessBankruptcyArray) > 0) {
                            if (array_key_exists($BLID, $marketplaceMinSeasoningBusinessBankruptcyArray)) $tempMinSeBus = $marketplaceMinSeasoningBusinessBankruptcyArray[$BLID];
                            for ($l = 0; $l < count($tempMinSeBus); $l++) {
                                $selectedMinSesBusinessBan[] = trim($tempMinSeBus[$l]['MinSeasoningBusinessBankruptcyVal']);
                            }
                        }
                        if (count($marketplaceMinSeasoningForeclosureArray) > 0) {
                            if (array_key_exists($BLID, $marketplaceMinSeasoningForeclosureArray)) $tempMinSesFore = $marketplaceMinSeasoningForeclosureArray[$BLID];
                            for ($l = 0; $l < count($tempMinSesFore); $l++) {
                                $selectedMinSesFore[] = trim($tempMinSesFore[$l]['MinSeasoningForeclosureVal']);
                            }
                        }

                        if (count($marketplaceLoanPurposeArray) > 0) {
                            if (array_key_exists($BLID, $marketplaceLoanPurposeArray)) $tempLoanPurpose = $marketplaceLoanPurposeArray[$BLID];
                            for ($l = 0; $l < count($tempLoanPurpose); $l++) {
                                $selectedLoanPurpose[] = trim($tempLoanPurpose[$l]['purposeName']);
                            }
                        }
                        if (count($marketMinTimeInBusinessArray) > 0) {
                            if (array_key_exists($BLID, $marketMinTimeInBusinessArray)) $tempMinTimeInBusiness = $marketMinTimeInBusinessArray[$BLID];
                            for ($l = 0; $l < count($tempMinTimeInBusiness); $l++) {
                                $selectedMinTimeInBusiness[] = trim($tempMinTimeInBusiness[$l]['minTimeVal']);
                            }
                        }
                        //Rate Lock Period
                        if (count($rateLockPeriodInfoArray) > 0) {
                            if (array_key_exists($BLID, $rateLockPeriodInfoArray)) $rateLockPeriod = $rateLockPeriodInfoArray[$BLID];
                            foreach ($rateLockPeriod as $eachRateLockPeriod) {
                                $selRateLockPeriodVal[] = $eachRateLockPeriod['rateLockPeriod'];
                            }
                        }
                    }
                }
            }

            $tblPCHMLOBasicLoanBusinessCategories = tblPCHMLOBasicLoanBusinessCategories::GetAll([
                'BLID' => $BLID,
            ]);
            $businessCategories = array_map(function ($item) {
                return $item->businessCategory;
            }, $tblPCHMLOBasicLoanBusinessCategories);

            $tblPCHMLOBasicLoanLienPositions = tblPCHMLOBasicLoanLienPositions::GetAll([
                'BLID' => $BLID,
            ]);
            $lienPositions = array_map(function ($item) {
                return $item->lienPosition;
            }, $tblPCHMLOBasicLoanLienPositions);
        }

        $encryptedPCID = cypher::myEncryption($PCID);
        $encryptedBLID = cypher::myEncryption($BLID);
        $stateArray = Arrays::fetchStates();
        /** Fetch all States **/

        /*   if ($reqForLoanProUnderwriting == '') {
            //   $reqForLoanProUnderwriting = preliminaryApproval();
           }*/

        /* Get Modules */
        $ipInfo = [];
        $ipInfo['keyNeeded'] = 'n';
        $ipInfo['PCID'] = $PCID;
        $moduleRequested = getPCModules::getReport($ipInfo);
        /* End of Get Modules */

        $PCLMRClientTypeInfoArray = [];
        if ($PCID > 0) {
            $ip['PCID'] = $PCID;
            if ($BLModuleCode != '') {
                $ip['moduleCode'] = $BLModuleCode;
            } else {
                $ip['moduleCode'] = 'HMLO';
                $BLModuleCode = 'HMLO';
            }
            $ip['keyNeeded'] = 'n';
            $ip['getInternalLoanPrograms'] = '1';
            $PCLMRClientTypeInfoArray = getPCServiceType::getReport($ip);  /* get Service Types / Loan Programs */
            $marketPlaceLPArray = getMarketPlaceLoanPrograms::getReport(['active' => 1, 'moduleCode' => $ip['moduleCode']]); /* get Marketplace Loan Programs */
            $PCNichesArrayNewArray = getPCNiches::getReport(['PCID' => $PCID, 'moduleCode' => $ip['moduleCode']]);
        }

        $jsVr = CONST_JS_VERSION;
        $saveUrl = CONST_URL_POPS . 'saveBasicLoanTerms.php';
        $formHtml1 .= <<<EOT
<script type="text/javascript" src="/assets/js/3rdParty/tinymce-5.6.1/tinymce.bundle.js?$jsVr"></script>
<script type="text/javascript" src="/assets/js/popup.js?$jsVr"></script>
<script type="text/javascript" src="/assets/js/models/Dates.js?$jsVr"></script>
<script type="text/javascript" src="/assets/js/models/formValue.js?$jsVr"></script>
<script type="text/javascript" src="/assets/js/fileCommon.js?$jsVr"></script>
<script>
    function initMCEall(){
        tinymce.init({
            selector: '.tinyMceClassB',
            forced_root_block: 'div',
            content_style: "body {font-size: 12pt;}",
            plugins: 'advlist autolink link image paste lists charmap print preview code table hr',
            menubar: false,
            toolbar: ['styleselect | fontselect | fontsizeselect | undo redo | cut copy paste | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent | blockquote subscript superscript | advlist | autolink | code | table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | hr'],
            browser_spellcheck: true,
            contextmenu: false,
            //contextmenu false - this makes it so you can right click on misspelled and get correction suggestion, without this tinymce wants to make it a hyperlink
            image_title: true,
            file_picker_types: 'image',
            width: '100%',
            height: 200,
        });
    }
    // add all tes to document
    initMCEall();
</script>
<form name="addBasicLoanTermForm" id="addBasicLoanTermForm" method="POST"   autocomplete="off" action="$saveUrl" enctype="multipart/form-data" onsubmit="return savePCBasicLoanTerm();">
            <input type="hidden" name="PCID" id="PCID" value="$PCID" />
            <input type="hidden" name="BLID" id="BLID" value="$encryptedBLID" />
            <input type="hidden" name="encryptedPCID" id="encryptedPCID" value="$encryptedPCID" />
EOT;
        $marketPlaceUnchecked = '';
        $marketPlaceStatus = '';
        $hideForLOC = $hideForHMLO = ' hidden ';
        $disableForLOC = $disableForHMLO = '';
        $disableSelectForLOC = $disableSelectForHMLO = '';
        $marketPlaceHMLOMinFixFlip = '';
        $marketPlaceHMLOGrounCons = '';
        if ($BLID > 0) {
            if (count($marketLoanProgramSelected) > 0) {
                $temDisableSelect = '';
                $marketLoanProgramChecked = ' checked ';
                $marketPlaceStatus = '';
                $hideIfMarketPlaceYes = ' hidden ';
                $marketPlaceHMLOMinFixFlip = ' hidden ';
                $marketPlaceHMLOGrounCons = ' hidden ';
            } else {
                /* New Custom Loan Guidlines */
                $temDisableSelect = ' disabled readonly=""  ';
                $marketLoanProgramChecked = ' ';
                $marketPlaceUnchecked = ' checked ';
                $hideIfMarketPlaceYes = '';
                $marketPlaceHMLOMinFixFlip = '';
                $marketPlaceHMLOGrounCons = '';
                $marketPlaceStatus = ' hidden ';
            }
            if ($BLModuleCode == 'loc') {
                $hideForLOC = ' hidden ';
                $hideForHMLO = ' ';
                $disableForLOC = ' disabled ';
                $disableForHMLO = '';
                $disableSelectForLOC = ' disabled readonly=""  ';
                $disableSelectForHMLO = '';
                $marketPlaceHMLOMinFixFlip = 'hidden';
                $marketPlaceHMLOGrounCons = 'hidden';

            }
            if ($BLModuleCode == 'HMLO') {
                $hideForHMLO = ' hidden ';
                $hideForLOC = '';
                $disableForHMLO = ' disabled ';
                $disableForLOC = '';
                $disableSelectForHMLO = ' disabled readonly=""  ';
                $disableSelectForLOC = '';
            }
        } else {
            /* New Custom Loan Guidlines */
            $temDisableSelect = ' disabled readonly=""  ';
            $marketLoanProgramChecked = '';
            $marketPlaceUnchecked = ' ';
            $hideIfMarketPlaceYes = '';
            $marketPlaceHMLOMinFixFlip = '';
            $marketPlaceHMLOGrounCons = '';
            $marketPlaceStatus = ' hidden ';
            $hideForLOC = '';
        }

        if ($allowPCUsersToMarketPlace) {
            $formHtml1 .= <<<EOT
                 <div class=" row ">
                  <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5  font-weight-bold align-self-start ">
                             Enable Marketplace Loan? <i class="fa fa-info-circle text-primary popoverClass"  data-html="true"  data-content="If Yes, the loan programs(s) will display in the marketplace tab of the loan file for users with Marketplace permission enabled. if your company is part of the LendingWise Capital network,it will display globally for all users on the platform."></i>
                            </label>
                            <div class="col-md-7">
                                <input type="radio" name="enableMarketPlaceForGuidelines" {$marketLoanProgramChecked} class="enableMarketPlaceForGuidelines" value="1" > Yes
                                <input type="radio" name="enableMarketPlaceForGuidelines" {$marketPlaceUnchecked} class="enableMarketPlaceForGuidelines" value="0" > No
                            </div>
                        </div>
                    </div>
                </div>
EOT;
            if ($allowPCUsersToMarketPlacePublic) {
                $enablePrivateMarketPlaceOnlyChecked = '';
                $enablePrivateMarketPlaceOnlyUnChecked = '';
                if ($enablePrivateMarketPlaceOnly) {
                    $enablePrivateMarketPlaceOnlyChecked = ' checked ';
                } else {
                    $enablePrivateMarketPlaceOnlyUnChecked = ' checked ';
                }
                $formHtml1 .= <<<EOT
                 <div class=" row marketPlaceDepenShowHide {$marketPlaceStatus} ">
                  <div class="col-md-6">
                        <div class="form-group row">
                            <label class="col-md-5  font-weight-bold align-self-start ">
                             Private Marketplace Only? <i class="fa fa-info-circle text-primary popoverClass"  data-html="true"  
                             data-content=" Setting to private, means that this loan program will only show up in your marketplace only. If you set to NOT private, and the loan program is enabled for MarketPlace then it will display for your company & all global users of LendingWise that have the Marketplace feature enabled."></i>
                            </label>
                            <div class="col-md-7">
                                <input type="radio" name="enablePrivateMarketPlaceOnly" {$enablePrivateMarketPlaceOnlyChecked} class="enablePrivateMarketPlaceOnly" value="1"> Yes
                                <input type="radio" name="enablePrivateMarketPlaceOnly" {$enablePrivateMarketPlaceOnlyUnChecked} class="enablePrivateMarketPlaceOnly" value="0"> No
                            </div>
                        </div>
                    </div>
                </div>
EOT;
            }
        }
        $formHtml1 .= <<<EOT
 <div class=" row  ">			 
        <label class="col-lg-12  bg-secondary  py-2  m-0 mb-4 py-2"><b>Setup</b> <i class="fa fa-info-circle text-primary popoverClass"  data-html="true"  data-content="Please select which File Types and Loan Programs this Guideline Rule Set Will Apply To."></i></label>
		 <div class="col-md-6 ">
		 		 <div class=" row form-group">
                            <label class="col-md-5  font-weight-bold align-self-start " for="moduleCode">File Type : </label>
                            <div class="col-md-7">
                                        <select class="mandatory form-control chzn-select" 
data-placeholder="Select File Type" name="moduleCode" id="moduleCode" onchange="getPCServiceTypes(this.value,'$PCID');getMarketPlaceLoanPrograms(this.value);getPCNiches(this.value);" >
            <option value=""></option>
EOT;
        foreach ($moduleRequested as $eachModule) {
            $eachModuleCode = $moduleName = $chk = '';
            $eachModuleCode = trim($eachModule['moduleCode']);
            if (in_array($eachModuleCode, ['HMLO', 'loc'])) {
                $moduleName = trim($eachModule['moduleName']);
                if ($eachModuleCode == $BLModuleCode) {
                    $chk = 'selected ';
                }
                $formHtml1 .= <<<EOT
        <option $chk value="$eachModuleCode">$moduleName</option>
EOT;
            }
        }
        $formHtml1 .= <<<EOT
                                        </select>
                            </div>
                            </div>
                            </div>
              
            <div class="col-md-6 ">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Loan Program : <i class="fa fa-info-circle text-primary popoverClass"  data-html="true" data-content="These are the loan programs selected or created on the company level. The guidelines below will be associated to the programs you select here."></i>
                    </label>
                    <div class="col-md-7">                        
						<select  data-placeholder="- Select Loan Program -" name="loanPgm[]" id="loanPgm" class="chzn-select mandatory form-control" multiple="" >
EOT;
        $tempUsedLoanPgmJsonEncoded = json_encode($tempUsedLoanPgm);
        for ($j = 0; $j < count($PCLMRClientTypeInfoArray); $j++) {
            $serviceCode = '';
            $serviceType = '';
            $internalLoanProgram = 0;
            $serviceCode = trim($PCLMRClientTypeInfoArray[$j]['LMRClientType']);
            $serviceType = trim($PCLMRClientTypeInfoArray[$j]['serviceType']);
            $internalLoanProgram = trim($PCLMRClientTypeInfoArray[$j]['internalLoanProgram']);
            if ($internalLoanProgram == '1') {
                $optionColor = 'style="color: red;"';
            } else {
                $optionColor = '';
            }
            $sel = '';
            $sel = Arrays::isSelectedArray($loanPgm, $serviceCode);
            if (!in_array($serviceCode, $tempUsedLoanPgm)) {
                $formHtml1 .= <<<EOT
                    <option $optionColor value="$serviceCode" $sel>$serviceType</option>
EOT;
            }
        }
        $formHtml1 .= <<<EOT
						</select>                
                    </div>
                </div>
            </div>
<input type="hidden" id="tempUsedLoanPgm" value='{$tempUsedLoanPgmJsonEncoded}'>
        </div>
EOT;

        if ($allowPCUsersToMarketPlace) {
            $formHtml1 .= <<<EOT
<div class="row marketPlaceDepenShowHide {$marketPlaceStatus}  ">
            <div class="col-md-6 ">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       Marketplace Loan Category  <i class="fa fa-info-circle text-primary popoverClass"  data-html="true"  data-content="These Programs are the main categories to better organize the loan programs and to help match loans more accurately."></i>
                    </label>
                        <div class="col-md-7">
EOT;
            $formHtml1 .= <<<EOT
                        <select  data-placeholder="- Select Market Loan Program-" name="marketLoanProgram[]" id="marketLoanProgram"  {$temDisableSelect} class="chzn-select" multiple="" onchange="changeMarketLoanProgram();">
EOT;
            for ($i = 0; $i < count($marketPlaceLPArray); $i++) {
                $sOpt = '';
                $typeOfLoan = '';
                $typeOfLoanId = '';
                $typeOfLoan = trim($marketPlaceLPArray[$i]['LoanProgramName']);
                $typeOfLoanId = trim($marketPlaceLPArray[$i]['MPLID']);
                $sOpt = Arrays::isSelectedArray($marketLoanProgramSelected, $typeOfLoanId);
                if (trim($sOpt) != '') {
                    $marketLoanProgramSelectedText[] = $typeOfLoan;
                }
                $formHtml1 .= <<<EOT
                                                        <option value="$typeOfLoanId" $sOpt>$typeOfLoan</option>
EOT;
            }
            $formHtml1 .= <<<EOT
                        </select>
                    </div>
                </div>
            </div>
        </div>
EOT;
        }

        $formHtml1 .= <<<EOT
<!-- Start Available Fields -->
		<div class="row ">		
		     <label class="col-lg-12  bg-secondary  py-2  m-0 mb-4 py-2"><b>Configure Available Values in Dropdown Fields</b>
		        <i class="fa fa-info-circle text-primary popoverClass"  data-html="true"  data-content="You can control the dropdown values in the listed form fields."></i>
		     </label>
		     
		                <div class="col-md-6 HMLOClass {$hideForLOC} "> 
                <div class="form-group row   ">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       Transaction Type  <i class="fa fa-info-circle text-primary popoverClass"  data-html="true" data-content="Transaction types are like loan purpose and have the following hard coded conditional logic: \nPurchase & Commercial Purchase- Purchase pirce,Acquisition LTV,and Down Payment will be displayed.\n Rate Term & Cash out Refi- Purchase price,Acquisition LTV and Down Payment will Not. "></i>
                    </label>
                    <div class="col-md-7">
                    <select  data-placeholder="- Select Transaction Type -" name="transactionType[]"  id= "transactionType"  class="chzn-select form-control" multiple="" >
EOT;
        for ($i = 0; $i < count($gltypeOfHMLOLoanRequesting); $i++) {
            $sOpt = '';
            $typeOfLoan = '';
            $typeOfLoan = trim($gltypeOfHMLOLoanRequesting[$i]);
            $sOpt = Arrays::isSelectedArray($transactionType, $typeOfLoan);
            $formHtml1 .= <<<EOT
								<option value="$typeOfLoan" $sOpt>$typeOfLoan</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>           
                    </div>
                </div>
            </div>
            
            
            <div class="col-md-6 hideIfMarketPlaceYes $hideIfMarketPlaceYes">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       Credit Score Range
                    </label>
                    <div class="col-md-7">
                    <select data-placeholder="- Select Credit Score Range -" name="PCBorrCreditScoreRange[]" id="PCBorrCreditScoreRange" class="chzn-select form-control" multiple="" >	
EOT;
        for ($i = 0; $i < count($glHMLOCreditScoreRange); $i++) {
            $sOpt = '';
            $glCreditScoreRange = '';
            $glCreditScoreRange = trim($glHMLOCreditScoreRange[$i]);
            if (in_array($glCreditScoreRange, $PCBorrCreditScoreRangeArray)) $sOpt = 'Selected';
            $formHtml1 .= <<<EOT
	<option value="$glCreditScoreRange" $sOpt>$glCreditScoreRange</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>
</div>
                </div>
            </div>
            <div class="col-md-6 HMLOClass {$hideForLOC}">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Property Type : <i class="fa fa-info-circle text-primary popoverClass" data-html="true" data-content="There are 2 special values in the list as separators, if you wish to dress up your webform with the separators you see in the back office when you are controlling the property types with loan guidelines, then add these two separators<br><img src='/assets/images/proptypes.png'>"></i>
                    </label>
                    <div class="col-md-7">
                    <select data-placeholder="- Select Property Type -" name="propertyType[]" id= "propertyType" class="chzn-select form-control" multiple="" {$disableSelectForLOC}>
EOT;
        $propertyTypeKeyArray = [];
        if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) {
            $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
        }
        foreach ($propertyTypeKeyArray as $propertyTypeValue) {
            $propKey = trim($propertyTypeValue ?? '');
            $selOpt = Arrays::isSelectedArray($propertyType, $propKey) ?? '';
            $propVal = trim(GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$propertyTypeValue] ?? '');
            if ($propKey == 1000) {
                $formHtml1 .= <<<EOT
            <option value="$propKey" $selOpt style="color:white;background-color: rgb(0, 130, 187);">--Residential--</option>
EOT;
            } else if ($propKey == 1001) {
                $formHtml1 .= <<<EOT
            <option value="$propKey" $selOpt style="color:white;background-color: rgb(0, 130, 187);">--Commercial--</option>
EOT;
            } else {
                $formHtml1 .= <<<EOT
            <option value="$propKey" $selOpt> $propVal</option>
EOT;
            }
        }
        $formHtml1 .= <<<EOT
						</select>
                    </div>
                </div>
            </div>
            <div class="col-md-6 HMLOClass {$hideForLOC}">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Future Property Type : <i class="fa fa-info-circle text-primary popoverClass" data-html="true" data-content="There are 2 special values in the list as separators, if you wish to dress up your webform with the separators you see in the back office when you are controlling the future property types with loan guidelines, then add these two separators<br><img src='/assets/images/proptypes.png'>"></i>
                    </label>
                    <div class="col-md-7">
                    <select data-placeholder="- Select Future Property Type -" name="futurePropertyType[]" id= "futurePropertyType" class="chzn-select form-control" multiple="" {$disableSelectForLOC}>
EOT;
        $futurePropertyTypeKeyArray = [];
        if (count(Property::$propertyFuturePropertyType) > 0) {
            $futurePropertyTypeKeyArray = array_keys(Property::$propertyFuturePropertyType);
        }
        foreach ($futurePropertyTypeKeyArray as $futurePropertyTypeVal) {
            $propKey = trim($futurePropertyTypeVal ?? '');
            $selOpt = Arrays::isSelectedArray($futurePropertyType, $propKey) ?? '';
            $propVal = trim(Property::$propertyFuturePropertyType[$futurePropertyTypeVal] ?? '');
            if ($propKey == 1000) {
                $formHtml1 .= <<<EOT
            <option value="$propKey" $selOpt style="color:white;background-color: rgb(0, 130, 187);">--Residential--</option>
EOT;
            } else if ($propKey == 1001) {
                $formHtml1 .= <<<EOT
            <option value="$propKey" $selOpt style="color:white;background-color: rgb(0, 130, 187);">--Commercial--</option>
EOT;
            } else {
                $formHtml1 .= <<<EOT
            <option value="$propKey" $selOpt> $propVal</option>
EOT;
            }
        }
        $formHtml1 .= <<<EOT
						</select>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 hideIfMarketPlaceYes $hideIfMarketPlaceYes">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start">
                       Entity Type
                    </label>
                    <div class="col-md-7">
                        <select data-placeholder="- Select Entity Type -" name="entityType[]" id="entityType" class="chzn-select" multiple="">
EOT;
        foreach ($glEntityTypeArray as $et) {
            $etSelOpt = Arrays::isSelectedArray($entityTypeData, $et);
            $formHtml1 .= <<<EOT
								<option value="$et" $etSelOpt>$et</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                        </select>
                    </div>
                </div>
            </div>
   
            <div class="col-md-6 hideIfMarketPlaceYes $hideIfMarketPlaceYes">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       Extension Options
                    </label>
                    <div class="col-md-7">
                    <select data-placeholder="- Select Extension Options -" name="extnOption[]" id="extnOption" class="chzn-select" multiple="" >
EOT;
        foreach ($glHMLOExtensionOption as $extKey => $extValue) {
            $sOpt = '';
            if (in_array($extKey, $extnOption)) $sOpt = 'Selected';
            $formHtml1 .= <<<EOT
								<option value="$extKey" $sOpt> $extValue</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>
					</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       Loan Term:
                    </label>
                    <div class="col-md-7">
                    <select data-placeholder="- Select Loan Term -" name="loanTerm[]" id= "loanTerm"  class="chzn-select form-control" multiple="" >
EOT;
        for ($i = 0; $i < count($glHMLOLoanTerms); $i++) {
            $sOpt = '';
            $proLoanTerm = '';
            $proLoanTerm = trim($glHMLOLoanTerms[$i]);
            //$sOpt = Arrays::isSelectedArray($loanTerm, $proLoanTerm);
            $sOpt = '';
            if (in_array($proLoanTerm, $loanTerm)) $sOpt = 'Selected';
            $formHtml1 .= <<<EOT
								<option value="$proLoanTerm" $sOpt> $proLoanTerm</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>
                    </div>
                </div>
            </div>
EOT;
        $marketPlaceHMLOBorrowerOccu = '';
        $marketPlaceHMLOBorrowerOccuDisabled = '';
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'HMLO') {
            $marketPlaceHMLOBorrowerOccu = ' hidden ';
            $marketPlaceHMLOBorrowerOccuDisabled = ' disabled readonly=""  ';
            $marketSbaLoanProductClassShowArray = ['Residential Bridge', 'Fix & Flip', 'Long Term Rental', 'SBA']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceHMLOBorrowerOccu = '';
                $marketPlaceHMLOBorrowerOccuDisabled = '';
            }
        } else {
            $marketPlaceHMLOBorrowerOccu = '';
            $marketPlaceHMLOBorrowerOccuDisabled = '';
        }

        $formHtml1 .= <<<EOT
            <div class="col-md-6 HMLOClass marketBorrowerOccupancyClass {$hideForLOC} {$marketPlaceHMLOBorrowerOccu} ">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Borrower Occupancy:
                    </label>
                    <div class="col-md-7">
                        <select data-placeholder="- Select Occupancy -" name="occupancy[]" id="occupancy"  class="chzn-select form-control" multiple="" {$disableSelectForLOC} {$marketPlaceHMLOBorrowerOccuDisabled}>
EOT;
        for ($i = 0; $i < count($glHMLOHouseType); $i++) {
            $sOpt = '';
            $glOccupancy = '';
            $glOccupancy = trim($glHMLOHouseType[$i]);
            $sOpt = Arrays::isSelectedArray($occupancy, $glOccupancy);
            $formHtml1 .= <<<EOT
                                    <option value="$glOccupancy" $sOpt>$glOccupancy</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                            </select>
					</div>
                </div>
            </div>

     
    <div class="col-md-6 HMLOClass {$hideForLOC}">
        <div class="form-group row">
            <label class="col-md-3  font-weight-bold align-self-start " for="HMLOstateAll">Property State : </label>
            <div class="col-md-2">		 
                <div class="checkbox-list">
                    <label class="checkbox" for="HMLOstateAll">
                        <input type= "checkbox" class="chosen-select selectALlCheckBox" data-id="state" id ="HMLOstateAll" /> 
                        <span></span>Select All
                    </label>
                </div>
            </div>
            <div class="col-md-7">
            <select data-placeholder="- Select State -" name="state[]" id="state" class="chzn-select form-control"  {$disableSelectForLOC} multiple="">
EOT;
        for ($s = 0; $s < count($stateArray); $s++) {
            $sOpt = '';
            $stateName = '';
            $stateCode = trim($stateArray[$s]['stateCode']);
            $sOpt = Arrays::isSelectedArray($state, trim($stateArray[$s]['stateCode']));
            $stateName = trim($stateArray[$s]['stateName']);
            $formHtml1 .= <<<EOT
								<option value="$stateCode" $sOpt> $stateName</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>
                </div>
        </div>
    </div>

            <div class="col-md-6 nichesClassText marketPlaceDepenShowHide {$marketPlaceStatus}  ">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Niches <i class="fa fa-info-circle popoverClass text-primary" data-html="true" data-content="Niches can be multi-selected and help you narrow your search inside marketplace. <NAME_EMAIL> to add any new niches."></i>
                    </label>
                    <div class="col-md-7">
                     <select {$temDisableSelect} data-placeholder="- Select Niches -" name="niches[]" id="niches" class="chzn-select form-control" multiple="" >
EOT;
        foreach ($PCNichesArrayNewArray as $grpKeyNich => $grpValueNich) {
            $formHtml1 .= <<<EOT
                                    <optgroup label="$grpKeyNich">
EOT;
            for ($i = 0; $i < count($grpValueNich); $i++) {
                $sOpt = '';
                $nichesVal = '';
                $nichesID = '';
                $nichesID = trim($grpValueNich[$i]['NID']);
                $sOpt = Arrays::isSelectedArray($niches, $nichesID);
                $nichesVal = trim($grpValueNich[$i]['niches']);
                $formHtml1 .= <<<EOT
                                    <option value="$nichesID" $sOpt> $nichesVal</option>
EOT;
            }
            $formHtml1 .= <<<EOT
                                    </optgroup>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>
						<div class="" id="loaderImg1" style="display: none;">
						    <img src="{$siteSSLUrl}assets/images/ajax-loader.gif" alt="">
						</div>
                    </div>
                </div>
            </div>
  
            <div class="col-md-6 HMLOClass {$hideForLOC}">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       Amortization:
                    </label>
                    <div class="col-md-7">
                        <select data-placeholder="- Select -" name="lien1Terms[]" id="lien1Terms"  class="chzn-select" {$disableSelectForLOC} multiple="">
EOT;
        $glHMLOAmortization = [
            LoanTerms::INTEREST_ONLY,
            '1 Years',
            '2 Years',
            '3 Years',
            '4 Years',
            '5 Years',
            '6 Years',
            '7 Years',
            '8 Years',
            '9 Years',
            '10 Years',
            '15 Years',
            '20 Years',
            '25 Years',
            '30 Years',
            '40 Years',
        ];

        for ($i = 0; $i < count($glHMLOAmortization); $i++) {
            $sOpt = '';
            $amort = '';
            $amort = trim($glHMLOAmortization[$i]);
            $sOpt = Arrays::isSelectedArray($Amortization, $amort);
            $formHtml1 .= <<<EOT
<option value="$amort" $sOpt> $amort</option>
EOT;
        }
        $formHtml1 .= <<<EOT
					    </select>
                    </div>
                </div>
            </div>
     
        <div class="col-md-6 HMLOClass {$hideForLOC}">
            <div class="form-group row">
                <label class="col-md-5  font-weight-bold align-self-start ">
                    Rate Lock Period:
                </label>
                <div class="col-md-7">
                    <select data-placeholder="- Select Rate Lock Period -" name="rateLockPeriod[]" id="rateLockPeriod" class="chzn-select" {$disableSelectForLOC} multiple="">
EOT;
        foreach ($glRateLockPeriod as $rateLockPeriodVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selRateLockPeriodVal, $rateLockPeriodVal);
            $formHtml1 .= <<<EOT
            <option value="$rateLockPeriodVal" $sOpt> $rateLockPeriodVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-6 HMLOClass {$hideForLOC}">
            <div class="form-group row">
                <label class="col-md-5  font-weight-bold align-self-start ">
                    Loan/Exit Plan:
                </label>
                <div class="col-md-7">
                    <select data-placeholder="- Select Loan/Exit Plan -" 
                            name="exitStrategy[]" 
                            id="exitStrategy"
                            class="chzn-select" 
                            {$disableSelectForLOC} 
                            multiple="">
EOT;
        foreach (glHMLOExitStrategy::getExitStrategy($PCID) ?? [] as $eachExitStrategy) {
            $sOpt = (!empty($exitStrategyInfo)
                && in_array($eachExitStrategy, array_column($exitStrategyInfo, 'exitStrategy'), true)) ? 'selected' : '';
            $formHtml1 .= <<<EOT
            <option value="$eachExitStrategy" $sOpt>$eachExitStrategy</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
                </div>
            </div>
        </div>



EOT;
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCLogicEquipmentType = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['Equip Financing', 'Equip Leasing']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCLogicEquipmentType = '';
            }
        } else {
            $marketPlaceLOCLogicEquipmentType = '';
        }
        $formHtml1 .= <<<EOT

    <div class="col-md-6 locClass marketEquipmentTypeClass filterClass $hideForHMLO $marketPlaceLOCLogicEquipmentType">
        <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                    Equipment Type
            </label>
            <div class="col-md-7">
<select  data-placeholder="- Select Equipment Type -" name="marketEquipmentType[]" id="marketEquipmentType"   class="chzn-select "  {$disableSelectForHMLO} multiple>
            <option value=""></option>
EOT;
        foreach ($glEquipmentType as $equipmentTypeVal) {
            $sOpt = Arrays::isSelectedArray($selectedEquipmentTypeVals, $equipmentTypeVal);
            $formHtml1 .= <<<EOT
                                        <option value="$equipmentTypeVal" $sOpt>$equipmentTypeVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
</select>
            </div>
        </div>
    </div>

EOT;

        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCLogicmaxNSF = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['Factoring', 'Line of Credit', 'Term Loan', 'MCA', 'Equip Financing', 'Equip Leasing']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCLogicmaxNSF = '';
            }
        } else {
            $marketPlaceLOCLogicmaxNSF = '';
        }

        $formHtml1 .= <<<EOT
    <div class="col-md-6 locClass marketMaxNSFsAllowedClass  filterClass $hideForHMLO $marketPlaceLOCLogicmaxNSF ">
         <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                Max NSFs Allowed <i class="fa fa-info-circle text-primary popoverClass ml-2" data-html="true" data-content="The usually refers to the Avg. of last 6 months or last 1 month period. or in some cases over last 1 year, depending on the lender."></i>
            </label>
            <div class="col-md-7">
<select  data-placeholder="- Select Max NSFs Allowed -" name="marketMaxNSFsAllowed" id="marketMaxNSFsAllowed"  class="chzn-select " {$disableSelectForHMLO}>
            <option value=""></option>
EOT;
        foreach ($globalRecentNSFsCat as $eachRecentNSFsID => $eachRecentNSFsVal) {
            $sOpt = '';
            if ($marketMaxNSFsAllowed == $eachRecentNSFsID) {
                $sOpt = 'selected';
            }
            $formHtml1 .= <<<EOT
                                        <option value="$eachRecentNSFsID" $sOpt>$eachRecentNSFsVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
</select>
            </div>
        </div>
    </div>


     

    <div class="col-md-6 locClass marketEntityStateOfFormationClass filterClass $hideForHMLO">
        <div class="row form-group">
            <label class="col-md-3  font-weight-bold align-self-start ">
                   Allowed States <i class="fa fa-info-circle text-primary popoverClass ml-2" data-html="true" data-content="Which States associated to the entity State of formation are eligible for this product."></i>
            </label>
                  <div class="col-md-2 ">
                                <div class="checkbox-inline">
                                    <label class="checkbox" for="allowedStates">
                                                <input type= "checkbox" class="chosen-select selectALlCheckBox" data-id="marketEntityStateOfFormation" id ="allowedStates" />
                                             <span></span>Select All
                                         </label>
                                </div>
                        </div>
            <div class="col-md-7">
<select  data-placeholder="- Select Allowed States -" name="marketEntityStateOfFormation[]" id="marketEntityStateOfFormation"   class="chzn-select " {$disableSelectForHMLO}  multiple>
            <option value=""></option>
EOT;
        for ($s = 0; $s < count($stateArray); $s++) {
            $sOpt = '';
            $stateName = '';
            $stateCode = trim($stateArray[$s]['stateCode']);
            $sOpt = Arrays::isSelectedArray($selectedEntityStateFormation, $stateCode);
            $stateName = trim($stateArray[$s]['stateName']);
            $formHtml1 .= <<<EOT
								<option value="$stateCode" $sOpt> $stateName</option>
EOT;
        }
        $formHtml1 .= <<<EOT
</select>
            </div>
        </div>
    </div>
EOT;
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCPaymentFreq = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['Factoring', 'MCA']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCPaymentFreq = '';
            }
        } else {
            $marketPlaceLOCPaymentFreq = '';
        }
        $formHtml1 .= <<<EOT
    <div class="col-md-6 locClass marketPaymentFrequencyClass  filterClass $hideForHMLO $marketPlaceLOCPaymentFreq    ">
         <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                Payment Frequency
            </label>
            <div class="col-md-7">
                    <select  data-placeholder="- Select Payment Frequency -" name="marketPaymentFrequency[]" multiple id="marketPaymentFrequency"   class="chzn-select " {$disableSelectForHMLO} >
                            <option value=""></option>
EOT;
        foreach ($glpaymentFrequency as $payFrequencyKey => $payFrequencyVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selectedPaymentFrequency, $payFrequencyKey);
            $formHtml1 .= <<<EOT
                            <option value="$payFrequencyKey" $sOpt>$payFrequencyVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
            </div>
        </div>
    </div>
</div>
</div>
<!-- end Of Available Fields -->
<span id="guideline_warnings"><!--span used for walkthru-->
<div class="row">
 <label class="col-lg-12  bg-secondary  py-2  m-0 mb-4 py-2"><b>Guideline Warnings</b> <i class="fa fa-info-circle text-primary popoverClass"  data-html="true"  data-content="These fields will trigger warnings on your Loan Info Tab or Webforms that will inform you if a value is outside any given range, maximum, or minimum."></i></label>
 </div>

EOT;

        $enableRehabConstructionTrue = $enableRehabConstructionFalse = '';
        $enableRehabConstructionFalse = 'checked';
        $hideIfRehabDisabled = '';
        if ($enableRehabConstruction) {
            $enableRehabConstructionTrue = 'checked';
            $enableRehabConstructionFalse = '';
            $hideIfRehabDisabled = ' ';
            $marketPlaceHMLOMinFixFlip = ' ';
            $marketPlaceHMLOGrounCons = ' ';
        } else {
            $enableRehabConstructionTrue = '';
            $enableRehabConstructionFalse = 'checked';
            $hideIfRehabDisabled = ' hidden ';
            $marketPlaceHMLOMinFixFlip = ' hidden ';
            $marketPlaceHMLOGrounCons = ' hidden ';
        }
        $formHtml1 .= <<<EOT
        <div class="row hideifMarketPlaceOn ">
            <div class="col-md-12 HMLOClass $hideForLOC hideIfMarketPlaceYes $hideIfMarketPlaceYes">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                     Does this Loan Program(s) Support Rehab or Construction?
                    </label>
                    <div class="col-md-7">
                       <input type="radio" name="enableRehabConstruction" id="enableRehabConstructionYes" {$enableRehabConstructionTrue} {$disableForLOC} class="enableRehabConstruction" value="1" > Yes
                       <input type="radio" name="enableRehabConstruction" id="enableRehabConstructionNo" {$enableRehabConstructionFalse} {$disableForLOC} class="enableRehabConstruction" value="0" > No
                    </div>
                </div>
            </div>
        </div>
EOT;
        // $marketPlaceHMLOMinFixFlip = '';
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'HMLO') {
            $marketPlaceHMLOMinFixFlip = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['Fix & Flip']; //'New Construction'
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceHMLOMinFixFlip = '';
            }
        }
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceHMLOMinFixFlip = ' hidden ';
            $marketPlaceHMLOGrounCons = ' hidden ';
        }
        $formHtml1 .= <<<EOT
     <div class="row HMLOClass  marketFixFlipClass hideIfMarketPlaceYes  fixandflipMarketPlace $marketPlaceHMLOMinFixFlip ">
            <div class="col-md-6 ">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Min # of properties completed for fix and flip:
                    </label>
                    <div class="col-md-7"><input type="text" name="minPropertyForFixFlop" id="minFixFlop" class="minMaxrange form-control" value="$minPropertyForFixFlop" size="20" autocomplete="off"></div>
                </div>
            </div>
            <div class="col-md-6 ">
                <!--   <div class="form-group row d-none">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                      Max # of properties completed for fix and flip
                    </label>
                    <div class="col-md-7"><input type="text" name="maxPropertyForFixFlop" id="maxFixFlop" class="minMaxrange form-control" value="$maxPropertyForFixFlop" size="20" autocomplete="off"></div>
                </div> -->
            </div>
        </div>
EOT;

        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'HMLO') {
            $marketPlaceHMLOGrounCons = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['New Construction', 'Commercial- New Construction']; //'Fix & Flip', 'Commercial Bridge'
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceHMLOGrounCons = '';
            }
        }
        $formHtml1 .= <<<EOT
        <div class="row HMLOClass  marketGroundUpConstruction hideIfMarketPlaceYes  newConstructionMarketPlace $marketPlaceHMLOGrounCons ">
            <div class="col-md-6 ">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                   Min # of properties completed for ground up construction:
                    </label>
                    <div class="col-md-7"><input type="text" name="minPropertyForGrndConst" id="minGrndConst" class="minMaxrange form-control" value="$minPropertyForGrndConst" size="20" autocomplete="off"></div>
                </div>
            </div>
            <div class="col-md-6 ">
                <!--    <div class="form-group row d-none">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                      Max # of properties completed for ground up construction:
                    </label>
                    <div class="col-md-7">
                    <input type="text" name="maxPropertyForGrndConst" id="maxGrndConst" class="minMaxrange form-control" value="$maxPropertyForGrndConst" size="20" autocomplete="off">
</div>
                </div> -->
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Min Loan Amount:
                    </label>
                    <div class="col-md-7">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                <input class="form-control" type="text" name="minLoanAmount" id="minLoanAmount" onchange = "currencyConverter(this, this.value);" value="$minLoanAmount" 
                                 placeholder="Enter Min Loan Amount"
                                size="10" maxlength="75" autocomplete="off">
                                </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                    <div class="form-group row"> 
                        <label class="col-md-5  font-weight-bold align-self-start ">
                           Max Loan Amount:
                        </label>
                        <div class="col-md-7">
                               <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input class="form-control" type="text" name="maxLoanAmount" id="maxLoanAmount" onchange = "currencyConverter(this, this.value);" 
                                      placeholder="Enter Max Loan Amount"
                                    value="$maxLoanAmount" size="10" maxlength="75" autocomplete="off">
                               </div>
                        </div>
                    </div>
            </div>
                
            <div class="col-md-6">
                <div class="form-group row">
                        <label class="col-md-5  font-weight-bold align-self-start ">
                           Min Rate: <i class="fa fa-info-circle text-primary popoverClass" data-html="true" data-content="The min & max rate can help create a mini pricing engine and be used for specific programs that have tiers related to other factors like LTV,Mid Fico,Property Type,Borrower Experience,Property State,State, or specical niche. You will need to create a loan program for each unique scenario you want priced automatically."></i>
                        </label>
                    <div class="col-md-7">
                    <div class="input-group">
                        <input class="form-control" type="text" name="minRate" id="minRate" value="$minRate"    placeholder="Enter Min Rate" size="10" maxlength="75" autocomplete="off">
                        <div class="input-group-append">
                                <span class="input-group-text">
                                    %
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                    <div class="form-group row">
                        <label class="col-md-5  font-weight-bold align-self-start ">
                            Max Rate:
                        </label>
                         <div class="col-md-7">
                            <div class="input-group">
                                <input class="form-control" type="text" name="maxRate" id="maxRate" value="$maxRate"  placeholder="Enter Max Rate" size="10" maxlength="75" autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                         </div>
                    </div>
               </div>
EOT;

        $marketPlaceLOCMinFico = '';
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCMinFico = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['SBA', 'MCA', 'Equip Financing', 'Equip Leasing', 'Term Loan', 'Line of Credit', 'Factoring', 'Purchase Order Finacing']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCMinFico = '';
            }
        } else {
            $marketPlaceLOCMinFico = '';
        }
        $formHtml1 .= <<<EOT
            <div class="col-md-6 minFicoClass  {$marketPlaceLOCMinFico} filterClass ">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Min Mid Fico:
                    </label>
                    <div class="col-md-7"><input type="text" name="minMidFico" id="minFico" class="ficorange form-control text-left" value="$minMidFico" size="10"  autocomplete="off"></div>
                </div>
            </div>
            
            
           <div class="col-md-6 filterClass ">
                <div class="form-group row">
                    <label class="col-md-5 font-weight-bold align-self-start ">
                        Min DSCR:
                    </label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text"
                                   name="minDSCR" 
                                   id="minDSCR" 
                                   class="form-control text-left" 
                                   value="$minDSCR" 
                                   onkeyup="validatePercentage(this);return restrictAlphabetsLoanTerms(this)"
                                   autocomplete="off">
                                   <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
            
            
         <!--   <div class="col-md-6 d-none">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                        Max Mid Fico:
                    </label>
                    <div class="col-md-7"><input  type="text" name="maxMidFico" id="maxFico" class="ficorange form-control" value="$maxMidFico" size="10" maxlength="999" autocomplete="off"></div>
                </div>
            </div> -->
        </div>
		
   

		<div class="row">
            <div class="col-md-6">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       Lender Points:
                    </label>
                    <div class="col-md-7">
                    <input class="form-control" type="text" name="minPoints" id="minPoints" value="$minPoints" size="12" maxlength="75" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                     Max Lender Points: <i class="fa fa-info-circle text-primary popoverClass" data-html="true" data-content="Enter the most amount of points that can be charged b/w lender and broker"></i>
                    </label>
                    <div class="col-md-7"><input class="form-control" type="text" name="maxPoints" id="maxPoints" value="$maxPoints" size="12" maxlength="75" autocomplete="off"></div>
                </div>
            </div>
            
            <div class="col-md-6 HMLOClass {$hideForLOC}">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                      Down Payment % <i class="fa fa-info-circle text-primary popoverClass" data-html="true"  data-content="The amount entered here will be the default value entered during the quick app and full app process. So it asks as a 'Suggested' amount, not as a forced requirement."></i>
                    </label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input class="form-control" type="text" name="downPaymentPercentage" id="downPaymentPercentage"  $disableForLOC value="$downPaymentPercentage" maxlength="8" onkeyup ="validatePercentage(this)" autocomplete="off">
                                <div class="input-group-append">
                                    <span class="input-group-text">%</span>
                                </div>
                        </div>
                    </div>
                </div>
            </div>

      <!--fixandflipMarketPlacenewConstructionMarketPlace-->
            <div class="col-md-6 hideRehab HMLOClass  {$hideForLOC}">
                <div class="form-group row">
                    <label class="col-md-5  font-weight-bold align-self-start ">
                     Rehab/Construction % Financed <i class="fa fa-info-circle text-primary popoverClass" data-html="true" data-content="The amount entered here will be the default value entered during the quick app and full app process. so it asks as 'Suggested' amount, not as a forced requirement."></i>
                    </label>
                    <div class="col-md-7">
                         <div class="input-group">
                            <input class="form-control" type="text" $disableForLOC name="rehabCostPercentageFinanced" id="rehabCostPercentageFinanced" value="$rehabCostPercentageFinanced" maxlength="8" onkeyup ="validatePercentage(this)" autocomplete="off">
                            <div class="input-group-append">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
EOT;
        if ($marketIsTherePrePaymentPenalty == 'Yes') {
            $marketIsTherePrePaymentPenaltyTrue = 'checked';
            $marketIsTherePrePaymentPenaltyFalse = '';
        } else {
            $marketIsTherePrePaymentPenaltyTrue = '';
            $marketIsTherePrePaymentPenaltyFalse = 'checked';
        }
        $marketPlaceLOCPrepayPenalty = '';
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCPrepayPenalty = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['SBA', 'Equip Financing', 'Equip Leasing', 'Term Loan']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCPrepayPenalty = '';
            }
        } else {
            $marketPlaceLOCPrepayPenalty = '';
        }
        $formHtml1 .= <<<EOT
  
    <div class="col-md-6  {$marketPlaceLOCPrepayPenalty} marketPlaceDepenShowHide  marketIsTherePrePaymentPenaltyClass filterClass $marketPlaceStatus ">
        <div class="row form-group ">
            <label class="col-md-5  font-weight-bold align-self-start ">
                    Pre-Pay Penalty
            </label>
            <div class="col-md-7">
                <div class="radio-inline">
                        <label class="radio radio-solid  font-weight-bold align-self-start " for="marketIsTherePrePaymentPenaltyYes">
                            <input type="radio" name="marketIsTherePrePaymentPenalty"
                                   id="marketIsTherePrePaymentPenaltyYes" {$marketIsTherePrePaymentPenaltyTrue}
                                   value="Yes"><span></span>Yes
                        </label>
                        <label class="radio radio-solid  font-weight-bold align-self-start " for="isTherePrePaymentPenaltyNo">
                            <input type="radio" name="marketIsTherePrePaymentPenalty"
                                   id="isTherePrePaymentPenaltyNo" {$marketIsTherePrePaymentPenaltyFalse}
                                   value="No"><span></span>No
                        </label>
                </div>
            </div>
        </div>
    </div>
        <div class="col-md-6 locClass marketAnnualGrossSalesClass filterClass $hideForHMLO ">
        <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                    Min Annual Revenue
            </label>
            <div class="col-md-7">
                <input type="text" name="marketAnnualGrossSales" 
                id="marketAnnualGrossSales" {$disableForHMLO}  
                value="{$marketAnnualGrossSales}" 
                placeholder="Enter Annual Revenue"
                   onblur="currencyConverter(this, this.value);"  
                   class="form-control  ">
            </div>
        </div>
    </div>
EOT;
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCAvgBankBal = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['MCA']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCAvgBankBal = '';
            }
        } else {
            $marketPlaceLOCAvgBankBal = '';

        }
        $formHtml1 .= <<<EOT
    <div class="col-md-6 locClass marketAverageBankBalanceClass filterClass $hideForHMLO  $marketPlaceLOCAvgBankBal ">
        <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                    Min. Avg Bank Balance
            </label>
            <div class="col-md-7">
                <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                    <input type="text" name="marketAverageBankBalance" id="marketAverageBankBalance" {$disableForHMLO} value="{$marketAverageBankBalance}" class="form-control  " placeholder="Enter Min Average Bank Balance"
                     onblur="currencyConverter(this, this.value);" 
                     size="10" maxlength="10">
                </div>
            </div>
        </div>
    </div>
EOT;
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCAvgTotalMonthlySale = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['MCA']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCAvgTotalMonthlySale = '';
            }
        } else {
            $marketPlaceLOCAvgTotalMonthlySale = '';

        }
        $formHtml1 .= <<<EOT
    <div class="col-md-6 locClass marketAvgTotalMonthlySaleClass filterClass $hideForHMLO $marketPlaceLOCAvgTotalMonthlySale">
         <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                Min. Avg Monthly Deposit
            </label>
            <div class="col-md-7">
                  <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text" 
                        name="marketAvgTotalMonthlySale" {$disableForHMLO} 
                        id="marketAvgTotalMonthlySale" 
                        class="form-control  " 
                        value="{$marketAvgTotalMonthlySale}" 
                        placeholder="Enter  Min Avg Monthly Deposit"
                        onblur="currencyConverter(this, this.value);"
                        >
                   </div>
            </div>
        </div>
    </div>
    
        <div class="col-md-6">
             <div class="row form-group">
            <label class="col-md-3  font-weight-bold align-self-start ">
               Eligible States
            </label>
              <div class="col-md-2">		 
                <div class="checkbox-list">
                    <label class="checkbox" for="elgibleStatesAll">
                        <input type= "checkbox" class="chosen-select selectALlCheckBox" data-id="elgibleStates" id="elgibleStatesAll" /> 
                        <span></span>Select All
                    </label>
                </div>
            </div>
            <div class="col-md-7">
<select  data-placeholder="- Select Elgible States -" name="elgibleStates[]" id="elgibleStates"   class="chzn-select "  multiple>
            <option value=""></option>
EOT;
        for ($s = 0; $s < count($stateArray); $s++) {
            $sOpt = '';
            $stateName = '';
            $stateCode = trim($stateArray[$s]['stateCode']);
            $sOpt = Arrays::isSelectedArray($selectedElgibleStates, $stateCode);
            $stateName = trim($stateArray[$s]['stateName']);
            $formHtml1 .= <<<EOT
								<option value="$stateCode" $sOpt> $stateName</option>
EOT;
        }
        $formHtml1 .= <<<EOT
</select>
            </div>
    </div>
    </div>
    
    
  </div>
  </div>
        
        
EOT;
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'loc') {
            $marketPlaceLOCLogicSBALoanProduct = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['SBA']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceLOCLogicSBALoanProduct = '';
            }
        } else {
            $marketPlaceLOCLogicSBALoanProduct = '';
        }

        $formHtml1 .= <<<EOT
<div class="row  ">
    <div class="col-md-6 locClass marketSbaLoanProductClass filterClass $hideForHMLO $marketPlaceLOCLogicSBALoanProduct">
        <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                    SBA Loan Product 
            </label>
            <div class="col-md-7">  
                <select  data-placeholder="- Select SBA Loan Product -" name="marketSbaLoanProduct[]" multiple id="marketSbaLoanProduct"   class="chzn-select " {$disableSelectForHMLO}>
                            <option value=""></option>
EOT;
        foreach ($globalSBALoanProductsCat as $eachSBALoanProductID => $eachSBALoanProductVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selectedSBALoanProducts, $eachSBALoanProductID);
            $formHtml1 .= <<<EOT
                              <option value="$eachSBALoanProductID" $sOpt>$eachSBALoanProductVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                </select>
            </div>
        </div>
    </div>

    <div class="col-md-6 locClass marketLoanPurpose filterClass $hideForHMLO ">
         <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                Loan Purpose 
            </label>
            <div class="col-md-7">                 
                <select  data-placeholder="- Select Purpose Of Loan -" name="marketLoanPurpose[]" id="marketLoanPurpose"  class="chzn-select " {$disableSelectForHMLO}  multiple>
                            <option value=""></option>
EOT;
        foreach ($purposeOfLoanArray as $eachPurposeOfLoanId => $eachPurposeOfLoanVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selectedLoanPurpose, $eachPurposeOfLoanId);
            $formHtml1 .= <<<EOT
                                                        <option value="$eachPurposeOfLoanId" $sOpt>$eachPurposeOfLoanVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                </select>
            </div>
        </div>
    </div>
    
        <div class="col-md-6 locClass marketMinTimeInBusinessClass filterClass $hideForHMLO">
         <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
                Min time in Business
            </label>
            <div class="col-md-7">
<select  data-placeholder="- Select Min Time in Business -" name="marketMinTimeInBusiness[]" id="marketMinTimeInBusiness"  class="chzn-select " {$disableSelectForHMLO}  >
            <option value=""></option>
EOT;
        foreach ($globalMinTimeInBusinessCat as $eachMinTimeInBusinessID => $eachMinTimeInBusinessVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selectedMinTimeInBusiness, $eachMinTimeInBusinessID);
            $formHtml1 .= <<<EOT
                                        <option value="$eachMinTimeInBusinessID" $sOpt>$eachMinTimeInBusinessVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
</select>
            </div>
        </div>
    </div>
    
    
    <div class="col-md-6 marketPlaceDepenShowHide locClass $hideForHMLO $marketPlaceStatus ">
        <div class="row form-group ">
            <label class="col-md-5  font-weight-bold align-self-start ">
                    Min seasoning for Business Bankruptcy
            </label>
            <div class="col-md-7">
                    <select  data-placeholder="- Select Business Bankruptcy -" name="marketplaceMinSeasoningBusinessBankruptcy[]"  id="marketplaceMinSeasoningBusinessBankruptcy"   class="chzn-select " $disableSelectForHMLO >
                        <option value=""></option>
EOT;
        foreach ($globalBusinessBankruptcyCat as $eachBankruptcyID => $eachBankruptcyVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selectedMinSesBusinessBan, $eachBankruptcyID);
            $formHtml1 .= <<<EOT
<option value="$eachBankruptcyID" $sOpt>$eachBankruptcyVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
            </div>
        </div>
    </div>
    
        <div class="col-md-6 marketPlaceDepenShowHide $marketPlaceStatus ">
         <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
              Min seasoning for Personal Bankruptcy
            </label>
            <div class="col-md-7">
                    <select  data-placeholder="- Select Personal Bankruptcy -" name="marketplaceMinSeasoningPersonalBankruptcy[]"  id="marketplaceMinSeasoningPersonalBankruptcy"   class="chzn-select " >
                        <option value=""></option>
EOT;
        foreach ($globalBankruptcyCat as $eachBankruptcyID => $eachBankruptcyVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selectedMinSesPersonalBan, $eachBankruptcyID);
            $formHtml1 .= <<<EOT
     <option value="$eachBankruptcyID" $sOpt>$eachBankruptcyVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
            </div>
        </div>
    </div>

    <div class="col-md-6 marketPlaceDepenShowHide $marketPlaceStatus ">
         <div class="row form-group">
            <label class="col-md-5  font-weight-bold align-self-start ">
              Min seasoning for Foreclosure
            </label>
            <div class="col-md-7">
                    <select  data-placeholder="- Select Foreclosure -" name="marketplaceMinSeasoningForeclosure[]"  id="marketplaceMinSeasoningForeclosure"   class="chzn-select " >
                        <option value=""></option>
EOT;
        foreach ($globalBankruptcyCat as $eachBankruptcyID => $eachBankruptcyVal) {
            $sOpt = '';
            $sOpt = Arrays::isSelectedArray($selectedMinSesFore, $eachBankruptcyID);
            $formHtml1 .= <<<EOT
                                                    <option value="$eachBankruptcyID" $sOpt>$eachBankruptcyVal</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
            </div>
        </div>
    </div>   

     <div class="col-md-6 marketPlaceDepenShowHide $marketPlaceStatus ">
         <div class="row form-group">
            <label class="col-md-5 font-weight-bold align-self-start ">
              Business Category
            </label>
            <div class="col-md-7">
            <select data-placeholder="- Select Business Category -"  
                    multiple
                    name="businessCategory[]"  
                    id="businessCategory"   
                    class="chzn-select" >
                        <option value=""></option>
EOT;
        foreach (glbusinessCategoryArray::$glbusinessCategoryArray as $value) {
            $sOpt = Arrays::isSelectedArray($businessCategories, $value);
            $formHtml1 .= <<<EOT
             <option value="$value" $sOpt>$value</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 marketPlaceDepenShowHide $marketPlaceStatus ">
         <div class="row form-group">
            <label class="col-md-5 font-weight-bold align-self-start ">
              Lien Position
            </label>
            <div class="col-md-7">
            <select data-placeholder="- Select Lien Positions -"  
                    multiple
                    name="lienPosition[]"  
                    id="lienPosition"   
                    class="chzn-select" >
                        <option value=""></option>
EOT;
        foreach (glHMLOLienPosition::getLienPosition($PCID) as $key => $value) {
            $sOpt = Arrays::isSelectedArray($lienPositions, $key);
            $formHtml1 .= <<<EOT
             <option value="$key" $sOpt>$value</option>
EOT;
        }
        $formHtml1 .= <<<EOT
                    </select>
            </div>
        </div>
    </div>
    
    
    
    
</div>
</span>
<div class="row" id="addRow">
    <div class=" col-lg-12 m-0 mb-4 px-0 HMLOClass {$hideForLOC} " >
            <label class=" font-weight-bold align-self-start   bg-secondary  py-2  col-lg-12"><b>Guideline Warnings Cont. Max LTVs & Ratios</b>
                 <i class="fa fa-info-circle tooltipClass text-primary popoverClass ml-2" data-html="true" data-content="These transaction related LTV guidelines will warn users if the loan exceeds the allowed LTV.In the marketplace, your loan program(s) match score will correlated to this LTV threshold."></i>
             </label>
        </div>
EOT;
        $marketPlaceHMLOLoanToCost = '';
        if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'HMLO') {
            $marketPlaceHMLOLoanToCost = ' hidden ';
            $marketSbaLoanProductClassShowArray = ['Fix & Flip', 'New Construction', 'Commercial- New Construction', 'Commercial Bridge', 'Commercial-Permanent 3+ Years', 'Residential Bridge', 'SBA']; //Add
            $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
            if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                $marketPlaceHMLOLoanToCost = '';
            }
        } else {
            $marketPlaceHMLOLoanToCost = '';
        }
        //fixandflipMarketPlacenewConstructionMarketPlace
        $formHtml1 .= <<<EOT
<!--hideRehab-->
            <div class="col-md-6 HMLOClass marketLoanToCostClass {$hideForLOC} {$marketPlaceHMLOLoanToCost}    " >
                <div class="form-group row" >
                    <label class="col-md-5  font-weight-bold align-self-start ">
                       % Total Loan to Cost:
                    </label>
                    <div class="col-md-7">    
                     <input class="form-control" {$disableForLOC} type="text" name="totalLTC" id="totalLTC" class="totalLTC" value="$totalLTC" size="20" autocomplete="off"></div>
                </div>
            </div>    
EOT;
        if (count($tempTransactionType) > 0) {
            for ($j = 0; $j < count($tempTransactionType); $j++) {
                $tempTransType = '';
                $transactionTypeVal = '';
                $transType = '';
                $transType = trim($tempTransactionType[$j]['transactionType']);
                $tempTransType = Strings::removeSpaceWithSpecialChars($transType);
                $transactionTypeVal = trim($tempTransactionType[$j]['maxLTV']);
                $formHtml1 .= <<<EOT
<div class="$tempTransType col-md-6 HMLOClass LTVClass {$hideForLOC} ">
	<div class="form-group row" >
	   <label class="col-md-5  font-weight-bold align-self-start ">% for $transType</label>
         <div class="col-md-7">
             <input class="form-control" {$disableForLOC} type="text" id="$tempTransType" name="$tempTransType" value="$transactionTypeVal" />
         </div>
     </div>
</div>
EOT;
                // || $transType == 'Cash-Out / Refinance' || $transType == 'Refinance' || $transType == 'Commercial Cash Out Refinance'
                if ($transType == 'Purchase' || $transType == 'Commercial Purchase') {
                    $tempTransType = Strings::removeSpaceWithSpecialChars($transType);
                    $transactionTypeRehabVal = trim($tempTransactionType[$j]['maxLTVAfterRehab']);

                    $marketPlaceHMLOARV = '';
                    if (trim($marketPlaceStatus) == '' && $BLModuleCode == 'HMLO') {
                        $marketPlaceHMLOARV = ' hidden ';
                        $marketSbaLoanProductClassShowArray = ['Fix & Flip', 'New Construction', 'Commercial- New Construction', 'Commercial Bridge']; //Add
                        $marketSbaLoanProductClassShowCommnArray = (array_intersect($marketLoanProgramSelectedText, $marketSbaLoanProductClassShowArray));
                        if (count($marketSbaLoanProductClassShowCommnArray) > 0) {
                            $marketPlaceHMLOARV = '';
                        }
                    } else {
                        $marketPlaceHMLOARV = '';
                    }

                    $formHtml1 .= <<<EOT
<!--hideRehab-->
<div class="afterrehab$tempTransType col-md-6 ARVClass  HMLOClass {$hideForLOC} $marketPlaceHMLOARV " >
    <div class="form-group row" >
             <label class="col-md-5  font-weight-bold align-self-start ">$transType ARV(%)</label>
             <div class="col-md-7">
                <input class="form-control" type="text" {$disableForLOC} id="afterrehab$tempTransType" name="afterrehab$tempTransType" value="$transactionTypeRehabVal" >
             </div>
    </div>
</div>
EOT;
                }
            }
        }
        $formHtml1 .= <<<EOT
</div>

    
    <div class="row">
			<div id="default_fees" class="col-lg-12 m-0 mb-2 px-0 HMLOClass {$hideForLOC} hideIfMarketPlaceYes $hideIfMarketPlaceYes">
                    <div class="form-group row col-lg-12 m-0 mb-4 px-0 " >
                        <label class=" font-weight-bold align-self-start   bg-secondary  py-2  col-lg-12"><b>Default Fees:</b> </label>
                    </div>  
   <div class="form-group row col-lg-12 m-0 mb-2  " >
		<table style="width:100%" border="0">
					<tr>
						<td>Origination Points @ </td>
						<td>
							<input class="form-control d-none" type="text" name="originationPointsValue" onblur="javascript:calculateOriginationPointsRate('addBasicLoanTermForm', 'originationPointsRate');" id="originationPointsValue" onchange = "javascript:currencyConverter(this, this.value);" value="$originationPointsValue" />							
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">Points</span>
	</div>
	<input type="text" onchange = "javascript:currencyConverter(this, this.value);" name="originationPointsRate" class="form-control" id="originationPointsRate" value="$originationPointsRate"autocomplete="off" /> 
</div>
						</td>
						<td>Broker Points @ </td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">Points</span>
	</div>
	<input type="text" class="form-control" onchange = "javascript:currencyConverter(this, this.value);"  name="brokerPointsRate" id="brokerPointsRate" value="$brokerPointsRate" autocomplete="off" /> 
</div>
                </td>
					</tr>
					<tr class="even">
						<td>Closing Cost Financing fee:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
	<input class="form-control" type="text" name="closingCostFinancingFee" id="closingCostFinancingFee" onchange = "javascript:currencyConverter(this, this.value);" value="$closingCostFinancingFee" size="20" maxlength="68"   autocomplete="off" />
</div>
        </td>
						<td>Attorney fee:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
	<input class="form-control"  type="text" name="attorneyFee" id="attorneyFee" onchange = "javascript:currencyConverter(this, this.value);" value="$attorneyFee" size="20" maxlength="68"   autocomplete="off" />
</div>
        </td>
					</tr>
					<tr class="even">
						<td>Application Fee:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
	<input  class="form-control" type="text" name="applicationFee" id="applicationFee" onchange = "javascript:currencyConverter(this, this.value);" value="$applicationFee" size="20" maxlength="68"   autocomplete="off" />
</div>

						</td>
						<td>Appraisal Fee:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
		<input  class="form-control" type="text" name="appraisalFee" id="appraisalFee" onchange = "javascript:currencyConverter(this, this.value);" value="$appraisalFee" size="20" maxlength="68"   autocomplete="off" />
</div>
            </td>
					</tr>
					<tr>
						<td>Estimated Title Insurance Fees:<div class="with-children-tip right">
								<i class="fa fa-info-circle text-primary popoverClass"  data-content="Auto calculated to 1% of the Total Loan Amount. The value can be changed within the respective borrower's loan file."></i></div></td>
						<td>
						
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
		<input  class="form-control" type="text" name="estdTitleClosingFee" id="estdTitleClosingFee" onchange = "javascript:currencyConverter(this, this.value);" value="$estdTitleClosingFee" size="20" maxlength="68"   autocomplete="off" />
</div>
                </td>
						<td>Processing Fees:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
		<input class="form-control" type="text" name="processingFee" id="processingFee" onchange = "javascript:currencyConverter(this, this.value);" value="$processingFee" size="20" maxlength="68"   autocomplete="off" />
</div>
</td>
					</tr>
					
					<tr class="even">
						<td>Draws Set Up Fee:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
	<input class="form-control" type="text" name="drawsSetUpFee" id="drawsSetUpFee" onchange = "javascript:currencyConverter(this, this.value);" value="$drawsSetUpFee" size="20" maxlength="68"   autocomplete="off"/>
</div>
					
						</td>
						<td>Draws Fee</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
		<input class="form-control" type="text" name="drawsFee" id="drawsFee" onchange = "javascript:currencyConverter(this, this.value);" value="$drawsFee" size="20" maxlength="68"   autocomplete="off"/>
</div></td>
					</tr>
					<tr>
						<td>Valuation - BPO:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
<input class="form-control" type="text" name="valuationBPOFee" id="valuationBPOFee" onchange = "javascript:currencyConverter(this, this.value);" value="$valuationBPOFee" size="20" maxlength="68" autocomplete="off" />
</div></td>
						<td>Valuation - AVM:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
	<input class="form-control"  type="text" name="valuationAVMFee" id="valuationAVMFee" onchange = "javascript:currencyConverter(this, this.value);" value="$valuationAVMFee" size="20" maxlength="68" autocomplete="off" />
</div>
</td>
					</tr>
					<tr>
						<td>Credit Report:</td>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
		<input class="form-control" type="text" name="creditReportFee" id="creditReportFee" onchange = "javascript:currencyConverter(this, this.value);" value="$creditReportFee" size="20" maxlength="68" autocomplete="off" />
</div></td>
						<td>Background Check:</td>
						<td>
									<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="backgroundCheckFee" id="backgroundCheckFee" onchange = "javascript:currencyConverter(this, this.value);" value="$backgroundCheckFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Flood Certificate:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="floodCertificateFee" id="floodCertificateFee" onchange = "javascript:currencyConverter(this, this.value);" value="$floodCertificateFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Document Preparation:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="documentPreparationFee" id="documentPreparationFee" onchange = "javascript:currencyConverter(this, this.value);" value="$documentPreparationFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Wire Fee:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="wireFee" id="wireFee" onchange = "javascript:currencyConverter(this, this.value);" value="$wireFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Servicing Set Up Fee:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="servicingSetUpFee" id="servicingSetUpFee" onchange = "javascript:currencyConverter(this, this.value);" value="$servicingSetUpFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Tax Service:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="taxServiceFee" id="taxServiceFee" onchange = "javascript:currencyConverter(this, this.value);" value="$taxServiceFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Flood Service:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="floodServiceFee" id="floodServiceFee" onchange = "javascript:currencyConverter(this, this.value);" value="$floodServiceFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Inspection Fees:</td>
						<td>
									<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="inspectionFees" id="inspectionFees" onchange = "javascript:currencyConverter(this, this.value);" value="$inspectionFees" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Project Feasibility:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="projectFeasibility" id="projectFeasibility" onchange = "javascript:currencyConverter(this, this.value);" value="$projectFeasibility" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Due Diligence:</td>
						<td>
									<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="dueDiligence" id="dueDiligence" onchange = "javascript:currencyConverter(this, this.value);" value="$dueDiligence" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Ucc/Lien Search:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="UccLienSearch" id="UccLienSearch" onchange = "javascript:currencyConverter(this, this.value);" value="$UccLienSearch" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Lender Credit to Offset 3rd Party Fees:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="thirdPartyFees" id="thirdPartyFees" onchange = "javascript:currencyConverter(this, this.value);" value="$thirdPartyFees" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Other:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="otherFee" id="otherFee" onchange = "javascript:currencyConverter(this, this.value);" value="$otherFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Escrow Fees:</td>
						<td>
									<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="escrowFees" id="escrowFees" onchange = "javascript:currencyConverter(this, this.value);" value="$escrowFees" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Recording Fee:</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="recordingFee" id="recordingFee" onchange = "javascript:currencyConverter(this, this.value);" value="$recordingFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Underwriting Fees</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="underwritingFees" id="underwritingFees" onchange = "javascript:currencyConverter(this, this.value);" value="$underwritingFees" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Property tax</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="propertyTax" id="propertyTax" onchange = "javascript:currencyConverter(this, this.value);" value="$propertyTax" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Discount Fee</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="bufferAndMessengerFee" id="bufferAndMessengerFee" onchange = "javascript:currencyConverter(this, this.value);" value="$bufferAndMessengerFee" size="20" maxlength="68" autocomplete="off" /></div>
						</td>
						<td>Travel Notary Fee</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="travelNotaryFee" id="travelNotaryFee" onchange = "javascript:currencyConverter(this, this.value);" value="$travelNotaryFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Pre paid Interest</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="prePaidInterest" id="prePaidInterest" onchange = "javascript:currencyConverter(this, this.value);" value="$prePaidInterest" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Real Estate Taxes</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="realEstateTaxes" id="realEstateTaxes" onchange = "javascript:currencyConverter(this, this.value);" value="$realEstateTaxes" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
					<tr>
						<td>Insurance Premium</td>
						<td>
									<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="insurancePremium" id="insurancePremium" onchange = "javascript:currencyConverter(this, this.value);" value="$insurancePremium" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td>Pay Off Liens/Creditors</td>
						<td>
									<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="payOffLiensCreditors" id="payOffLiensCreditors" onchange = "javascript:currencyConverter(this, this.value);" value="$payOffLiensCreditors" size="20" maxlength="68" autocomplete="off" /></div>
						</td>
					</tr>
					<tr>
						<td>Wire Transfer Fee to Title</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="wireTransferFeeToTitle" id="wireTransferFeeToTitle" onchange = "javascript:currencyConverter(this, this.value);" value="$wireTransferFeeToTitle" size="20" maxlength="68" autocomplete="off" /></div>
						</td>
						<td>Wire Transfer Fee to Escrow</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="wireTransferFeeToEscrow" id="wireTransferFeeToEscrow" onchange = "javascript:currencyConverter(this, this.value);" value="$wireTransferFeeToEscrow" size="20" maxlength="68" autocomplete="off" /></div>
						</td>
					</tr>
					<tr>
						<td>Past Due Property Taxes</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="pastDuePropertyTaxes" id="pastDuePropertyTaxes" onchange = "javascript:currencyConverter(this, this.value);" value="$pastDuePropertyTaxes" size="20" maxlength="68" autocomplete="off" /></div>
						</td>
						<td>Survey</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="survey" id="survey" onchange = "javascript:currencyConverter(this, this.value);" value="$survey" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
					</tr>
                                        <tr>
						<td>Wholesale Admin Fee</td>
						<td>
										<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div><input class="form-control" type="text" name="wholeSaleAdminFee" id="wholeSaleAdminFee" onchange = "javascript:currencyConverter(this, this.value);" value="$wholeSaleAdminFee" size="20" maxlength="68" autocomplete="off" />
	</div>
						</td>
						<td></td>
						<td></td>
					</tr>	
					<tr>
						<td>Tax impounds @ 
						<div class=" col-md-12">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Months</span>
                                        </div>
                                        <input type="number" class="form-control input-sm col-md-2" name="taxImpoundsMonth"
                                               id="taxImpoundsMonth" value="$taxImpoundsMonth"
                                               onblur="javascript:calculateTaxImpoundsFee('addBasicLoanTermForm', 'taxImpoundsFee');"
                                                autocomplete="off"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text">$ </span>
                                        </div>
                                        <input type="text" class="form-control input-sm col-md-2" name="taxImpoundsMonthAmt"
                                               id="taxImpoundsMonthAmt"
                                               placeholder="0.00"
                                               value="$taxImpoundsMonthAmt"
                                               onblur="currencyConverter(this, this.value);calculateTaxImpoundsFee('addBasicLoanTermForm', 'taxImpoundsFee');"
                                               autocomplete="off"/>

                                    </div>
                                </div>
								<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
		<input class="form-control" type="text" name="taxImpoundsFee" id="taxImpoundsFee" onchange = "javascript:currencyConverter(this, this.value);" value="$taxImpoundsFee" >
</div>	</td>
						<td>Ins impounds @ 
						<div class="col-md-12">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">Months</span>
                                        </div>
                                        <input type="number"
                                               class="form-control input-sm col-md-2"
                                               name="insImpoundsMonth"
                                               id="insImpoundsMonth"
                                               value="$insImpoundsMonth"
                                               autocomplete="off"
                                               onblur="calculateInsImpoundsFee('addBasicLoanTermForm', 'insImpoundsFee');"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input type="text"
                                               class="form-control input-sm col-md-2"
                                               name="insImpoundsMonthAmt"
                                               placeholder="0.00"
                                               id="insImpoundsMonthAmt"
                                               value="$insImpoundsMonthAmt"
                                               onblur="currencyConverter(this, this.value);calculateInsImpoundsFee('addBasicLoanTermForm', 'insImpoundsFee');"
                                               autocomplete="off">
                                    </div>
                                </div>
						<td>
						<div class="input-group">
	<div class="input-group-prepend">
		<span class="input-group-text">$</span>
	</div>
		<input class="form-control" type="text" name="insImpoundsFee" id="insImpoundsFee" onchange = "javascript:currencyConverter(this, this.value);" value="$insImpoundsFee" onchange = "javascript:currencyConverter(this, this.value);">
</div>
</td>
					</tr>
					<tr>
						<td colspan="4"></td>
					</tr>
				</table>
			</div>
			</div>

              <div class="col-lg-12 m-0 mb-4 px-0 " >
                    <label class=" font-weight-bold align-self-start   bg-secondary  py-2  col-lg-12"><b>Additional Details:</b> </label>
                </div>              
                <div class="col-md-12">
                    <div class="row form-group" id="loanprog_details">
                         <label class="col-md-2  font-weight-bold align-self-start ">Loan Program Details: <i class="fa fa-info-circle tooltipClass text-primary popoverClass ml-2" data-html="true" data-content="Enter additional details about the loan program to help all users fully understand the eligibility criteria for this product."></i></label>
                           <div class="col-md-10">
                                <textarea  class="tinyMceClassB" rows="5" name="loanPgmDetails" id="loanPgmDetails">$loanPgmDetails</textarea>
                            </div>
                    </div>      
                            
                    <div class="row form-group" id="addnl_terms_and_req">
                         <label class="col-md-2  font-weight-bold align-self-start ">Additional Terms & Requirements for Loan Processing & Underwriting: <i class="fa fa-info-circle tooltipClass text-primary popoverClass ml-2" data-html="true" data-content="Enter additional details related to how to get pre-approved, submitting the loan, required docs, etc..."></i></label>
                           <div class="col-md-10">
                                <textarea  class="tinyMceClassB" rows="10" name='reqForLoanProUnderwriting' id='reqForLoanProUnderwriting'>$reqForLoanProUnderwriting</textarea>
                            </div>
                    </div>
                </div> 
                <div class="col-md-12 marketPlaceDepenShowHide {$marketPlaceStatus} ">
                    <div class="row form-group">
                         <label class="col-md-2  font-weight-bold align-self-start ">Upload Files:
                            <button class="btn btn-sm btn-icon btn-upload btn-success btnaddNew{$randomNum} "
                             data-parent="fileUploadMultiple"
                              data-clonediv="fileUploadMultipleDiv" type="button">  
                               <span class=" icon-md fas fa-plus "></span>
                            </button> 
                         </label>
                         
                           <div class="col-md-10 fileUploadMultiple">
EOT;
        if (count($marketPlaceFilesArray ?? []) == 0) {
            $marketPlaceFilesArray[] = [];
        }
        foreach ($marketPlaceFilesArray as $eachMarketPlaceFileKey => $eachMarketPlace) {
            $btnClass = $btnIcon = '';
            $uploadDocUrl = '';
            $deleteDoc = '';
//            if ($eachMarketPlaceFileKey < (count($marketPlaceFilesArray) - 1)) {
//                $btnClass = ' btn-success btn-addNew' . $randomNum;
//                $btnIcon = ' <span class=" icon-md fas fa-plus "></span> ';
//            } else {
            $btnClass = ' btn-danger btn-remove' . $randomNum . ' ';
            $btnIcon = ' <span class="fa fa-trash"> </span> ';
//            }
            if ($eachMarketPlace['fileName'] != '') {
                $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($eachMarketPlace['fileName']) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID) . '&opt=enc';
                $uploadDocUrl = '<a class="btn btn-sm btn-light btn-icon previewFile " target="_blank" href="' . $uploadDocUrl . '"><i class="fa fa-eye tooltipClass" title="Click to View Document"></i></a>';
                //   $deleteDoc = '<button type="button" class="btn btn-sm btn-icon btn-upload btn-remove btn-danger tooltipClass" target="_blank" ><span class="fa fa-trash"> </span></button>';
            }

            $formHtml1 .= <<<EOT
                    <div class="row form-group fileUploadMultipleDiv ">
                        <div class="col-md-5">
                            <input type="hidden" name="fileUploadId[]" value="{$eachMarketPlace['id']}"  placeholder="Please Enter File Name" class="form-control">
                            <input type="text" name="fileUploadName[]" value="{$eachMarketPlace['fileGivenName']}"  placeholder="Please Enter File Name" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <input type="file" name="fileUpload[]"  value="{$eachMarketPlace['fileName']}" class="form-control">
                        </div>
                        <div class="col-md-2">                                
                             $uploadDocUrl
                            <button class="btn btn-sm btn-icon btn-upload {$btnClass} ml-2 "
                             data-documentId="{$eachMarketPlace['id']}"
                             data-parent="fileUploadMultiple" data-clonediv="fileUploadMultipleDiv" type="button">  
                               {$btnIcon}
                            </button> 
                         </div>
                    </div>
EOT;
        }
        $formHtml1 .= <<<EOT
                           </div> 
                    </div>
                     <div class="row form-group">
                         <label class="col-md-2  font-weight-bold align-self-start ">Links:</label>
                           <div class="col-md-10 fileUploadMultipleLink">
EOT;
        //pr($marketPlaceLinksArray);
        // for ( $ef = 0; $ef < count($marketPlaceLinksArray); $ef++) {
        if (count($marketPlaceLinksArray ?? []) == 0) {
            $marketPlaceLinksArray[] = [];
        }
        foreach ($marketPlaceLinksArray as $eachMarketPlacelinkKey => $eachMarketPlace) {
            $btnClass = $btnIcon = '';
            if ($eachMarketPlacelinkKey < (count($marketPlaceLinksArray) - 1)) {
                $btnClass = ' btn-danger btn-remove ';
                $btnIcon = ' <span class="fa fa-trash"> </span> ';
            } else {
                $btnClass = ' btn-success btn-addNew' . $randomNum;
                $btnIcon = ' <span class=" icon-md fas fa-plus "></span> ';
            }
            $formHtml1 .= <<<EOT
                    <div class="row form-group fileUploadMultipleLinkDiv">
                        <div class="col-md-5">
                            <input type="text" name="links[linkName][]" value="{$eachMarketPlace['linkName']}"  placeholder="Please Enter Link Name" class="form-control">
                        </div>
                        <div class="col-md-5">
                              <input type="text" name="links[linkUrl][]" value="{$eachMarketPlace['linkUrl']}"  placeholder="Please Enter Link" class="form-control">
                        </div>
                        <div class="col-md-2">  
                            <button class="btn btn-sm btn-icon btn-upload btn-success $btnClass " 
                            data-parent="fileUploadMultipleLink" data-clonediv="fileUploadMultipleLinkDiv"
                            type="button">  
                              {$btnIcon}
                            </button> 
                         </div>
                    </div>
EOT;
        }
        $progressBarImg = IMG_PROGRESS_BAR;
        $formHtml1 .= <<<EOT
                           </div>
                    </div>
                     <div class="row form-group">
                             <label class="col-md-2  font-weight-bold align-self-start ">Contact Information Details: <i class="fa fa-info-circle tooltipClass text-primary popoverClass ml-2" data-html="true" data-content="This information will display in the marketplace search results for all users with permission to access the marketplace tool."></i></label>
                               <div class="col-md-10">
                                    <textarea  class="tinyMceClassB" rows="5" name="marketPlaceContactInfoDetails" id="marketPlaceContactInfoDetails">$marketPlaceContactInfoDetails</textarea>
                                </div>
                        </div>        
                       <div class="row form-group">
                         <label class="col-md-2  font-weight-bold align-self-start ">Company Details: <i class="fa fa-info-circle tooltipClass text-primary popoverClass ml-2" data-html="true" data-content="This information will display in the marketplace search results for all users with permission to access the marketplace tool."></i></label>
                           <div class="col-md-10">
                                <textarea  class="form-control" rows="5" name="marketPlaceCompanyDetails" id="marketPlaceCompanyDetails">$marketPlaceCompanyDetails</textarea>
                            </div>
                    </div>
                 </div>                 
             </div>
		<div class="right pad5" id="loaderDiv" style="display:none"><img src="{$progressBarImg}" alt="Progress Bar"></div>
EOT;
        $temDisableSelect = '';
        if (count($marketLoanProgramSelected ?? []) > 0) {
            $temDisableSelect = '';
            $marketLoanProgramNotificationChecked = ' checked ';
        } else {
            $temDisableSelect = ' disabled readonly=""  ';
            $marketLoanProgramNotificationChecked = ' ';
        }
        $formHtml1 .= <<<EOT
<div class="row " id="notificationsId"  >
            <div class=" col-lg-12 m-0 mb-4 px-0  marketPlaceDepenShowHide {$marketPlaceStatus}" >
                <label class=" font-weight-bold align-self-start   bg-secondary  py-2  col-lg-12"><b>Notification:</b> <i class="fa fa-info-circle tooltipClass text-primary popoverClass ml-2" data-html="true" data-content="Select a user or enter an email to be the default person to receive any financing request emails."></i></label>
            </div>    
            
            <div class="col-md-6  marketPlaceDepenShowHide {$marketPlaceStatus}">
                <div class="row  form-group">
                         <label class="col-md-2  font-weight-bold align-self-start ">Select Branch: </label>
                          <div class="col-md-2">
                               <div class="checkbox-list">
                                    <label class="checkbox">
                                     <input type= "checkbox" class="chosen-select selectALlCheckBox" data-id="branchesId" id ="selectALlCheckBox_1" />
                                     <span></span>
                                   Select All
                                    </label>
                                </div>	
                            </div>
                        <div class="col-md-8">
                        <select data-placeholder="- Select Branches -" name="selectedBranchesList[]" id="branchesId" {$temDisableSelect} class="chzn-select form-control" multiple=""   >
EOT;
        for ($j = 0; $j < count($executiveInfoArray ?? []); $j++) {
            $LMRExecutive = '';
            $executiveId = 0;
            $LMRExecutive = trim($executiveInfoArray[$j]['LMRExecutive']);
            $executiveId = trim($executiveInfoArray[$j]['executiveId']);
            $sOpt = Arrays::isSelectedArray($marketLoanProgramNotificationSelected['Branch'], $executiveId);
            $formHtml1 .= <<<EOT
  <option value="$executiveId" $sOpt >$LMRExecutive</option>
EOT;
        }
        $formHtml1 .= <<<EOT
      </select>
						  </div>
                </div>
            </div>
            
            <div class="col-md-6  marketPlaceDepenShowHide {$marketPlaceStatus}">
               <div class="row  form-group">
                     <label class="col-md-2  font-weight-bold align-self-start ">
                       Select Loan Officer:
                    </label>
                    <div class="col-md-2">	  
                         <div class="checkbox-list">
                                <label class="checkbox">
                            <input type= "checkbox" class="chosen-select selectALlCheckBox" data-id="brokersId" id ="selectALlCheckBox_2" />
                                 <span></span>
                                Select All
                                </label>
                         </div>
                    </div>
                    <div class="col-md-8">
                     <select data-placeholder="- Select Loan officer/Broker -" name="selectedBrokersList[]" id="brokersId" {$temDisableSelect} class="chzn-select form-control" multiple=""   >
EOT;
        for ($j = 0; $j < count($agentList); $j++) {
            $agentName = trim($agentList[$j]['brokerName']);
            $agentId = trim($agentList[$j]['brokerNumber']);
            $sOpt = Arrays::isSelectedArray($marketLoanProgramNotificationSelected['Agent'], $agentId);
            $formHtml1 .= <<<EOT
						  <option value="$agentId" $sOpt>$agentName</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>            
                    </div>
               </div>
            </div>     
            
            <div class="col-md-6  marketPlaceDepenShowHide {$marketPlaceStatus}">
                <div class="row form-group">
                    <label class="col-md-2  font-weight-bold align-self-start ">Select Employee(s):</label>
                    <div class="col-md-2">		
                      <div class="checkbox-list">
                                <label class="checkbox">
                                        <input type= "checkbox" class="chosen-select selectALlCheckBox" data-id="employeesId" id ="selectALlCheckBox_3" />
                                     <span></span>Select All
                                 </label>
                      </div>
                    </div>
                    <div class="col-md-8"> 
                    <select  data-placeholder="- Select Employee -" name="selectedEmployeeList[]" id="employeesId" {$temDisableSelect} class="chzn-select form-control" multiple="" >
EOT;
        for ($i = 0; $i < count($employeeListArray); $i++) {
            $empName = '';
            $AID = 0;
            $sOpt = '';
            $empName = ucwords(trim($employeeListArray[$i]['processorName'] . ' (' . $employeeListArray[$i]['role'] . ')'));
            $AID = trim($employeeListArray[$i]['AID']);
            $chOpt = '';
            $sOpt = Arrays::isSelectedArray($marketLoanProgramNotificationSelected['Employee'], $AID);
            if (trim($chOpt) == 'checked') {
                $sOpt = 'selected';
            }
            $formHtml1 .= <<<EOT
        <option value="$AID" $sOpt>$empName</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>
					</div>
                </div>
            </div>
            
            <div class="col-md-6  marketPlaceDepenShowHide {$marketPlaceStatus}">
                <div class="row form-group">
                    <label class="col-md-2  font-weight-bold align-self-start ">
                     Select Contacts:
                    </label>
                    <div class="col-md-2">		 
                      <div class="checkbox-list">
                        <label class="checkbox">
                    <input type= "checkbox" class="chosen-select selectALlCheckBox" data-id="contactsId" id ="selectALlCheckBox_4" /> 
                         <span></span>Select All
                        </label>
                      </div>
					 </div>
                    <div class="col-md-8">
                    <select data-placeholder="- Select Contacts-" name="selectedContactsList[]" id="contactsId" {$temDisableSelect} class="chzn-select form-control" multiple=""  >
EOT;
        for ($j = 0; $j < count($oContactsList); $j++) {
            $CTypeID = $companyNameContactName = '';
            $companyNameContactName = '' . trim($oContactsList[$j]['companyName']) . ' - ' . $oContactsList[$j]['contactName'] . ' ' . $oContactsList[$j]['contactLName'];
            $CID = trim($oContactsList[$j]['CID']);
            $sOpt = Arrays::isSelectedArray($marketLoanProgramNotificationSelected['Contact'], $CID);
            $companyNameContactName = $companyNameContactName . '(' . $contactTypeArray[$oContactsList[$j]['CTypeID']]['type'] . ')';
            $formHtml1 .= <<<EOT
						  <option value="$CID" $sOpt>$companyNameContactName</option>
EOT;
        }
        $formHtml1 .= <<<EOT
						</select>			
                    </div>
                </div>
            </div>
            <div class="col-md-6  marketPlaceDepenShowHide {$marketPlaceStatus}">
                 <div class="row form-group">
                    <label class="col-md-2  font-weight-bold align-self-start ">To email address:</label>
                    <div class="col-md-2">Use <b style="color:#ee0000;">;</b> (semicolon) to seperate email addresses</div>
                    <div class="col-md-8">
                        <textarea class="form-control" name="docRecipientEmail" {$temDisableSelect} id="docRecipientEmail">{$customEmailIdsNotify}</textarea>
			         </div>
                  </div>
           </div>
        </div>
		<table class="d-none">
			<tr>
				<td colspan="4" style="text-align:center;">
					<input class="button" type="submit" name="submit" id="submit" value="Save"> 
					<div class="right pad5" id="loaderDiv" style="display:none"><img src="{$progressBarImg}" alt="Progress Bar"></div>
				</td>
			</tr>
		</table>
EOT;
        $formHtml1 .= <<<EOT
	</form>
EOT;
        return $formHtml1;
    }
}
