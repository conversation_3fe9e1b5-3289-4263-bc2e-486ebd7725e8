<?php

namespace models\pops;

use models\Controllers\backoffice\LMRequest;
use models\Controllers\Base\generateWebformLinks;
use models\Controllers\LMRequest\Property;

class shareThisFile
{

    /**
     * @param int $LMRId
     * @return string
     */
    public function getHtml(int $LMRId): string
    {

        LMRequest::setLMRId($LMRId);
        generateWebformLinks::init($LMRId);

        $quickAppLinkBorrower = generateWebformLinks::getLink('quickAppLinkBorrower', 'Share this URL link to the borrower and only the quick app fields will display. Any data saved will be recorded under the borrower identity.');
        $quickAppLinkBorrowerMulti = generateWebformLinks::getLink('quickAppLinkBorrowerMulti', 'Share this URL link to the borrower and only the quick app fields will display. Any data saved will be recorded under the borrower identity.');
        $fullAppLinkBorrower = generateWebformLinks::getLink('fullAppLinkBorrower', 'Share this URL link to the borrower and only the full app fields will display. Any data saved will be recorded under the borrower identity.');
        $fullAppLinkBorrowerMulti = generateWebformLinks::getLink('fullAppLinkBorrowerMulti', 'Share this URL link to the borrower and only the full app fields will display. Any data saved will be recorded under the borrower identity.');
        $quickAppLinkBroker = generateWebformLinks::getLink('quickAppLinkBroker', 'Share this URL link to the broker and only the quick app fields will display. Any data saved will be recorded under the broker identity.');
        $quickAppLinkBrokerMulti = generateWebformLinks::getLink('quickAppLinkBrokerMulti', 'Share this URL link to the broker and only the quick app fields will display. Any data saved will be recorded under the broker identity.');
        $fullAppLinkBroker = generateWebformLinks::getLink('fullAppLinkBroker', 'Share this URL link to the broker and only the full app fields will display. Any data saved will be recorded under the broker identity.');
        $fullAppLinkBrokerMulti = generateWebformLinks::getLink('fullAppLinkBrokerMulti', 'Share this URL link to the broker and only the full app fields will display. Any data saved will be recorded under the broker identity.');
        $requiredDocsBorrower = generateWebformLinks::getLink('requiredDocsBorrower', 'This link is useful for the borrower to see their loan status and required docs status. They can also upload any missing docs from their mobile phone too. Any docs uploaded will be recorded under their name');
        $requiredDocsBorrowerBrokerLogo = generateWebformLinks::getLink('requiredDocsBorrowerBrokerLogo', 'This link is useful for the borrower to see their loan status and required docs status. They can also upload any missing docs from their mobile phone too. Any docs uploaded will be recorded under their name');
        $requiredDocsBroker = generateWebformLinks::getLink('requiredDocsBroker', 'This link is useful for the broker to see the loan status and required docs status. They can also upload any missing docs from their mobile phone too. Any docs uploaded will be recorded under their name');
        $requiredDocsLoanOfficer = generateWebformLinks::getLink('requiredDocsLoanOfficer', 'This link is useful for the broker to see the loan status and required docs status. They can also upload any missing docs from their mobile phone too. Any docs uploaded will be recorded under their name');
        $pointOfContactInfo = generateWebformLinks::$pointOfContactInfo;
        $dealStatusInfo = generateWebformLinks::$dealStatusInfo;
        $quickAppBorrowerRedacted = generateWebformLinks::getLink('quickAppBorrowerRedacted', 'Share this URL link with potential lenders or investors and only the quick app fields will display in read only without displaying the Entity, entity members, or borrower\'s personal info like name, phone or email. We also removed the 3rd party contacts like title, escrow, atty, etc... The lenders/investors will be able to easily provide a pre-approval offer.');
        $quickAppRedactedOfferSubmission = generateWebformLinks::getLink('quickAppRedactedOfferSubmission','');
        $fullAppBorrowerRedacted = generateWebformLinks::getLink('fullAppBorrowerRedacted', 'Share this URL link with potential lenders or investors and only the full app fields will display in read only without displaying the Entity, entity members, or borrower\'s personal info like name, phone or email. We also removed the 3rd party contacts like title, escrow, atty, etc... The lenders/investors will be able to easily provide a pre-approval offer.');
        $fullAppShareLinkSubmitOffer = generateWebformLinks::getLink('fullAppShareLinkSubmitOffer', '');
        $quickAppLinkReadOnly = generateWebformLinks::getLink('quickAppLinkReadOnly', 'Share this URL link with potential lenders or investors and only the quick app fields will display in read only. They will be able to easily provide a pre-approval offer');
        $quickAppLinkReadOnlySubmitOffer = generateWebformLinks::getLink('quickAppLinkReadOnlySubmitOffer', '');
        $fullAppReadOnly = generateWebformLinks::getLink('fullAppReadOnly', 'Share this URL link with potential lenders or investors and only the full app fields will display in read only. They will be able to easily provide a pre-approval offer.');
        $fullAppReadOnlySubmitOffer = generateWebformLinks::getLink('fullAppReadOnlySubmitOffer', '');
        $quickAppReadOnlyUploadDocs = generateWebformLinks::getLink('quickAppReadOnlyUploadDocs', 'Share this URL link with potential lenders or investors and the quick app fields & all the uploaded docs will be available in read only. This is useful when submitting a file to a lender or getting a pre-approval.');
        $quickAppShareLinkAUDSubmitOffer = generateWebformLinks::getLink('quickAppShareLinkAUDSubmitOffer', '');
        $fullAppReadOnlyUploadDocs = generateWebformLinks::getLink('fullAppReadOnlyUploadDocs', 'Share this URL link with potential lenders or investors and the full app fields & all the uploaded docs will be available in read only. This is useful when submitting a file to a lender or getting a pre-approval.');
        $fullAppShareLinkAUDSubmitOffer = generateWebformLinks::getLink('fullAppShareLinkAUDSubmitOffer', '');
        $rentRollWebFormBO = generateWebformLinks::getLink('rentRollWebFormBO', 'Rent Roll Non Sign Link');
        $rentRollWebFormBOSign = generateWebformLinks::getLink('rentRollWebFormBOSign', 'Rent Roll Signable Link');
        $rentRollWebFormBOReadOnly = generateWebformLinks::getLink('rentRollWebFormBOReadOnly', 'Rent Roll Read Only Link');
        $uniformResLoanApp = generateWebformLinks::getLink('uniformResLoanApp','Uniform Residential Loan Application-Non Sign');
        $uniformResLoanAppSign = generateWebformLinks::getLink('uniformResLoanAppSign', 'Uniform Residential Loan Application-Sign');
        $uniformResLoanAppReadOnly = generateWebformLinks::getLink('uniformResLoanAppReadOnly', 'Uniform Residential Loan Application-Sign');
        $PFSWebformBO = generateWebformLinks::getLink('PFSWebformBO', 'PFS Webform');
        $PFSWebformBOSign = generateWebformLinks::getLink('PFSWebformBOSign', 'PFS Webform');
        $PFSWebformBOReadOnly = generateWebformLinks::getLink('PFSWebformBOReadOnly','PFS Webform');

        $CashFlowWebformBO = generateWebformLinks::getLink('CashFlowWebformBO', 'Subject Property CashFlow');
        $CashFlowWebformBOSign = generateWebformLinks::getLink('CashFlowWebformBOSign', 'Subject Property CashFlow');
        $CashFlowWebformBOReadOnly = generateWebformLinks::getLink('CashFlowWebformBOReadOnly', 'Subject Property CashFlow');
        $fullAppLinkCoBorrower = generateWebformLinks::getLink('fullAppLinkCoBorrower', 'Share this URL link to the co-borrower and only the full app fields will display. Any data saved will be recorded under the co-borrower identity.');
        $quickAppLinkCoBorrower = generateWebformLinks::getLink('quickAppLinkCoBorrower', 'Share this URL link to the co-borrower and only the quick app fields will display. Any data saved will be recorded under the co-borrower identity.');
        $fullAppLinkCoBorrowerMulti = generateWebformLinks::getLink('fullAppLinkCoBorrowerMulti', 'Share this URL link to the co-borrower and only the full app fields will display. Any data saved will be recorded under the co-borrower identity.');
        $quickAppLinkCoBorrowerMulti = generateWebformLinks::getLink('quickAppLinkCoBorrowerMulti', 'Share this URL link to the co-borrower and only the quick app fields will display. Any data saved will be recorded under the co-borrower identity.');
        $paymentInfoWebFormBO = generateWebformLinks::getLink('paymentInfoWebFormBO', 'ACH/Check -Non Sign');
        $paymentInfoWebFormBOSign = generateWebformLinks::getLink('paymentInfoWebFormBOSign', 'ACH/Check -Non Sign');
        $paymentInfoWebFormBOReadOnly = generateWebformLinks::getLink('paymentInfoWebFormBOReadOnly', 'ACH/Check Read Only');
        $creditPaymentInfoWebFormBO = generateWebformLinks::getLink('creditPaymentInfoWebFormBO', 'Credit Card Info  -Non Sign');
        $creditPaymentInfoWebFormBOSign = generateWebformLinks::getLink('creditPaymentInfoWebFormBOSign', 'Credit Card Info  -Non Sign');
        $creditPaymentInfoWebFormBOReadOnly = generateWebformLinks::getLink('creditPaymentInfoWebFormBOReadOnly', 'Credit Card Info  Read Only');
        $ACHCreditPaymentInfoWebFormBO = generateWebformLinks::getLink('ACHCreditPaymentInfoWebFormBO', 'ACH/Check/CreditCard  -Non Sign');
        $ACHCreditPaymentInfoWebFormBOSign = generateWebformLinks::getLink('ACHCreditPaymentInfoWebFormBOSign', 'ACH/Check/CreditCard  -Non Sign');
        $ACHCreditPaymentInfoWebFormBOReadOnly = generateWebformLinks::getLink('ACHCreditPaymentInfoWebFormBOReadOnly', 'ACH/Check/CreditCard  Read Only');

        $additionalGuarantors = generateWebformLinks::getLink('additionalGuarantors', 'Additional Guarantors -Non Sign');
        $additionalGuarantorsSign = generateWebformLinks::getLink('additionalGuarantorsSign', 'Additional Guarantors -Sign');
        $additionalGuarantorsReadOnly = generateWebformLinks::getLink('additionalGuarantorsReadOnly', 'Additional Guarantors Read Only');
        $submitReviseSow = generateWebformLinks::getLink('submitReviseSow', 'Submit/Revise Scope of Work');

        $tblFile = LMRequest::File();
        $propertyCount = Property::getPropertyCount($LMRId);
        $formHtml1 = <<<EOT
<script type="text/javascript" src="/assets/js/popup.js"></script>
<div class="row">
<div class="table-responsive">
<table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center" >
    <thead class="thead-light">
        <tr>
            <th colspan="4" class="text-center"><h5>Share Links for Borrower or Broker</h5></th>
        </tr>
            <tr>
                <th class="text-center" colspan="2"><h5>Links to Quick App</h5></th>
                <th class="text-center" colspan="2"><h5>Links to Full App</h5></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="p-4">$quickAppLinkBorrower</td>
                <td class="p-4">$quickAppLinkBorrowerMulti</td>
                <td class="p-4">$fullAppLinkBorrower</td>
                <td class="p-4">$fullAppLinkBorrowerMulti</td>
            </tr>

EOT;
        if ($tblFile->isCoBorrower) {
            $formHtml1 .= <<<EOT
                <tr>
                    <td class="p-4">$quickAppLinkCoBorrower</td>
                    <td class="p-4">$quickAppLinkCoBorrowerMulti</td>
                    <td class="p-4">$fullAppLinkCoBorrower</td>
                    <td class="p-4">$fullAppLinkCoBorrowerMulti</td>
                </tr>
EOT;
        }
        $formHtml1 .= <<<EOT
            <tr>
                <td class="p-4">$quickAppLinkBroker</td>
                <td class="p-4">$quickAppLinkBrokerMulti</td>
                <td class="p-4">$fullAppLinkBroker</td>
                <td class="p-4">$fullAppLinkBrokerMulti</td>
            </tr>
        </tbody>
</table>
<table class="table table-hover table-bordered table-condensed table-sm table-vertical-center" >
    <thead class="thead-light">
            <tr class="thead-light">
                <th colspan="3" style="text-align: center"><h5>Draw Management</h5></th>
            </tr>
    </thead>
        <tbody>
            <tr>
                <td class="p-4" style="width:30%">$submitReviseSow</td>
                <td></td>
                <td></td>
            </tr>
        </tbody>
</table>
<table class="table table-hover table-bordered table-condensed table-sm table-vertical-center" >
    <thead class="thead-light">
            <tr class="thead-light">
                <th colspan="3" style="text-align: center"><h5>Loan Status & Doc Upload Portal</h5></th>
            </tr>
    </thead>
        <tbody>
            <tr>
                 <td class="p-4">$requiredDocsBorrower</td>
                 <td class="p-4">$requiredDocsBroker</td>
                 <td class="p-4">$requiredDocsLoanOfficer</td>
            </tr>
            <tr>
            <td class="p-4">$requiredDocsBorrowerBrokerLogo</td>
            <td></td>
            <td></td>
            </tr>
        </tbody>
</table>
    $pointOfContactInfo
    $dealStatusInfo
<table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center" >
    <thead class="thead-light">
        <tr>
            <th colspan="4" class="text-center"><h5>Share Links for 3rd Parties (Lenders, Investors, Title)</h5></th>
        </tr>
            <tr>
                <th class="wt-30" colspan="3" style="text-align: center"><h5>Quick App (View Only)</h5></th>
                <th class="wt-30" colspan="3" style="text-align: center"><h5>Full App (View Only)</h5></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                 <td class="p-4">$quickAppBorrowerRedacted</td>
                 <td class="p-4">
EOT;
        if (generateWebformLinks::$linkWithOfferSubmission) {
            $formHtml1 .= $quickAppRedactedOfferSubmission;
        }
        $formHtml1 .= <<<EOT
                </td>
                 <td class="p-4">$fullAppBorrowerRedacted</td>
                <td class="p-4">
EOT;
        if (generateWebformLinks::$linkWithOfferSubmission) {
            $formHtml1 .= $fullAppShareLinkSubmitOffer;
        }
        $formHtml1 .= <<<EOT
                </td>
            </tr>
            <tr>
                 <td class="p-4">$quickAppLinkReadOnly</td>
                <td class="p-4">

EOT;
        if (generateWebformLinks::$linkWithOfferSubmission) {
            $formHtml1 .= $quickAppLinkReadOnlySubmitOffer;
        }
        $formHtml1 .= <<<EOT
                </td>
                 <td class="p-4">$fullAppReadOnly</td>
                <td class="p-4">
EOT;
        if (generateWebformLinks::$linkWithOfferSubmission) {
            $formHtml1 .= $fullAppReadOnlySubmitOffer;
        }
        $formHtml1 .= <<<EOT
                </td>
            </tr>
            <tr>
                 <td class="p-4">$quickAppReadOnlyUploadDocs</td>
            <td class="p-4">

EOT;
        if (generateWebformLinks::$linkWithOfferSubmission) {
            $formHtml1 .= $quickAppShareLinkAUDSubmitOffer;
        }
        $formHtml1 .= <<<EOT
                </td>
                <td class="p-4">$fullAppReadOnlyUploadDocs</td>
                <td class="p-4">
EOT;
        if (generateWebformLinks::$linkWithOfferSubmission) {
            $formHtml1 .= $fullAppShareLinkAUDSubmitOffer;
        }
        $formHtml1 .= <<<EOT
                </td>
            </tr>
    </tbody>
    </table>
EOT;
        if (in_array(generateWebformLinks::$fileType, ['HMLO', 'loc'])) {
            $formHtml1 .= <<<EOT
<table class="table table-hover  table-bordered table-condensed table-sm table-vertical-center" >
    <thead class="thead-light">
       <tr>
            <th colspan="3" class="text-center"><h5>Info Requests</h5></th>
        </tr>
        <tr>
            <th>Non-Signable</th>
            <th>Signable</th>
            <th>Read Only</th>
        </tr>
    </thead>
    <tbody>
EOT;
            if ($propertyCount) {
                $formHtml1 .= <<<EOT
<tr>
            <td class="p-4" >$rentRollWebFormBO</td>
             <td class="p-4" >$rentRollWebFormBOSign</td>
            <td class="p-4" >$rentRollWebFormBOReadOnly</td>
        </tr>
EOT;
            }
            $formHtml1 .= <<<EOT
<tr>
            <td class="p-4" >$uniformResLoanApp</td>
              <td class="p-4" >$uniformResLoanAppSign</td>
             <td class="p-4" >$uniformResLoanAppReadOnly</td>
        </tr>
        <tr>
            <td class="p-4" >$PFSWebformBO</td>
            <td class="p-4" >$PFSWebformBOSign</td>
            <td class="p-4" >$PFSWebformBOReadOnly</td>
        </tr>

                <tr>
            <td class="p-4" >$CashFlowWebformBO</td>
            <td class="p-4" >$CashFlowWebformBOSign</td>
            <td class="p-4" >$CashFlowWebformBOReadOnly</td>
        </tr>
        <tr>
            <td class="p-4">$paymentInfoWebFormBO</td>
            <td class="p-4">$paymentInfoWebFormBOSign</td>
            <td class="p-4">$paymentInfoWebFormBOReadOnly</td>
        </tr>
        <tr>
            <td class="p-4">$creditPaymentInfoWebFormBO</td>
            <td class="p-4">$creditPaymentInfoWebFormBOSign</td>
            <td class="p-4">$creditPaymentInfoWebFormBOReadOnly</td>

        </tr>
        <tr>
            <td class="p-4">$ACHCreditPaymentInfoWebFormBO</td>
            <td class="p-4">$ACHCreditPaymentInfoWebFormBOSign</td>
            <td class="p-4">$ACHCreditPaymentInfoWebFormBOReadOnly</td>
        </tr>
            <tr>
            <td class="p-4">$additionalGuarantors</td>
            <td class="p-4">$additionalGuarantorsSign</td>
            <td class="p-4">$additionalGuarantorsReadOnly</td>
        </tr>
    </tbody>
</table>
EOT;
        }
        $formHtml1 .= <<<EOT

</div>
</div>
EOT;
        return $formHtml1;
    }
}
