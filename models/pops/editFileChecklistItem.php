<?php
namespace models\pops;


use models\composite\oChecklist\getChecklistForFile;
use models\composite\oFile\getFileInfo;
use models\composite\oPC\getPCInternalServiceType;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\constants\gl\glRequiredByArray;
use models\cypher;
use models\lendingwise\tblPCChecklistCategory;
use models\standard\Arrays;
use models\standard\Integers;
use models\standard\Strings;

/**
 *
 */
class editFileChecklistItem
{

    public $thankYouMessage = '';
    public $error = '';

    /**
     * @param int $ajax
     * @param array $inArray
     * @return string
     */
    public function getFormHtml(int $ajax = 0, array $inArray = []): string
    {
        $glRequiredByArray = glRequiredByArray::$glRequiredByArray;

        $formHtml = '';

        $cancelLink = $ajax ? '<a href="#" class="close-overlay"><input class="button cancelButton" type="button" name="cancel" id="cancel" value="Cancel"></a>' : '';
        $saveUrl = CONST_URL_POPS . 'saveCheckListInfo.php';

        $UID = null;
        $PCClientTypeInfoArray = [];
        $checklistInfoArray = [];
        $fileInfoArray = [];
        $checklistId = 0;
        $docName = '';
        $staffArray = [];
        $resultChecklistInfoArray = [];
        $resultRequiredBy = [];
        $LMRInfoArray = $filePCInfo = $BranchInfoArray = $BrokerInfoArray = $AssignedStaffArray = $employeeInfo = [];
        $fileMultiContacts = [];
        $isCoBorrower = $borrowerEmail = $coBorrowerEmail = $clientName = $coBorrowerName = '';
        $LMRExecutive = '';
        $executiveEmail = '';
        $brokerName = $brokerEmail = '';
        $allowPCToSendCustomText = '';
        $checklistDesc = '';
        $refDocName = '';
        $refDocUrl = '';

        $PCID = $inArray['PCID'];
        if (array_key_exists('checklistId', $inArray)) {
            $checklistId = $inArray['checklistId'];
        }

        $MultiMType = $inArray['SMC'];
        $searchModuleType = $inArray['searchModuleType'];
        $inArray['opt'] = 'WMS';
        $fileID = $inArray['fileID'];
        $selServiceType = $inArray['selServiceType'];
        $addOpt = $inArray['addOpt'];
        $executiveId = $inArray['executiveId'];
        $userName = $inArray['userName'];
        $userEmail = $inArray['userEmail'];



        $fileInArr = ['LMRId' => $fileID];
        if ($fileID > 0) {
            $fileInfoArray = getFileInfo::getReport($fileInArr);
        }

        if (array_key_exists($fileID, $fileInfoArray)) {
            $LMRInfoKeyArray = $fileInfoArray[$fileID];
            $LMRInfoArray = $LMRInfoKeyArray['LMRInfo'];
            $filePCInfo = $LMRInfoKeyArray['PCInfo'];
            $BranchInfoArray = $LMRInfoKeyArray['BranchInfo'];
            $BrokerInfoArray = $LMRInfoKeyArray['BrokerInfo'];
            $AssignedStaffArray = $LMRInfoKeyArray['AssignedBOStaffInfo'];
            $employeeInfo = $LMRInfoKeyArray['employeeInfo'];
            $fileMultiContacts = $LMRInfoKeyArray['fileContacts']['multiContact'] ?? [];
        }


        if (count($LMRInfoArray) > 0) {
            $borrowerName = $LMRInfoArray['borrowerName'];
            $borrowerLName = $LMRInfoArray['borrowerLName'];
            $isCoBorrower = trim($LMRInfoArray['isCoBorrower']);
            $coBorrowerFName = $LMRInfoArray['coBorrowerFName'];
            $coBorrowerLName = $LMRInfoArray['coBorrowerLName'];
            $borrowerEmail = $LMRInfoArray['borrowerEmail'];
            $coBorrowerEmail = $LMRInfoArray['coBorrowerEmail'];
            $clientName = ucwords($borrowerName . ' ' . $borrowerLName);
            $coBorrowerName = ucwords($coBorrowerFName . ' ' . $coBorrowerLName);
        }

        for ($i = 0; $i < count($AssignedStaffArray); $i++) {
            $staffArray[trim($AssignedStaffArray[$i]['AID'])] = trim($AssignedStaffArray[$i]['AID']);
        }

        if (count($BranchInfoArray) > 0) {
            $LMRExecutive = trim($BranchInfoArray['LMRExecutive']);
            $executiveEmail = trim($BranchInfoArray['executiveEmail']);
            $executiveId = $BranchInfoArray['executiveId'];
        }

        if (count($BrokerInfoArray) > 0) {
            $brokerFName = $BrokerInfoArray['firstName'];
            $brokerLName = $BrokerInfoArray['lastName'];
            $brokerName = ucwords($brokerFName . ' ' . $brokerLName);
            $brokerEmail = $BrokerInfoArray['email'];
        }

        if (count($filePCInfo) > 0) {
            $allowPCToSendCustomText = $filePCInfo['allowPCToSendCustomText'];
        }

        $myModulesArray = [];
        $modulesInfoArray = [];
        if ($PCID > 0) {
            $ip['PCID'] = $PCID;
            $ip['keyNeeded'] = 'n';
            $modulesInfoArray = getPCModules::getReport($ip);
        }
        for ($i = 0; $i < count($modulesInfoArray); $i++) {
            $myModulesArray [] = $modulesInfoArray[$i]['moduleCode'];
        }

        $userGroup = null;
        if (isset($_REQUEST['userGroup'])) {
            $userGroup = cypher::myDecryption(trim($_REQUEST['userGroup']));
        }
        if (isset($_REQUEST['userNumber'])) {
            $UID = cypher::myDecryption(trim($_REQUEST['userNumber']));
        }

        $inArray['moduleCode'] = $MultiMType;
        $PCLMRClientTypeInfoArray = getPCServiceType::getReport($inArray);
        $inArray['moduleCode'] = '';

        if (array_key_exists('checklistId', $inArray)) {
            $resultChecklistInfoArray = getChecklistForFile::getReport($inArray);
        }

        if (count($resultChecklistInfoArray) > 0) {
            $checklistInfoArray = $resultChecklistInfoArray['checklistInfo'];
            $resultRequiredBy = $resultChecklistInfoArray['resultRequiredBy'];
        }
        $categoryId = null;
        foreach ($checklistInfoArray as $checkList) {
            $docName = htmlspecialchars(trim($checkList['docName']));
            $checklistDesc = trim($checkList['description']);
            $refDocName = trim($checkList['refDocName']);
            $refDocUrl = trim($checkList['refDocUrl']);
            $categoryId = trim($checkList['categoryId']);
        }

        /** RequiredBy Start - March 23, 2017**/
        $tempRequiredBy = '';

        for ($r = 0; $r < count($resultRequiredBy); $r++) {
            if ($r > 0) {
                $tempRequiredBy .= ',' . trim($resultRequiredBy[$r]['requiredBy']);
            } else {
                $tempRequiredBy .= trim($resultRequiredBy[$r]['requiredBy']);
            }
        }
        $resultRequiredByArray = array_unique(explode(',', $tempRequiredBy));

        /* */

        if ($fileID > 0 && $fileID != '') {
            unset($glRequiredByArray['Lender']);
        }

        /** RequiredBy END**/

        if (count($PCLMRClientTypeInfoArray) > 0) {
            if (array_key_exists($PCID, $PCLMRClientTypeInfoArray)) {
                $PCClientTypeInfoArray = $PCLMRClientTypeInfoArray[$PCID];
            }
        }

        if (count($PCLMRClientTypeInfoArray) > 0) {
            if (array_key_exists($PCID, $PCLMRClientTypeInfoArray)) {
                $PCClientTypeInfoArray = $PCLMRClientTypeInfoArray[$PCID];
            }
        }

        $ip['moduleCode'] = $MultiMType;
        $PCServiceTypeInternalLoanInfoArray = getPCInternalServiceType::getReport($ip);
        $PCClientTypeInfoArray = (array_merge($PCClientTypeInfoArray, $PCServiceTypeInternalLoanInfoArray));
        $PCClientTypeInfoArraySortByModule = Arrays::buildKeyByValue($PCClientTypeInfoArray, 'moduleCode');

        $PCClientTypeInfoArray = [];
        foreach ($PCClientTypeInfoArraySortByModule as $ModuleCodeArr) {
            $PCClientTypeInfoArray = array_merge($PCClientTypeInfoArray, $ModuleCodeArr);
        }
        $tblPCChecklistCategory = tblPCChecklistCategory::getPCCategories($PCID,1);
        $formHtml .= <<<EOT
<script>
/* Show and Hide PC Checklist Sevices Div's */
function showAndHideModuleServiceTypesForCheckList(t, val, opt) {
	if (opt === 'show') {
		document.getElementById("div_"+val).style.display = 'block';
	} else {
		document.getElementById("div_"+val).style.display = 'none';
	}
}
</script>
<script type="text/javascript" src="/assets/js/popup.js"></script>
 <form name="checklistForm" id="checklistForm" method="POST"   enctype="multipart/form-data" action="$saveUrl"
EOT;
        if ($addOpt == 'FCL') {
            $formHtml .= <<<EOT
	onsubmit="javascript:return validateFileChecklist();">
EOT;
        }
        $refDocNameHtml = '';
        if ($refDocName != '') {
            $refDocNameUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . cypher::myEncryption($refDocName) . '&fd=' . cypher::myEncryption(CONST_PATH_PC_UP_DOC . $PCID) . '&opt=enc';

            $checklistIdTxt = "'" . cypher::myEncryption($checklistId) . "'";
            $reqDocType = "'" . cypher::myEncryption('FCL') . "'";
            $refDocNameHtml = '<div 
class="form-group mt-2 refDocNameHtmlDiv">
<a 
class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass" 
style="text-decoration:none;"
                                   rel="nofollow" 
                                   target="_blank"
                                    href="' . $refDocNameUrl . '"
                                   title="Click to view File Doc Info"><i class="fa fa-eye tooltipClass" ></i></a>
                                   <a 
                                   class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass" 
                                   style="text-decoration:none;"  onclick="editFileChecklistItemControls.deleteRefDoc(' . $checklistIdTxt . ',' . $reqDocType . ')"  
                                   title="Click to Delete"><i class="tooltipClass flaticon2-trash" ></i></a>
                                   </div>';
        }

        $formHtml .= <<<EOT
	<input type="hidden" name="PCID" id="PCID" value="$PCID">
	<input type="hidden" name="checklistId" id="checklistId" value="$checklistId">
	<input type="hidden" name="exC" id="exC" value="$docName">
		<input type="hidden" name="exRdesc" id="exRdesc" value="$checklistDesc">

	<input type="hidden" name="searchModuleType" id="searchModuleType" value="$searchModuleType">
	<input type="hidden" name="UID" id="UID" value="$UID">
	<input type="hidden" name="userGroup" id="userGroup" value="$userGroup">
	<input type="hidden" name="fileID" id="fileID" value="$fileID">
	<input type="hidden" name="serviceType" id="serviceType" value="$selServiceType">
	<input type="hidden" name="tabOpt" id="tabOpt" value="DOC">
	<input type="hidden" name="executiveId" id="executiveId" value="$executiveId">

	<input type="hidden" name="brokerName" id="brokerName" value="$brokerName">
	<input type="hidden" name="clientName" id="clientName" value="$clientName">
	<input type="hidden" name="coBorrowerName" id="coBorrowerName" value="$coBorrowerName">
	<input type="hidden" name="LMRExecutive" id="LMRExecutive" value="$LMRExecutive">
	<input type="hidden" name="execName" id="execName" value="$userName">
	<input type="hidden" name="userEmail" id="userEmail" value="$userEmail">
	<input type="hidden" name="LMRId" id="LMRId" value="$fileID">
	<input type="hidden" name="employeeIds" id="employeeIds" value="" >
	<input type="hidden" name="allowPCToSendCustomText" id="allowPCToSendCustomText" value="$allowPCToSendCustomText">
	<input type="hidden" name="contact_ids" id="contact_ids" value="" >

	<div class="card-body">
		<div class="dragable-class">
		<div class="form-group">
                     <label class="font-weight-bolder">Please Select Category:</label>
                     <select name="categoryId" 
                             id="categoryId" 
                             class="form-control chzn-select mandatory"
                             data-placeholder="Please Select Category">
                        <option value=""></option>
EOT;
        foreach ($tblPCChecklistCategory as $category) {
            $encryptedId = cypher::myEncryption($category->id);
            $isSelected = Arrays::isSelected($categoryId, $category->id);
            $formHtml .= <<<EOT
                    <option value="{$encryptedId}" {$isSelected}>{$category->categoryName}</option>
EOT;
        }
        $formHtml .= <<<EOT
                     </select>      
            </div>
			<div class="form-group">
                     <label class="font-weight-bolder">Enter Required Docs Name:</label>
                     <input  value="$docName" type="text"  name="checklistItem" id="checklistItem" class="form-control mandatory">
            </div>
				<div class="form-group">
                    <label class="font-weight-bolder">Description:</label>
                    <textarea class="form-control" rows="3" cols="200" name="checklistDesc" id="checklistDesc">$checklistDesc</textarea>
            </div>
            
            <div class="form-group" >
                    <label class="font-weight-bolder">Reference Document/Template:
                    <a style="text-decoration:none" class="fa fa-info-circle fa-lg tip-bottom icon-dark-grey" 
                    data-toggle="tooltip"
                    data-html="true"
                    data-theme="dark"
                    title="You can upload a reference file or URL link that allows users to download. It will display next to the required doc name.">&nbsp;</a></label>
                    <input type="FILE"  class="filelevelDocChooser form-control" name="refDocName" id="refDocFile" value="$refDocName"  />
                    <input type="hidden" name="uploadedRefDocName" id="uploadedRefDocName"  value="$refDocName"> 
                     $refDocNameHtml
            </div>
            <div class="form-group" >
                    <label class="font-weight-bolder">Ref URL:
                    <a style="text-decoration:none" class="fa fa-info-circle fa-lg tip-bottom icon-dark-grey"
                    data-toggle="tooltip"
                    data-html="true"
                    data-theme="dark"
                     title="You can upload a reference file or URL link that allows users to download. It will display next to the required doc name."></a></label>
                    <input class="form-control" type="text"  name="refDocUrl" id="refDocUrl" value="$refDocUrl"> 
            </div>
        <div class="form-group">
          <label class="font-weight-bold">Display & make available for other users:</label>
                <select data-placeholder="" name="requiredBy[]" id="requiredBy" class="chzn-select1" multiple="" style="width:100%;">
EOT;
        $resultRequiredByArray = (array_filter($resultRequiredByArray));
        foreach ($glRequiredByArray as $reqByKey => $reqByValue) {
            $selOpt = Arrays::isSelectedArray($resultRequiredByArray, $reqByKey);
            if (count($resultRequiredByArray) == 0) {
                $selOpt = 'selected';
            }
            //if($reqByValue == 'Lender') { } else {
            $formHtml .= <<<EOT
                    <option $selOpt value="$reqByKey">$reqByValue</option>
EOT;
            //}
        }
        $formHtml .= <<<EOT
              </select>
              <br><span class="note">Note: All the Required Docs list will be visible to the backoffice users.</span>
        </div>
EOT;
        if ($addOpt == 'FCL') {
            $tempChkFldName = 'services_' . $searchModuleType . '[]';
            $formHtml .= <<<EOT
	<input type="hidden" name="moduleName[]" id="moduleName" value="$searchModuleType"/>
	<input type="hidden" name="$tempChkFldName" id = "services_$tempChkFldName" value="$selServiceType"/>
EOT;
        }

        /** Notifying Section Start. **/
        if ($addOpt == 'FCL') {
            $formHtml .= <<<EOT
			<div class="form-group row">
			<label class="col-lg-4 font-weight-bolder font-weight-bolder">Do you want to notify anyone?</label>
			    <div class="col-lg-6">
                <span class="switch switch-icon">
	            <label>
		        <input class="form-control " id="notifyUsersDiv" type="checkbox"  value="0"  onchange="toggleSwitch('notifyUsersDiv','notifyUsers','1','0' ); showAndHideNotifyUsers();"  />
		 		<input type="hidden" name="notifyUsers" id="notifyUsers" value="0">
                <span></span>
	            </label>
                </span>
			</div>

		<div  class="hide notifyUsers">
			<div class="form-group row hide notifyUsers align-items-center">
			    <label class="col-lg-12 font-weight-bolder bg-gray-200 text-center font-weight-bolder py-2">Select the user(s) to notify reg. this update:</label>
			</div>
EOT;

            /* Borrower Info */
            if (trim($borrowerEmail) != '') {
                if (Integers::checkEmailIsDummy($borrowerEmail) == '0') {
                    $formHtml .= <<<EOT
		<div class="form-group row">
			<label class="col-md-3 font-weight-bolder">Borrower:</label>
				<div class="col-md-1"><label for="borrowerEmail"><input class="newCheck" type="checkbox" name="borrowerEmail" id="borrowerEmail" value="$borrowerEmail"><span>&nbsp;</span></label></div>
				<div class="col-md-6">$clientName</div>
		</div>
EOT;
                }
            }

            /* Co Borrower Info */
            if (trim($coBorrowerEmail) != '' && $isCoBorrower == 1) {
                if (Integers::checkEmailIsDummy($borrowerEmail) == '0') {
                    $formHtml .= <<<EOT
		<div class="form-group row">
			<label class="col-md-3 font-weight-bolder">Co-Borrower:</label>
				<div class="col-md-1"><label for="coBorrowerEmail"><input class="newCheck" type="checkbox" name="coBorrowerEmail" id="coBorrowerEmail" value="$coBorrowerEmail"><span>&nbsp;</span></label></div>
				<div class="col-md-6">$coBorrowerName</div>
		</div>
EOT;
                }
            }

            /* Agent/Broker and Branch Info */
            $formHtml .= <<<EOT
		<div class="form-group row">
			<label id="agentDiv1"  class="col-md-3 font-weight-bolder">Loan Officer/Broker:</label>
				<div class="col-md-1" ><label for="brokerEmail"><input class="newCheck" type="checkbox" name="brokerEmail" id="brokerEmail" value="$brokerEmail"><span>&nbsp;</span></label></div>
				<div class="col-md-4">$brokerName</div>
		</div>
		<div class="form-group row">
			<label id="branchDiv1"  class="col-md-3 font-weight-bolder">Branch:</label>
				<div class="col-md-1" ><label for="branchEmail"><input class="newCheck" type="checkbox" name="branchEmail" id="branchEmail" value="$executiveEmail"><span>&nbsp;</span></label></div>
				<div class="col-md-4">$LMRExecutive</div>
		</div>
EOT;

            /* Assigned Employee Info */
            $empCnt = count($employeeInfo);
            if (count($employeeInfo) > 0) {
                $formHtml .= <<<EOT
		<input type="hidden" name="empCnt" id="empCnt" value="$empCnt" >
		<div class="form-group hide notifyUsers row align-items-center px-10">
        <label class="font-weight-bolder mb-4">Employee(s):</label>
           <select  title="- Select Employee(s) -"  name="employeeIds[]"  id= "employeeIds" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true"  data-size="5" data-actions-box="true"  style="width:250px;">
EOT;

                $e1 = 0;
                for ($e = 0; $e < $empCnt; $e++) {
                    $employeeName = trim($employeeInfo[$e]['processorName']);
                    $employeeRole = trim($employeeInfo[$e]['role']);
                    $employeeId = trim($employeeInfo[$e]['AID']);
                    $clsName = 'text-dark-75';

                    if (array_key_exists($employeeId, $staffArray)) {
                        $clsName = 'bg-danger-o-40 text-white';
                    }

                    $formHtml .= <<<EOT
                    <option value="$employeeId" class="$clsName" data-subtext="$employeeRole" >$employeeName</option>
EOT;
                    $e1++;
                }
                $formHtml .= <<<EOT
              </select>
</div>
EOT;
            }
            $formHtml .= <<<EOT
      	<div class="form-group hide notifyUsers row align-items-center px-10">
EOT;

            if (count($fileMultiContacts) > 0) {
                $formHtml .= <<<EOT
						   <label class="font-weight-bolder mb-4">File Contact(s):</label>
         <select  title="- Select File Contact(s) -"  name="CIDs[]"  id= "CIDs" class="chzn-selectShowSelectAll form-control" multiple="" data-live-search="true"  data-actions-box="true"  style="width:250px;">
EOT;
                    foreach ($fileMultiContacts as $multiContactKey => $multiContactValue) {
                        $fileContactRole = '';
                        for ($m = 0; $m < count($multiContactValue); $m++) {
                            $mutiContactCID = '';
                            if ($multiContactValue[$m]['email'] != '') {
                                $fileContactRole = $multiContactValue[$m]['cRole'];
                                $mutiContactCID = $multiContactValue[$m]['CID'];
                                $formHtml .= <<<EOT
		<option value="$mutiContactCID" data-subtext="$fileContactRole" >{$multiContactValue[$m]['contactName']} {$multiContactValue[$m]['contactLName']} - {$multiContactValue[$m]['companyName']}</option>
EOT;
                            }
                        }
                    }
            }
            $formHtml .= <<<EOT
         </select></div>
			<div class="form-group hide notifyUsers px-10">
				<label class="font-weight-bolder">To (EMAIL ADDRESSES ONLY):</label>
						   						<textarea class="form-control" name="customRecipientEmail" id="customRecipientEmail" cols="40" rows="2"></textarea>
<small class="text-muted">(semicolon) to seperate email addresses, only public notes will be sent.</small>
						   </div>
EOT;
        }

        $formHtml .= <<<EOT
			<div class="d-none">
					<div class="pad10 left" style="padding-left:60px"><input class="button"  type="submit" name="submit" id="submit" value="Save" alt="Start Search"></div><div class="pad10 left">$cancelLink</div>
			</div>
</form>
EOT;
        return $formHtml;
    }
}
