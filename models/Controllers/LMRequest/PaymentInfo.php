<?php

namespace models\Controllers\LMRequest;

use models\composite\oFileDoc\HtmlPdfLMRFileDoc;
use models\composite\oFileUpdate2\saveACHInfo;
use models\composite\oFileUpdate2\saveLMRCCInfo;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glLoanServicer;
use models\Controllers\backoffice\LMRequest;
use models\cypher;
use models\lendingwise\tblACHInfo;
use models\lendingwise\tblFileWebFormEsignDetails;
use models\lendingwise\tblRemoteUrl;
use models\Request;
use models\Resque\AutomatedHTMLPdfTask;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class PaymentInfo extends strongType
{
    public static ?int $allowToEdit = null;
    public static ?int $LMRId = null;
    public static ?string $isPaymentWebform = null;
    public static ?string $depositDate = null;
    public static ?string $ACTRoutingNumber = null;
    public static ?string $ACTNumber = null;
    public static ?string $creditCardNumber = null;
    public static ?string $cardNumberOnBack = null;
    public static ?string $paymentWebform = null;
    public static ?string $acci = null;
    public static ?array $ACTTypeArray = null;
    public static ?array $stateArray = null;
    public static ?array $secArr = null;
    public static ?array $WebFormEsignInfo = null;
    public static ?string $t = null;
    public static ?string $FileCreatedDate = null;
    public static ?bool $isPDFFinished = null;
    public static ?string $jobId = null;
    public static ?bool $checkPdfDocStatus = false;
    public static ?string $folderName = null;
    public static ?string $uploadDocUrl = null;
    public static ?int $clientId = null;
    public static ?int $isSysNotesPrivate = null;
    public static ?int $LMRResponseId = null;
    public static ?int $PCID = null;
    public static ?string $borrowerName = null;
    public static ?string $formType = null;

    public static function init($LMRId)
    {
        self::$WebFormEsignInfo = tblFileWebFormEsignDetails::getData($LMRId, 'CashFlow');
        if (self::$t && HTML_PDFCROWD) {
            self::getPDF();
        }
    }

    public static function Post()
    {
        $inputArray = [];
        self::$allowToEdit = Request::GetClean('allowToEdit') ?? null;
        self::$isPaymentWebform = Request::GetClean('isPaymentWebform') ?? null;
        self::$paymentWebform = Request::GetClean('paymentWebform') ?? null;
        $paymentMethod = Request::GetClean('paymentMethod') ?? null;
        self::$FileCreatedDate = cypher::myDecryption(Request::GetClean('recordDate')) ?: null;
        self::$PCID = cypher::myDecryption(Request::GetClean('encPCID')) ?: null;
        self::$clientId = cypher::myDecryption(Request::GetClean('clientId')) ?: null;
        self::$borrowerName = cypher::myDecryption(Request::GetClean('borrowerName')) ?: null;
        self::$isSysNotesPrivate = cypher::myDecryption(Request::GetClean('isSysNotesPrivate')) ?: null;
        self::$LMRResponseId = cypher::myDecryption(Request::GetClean('LMRResponseId')) ?: null;
        self::$LMRId = cypher::myDecryption(Request::GetClean('LMRId')) ?? null;
        $result = 0;
        $tblRemoteUrl = tblRemoteUrl::Get(['RUID' => Request::GetClean('RUID')]) ?? new tblRemoteUrl();
        $tblRemoteUrl->paymentMethod = $paymentMethod;
        $tblRemoteUrl->save();
        if (self::$paymentWebform == 'check' || $paymentMethod == 'check') {
            $inputArray['bankName'] = Request::GetClean('ACTBankName') ?? null;
            $inputArray['accountName'] = Request::GetClean('ACTHolderName') ?? null;
            $inputArray['acctHolderAddr'] = Request::GetClean('ACTHolderAddr') ?? null;
            $inputArray['acctHolderCity'] = Request::GetClean('ACTHolderCity') ?? null;
            $inputArray['acctHolderState'] = Request::GetClean('ACTHolderState') ?? null;
            $inputArray['acctHolderZip'] = Request::GetClean('ACTHolderZip') ?? null;
            $inputArray['depositDate'] = Request::GetClean('depositDate') ?? null;
            $inputArray['routingNo'] = Request::GetClean('ACTRoutingNumber') ?? null;
            $inputArray['accountNo'] = Request::GetClean('ACTNumber') ?? null;

            $inputArray['LMRId'] = self::$LMRId;
            $inputArray['ACID'] = Request::GetClean('ACID') ?? null;
            $inputArray['accType'] = Request::GetClean('ACTType') ?? null;

            $inputArray['ACTLoanServicer'] = Request::GetClean('ACTLoanServicer') ?? null;
            $inputArray['ACTServicerException'] = Request::isset('ACTServicerException') ? Request::GetClean('ACTServicerException') : null;
            $inputArray['ACTAccountHolderType'] = Request::GetClean('ACTAccountHolderType') ?? null;
            $inputArray['ACTBankAccountType'] = Request::GetClean('ACTBankAccountType') ?? null;
            $result = saveACHInfo::getReport($inputArray);
        } elseif (self::$paymentWebform == 'credit' || $paymentMethod == 'credit') {
            $inputArray['CCType'] = Request::GetClean('creditCardType') ?? null;
            $inputArray['CCExpiryMonth'] = Request::GetClean('expirationMonth') ?? null;
            $inputArray['CCExpiryYear'] = Request::GetClean('expirationYear') ?? null;
            $inputArray['CCName'] = Request::GetClean('cardHolderName') ?? null;
            $inputArray['CCAddress'] = Request::GetClean('CCAddress') ?? null;
            $inputArray['CCCity'] = Request::GetClean('CCCity') ?? null;
            $inputArray['CCState'] = Request::GetClean('CCState') ?? null;
            $inputArray['CCZip'] = Request::GetClean('CCZip') ?? null;
            $inputArray['CCNumber'] = Request::GetClean('creditCardNumber') ?? null;
            $inputArray['CCSecCode'] = Request::GetClean('cardNumberOnBack') ?? null;

            $inputArray['LMRID'] = self::$LMRId;
            $inputArray['LCID'] = Request::GetClean('LCID') ?? null;
            $rs = saveLMRCCInfo::getReport($inputArray);
            $result = $rs['LCID'];
        }

        signatureForm::$LMRId = self::$LMRId;
        if (self::$paymentWebform == 'credit') {
            signatureForm::$formType = 'CreditPaymentInfoWebForm';
            self::$formType = 'CreditPaymentInfoWebForm';
        } elseif (self::$paymentWebform == 'check') {
            signatureForm::$formType = 'CheckPaymentInfoWebForm';
            self::$formType = 'CheckPaymentInfoWebForm';
        } elseif (self::$paymentWebform == 'both') {
            signatureForm::$formType = 'CreditCheckPaymentInfoWebForm';
            self::$formType = 'CreditCheckPaymentInfoWebForm';
        }
        signatureForm::signatureSave();
        if (HTML_PDFCROWD && $_POST['signCode']) {
            self::generatePDF();
        }
        Strings::SetSess('msg', 'Your submission was successful');
    }

    public static function generatePDF()
    {
        $uniqueId = Dates::Timestamp(null, null, 'YmdHis');
        $docName = signatureForm::$formType.'_' . self::$borrowerName . '_' . date('M/d/Y') . '_' . date('H:i') . '.pdf';
        self::$t = AutomatedHTMLPdfTask::createTask([
            'LMRId' => self::$LMRId,
            'PCID' => self::$PCID,
            'fileCreatedDate' => self::$FileCreatedDate,
            'oldFPCID' => self::$PCID,
            'userGroup' => 'Client',
            'userNumber' => self::$clientId,
            'borrowerLName' => self::$borrowerName,
            'userName' => 'Client',
            'uploadedBy' => self::$clientId,
            'docName' => cypher::myEncryption($docName),
            'docCategory' => 'WebForm PDF',
            'isSysNotesPrivate' => self::$isSysNotesPrivate,
            'recordDate' => self::$FileCreatedDate,
            'LMRResponseId' => self::$LMRResponseId,
            'webFormURL' => $_SERVER['HTTP_REFERER'],
            'ipAddress' => $_SERVER['REMOTE_ADDR'],
            'uniqueJobId' => $uniqueId,
        ]);
    }

    public static function getPDF()
    {
        self::$jobId = self::$t;

        $job = AutomatedHTMLPdfTask::getTask(self::$t);

        if ($job) {
            self::$checkPdfDocStatus = true;
            self::$isPDFFinished = (bool)$job->completed_at;
            $ResqueData = json_decode($job->params, true);
            $uniqueJobId = $ResqueData['uniqueJobId'];
            $htmlPDFDocInfo = HtmlPdfLMRFileDoc::getReport(['uniqueJobId' => $uniqueJobId]);
            if (sizeof($htmlPDFDocInfo) > 0) {
                HtmlPdfLMRFileDoc::update([
                    'uniqueJobId' => $uniqueJobId,
                    'tokenId' => self::$jobId,
                ]);
            }
        } else {
            self::$checkPdfDocStatus = false;
            $htmlPDFDocInfo = HtmlPdfLMRFileDoc::getReport(['tokenId' => self::$jobId]);
        }
        self::$folderName = self::$PCID . '/'
            . date('Y', strtotime(self::$FileCreatedDate)) . '/'
            . date('m', strtotime(self::$FileCreatedDate)) . '/'
            . date('d', strtotime(self::$FileCreatedDate));

        self::$folderName .= '/' . self::$LMRId . '/upload/';

        self::$uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn='
            . cypher::myEncryption($htmlPDFDocInfo['docName'])
            . '&fd=' . cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . self::$folderName)
            . '&opt=enc&dn=' . cypher::myEncryption(str_replace(' ', '_', $htmlPDFDocInfo['docName']));

    }

    public static function getLoanServicerOptions(): array
    {
        if(glCustomJobForProcessingCompany::isPC_CV3(LMRequest::File()->FPCID)){
            return glLoanServicer::$loanServicer_CV3;
        }

        return glLoanServicer::$loanServicer;
    }

    public static function setLoanServicer(): string
    {
        $LoanServicer = '';
        $servicerException = null;
        if (!empty(self::$LMRId)) {
            $servicerException = LMRequest::myFileInfo()->tblFile()->getTblACHInfo_by_LMRID()->ACTServicerException;
        }
        $propertyState = LMRequest::myFileInfo()->getPrimaryProperty()->propertyState;
        //CU-868c81wme
        if (glCustomJobForProcessingCompany::isPC_CV3(LMRequest::File()->FPCID)
            && in_array($propertyState, ['NC', 'SC'])) {
            return 'ServEase';
        }
        if (in_array($propertyState, glLoanServicer::$loanServicerStates) && !$servicerException) {
            $LoanServicer = 'FCI';
        } elseif (!$servicerException) {
            $LoanServicer = 'ServEase';
        }
        return $LoanServicer;
    }

    public static function setLoanServicerValue($ACTLoanServicer)
    {
        $tblACHInfo = tblACHInfo::Get(['LMRID' => self::$LMRId]);
        if (!$tblACHInfo) {
            $tblACHInfo = new tblACHInfo();
            $tblACHInfo->LMRID = self::$LMRId;
            $tblACHInfo->recordDate = Dates::Timestamp();
        }
        $tblACHInfo->ACTLoanServicer = $ACTLoanServicer;
        $tblACHInfo->Save();
    }
}