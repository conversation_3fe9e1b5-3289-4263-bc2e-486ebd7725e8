<?php

namespace models\Controllers\backoffice;

use models\constants\gl\glPaymentBasedArray;
use models\constants\gl\glUserRole;
use models\Database2;
use models\composite\oPC\PCInfo;
use models\composite\oUser\getDefaultTabOnPipeline;
use models\composite\oUser\getDefaultTabOnPipelineUser;
use models\composite\oUser\getUserPreferences;
use models\composite\oUserAccess\getAutomationControlAccess;
use models\constants\accessSecondaryWFPC;
use models\constants\agentPCException;
use models\constants\CFPBAuditStatusArray;
use models\constants\gl\glNotesTypeArray;
use models\constants\gl\glSalesRepresentativeArray;
use models\constants\gl\glServicingSubStatus;
use models\constants\gl\glStaleFileArray;
use models\constants\loanAuditStatusArray;
use models\constants\PCException;
use models\constants\priorityLevelArray;
use models\Controllers\Base\Base;
use models\lendingwise\tblSavedView;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\UserAccess;

/**
 *
 */
class myPipeline extends Base
{
    public static ?array $tabCount = [];
    public static ?array $tabTip = [];
    private static ?string $loggedInUserPCID = null;
    public static ?string $userGroup = null;
    public static ?string $userNumber = null;
    private static ?string $externalBroker = null;
    private static ?string $userRole = null;
    private static ?array $executiveId = null;
    private static ?array $brokerNumb = null;


    public static ?string $PCID = null;
    public static ?array $defaultTabOnPipelineUserArray = null;
    public static ?string $reportId = null;
    public static ?string $defaultTabOnPipeline = null;
    public static ?array $defaultTabOnPipelineArray = null;
    public static ?array $AllPCInfo = null;
    public static ?array $exResultArray = null;

    public static ?array $PCStatusInfoForTab = null;

    public static ?array $searchModuleCodeArray = null;
    public static ?array $PCInternalServicesModule = null;
    public static ?array $userSearchFieldsPreference = null;

    public static ?array $glSalesRepresentativeArray = null;
    public static ?array $agentPCException = null;
    public static ?array $PCException = null;
    public static ?array $glStaleFileArray = null;
    public static ?array $glPaymentBasedArray = null;
    public static ?array $glNotesTypeArray = null;
    public static ?array $loanAuditStatusArray = null;
    public static ?array $CFPBAuditStatusArray = null;
    public static ?array $priorityLevelArray = null;
    public static ?array $accessSecondaryWFPC = null;
    public static ?array $searchFields = [];
    public static ?string $multipleModuleCode = null;
    public static array $searchFilter = [];

    public static ?int $userAutomationControlAccess = 0;
    public static ?array $FileWorkFlowStepsArray = [];

    /**
     * @param $loggedInUserPCID
     * @param $userGroup
     * @param $userNumber
     * @param $PCID
     * @param $userRole
     * @param $executiveId
     * @param $brokerNumb
     * @param $reportId
     * @param $externalBroker
     * @return void
     */
    public static function setVars(
        $loggedInUserPCID,
        $userGroup,
        $userNumber,
        $PCID,
        $userRole,
        $executiveId,
        $brokerNumb,
        $reportId,
        $externalBroker
    )
    {
        self::$loggedInUserPCID = $loggedInUserPCID ?: $PCID;
        self::$userGroup = $userGroup;
        self::$userNumber = $userNumber;
        self::$PCID = $PCID;
        self::$userRole = $userRole;
        self::$executiveId = Arrays::explodeIntVals($executiveId);
        self::$brokerNumb = Arrays::explodeIntVals($brokerNumb);
        self::$reportId = $reportId;
        self::$externalBroker = $externalBroker;
    }

    /**
     * @return void
     */
    public static function Init()
    {
        if (!self::$PCID) {
            return; // do not run the report for no PCID
        }
        self::$defaultTabOnPipelineArray = [];
        self::$defaultTabOnPipelineUserArray = [];
        self::$defaultTabOnPipeline = 0;
        self::$PCStatusInfoForTab = [];
        self::$userAutomationControlAccess = 0;

        self::$defaultTabOnPipelineUserArray = getDefaultTabOnPipelineUser::getReport([
            'PCID'       => self::$loggedInUserPCID,
            'userGroup'  => self::$userGroup,
            'userNumber' => self::$userNumber
        ]);

        if (count(self::$defaultTabOnPipelineUserArray) > 0) {
            self::$defaultTabOnPipeline = self::$defaultTabOnPipelineUserArray[0]['PSID'];
            self::$reportId = self::$defaultTabOnPipeline;
        } else {
            self::$defaultTabOnPipelineArray = getDefaultTabOnPipeline::getReport(self::$loggedInUserPCID);
            if (count(self::$defaultTabOnPipelineArray) > 0) {
                self::$defaultTabOnPipeline = self::$defaultTabOnPipelineArray[0]['PSID'];
                self::$reportId = self::$defaultTabOnPipeline;
            }
        }

        self::$AllPCInfo = [];
        if (self::$PCID > 0) {
            self::$AllPCInfo = PCInfo::getReport(['PCID' => self::$PCID]);
        }

        self::$exResultArray = null;

        if (self::$userRole == glUserRole::SUPER && self::$PCID == 0) {
            if (sizeof(self::$executiveId) == 1) {
                $exInArray['TABLENAME'] = 'tblBranch';
                $exInArray['FIELDNAMEARRAY'] = ['processingCompanyId'];
                $exInArray['CONDITION'] = ' where activeStatus = 1 and executiveId in (' . implode(',', self::$executiveId) . ')';
                self::$exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);
                if (count(self::$exResultArray) > 0) {
                    self::$PCID = trim(self::$exResultArray['processingCompanyId']);
                }
            } elseif (sizeof(self::$brokerNumb) == 1 && self::$PCID == 0) {
                $exInArray['TABLENAME'] = 'tblAgentPC';
                $exInArray['FIELDNAMEARRAY'] = ['PCID'];
                $exInArray['CONDITION'] = ' where activeStatus = 1 and AID in (' . implode(',', self::$brokerNumb) . ')';
                self::$exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);
                if (count(self::$exResultArray) > 0) {
                    self::$PCID = trim(self::$exResultArray['PCID']);
                }
            }
        }

        $searchModuleCodeArray = [];
        $multipleModuleCode = '';
        if (isset($_POST['multipleModuleCode'])) {
            if (is_array($_POST['multipleModuleCode'])) {
                $searchModuleCodeArray = $_POST['multipleModuleCode'];
                $multipleModuleCode = implode(',', $searchModuleCodeArray);
            } else {
                $multipleModuleCode = trim($_POST['multipleModuleCode']);
            }
        } elseif (isset($_REQUEST['multipleModuleCode'])) {
            $multipleModuleCode = trim($_REQUEST['multipleModuleCode']);
            if (trim($multipleModuleCode) != '') {
                $searchModuleCodeArray = preg_split('/[, ]+/', $multipleModuleCode);
            }
        }

        $sql = 'CALL SP_GetPCInternalServiceType_new (
                :PCID,
                :moduleCode,
                :opt
            ); ';
        $params = [
            'PCID'       => self::$PCID,
            'moduleCode' => $multipleModuleCode,
            'opt'        => '',
        ];
        $PCInternalServices = Database2::getInstance()->queryData($sql, $params);
        $PCInternalServicesModule = Arrays::buildKeyByValue($PCInternalServices, 'moduleName');

        if (isset($_REQUEST['searchFilter'])) {
            $userSearchFieldsPreference = $_REQUEST['searchFilter'];
        } else {
            $userSearchFieldsPreferenceArrays = getUserPreferences::getSearchFields([
                'UID'   => self::$userNumber,
                'UType' => self::$userGroup,
            ]);
            $userSearchFieldsPreference = array_keys($userSearchFieldsPreferenceArrays);
        }

        self::$searchModuleCodeArray = $searchModuleCodeArray;
        self::$PCInternalServicesModule = $PCInternalServicesModule;
        self::$userSearchFieldsPreference = $userSearchFieldsPreference;

        self::$glSalesRepresentativeArray = glSalesRepresentativeArray::$glSalesRepresentativeArray;
        self::$agentPCException = agentPCException::$agentPCException;
        self::$PCException = PCException::$PCException;
        self::$glStaleFileArray = glStaleFileArray::$glStaleFileArray;
        self::$glPaymentBasedArray = glPaymentBasedArray::$glPaymentBasedArray;
        self::$glNotesTypeArray = glNotesTypeArray::$glNotesTypeArray;
        self::$loanAuditStatusArray = loanAuditStatusArray::$loanAuditStatusArray;
        self::$CFPBAuditStatusArray = CFPBAuditStatusArray::$CFPBAuditStatusArray;
        self::$priorityLevelArray = priorityLevelArray::$priorityLevelArray;
        self::$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
        self::$multipleModuleCode = $multipleModuleCode;
        self::$userAutomationControlAccess = getAutomationControlAccess::getReport(self::$userNumber);
    }

    /* @var tblSavedView[] $_savedViews */
    private static ?array $_savedViews = null;

    /**
     * @return tblSavedView[]
     */
    public static function getSavedViews(): ?array
    {
        if (is_null(self::$_savedViews)) {
            self::$_savedViews = tblSavedView::getForUserFormID(
                self::$userNumber,
                self::$userGroup,
                'LMRReport'
            );
        }
        return self::$_savedViews;
    }

    public static function initSearchFilter(
        $userSeeBilling,
        $fileType,
        $allowCFPBAuditing,
        $PCModuleInfo,
        $workflowInfo
    )
    {
        self::$searchFields = [
            'ent.entityName'            => 'Entity Name',
            'tl.borrowerName'           => 'Borrower First Name',
            'tl.borrowerLName'          => 'Borrower Last Name',
            'tl.borrowerEmail'          => 'Borrower Email',
            'tl.borrowerSecondaryEmail' => 'Borrower Secondary Email',
            'tl.ssnNumber'              => 'Borrower SSN',
            'tl.coBorrowerFName'        => 'Co-borrower First Name',
            'tl.coBorrowerLName'        => 'Co-borrower Last Name',
            'tl.coBorrowerEmail'        => 'Co-borrower Email',
            'tl.coBSsnNumber'           => 'Co-borrower SSN',
            'tl.loanNumber'             => 'Loan Number',
            'tl.loanNumber2'            => 'Loan Number 2 lien',
            'tp.propertyAddress'        => 'Property Address',
            'tp.propertyCity'           => 'Property City',
            'tp.propertyZipCode'        => 'Property Zip',
            'tlr.fileNumber'            => 'File Number',
            'tl.phoneNumber'            => 'Borrower Phone',
            'tl.coBPhoneNumber'         => 'Co-borrower Phone',
            'tl.cellNumber'             => 'Borrower Cell',
            'tl.workNumber'             => 'Borrower Work Number',
//    'tl.coBorrowerWorkNumber' => 'Co-borrower Work Number',
            'tl.coBCellNumber'          => 'Co-borrower Cell',
            'tl.altPhoneNumber'         => 'Borrower ALT Phone Number',
            'tl.coBAltPhoneNumber'      => 'Co-Borrower ALT Phone Number',
            'tl.LMRId'                  => 'File ID'
        ];
        if ($userSeeBilling) {
            self::$searchFields['tcc.CCNumber'] = 'Credit Card Number';
            self::$searchFields['tca.accountNo'] = 'Checking ACT Number';
        }


        if (self::$userRole == 'REST' || $fileType == 2 || $fileType == 4) {
            self::$searchFields['thr.fileID'] = 'Report ID';
        }


        $searchFilter = [];

        $searchFilter['General']['searchField_Filter']['text'] = 'searchField';
        $searchFilter['General']['searchField_Filter']['status'] = 'checked';
        $searchFilter['General']['searchField_Filter']['class'] = 'hide';

        if ($fileType != 2 && $fileType != 4 && self::$userRole != 'REST') {
            if (self::$userRole == 'Super'
                || self::$userRole == 'Auditor'
                || self::$userRole == 'CFPB Auditor'
                || self::$userRole == 'Auditor Manager'
                || ($fileType == 'CFPB' && $allowCFPBAuditing == 1)
            ) {
                $searchFilter['General']['listAllPC_Filter']['text'] = 'List all PCs';
                $searchFilter['General']['listAllPC_Filter']['status'] = UserAccess::userSearchFieldStatus('listAllPC_Filter', myPipeline::$userSearchFieldsPreference);
            }
            if (self::$userRole == 'Branch') {
                if (in_array(self::$PCID, myPipeline::$agentPCException)) {
                    $searchFilter['General']['listAllBranch_Filter']['text'] = 'List all Branches';
                    $searchFilter['General']['listAllBranch_Filter']['status'] = UserAccess::userSearchFieldStatus('listAllBranch_Filter', myPipeline::$userSearchFieldsPreference);
                }
            } else {
                $searchFilter['General']['listAllBranch_Filter']['text'] = 'List all Branches';
                $searchFilter['General']['listAllBranch_Filter']['status'] = UserAccess::userSearchFieldStatus('listAllBranch_Filter', myPipeline::$userSearchFieldsPreference);
            }

            if (self::$userGroup == 'Super'
                || self::$userGroup == 'Branch'
                || self::$userGroup == 'Employee'
                || (self::$userGroup == 'Agent' && (in_array(self::$PCID, myPipeline::$PCException)
                        || in_array(self::$PCID, myPipeline::$agentPCException)
                        || self::$externalBroker > 0
                    ))
            ) {
                /** Only First American mitigators Agents to see all files under Lead status  **/
                $searchFilter['General']['listAllBroker_Filter']['text'] = 'List all Brokers';
                $searchFilter['General']['listAllBroker_Filter']['status'] = UserAccess::userSearchFieldStatus('listAllBroker_Filter', myPipeline::$userSearchFieldsPreference);

                if (!self::$externalBroker) {
                    $searchFilter['General']['listAllLoanOfficer_Filter']['text'] = 'List all Loan Officers';
                    $searchFilter['General']['listAllLoanOfficer_Filter']['status'] = UserAccess::userSearchFieldStatus('listAllLoanOfficer_Filter', myPipeline::$userSearchFieldsPreference);
                }
            }

            if (self::$userRole != 'Branch' && self::$userRole != 'Agent') {
                if (!(self::$PCID == 1545 && self::$userNumber == 7910)) { /*  hide the advanced search >> employee filter for PC = Synergy Attorney Services, LLC (M. Pillar Gracia) on May 17, 2016  */
                    $searchFilter['General']['listAllEmployees_Filter']['text'] = 'List all Employees';
                    $searchFilter['General']['listAllEmployees_Filter']['status'] = UserAccess::userSearchFieldStatus('listAllEmployees_Filter', myPipeline::$userSearchFieldsPreference);
                }
            }

            $searchFilter['General']['clientBilling_Filter']['text'] = 'Client Billing';
            $searchFilter['General']['clientBilling_Filter']['status'] = UserAccess::userSearchFieldStatus('clientBilling_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['fileIdle_Filter']['text'] = 'File Idle';
            $searchFilter['General']['fileIdle_Filter']['status'] = UserAccess::userSearchFieldStatus('fileIdle_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['notesType_Filter']['text'] = 'Notes Type';
            $searchFilter['General']['notesType_Filter']['status'] = UserAccess::userSearchFieldStatus('notesType_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['priorityLevel_Filter']['text'] = 'Priority';
            $searchFilter['General']['priorityLevel_Filter']['status'] = UserAccess::userSearchFieldStatus('priorityLevel_Filter', myPipeline::$userSearchFieldsPreference);

            if (self::$userRole == 'Super'
                || self::$userRole == 'Branch'
                || self::$userRole == 'Agent'
                || self::$userRole == 'Manager'
                || self::$userRole == 'Auditor'
                || self::$userRole == 'CFPB Auditor'
                || self::$userRole == 'Auditor Manager'
            ) {
                $searchFilter['General']['activeDeactiveFile_Filter']['text'] = 'Active / Inactive Files';
                $searchFilter['General']['activeDeactiveFile_Filter']['status'] = UserAccess::userSearchFieldStatus('activeDeactiveFile_Filter', myPipeline::$userSearchFieldsPreference);
            }

            $searchFilter['General']['noActiveDeactive_Filter']['text'] = 'Num Of Records Per Page';
            $searchFilter['General']['noActiveDeactive_Filter']['status'] = UserAccess::userSearchFieldStatus('noActiveDeactive_Filter', myPipeline::$userSearchFieldsPreference);

            if (self::$userGroup == 'Auditor'
                || self::$userGroup == 'CFPB Auditor'
                || $fileType == 'CFPB'
                || self::$userGroup == 'Auditor Manager'
            ) {
                $searchFilter['General']['loanAuditStatus_Filter']['text'] = 'Loan Audit Status';
                $searchFilter['General']['loanAuditStatus_Filter']['status'] = UserAccess::userSearchFieldStatus('loanAuditStatus_Filter', myPipeline::$userSearchFieldsPreference);
            }

            $searchFilter['General']['leadSource_Filter']['text'] = 'List all Lead Source';
            $searchFilter['General']['leadSource_Filter']['status'] = UserAccess::userSearchFieldStatus('leadSource_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['referringParty_Filter']['text'] = 'Referring Party';
            $searchFilter['General']['referringParty_Filter']['status'] = UserAccess::userSearchFieldStatus('referringParty_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['workflow_Filter']['text'] = 'Select Workflows';
            $searchFilter['General']['workflow_Filter']['status'] = UserAccess::userSearchFieldStatus('workflow_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['workflowStep_Filter']['text'] = 'Select Completed Workflow Steps';
            $searchFilter['General']['workflowStep_Filter']['status'] = UserAccess::userSearchFieldStatus('workflowStep_Filter', myPipeline::$userSearchFieldsPreference);
            $workflowInfoById = Arrays::buildKeyByValue($workflowInfo, 'WFID');

            $searchFilter['General']['propertyState_Filter']['text'] = 'Property State';
            $searchFilter['General']['propertyState_Filter']['status'] = UserAccess::userSearchFieldStatus('propertyState_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['fileType_Filter']['text'] = 'Select File Types';
            $searchFilter['General']['fileType_Filter']['status'] = UserAccess::userSearchFieldStatus('fileType_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['LMRClientType_Filter']['text'] = 'Select Loan Programs';
            $searchFilter['General']['LMRClientType_Filter']['status'] = UserAccess::userSearchFieldStatus('LMRClientType_Filter', myPipeline::$userSearchFieldsPreference);

            if (self::$userGroup == 'Employee'
                || self::$userGroup == 'Super'
                || (self::$userGroup == 'Agent' && PageVariables::$allowToAccessInternalLoanProgram && PageVariables::$externalBroker)
            ) {
                $searchFilter['General']['LMRInternalClientType_Filter']['text'] = 'Select Internal Loan Programs';
                $searchFilter['General']['LMRInternalClientType_Filter']['status'] = UserAccess::userSearchFieldStatus('LMRInternalClientType_Filter', myPipeline::$userSearchFieldsPreference);
            }

            $searchFilter['General']['clientSubstatus_Filter']['text'] = 'Client File Substatus';
            $searchFilter['General']['clientSubstatus_Filter']['status'] = UserAccess::userSearchFieldStatus('clientSubstatus_Filter', myPipeline::$userSearchFieldsPreference);

            if (self::$userGroup == 'Super'
                || (self::$userGroup != 'Auditor'
                    && self::$userGroup != 'CFPB Auditor'
                    && $fileType != 'CFPB' &&
                    self::$userGroup != 'Auditor Manager')
            ) {
                $searchFilter['General']['multiplePrimaryStatus_Filter']['text'] = 'Primary Status';
                $searchFilter['General']['multiplePrimaryStatus_Filter']['status'] = UserAccess::userSearchFieldStatus('multiplePrimaryStatus_Filter', myPipeline::$userSearchFieldsPreference);
            }
            $PCModuleInfoKeys = array_keys($PCModuleInfo);
            if (!in_array('HMLO', $PCModuleInfoKeys)) {

                $searchFilter['General']['lenderName_Filter']['text'] = 'Lender name';
                $searchFilter['General']['lenderName_Filter']['status'] = UserAccess::userSearchFieldStatus('lenderName_Filter', myPipeline::$userSearchFieldsPreference);
            }
            if (in_array(self::$PCID, myPipeline::$accessSecondaryWFPC ?? [])) {
                $searchFilter['General']['WFStepID_Filter']['text'] = 'Current Secondary Status';
                $searchFilter['General']['WFStepID_Filter']['status'] = UserAccess::userSearchFieldStatus('WFStepID_Filter', myPipeline::$userSearchFieldsPreference);
            }

            if (in_array('HMLO', $PCModuleInfoKeys) || in_array('loc', $PCModuleInfoKeys)) {
                $glServicingSubStatus = glServicingSubStatus::$glServicingSubStatus;
                $searchFilter['General']['servicingStatus_Filter']['text'] = 'Servicing Status';
                $searchFilter['General']['servicingStatus_Filter']['status'] = UserAccess::userSearchFieldStatus('servicingStatus_Filter', myPipeline::$userSearchFieldsPreference);
            }

            $searchFilter['General']['paymentBased_Filter']['text'] = 'Payment Based';
            $searchFilter['General']['paymentBased_Filter']['status'] = UserAccess::userSearchFieldStatus('paymentBased_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['General']['contacts_Filter']['text'] = 'List All Contacts';
            $searchFilter['General']['contacts_Filter']['status'] = UserAccess::userSearchFieldStatus('contacts_Filter', myPipeline::$userSearchFieldsPreference);


            $searchFilter['Dates']['fileCreatedDate_Filter']['text'] = 'File Created';
            $searchFilter['Dates']['fileCreatedDate_Filter']['status'] = UserAccess::userSearchFieldStatus('fileCreatedDate_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['Dates']['fileModifiedDate_Filter']['text'] = 'File Modified';
            $searchFilter['Dates']['fileModifiedDate_Filter']['status'] = UserAccess::userSearchFieldStatus('fileModifiedDate_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['Dates']['closingDate_Filter']['text'] = 'Closing Date';
            $searchFilter['Dates']['closingDate_Filter']['status'] = UserAccess::userSearchFieldStatus('closingDate_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['Dates']['maturityDate_Filter']['text'] = 'Maturity Date';
            $searchFilter['Dates']['maturityDate_Filter']['status'] = UserAccess::userSearchFieldStatus('maturityDate_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['Dates']['appraisalOrderDate_Filter']['text'] = 'Appraisal Order Date';
            $searchFilter['Dates']['appraisalOrderDate_Filter']['status'] = UserAccess::userSearchFieldStatus('appraisalOrderDate_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['Dates']['receivedDate_Filter']['text'] = 'Received Date';
            $searchFilter['Dates']['receivedDate_Filter']['status'] = UserAccess::userSearchFieldStatus('receivedDate_Filter', myPipeline::$userSearchFieldsPreference);

            $searchFilter['Dates']['disclosureSentDate_Filter']['text'] = 'Disclosure Sent Date';
            $searchFilter['Dates']['disclosureSentDate_Filter']['status'] = UserAccess::userSearchFieldStatus('disclosureSentDate_Filter', myPipeline::$userSearchFieldsPreference);

            if ($fileType == 'LA') {
                $searchFilter['Dates']['paymentStatus_Filter']['text'] = 'Closing Date';
                $searchFilter['Dates']['paymentStatus_Filter']['status'] = UserAccess::userSearchFieldStatus('paymentStatus_Filter', myPipeline::$userSearchFieldsPreference);
            }
        }
        self::$searchFilter = $searchFilter;
    }
}
