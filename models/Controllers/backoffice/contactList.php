<?php

namespace models\Controllers\backoffice;

use models\composite\oContacts\getContactList;
use models\types\strongType;

class contactList extends strongType
{
    public static ?string $sortOpt = null;
    public static ?string $orderBy = null;

    public static ?array $roleContactTypeArray = null;
    public static ?string $userRole = null;
    public static ?string $userGroup = null;
    public static ?string $role = null;
    public static ?int $allow = null;
    public static ?int $isHMLO = null;
    public static ?int $allowDashboard = null;
    public static ?int $LMRId = null;
    public static ?int $allowToEditProfile = null;
    public static ?string $pcName = null;
    public static ?int $procCompanyId = null;
    public static ?int $fileCnt = null;

    /* @var getContactList[] $contactInfoArray */

    public static ?array $contactInfoArray = null;
    public static ?string $searchTerm = null;
    public static ?int $activeEmp = null;
    public static ?array $assignedFileCnt = null;
    public static ?array $assignedDeactivatedFileCnt = null;
    public static ?array $contactTypeArray = null;
    public static ?string $borrowerName = null;
    public static ?string $contactRole = null;
    public static ?string $allowToViewContactsList = null;

    public static ?int $pageNumber = null;

    public static ?string $but_sumbit = null;

    public static ?int $noOfRecordsPerPage = 20;

    public static ?string $generateCSV = null;

    public static ?int $noOfRecords = null;

}
