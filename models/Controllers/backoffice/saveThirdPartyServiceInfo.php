<?php

namespace models\Controllers\backoffice;

use models\types\strongType;
use models\cypher;
use models\standard\Arrays;
use models\standard\Strings;
use models\constants\gl\glCRAServices;
use models\thirdPartyServicesResponse;
use models\constants\gl\glThirdPartyServicesCRA;
use models\composite\oFile\getLoanFileAndCompanyInformation;
use models\composite\oThirdPartyServices\generate_third_party_docs;

class saveThirdPartyServiceInfo extends strongType
{
    public static function getReport(
        int $LMRId,
        array $post,
        ?string $firstName = null,
        ?string $lastName = null,
        ?string $userGroup = null,
        ?string $userRole = null,
        ?int $userNumber = null
    ): thirdPartyServicesResponse
    {
        $result = getLoanFileAndCompanyInformation::getReportObject($LMRId);
        $service = $post['thirdPartyServices'];
        $post['service'] = $service;

        if ($service === glCRAServices::SOCIAL_SECURITY) {
            $file = $post['files']['ssa89'] ?? [];
            if ($file['error'] === UPLOAD_ERR_OK) {
                $fileContent = file_get_contents($file['tmp_name']);
                $post['ssa89'] = base64_encode($fileContent);
                unset($post['files']['ssa89']);
            }
        }

        /* Generate loan identifier/document name */
        $post['loanIdentifier'] = glThirdPartyServicesCRA::init()[$post['cra']]->Services[$service]['Name'];
        /* AVM submodules added. */
        if (Arrays::getArrayValue('valuationModel', $post) != '') {
            $post['loanIdentifier'] .= $post['loanIdentifier'] . '-' . Arrays::getArrayValue('valuationModel', $post);
        }

        if ($service === glCRAServices::BUSINESS_CREDIT_REPORT || ($service === glCRAServices::FLOOD && $post['borrowerType'] !== 'Individual')) {
            $post['loanIdentifier'] .= '_' . Strings::cleanString($post['entityName']);
        } else {
            $post['loanIdentifier'] .= '_' . Strings::cleanString($result->borrowerName . $result->borrowerLName);
        }

        if (strpos($service, 'coborrower') !== false || strpos($service, 'joint') !== false) {
            $post['loanIdentifier'] .= '_' . Strings::cleanString($result->coBorrowerFName . $result->coBorrowerLName);
        }

        if ($post['loanNumber']) {
            $post['loanIdentifier'] = $post['loanIdentifier'] . '_' . $post['loanNumber'];
        }

        $cnt = strlen(Strings::getNumberValue($_REQUEST['creditCardNumber']));
        if ($cnt < 14) {
            $post['creditCardNumber'] = cypher::myDecryption($post['creditCardNumber']);
        }
        $num = Strings::getNumberValue($_REQUEST['cardNumberOnBack']);
        if ($num < 3) {
            $post['cardNumberOnBack'] = cypher::myDecryption($post['cardNumberOnBack']);
        }

        if($post['paymentThrough'] == 'One-Time Credit Card') {
            $post['creditCardNumber'] = $post['creditCardNumber_decrypt'];
            $post['cardNumberOnBack'] = $post['cardNumberOnBack_decrypt'];
        }

        $item = new generate_third_party_docs();

        foreach($post as $k => $v) {
            if(property_exists(generate_third_party_docs::class, $k)) {
                $item->safeSet($k, $v);
            }
        }

        $firstName = Strings::AlphaNumericOnly($firstName);
        $lastName = Strings::AlphaNumericOnly($lastName);
        $item->firstName = $firstName;
        $item->lastName = $lastName;

        $item->userGroup = $userGroup;
        $item->userNumber = $userNumber;
        $item->notificationEmail = $post['notificationEmail'] ?? null;
        $item->userRole = $userRole;

        return $item->Save();
    }
}