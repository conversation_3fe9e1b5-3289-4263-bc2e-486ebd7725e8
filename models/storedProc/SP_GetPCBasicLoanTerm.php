<?php

namespace models\storedProc;

use models\types\strongType;
use models\Database2;

class SP_GetPCBasicLoanTerm extends strongType
{
    public static function getReport(
        ?int $PCID = 0,
        ?int $BLID = 0
    )
    {
        $queryDataPCID = [];
        $result = [];

        if ($PCID > 0) {
            $queryDataPCID = [
                'basicInfo' => [
                    'table' => 'tblPCHMLOBasicLoanInfo',
                    'where' => ' activeStatus = 1 '
                ],
                'loanPgmInfo' => [
                    'table' => 'tblPCHMLOBasicLoanPgmInfo',
                    'orderBy' => ' PCID, BLID, loanPgm '
                ],
                'transactionTypeInfo' => [
                    'table' => 'tblPCHMLOBasicLoanTransactionType',
                    'orderBy' => ' PCID, BLID, transactionType '
                ],
                'loanPropertyTypeInfo' => [
                    'table' => 'tblPCHMLOBasicLoanPropertyType',
                    'orderBy' => ' PCID, BLID, propertyType '
                ],
                'loanFuturePropertyTypeInfo' => [
                    'table' => 'tblPCHMLOBasicLoanFuturePropertyType',
                    'orderBy' => ' PCID, BLID, futurePropertyType '
                ],
                'loanEntityTypeInfo' => [
                    'table' => 'tblPCHMLOBasicLoanEntityType',
                    'orderBy' => ' PCID, BLID, entityType '
                ],
                'marketPlaceLoanProgramInfo' => [
                    'select' => 'guidliensMarket.*, MPL.LoanProgramName',
                    'table' => 'tblPCHMLOBasicLoanMarketPlaceLoanProgram AS guidliensMarket LEFT JOIN tblMarketPlaceLoanPrograms AS MPL ON MPL.MPLID = guidliensMarket.MPLID',
                    'wherePCID' => ' AND guidliensMarket.PCID = :PCID ',
                    'whereBLID' => ' AND guidliensMarket.BLID = :BLID ',
                    'orderBy' => ' guidliensMarket.PCID, guidliensMarket.BLID, guidliensMarket.MPLID '
                ],
                'marketPlaceLoanProgranNotification' => [
                    'table' => 'tblPCHMLOBasicLoanMarketLoanNotification',
                    'orderBy' => ' NFID '
                ],
                'marketPlaceLoanProgranCustomNotification' => [
                    'select' => 'GROUP_CONCAT(NotificationUserEmailId SEPARATOR \';\') as customEmailIdsNotify, BLID ',
                    'table' => 'tblPCHMLOBasicLoanMarketLoanCustomNotification',
                    'orderBy' => ' NFCID '
                ],
                'extnOptionInfo' => [
                    'table' => 'tblPCHMLOBasicLoanExtensionOption',
                    'orderBy' => ' PCID, BLID, extnOption '
                ],
                'loanTermInfo' => [
                    'table' => 'tblPCHMLOBasicLoanTermInfo',
                    'orderBy' => ' PCID, BLID, loanTerm '
                ],
                'loanOccupancyInfo' => [
                    'table' => 'tblPCHMLOBasicLoanOccupancy',
                    'orderBy' => ' PCID, BLID, occupancy '
                ],
                'loanStateInfo' => [
                    'table' => 'tblPCHMLOBasicLoanStateInfo',
                    'orderBy' => ' PCID, BLID, stateCode '
                ],
                'loanNichesInfo' => [
                    'table' => 'tblPCHMLOBasicLoanNichesInfo',
                    'orderBy' => ' PCID, BLID, nichesID '
                ],
                'loanAmortizationInfo' => [
                    'table' => 'tblPCHMLOBasicLoanAmortizationInfo',
                    'orderBy' => ' PCID, BLID, AmortizationVal '
                ],
                'rateLockPeriodInfo' => [
                    'table' => 'tblPCHMLOBasicLoanRateLockPeriod',
                    'orderBy' => ' PCID, BLID, rateLockPeriod '
                ],
                'usedLoanPgm' => [
                    'select' => 'loanPgm',
                    'table' => 'tblPCHMLOBasicLoanInfo t1 JOIN tblPCHMLOBasicLoanPgmInfo t2 ON t1.PCID = t2.PCID AND t1.BLID = t2.BLID',
                    'where' => ' t1.activeStatus = 1 ',
                    'wherePCID' => ' AND t1.PCID = :PCID ',
                    'whereBLID' => ' AND t1.BLID NOT IN (:BLID) ',
                    'groupBy' => ' loanPgm ',
                    'orderBy' => ' t1.PCID '
                ]
            ];
        }

        $queryData = [
            'glNiches' => [
                'select' => 'tn.NID, tn.niches, tnc.NCID, CASE WHEN tnc.nichesCategory IS NULL THEN " No Category" ELSE tnc.nichesCategory END AS nichesCategory',
                'table' => 'tblHMLONiches AS tn LEFT JOIN tblNichesCategoryValues AS tncv ON tncv.NID = tn.NID LEFT JOIN tblNichesCategories AS tnc ON tnc.NCID = tncv.NCID',
                'where' => ' ((tn.nichesType = "M") OR (tn.nichesType= "C" AND tn.PCID = ",PCID,")) AND tn.activeStatus = 1 ',
                'wherePCID' => ' ',
                'whereBLID' => ' ',
            ],
            'marketPlaceFiles' => [
                'table' => 'tblPCHMLOBasicLoanFiles',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketPlaceLinks' => [
                'table' => 'tblPCHMLOBasicLoanlinks',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketSBALoanProduct' => [
                'table' => 'tblPCHMLOBasicLoanSBALoanProduct',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'exitStrategyInfo' => [
                'table' => 'tblPCHMLOBasicLoanExitStrategy',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketEquipmentType' => [
                'table' => 'tblPCHMLOBasicLoanEquipmentType',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketEntityStateOfFormation' => [
                'table' => 'tblPCHMLOBasicLoanEntityStateOfFormation',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketeElgibleState' => [
                'table' => 'tblPCHMLOBasicLoanElgibleState',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketPaymentFrequency' => [
                'table' => 'tblPCHMLOBasicLoanPaymentFrequency',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketplaceMinSeasoningPersonalBankruptcy' => [
                'table' => 'tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketplaceMinSeasoningBusinessBankruptcy' => [
                'table' => 'tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketplaceMinSeasoningForeclosure' => [
                'table' => 'tblPCHMLOBasicLoanMinSeasoningForeclosure',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketplaceLoanPurpose' => [
                'table' => 'tblPCHMLOBasicLoanLoanPurpose',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ],
            'marketMinTimeInBusiness' => [
                'table' => 'tblPCHMLOBasicLoanMinTimeInBusiness',
                'wherePCID' => ' ',
                'orderBy' => ' id '
            ]
        ];

        $queryData = array_merge($queryDataPCID, $queryData);
        foreach ($queryData as $key => $value) {
            $table = $value['table'];
            $select = $value['select'] ?? '*';
            $orderBy = $value['orderBy'] ? ' ORDER BY ' . $value['orderBy'] : '';
            $groupBy = $value['groupBy'] ? ' GROUP BY ' . $value['groupBy'] : '';

            $where = $value['where'] ? ' AND ' . $value['where'] : '';
            if ($value['wherePCID']) {
                $where .= $value['wherePCID'];
            } else {
                $where .= " AND PCID = :PCID ";
            }
            $params = ['PCID' => $PCID];
            if ($BLID > 0) {
                if ($value['whereBLID']) {
                    $where .= $value['whereBLID'];
                } else {
                    $where .= " AND BLID = :BLID ";
                }
                $params['BLID'] = $BLID;
            }
            $sql = "SELECT $select, '" . $key . "' AS myOpt FROM $table WHERE 1 $where $groupBy $orderBy";
            $res = Database2::getInstance()->queryData($sql, $params);
            if (!empty($res)) {
                $result[$key] = $res;
            }
        }

        return $result;
    }

}
