<?php

namespace models\storedProc;

use models\types\strongType;
use models\Database2;

class SP_GetPCHMLOBasicLoanInfoForFileLevel extends strongType
{
    public static function getReport(
        ?string $loanPgm = '',
        ?int $PCID = 0
    ): array
    {
        $result = [];

        if ($PCID > 0) {
            $sql = "SELECT 
                    t1.BLID 
                FROM 
                    tblPCHMLOBasicLoanInfo t1, 
                    tblPCHMLOBasicLoanPgmInfo t2
                WHERE 
                    t1.BLID = t2.BLID
                    AND t1.activeStatus = 1
                    AND t2.loanPgm = :loanPgm 
                    AND t2.PCID = :PCID 
                LIMIT 1";
            $params = [
                'PCID' => $PCID,
                'loanPgm' => $loanPgm
            ];
            $BLID = (int)Database2::getInstance()->queryData($sql, $params, null, true)['BLID'] ?? 0;

            if ($BLID > 0) {
                $queryData = [
                    'HMLOPCTransactionType' => [
                        'select' => 'transactionType',
                        'table' => 'tblPCHMLOBasicLoanTransactionType'
                    ],
                    'HMLOPCBasicLoanInfo' => [
                        'table' => 'tblPCHMLOBasicLoanInfo'
                    ],
                    'HMLOPCPropertyType' => [
                        'select' => 'propertyType',
                        'table' => 'tblPCHMLOBasicLoanPropertyType'
                    ],
                    'HMLOPCFuturePropertyType' => [
                        'select' => 'futurePropertyType',
                        'table' => 'tblPCHMLOBasicLoanFuturePropertyType'
                    ],
                    'HMLOPCExtnOption' => [
                        'select' => 'extnOption',
                        'table' => 'tblPCHMLOBasicLoanExtensionOption'
                    ],
                    'HMLOPCLoanTerm' => [
                        'select' => 'loanTerm',
                        'table' => 'tblPCHMLOBasicLoanTermInfo'
                    ],
                    'HMLOPCOccupancy' => [
                        'select' => 'occupancy',
                        'table' => 'tblPCHMLOBasicLoanOccupancy'
                    ],
                    'HMLOPCState' => [
                        'select' => 't1.stateCode, t2.stateName',
                        'table' => 'tblPCHMLOBasicLoanStateInfo t1 JOIN tblStates t2 ON t1.stateCode = t2.stateCode'
                    ],
                    'HMLOPCNiches' => [
                        'select' => 'nichesID',
                        'table' => 'tblPCHMLOBasicLoanNichesInfo'
                    ],
                    'HMLOPCAmortization' => [
                        'select' => 'AmortizationVal',
                        'table' => 'tblPCHMLOBasicLoanAmortizationInfo'
                    ],
                    'HMLOPCBasicRateLockPeriodInfo' => [
                        'select' => 'rateLockPeriod',
                        'table' => 'tblPCHMLOBasicLoanRateLockPeriod'
                    ],
                    'HMLOPCElgibleState' => [
                        'select' => 'stateCode',
                        'table' => 'tblPCHMLOBasicLoanElgibleState'
                    ],
                    'HMLOPCEntityType' => [
                        'select' => 'entityType',
                        'table' => 'tblPCHMLOBasicLoanEntityType'
                    ] ,
                    'customLoanGuidelinesExitStrategy' => [
                        'select' => 'exitStrategy',
                        'table' => 'tblPCHMLOBasicLoanExitStrategy'
                    ]
                ];

                foreach ($queryData as $key => $value) {
                    $table = $value['table'];
                    $select = $value['select'] ?? '*';

                    $where = $value['where'] ? ' AND ' . $value['where'] : '';
                    if ($value['whereBLID']) {
                        $where .= $value['whereBLID'];
                    } else {
                        $where .= " AND BLID = :BLID ";
                    }
                    $params['BLID'] = $BLID;
                    $sql = "SELECT $select, '" . $key . "' AS myOpt FROM $table WHERE 1 $where ";
                    $res = Database2::getInstance()->queryData($sql, $params);
                    if (!empty($res)) {
                        $result[$key] = $res;
                    }
                }
            }
        }

        return $result;
    }
}
