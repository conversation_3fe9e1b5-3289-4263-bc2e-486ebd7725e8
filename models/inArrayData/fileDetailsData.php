<?php

namespace models\inArrayData;

use models\lendingwise\tblFundingClosing;
use models\types\strongType;

class fileDetailsData extends strongType
{
    /* @var fileExtensionInfoData[] $fileExtensionInfo */
    public ?array $fileExtensionInfo = null;

    public ?tblFundingClosing $HUDFundingClosingInfo = null;

    public ?LMRInfoData $LMRInfo = null;
    public ?string $addedToFav = null;
    public ?file2InfoData $file2Info = null;
    public ?ResponseInfoData $ResponseInfo = null;
    public ?BrokerInfoData $BrokerInfo = null;
    public ?string $dummyBrokerId = null;
    public ?SecondaryBrokerInfoData $SecondaryBrokerInfo = null;
    public ?BranchInfoData $BranchInfo = null;
    /* @var employeeInfoData[] */
    public ?array $employeeInfo = null;
    public ?AssignedStaffInfoData $AssignedStaffInfo = null;
    public ?array $AssignedBOStaffInfo = null;
    public ?listingRealtorInfoData $listingRealtorInfo = null;
    public ?array $BillingPaymentInfo = null;
    public ?array $BillingFeeInfo = null;
    public ?array $listingInfo = null;
    public ?array $SSRegnInfo = null;
    /* @var hardshipInfoData[] $hardshipInfo */
    public ?array $hardshipInfo = null;
    public ?PCInfoData $PCInfo = null;

    /* @var PCCheckListInfoData[][] $PCCheckListInfo */
    public ?array $PCCheckListInfo = null;
    public ?array $LMRClientTypeInfo = null;
    public ?array $LMRChecklistInfo = null;
    public ?incomeInfoData $incomeInfo = null;
    public ?QAInfoData $QAInfo = null;
    public ?AssetsInfoData $AssetsInfo = null;
    public ?ACHInfoData $ACHInfo = null;
    public ?CCInfoData $CCInfo = null;
    public ?LenderInfo1Data $LenderInfo1 = null;
    public ?LenderInfo2Data $LenderInfo2 = null;
    public ?proposalInfoData $proposalInfo = null;
    public ?hardshipDescInfoData $hardshipDescInfo = null;

    /* @var RealEstateInfoData[] $RealEstateInfo */
    public ?array $RealEstateInfo = null;
    public ?REBrokerInfoData $REBrokerInfo = null;
    /* @var processorCommentsData[] */
    public ?array $processorComments = null;
    public ?clientInfoData $clientInfo = null;
    /* @var PCStatusInfoData[] */
    public ?array $PCStatusInfo = null;
    /* @var PCSubStatusInfoData[] */
    public ?array $PCSubStatusInfo = null;
    public ?array $fileSubstatusInfo = null;

    /* @var lenderNotesData[] $lenderNotes */
    public ?array $lenderNotes = null;
    public ?billingUrlInfoData $billingUrlInfo = null;

    /* @var AdditionalInfoData[] $AdditionalInfo */
    public ?array $AdditionalInfo = null;
    /* @var FileProInfoData[] */
    public ?array $FileProInfo = null;
    public ?FilePropInfoData $FilePropInfo = null;
    public ?principalResInfoData $principalResInfo = null;
    /* @var propertyCountyInfoData[] */
    public ?array $propertyCountyInfo = null;
    /* @var coBorrowerCountyInfoData[] */
    public ?array $coBorrowerCountyInfo = null;
    /* @var branchClientTypeInfoData[] */
    public ?array $branchClientTypeInfo = null;

    /* @var cklistNotRequiredArrayData[] $cklistNotRequiredArray */
    public ?array $cklistNotRequiredArray = null;

    /* @var chkNotReqEmpInfoData[] $chkNotReqEmpInfo */
    public ?array $chkNotReqEmpInfo = null;

    /* @var chkNotReqAgentInfoData[] $chkNotReqAgentInfo */
    public ?array $chkNotReqAgentInfo = null;

    /* @var chkNotReqBranchInfoData[] $chkNotReqBranchInfo */
    public ?array $chkNotReqBranchInfo = null;

    public ?array $LMRWFArray = null;
    public ?HUDBasicInfoData $HUDBasicInfo = null;
    public ?array $HUDLoanTypeInfo = null;
    public ?array $HUDTransactionInfo = null;
    public ?array $HUDSettlementInfo = null;
    public ?array $HUDItemsPayableInfo = null;
    public ?array $HUDLenderToPayInfo = null;
    public ?array $HUDReservesDepositInfo = null;
    public ?array $HUDAdditionalChargesInfo = null;
    /* @var RecentInfoData[] */
    public ?array $RecentInfo = null;
    /* @var CMAInfoData[] */
    public ?array $CMAInfo = null;
    public ?array $commissionInfo = null;
    /* @var creditorInfoData[] */
    public ?array $creditorInfo = null;
    public ?RESTInfoData $RESTInfo = null;
    public ?RESTNotesInfoData $RESTNotesInfo = null;
    public ?SSProposalInfoData $SSProposalInfo = null;
    public ?emailHistoryInfoData $emailHistoryInfo = null;
    public ?substatusHistoryInfoData $substatusHistoryInfo = null;
    public ?primeStatusHistoryInfoData $primeStatusHistoryInfo = null;
    public ?assignedStaffHistoryInfoData $assignedStaffHistoryInfo = null;
    public ?proposalSummaryInfoData $proposalSummaryInfo = null;
    public ?SSInfoData $SSInfo = null;
    public ?SSProposalSummaryInfoData $SSProposalSummaryInfo = null;
    /* @var HAFADocInfoData[] */
    public ?array $HAFADocInfo = null;
    public ?faxedDocHistoryInfoData $faxedDocHistoryInfo = null;
    public ?faxedDocRecipientInfoData $faxedDocRecipientInfo = null;
    public ?array $PCWFHistoryInfo = null;
    public ?array $LMRWFHistoryInfo = null;
    public ?array $WFListArray = null;
    public ?string $AdditionalLienContactInfo = null;
    public ?array $docArray = null;

    /* @var propertyValuationData[] */
    public ?array $propertyValuation = null;

    /* @var scheduledEmailData[] */
    public ?array $scheduledEmail = null;

    /* @var planInfoData[] $planInfo */
    public ?array $planInfo = null;

    public ?array $billingInfo = null;
    public ?eSignedDocData $eSignedDoc = null;
    public ?array $empWFInfo = null;

    /* @var branchWFInfoData[] $branchWFInfo */
    public ?array $branchWFInfo = null;

    /* @var agentWFInfoData[] $agentWFInfo */
    public ?array $agentWFInfo = null;
    public ?JoinderAppInfoData $JoinderAppInfo = null;
    public ?array $PCServiceType = null;
    public ?purchaseInfoData $purchaseInfo = null;
    public ?array $taskInfo = null;
    public ?array $notesEmpInfo = null;
    public ?array $notesAgentInfo = null;
    public ?array $notesBranchInfo = null;
    public ?array $notesClientInfo = null;
    public ?clientPaymentInfoData $clientPaymentInfo = null;
    /* @var PCLegalContractInfoData[] $PCLegalContractInfo */
    public ?array $PCLegalContractInfo = null;
    public ?array $billableMinutes = null;
    public ?array $fileWFNotesInfo = null;

    /* @var fileChecklistNotesInfoData[][] $fileChecklistNotesInfo */
    public ?array $fileChecklistNotesInfo = null;

    public ?HRRequiredCheckListInfoData $HRRequiredCheckListInfo = null;
    public ?HRInfoData $HRInfo = null;
    public ?HRHistoryInfoData $HRHistoryInfo = null;
    public ?loanAuditInfoData $loanAuditInfo = null;
    public ?loanAuditProductInfoData $loanAuditProductInfo = null;
    public ?loanAuditChecklistInfoData $loanAuditChecklistInfo = null;
    public ?loanAuditDocInfoData $loanAuditDocInfo = null;
    public ?loanAuditNotesInfoData $loanAuditNotesInfo = null;
    public ?empLAInfoData $empLAInfo = null;
    public ?branchLAInfoData $branchLAInfo = null;
    public ?agentLAInfoData $agentLAInfo = null;
    public ?empLADocInfoData $empLADocInfo = null;
    public ?branchLADocInfoData $branchLADocInfo = null;
    public ?agentLADocInfoData $agentLADocInfo = null;
    public ?listingprincipalResInfoData $listingprincipalResInfo = null;
    public ?studentLoanInfoData $studentLoanInfo = null;
    public ?studentLoanQAInfoData $studentLoanQAInfo = null;
    public ?array $PCWFServiceType = null;
    public ?array $PCClientWFServiceType = null;

    public ?fileContactsData $fileContacts = null;

    public ?studentReferenceInfoData $studentReferenceInfo = null;
    public ?CourtInfoData $CourtInfo = null;
    public ?string $legalBankRuptcyInfo = null;
    public ?preliminaryWorkInfoData $preliminaryWorkInfo = null;
    public ?interiorWorkInfoData $interiorWorkInfo = null;
    public ?exteriorWorkInfoData $exteriorWorkInfo = null;
    public ?otherWorkInfoData $otherWorkInfo = null;
    public ?fileSecondaryWFStatusData $fileSecondaryWFStatus = null;
    public ?counselAttorneyData $counselAttorney = null;
    public ?file3InfoData $file3Info = null;
    public ?payeeInfoData $payeeInfo = null;
    public ?CFPBInfoData $CFPBInfo = null;
    public ?CFPBAuditDocInfoData $CFPBAuditDocInfo = null;
    public ?CFPBDocsEmpIdArrayData $CFPBDocsEmpIdArray = null;
    public ?CFPBDocsBranchIdArrayData $CFPBDocsBranchIdArray = null;
    public ?CFPBDocsAgentIdArrayData $CFPBDocsAgentIdArray = null;
    public ?CFPBDocsClientIdArrayData $CFPBDocsClientIdArray = null;
    public ?CFPBAuditNotesInfoData $CFPBAuditNotesInfo = null;
    public ?empCFPBInfoData $empCFPBInfo = null;
    public ?branchCFPBInfoData $branchCFPBInfo = null;
    public ?agentCFPBInfoData $agentCFPBInfo = null;
    public ?clientCFPBInfoData $clientCFPBInfo = null;
    public ?notesHistoryInfoData $notesHistoryInfo = null;
    public ?taskHistoryInfoData $taskHistoryInfo = null;
    public ?taskEmpInfoData $taskEmpInfo = null;
    public ?CFPBAuditSearchSNHInfoData $CFPBAuditSearchSNHInfo = null;
    public ?CFPBAuditSearchSEHInfoData $CFPBAuditSearchSEHInfo = null;
    public ?CFPBAuditSearchSFHInfoData $CFPBAuditSearchSFHInfo = null;
    public ?CFPBAuditSearchSTHInfoData $CFPBAuditSearchSTHInfo = null;
    public ?faxedSentDocInfoData $faxedSentDocInfo = null;
    public ?CFPBAuditSearchSFHSentInfoData $CFPBAuditSearchSFHSentInfo = null;
    public ?CFPBAuditSearchSTHEmpInfoData $CFPBAuditSearchSTHEmpInfo = null;
    public ?additionalStateBarInfoData $additionalStateBarInfo = null;
    /* @var branchModuleInfoData[] */
    public ?array $branchModuleInfo = null;
    public ?array $fileModuleInfo = null;
    public ?moduleFileTabInfoData $moduleFileTabInfo = null;
    public ?PCRAMAttorneyInfoData $PCRAMAttorneyInfo = null;
    public ?PCRAMAffiliateInfoData $PCRAMAffiliateInfo = null;
    public ?PCRAMFeeGroupInfoData $PCRAMFeeGroupInfo = null;
    public ?fileRAMClientInfoData $fileRAMClientInfo = null;
    public ?fileRAMPaymentInfoData $fileRAMPaymentInfo = null;
    public ?maxRowRAMPaymentInfoData $maxRowRAMPaymentInfo = null;
    public ?MFInfoData $MFInfo = null;
    public ?LOExplanationInfoData $LOExplanationInfo = null;
    public ?fileLoanOriginationInfoData $fileLoanOriginationInfo = null;
    public ?fileLOAssetsInfoData $fileLOAssetsInfo = null;
    /* @var fileLOChekingSavingInfoData[] */
    public ?array $fileLOChekingSavingInfo = null;
    /* @var liabilitiesInfoData[] */
    public ?array $liabilitiesInfo = null;
    /* @var fileLOScheduleRealInfoData[] */
    public ?array $fileLOScheduleRealInfo = null;
    public ?fileLOPropInfoData $fileLOPropInfo = null;
    public ?OtherCreditsInfoData $OtherCreditsInfo = null;
    /* @var borEmploymentInfoData[] */
    public ?array $borEmploymentInfo = null;
    /* @var coBEmploymentInfoData[] */
    public ?array $coBEmploymentInfo = null;
    public ?fileLOExpensesInfoData $fileLOExpensesInfo = null;
    public ?FUInfoData $FUInfo = null;
    public ?array $CLEmpInfo = null;

    /* @var CLBranchInfoData[] $CLBranchInfo */
    public ?array $CLBranchInfo = null;

    /* @var CLAgentInfoData[] $CLAgentInfo */
    public ?array $CLAgentInfo = null;

    public ?fileVelocityInfoData $fileVelocityInfo = null;
    public ?fileStatusHistoryData $fileStatusHistory = null;
    public ?fileLSSummaryInfoData $fileLSSummaryInfo = null;
    public ?fileHMLOInfoData $fileHMLOInfo = null;
    public ?collateralPropertyInfoData $collateralPropertyInfo = null;
    public ?fileHMLOAssetsInfoData $fileHMLOAssetsInfo = null;
    public ?fileHMLOPropertyInfoData $fileHMLOPropertyInfo = null;
    public ?rehabItemInfoData $rehabItemInfo = null;
    public ?fileHMLOEntityInfoData $fileHMLOEntityInfo = null;
    /* @var fileMemberOfficerInfoData[] */
    public ?array $fileMemberOfficerInfo = null;
    /* @var fileHMLOEntityRefInfoData[] */
    public ?array $fileHMLOEntityRefInfo = null;
    public ?fileHMLOBackGroundInfoData $fileHMLOBackGroundInfo = null;
    public ?SBABackgroundData $SBABackground = null;
    public ?fileHMLOExperienceInfoData $fileHMLOExperienceInfo = null;
    public ?HMLOPropValuationDocInfoData $HMLOPropValuationDocInfo = null;
    public ?fileHMLOListOfRepairsData $fileHMLOListOfRepairs = null;
    public ?string $PCFileDocsCategory = null;
    public ?string $PCFileDocsName = null;
    public ?MFLoanInfoData $MFLoanInfo = null;

    /* @var fileCheckListInfoData[] $fileCheckListInfo */
    public ?array $fileCheckListInfo = null;

    /* @var fileChecklistNotesInfoNewData[][][] $fileChecklistNotesInfoNew */
    public ?array $fileChecklistNotesInfoNew = null;

    /* @var cklistNotRequiredNewArrayData[][] $cklistNotRequiredNewArray */
    public ?array $cklistNotRequiredNewArray = null;

    public ?borrowerMissingDocData $borrowerMissingDoc = null;
    public ?fileHMLONewLoanInfoData $fileHMLONewLoanInfo = null;
    public ?fileCalculatedValuesData $fileCalculatedValues = null;
    public ?MFLoanTermsInfoData $MFLoanTermsInfo = null;
    public ?dealAnalysisInfoData $dealAnalysisInfo = null;
    public ?trustDocPositionsInfoData $trustDocPositionsInfo = null;
    public ?notesArrayData $notesArray = null;

    /* @var HMLOPCTransactionTypeData[] */
    public ?array $HMLOPCTransactionType = null;

    /* @var HMLOPCPropertyTypeData[] */
    public ?array $HMLOPCPropertyType = null;

    /* @var HMLOPCExtnOptionData[] */
    public ?array $HMLOPCExtnOption = null;

    /* @var HMLOPCLoanTermData[] */
    public ?array $HMLOPCLoanTerm = null;


    /* @var HMLOPCOccupancyData[] */
    public ?array $HMLOPCOccupancy = null;

    /* @var HMLOPCStateData[] */
    public ?array $HMLOPCState = null;


    /* @var HMLOPCNichesData[] $HMLOPCNiches */
    public ?array $HMLOPCNiches = null;

    /* @var HMLOPCAmortizationValInfoData[] $HMLOPCAmortizationValInfo */
    public ?array $HMLOPCAmortizationValInfo = null;
    /* @var HMLOPCBasicLoanInfoData[] */
    public ?array $HMLOPCBasicLoanInfo = null;
    public ?PCBasicLoanTabFileIdExistsData $PCBasicLoanTabFileIdExists = null;

    /* @var filRentRollInfoData[] $filRentRollInfo */
    public ?array $filRentRollInfo = null;
    public ?listingRealtorInfo2Data $listingRealtorInfo2 = null;
    public ?fileFUEntityInfoData $fileFUEntityInfo = null;
    public ?fileFUCreditEnhancementInfoData $fileFUCreditEnhancementInfo = null;
    public ?fileFUCreditInfoData $fileFUCreditInfo = null;
    public ?array $WFNEmpInfo = null;

    /* @var WFNBranchInfoData[] */
    public ?array $WFNBranchInfo = null;

    /* @var WFNAgentInfoData[] $WFNAgentInfo */
    public ?array $WFNAgentInfo = null;

    public ?PCFieldsInfoData $PCFieldsInfo = null;
    public ?array $equipmentInfo = null;
    public ?array $PCquickAppFieldsInfo = null;
    public ?propertyValuationDocsData $propertyValuationDocs = null;
    /* @var AddGuarantorsInfoData[] */
    public ?array $AddGuarantorsInfo = null;
    public ?array $fileExpFilpGroundUp = null;
    /* @var clientDocsArrayData[] */
    public ?array $clientDocsArray = null;
    public ?sellerInfoData $sellerInfo = null;
    /* @var budgetAndDrawsInfoData[] */
    public ?array $budgetAndDrawsInfo = null;
    public ?fileBudgetAndDrawDocData $fileBudgetAndDrawDoc = null;
    public ?array $investorOtherInfo = null;
    public ?array $getInsuranceTypesGlobal = null;
    public ?array $getValuationMethodsGlobal = null;
    /* @var getInsuranceDtlsData[] */
    public ?array $getInsuranceDtls = null;
    public ?peerStreetData $peerStreet = null;
    public ?array $paydownInfo = null;
    public ?string $alowareContact = null;
    public ?thirdPartyServiceInfoData $thirdPartyServiceInfo = null;
    /* @var multiPropertyCountryData[] */
    public ?array $multiPropertyCountry = null;
    public ?array $LMRInternalLoanprograms = null;
    /* @var HMLOPCBasicEntityTypeData[] */
    public ?array $HMLOPCBasicEntityType = null;
    public ?LMRInternalLoanprogramsLongData $LMRInternalLoanprogramsLong = null;

    public ?array $LMRadditionalLoanprograms = null;

    /* @var sbaOtherBusinessDataData[] $sbaOtherBusinessData */
    public ?array $sbaOtherBusinessData = null;

    /* @var contingentLiabilitiesData[] */
    public ?array $contingentLiabilities = null;
    public ?estimatedProjectCostData $estimatedProjectCost = null;
    public ?collateralArrayData $collateralArray = null;

    /* @var collateralValuesArrayData[] $collateralValuesArray */
    public ?array $collateralValuesArray = null;

    public ?array $offerArray = null;
    public ?listingPageArrayData $listingPageArray = null;
    public ?lpArrayData $lpArray = null;
    public ?array $offerDocsArray = null;
    public ?propMgmtArrayData $propMgmtArray = null;
    public ?array $propMgmtDocsArray = null;
    /* @var creditMemoArrayData[] */
    public ?array $creditMemoArray = null;
    public ?array $missingDocInfo = null;
    /* @var HMLOPCBasicMinSeasoningPersonalBankruptcyInfoData[] */
    public ?array $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo = null;

    /* @var HMLOPCBasicMinSeasoningBusinessBankruptcyInfoData[] $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo */
    public ?array $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo = null;
    /* @var HMLOPCBasicMinSeasoningForeclosureInfoData[] */
    public ?array $HMLOPCBasicMinSeasoningForeclosureInfo = null;

    /* @var HMLOPCBasicSBALoanProductInfoData[] $HMLOPCBasicSBALoanProductInfo */
    public ?array $HMLOPCBasicSBALoanProductInfo = null;

    /* @var HMLOPCBasicEquipmentTypeInfoData[] $HMLOPCBasicEquipmentTypeInfo */
    public ?array $HMLOPCBasicEquipmentTypeInfo = null;

    /* @var HMLOPCBasicRateLockPeriodInfoData[] $HMLOPCBasicRateLockPeriodInfo */
    public ?array $HMLOPCBasicRateLockPeriodInfo = null;

    /* @var HMLOPCBasicLoanExitStrategyInfoData[] $HMLOPCBasicLoanExitStrategyInfo */
    public ?array $HMLOPCBasicLoanExitStrategyInfo = null;

    /* @var HMLOPCBasicEntitityStateFormationInfoData[] $HMLOPCBasicEntitityStateFormationInfo */
    public ?array $HMLOPCBasicEntitityStateFormationInfo = null;

    /* @var HMLOPCBasicPaymentFrequencyInfoData[] $HMLOPCBasicPaymentFrequencyInfo */
    public ?array $HMLOPCBasicPaymentFrequencyInfo = null;

    /* @var HMLOPCBasicLoanPurposeInfoData[] $HMLOPCBasicLoanPurposeInfo */
    public ?array $HMLOPCBasicLoanPurposeInfo = null;

    /* @var HMLOPCBasicMinTimeInBusinessInfoData[] $HMLOPCBasicMinTimeInBusinessInfo */
    public ?array $HMLOPCBasicMinTimeInBusinessInfo = null;

    /* @var borrowerAlternateNamesData[] */
    public ?array $borrowerAlternateNames = null;
    /* @var mortgagePropLoanInfoData[] */
    public ?array $mortgagePropLoanInfo = null;
    /* @var gogInfoData[] */
    public ?array $gogInfo = null;
    public ?loanOriginatorInfoData $loanOriginatorInfo = null;
    public ?salesMethodInfoData $salesMethodInfo = null;
    public ?feeScheduleInfoData $feeScheduleInfo = null;
    public ?equipmentInformationData $equipmentInformation = null;
    public ?adverseActionInfoData $adverseActionInfo = null;
    /* @var refinanceMortgageInfoData[] */
    public ?array $refinanceMortgageInfo = null;
    public ?array $fileHMLOChecklistUploadDocs = null;
    public ?array $LMRChecklistInfoNew = null;
    public ?array $fileHMLOChecklistUploadDocsNew = null;
    public ?loanSettingData $loanSetting = null;

    public ?HMLOPropInfoData $HMLOPropInfo = null;
    public ?HMLOlistingRealtorInfoData $HMLOlistingRealtorInfo = null;
    /* @var AssignedStaffInfoData[] */
    public ?array $assignedStaffInfo = null;
    public ?FilePropInfoData $filePropInfo = null;
    /* @var internalLPData[] */
    public ?array $internalLP = null;
    public ?noOfDaysInCurrentStatusData $noOfDaysInCurrentStatus = null;
    public ?noOfPendingTaskData $noOfPendingTask = null;

    public ?HMLOListOfRepairsData $HMLOListOfRepairs = null;

    /* @var legalContractInfoData[] */
    public ?array $legalContractInfo = null;

    /* @var creditorInfoStatusData[] */
    public ?array $creditorInfoStatus = null;

    /* @var fileMemberOfficerInfoData[] */
    public ?array $additionalGuarantors = null;

    /* @var PCChecklist1Data[] */
    public ?array $PCChecklist1 = null;

    public ?agentPromoCodesData $agentPromoCodes = null;
    public ?fileAgentInfoData $fileAgentInfo = null;

    public ?branchPromoCodesData $branchPromoCodes = null;

    public ?fileLOInfoData $fileLOInfo = null;

    public ?statusInfoData $statusInfo = null;

    public function fromData(?array $data): self
    {
        $missing = [];

        foreach ($data as $k => $v) {
            if (!$v) {
                $v = null;
                continue;
            }

            if (is_array($v) || is_callable($v)) {
                $class = 'models\\inArrayData\\' . $k . 'Data';

                switch ($k) {
                    case 'notesEmpInfo':
                    case 'notesClientInfo':
                    case 'notesAgentInfo':
                    case 'notesBranchInfo':
                    case 'billableMinutes':
                        $this->$k = [];
                        foreach ($v() as $i => $ds) {
                            if (!is_array($ds)) {
                                continue;
                            }
                            $c = new $class();
                            $c->fromData($ds);
                            $this->{$k}[$i] = $c;
                        }
                        break;

                    case 'processorComments':
                    case 'notesArray':
                        $this->$k = [];
                        foreach ($v() as $d) {
                            if (is_object($d)) {
                                //Debug($d);
                                /* @var strongType $d */
                                $d = $d->toArray();
                            }
                            $c = new $class();
                            $c->fromData($d);
                            $this->{$k}[] = $c;
                        }
                        break;

                    case 'employeeInfo':
                    case 'AssignedBOStaffInfo':
                    case 'BillingPaymentInfo':
                    case 'listingInfo':
                    case 'SSRegnInfo':
                    case 'PCStatusInfo':
                    case 'PCSubStatusInfo':
                    case 'fileSubstatusInfo':
                    case 'FileProInfo':
                    case 'creditorInfo':
                    case 'LMRWFHistoryInfo':
                    case 'billingInfo':
                    case 'fileLOChekingSavingInfo':
                    case 'fileLOScheduleRealInfo':
                    case 'borEmploymentInfo':
                    case 'equipmentInfo':
                    case 'AddGuarantorsInfo':
                    case 'budgetAndDrawsInfo':
                    case 'getInsuranceDtls':
                    case 'offerArray':
                    case 'propMgmtDocsArray':
                    case 'mortgagePropLoanInfo':
                    case 'HUDLoanTypeInfo':
                    case 'docArray':
                    case 'liabilitiesInfo':
                    case 'fileMemberOfficerInfo':
                    case 'fileHMLOEntityRefInfo':
                    case 'HMLOPCTransactionType':
                    case 'HMLOPCOccupancy':
                    case 'HMLOPCBasicLoanInfo':
                    case 'contingentLiabilities':
                    case 'offerDocsArray':
                    case 'creditMemoArray':
                    case 'HMLOPCBasicMinSeasoningPersonalBankruptcyInfo':
                    case 'HMLOPCBasicMinSeasoningForeclosureInfo':
                    case 'borrowerAlternateNames':
                    case 'gogInfo':
                    case 'paydownInfo':
                    case 'refinanceMortgageInfo':
                    case 'branchClientTypeInfo':
                    case 'propertyCountyInfo':
                    case 'coBorrowerCountyInfo':
                    case 'coBEmploymentInfo':
                    case 'legalContractInfo':
                    case 'assignedStaffInfo':
                    case 'internalLP':
                    case 'PCChecklist1':
                    case 'HMLOPCBasicEntityType':
                    case 'HMLOPCPropertyType':
                    case 'HMLOPCLoanTerm':
                    case 'HMLOPCState':
                    case 'sbaOtherBusinessData':
                    case 'HMLOPCExtnOption':
                    case 'scheduledEmail':
                    case 'hardshipInfo':
                    case 'PCLegalContractInfo':
                    case 'AdditionalInfo':
                    case 'HMLOPCAmortizationValInfo':
                    case 'HMLOPCBasicMinSeasoningBusinessBankruptcyInfo':
                    case 'HMLOPCBasicMinTimeInBusinessInfo':
                    case 'HMLOPCBasicSBALoanProductInfo':
                    case 'HMLOPCBasicEntitityStateFormationInfo':
                    case 'RealEstateInfo':
                    case 'HMLOPCNiches':
                    case 'HMLOPCBasicLoanPurposeInfo':
                    case 'HMLOPCBasicPaymentFrequencyInfo':
                    case 'HMLOPCBasicEquipmentTypeInfo':
                    case 'HMLOPCBasicRateLockPeriodInfo' :
                    case 'HMLOPCBasicLoanExitStrategyInfo' :
                    case 'fileExtensionInfo':
                    case 'RecentInfo':
                    case 'CMAInfo':
                    case 'HAFADocInfo':
                        $this->$k = [];
                        foreach ($v as $d) {
                            if (is_object($d)) {
                                //Debug($d);
                                /* @var strongType $d */
                                $d = $d->toArray();
                            }
                            $c = new $class();
                            $c->fromData($d);
                            $this->{$k}[] = $c;
                        }
                        break;
                    case 'LMRClientTypeInfo':
                    case 'LMRWFArray':
                    case 'commissionInfo':
                    case 'PCWFHistoryInfo':
                    case 'WFListArray':
                    case 'taskInfo':
                    case 'fileModuleInfo':
                    case 'fileExpFilpGroundUp':
                    case 'PCServiceType':
                    case 'fileCheckListInfo':
                    case 'multiPropertyCountry':
                    case 'PCCheckListInfo':
                    case 'lenderNotes':
                        $this->$k = [];
                        foreach ($v as $i => $ds) {
                            foreach ($ds as $d) {
                                if (!is_array($d)) {
                                    continue;
                                }
                                $c = new $class();
                                $c->fromData($d);
                                $this->{$k}[$i][] = $c;
                            }
                        }
                        break;
                    case 'fileContacts':
                        $this->$k = new fileContactsData();
                        $v['multiContact'] = $v['multiContact'] ?? [];
                        foreach ($v['multiContact'] as $i => $ds) {
                            if(isset(fileContactsData::$_alias[$i])){
                                $i = fileContactsData::$_alias[$i];
                            }
                            // we don't need every single type as they are custom
                            // types set by users
                            // if you need it for a report or package, add the type to the fileContactsData
                            if (!property_exists($this->{$k}, $i)) {
                                continue;
                            }

                            foreach ($ds as $d) {
                                if (!is_array($d)) {
                                    continue;
                                }

                                $c = new $class();
                                $c->fromData($d);
                                $this->{$k}->{$i}[] = $c;
                            }
                        }
                        break;
                    case 'BillingFeeInfo':
                        $this->$k = [];
                        foreach ($v as $i => $ds) {
                            foreach ($ds as $d) {
                                $c = new $class();
                                $c->fromData($d);
                                $this->{$k}[$i][$d['phase']] = $c;
                            }
                        }
                        break;
                    case 'filRentRollInfo':
                    case 'missingDocInfo':
                    case 'planInfo':
                    case 'cklistNotRequiredNewArray':
                    case 'fileChecklistNotesInfo':
                        $this->$k = [];
                        foreach ($v as $i => $ds) {
                            foreach ($ds as $j => $d) {
                                $c = new $class();
                                $c->fromData($d);
                                $this->{$k}[$i][$j] = $c;
                            }
                        }
                        break;
                    case 'empWFInfo':
                    case 'fileWFNotesInfo':
                    case 'branchModuleInfo':
                    case 'CLEmpInfo':
                    case 'WFNEmpInfo':
                    case 'investorOtherInfo':
                    case 'HUDTransactionInfo':
                    case 'HUDSettlementInfo':
                    case 'HUDItemsPayableInfo':
                    case 'HUDLenderToPayInfo':
                    case 'HUDReservesDepositInfo':
                    case 'HUDAdditionalChargesInfo':
                    case 'PCquickAppFieldsInfo':
                    case 'fileHMLOChecklistUploadDocs':
                    case 'creditorInfoStatus':
                    case 'LMRChecklistInfo':
                    case 'collateralValuesArray':
                    case 'propertyValuation':
                    case 'clientDocsArray':
                    case 'cklistNotRequiredArray':
                    case 'chkNotReqEmpInfo':
                    case 'chkNotReqAgentInfo':
                    case 'CLAgentInfo':
                    case 'WFNAgentInfo':
                    case 'agentWFInfo':
                    case 'CLBranchInfo':
                    case 'chkNotReqBranchInfo':
                    case 'branchWFInfo':
                    case 'WFNBranchInfo':
                        $this->$k = [];
                        foreach ($v as $i => $ds) {
                            if (!is_array($ds)) {
                                continue;
                            }
                            $c = new $class();
                            $c->fromData($ds);
                            $this->{$k}[$i] = $c;
                        }
                        break;

                    case 'getInsuranceTypesGlobal':
                    case 'getValuationMethodsGlobal':
                    case 'LMRInternalLoanprograms':
                    case 'LMRadditionalLoanprograms':
                        $this->{$k} = $v;
                        break;

                    case 'PCClientWFServiceType':
                    case 'PCWFServiceType':
                    case 'LMRChecklistInfoNew':
                    case 'fileHMLOChecklistUploadDocsNew':
                    case 'fileChecklistNotesInfoNew':
                        $this->$k = [];
                        foreach ($v as $i => $ds) {
                            foreach ($ds as $j => $d) {
                                $this->{$k}[$i][$j] = $d;
                            }
                        }
                        break;
                    case 'LMRInfo':
                    case 'file2Info':
                    case 'ResponseInfo':
                    case 'BrokerInfo':
                    case 'BranchInfo':
                    case 'listingRealtorInfo':
                    case 'PCInfo':
                    case 'incomeInfo':
                    case 'QAInfo':
                    case 'AssetsInfo':
                    case 'clientInfo':
                    case 'billingUrlInfo':
                    case 'FilePropInfo':
                    case 'LOExplanationInfo':
                    case 'fileLOAssetsInfo':
                    case 'fileLOPropInfo':
                    case 'fileVelocityInfo':
                    case 'fileHMLOInfo':
                    case 'fileHMLOAssetsInfo':
                    case 'fileHMLOPropertyInfo':
                    case 'fileHMLOEntityInfo':
                    case 'fileHMLOBackGroundInfo':
                    case 'SBABackground':
                    case 'fileHMLOExperienceInfo':
                    case 'fileHMLONewLoanInfo':
                    case 'fileCalculatedValues':
                    case 'sellerInfo':
                    case 'LMRInternalLoanprogramsLong':
                    case 'estimatedProjectCost':
                    case 'collateralArray':
                    case 'propMgmtArray':
                    case 'loanOriginatorInfo':
                    case 'equipmentInformation':
                    case 'loanSetting':
                    case 'ACHInfo':
                    case 'CCInfo':
                    case 'principalResInfo':
                    case 'RESTInfo':
                    case 'SecondaryBrokerInfo':
                    case 'fileLoanOriginationInfo':
                    case 'salesMethodInfo':
                    case 'feeScheduleInfo':
                    case 'payeeInfo':
                    case 'listingRealtorInfo2':
                    case 'HUDBasicInfo':
                    case 'listingPageArray':
                    case 'lpArray':
                    case 'adverseActionInfo':
                    case 'LenderInfo2':
                    case 'proposalInfo':
                    case 'hardshipDescInfo':
                    case 'LenderInfo1':
                    case 'proposalSummaryInfo':
                    case 'CourtInfo':
                    case 'SSProposalInfo':
                    case 'SSInfo':
                    case 'SSProposalSummaryInfo':
                    case 'counselAttorney':
                        $c = new $class();
                        $c->fromData($v);
                        $this->$k = $c;
                        break;
                    default: // break this out for dev
                        $missing[] = $k;
                        Debug($class, $v);
                        $c = new $class();
                        $c->fromData($v);
                        $this->$k = $c;
                        break;
                }
            } else {
                $this->$k = $v;
            }
        }
        if (sizeof($missing)) {
            Debug($missing);
        }

        $this->additionalGuarantors = [];
        if ($this->fileMemberOfficerInfo) {
            foreach ($this->fileMemberOfficerInfo as $item) {
                if (strcasecmp($item->memberPersonalGuarantee, 'Yes') == 0) {
                    $this->additionalGuarantors[] = $item;
                }
            }
        }
        return $this;
    }
}
