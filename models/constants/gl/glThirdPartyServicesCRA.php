<?php

namespace models\constants\gl;

use models\types\strongType;
use models\lendingwise\tblCreditReportingAgencyServiceMappings;

/**
 *
 */
class glThirdPartyServicesCRA extends strongType
{
    public ?string $Name = null;
    public ?string $Link = null;
    public ?array $Services = null;
    
    /* @var array $glThirdPartyServicesCRA */
    public static array $glThirdPartyServicesCRA;

    /**
     * @return self[]
     */
    public static function init(): array
    {
        if (empty(self::$glThirdPartyServicesCRA)) {
            $CRAs = tblCreditReportingAgencyServiceMappings::getCRAs();
            foreach ($CRAs as $key => $value) {
                self::$glThirdPartyServicesCRA[$key] = new self($value);
            }
        }
        return self::$glThirdPartyServicesCRA;
    }
}