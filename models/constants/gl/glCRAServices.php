<?php

namespace models\constants\gl;

use models\lendingwise\tblCreditReportingServices;
use models\types\strongType;

class glCRAServices extends strongType
{
    public const BORROWER_CREDIT_REPORT = 'borrowercreditreport';
    public const CO_BORROWER_CREDIT_REPORT = 'coborrowercreditreport';
    public const JOINT_CREDIT_REPORT = 'jointcreditreport';
    public const BORROWER_SOFT_PULL = 'borrowersoftpull';
    public const CO_BORROWER_SOFT_PULL = 'coborrowersoftpull';
    public const JOINT_SOFT_PULL = 'jointsoftpull';
    public const BORROWER_FRAUD = 'borrowerfraud';
    public const CO_BORROWER_FRAUD = 'coborrowerfraud';
    public const JOINT_FRAUD = 'jointfraud';
    public const BUSINESS_CREDIT_REPORT = 'businesscreditreport';
    public const AVM = 'avm';
    public const FLOOD = 'flood';
    public const CO_BORROWER_FLOOD = 'coborrowerflood';
    public const JOINT_FLOOD = 'jointflood';
    public const MERS = 'mers';
    public const CRIMINAL_RECORD_REPORT = 'criminalrecordreport';
    public const IDENTITY_IDX = 'identityidx';
    public const SOCIAL_SECURITY = 'socialsecurity';

    public static array $glCRAServices;

    public static function init(): array
    {
        if (empty(self::$glCRAServices)) {
            $CRAServices = tblCreditReportingServices::GetAll(['status' => 1]);
            foreach ($CRAServices as $value) {
                self::$glCRAServices[$value->key] = $value->name;
            }
        }
        return self::$glCRAServices;
    }
}