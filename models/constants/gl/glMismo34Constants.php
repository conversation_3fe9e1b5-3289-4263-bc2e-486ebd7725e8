<?php

namespace models\constants\gl;

use models\types\strongType;

class glMismo34Constants extends strongType
{
    public static array $borrowerRace = [
        1 => 'AmericanIndianOrAlaskaNative',
        2 => 'Asian',
        3 => 'BlackOrAfricanAmerican',
        4 => 'NativeHawaiianOrOtherPacificIslander',
        5 => 'White',
        6 => 'InformationNotProvided'
    ];

    public static array $borrowerGender = [
        1 => 'Female',
        2 => 'Male',
        3 => 'InformationNotProvided'
    ];

    public static array $borrowerDemoInfo = [
        1 => 'FaceToFace',
        2 => 'Telephone',
        3 => 'Mail',
        4 => 'Internet'
    ];

    public static array $borrowerEthnicity = [
        1 => 'FaceToFace',
        2 => 'Telephone',
        3 => 'Mail',
        4 => 'Internet'
    ];

    public static array $borrowerCitizenship = [
        'U.S. Citizen' => 'USCitizen',
        'Perm Resident Alien' => 'PermanentResidentAlien',
        'Non-Perm Resident Alien' => 'NonPermanentResidentAlien'
    ];

    public static array $lienPosition = [
        1 => 'FirstLien',
        2 => 'SecondLien',
    ];

    public static array $propertyUsageType = [
        'Owner Occupied' => 'PrimaryResidence',
        '2nd Home' => 'SecondHome',
        'Investment' => 'Investment'
    ];

    public static array $borVestingInfo = [
        'Joint tenancy with right of survivorship' => 'JointTenancyWithRightOfSurvivorship',
        'Community property with right of survivorship' => 'CommunityPropertyWithRightOfSurvivorship',
        'Tenancy in Common' => 'TenancyInCommon',
        'Sole Ownership' => 'SoleOwnership',
        'Living Trust' => 'Trust',
    ];

}