<?php

namespace models\constants\gl;

class glHMLOExitStrategy
{

    const applicationLoanExitOption = [
        'Fix & Flip'        => 'Fix & Flip',
        'Fix & Hold'        => 'Fix & Hold',
        'Wholesale/Re-sale' => 'Wholesale/Re-sale',
        'Refi'              => 'Refi',
    ];
    public static array $filteredOptions = [];
    public static array $options = [
        'Fix & Flip'        => 'Fix & Flip',
        'Fix & Hold'        => 'Fix & Hold',
        'Wholesale/Re-sale' => 'Wholesale/Re-sale',
        'Refi'              => 'Refi',
        'Long Term Rental'  => 'Long Term Rental',
        'Short Term Rental' => 'Short Term Rental',
        'AirBnb'            => 'AirBnb',
        'Build and Sell'    => 'Build and Sell',
        'Other'             => 'Other',
    ];
    public static array $glHMLOExitStrategy = [
        'Fix & Flip',
        'Fix & Hold',
        'Wholesale/Re-sale',
        'Refi',
        'Long Term Rental',
        'Short Term Rental',
        'AirBnb',
        'Build and Sell',
        'Other',
    ];
    public static array $applicationLoanExitOptions = self::applicationLoanExitOption;

    public static array $glHMLOExitStrategyRBI = [
        'Refi'    => 'Refi',
        'Sell'    => 'Sell',
        'Pay Off' => 'Pay Off',
    ];
    public static array $glHMLOExitStrategyBDCAPTIAL = [
        'Long Term Rental'  => 'Long Term Rental',
        'Short Term Rental' => 'Short Term Rental',
    ];
    public static array $glHMLOExitStrategyPC_GIF = [
        'Fix & Flip' => 'Fix & Flip',
        'Fix & Hold' => 'Fix & Hold',
    ];

    public static array $groundUpConstructionExitOptions = [
        'Build To Rent' => 'Build To Rent',
        'Build To Sell' => 'Build To Sell',
    ];

    public static function getExitStrategy($PCID, ?string $loanProgram = null): array
    {
        switch ($PCID) {
            case glPCID::PCID_RBI_PRIVATE_LENDING :
            case glPCID::PCID_DEV_DAVE :
                return self::$glHMLOExitStrategyRBI;
            case glPCID::PCID_PROD_CV3 :
                return $loanProgram == 'BRLGUC' ? self::$groundUpConstructionExitOptions : self::$applicationLoanExitOptions;
            case glPCID::PCID_BD_CAPTIAL :
                return self::$glHMLOExitStrategyBDCAPTIAL;
            case glPCID::PCID_3198 :
                return self::$glHMLOExitStrategyPC_GIF;
            default:
                return self::$options;
        }
    }

    public static function getApplicationLoanExitOptions($PCID, ?string $loanProgram = null): array
    {
        if ($PCID == glPCID::PCID_PROD_CV3 && $loanProgram == 'BRLGUC') {
            return self::$groundUpConstructionExitOptions;
        }
        return self::$applicationLoanExitOptions;
    }

}
