<?php

namespace models\types;

use models\Database2;
use models\dbExaminer;
use models\lendingwise\tblCustomFieldType;
use models\lendingwise\tblFile;
use models\lendingwise\tblProperties;
use models\lendingwise_log\ChangeLog;
use models\lendingwise_log\ContactsLog;
use models\lendingwise_log\DeleteLog;
use models\standard\Dates;
use models\standard\Strings;
use ReflectionClass;
use ReflectionNamedType;

/**
 *
 */
class databaseType extends strongType
{
    protected static ?array $_propertyType = null;
    protected static ?array $_keys = null;
    protected static ?array $_defaultValues = null;
    protected static bool $_has_change_log = true;

    public function getLinkedTable(string $tableName, bool $create = false)
    {
        if ($tableName == static::TABLE) {
            return $this;
        }

        Debug($tableName);
        return null;
    }

    public function getNewLinkedTable(string $tableName)
    {
        return $this->getLinkedTable($tableName, true);
    }


    public static function RecordChangeLog(
        databaseType  $new,
        ?databaseType $old, // allow null so we can set has change log to false and not get original record
        string        $primaryKey,
        string        $source,
        bool          $deleted = false,
        bool          $is_insert = false
    ): void
    {
        if (!static::$_has_change_log) {
            return;
        }


        $old_array = $old ? $old->toArray() : [];
        $new_array = $new->toArray();

        $changes = [];

        if ($deleted) {
            $changes['--DELETED--'] = 1;
        } else {
            foreach ($old_array as $k => $v) {
                if(!$new_array[$k] && !$v){
                    continue;
                }
                if ($new_array[$k] !== $v) {
                    $changes[$k] = ['old' => $v, 'new' => $new_array[$k]];
                }
            }
        }

        if (!sizeof($changes)) {
            return;
        }
        $operation = null;
        if ($deleted) {
            $dataColumn = ChangeLog::getLMRIdColumn($new);
            $LMRId = array_key_exists($dataColumn, $old_array) ? $old_array[$dataColumn] : null;
            if ($LMRId) {
                DeleteLog::Insert(
                    static::class,
                    $primaryKey,
                    $source,
                    $old->toArray(),
                    $_SESSION['userNumber'] ?? 0,
                    $LMRId
                );
            }
            $operation = 'Delete';
        }
        ChangeLog::Insert(
            static::class,
            $primaryKey,
            $source,
            $changes,
            $_SESSION['userNumber'] ?? 0,
        );
        if ($is_insert) {
            $operation = 'Insert';
            $dataColumn = ChangeLog::getLMRIdColumn($new);
            $LMRId = array_key_exists($dataColumn, $new_array) ? $new_array[$dataColumn] : null;
        }
        if ($source == 'tblFileContacts') {
            ContactsLog::Insert(
                static::class,
                $primaryKey,
                $source,
                $deleted ? $old->toArray() : $new_array,
                $_SESSION['userNumber'] ?? 0,
                $LMRId,
                $operation
            );
        }
    }

    public function safeSet(
        string $name,
               $v,
        ?int   $fieldType = null
    ): void
    {
        if ($fieldType) { // for cases when the database column does not match the data type intended
            switch ($fieldType) {
                case tblCustomFieldType::SSN:
                case tblCustomFieldType::Phone:
                    $v = Strings::NumbersOnly($v);
                    break;
                case tblCustomFieldType::Currency:
                    $v = (is_null($v) || $v === '') ? null : Strings::replaceCommaValues($v);
                    break;
            }
        }
        if (isset(static::$_propertyType[$name])) {
            switch (static::$_propertyType[$name]) {
                case 'date':
                    if (!is_null($v)) {
                        if ($v == '0000-00-00') {
                            $v = null;
                        }
                        parent::safeSet($name, $v ? Dates::Datestamp($v) : null);
                        return;
                    }
                    break;
                case 'datetime':
                    if (!is_null($v)) {
                        if ($v == '0000-00-00 00:00:00') {
                            $v = null;
                        }
                        parent::safeSet($name, $v ? Dates::Timestamp($v) : null);
                        return;
                    }
                    break;
            }
        }
        parent::safeSet($name, $v);
    }

    /**
     * @param array|null $data
     */
    public function __construct(array $data = null)
    {
        if ($data) {
            $this->fromData($data);
        }
    }

    /**
     * @return mixed
     */
    public function toArray(): array
    {
        $list = get_object_vars($this);
        foreach ($list as $k => $v) {
            if ($k[0] === '_' && !isset(static::$_propertyType[$k])) {
                unset($list[$k]);
            }
        }
        return $list;
    }

    public static function queryData(
        string   $qry,
        array    $params = null,
        callable $mapping_function = null,
                 $single = null,
        string   $keyParam = null,
        bool     $debug = false
    ): array
    {
        Database2::getInstance()->setDatabase(static::$_database ?: DB_NAME);
        $res = Database2::getInstance()->queryData(
            $qry,
            $params,
            $mapping_function,
            $single,
            $keyParam,
            $debug
        );
        Database2::getInstance()->setDatabase(DB_NAME);
        return $res;
    }

    public static function executeQuery(
        string $qry,
        array  $params = null): array
    {
        Database2::getInstance()->setDatabase(static::$_database ?: DB_NAME);
        $res = Database2::getInstance()->executeQuery($qry, $params);
        Database2::getInstance()->setDatabase(DB_NAME);
        return $res;
    }

    /**
     * @param string $qry
     * @param array $param_sets
     * @return int
     */
    public function insertMulti(
        string $qry,
        array  $param_sets
    ): int
    {
        Database2::getInstance()->setDatabase(static::$_database ?: DB_NAME);
        $res = Database2::getInstance()->insertMulti($qry, $param_sets);
        Database2::getInstance()->setDatabase(DB_NAME);
        return $res;
    }

    /**
     * @param string $namespace
     * @param string $table
     * @param array $params
     * @param string|null $order_by
     * @return array
     */
    public function getLinkedRecords(
        string  $namespace,
        string  $table,
        array   $params,
        ?string $order_by = null
    ): array
    {
        $where = [];
        foreach ($params as $k => $v) {
            $where[] = $k . ' = :' . $k;
        }
        $sql = '
            SELECT
                *
            FROM
                `' . (static::$_database ?: DB_NAME) . '`.`' . $table . '`
            WHERE
                ' . implode(' AND ', $where) . '
        ';
        if ($order_by) {
            $sql .= ' ORDER BY ' . $order_by;
        }
        $class = $namespace . '\\' . $table;
        return static::queryData($sql, $params, function ($row) use ($class) {
            return new $class($row);
        });
    }

    /**
     * @param $namespace
     * @param $table
     * @param $params
     * @return mixed|null
     */
    public function getLinkedRecord($namespace, $table, $params)
    {
        $where = [];
        foreach ($params as $k => $v) {
            $where[] = $k . ' = :' . $k;
        }
        $sql = '
            SELECT
                *
            FROM
                `' . (static::$_database ?: DB_NAME) . '`.`' . $table . '`
            WHERE
                ' . implode(' AND ', $where) . '
        ';
        $class = $namespace . '\\' . $table;
        return static::queryData($sql, $params, function ($row) use ($class) {
            /* @var databaseType $t */
            $t = new $class();
            $t->fromData($row);
            return $t;
        })[0] ?? null;
    }

    /**
     * @param array|null $where
     * @return static|null
     */
    public static function Get(array $where = null)
    {
        $params = [];
        $t = [];
        foreach ($where as $c => $v) {
            $cv = self::_parse_col_val($c, $v);
            $v = $cv['val'];

            if (!is_array($v) && strtolower($v) === 'null') {
                $t[] = $c . ' IS NULL';
            } else {
                $t[] = $cv['col'];
                if (is_array($v)) {
                    foreach ($v as $a) {
                        $params[] = $a;
                    }
                } elseif (!is_null($v)) {
                    $params[] = $v;
                }

            }
        }
        $where_sql = implode(' AND ', $t);
        $sql = '
            SELECT
                *
            FROM
                `' . (static::$_database ?: DB_NAME) . '`.`' . static::$_table . '`
                WHERE 1 = 1
                ' . (sizeof($where) ? ' AND ' . $where_sql : '') . '
            LIMIT 1
        ';

        foreach ($params as $k => $v) {
            $params[$k] = $v && !is_numeric($v) ? addslashes($v) : $v;
        }


        $res = static::queryData($sql, $params, function ($row) {
            return new static($row);
        });
        return $res[0] ?? null;
    }

    /**
     * @param array|null $where
     * @return int
     */
    public static function GetCount(array $where = null): int
    {
        $params = [];

        $sql_order = '';

        $sql_where = '1=1';
        if ($where) {
            $t = [];
            foreach ($where as $c => $v) {
                $cv = self::_parse_col_val($c, $v);
                $v = $cv['val'];

                if (!is_array($v) && strtolower($v) === 'null') {
                    $t[] = $c . ' IS NULL';
                } else {
                    $t[] = $cv['col'];
                    if (is_array($v)) {
                        foreach ($v as $a) {
                            $params[] = $a;
                        }
                    } elseif (!is_null($v)) {
                        $params[] = $v;
                    }
                }
            }
            $sql_where = implode(' AND ', $t);
        }

        $sql = '
            SELECT
                COUNT(*) AS count
            FROM
                `' . (static::$_database ?: DB_NAME) . '`.`' . static::$_table . '`
                WHERE 1 = 1
                ' . ($where && sizeof($where) ? ' AND ' . $sql_where : '') . '
                ' . $sql_order . '
        ';

        foreach ($params as $k => $v) {
            $params[$k] = $v && !is_numeric($v) ? addslashes($v) : $v;
        }

        return static::queryData($sql, $params)[0]['count'] ?? 0;
    }

    /**
     * @param array|null $where
     * @return static[]
     */
    public static function GetAll(array $where = null, array $order_by = null, int $limit = 0): array
    {
        $params = [];

        $sql_order = '';
        if (is_array($order_by)) {
            $sql_order = [];
            foreach ($order_by as $col => $dir) {
                $sql_order[] .= '`' . trim($col) . '` ' . $dir;
            }
            $sql_order = 'ORDER BY ' . implode(', ', $sql_order);
        }


        $sql_where = '1=1';
        if ($where) {
            $t = [];
            foreach ($where as $c => $v) {
                $cv = self::_parse_col_val($c, $v);
                $v = $cv['val'];

                if (!is_array($v) && strtolower($v) === 'null') {
                    $t[] = $c . ' IS NULL';
                } else {
                    $t[] = $cv['col'];
                    if (is_array($v)) {
                        foreach ($v as $a) {
                            $params[] = $a;
                        }
                    } elseif (!is_null($v)) {
                        $params[] = $v;
                    }
                }
            }
            $sql_where = implode(' AND ', $t);
        }

        $sql = '
            SELECT
                *
            FROM
                `' . (static::$_database ?: DB_NAME) . '`.`' . static::$_table . '`
                WHERE 1 = 1
                ' . ($where && sizeof($where) ? ' AND ' . $sql_where : '') . '
                ' . $sql_order . '
        ';

        foreach ($params as $k => $v) {
            $params[$k] = $v && !is_numeric($v) ? addslashes($v) : $v;
        }

        if ($limit) {
            $sql .= ' LIMIT ' . $limit;
        }
        return static::queryData($sql, $params, function ($row) {
            return new static($row);
        });
    }

    /**
     * @param string $table_name
     * @param array $where
     * @param array|null $order_by
     * @return static[]
     */
    protected static function doSearch(string $table_name, array $where, array $order_by = null): array
    {
        // remove namespaces
        $table_name = explode('\\', $table_name);
        $table_name = $table_name[sizeof($table_name) - 1];

        $sql = '

            SELECT
                *
            FROM
                ' . $table_name . '
        ';
        $sql_where = [];
        foreach ($where as $key => $value) {
            $sql_where[] = $key . ' = :' . $key;
        }
        $sql .= 'WHERE ' . implode(' AND ', $sql_where);
        if ($order_by && sizeof($order_by)) {
            $sql .= ' ORDER BY ' . implode(', ', $order_by);
        }
        return static::queryData($sql, $where, function ($row) {
            return new static($row);
        });
    }

    protected static function _parse_col_val($col, $val): array
    {
        // extra + symbols allow us to do AND on the same column
        $col = str_replace('+', '', $col);
        $col = '`' . $col . '`';

        if (is_object($val)) {
            Debug(['QuickDRY Error' => '$val is object', $val]);
        }
        if (substr($val, 0, strlen('{BETWEEN} ')) === '{BETWEEN} ') {
            $val = trim(Strings::RemoveFromStart('{BETWEEN}', $val));
            $val = explode(',', $val);
            $col = $col . ' BETWEEN {{}} AND {{}}';
        } elseif (substr($val, 0, strlen('{IN} ')) === '{IN} ') {
            $val = trim(Strings::RemoveFromStart('{IN}', $val));
            $val = explode(',', $val);
            $col = $col . ' IN (' . Strings::StringRepeatCS('@', sizeof($val)) . ')';
        } elseif (substr($val, 0, strlen('{DATE} ')) === '{DATE} ') {
            $col = 'CONVERT(date, ' . $col . ') = {{}}';
            $val = trim(Strings::RemoveFromStart('{DATE}', $val));
        } elseif (substr($val, 0, strlen('{YEAR} ')) === '{YEAR} ') {
            $col = 'DATEPART(yyyy, ' . $col . ') = {{}}';
            $val = trim(Strings::RemoveFromStart('{YEAR}', $val));
        } elseif (substr($val, 0, strlen('NLIKE ')) === 'NLIKE ') {
            $col = $col . ' NOT LIKE {{}}';
            $val = trim(Strings::RemoveFromStart('NLIKE', $val));
        } elseif (substr($val, 0, strlen('NILIKE ')) === 'NILIKE ') {
            $col = 'LOWER(' . $col . ')' . ' NOT LIKE LOWER({{}}) ';
            $val = trim(Strings::RemoveFromStart('NILIKE', $val));
        } elseif (substr($val, 0, strlen('ILIKE ')) === 'ILIKE ') {
            $col = 'LOWER(' . $col . ')' . ' ILIKE LOWER({{}}) ';
            $val = trim(Strings::RemoveFromStart('ILIKE', $val));
        } elseif (substr($val, 0, strlen('LIKE ')) === 'LIKE ') {
            $col = $col . ' LIKE {{}}';
            $val = trim(Strings::RemoveFromStart('LIKE', $val));
        } elseif (substr($val, 0, strlen('<= ')) === '<= ') {
            $col = $col . ' <= {{}} ';
            $val = trim(Strings::RemoveFromStart('<=', $val));
        } elseif (substr($val, 0, strlen('>= ')) === '>= ') {
            $col = $col . ' >= {{}} ';
            $val = trim(Strings::RemoveFromStart('>=', $val));
        } elseif (substr($val, 0, strlen('<> ')) === '<> ') {
            $val = trim(Strings::RemoveFromStart('<>', $val));
            if (strtolower($val) !== 'null') {
                $col = $col . ' <> {{}} ';
            } else {
                $col = $col . ' IS NOT NULL';
                $val = null;
            }
        } elseif (substr($val, 0, strlen('< ')) === '< ') {
            $col = $col . ' < {{}} ';
            $val = trim(Strings::RemoveFromStart('<', $val));
        } elseif (substr($val, 0, strlen('> ')) === '> ') {
            $col = $col . ' > {{}} ';
            $val = trim(Strings::RemoveFromStart('>', $val));
        } else {
            if (is_null($val)) {
                $col = $col . ' IS NULL';
            } else {
                $col = $col . ' = {{}} ';
            }
        }

        return ['col' => $col, 'val' => $val];
    }


    public function sanitizeStringData($reflect): void
    {
        foreach ($this as $k => $v) {
            if ($reflect->hasProperty($k)) {
                $property = $reflect->getProperty($k);
                $type = $property->getType();
                if ($type instanceof ReflectionNamedType
                    && $type->getName() === 'string'
                    && is_string($v)) {
                    $this->$k = stripslashes($v);
                }
            }
        }
    }
    public function Save(): array
    {
        $reflect = new ReflectionClass(static::class);
        $this->sanitizeStringData($reflect);
        foreach ((static::$_defaultValues ?? []) as $column => $val) {
            if (is_null($this->$column)) {
                if ($val === 'CURRENT_TIMESTAMP') {
                    $this->$column = Dates::Timestamp();
                } else {
                    $this->$column = $val;
                }
            }
        }

        $primary_set = false;
        $primary_index = null;
        if (static::$_keys) {
            foreach (static::$_keys as $index => $cols) {
                $primary_set = false;
                if (sizeof($cols)) {
                    $primary_set = true;
                    $primary_index = $index;
                    foreach ($cols as $col) {
                        if (!$this->$col) {
                            $primary_set = false;
                            break;
                        }
                    }
                }
                if ($primary_set) {
                    break;
                }
            }
        }
        $is_insert = true;
        if ($primary_set) {
            $is_insert = false;
            if ($primary_index !== 'PRIMARY' && isset(static::$_keys['PRIMARY'])) {
                // if a unique key is set but the primary key isn't, it's an insert
                foreach (static::$_keys['PRIMARY'] as $k) {
                    if (!$this->$k) {
                        $is_insert = true;
                    }
                    break;
                }
            }
        }

        foreach (static::$_propertyType as $k => $type) {
            if ($type === 'date' && $this->$k == '0000-00-00') {
                $this->$k = null;
            }
            if ($type === 'datetime' && $this->$k == '0000-00-00 00:00:00') {
                $this->$k = null;
            }
        }

        $props = $reflect->getProperties();
        $columns = [];
        foreach ($props as $prop) {
            $colName = $prop->getName();

            if ($colName[0] == '_') {
                if (!isset(static::$_propertyType[$colName])) {
                    continue;
                }
            }
            if ($prop->isPublic()) {
                if (isset(static::$_keys['PRIMARY'])) {
                    if (in_array($prop->getName(), static::$_keys['PRIMARY'])) {
                        continue;
                    }
                }
                $columns[] = $colName;
            }
        }

        $orig = null;
        $primary_col = static::$_keys['PRIMARY'][0] ?? null;
        if (static::$_has_change_log && $primary_col) {
            if (!$is_insert) {
                $where = [];
                $where[$primary_col] = $this->$primary_col;
                $orig = static::Get($where);
            } else {
                $orig = new static();
            }
        }

        $sql = '
            ' . ($is_insert ? 'INSERT INTO' : 'UPDATE') . '
                `' . (static::$_database ?: DB_NAME) . '`.`' . static::$_table . '`
SET
            ';


        $set_sql = [];
        foreach ($columns as $column) {
            $column_name = $column;
            if ($column_name[0] == '_') {
                $column_name = substr($column_name, 1);
            }

            $set_sql [] = '`' . $column_name . '` = :' . $column . PHP_EOL;
        }
        $sql .= implode(', ', $set_sql);

        if (!$is_insert) {
            $where = [];
            foreach (static::$_keys[$primary_index] as $k) {
                $col_val = self::_parse_col_val($k, $this->$k);
                $where_col = $col_val['col'];
                $where[] = str_replace('{{}}', ':' . $k, $where_col);
            }
            $sql .= ' WHERE ' . implode(PHP_EOL . ' AND ', $where);
        }

        $params = $this->toArray();
        foreach ($params as $k => $v) {
            $params[$k] = $v && !is_numeric($v) ? addslashes($v) : $v;
        }

        $res = static::executeQuery($sql, $params);
        if ($is_insert && isset(static::$_keys['PRIMARY'])) {
            $col = static::$_keys['PRIMARY'][0];
            $this->$col = $res['last_id'];
        }
        if ($res['error']) {
            Debug($res);
        }

        if ($primary_col && $this->$primary_col) {
            self::RecordChangeLog(
                $this,
                $orig,
                $this->$primary_col,
                static::$_table,
                false,
                $is_insert
            );
        }
        return $res;
    }

    public function Delete(): ?array
    {
        $primary_set = false;
        $primary_index = null;
        if (static::$_keys) {
            foreach (static::$_keys as $index => $cols) {
                $primary_set = false;
                if (sizeof($cols)) {
                    $primary_set = true;
                    $primary_index = $index;
                    foreach ($cols as $col) {
                        if (!$this->$col) {
                            $primary_set = false;
                            break;
                        }
                    }
                }
                if ($primary_set) {
                    break;
                }
            }
        }

        // unique key or primary key must be set
        if (!$primary_set) {
            return null;
        }

        $where = [];
        foreach (static::$_keys[$primary_index] as $k) {
            $col_val = self::_parse_col_val($k, $this->$k);
            $where_col = $col_val['col'];
            $where[] = str_replace('{{}}', ':' . $k, $where_col);
        }
        $sql = '
            DELETE FROM
                `' . (static::$_database ?: DB_NAME) . '`.`' . static::$_table . '`
             WHERE ' . implode(PHP_EOL . ' AND ', $where);

        $params = $this->toArray();

        $res = static::executeQuery($sql, $params);
        if ($res['error']) {
            Debug($res);
        }

        $primary_col = static::$_keys['PRIMARY'][0] ?? null;
        if ($primary_col && $this->$primary_col) {
            self::RecordChangeLog(
                new static(),
                $this,
                $this->$primary_col,
                static::$_table,
                true,
                false
            );
        }

        return $res;
    }

    public static function getPropertyLength(string $columnName): int
    {
        return intval(dbExaminer::getDataLength(static::$_propertyType[$columnName] ?? ''));
    }
}
