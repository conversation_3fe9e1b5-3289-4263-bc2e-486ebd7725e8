<?php

namespace models;

use models\constants\gl\glMortgageInvestorOwnerArray;
use models\constants\gl\glpaymentFrequency;
use models\constants\GpropertyTypeNumbArray;
use models\constants\HMDAActionTaken;
use models\constants\methodOfContactArray;
use models\constants\SMSServiceProviderArray;
use models\constants\SMSServiceProviderDomainArray;
use models\constants\timeZoneArray;
use models\Controllers\LMRequest\Property;
use models\lendingwise\_tblSlowQueries;
use models\lendingwise\tblCustomFieldType;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Integers;
use models\standard\Strings;
use mysqli_result;
use phpseclib3\Math\BigInteger\Engines\PHP;

/**
 *
 */
class Database2 extends masterSlaveDBConnection2
{
    const DATATYPE_ZIP = 'zip';
    const DATATYPE_NUMBER = 'number';
    const DATATYPE_MONEY = 'money';
    const DATATYPE_MONEY_STR = 'money_str';
    const DATATYPE_PERCENT_STR = 'DATATYPE_PERCENT_STR';
    const DATATYPE_DATE = 'date';
    const DATATYPE_DATE_STANDARD = 'standard_date';
    const DATATYPE_EMAIL = 'email';
    const DATATYPE_STATE = 'state';
    const DATATYPE_STRING = 'string';
    const DATATYPE_MULTI_SELECT = 'multiselect';
    const DATATYPE_PROPERTY_TYPE_NUMBER = 'propertyTypeNumb';
    const DATATYPE_METHOD_OF_CONTACT = 'methodOfContactArray';
    const DATATYPE_SERVICE_PROVIDER = 'SMSServiceProviderArray';
    const DATATYPE_TIMEZONE = 'timeZoneArray';
    const DATATYPE_PHONE_NUMBER = 'phone';
    const DATATYPE_SSN4 = 'ssn4';
    const DATATYPE_SSN = 'ssn';
    const DATATYPE_ACTION_TAKEN_STR = 'action_taken_str';
    const DATATYPE_TYPE_OF_PURCHASER_STR = 'type_of_purchases_str';
    const DATATYPE_REASON_FOR_DENIAL_STR = 'DATATYPE_REASON_FOR_DENIAL_STR';
    const DATATYPE_FURNISH_THIS_INFO_STR = 'DATATYPE_FURNISH_THIS_INFO_STR';
    const DATATYPE_ETHNICITY = 'DATATYPE_ETHNICITY';
    const DATATYPE_ETHNICITY_SUB = 'DATATYPE_ETHNICITY_SUB';
    const DATATYPE_RACE = 'DATATYPE_RACE';
    const DATATYPE_RACE_SUB = 'DATATYPE_RACE_SUB';
    const DATATYPE_GENDER = 'DATATYPE_GENDER';
    const DATATYPE_VETERAN = 'DATATYPE_VETERAN';
    const DATATYPE_INSURANCE_TYPE = 'DATATYPE_INSURANCE_TYPE';
    const DATATYPE_PAYMENT_FREQUENCY = 'DATATYPE_PAYMENT_FREQUENCY';
    const DATATYPE_CELL_SMS_PROVIDER = 'DATATYPE_CELL_SMS_PROVIDER';
    const DATATYPE_MORTGAGE_INVESTOR_OWNER = 'DATATYPE_MORTGAGE_INVESTOR_OWNER';
    const DATATYPE_HMDA_LOAN_TYPE_STR = 'DATATYPE_HMDA_LOAN_TYPE_STR';
    const DATATYPE_HMDA_LOAN_PURPOSE_STR = 'DATATYPE_HMDA_LOAN_PURPOSE_STR';
    const DATATYPE_MANUFACTURED_HOME_SECURED_PROPERTY_TYPE_STR = 'DATATYPE_MANUFACTURED_HOME_SECURED_PROPERTY_TYPE_STR';
    const DATATYPE_MANUFACTURED_HOME_LAND_PROPERTY_INTEREST_STR = 'DATATYPE_MANUFACTURED_HOME_LAND_PROPERTY_INTEREST_STR';
    const DATATYPE_PREAPPROVAL_STR = 'DATATYPE_PREAPPROVAL_STR';
    const DATATYPE_CONSTRUCTION_METHOD_STR = 'DATATYPE_CONSTRUCTION_METHOD_STR';
    const DATATYPE_APPLICATION_CHANNEL_STR = 'DATATYPE_APPLICATION_CHANNEL_STR';
    const DATATYPE_INITIALLY_PAYABLE_TO_YOUR_INSTITUTION_STR = 'DATATYPE_INITIALLY_PAYABLE_TO_YOUR_INSTITUTION_STR';
    const DATATYPE_HOEPA_STATUS_STR = 'DATATYPE_HOEPA_STATUS_STR';
    const DATATYPE_AUTOMATED_UNDERWRITING_SYSTEM_STR = 'DATAYPE_AUTOMATED_UNDERWRITING_SYSTEM_STR';
    const DATATYPE_AUTOMATED_UNDERWRITING_SYSTEM_RESULT_STR = 'DATAYPE_AUTOMATED_UNDERWRITING_SYSTEM_RESULT_STR';
    const DATATYPE_REVERSE_MORTGAGE_STR = 'DATATYPE_REVERSE_MORTGAGE_STR';
    const DATATYPE_OPEN_END_LINE_OF_CREDIT_STR = 'DATATYPE_OPEN_END_LINE_OF_CREDIT_STR';
    const DATATYPE_BUSINESS_OR_COMMERCIAL_PURPOSE_STR = 'DATATYPE_BUSINESS_OR_COMMERCIAL_PURPOSE_STR';
    const DATATYPE_BALLOON_PAYMENT_STR = 'DATATYPE_BALLOON_PAYMENT_STR';
    const DATATYPE_ARE_THERE_INTEREST_ONLY_PAYMENT_STR = 'DATATYPE_ARE_THERE_INTEREST_ONLY_PAYMENT_STR';
    const DATATYPE_NEGATIVE_AMORTIZATION_STR = 'DATATYPE_NEGATIVE_AMORTIZATION_STR';
    const DATATYPE_OTHER_NON_AMORTIZING_FEATURES_STR = 'DATATYPE_OTHER_NON_AMORTIZING_FEATURES_STR';
    const DATATYPE_DEMOGRAPHIC_INFO_PROVIDED_BY = 'DATATYPE_DEMOGRAPHIC_INFO_PROVIDED_BY';

    const DATATYPE_FUTURE_PROPERTY_TYPE_STR = 'DATATYPE_FUTURE_PROPERTY_TYPE_STR';

    private static ?self $instance = null;
    private static ?array $stateCodeArray = null;

    public static bool $useLegacy = false;

    public function __construct()
    {
        parent::__construct();
    }

    private static function getStates()
    {
        $stateArray = Arrays::fetchStates();
        foreach ($stateArray as $state) {
            self::$stateCodeArray[trim($state['stateName'])] = trim($state['stateCode']);
        }
    }

    public static function applyArray(
        ?array $values,
        string $var,
        string &$sql,
        array  &$params
    )
    {
        if (!$values || !sizeof($values)) {
            $params[$var] = null;
            return;
        }

        $values = array_values(array_filter($values));

        if (!sizeof($values)) {
            $params[$var] = null;
            return;
        }

        $params[$var] = sizeof($values);

        foreach ($values as $i => $v) {
            $params[$var . $i] = $v;
        }

        $sql = str_replace('\'--' . $var . '--\'', Database2::GetPlaceholders(sizeof($values), ':' . $var, true), $sql);
    }

    public static function strongTypeValue(
        $value,
        ?string $type,
        ?string $dobFormat = null,
        ?string &$error = null
    ) // don't set the return type, it returns floats, ints and strings
    {
        $orig = $value;
        switch ($type) {
            case self::DATATYPE_DEMOGRAPHIC_INFO_PROVIDED_BY:
                switch(intval($value)) {
                    case 1:
                        return 'Fax or Mail';
                    case 2:
                        return 'Phone';
                    case 3:
                        return 'In Person';
                    case 4:
                        return 'Email or Internet';
                }
                return $value;

            case tblCustomFieldType::excludeFromHMDAReport:
                return $value == 'Yes' ? $value : 'No';
            case tblCustomFieldType::applicantConfirmed:
                return $value ? 'Checked' : 'Not Checked';
            case tblCustomFieldType::bDemoInfo:
                return BaseHTML::getBorrowerInfoByIndex('DemoInfo', $value);
            case tblCustomFieldType::cbDemoInfo:
                return BaseHTML::getCoBorrowerInfoByIndex('DemoInfo', $value);
            case tblCustomFieldType::borrowerCreditScoringModel:
            case tblCustomFieldType::coBorrowerCreditScoringModel:
                return HMDAActionTaken::$creditScoringModel[$value];
            case tblCustomFieldType::loanTermNumeric:
                if ($value) {
                    return explode(' ', $value, 2)[0];
                } else {
                    return '';
                }
            case tblCustomFieldType::loanTermText:
                if ($value) {
                    return explode(' ', $value, 2)[1];
                } else {
                    return '';
                }
            case self::DATATYPE_MORTGAGE_INVESTOR_OWNER:
                return glMortgageInvestorOwnerArray::$glMortgageInvestorOwnerArray[$value] ?? $value;

            case self::DATATYPE_CELL_SMS_PROVIDER:
                $parts = explode('::', $value);
                $phone = Strings::numberOnly($parts[0] ?? '');
                $sms = SMSServiceProviderDomainArray::$SMSServiceProviderDomainArray[$parts[1] ?? 0] ?? '';
                return $phone . '@' . $sms;

            case self::DATATYPE_PAYMENT_FREQUENCY:
                return glpaymentFrequency::$glpaymentFrequency[$value] ?? $value;

            case self::DATATYPE_INSURANCE_TYPE:
                return $value; // TODO: get from DB

            case self::DATATYPE_VETERAN:
                return BaseHTML::getBorrowerInfoByIndex('veteran', $value);

            case self::DATATYPE_GENDER:
                return BaseHTML::getBorrowerInfoByIndex('sex', $value);

            case self::DATATYPE_RACE_SUB:
                return BaseHTML::getBorrowerInfoByIndex('FIRaceSub', $value);

            case self::DATATYPE_RACE:
                return BaseHTML::getBorrowerInfoByIndex('race', $value);

            case self::DATATYPE_ETHNICITY_SUB:
                return BaseHTML::getBorrowerInfoByIndex('FIEthnicitySub', $value);

            case self::DATATYPE_FURNISH_THIS_INFO_STR:
                return BaseHTML::getBorrowerInfoByIndex('furnishThisInformation', $value);

            case self::DATATYPE_ETHNICITY:
                $list = [];
                $ethnicityArray = explode(',',$value) ?? [];
                foreach ($ethnicityArray as $value){
                    $list[] = BaseHTML::getBorrowerInfoByIndex('ethnicity', $value);
                }
                $list = array_unique($list);
                return implode(', ',$list);
            case self::DATATYPE_REASON_FOR_DENIAL_STR:
                $reasonForDenialArray = explode('~', $value) ?? [];
                $list = [];
                foreach ($reasonForDenialArray as $value) {
                    $list[] = HMDAActionTaken::$reasonForDenial[$value] ?? '';
                }
                $list = array_unique($list);
                return implode(', ', $list);

            case self::DATATYPE_TYPE_OF_PURCHASER_STR:
                return HMDAActionTaken::$typeOfPurchaser[$value] ?? '';

            case self::DATATYPE_ACTION_TAKEN_STR:
                return HMDAActionTaken::$getHMDAActionTaken[$value] ?? '';

            case self::DATATYPE_HMDA_LOAN_TYPE_STR:
                return HMDAActionTaken::$HMDALoanType[$value] ?? '';

            case self::DATATYPE_HMDA_LOAN_PURPOSE_STR:
                return HMDAActionTaken::$HMDALoanPurpose[$value] ?? '';

            case self::DATATYPE_MANUFACTURED_HOME_SECURED_PROPERTY_TYPE_STR:
                return HMDAActionTaken::$HMDAManufacturedHomeSecuredPropertyType[$value] ?? '';

            case self::DATATYPE_MANUFACTURED_HOME_LAND_PROPERTY_INTEREST_STR:
                return HMDAActionTaken::$HMDAManufacturedHomeLandPropertyInterest[$value] ?? '';

            case self::DATATYPE_PREAPPROVAL_STR:
                return HMDAActionTaken::$HMDAPreapproval[$value] ?? '';

            case self::DATATYPE_CONSTRUCTION_METHOD_STR:
                return HMDAActionTaken::$HMDAConstructionMethod[$value] ?? '';

            case self::DATATYPE_APPLICATION_CHANNEL_STR:
                return HMDAActionTaken::$HMDAApplicationChannel[$value] ?? '';

            case self::DATATYPE_INITIALLY_PAYABLE_TO_YOUR_INSTITUTION_STR:
                return HMDAActionTaken::$HMDAInitiallyPayableToYourInstitution[$value] ?? '';

            case self::DATATYPE_HOEPA_STATUS_STR:
                return HMDAActionTaken::$HMDAHoepaStatus[$value] ?? '';

            case self::DATATYPE_AUTOMATED_UNDERWRITING_SYSTEM_STR:
                $automatedUnderwritingSystemArray = explode(',', $value) ?? [];
                $list = [];
                foreach ($automatedUnderwritingSystemArray as $value) {
                    $list[] = HMDAActionTaken::$HMDAAutomatedUnderwritingSystem[$value] ?? '';
                }
                $list = array_unique($list);
                return implode(', ', $list);

            case self::DATATYPE_AUTOMATED_UNDERWRITING_SYSTEM_RESULT_STR:
                return HMDAActionTaken::$HMDAAutomatedUnderwritingSystemResult[$value] ?? '';

            case self::DATATYPE_REVERSE_MORTGAGE_STR:
                return HMDAActionTaken::$HMDAReverseMortgage[$value] ?? '';

            case self::DATATYPE_OPEN_END_LINE_OF_CREDIT_STR:
                return HMDAActionTaken::$HMDAOpenEndLineOfCredit[$value] ?? '';

            case self::DATATYPE_BUSINESS_OR_COMMERCIAL_PURPOSE_STR:
                return HMDAActionTaken::$HMDABusinessOrCommercialPurpose[$value] ?? '';

            case self::DATATYPE_BALLOON_PAYMENT_STR:
                return HMDAActionTaken::$HMDABalloonPayment[$value] ?? '';

            case self::DATATYPE_ARE_THERE_INTEREST_ONLY_PAYMENT_STR:
                return HMDAActionTaken::$HMDAInterestOnlyPayments[$value] ?? '';

            case self::DATATYPE_NEGATIVE_AMORTIZATION_STR:
                return HMDAActionTaken::$HMDANegativeAmortization[$value] ?? '';

            case self::DATATYPE_OTHER_NON_AMORTIZING_FEATURES_STR:
                return HMDAActionTaken::$HMDAOtherNonAmortizingFeatures[$value] ?? '';

            case self::DATATYPE_SSN4:
                $value = strlen($value) > 4 ? substr($value, -4) : '';
                break;

            case self::DATATYPE_SSN:
                $value = Strings::formatSSNNumber($value, false);
                break;

            case self::DATATYPE_TIMEZONE:
                if ($value) {
                    $temp = timeZoneArray::sanitize($value);
                    if (!$temp) {
                        $error = 'Invalid Time Zone: ' . $value;
                    }
                    $value = $temp;
                }
                break;

            case self::DATATYPE_SERVICE_PROVIDER:
                if ($value) {
                    $temp = SMSServiceProviderArray::sanitize($value);
                    if (!$temp) {
                        $error = 'Invalid Service Provider: ' . $value;
                    }
                    $value = $temp;
                }
                break;

            case self::DATATYPE_METHOD_OF_CONTACT:
                if ($value) {
                    $temp = methodOfContactArray::sanitize($value);
                    if (!$temp) {
                        $error = 'Invalid Method of Contact: ' . $value;
                    }
                    $value = $temp;
                }
                break;

            case self::DATATYPE_PROPERTY_TYPE_NUMBER:
                $temp = null;
                if ($value === 'Single Family') {
                    $value = 'Single Family Home';
                }

                foreach (GpropertyTypeNumbArray::$GpropertyTypeNumbArray as $k => $v) {
                    if ($value == $k || strcasecmp($value, $v) == 0) {
                        $temp = $k;
                        break;
                    }
                }
                $value = $temp;
                if ($orig && !$temp) {
                    if (!is_null($error)) {
                        $error = 'Invalid Property Type: ' . $orig;
                    }
                }
                break;

            case self::DATATYPE_FUTURE_PROPERTY_TYPE_STR:
                return Property::$propertyFuturePropertyType[$value] ?? '';

            case self::DATATYPE_DATE_STANDARD:
                $value = Dates::StandardDate($value, '');
                break;

            case self::DATATYPE_DATE:
                if ($value) {
                    if (Dates::IsEmpty($value)) {
                        $value =  null;
                    } else {
                        if (trim($dobFormat) == '') {
                            $org = $value;
                            if (preg_match('/\d{4}-\d{2}-\d{2}/', $value)) {
                                $value = Dates::validateDateWithRE($value, 'YYYY-MM-DD');
                            } else {
                                $value = Dates::validateDateWithRE($value, 'MM/DD/YYYY');
                            }
                        } else {
                            $value = Dates::validateDateWithRE($value, $dobFormat);
                        }
                        if (trim($value) == '') {
                            $value = Dates::validateDateWithRE($value, 'MM/DD/YY');
                        }
                    }
                    if (!$value) {
                        if (!is_null($error)) {
                            $error = 'Invalid Date Format: ' . $value;
                        }
                        $value = null;
                    }
                } else {
                    $value = null;
                }
                break;
            case self::DATATYPE_ZIP:
                $value = substr(trim($value), 0, 10);
                if ($orig && !$value) {
                    if (!is_null($error)) {
                        $error = 'Invalid Zip Code: ' . $value;
                    }
                }
                break;
            case self::DATATYPE_NUMBER:
                $value = intval(Strings::numberOnly($value));
                if ($orig && !$value) {
                    if (!is_null($error)) {
                        $error = 'Invalid Number: ' . $value;
                    }
                }
                break;
            case self::DATATYPE_MONEY:
                $value = Strings::replaceCommaValues($value);
                if ($orig && !$value) {
                    if (!is_null($error)) {
                        $error = 'Invalid Decimal Value: ' . $value;
                    }
                }
                break;

            case self::DATATYPE_PERCENT_STR:
                $value = Strings::replaceCommaValues($value);
                if ($orig && !$value) {
                    if (!is_null($error)) {
                        $error = 'Invalid Decimal Value: ' . $value;
                    }
                }
                $value = number_format($value, 3);
                break;

            case self::DATATYPE_MONEY_STR:
                $value = Strings::replaceCommaValues($value);
                if ($orig && !$value) {
                    if (!is_null($error)) {
                        $error = 'Invalid Decimal Value: ' . $value;
                    }
                }
                $value = number_format($value, 2);
                break;

            case self::DATATYPE_STATE:
                if (!self::$stateCodeArray) {
                    self::getStates();
                }
                if (strlen($value) == 2) {
                    $value = strtoupper($value);
                } else if (array_key_exists($value, self::$stateCodeArray)) {
                    $value = strtoupper(self::$stateCodeArray[$value]);
                } else {
                    $value = '';
                }
                if ($orig && !$value) {
                    if (!is_null($error)) {
                        $error = 'Invalid State Value: ' . $value;
                    }
                }
                break;
            case self::DATATYPE_EMAIL:
                if ($value) {
                    $origValue = trim(Strings::stripQuote($value));
                    if (!is_null($error) && !Integers::check_email_address($value)) {
                        $error = 'Invalid Email Address: ' . $origValue;
                    }
                }
                break;
            case self::DATATYPE_PHONE_NUMBER:
                $value = Strings::formatPhoneNumber($value);
                break;
            case self::DATATYPE_MULTI_SELECT:
                $value = implode(',', $value);
                break;
            case self::DATATYPE_STRING:
            default:
                if (is_array($value)) {
                    Debug($value);
                }
                $value = trim($value);
        }
        return $value;
    }

    /**
     * @param int $count
     * @param string $str
     * @param bool $use_increment
     * @return string
     */
    public static function GetPlaceholders(
        int    $count,
        string $str,
        bool   $use_increment
    ): string
    {
        if (!$use_increment) {
            return implode(', ', array_fill(0, $count, $str));
        }
        $list = [];
        for ($j = 0; $j < $count; $j++) {
            $list[] = $str . $j;
        }
        return implode(', ', $list);
    }

    private static array $log = [];
    private static float $total_time = 0;
    private static int $total_queries = 0;

    public static function getQueryLog(): array
    {
        return self::$log;
    }

    public static function resetLogQuery()
    {
        self::$log = [];
        self::$total_time = 0;
        self::$total_queries = 0;
    }

    /**
     *
     */
    public static function saveLogQuery()
    {
        // do not use tblFileStorage for this

        if (!CONST_LOG_QUERIES) {
            return;
        }

        $diskSpace = diskSpace::getSpace();
        if ($diskSpace->isFull()) {
            return;
        }

        $path = str_replace('/', '_', $_SERVER['SCRIPT_NAME'] ?? 'na');

        if (!is_dir(__DIR__ . '/../logs')) {
            mkdir(__DIR__ . '/../logs');
        }
        $json = json_encode(
            [
                'log'     => self::$log,
                'queries' => self::$total_queries,
                'time'    => self::$total_time,
            ],
            JSON_PRETTY_PRINT
        );
        $json = str_replace('\n', ' ', $json);
        $json = str_replace('\r', ' ', $json);
        $j = 1;
        $filename = __DIR__ . '/../logs/' . $path . '.' . GUID . '.json';
        while (file_exists($filename)) {
            $j++;
            $filename = __DIR__ . '/../logs/' . $path . '.' . GUID . '.' . $j . '.json';
        }
        file_put_contents(
            $filename,
            $json,
            FILE_APPEND
        );
    }

    private static function checkInvalidNumeric(string $sql, ?array $params, $param)
    {
        if (!defined('DATA_VALIDATION') || !DATA_VALIDATION) {
            return;
        }

        if (!preg_match('/\b' . preg_quote($param) . '\b/i', $sql)) {
            return; // parameter not in query
        }

        if (!isset($params[$param])) {
            // we have two cases
            // 1. we didn't do a proper parameterized query, so we need to report it
            // 2. we're doing an insert into select from statement, so it wouldn't be in the parameter list
            if (stristr($sql, 'INSERT INTO') !== false) {
                if (stristr($sql, 'SELECT') !== false) {
                    return;
                }
            }
        }

        if (is_null($params[$param])) {
            return; // null is fine
        }

        if ($params[$param] && is_numeric($params[$param])) {
            return;
        }

        if (
            $params[$param] != '0'
            && $params[$param] != '0.00'
            && $params[$param] != '0.0'
        ) {
            ksort($params);
            Debug('checkInvalidNumeric: ' . $param, $sql, $params);
        }
    }

    private static array $numericFields = [
        '_tblTriggerChangeLog'                             => [
            'primary_id'
        ],
        'contingentLiabilities'                            => [
            'typeOfLiability',
            'monthlyPayment',
            'clBalance',
            'created_by'
        ],
        'emailNotification'                                => [
            'tls',
            'attempt'
        ],
        'estimatedProjectCost'                             => [
            'branchId',
            'clientId',
            'UID',
            'publicUser',
            'created_by'
        ],
        'fixpayment'                                       => [
            'firstModPaymentAmt'
        ],
        'importJCAP'                                       => [
            'loanAmount',
            'interestRate',
            'propertyType',
            'lien1',
            'initialDraw',
            'constructionBudget',
            'arv'
        ],
        'purchaseInfo'                                     => [
            'purchaserZip'
        ],
        'tblACHInfo_h'                                     => [
            'checkNo',
            'bankZip'
        ],
        'tblAddlnLienContactInfo'                          => [
            'addLienPhone',
            'addLienFax'
        ],
        'tblAdminUsers'                                    => [
            'attorneyRate',
            'serviceFeeRate',
            //            'allowToupdateFileAndClient', -- comma separated
            'TwoFAStatus'
        ],
        'tblAdverseAction'                                 => [
            'HMDAActionTaken',
            'agencyPhone',
            'agencyScoreRangeFrom',
            'agencyScoreRangeTo',
            'agencyInquiries',
            'daysForCondition',
            'lenderLetterPhone',
            'userNumber'
        ],
        'tblAgent'                                         => [
            'planType',
            'noOfMortgageBropkers',
            //            'surveyCompDesc', -- comma separated
            // 'surveyPrefPerson', -- comma separated
            // 'significantPerson', -- comma separated
            // 'surveyAEProb', -- comma separated
            // 'surveyOnlineTool', -- comma separated
            // 'surveyMonthlyLoan', -- comma separated
            //            'allowToupdateFileAndClient', -- comma separated
            'TwoFAStatus'
        ],
        'tblAssetsInfo'                                    => [
            'assetTotalRetirementValue',
            'assetAvailabilityLinesCredit',
            'assetAccount',
            'assetAccountOwd',
            'assetNonMarketableSecurities',
            'assetNonMarketableSecuritiesOwd',
            'assetStocksOwed',
            'assetIRAAccountsOwed',
            'assetESPOAccountsOwed',
            'assetLifeInsuranceOwed',
            'assetAvailabilityLinesCreditOwed',
            'notesPayableToBanksOthersOwed',
            'installmentAccountOwed',
            'revolvingDebtOwed',
            'unpaidPayableTaxesOwed',
            'otherLiabilitiesOwed'
        ],
        'tblAssetsInfo_h'                                  => [
            'assetSavingMoneyMarket',
            'assetStocks',
            'assetESPOAccounts',
            'assetHome',
            'assetCars',
            'assetLifeInsurance',
            'assetOther',
            'assetCash',
            'assetOREOwed',
            'otherAmtOwed',
            'assetTotalRetirementValue',
            'assetAvailabilityLinesCredit',
            'assetSR',
            'assetAccount',
            'assetAccountOwd',
            'assetNonMarketableSecurities',
            'assetNonMarketableSecuritiesOwd',
            'assetStocksOwed',
            'assetIRAAccountsOwed',
            'assetESPOAccountsOwed',
            'assetLifeInsuranceOwed',
            'assetAvailabilityLinesCreditOwed',
            'notesPayableToBanksOthersOwed',
            'installmentAccountOwed',
            'revolvingDebtOwed',
            'unpaidPayableTaxesOwed',
            'otherLiabilitiesOwed'
        ],
        'tblAutomatedEmail'                                => [
            'emailFromEmail',
            'userNumber',
            'createdBy'
        ],
        'tblAutomatedEmailAutoDocs'                        => [
            'pkgId',
            'eSign',
            'createdBy'
        ],
        'tblAutomatedEmailDocs'                            => [
            'createdBy'
        ],
        'tblAutomatedEmailStatus'                          => [
            'updatedBy'
        ],
        'tblAutomatedEmailUser'                            => [
            'userId'
        ],
        'tblAutomatedRuleActionLoanFile'                   => [
            'LMRId',
            'createdBy'
        ],
        'tblAutomatedRules'                                => [
            'userNumber',
            'createdBy'
        ],
        'tblAutomatedRulesConditions'                      => [
            'params3',
            'createdBy'
        ],
        'tblAutomatedRulesEvents'                          => [
            'createdBy'
        ],
        'tblAutomatedTask'                                 => [
            'taskFromEmail',
            'userNumber',
            'createdBy'
        ],
        'tblAutomatedTaskAutoDocs'                         => [
            'pkgId',
            'eSign',
            'createdBy'
        ],
        'tblAutomatedTaskDocs'                             => [
            'createdBy'
        ],
        'tblAutomatedTaskStatus'                           => [
            'updatedBy'
        ],
        'tblAutomatedTaskUser'                             => [
            'userId'
        ],
        'tblAutomatedWebhookStatus'                        => [
            'updatedBy'
        ],
        'tblBoundDocs'                                     => [
            'docID'
        ],
        'tblBranch'                                        => [
            'forensicFee',
            'attorneyProcessingFee',
            'rescissionPhNo',
            'packageId',
            'brokerProcessingFee',
            'loanModificationFee',
            'globalAcctNo',
            'defaultPrimaryStatus',
            'defaultPrimaryStatusForFA',
            //            'allowToupdateFileAndClient',
            'TwoFAStatus'
        ],
        'tblBrokerSubscription'                            => [
            'transactionID',
            'ARBSubscriptionID'
        ],
        'tblClient'                                        => [
            'altPhoneNumber',
            'FIEthnicitySub',
            'FIRaceSub',
            'DemoInfo'
        ],
        'tblClientAssetsInfo'                              => [
            'assetTotalRetirementValue',
            'vestedInterest',
            'otherAssets',
            'assetAccount',
            'assetAccountOwd',
            'assetNonMarketableSecurities',
            'assetNonMarketableSecuritiesOwd',
            'assetStocksOwed',
            'assetIRAAccountsOwed',
            'assetESPOAccountsOwed',
            'assetLifeInsuranceOwed',
            'assetAvailabilityLinesCreditOwed',
            'notesPayableToBanksOthersOwed',
            'installmentAccountOwed',
            'revolvingDebtOwed',
            'unpaidPayableTaxesOwed',
            'otherLiabilitiesOwed'
        ],
        'tblClientExp'                                     => [
            'propertyType'
        ],
        'tblClientLOScheduleRealInfo'                      => [
            'propType',
            'presentMarketValue',
            'accountNumberAnother',
            'ownership'
        ],
        'tblClientPayments'                                => [
            'amount',
            'membership',
            'referralCode'
        ],
        'tblClient_backup'                                 => [
            'altPhoneNumber'
        ],
        'tblCreditMemo'                                    => [
            'LMRId',
            'publicUser',
            'memoCategory',
            'created_by'
        ],
        'tblFaxServerInfo'                                 => [
            'eFaxCompany'
        ],
        'tblFile'                                          => [
            'borrowerLoanRate',
            //            'negotiatorPhoneNumber'
        ],
        'tblFile2'                                         => [
            'chQ1',
            'chQ2'
        ],
        'tblFile3'                                         => [
            'cashOutAmount',
            'appointmentDay_del',
            'appointmentYear_del',
            'creditRating'
        ],
        'tblFileBrokers'                                   => [
            'brokerPhone2'
        ],
        'tblFileCFPB'                                      => [
            'additionalServicer',
            'estimatedSettlementAmount',
            'awardedSettlement'
        ],
        'tblFileCourtInfo'                                 => [
            'counselAttorneyNum'
        ],
        'tblFileDealAnalysis'                              => [
            'realtorCommissions'
        ],
        'tblFileExpFilpGroundUp'                           => [
            'propertyType'
        ],
        'tblFileFUBusinessEntity'                          => [
            'entityPhone',
            'member2Zip',
            'member3Zip'
        ],
        'tblFileFUCreditEnhancement'                       => [
            'previousZip2'
        ],
        'tblFileFunding'                                   => [
            'amtDesired'
        ],
        'tblFileHMLONewLoanInfo'                           => [
            'brokerQuotedinterestRate',
            'cityCountyTaxStamps',
            'paymentFrequency'
        ],
        'tblFileHMLOPropInfo'                              => [
            'approvedLoanAmt',
            //            'proInsType', -- comma separated values
            //            'reqValuationMethod'  -- comma separated values
        ],
        'tblFileHUDSettlementCharges'                      => [
            'fieldID',
            'borrowerSettlementValue'
        ],
        'tblFileHUDTransaction'                            => [
            'fieldID'
        ],
        'tblFileLOPropertyInfo'                            => [
            'originalCost',
            'amtExistLiens',
            'presentValOfLot',
            'costOfImprovements'
        ],
        'tblFileLOScheduleRealInfo'                        => [
            'propType'
        ],
        'tblFileLOScheduleRealInfo_h'                      => [
            'propType',
            'presentMarketValue'
        ],
        'tblFileLoanAudit'                                 => [
            'fraudLevel',
            'price'
        ],
        'tblFileLoanAuditH'                                => [
            'fraudLevel',
            'price'
        ],
        'tblFileLoanServicing_h'                           => [
            'paymentDelinquent'
        ],
        'tblFileMFLoanTerms'                               => [
            'PRBalanceMonths'
        ],
        'tblFilePayee'                                     => [
            'monthlyMaintDonation'
        ],
        'tblFilePropertyInfo2'                             => [
            'principalResMortPaid'
        ],
        'tblFileRAMClientInfo'                             => [
            'vendorID',
            'ACHStatus'
        ],
        'tblFileRAMClientInfo_h'                           => [
            'vendorID',
            'ACHStatus'
        ],
        'tblFileRAMVendorInfo'                             => [
            'vendorID'
        ],
        'tblFileServicingHistory'                          => [
            'value',
            'createdBy'
        ],
        'tblFileVendorClient'                              => [
            'vendorID'
        ],
        'tblFile_h'                                        => [
            'lien1Rate',
            'lien2Rate',
            'homeValue',
            'phoneNumber',
            'coBPhoneNumber',
            'coBCellNumber',
            'coBFax',
            'mailingAddressAsProp',
            'propertyType',
            'workNumber',
            'mailingAddressAsBorrower',
            'lien1OriginalBalance',
            'previousAddrAsMailing',
            'howManyBedRoom',
            'howManyBathRoom',
            'howManyHalfBathRoom',
            'basementHome',
            'basementFinish',
            'garageHome'
        ],
        'tblGlobalIntegrity'                               => [
            'loanAmount',
            'len1Rate'
        ],
        'tblHMLOBlanketLoanOtherProps'                     => [
            'propValue',
            'propAnnualInsPremium',
            'annualPropTaxes',
            'stabilizedRate',
            'zillowValue',
            'waterFront',
            'basementHome',
            'garageHome',
            'propAnnualInsurancePremiums',
            'estimatedPropertyValue',
            'basementFinish',
            'addNoOfStories',
            'pastDuePropertyTaxes',
            'msa'
        ],
        'tblHtmlPdfLMRFileDocs'                            => [
            'uniqueJobId'
        ],
        'tblIncomeInfo'                                    => [
            'primaryMortgage1'
        ],
        'tblInsuranceDetails'                              => [
//            'policyType', // comma separated values
            'policyCoverage'
        ],
        'tblLMRHUDAdditionalCharges'                       => [
//            'fieldID'
        ],
        'tblLMRHUDItemsPayableLoan'                        => [
//            'fieldID',
            'sellerSettlementValue'
        ],
        'tblLMRHUDLenderToPay'                             => [
//            'fieldID',
            'perDay',
            'borrowerSettlementValue'
        ],
        'tblLMRHUDReservesDeposit'                         => [
//            'fieldID',
            'lenderAmount',
            'sellerSettlementValue'
        ],
        'tblLedger'                                        => [
            'credit_source_id',
            'debit_source_id'
        ],
        'tblLenderContacts'                                => [
            'CPCellNumber'
        ],
        'tblLenderLoanType'                                => [
            'ageOfBusiness'
        ],
        'tblLenderNOEInfo'                                 => [
            'NOE_zipCode'
        ],
        'tblLenderSSInfo'                                  => [
            'SS_fax',
            'SS_zipCode'
        ],
        'tblListingPageData'                               => [
            'pointOfContactId'
        ],
        //        'tblLoanOriginatorInfo' => [
        //            'loOriginatorPhone'
        //        ],
        'tblMailQueueAutomation'                           => [
            'cancelledBy'
        ],
        'tblMembersOfficers'                               => [
            'memberAnnualSalary'
        ],
        'tblModPostSurvey'                                 => [
            'clientChargesLow',
            'modificationCost'
        ],
        'tblNVAEmployee'                                   => [
            'hourlyRate',
            'paralegalRate',
            'officePhone',
            'cellPhone',
            'faxNo'
        ],
        'tblOfferDocs'                                     => [
            'LMRId',
            'PCID'
        ],
        'tblOffers'                                        => [
            'LMRId',
            'PCID',
            'offerLoanAmount',
            'createdBy',
            'isPublicWebForm'
        ],
        'tblPCChecklistPropertyType'                       => [
            'propertyType'
        ],
        'tblPCClientEntityInfo'                            => [
            'valueOfProperty',
            'totalDebtOnProperty',
            'grossIncomeLastYear',
            'netIncomeLastYear',
            'grossIncome2YearsAgo',
            'netIncome2YearsAgo',
            'averageBankBalance',
            'noOfEmployeesAfterLoan'
        ],
        'tblPCHMLOBasicLoanInfo'                           => [
            'marketAnnualGrossSales',
            'marketAverageBankBalance',
            'marketAvgTotalMonthlySale'
        ],
        'tblPCHMLOBasicLoanMinSeasoningBusinessBankruptcy' => [
            'MinSeasoningBusinessBankruptcyVal'
        ],
        'tblPCHMLOBasicLoanMinSeasoningForeclosure'        => [
            'MinSeasoningForeclosureVal'
        ],
        'tblPCHMLOBasicLoanMinSeasoningPersonalBankruptcy' => [
            'MinSeasoningPersonalBankruptcyVal'
        ],
        'tblPCHMLOBasicLoanMinTimeInBusiness'              => [
            'minTimeVal'
        ],
        'tblPCHMLOBasicLoanPropertyType'                   => [
            'propertyType'
        ],
        'tblPCHMLOBasicLoanSBALoanProduct'                 => [
            'SBALoanProductVal'
        ],
        'tblPCRAMAffiliateInfo'                            => [
            'affiliateID'
        ],
        'tblPCRAMAttorneyInfo'                             => [
            'feeGroup',
            'RAMAttorneyID'
        ],
        'tblPCRAMFeeGroupInfo'                             => [
            'retainerPCFee',
            'retainerAttorneyFee',
            'retainerAffiliateFee',
            'recurringPCFee',
            'recurringAttorneyFee',
            'recurringAffiliateFee'
        ],
        'tblPCWorkflowEvent'                               => [
            'fromEmail'
        ],
        'tblPCWorkflowStepsPropertyType'                   => [
            'propertyType'
        ],
        'tblPeerStreet'                                    => [
            'as_is_valuation',
            'b_piece_amount',
            'construction_reserve_at_origination',
            'construction_reserve_disbursed_at_valuation',
            'extension_term',
            'interest_rate',
            'interest_reserve_at_origination',
            'junior_liens_total_amount',
            'lender_spread',
            'lender_origination_points',
            'other_reserve_at_origination',
            'arv',
            'pari_passu_amount',
            'peerstreet_origination_points',
            'projected_rehab_budget',
            'total_principal_balance_at_origination',
            'total_purchase_price',
            'fico_score',
            'third_party_origination_points',
            'total_principal_payments',
            'ps_res_status'
        ],
        'tblPrincipalResidenceInfo'                        => [
            'lien1PIPayment',
            'lien2PIPayment',
            'monthlyInsurance',
            'floodInsurance',
            'HOAFees',
            'monthlyTaxes',
            'agentPhonenumber',
            'amountOffer'
        ],
        'tblProcessingCompany'                             => [
            'attorneyFascimile',
            'privateLabeled',
            //            'servicerPhone',
            //            'payoffPhoneNo'
        ],
        'tblPropertyManagement'                            => [
            'LMRId',
            'branchId',
            'agentId',
            'clientId',
            'UID',
            'publicUser',
            'created_by'
        ],
        'tblPropertyManagementDocs'                        => [
            'LMRId'
        ],
        'tblProposalInfo'                                  => [
            'lien1ProposalDTI',
            'lien1ProposalHomeValue',
            //            'lien2LenderPhoneNo',
            'proposalGrossIncome',
            'proposalNetMonthlyIncome',
            'nonMtgProposalTotalHouseHoldExpenses',
            'lien1EscrowShortage',
            'lien1FeesAdminCosts',
            'lien2FeesAdminCosts',
            'lien2ProposalFeesAdminCosts',
            'totalHousingTaxesInsurance',
            'proposalTotalHousingTaxesInsurance',
            'newHAMPT1DTI'
        ],
        'tblQAInfo'                                        => [
            'condominiumOrHOAFee',
            'isHOAOrCOAFee',
//            'bFiEthnicitySub',
//            'bFiRaceSub',
            'bDemoInfo',
            'HOAFeePaidCurrent',
            'occupiedBy',
            'loanModType',
            'delinquentInsuranceAmount',
            'isTaxReturn',
            'CBVeteran',
            'CBDDemoInfo',
//            'CBEthnicitySub',
//            'CBRaceSub'
        ],
        'tblRecentSalesInfo'                               => [
            'listPrice'
        ],
        'tblRestInfo'                                      => [
            'InvestorRiskPremium',
            'PMMS',
            'originalInterestRate',
            'maxMonthsPastDue',
            'delinquentFees',
            'delinquentInterest',
            'restLien1Rate',
            'restFloodInsurance1',
            'restTaxes1',
            'restCreditCardsBalance1',
            'restGrossIncome',
            'restHomeValue',
            'CaseFileId',
            'priorModPI',
            'grossMonthlyRental',
            'primaryResidenceHousingExpense'
        ],
        'tblSSComparables'                                 => [
            'bedrooms',
            'sqft'
        ],
        'tblSSProposalInfo'                                => [
            'SS_realEstateCommissions',
            'SS_REOArrearsMonths',
            'SS_attorneyFees',
            'SS_securePropertyCost',
            'SS_maintenanceCost',
            'SS_lien1EscrowShortage',
            'SS_lien1FeesAdminCosts',
            'SS_lien2FeesAdminCosts',
            'SS_CureRate',
            'SS_HomePriceForecastedDepreciation',
            'SS_REOStigmaDiscount',
            'SS_PIAEF'
        ],
        'tblSSRegnInfo'                                    => [
            'listingDollarAmount'
        ],
        'tblSbaOtherBusiness'                              => [
            'created_by'
        ],
        'tblSchools'                                       => [
            'zipCode'
        ],
        'tblShortSale'                                     => [
            'unPaidTax',
            'liens',
            'BPO2Value',
            'BPO3Value',
            'sqft2',
            'offer3',
            'sqft3',
            'timeOnMkt',
            'bedrooms',
            'bath',
            'sqft',
            //            'attorneyFax',
            'rehabValue2',
            //            'buyer1Phone2',
            //            'buyer2Phone2',
            'attorneyFee',
            'twelveMonthRent',
            'eighteenMonthRent',
            'quickSaleValue',
            //            'appraiser1Phone',
            //            'appraiser2Phone',
            //            'realtor1Phone',
            //            'realtor2Phone',
            //            'realtor3Phone',
            'twelveMonthRent1',
            'twelveMonthRent2',
            'AVM1Value',
            'AVM2Value',
            'AVM3Value'
        ],
        'tblShortSale2'                                    => [
            'transPhone1',
            'transPhone2',
            'transAttorneyFee',
            'buyerAttorneyPhone1',
            'BPOPhone1',
            'contractPrice',
            'relocationPayment',
            'appraiser1MonthlyRent',
            'appraiser2MonthlyRent',
            'AVM1MonthlyRent',
            'AVM2MonthlyRent',
            'AVM3MonthlyRent'
        ],
        'tblStudentLoanInfo'                               => [
            'loanRate',
            'loanEstimatedTerm'
        ],
        'tblStudentLoanQAInfo'                             => [
            'garnishmentsAmount',
            'jobLength',
            'privateDebtAmt',
            'studentDebtAmt',
            'spouseWorkPhoneNumber',
            'empPhoneNumber',
            'spouseAnnualIncome'
        ],
        'tblTMPSubscription'                               => [
            'ARBSubscriptionID'
        ],
        'tblTempClientUpdated'                             => [
            'zipCode',
            'homeNumber',
            'workNumber',
            'cellNumber',
            'alternateNumber'
        ],
        'tblTempFinancialInfoUpdated'                      => [
            'BorGrossMonthlyIncome',
            'SpouseGrossIncome',
            'BonusIncome',
            'ChildSupportIncome',
            'DisabilityIncome',
            'RetirementIncome',
            'RentalIncome',
            'OtherIncome',
            'OtherIncome2',
            'AssetsCheckingAccount',
            'AssetsSavingsAccount',
            'AssetsOther',
            'AssetsCash',
            '1stlienTotalPayment',
            '1stlienPropertyTax',
            '1stlienHomeInsurance',
            '1stlienHOA',
            'ExpOtherMortgages',
            'ExpAutoLoans',
            'CarExpenses',
            'Electricity',
            'NaturalGas',
            'WaterSewage',
            'UtlitiesOther',
            'UtilitiesTelephone',
            'Cable',
            'CreditCards',
            'UnsecuredLoans',
            'OtherLoans',
            'Insurancehealth',
            'ChurchClubdonations',
            'DoctorMedicalBills',
            'PrescriptionsDoctorMedicalBills',
            'ChildDependentCare',
            'ChildSupportAlimony',
            'StudentLoans',
            'Groceries',
            'OtherExpenses'
        ],
        'tblTempHardshipUpdated'                           => [
            'dependents',
            'totalOccupants'
        ],
        'tblTempPaymentInfoUpdated'                        => [
            'CheckingACTNumber',
            'ACTHoldersZip',
            'ExpDateYear',
            'Phase1TotalOwed',
            'Phase2TotalOwed',
            'Phase3TotalOwed',
            'Phase4TotalOwed'
        ],
        'tblTempPersonalInfoUpdated'                       => [
            'BorHomeNumber',
            'BorCellNumber',
            'BorWorkNumber',
            'MailingZip',
            'EmploymentPhone',
            'CoHomeNumber',
            'CoCellNumber',
            'CoWorkNumber',
            'CoEmploymentLength'
        ],
        'tblTempPropertyLenderUpdated'                     => [
            'homeValue',
            '1stlienoriginalloanamount',
            'bankruptcyFilingDate',
            '1stlienMonthlyPayment',
            '1stlienCurrentUnpaidPrincipalBalance',
            '1stlienRate',
            '1stlienRemainingMonths',
            '2ndlienloannumber',
            '2ndlienPayment',
            '2ndlienCurrentUnpaidBalance',
            '2ndlienmonthsbehind'
        ],
        'tblThirdPartyService'                             => [
            'vendorOrderIdentifier'
        ],
        'twoFactorAuthenticationLog'                       => [
            'errorCode'
        ]
    ];


    /**
     * @param string $sql
     * @param array|null $params
     * @param float|null $seconds
     * @param bool $insertUpdate
     * @param int $affected_rows
     */
    private static function logQuery(
        string $sql,
        ?array $params = null,
        float  $seconds = null,
        bool   $insertUpdate = false,
        int    $affected_rows = 0
    )
    {
        if ($insertUpdate
            && stristr($sql, 'change_log') === false
            && stristr($sql, '_tblSlowQueries') === false
            && stristr($sql, '_tblTriggerChangeLog') === false
            && stristr($sql, '_tblDBErrors') === false
        ) {
//            Metrics::Toggle('Datatype Check');
            foreach (self::$numericFields as $table => $fields) {
                if (!preg_match('/\b' . preg_quote($table) . '\b/i', $sql)) {
                    continue;
                }

                foreach ($fields as $field) {
                    self::checkInvalidNumeric($sql, $params, $field);
                }
            }
//            Metrics::Toggle('Datatype Check');
//            Debug(Metrics::ToString());
        }

        if (SLOW_QUERY_SECONDS && $seconds >= SLOW_QUERY_SECONDS) {
            if (stristr($sql, '_tblSlowQueries') === false) { // don't get into an infinite loop if the server is overloaded
                $l = new _tblSlowQueries();
                $l->environment = CONST_ENVIRONMENT;
                $l->remoteAddr = Server::RemoteADDR() ?? 'script';
                $l->query = preg_replace('/\s+/', ' ', trim($sql));
                $l->params = json_encode($params);
                $l->errorMsg = '';
                $l->duration = $seconds;
                $l->guid = GUID;
                $l->recordDate = Dates::Timestamp();
                $l->page = substr($_SERVER['REQUEST_URI'] ?? $_SERVER['PHP_SELF'], 0, 255);
                $l->affected_rows = $affected_rows;
                $l->Save();
            }
        }

        if (!CONST_LOG_QUERIES && !SLOW_QUERY_SECONDS) {
            return;
        }

        self::$total_queries++;
        self::$total_time += $seconds;
        self::$log[] = [
            'sql'          => $sql,
            'params'       => $params,
            'exec_time'    => $seconds,
            'is_slow'      => SLOW_QUERY_SECONDS && $seconds >= SLOW_QUERY_SECONDS,
            'memory_usage' => memory_get_usage(true),
        ];
    }

    /**
     * @param $conn
     * @param string $sql
     * @param array|null $params
     * @return string
     */

    private static function escapeQuery($conn, string $sql, ?array $params = null): string
    {
        if (is_null($conn)) {
            Debug('No MySQL Connection');
        }

        if (!$params || !sizeof($params)) {
            return $sql;
        }

        $sql = APIHelper::removeSqlComment($sql);


        // allow @var syntax
        $matches = [];
        // https://stackoverflow.com/questions/20767089/preg-replace-when-not-inside-double-quotes
        preg_match_all('/[\'\"][^\"\']*[\'\"](*SKIP)(*FAIL)|[:@]([^0-9]\w+)/si', $sql . ' ', $matches);

        if (sizeof($matches[1])) {
            Strings::SortArrayByValueLength($matches[1]);

            foreach ($matches[1] as $ph) {
                $sql = str_replace(':' . $ph, '{{' . $ph . '}}', $sql);
                $sql = str_replace('@' . $ph, '{{' . $ph . '}}', $sql);
            }

            // only sort if we have named parameters - 8/16/2023
            // anonymous parameters are only allowed when using ORM objects
            $keys = array_map('strlen', array_keys($params));
            array_multisort($keys, SORT_DESC, $params);
        }


        foreach ($params as $k => $v) {
            if (is_numeric($k)) {
                continue;
            }
            if (is_null($v)) {
                $sql = str_replace('{{' . $k . '}}', 'null', $sql);
            } else {
                $sql = str_replace(':' . $k, '{{' . $k . '}}', $sql);
                $sql = str_replace('@' . $k, '{{' . $k . '}}', $sql);
            }
        }

        $count = 0;
        $returnQry = preg_replace_callback('/{{(.*?)}}/i', function ($result)
        use ($params, &$count, $conn, $sql) {
            if (isset($result[1])) {

                if (isset($params[$count])) {
                    $count++;
                    if ($result[1] !== 'nq') {
                        return '\'' . mysqli_escape_string($conn, stripslashes($params[$count - 1])) . '\'';
                    } else {
                        return $params[$count - 1]; // don't use mysqli_escape_string here because it will escape quotes which breaks things
                    }
                }

                if (isset($params[$result[1]])) {
                    if (is_array($params[$result[1]])) {
                        Debug(['Parameter cannot be array', $params]);
                    }

                    $MAX_GB = defined('MAX_MYSQL_MEMORY_GB') && MAX_MYSQL_MEMORY_GB ? MAX_MYSQL_MEMORY_GB : 1;
                    if (memory_get_usage() > $MAX_GB * 1024 * 1024 * 1024) {
//                        Log::Insert(self::$log); -- don't do this, blows up log files
                        Debug([
                            'error'            => 'excessive memory usage',
                            'MAX_GB'           => $MAX_GB,
                            'memory_get_usage' => memory_get_usage() / 1024 / 1024 / 1024,
                            'sql'              => $sql,
                            'params'           => $params,
                            'debug_backtrace'  => debug_backtrace(),
                        ]);
                        self::resetLogQuery();
                    }
                    return '\'' . mysqli_escape_string($conn, stripslashes($params[$result[1]])) . '\'';
                }

                if (NULL_ON_MISSING_PARAMETER) {
                    return null;
                }

                Debug([
                    'sql'         => $sql,
                    'params'      => $params,
                    'param value' => $params[$result[1]],
                    'result 1'    => $result[1],
                    'result 0'    => $result[0] . ' does not having a matching parameter (mysql_escape_query).'
                ]);
            }
            return null;
        }, $sql);

        if (strpos($returnQry, 'debugQueryLW') !== false) {
            echo $returnQry;
            exit;
        }
        return $returnQry;
    }

    /**
     * @param string $table
     * @param array $columns
     * @param string $where
     * @param array|null $params
     * @param null $mapping_function
     * @return array|mixed|string
     */
    public function selectRowFromTable(
        string $table,
        array  $columns,
        string $where = '1=1',
        array  $params = null,
               $mapping_function = null)
    {
        $sql = '
            SELECT
                `' . implode('`,`', $columns) . '`
            FROM
                `' . $table . '`
            WHERE
               ' . $where . '
        ';
        return $this->queryData($sql, $params, $mapping_function)[0] ?? [];
    }


    /**
     * @param string $table
     * @param array $columns
     * @param string $where
     * @param array|null $params
     * @param null $mapping_function
     * @return array|null
     */
    public function selectRowsFromTable(
        string $table,
        array  $columns,
        string $where = '1=1',
        array  $params = null,
               $mapping_function = null): ?array
    {
        $sql = '
            SELECT
                `' . implode('`,`', $columns) . '`
            FROM
                `' . $table . '`
            WHERE
               ' . $where . '
        ';
        return $this->queryData($sql, $params, $mapping_function);
    }

    /**
     * @param string $sql
     * @param array $params
     * @return array
     */
    public function debugQuery(string $sql, array $params = []): array
    {
        $conn = $this->openMyConnection();
        $qry = self::escapeQuery($conn, $sql, $params);
        return [
            'qry'    => $qry,
            'sql'    => $sql,
            'params' => $params,
        ];
    }

    /**
     * @param string $sql
     * @param array $params
     * @return string
     */
    public function safeQuery(string $sql, array $params = []): string
    {
        $conn = $this->openMyConnection();
        return self::escapeQuery($conn, $sql, $params);
    }

    /**
     * @param string $qry
     * @param array|null $params
     * @param callable|null $mapping_function
     * @param mixed|null $single
     * @param string|null $keyParam
     * @param bool $debug
     * @return array
     */
    public function queryData(
        string   $qry,
        array    $params = null,
        callable $mapping_function = null,
                 $single = null,
        string   $keyParam = null,
        bool     $debug = false
    ): array
    {
        if ($single) { // use binary true for "single" - compatibility only
            $single = true;
        }

        $start = Dates::microtime_float();
        $conn = $this->openMyConnection();
        $list = [];

        $qry = self::escapeQuery($conn, $qry, $params);

        $res = mysqli_query($conn, $qry, MYSQLI_USE_RESULT);
        $err = mysqli_error($conn);
        if ($err) {
            self::saveLogQuery();
            Debug('MySQL Error: ' . mysqli_error($conn), $qry, self::$log);
        }

        if (!$res || !is_object($res)) {
            return [];
        }

        $rows = 0;
        if ($single) {
            if ($r = mysqli_fetch_assoc($res)) {
                $list = $r;
            }
        } else {
            while ($r = mysqli_fetch_assoc($res)) {
                $rows++;
                if (!is_null($keyParam)) {
                    $list[$r[$keyParam]] = !is_null($mapping_function) ? call_user_func($mapping_function, $r) : $r;
                } else {
                    $list[] = !is_null($mapping_function) ? call_user_func($mapping_function, $r) : $r;
                }
            }
            if ($debug) {
                Debug($list);
            }
        }
        mysqli_free_result($res);

        do {
            /* store first result set */
            if ($result = mysqli_store_result($conn)) {
                mysqli_free_result($result);
            }
        } while (mysqli_more_results($conn)
        && mysqli_next_result($conn));

        self::logQuery(
            $qry,
            $params,
            Dates::microtime_float() - $start,
            false,
            $rows
        );
        return $list;
    }

    /**
     * @param string $qry
     * @param string|null $keyParam
     * @param array|null $params
     * @param null $mapping_function
     * @param mixed|null $single
     * @return array
     */
    public function multiQueryData(
        string $qry,
        string $keyParam = null,
        array  $params = null,
               $mapping_function = null,
               $single = null
    ): array
    {
        if ($single) { // use binary true for "single" - compatibility only
            $single = true;
        }

        $start = Dates::microtime_float();
        $conn = $this->openMyConnection();
        $list = [];

        $qry = self::escapeQuery($conn, $qry, $params);
        $res = mysqli_multi_query($conn, $qry);
        if (mysqli_error($conn)) {
            $ip = [
                'qry'   => $qry,
                'errNo' => $conn->errno,
                'err'   => $conn->error,
            ];
            $this->alertDBError($ip);
            Debug($ip, self::$log);
        }

        $index = 0;
        $rows = 0;
        if ($res) {
            do {
                /* store first result set */
                if ($result = mysqli_store_result($conn)) {
                    $rows += $result->num_rows;
                    if ($result->num_rows == 0) {
                        if (!$keyParam) { // for a simple array of result sets, we need to include empty arrays for queries with no results
                            // $list[$index] = [];
                        }
                    } else {
                        while ($r = mysqli_fetch_assoc($result)) {
                            if ($single) {
                                if ($keyParam) {
                                    $list[$r[$keyParam]] = !is_null($mapping_function) ? call_user_func($mapping_function, $r) : $r;
                                } else {
                                    $list = !is_null($mapping_function) ? call_user_func($mapping_function, $r) : $r;
                                }
                                break;
                            } else {
                                if ($keyParam) {
                                    $index = $r[$keyParam];
                                }
                                $list[$index][] = !is_null($mapping_function) ? call_user_func($mapping_function, $r) : $r;
                            }
                        }
                    }
                    mysqli_free_result($result);
                    if (!$keyParam) {
                        $index++;
                    }
                }

            } while (mysqli_more_results($conn)
            && mysqli_next_result($conn));
        }
        self::logQuery(
            $qry,
            $params,
            Dates::microtime_float() - $start,
            false,
            $rows
        );
        return $list;
    }

    /**
     * @param string $qry
     * @param array $param_sets
     * @return array
     */
    public function insertMultiReturnArray(
        string $qry,
        array  $param_sets
    ): array
    {
        $res = [];
        foreach ($param_sets as $params) {
            $res[] = $this->insertReturnArray($qry, $params);
        }
        return $res;
    }

    /**
     * @param string $qry
     * @param array|null $params
     * @return array|null
     */
    public function insertReturnArray(
        string $qry,
        array  $params = null): ?array
    {
        return $this->executeQuery($qry, $params);
    }


    /**
     * @param string $qry
     * @param array $param_sets
     * @return array
     */
    public function insertMultiWithLastId(
        string $qry,
        array  $param_sets): array
    {
        $last_ids = [];
        foreach ($param_sets as $params) {
            $last_ids[] = $this->insert($qry, $params);
        }
        return $last_ids;
    }


    /**
     * @param string $qry
     * @param array $param_sets
     * @return int
     */
    public function insertMulti(
        string $qry,
        array  $param_sets): int
    {
        $last_ids = [];
        foreach ($param_sets as $params) {
            $last_ids[] = $this->insert($qry, $params);
        }
        return sizeof($last_ids);
    }

    /**
     * returns last insert id
     *
     * @param string $qry
     * @param array|null $params
     * @return int|null
     */
    public function insert(
        string $qry,
        array  $params = null): ?int
    {
        $res = $this->executeQuery($qry, $params);
        return (int)$res['last_id'] ?? null;
    }

    /**
     * returns the number of affected rows
     *
     * @param string $qry
     * @param array|null $params
     * @return int
     */
    public function update(
        string $qry,
        array  $params = null): int
    {
        $res = $this->executeQuery($qry, $params);
        return intval($res['affected_rows'] ?? 0);
    }

    /**
     * @param string $qry
     * @param array|null $params
     * @return array
     */
    public function executeQuery(
        string $qry,
        array  $params = null): array
    {
        $start = Dates::microtime_float();
        $conn = $this->openMyConnection();

        $affected_rows = 0;
        $qry = self::escapeQuery($conn, $qry, $params);
        //dd($qry);
        mysqli_begin_transaction($conn);
        $res = mysqli_multi_query($conn, $qry);
        if ($res) {
            do {
                /* store first result set */
                if ($result = mysqli_store_result($conn)) {
                    mysqli_free_result($result);
                }
                $affected_rows += mysqli_affected_rows($conn);
            } while (mysqli_more_results($conn)
            && mysqli_next_result($conn));
        }
        $last_id = mysqli_insert_id($conn);


        $error = mysqli_error($conn);
        if ($error) {
            $ip = [
                'qry'    => $qry,
                'errNo'  => $conn->errno,
                'err'    => $conn->error,
                'params' => $params,
            ];
            $this->alertDBError($ip);
            Debug($ip, self::$log);
            mysqli_rollback($conn);
        } else {
            mysqli_commit($conn);
        }
        self::logQuery(
            $qry,
            $params,
            Dates::microtime_float() - $start,
            true,
            $affected_rows
        );

        return [
            'error'         => $error,
            'sql'           => $qry,
            'last_id'       => $last_id,
            'affected_rows' => $affected_rows,
        ];
    }

    /**
     * Begin a database transaction
     * @return bool True on success, False on failure
     */
    public function beginTransaction(): bool
    {
        $conn = $this->openMyConnection();
        return mysqli_begin_transaction($conn);
    }

    /**
     * Commit a database transaction
     * @return bool True on success, False on failure
     */
    public function commit(): bool
    {
        $conn = $this->openMyConnection();
        return mysqli_commit($conn);
    }

    /**
     * Rollback a database transaction
     * @return bool True on success, False on failure
     */
    public function rollBack(): bool
    {
        $conn = $this->openMyConnection();
        return mysqli_rollback($conn);
    }

    /**
     * @param $ip
     * @return array
     */
    public function getSelectedFieldsForFile($ip): array
    { // replaced with selectRowFromTable / selectRowsFromTable

        $start = Dates::microtime_float();
        $condition = '';
        $fieldNames = '';
        $q = '';
        $resultArray = [];
        $needMultiArray = '';
        $tableName = trim($ip['TABLENAME']);
        $fieldNameArray = $ip['FIELDNAMEARRAY'];
        if (array_key_exists('CONDITION', $ip)) {
            $condition = trim($ip['CONDITION']);
        }
        if (array_key_exists('NEEDMULTIARRAY', $ip)) {
            $needMultiArray = trim($ip['NEEDMULTIARRAY']);
        }
        for ($f = 0; $f < count($fieldNameArray); $f++) {
            // $fieldName = "";
            $fieldName = trim($fieldNameArray[$f]);
            if ($f > 0) {
                $fieldNames .= ', ';
            }
            $fieldNames .= $fieldName;
        }
        if (count($fieldNameArray) > 0) {
            $q = ' select ' . $fieldNames . ' from ' . $tableName;
            if (array_key_exists('CONDITION', $ip)) {
                $q .= ' ' . $condition;
            }
        }
        $conn = self::getInstance()->openMyConnection();


        $rs = $conn->query($q);
        if ($rs->num_rows > 0) {
            while ($row = $rs->fetch_assoc()) {
                if ($needMultiArray == 'y') {
                    $resultArray[] = $row;
                } else {
                    $resultArray = $row;
                }
            }
        }
        mysqli_free_result($rs);

        if ($conn->errno) {
            $this->alertDBError(['qry' => $q, 'errNo' => $conn->errno, 'err' => $conn->error]);
        }

        self::logQuery(
            $q,
            [],
            Dates::microtime_float() - $start,
            false,
            sizeof($resultArray)
        );
        return $resultArray;
    }

    public static function getInstance()
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * @param $ip
     * @return bool|mysqli_result
     */
    public function createTable($ip)
    {
        $conn = self::getInstance()->openMyConnection();
        $q = trim($ip['qry']);
        $rs = $conn->query($q);
        if ($conn->errno) {
            $params = [
                'qry'   => $q,
                'errNo' => $conn->errno,
                'err'   => $conn->error,
            ];

            $this->alertDBError($params);
            Debug($params, self::$log);
        }
        return $rs;
    }

    /**
     * @param $ip
     * @return array|null
     */
    public function fetchRecords($ip): ?array
    {
        $keyParam = null;

        $q = trim($ip['qry']);
        if (array_key_exists('keyParam', $ip)) {
            $keyParam = trim($ip['keyParam']);
        }

        return self::queryData(
            $q,
            null,
            null,
            (bool)($ip['rec'] ?? null),
            $keyParam
        );
    }

    /**
     * @param $ip
     * @return mixed
     */
    public function fetchMultiResultSet($ip)
    {
        $keyParam = '';
        $rec = '';

        $q = trim($ip['qry']);
        if (array_key_exists('keyParam', $ip)) {
            $keyParam = trim($ip['keyParam']);
        }
        if (array_key_exists('rec', $ip)) {
            $rec = trim($ip['rec']);
        }

        return self::multiQueryData(
            $q,
            $keyParam,
            null,
            null,
            (bool)($rec ?? null)
        );
    }

    private static $mysqli = null;

    public function openMyConnection()
    {
        if (!self::$mysqli) {
            self::$mysqli = mysqli_connect(
                'p:' . DB_HOST,
                DB_USER,
                DB_PASS,
                DB_NAME
            );
            if (self::$mysqli->connect_errno) {
                Debug('cannot connect to ' . DB_HOST . ' ' . self::$mysqli->connect_error);
                exit('<p>Server is busy. Please try again later.</p>');
            }
            if (mysqli_error(self::$mysqli)) {
                echo('<p>Server is busy. Please try again later.</p>');
                exit();
            }
        }
        return self::$mysqli;
    }

    /**
     * Save Error Report in Table
     * @param $ip
     * @return void
     */
    public function alertDBError($ip)
    {
        $error = '';
        if (isset($ip['errNo'])) {
            $error .= 'MySQL error = ' . $ip['errNo'] . '<br>';
        }
        if (isset($ip['err'])) {
            $error .= $ip['err'] . '<br>';
        }
        if (isset($ip['qry'])) {
            $error .= $ip['qry'] . '<br>';
        }
        if (isset($ip['msg'])) {
            $error .= $ip['msg'] . '<br>';
        }
        if (isset($_SERVER['HTTP_REFERER'])) {
            $error .= '<br>Page: ' . $_SERVER['HTTP_REFERER'];
        }
        if (isset($_SERVER['PHP_SELF'])) {
            $error .= ' to ' . $_SERVER['PHP_SELF'];
        }
        $error .= "<br>REQUEST_URI : http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        $error .= '<br>Access IP : ' . (Server::RemoteADDR() ?? 'n/a') . '<br><hr>';
        if (isset($_SESSION)) {
            if (sizeof($_SESSION) > 0) {
                $error .= '<br>Session Info : ' . print_r($_SESSION, 1);
            }
        }
        if (isset($_REQUEST)) {
            if (sizeof($_REQUEST) > 0) {
                $error .= '<hr>Request Info : ' . print_r($_REQUEST, 1);
            }
        }
        $recordDate = Dates::Timestamp();

        if (stristr($error, 'INSERT INTO _tblDBErrors') !== false) {
            return;
        }

        if (stristr($error, 'INSERT INTO _tblSlowQueries') !== false) {
            return;
        }

        if (strlen($error) > 1024 * 1024) { // 256 KB was not large enough
            $error = substr($error, 0, 1024 * 1024);
        }


        $insSql = 'INSERT INTO _tblDBErrors
    ( environment, remoteAddr, errorMsg, errorDate, comments, errorLog )
VALUES
    (
     :environment
     , :remoteAddr
     , :errorMsg
     , :errorDate
     , :comments
     , :errorLog
     ); ';

        $this->insert($insSql, [
            'environment' => CONST_ENVIRONMENT,
            'remoteAddr'  => Server::RemoteADDR() ?? 'n/a',
            'errorMsg'    => $error,
            'errorDate'   => $recordDate,
            'comments'    => '',
            'errorLog'    => '',
        ]);
    }

    private ?string $selectedDB = null;

    public function setDatabase(?string $database)
    {
        if ($this->selectedDB && strcasecmp($database, $this->selectedDB) == 0) {
            return;
        }

        $this->selectedDB = $database;

        $conn = $this->openMyConnection();
        mysqli_select_db($conn, $database);
    }
}

