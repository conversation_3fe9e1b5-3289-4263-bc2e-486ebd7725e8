<?php

namespace models\packages;

use models\packages\PackageBorrowerSoftQuote\PackageBorrowerSoftQuoteData;
use models\pdf\CustomTCPDF;
use models\standard\Esign;

/**
 * Class PackageBorrowerSoftQuote
 */
class PackageBorrowerSoftQuote extends CustomTCPDF
{
    public static ?PackageBorrowerSoftQuoteData $ReportData = null;

    /**
     * Generate the PDF report.
     *
     * @param array $inArray
     * @param CustomTCPDF|null $pdf
     * @return CustomTCPDF
     */
    public static function GeneratePDF(array $inArray, CustomTCPDF $pdf = null): CustomTCPDF
    {
        //==========start of mandatory=================================
        global $txnID, $glPositionArray, $topMarginVal, $leftAndRightMarginVal, $siteUrl, $rootPath, $assetsUrl;

        $glPositionArray = $glPositionArray ?? [];

        /** Page layout params **/
        $xVal = 15;
        $yVal = 10;

        $txnID = '';
        if (count($inArray) > 0) {
            if (array_key_exists('txnID', $inArray)) $txnID = trim($inArray['txnID']);
        }

        $LMRId = intval($inArray['LMRId'] ?? 0);

        if(!$LMRId) {
            //dd('$LMRId not set', debug_backtrace());
        }

        self::$ReportData = new PackageBorrowerSoftQuoteData();
        self::$ReportData->Init(
            $inArray,
            $LMRId
        );

        //=======END OF MANDATORY===========================================

        $pdf = $pdf ?: new self(
            PDF_PAGE_ORIENTATION,
            PDF_UNIT,
            PDF_PAGE_FORMAT,
            true,
            'UTF-8',
            false
        );

        $pdf->setAutoPageBreak(TRUE, 0);

        $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
        $pdf->setFontSubsetting(false);

        $pdf->setLeftMargin($xVal);
        $pdf->setRightMargin($xVal);
        $pdf->setTopMargin($yVal);
        $pdf->setFont('helvetica', '', 7);
        $pdf->setCellHeightRatio('1.2');

        $pdf->AddPage('P', 'LETTER');

        $html = $pdf->generateHTML(self::$ReportData, __DIR__ . '/PackageBorrowerSoftQuote/html/BorrowerSoftQuote.html');
        $pdf->writeHTML($html, true, 0, true, 0);


        Esign::Esign(
            0,
            -31,
            0,
            0,
            0,
            0,
            $pdf,
            $glPositionArray
        );

        return $pdf;
    }

    public function Header()
    {
    }

    public function Footer()
    {
        global $txnID, $glPositionArray;

        if (!empty(trim($txnID))) {
            $this->addTxnNumberToFooterNew(['txnID' => $txnID]);
        }
    }
}
