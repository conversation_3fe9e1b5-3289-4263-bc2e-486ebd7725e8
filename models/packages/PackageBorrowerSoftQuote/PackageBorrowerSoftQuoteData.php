<?php

namespace models\packages\PackageBorrowerSoftQuote;

use models\inArrayData;
use models\standard\Strings;
use models\types\strongType;

class PackageBorrowerSoftQuoteData extends strongType
{
    public ?string $TodaysDate = null;
    public ?string $ImageUrl = null;

    public ?string $borrowerName = null;
    public ?string $entityName = null;
    public ?string $propertyAddress = null;
    public ?string $loanProgram = null;
    public ?string $transactionType = null;
    public ?string $maxLTCPer = null;
    public ?string $assumability = null;
    public ?string $guarantor = null;
    public ?string $collateral = null;
    public ?string $loanOfficer = null;

    public ?string $lien1Rates = null;
    public ?string $Term = null;
    public ?string $LoanAmount = null;
    public ?string $totalFeesAndCost = null;

    public ?string $lien1Rate = null;
    public ?string $loanTerm = null;
    public ?string $totalLoanAmount1 = null;
    public ?string $lien1Payment = null;
    public ?string $costBasis = null;
    public ?string $homeValue = null;
    public ?string $assessedValue = null;

    public ?string $rehabCostPercentageFinanced = null;
    public ?string $rehabCost = null;

    public ?string $downPaymentPercentage = null;
    public ?string $maxAmtToPutDown = null;
    public ?string $originationPointsValue = null;
    public ?string $prepaidInterestReserve = null;
    public ?string $totalCashToClose = null;

    public function Init(array $inArray, int $LMRId)
    {
        $inArrayData = inArrayData::fromInArray($inArray);

        //Get variables from DB/Array
        $fileDetails = $inArrayData->fileDetails[$LMRId];

        $this->TodaysDate = date('F j, Y');
        $this->ImageUrl = CONST_ASSETS_URL . 'images/carstenzcap/logo.png';

        $LMRInfoArray = $fileDetails->LMRInfo;
        $fileHMLOEntityInfo = $fileDetails->fileHMLOEntityInfo;
        $fileHMLOPropertyInfo = $fileDetails->fileHMLOPropertyInfo;
        $brokerInfo = $fileDetails->BrokerInfo;
        $fileHMLOEntityInfo = $fileDetails->fileHMLOEntityInfo;
        $fileHMLONewLoanInfo = $fileDetails->fileHMLONewLoanInfo;
        $filePropInfo = $fileDetails->FilePropInfo;
        $LMRClientTypeInfo = $fileDetails->LMRClientTypeInfo;
        $listingRealtorInfo = $fileDetails->listingRealtorInfo;

        $borrowerType = trim($fileHMLOEntityInfo->borrowerType);
        $this->borrowerName    = ucwords(trim($LMRInfoArray->borrowerFName) . ' ' . trim($LMRInfoArray->borrowerLName));
        $this->entityName = ($borrowerType == 'Entity')?ucwords(trim($fileHMLOEntityInfo->entityName)):$this->borrowerName;
        
        $subjPropaddress = $LMRInfoArray->propertyAddress;
        if ($filePropInfo->propertyUnit) {
            $subjPropaddress .= ', ' . $filePropInfo->propertyUnit;
        }
        $subjPropaddress .= ', '. $LMRInfoArray->propertyCity;
        $subjPropaddress .= ', '. Strings::convertState($LMRInfoArray->propertyState);
        $subjPropaddress .= ' '. $LMRInfoArray->propertyZip;
        $this->propertyAddress = $subjPropaddress;
        
        $this->loanProgram = $LMRClientTypeInfo[$LMRId][0]->ClientTypeName;
        $this->transactionType = $fileHMLOPropertyInfo->typeOfHMLOLoanRequesting;;
        $this->maxLTCPer   = $fileHMLONewLoanInfo->maxLTCPer;
        $this->loanOfficer = ucfirst($brokerInfo->firstName) . " " . ucfirst($brokerInfo->lastName);
        $this->lien1Rate    = trim($LMRInfoArray->lien1Rate);;
        $this->loanTerm     = trim($fileHMLOPropertyInfo->loanTerm);;
        $this->totalLoanAmount1  = Strings::Currency($fileHMLONewLoanInfo->totalLoanAmount);
        $this->lien1Payment    = Strings::Currency(trim($LMRInfoArray->lien1Payment));

        $this->costBasis = Strings::Currency(trim($fileHMLONewLoanInfo->costBasis));
        $this->homeValue  = Strings::Currency(trim($LMRInfoArray->homeValue));;
        $this->assessedValue = Strings::Currency(trim($listingRealtorInfo->assessedValue));

        $this->rehabCostPercentageFinanced = (trim($fileHMLONewLoanInfo->rehabCostPercentageFinanced))." %";
        $this->rehabCost = Strings::Currency($fileHMLONewLoanInfo->rehabCost);
        
        $this->totalFeesAndCost = ""; // No sample array found

        $this->downPaymentPercentage = Strings::Currency($fileHMLONewLoanInfo->downPaymentPercentage)." %";
        $this->maxAmtToPutDown = Strings::Currency($fileHMLOPropertyInfo->maxAmtToPutDown);
        $this->originationPointsValue = Strings::Currency($fileHMLONewLoanInfo->originationPointsValue);
        $this->prepaidInterestReserve = Strings::Currency($fileHMLONewLoanInfo->prepaidInterestReserve);

        $totalCashToClose = 0;
        $totalCashToClose += Strings::replaceCommaValues($fileHMLOPropertyInfo->maxAmtToPutDown);
        $totalCashToClose += Strings::replaceCommaValues($fileHMLOPropertyInfo->maxAmtToPutDown);
        $totalCashToClose += Strings::replaceCommaValues($fileHMLONewLoanInfo->prepaidInterestReserve);
        $this->totalCashToClose = Strings::Currency($totalCashToClose);
        $this->checkMissing();
    }
}
