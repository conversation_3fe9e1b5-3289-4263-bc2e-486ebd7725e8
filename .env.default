# *** DO NOT COMMIT ANY SENSITIVE DATA TO VERSION CONTROL ***

# Default Env Vars
LOG_ERRORS=0
LOG_QUERIES=0
SHOW_DEBUG_TOOLS=0
SLOWQUERYTIME=15
OUTPUT_ERRORS=0
OUTPUT_ERRORS_FILE=1
#ERROR_CATCH_ALL=1 # This line breaks things, use only for deep debugging
ALERTTOSLOWQUERIES=true
AMAZON_S3_BUCKET=loanpost
AMAZON_S3_URL="//example.us-east-2.amazonaws.com"
APP_CRON_SERVER_URL="http://local.lendingwise.com/"
APP_DEBUG=true
APP_ENC=true
APP_ENV=development
APP_HTTP_HOST=theloanpost.com
APP_NAME=lendingwise
APP_NODE_SERVER_URL="https://node.theloanpost.com:5100/"
APP_SSL_URL="http://local.lendingwise.com/"
APP_URL="http://local.lendingwise.com/"
AUTH_NET_DIY_URL=https://secure.authorize.net/gateway/transact.dll
#BASE_FILE_FOLDER="/tmp/app_logs" # used for error logs
BUGSNAG_ENABLED=true
DATA_LW_API=lendingwise_api
DATA_LW_CHARGEBEE=lendingwise_chargebee
DATA_LW_DATAWAREHOUSE=lendingwise_datawarehouse
DATA_LW_FCI=lendingwise_fci
DATA_LW_LOG=lendingwise_log
DEBTVAL_DOCID=562
DOMAIN_TITLE=TheLoanPost
EMAIL_ADMIN=
EMAIL_AUDITOR_2_CC=
EMAIL_BCC=
EMAIL_BOUNCE=
EMAIL_CC=
EMAIL_CONTACT=
EMAIL_DEV=
EMAIL_FROM=
EMAIL_FROMNAME=
EMAIL_LOANMOD=
EMAIL_LOAN_AUDITOR=
EMAIL_LOAN_AUDIT_BCC=
EMAIL_NEWACCT=
EMAIL_NEW_FORM_FIELDS=
EMAIL_NVA_REPLY_TO=
EMAIL_REPLY=
EMAIL_SUPPORT=
ERROR_REPORTING_LEVEL=2
HTML_PDFCROWD=1
IGNORE_MISSING_PROPERTIES=0
#LMR_FILE_DOC_IP="http://localhost/"
LMR_FILE_DOC_IP=https://uploadstagecis.lendingwise.com/
LOAD_MENU=v
MASTER_SECRET_KEY=test
NULL_ON_MISSING_PARAMETER=0
PATH_CONTACT_DOCS="contactsDocs/"
PATH_LMR_FILE_DOCS="LMRFileDoc/"
PATH_PC_UP_DOC="PCUpDoc/"
PATH_TASK_DOCS="doc/"
PATH_TRUST_DOCS="trustDocs/"
PRICING_ENGINE_ENV="https://app.loanpass.io"
PRICING_ENGINE_ENV_CV3="https://app.loanpass.io"
PRICING_ENGINE_ENV_RELENDING="https://app.loanpass.io"
PRICING_ENGINE_KEY=lendingwise
PRICING_ENGINE_KEY_CV3=cv3
PRICING_ENGINE_KEY_RELENDING=relending
RAMWSDL_URL="https://staging.ramservicing.com/services/RAMSGatewayVer2.asmx?WSDL"
RECAPTCHA_DISABLE=0
TZ=US/Eastern
USE_LEGACY_CONNECTOR=0
VERSION_MINIFY=
WEBSOCKET_PORT=8888
FORCE_HTTPS=1
#force https should not affect local.lendingwise.com

# ===================================================================================================================
# DEPLOYMENT NOTE:
# *** DO NOT COMMIT ANY SENSITIVE DATA TO VERSION CONTROL ***
# These sensitive env vars below are stored in AWS Stored Secrets Manager and automatically loaded into the machine.
# Any existing Env Vars set in the machine will not be overritten by these.
#
# DEVELOPER NOTE:
# You are allowed to uncomment and override them for your local development environment only.
# ===================================================================================================================

#ACQUALIFY_API_TOKEN=___SECRET_IN_SSM___
#API_KEY_REALESTATE=___SECRET_IN_SSM___
#AUTH_ARB_URL="https://api.authorize.net/xml/v1/request.api"
#AUTH_NET_TRANSACTION_KEY=___SECRET_IN_SSM___
#AUTH_NET_URL="https://secure.authorize.net/gateway/transact.dll"
#AUTH_NET_USER_ID=___SECRET_IN_SSM___
#BUGSNAG_API_KEY=___SECRET_IN_SSM___
#DB_HOST=___SECRET_IN_SSM___
#DB_NAME=___SECRET_IN_SSM___
#DB_PASS=___SECRET_IN_SSM___
#DB_TYPE=MySQL
#DB_USER=___SECRET_IN_SSM___
#DML_CLIENT_EXPORT_HOST_NAME=___SECRET_IN_SSM___
#DML_CLIENT_EXPORT_HOST_USERNAME=___SECRET_IN_SSM___
#DML_CLIENT_EXPORT_PCID=___SECRET_IN_SSM___
#DML_CLIENT_EXPORT_REMOTE_DIR=___SECRET_IN_SSM___
#ECS_AGENT_URI=___SECRET_IN_SSM___
#ECS_CONTAINER_METADATA_URI_V4=___SECRET_IN_SSM___
#ECS_CONTAINER_METADATA_URI=___SECRET_IN_SSM___
#FAX_AGE_COMPANY=___SECRET_IN_SSM___
#FAX_AGE_HOSTNAME=___SECRET_IN_SSM___
#FAX_AGE_PASSWORD=___SECRET_IN_SSM___
#FAX_AGE_USERNAME=___SECRET_IN_SSM___
#FROALA_ACTIVATION_KEY=___SECRET_IN_SSM___
#GOOGLE_CAPTCHA_SECRET_KEY=___SECRET_IN_SSM___
#GOOGLE_CAPTCHA_SITE_KEY=___SECRET_IN_SSM___
#GOOGLE_MAPS_API_KEY=___SECRET_IN_SSM___
#LMR_FILE_DOC_SSH_IP=___SECRET_IN_SSM___
#LMR_FILE_DOC_SSH_PASSWORD=___SECRET_IN_SSM___
#LMR_FILE_DOC_SSH_PORT=22
#LMR_FILE_DOC_SSH_USERNAME=ubuntu
#MCRYPT_IV=___SECRET_IN_SSM___
#MCRYPT_KEY=___SECRET_IN_SSM___
#METRO_FAX_COST=0.06
#METRO_FAX_PASSWORD=___SECRET_IN_SSM___
#METRO_FAX_URL="https://wsf.metrofax.com/WebService.asmx?WSDL"
#METRO_FAX_USER=___SECRET_IN_SSM___
#MICROBILT_IDVERIFY_API="https://apitest.microbilt.com/IDVerify/GetReport"
#MICROBILT_IPVERIFY_API="https://apitest.microbilt.com/IPAddressInfo/"
#MICROBILT_KEY=___SECRET_IN_SSM___
#MICROBILT_SECRET=___SECRET_IN_SSM___
#MICROBILT_TOKEN_URL="https://apitest.microbilt.com/OAuth/GetAccessToken"
#PDFCROWD_HTML_PDF_KEY=___SECRET_IN_SSM___
#PDFCROWD_HTML_PDF_USERNAME=___SECRET_IN_SSM___
#REST_REPORT_DOMAIN=___SECRET_IN_SSM___
#REST_REPORT_ID=___SECRET_IN_SSM___
#REST_REPORT_PARTNER_ACCESS_CODE=___SECRET_IN_SSM___
#REST_REPORT_PARTNER_LEVEL_CODE=___SECRET_IN_SSM___
#REST_REPORT_PARTY_IDENTIFIER=___SECRET_IN_SSM___
#REST_REPORT_PARTY_USER_IDENTIFIER=___SECRET_IN_SSM___
#REST_REPORT_PASSWORD=___SECRET_IN_SSM___
#REST_REPORT_SECURITY_TOKEN=___SECRET_IN_SSM___
#REST_REPORT_URL="http://staging.rest-reports.info/Services/RestGateway.asmx?wsdl"
#SENDGRID_API_KEY=___SECRET_IN_SSM___
#SENDGRID_HOST=smtp.sendgrid.net
#SENDGRID_MARKETING_API_KEY=___SECRET_IN_SSM___
#SENDGRID_MARKETING_PASSWORD=___SECRET_IN_SSM___
#SENDGRID_MARKETING_USERNAME=___SECRET_IN_SSM___
#SENDGRID_PASSWORD=___SECRET_IN_SSM___
#SENDGRID_PORT=587
#SENDGRID_SPOOF_API_KEY=___SECRET_IN_SSM___
#SENDGRID_SPOOF_PASSWORD=___SECRET_IN_SSM___
#SENDGRID_SPOOF_USERNAME=___SECRET_IN_SSM___
#SENDGRID_USERNAME=___SECRET_IN_SSM___
#SMS_EMAIL_HOSTNAME=mailer.theloanpost.com
#SMS_EMAIL_PASSWORD=___SECRET_IN_SSM___
#SMS_EMAIL_PORT=465
#SMS_EMAIL_USERNAME=___SECRET_IN_SSM___
#TWILIO_2FA_SID=___SECRET_IN_SSM___
#TWILIO_2FA_TOKEN=___SECRET_IN_SSM___
#TWILIO_SMS_SERVICEID=___SECRET_IN_SSM___
#VITELITY_DID=___SECRET_IN_SSM___
#VITELITY_HOSTNAME="https://api.vitelity.net/"
#VITELITY_LOGIN=___SECRET_IN_SSM___
#VITELITY_PASSWORD=___SECRET_IN_SSM___
