DROP TABLE IF EXISTS `tblBorrowerDraws`;

CREATE TABLE `tblBorrowerDraws` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `LMRId` INT UNSIGNED NOT NULL,
    `baseTemplateId` INT NULL,
    `submittedAt` <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    `approvedAt` D<PERSON>ETIME NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT FK_BorrowerDraws_BaseTemplate FOREIGN KEY (baseTemplateId)
        REFERENCES tblPcDrawTemplateSettings(id) ON DELETE SET NULL,
    CONSTRAINT FK_BorrowerDraws_File FOREIGN KEY (LMRId)
        REFERENCES tblFile(LMRId) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
