DROP TABLE IF EXISTS `tblBorrowerDrawLineItems`;

CREATE TABLE `tblBorrowerDrawLineItems` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `drawId` INT NOT NULL,
    `categoryId` INT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `order` INT UNSIGNED NOT NULL DEFAULT 1,
    `cost` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `completedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `completedPercent` DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE
            WHEN cost > 0 THEN (completedAmount / cost) * 100
            ELSE 0
        END
    ) STORED,
    `notes` TEXT NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX IX_BorrowerDrawLineItems_Draw_Order (drawId, `order`),
    INDEX IX_BorrowerDrawLineItems_Category_Order (categoryId, `order`),
    CONSTRAINT FK_BorrowerDrawLineItems_Draw FOREIGN KEY (drawId)
        REFERENCES tblBorrowerDraws(id) ON DELETE CASCADE,
    CONSTRAINT FK_BorrowerDrawLineItems_Category FOREIGN KEY (categoryId)
        REFERENCES tblBorrowerDrawCategories(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
