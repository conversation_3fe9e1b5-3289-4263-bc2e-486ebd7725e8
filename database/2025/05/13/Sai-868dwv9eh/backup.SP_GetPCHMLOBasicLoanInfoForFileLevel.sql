/*DELIMITER $$
DROP PROCEDURE IF EXISTS SP_GetPCHMLOBasicLoanInfoForFileLevel$$

CREATE PROCEDURE SP_GetPCHMLOBasicLoanInfoForFileLevel(IN PCID int, IN loanPgm text)
BEGIN
    DECLARE BLID INT DEFAULT 0;
    IF (PCID > 0) THEN

        SET @qry = CONCAT("SELECT t1.BLID INTO @BLID FROM tblPCHMLOBasicLoanInfo t1,
			tblPCHMLOBasicLoanPgmInfo t2
			WHERE t1.BLID = t2.BLID AND t1.activeStatus = 1
			AND t2.loanPgm = '", loanPgm, "' AND t2.PCID = ", PCID, " LIMIT 1");

        PREPARE stmt FROM @qry;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        IF (@BLID > 0) THEN
            SET @qry = CONCAT(
                    "SELECT transactionType, 'HMLOPCTransactionType' AS 'myOpt' FROM tblPCHMLOBasicLoanTransactionType WHERE BLID = ",
                    @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry = CONCAT("SELECT *, 'HMLOPCBasicLoanInfo' AS 'myOpt' FROM tblPCHMLOBasicLoanInfo WHERE BLID = ",
                              @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry = CONCAT(
                    "SELECT propertyType, 'HMLOPCPropertyType' AS 'myOpt' FROM tblPCHMLOBasicLoanPropertyType WHERE BLID = ",
                    @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry = CONCAT(
                    "SELECT extnOption, 'HMLOPCExtnOption' AS 'myOpt' FROM tblPCHMLOBasicLoanExtensionOption WHERE BLID = ",
                    @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry =
                    CONCAT("SELECT loanTerm, 'HMLOPCLoanTerm' AS 'myOpt' FROM tblPCHMLOBasicLoanTermInfo WHERE BLID = ",
                           @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry = CONCAT(
                    "SELECT occupancy, 'HMLOPCOccupancy' AS 'myOpt' FROM tblPCHMLOBasicLoanOccupancy WHERE BLID = ",
                    @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry = CONCAT(
                    "SELECT t1.stateCode, t2.stateName, 'HMLOPCState' AS 'myOpt' FROM tblPCHMLOBasicLoanStateInfo t1, tblStates t2 WHERE t1.stateCode = t2.stateCode AND BLID = ",
                    @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry =
                    CONCAT("SELECT nichesID, 'HMLOPCNiches' AS 'myOpt' FROM tblPCHMLOBasicLoanNichesInfo WHERE BLID = ",
                           @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;


            SET @qry =
                    CONCAT(
                            "SELECT AmortizationVal, 'HMLOPCAmortization' AS 'myOpt' FROM tblPCHMLOBasicLoanAmortizationInfo WHERE BLID = ",
                            @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry =
                    CONCAT(
                            "SELECT rateLockPeriod, 'HMLOPCBasicRateLockPeriodInfo' AS 'myOpt' FROM tblPCHMLOBasicLoanRateLockPeriod WHERE BLID = ",
                            @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry =
                    CONCAT(
                            "SELECT stateCode, 'HMLOPCElgibleState' AS 'myOpt' FROM tblPCHMLOBasicLoanElgibleState WHERE BLID = ",
                            @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            SET @qry =
                    CONCAT(
                            "SELECT entityType, 'HMLOPCEntityType' AS 'myOpt' FROM tblPCHMLOBasicLoanEntityType WHERE BLID = ",
                            @BLID);

            PREPARE stmt FROM @qry;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

        END IF;

    END IF;
END$$
DELIMITER ;*/
