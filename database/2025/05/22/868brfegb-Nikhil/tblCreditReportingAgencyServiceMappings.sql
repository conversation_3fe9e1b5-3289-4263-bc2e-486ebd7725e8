# Make sure to run this after tblCreditReportingAgencies.sql and tblCreditReportingServices.sql to avoid errors

CREATE TABLE `tblCreditReportingAgencyServiceMappings`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `agencyKey`   varchar(100) NOT NULL,
    `serviceKey`  varchar(100) NOT NULL,
    `URL`         varchar(500) NOT NULL,
    `displayName` VARCHAR(250) NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UNIQUE_agencyKey_service_Key` (`agencyKey`, `serviceKey`),
    CONSTRAINT `FK_agencyKey` FOREIGN KEY (`agencyKey`) REFERENCES `tblCreditReportingAgencies` (`key`),
    CONSTRAINT `FK_serviceKey` FOREIGN KEY (`serviceKey`) REFERENCES `tblCreditReportingServices` (`key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;


INSERT INTO `tblCreditReportingAgencyServiceMappings` (`agencyKey`, `serviceKey`, `URL`, `displayName`)
VALUES ('democra', 'borrowercreditreport', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('democra', 'coborrowercreditreport', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('democra', 'jointcreditreport', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('democra', 'businesscreditreport', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('democra', 'avm', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('democra', 'flood', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('democra', 'mers', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('democra', 'criminalrecordreport', 'https://demo.mortgagecreditlink.com/inetapi/request_products.aspx', null),
       ('advantagecredit', 'borrowercreditreport', 'https://credit.advcredit.com/inetapi/request_products.aspx', null),
       ('advantagecredit', 'coborrowercreditreport', 'https://credit.advcredit.com/inetapi/request_products.aspx',
        null),
       ('advantagecredit', 'jointcreditreport', 'https://credit.advcredit.com/inetapi/request_products.aspx', null),
       ('advantagecredit', 'businesscreditreport', 'https://credit.advcredit.com/inetapi/request_products.aspx', null),
       ('advantagecredit', 'avm', 'https://credit.advcredit.com/inetapi/request_products.aspx', null),
       ('advantagecredit', 'flood', 'https://credit.advcredit.com/inetapi/request_products.aspx', null),
       ('advantagecredit', 'mers', 'https://credit.advcredit.com/inetapi/request_products.aspx', null),
       ('advantagecredit', 'criminalrecordreport', 'https://credit.advcredit.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'borrowercreditreport',
        'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'coborrowercreditreport',
        'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'jointcreditreport',
        'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'businesscreditreport',
        'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'avm', 'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'flood', 'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'mers', 'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('advantagepluscredit', 'criminalrecordreport',
        'ttps://advantageplus.meridianlink.com/inetapi/request_products.aspx', null),
       ('alliance2020', 'borrowercreditreport', 'https://alliance.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('alliance2020', 'coborrowercreditreport', 'https://alliance.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('alliance2020', 'jointcreditreport', 'https://alliance.meridianlink.com/inetapi/request_products.aspx', null),
       ('alliance2020', 'businesscreditreport', 'https://alliance.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('alliance2020', 'avm', 'https://alliance.meridianlink.com/inetapi/request_products.aspx', null),
       ('alliance2020', 'flood', 'https://alliance.meridianlink.com/inetapi/request_products.aspx', null),
       ('alliance2020', 'mers', 'https://alliance.meridianlink.com/inetapi/request_products.aspx', null),
       ('alliance2020', 'criminalrecordreport', 'https://alliance.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('americanreportingcompany', 'borrowercreditreport',
        'https://arc.meridianlink.com/inetapi/request_products.aspx', null),
       ('americanreportingcompany', 'coborrowercreditreport',
        'https://arc.meridianlink.com/inetapi/request_products.aspx', null),
       ('americanreportingcompany', 'jointcreditreport', 'https://arc.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('americanreportingcompany', 'businesscreditreport',
        'https://arc.meridianlink.com/inetapi/request_products.aspx', null),
       ('americanreportingcompany', 'avm', 'https://arc.meridianlink.com/inetapi/request_products.aspx', null),
       ('americanreportingcompany', 'flood', 'https://arc.meridianlink.com/inetapi/request_products.aspx', null),
       ('americanreportingcompany', 'mers', 'https://arc.meridianlink.com/inetapi/request_products.aspx', null),
       ('americanreportingcompany', 'criminalrecordreport',
        'https://arc.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'borrowercreditreport',
        'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'coborrowercreditreport',
        'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'jointcreditreport',
        'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'businesscreditreport',
        'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'avm', 'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'flood', 'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'mers', 'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('birchwoodcreditservices', 'criminalrecordreport',
        'https://birchwood.meridianlink.com/inetapi/request_products.aspx', null),
       ('cbfsbusinesssolutions', 'borrowercreditreport',
        'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx', null),
       ('cbfsbusinesssolutions', 'coborrowercreditreport',
        'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx', null),
       ('cbfsbusinesssolutions', 'jointcreditreport',
        'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx', null),
       ('cbfsbusinesssolutions', 'businesscreditreport',
        'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx', null),
       ('cbfsbusinesssolutions', 'avm', 'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('cbfsbusinesssolutions', 'flood', 'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('cbfsbusinesssolutions', 'mers', 'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('cbfsbusinesssolutions', 'criminalrecordreport',
        'https://cbfbusinesssolutions.meridianlink.com/inetapi/request_products.aspx', null),
       ('certifiedcreditreporting', 'borrowercreditreport',
        'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx', null),
       ('certifiedcreditreporting', 'coborrowercreditreport',
        'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx', null),
       ('certifiedcreditreporting', 'jointcreditreport',
        'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx', null),
       ('certifiedcreditreporting', 'businesscreditreport',
        'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx', null),
       ('certifiedcreditreporting', 'avm', 'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('certifiedcreditreporting', 'flood', 'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('certifiedcreditreporting', 'mers', 'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('certifiedcreditreporting', 'criminalrecordreport',
        'https://certifiedcredit.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'borrowercreditreport', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'coborrowercreditreport', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'jointcreditreport', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'businesscreditreport', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'avm', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'flood', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'mers', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('ciccredit', 'criminalrecordreport', 'https://cic.meridianlink.com/inetapi/request_products.aspx', null),
       ('cisinformationsystems', 'borrowercreditreport', 'https://cis.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('cisinformationsystems', 'coborrowercreditreport', 'https://cis.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('cisinformationsystems', 'jointcreditreport', 'https://cis.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('cisinformationsystems', 'businesscreditreport', 'https://cis.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('cisinformationsystems', 'avm', 'https://cis.meridianlink.com/inetapi/request_products.aspx', null),
       ('cisinformationsystems', 'flood', 'https://cis.meridianlink.com/inetapi/request_products.aspx', null),
       ('cisinformationsystems', 'mers', 'https://cis.meridianlink.com/inetapi/request_products.aspx', null),
       ('cisinformationsystems', 'criminalrecordreport', 'https://cis.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('ciscocredit', 'borrowercreditreport', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('ciscocredit', 'coborrowercreditreport', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('ciscocredit', 'jointcreditreport', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('ciscocredit', 'businesscreditreport', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('ciscocredit', 'avm', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('ciscocredit', 'flood', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('ciscocredit', 'mers', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('ciscocredit', 'criminalrecordreport', 'https://credit.ciscocredit.com/inetapi/request_products.aspx', null),
       ('creditbureauservices', 'borrowercreditreport', 'https://cbs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditbureauservices', 'coborrowercreditreport', 'https://cbs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditbureauservices', 'jointcreditreport', 'https://cbs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditbureauservices', 'businesscreditreport', 'https://cbs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditbureauservices', 'avm', 'https://cbs.meridianlink.com/inetapi/request_products.aspx', null),
       ('creditbureauservices', 'flood', 'https://cbs.meridianlink.com/inetapi/request_products.aspx', null),
       ('creditbureauservices', 'mers', 'https://cbs.meridianlink.com/inetapi/request_products.aspx', null),
       ('creditbureauservices', 'criminalrecordreport', 'https://cbs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('service1st', 'borrowercreditreport', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('service1st', 'coborrowercreditreport', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('service1st', 'jointcreditreport', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx', null),
       ('service1st', 'businesscreditreport', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('service1st', 'avm', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx', null),
       ('service1st', 'flood', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx', null),
       ('service1st', 'mers', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx', null),
       ('service1st', 'criminalrecordreport', 'https://svc1stinfo.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditlink', 'borrowercreditreport', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditlink', 'coborrowercreditreport', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditlink', 'jointcreditreport', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx', null),
       ('creditlink', 'businesscreditreport', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditlink', 'avm', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx', null),
       ('creditlink', 'flood', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx', null),
       ('creditlink', 'mers', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx', null),
       ('creditlink', 'criminalrecordreport', 'https://creditlink.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('creditplus', 'borrowercreditreport', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('creditplus', 'coborrowercreditreport', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('creditplus', 'jointcreditreport', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('creditplus', 'businesscreditreport', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('creditplus', 'avm', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('creditplus', 'flood', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('creditplus', 'mers', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('creditplus', 'criminalrecordreport', 'https://credit.creditplus.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'borrowercreditreport',
        'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'coborrowercreditreport',
        'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'jointcreditreport',
        'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'businesscreditreport',
        'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'avm', 'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'flood', 'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'mers', 'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnologies', 'criminalrecordreport',
        'https://credit.credittechnologies.com/inetapi/request_products.aspx', null),
       ('credittechnology', 'borrowercreditreport', 'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('credittechnology', 'coborrowercreditreport',
        'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx', null),
       ('credittechnology', 'jointcreditreport', 'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('credittechnology', 'businesscreditreport', 'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('credittechnology', 'avm', 'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx', null),
       ('credittechnology', 'flood', 'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx', null),
       ('credittechnology', 'mers', 'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx', null),
       ('credittechnology', 'criminalrecordreport', 'https://ctinetwork.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('isc', 'borrowercreditreport', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('isc', 'coborrowercreditreport', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('isc', 'jointcreditreport', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('isc', 'businesscreditreport', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('isc', 'avm', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('isc', 'flood', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('isc', 'mers', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('isc', 'criminalrecordreport', 'https://iscsite.meridianlink.com/inetapi/request_products.aspx', null),
       ('kcbcredit', 'borrowercreditreport', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx',
        null),
       ('kcbcredit', 'coborrowercreditreport', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx',
        null),
       ('kcbcredit', 'jointcreditreport', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx', null),
       ('kcbcredit', 'businesscreditreport', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx',
        null),
       ('kcbcredit', 'avm', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx', null),
       ('kcbcredit', 'flood', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx', null),
       ('kcbcredit', 'mers', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx', null),
       ('kcbcredit', 'criminalrecordreport', 'https://credit.kewaneecreditbureau.com/inetapi/request_products.aspx',
        null),
       ('merchantscreditbureau', 'borrowercreditreport', 'https://mcb.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('merchantscreditbureau', 'coborrowercreditreport', 'https://mcb.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('merchantscreditbureau', 'jointcreditreport', 'https://mcb.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('merchantscreditbureau', 'businesscreditreport', 'https://mcb.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('merchantscreditbureau', 'avm', 'https://mcb.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureau', 'flood', 'https://mcb.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureau', 'mers', 'https://mcb.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureau', 'criminalrecordreport', 'https://mcb.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('merchantscreditbureausavannah', 'borrowercreditreport',
        'https://mcbsav.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureausavannah', 'coborrowercreditreport',
        'https://mcbsav.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureausavannah', 'jointcreditreport',
        'https://mcbsav.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureausavannah', 'businesscreditreport',
        'https://mcbsav.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureausavannah', 'avm', 'https://mcbsav.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureausavannah', 'flood', 'https://mcbsav.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('merchantscreditbureausavannah', 'mers', 'https://mcbsav.meridianlink.com/inetapi/request_products.aspx', null),
       ('merchantscreditbureausavannah', 'criminalrecordreport',
        'https://mcbsav.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureau', 'borrowercreditreport', 'https://premium.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('premiumcreditbureau', 'coborrowercreditreport',
        'https://premium.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureau', 'jointcreditreport', 'https://premium.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('premiumcreditbureau', 'businesscreditreport', 'https://premium.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('premiumcreditbureau', 'avm', 'https://premium.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureau', 'flood', 'https://premium.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureau', 'mers', 'https://premium.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureau', 'criminalrecordreport', 'https://premium.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('premiumcreditbureaudata', 'borrowercreditreport', 'https://cdc.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('premiumcreditbureaudata', 'coborrowercreditreport',
        'https://cdc.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureaudata', 'jointcreditreport', 'https://cdc.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('premiumcreditbureaudata', 'businesscreditreport', 'https://cdc.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('premiumcreditbureaudata', 'avm', 'https://cdc.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureaudata', 'flood', 'https://cdc.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureaudata', 'mers', 'https://cdc.meridianlink.com/inetapi/request_products.aspx', null),
       ('premiumcreditbureaudata', 'criminalrecordreport', 'https://cdc.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('sarma', 'borrowercreditreport', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('sarma', 'coborrowercreditreport', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('sarma', 'jointcreditreport', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('sarma', 'businesscreditreport', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('sarma', 'avm', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('sarma', 'flood', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('sarma', 'mers', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('sarma', 'criminalrecordreport', 'https://credit.sarmamortgage.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'borrowercreditreport',
        'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'coborrowercreditreport',
        'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'jointcreditreport',
        'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'businesscreditreport',
        'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'avm', 'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'flood', 'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'mers', 'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('strategicinformationresources', 'criminalrecordreport',
        'https://sir.meridianlink.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'borrowercreditreport',
        'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'coborrowercreditreport',
        'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'jointcreditreport',
        'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'businesscreditreport',
        'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'avm', 'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'flood', 'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'mers', 'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('settlementonecreditcorp', 'criminalrecordreport',
        'https://credit.settlementone.com/inetapi/request_products.aspx', null),
       ('thecreditbureaucom', 'borrowercreditreport', 'https://ncs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('thecreditbureaucom', 'coborrowercreditreport', 'https://ncs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('thecreditbureaucom', 'jointcreditreport', 'https://ncs.meridianlink.com/inetapi/request_products.aspx', null),
       ('thecreditbureaucom', 'businesscreditreport', 'https://ncs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('thecreditbureaucom', 'avm', 'https://ncs.meridianlink.com/inetapi/request_products.aspx', null),
       ('thecreditbureaucom', 'flood', 'https://ncs.meridianlink.com/inetapi/request_products.aspx', null),
       ('thecreditbureaucom', 'mers', 'https://ncs.meridianlink.com/inetapi/request_products.aspx', null),
       ('thecreditbureaucom', 'criminalrecordreport', 'https://ncs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('unitedoneresources', 'borrowercreditreport',
        'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx', null),
       ('unitedoneresources', 'coborrowercreditreport',
        'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx', null),
       ('unitedoneresources', 'jointcreditreport',
        'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx', null),
       ('unitedoneresources', 'businesscreditreport',
        'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx', null),
       ('unitedoneresources', 'avm', 'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx', null),
       ('unitedoneresources', 'flood', 'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('unitedoneresources', 'mers', 'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('unitedoneresources', 'criminalrecordreport',
        'https://unitedoneresources.meridianlink.com/inetapi/request_products.aspx', null),
       ('universalcreditservices', 'borrowercreditreport', 'https://ucs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('universalcreditservices', 'coborrowercreditreport',
        'https://ucs.meridianlink.com/inetapi/request_products.aspx', null),
       ('universalcreditservices', 'jointcreditreport', 'https://ucs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('universalcreditservices', 'businesscreditreport', 'https://ucs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('universalcreditservices', 'avm', 'https://ucs.meridianlink.com/inetapi/request_products.aspx', null),
       ('universalcreditservices', 'flood', 'https://ucs.meridianlink.com/inetapi/request_products.aspx', null),
       ('universalcreditservices', 'mers', 'https://ucs.meridianlink.com/inetapi/request_products.aspx', null),
       ('universalcreditservices', 'criminalrecordreport', 'https://ucs.meridianlink.com/inetapi/request_products.aspx',
        null),
       ('xactus', 'borrowercreditreport', '/mismo3', 'Full Credit Report - Borrower'),
       ('xactus', 'coborrowercreditreport', '/mismo3', 'Full Credit Report - Co-Borrower'),
       ('xactus', 'jointcreditreport', '/mismo3', 'Full Credit Report - Joint'),
       ('xactus', 'flood', '/mismo', 'Flood Determination - Borrower'),
       ('xactus', 'coborrowerflood', '/mismo', null),
       ('xactus', 'jointflood', '/mismo', null),
       ('xactus', 'borrowerfraud', '/api/epc/frx', null),
       ('xactus', 'coborrowerfraud', '/api/epc/frx', null),
       ('xactus', 'jointfraud', '/api/epc/frx', null),
       ('xactus', 'socialsecurity', '/ssnvxml', null),
       ('xactus', 'borrowersoftpull', '/mismo3', null),
       ('xactus', 'coborrowersoftpull', '/mismo3', null),
       ('xactus', 'jointsoftpull', '/mismo3', null);