UPDATE tblThirdPartyService
SET service =
        CASE
            WHEN service = 'Consumer credit report' THEN 'borrowercreditreport'
            WHEN service = 'Business credit report' THEN 'businesscreditreport'
            WHEN service = 'Co-Borrower credit report' THEN 'coborrowercreditreport'
            WHEN service = 'Borrower Credit Report' THEN 'borrowercreditreport'
            WHEN service = 'Co-Borrower Credit Report' THEN 'coborrowercreditreport'
            WHEN service = 'Joint Credit Report - Borrower and Co-Borrower' THEN 'jointcreditreport'
            WHEN service = 'Business Credit Report' THEN 'businesscreditreport'
            WHEN service = 'AVM' THEN 'avm'
            WHEN service = 'Flood' THEN 'flood'
            WHEN service = 'MERS' THEN 'mers'
            WHEN service = 'Criminal Record Report' THEN 'criminalrecordreport'
            ELSE service
            END,
    cra     =
        CASE
            WHEN cra = 'Demo CRA' THEN 'democra'
            WHEN cra = 'Advantage Credit' THEN 'advantagecredit'
            WHEN cra = 'Advantage Plus Credit' THEN 'advantagepluscredit'
            WHEN cra = 'Alliance 2020' THEN 'alliance2020'
            WHEN cra = 'American Reporting Company' THEN 'americanreportingcompany'
            WHEN cra = 'Birchwood Credit Services' THEN 'birchwoodcreditservices'
            WHEN cra = 'CBFS Business Solutions' THEN 'cbfsbusinesssolutions'
            WHEN cra = 'Certified Credit Reporting' THEN 'certifiedcreditreporting'
            WHEN cra = 'CIC Credit' THEN 'ciccredit'
            WHEN cra = 'CIS Information Systems' THEN 'cisinformationsystems'
            WHEN cra = 'CISCO Credit' THEN 'ciscocredit'
            WHEN cra = 'Credit Bureau Services' THEN 'creditbureauservices'
            WHEN cra = 'Service 1st' THEN 'service1st'
            WHEN cra = 'Credit Link' THEN 'creditlink'
            WHEN cra = 'Credit Plus' THEN 'creditplus'
            WHEN cra = 'Credit Technologies' THEN 'credittechnologies'
            WHEN cra = 'Credit Technology' THEN 'credittechnology'
            WHEN cra = 'ISC' THEN 'isc'
            WHEN cra = 'KCB Credit' THEN 'kcbcredit'
            WHEN cra = 'Merchants Credit Bureau' THEN 'merchantscreditbureau'
            WHEN cra = 'Merchants Credit Bureau (Savannah)' THEN 'merchantscreditbureausavannah'
            WHEN cra = 'Premium Credit Bureau' THEN 'premiumcreditbureau'
            WHEN cra = 'Premium Credit Bureau Data' THEN 'premiumcreditbureaudata'
            WHEN cra = 'Sarma' THEN 'sarma'
            WHEN cra = 'Strategic Information Resources' THEN 'strategicinformationresources'
            WHEN cra = 'Settlementone Credit Corp' THEN 'settlementonecreditcorp'
            WHEN cra = 'TheCreditBureau.com' THEN 'thecreditbureaucom'
            WHEN cra = 'United One Resources' THEN 'unitedoneresources'
            WHEN cra = 'Universal Credit Services' THEN 'universalcreditservices'
            ELSE cra
            END;