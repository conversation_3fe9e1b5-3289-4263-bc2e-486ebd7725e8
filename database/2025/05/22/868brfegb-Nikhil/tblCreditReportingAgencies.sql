CREATE TABLE `tblCreditReportingAgencies`
(
    `id`     int unsigned NOT NULL AUTO_INCREMENT,
    `key`    varchar(100) NOT NULL,
    `name`   varchar(250) NOT NULL,
    `status` tinyint      NULL DEFAULT 1,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UNIQUE_key` (`key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;


INSERT INTO `tblCreditReportingAgencies` (`key`, `name`)
VALUES ('democra', 'Demo CRA'),
       ('advantagecredit', 'Advantage Credit'),
       ('advantagepluscredit', 'Advantage Plus Credit'),
       ('alliance2020', 'Alliance 2020'),
       ('americanreportingcompany', 'American Reporting Company'),
       ('birchwoodcreditservices', 'Birchwood Credit Services'),
       ('cbfsbusinesssolutions', 'CBFS Business Solutions'),
       ('certifiedcreditreporting', 'Certified Credit Reporting'),
       ('ciccredit', 'CIC Credit'),
       ('cisinformationsystems', 'CIS Information Systems'),
       ('ciscocredit', 'CISCO Credit'),
       ('creditbureauservices', 'Credit Bureau Services'),
       ('service1st', 'Service 1st'),
       ('creditlink', 'Credit Link'),
       ('creditplus', 'Credit Plus'),
       ('credittechnologies', 'Credit Technologies'),
       ('credittechnology', 'Credit Technology'),
       ('isc', 'ISC'),
       ('kcbcredit', 'KCB Credit'),
       ('merchantscreditbureau', 'Merchants Credit Bureau'),
       ('merchantscreditbureausavannah', 'Merchants Credit Bureau (Savannah)'),
       ('premiumcreditbureau', 'Premium Credit Bureau'),
       ('premiumcreditbureaudata', 'Premium Credit Bureau Data'),
       ('sarma', 'Sarma'),
       ('strategicinformationresources', 'Strategic Information Resources'),
       ('settlementonecreditcorp', 'Settlementone Credit Corp'),
       ('thecreditbureaucom', 'TheCreditBureau.com'),
       ('unitedoneresources', 'United One Resources'),
       ('universalcreditservices', 'Universal Credit Services'),
       ('xactus', 'Xactus');