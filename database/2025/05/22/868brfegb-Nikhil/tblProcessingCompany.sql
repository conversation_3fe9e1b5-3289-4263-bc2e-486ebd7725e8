UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--Borrower Credit Report', '_borrowercreditreport');

UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--Co-Borrower Credit Report', '_coborrowercreditreport');

UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--Joint Credit Report - Borrower and Co-Borrower', '_jointcreditreport');

UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--Business Credit Report', '_businesscreditreport');

UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--AVM', '_avm');

UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--Flood', '_flood');

UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--MERS', '_mers');

UPDATE tblProcessingCompany
SET thirdPartyServicesProducts =
    REPLACE(thirdPartyServicesProducts, '--report--Criminal Record Report', '_criminalrecordreport');