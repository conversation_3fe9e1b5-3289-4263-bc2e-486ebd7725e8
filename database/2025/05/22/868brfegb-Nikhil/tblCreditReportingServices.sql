CREATE TABLE `tblCreditReportingServices`
(
    `id`        int unsigned NOT NULL AUTO_INCREMENT,
    `key`       varchar(100) NOT NULL,
    `name`      varchar(250) NOT NULL,
    `sortOrder` int          NULL DEFAULT NULL,
    `status`    tinyint      NULL DEFAULT 1,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UNIQUE_key` (`key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;


INSERT INTO `tblCreditReportingServices` (`key`, `name`, `status`, `sortOrder`)
VALUES ('borrowercreditreport', 'Borrower Credit Report', 1, 10),
       ('coborrowercreditreport', 'Co-Borrower Credit Report', 1, 20),
       ('jointcreditreport', 'Joint Credit Report - Borrower and Co-Borrower', 1, 30),
       ('borrowersoftpull', 'Soft Pull - Borrower', 1, 40),
       ('coborrowersoftpull', 'Soft Pull - Co-Borrower', 1, 50),
       ('jointsoftpull', 'Soft Pull - Joint', 1, 60),
       ('borrowerfraud', 'Fraud Report - Borrower', 0, 70),
       ('coborrowerfraud', 'Fraud Report - Co-Borrower', 0, 80),
       ('jointfraud', 'Fraud Report - Joint', 0, 90),
       ('businesscreditreport', 'Business Credit Report', 1, 100),
       ('avm', 'AVM', 1, 110),
       ('flood', 'Flood Determination', 1, 120),
       ('coborrowerflood', 'Flood Determination - Co-Borrower', 1, 130),
       ('jointflood', 'Flood Determination - Joint', 1, 140),
       ('mers', 'Mers', 1, 150),
       ('criminalrecordreport', 'Criminal Record Report', 0, 160),
       ('identityidx', 'Identity IDX', 0, 170),
       ('socialsecurity', 'Social Security Number Authentication', 0, 180);