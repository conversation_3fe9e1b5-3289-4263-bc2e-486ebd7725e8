DELIMITER $$

DROP TRIGGER `insert_system_notes_file_create`$$

CREATE
    TRIGGER `insert_system_notes_file_create` AFTER INSERT ON `tblFile`
    FOR EACH ROW BEGIN
    DECLARE cnt INT DEFAULT 0;
		<PERSON><PERSON><PERSON><PERSON> comments VARCHAR(100);
		DECLARE brokerNumber INT DEFAULT 0;
		DECLARE clientId INT DEFAULT 0;
		DECLARE branchID INT DEFAULT 0;
		DECLARE employeeId INT DEFAULT 0;
		IF (new.createdUserType = 'Employee') THEN
			SET comments = 'Created via Back Office';
			SET employeeId =  new.createdBy;
			ELSEIF (new.createdUserType = 'Admin') THEN
				SET comments = 'Created By Super';
			ELSEIF (new.createdUserType = 'LMR AE' || new.createdUserType = 'Branch') THEN
				SET comments = 'Created via Branch';
				SET branchID =  new.FBRID;
			ELSEIF (new.createdUserType = 'Client') THEN
				SET comments = 'Created via Borrower Portal';
				SET clientId =  new.clientId;
			ELSEIF (new.createdUserType = 'Agent') THEN
				SET comments = 'Created via Broker';
				SET brokerNumber =  new.brokerNumber;
			ELSEIF (new.createdUserType = 'secondaryAgent') THEN
				SET comments = 'Created via Loan Officer';
			ELSEIF (new.createdUserType = 'Lead') THEN
				SET comments = 'Created via POST';
	                ELSEIF (new.createdUserType = 'QA Web Form') THEN
				SET comments = 'Created via Quick App Webform';
                        ELSEIF (new.createdUserType = 'FA Web Form') THEN
				SET comments = 'Created via Full App Webform';
 ELSEIF (new.createdUserType = 'QA Web Form wizard') THEN
				SET comments = 'Auto Saved - Submission Not Completed - Via Multi Step Quick App';
 ELSEIF (new.createdUserType = 'FA Web Form wizard') THEN
				SET comments = 'Auto Saved - Submission Not Completed - Via Multi Step Full App';
			ELSEIF (new.createdUserType = 'Web Form') THEN
			SET comments = 'Created via Webform';
END IF;
SET cnt = (SELECT COUNT(*) FROM tblLMRProcessorComments WHERE fileID = new.LMRId); 			IF cnt = 0 THEN
				INSERT IGNORE INTO tblLMRProcessorComments (fileID, notesDate, processorComments,private, isSysNotes,notesType, brokerNumber, clientId, executiveId, employeeId)
				VALUES (new.LMRId, new.lastUpdatedDate, comments,'0', '1','GE', brokerNumber, new.clientId, branchID, employeeId);
END IF;
END;
$$

DELIMITER ;