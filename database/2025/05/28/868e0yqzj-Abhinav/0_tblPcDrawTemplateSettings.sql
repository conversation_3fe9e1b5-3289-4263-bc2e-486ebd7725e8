DROP TABLE IF EXISTS `tblPcDrawTemplateSettings`;

CREATE TABLE `tblPcDrawTemplateSettings` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `PCID` INT UNSIGNED NOT NULL,
    `allowBorrowersAddEditCategories` TINYINT(1) NOT NULL DEFAULT 0,
    `allowBorrowersDeleteCategories` TINYINT(1) NOT NULL DEFAULT 0,
    `allowBorrowersAddEditLineItems` TINYINT(1) NOT NULL DEFAULT 0,
    `allowBorrowersDeleteLineItems` TINYINT(1) NOT NULL DEFAULT 0,
    `allowBorrowersSOWRevisions` TINYINT(1) NOT NULL DEFAULT 0,
    `allowBorrowersExceedFinancedRehabCostOnRevision` TINYINT(1) NOT NULL DEFAULT 0,
    `drawFee` INT UNSIGNED NOT NULL DEFAULT 0,
    CONSTRAINT UQ_tblPcDrawTemplateSettings_ProcessingCompanyID UNIQUE (PCID),
    CONSTRAINT FK_tblPcDrawTemplateSettings_ProcessingCompany FOREIGN KEY (PCID) REFERENCES tblProcessingCompany(PCID)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
