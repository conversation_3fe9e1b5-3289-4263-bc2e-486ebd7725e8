CREATE TABLE `tblPCHMLOBasicLoanExitStrategy`
(
    `id`           INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `BLID`         INT UNSIGNED NOT NULL,
    `exitStrategy` VARCHAR(50)  NOT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `FK_tblPCHMLOBasicLoanExitStrategy_BLID`
        FOREIGN KEY (`BLID`)
            REFERENCES `tblPCHMLOBasicLoanInfo` (`BLID`)
            ON UPDATE CASCADE
            ON DELETE RESTRICT
)
    COMMENT = 'Stores custom loan guideline Loan Exit strategies (BLID level).';
