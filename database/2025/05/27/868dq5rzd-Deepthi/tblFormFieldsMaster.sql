INSERT INTO tblFormFieldsMaster(fieldName, fieldLabel, sectionID, tabName, activeStatus, displayOrder,
                                FADefaultShow, QADefaultShow, BODefaultShow, fileType, loanProgram,
                                tableName, fieldType, enableAllLoanPrograms)
VALUES ('coBorrowerUnit', 'Unit #', 'CBI', 'LI', 1, 95, 0, 0, 0, 'HMLO,loc', NULL, 'tblFile2', 'varchar(16)',
        1),
       ('coBorrowerCounty', 'County', 'CBI', 'LI', 1, 125, 0, 0, 0, 'HMLO,loc', NULL, 'tblFile2', 'varchar(32)', 1);

SET
@parentID = (SELECT fieldID
                 FROM tblFormFieldsMaster
                 WHERE fieldName = 'coBResidedPresentAddr');

INSERT INTO tblFormFieldsMaster(fieldName, fieldLabel, sectionID, tabName, activeStatus, displayOrder,
                                FADefaultShow, QADefaultShow, BODefaultShow, fileType, loanProgram,
                                tableName, fieldType, enableAllLoanPrograms, parentID)
VALUES ('coBorrowerPreviousUnit', 'Previous Unit #', 'CBI', 'LI', 1, 155, 0, 0, 0, 'HMLO,loc', NULL, 'tblFile2',
        'varchar(16)', 1, @parentID),
       ('coBorrowerPreviousCounty', 'Previous County', 'CBI', 'LI', 1, 185, 0, 0, 0, 'HMLO,loc', NULL, 'tblFile2',
        'varchar(32)', 1, @parentID);

INSERT INTO tblFormFieldsMaster(fieldName, fieldLabel, sectionID, tabName, activeStatus, displayOrder,
                                FADefaultShow, QADefaultShow, BODefaultShow, fileType, loanProgram,
                                tableName, fieldType, enableAllLoanPrograms)
VALUES ('coBorrowerMailingUnit', 'Mailing Unit #', 'CBI', 'LI', 1, 215, 0, 0, 0, 'HMLO,loc', NULL, 'tblFile2',
        'varchar(16)', 1),
       ('coBorrowerMailingCounty', 'Mailing County', 'CBI', 'LI', 1, 245, 0, 0, 0, 'HMLO,loc', NULL, 'tblFile2',
        'varchar(32)', 1);