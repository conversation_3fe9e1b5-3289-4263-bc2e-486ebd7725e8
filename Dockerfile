FROM php:7.4-apache

# Set container-wide timezone to New York
ENV TZ=America/New_York
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install updated keyring to fix GPG signature issues
RUN apt-get -o Acquire::AllowInsecureRepositories=true \
    -o Acquire::AllowDowngradeToInsecureRepositories=true \
    update && \
    apt-get -y --allow-unauthenticated install --no-install-recommends \
        debian-archive-keyring gnupg && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/*

# Install necessary extensions and dependencies
RUN apt-get update && apt-get install -y \
    vim \
    nano \
    nginx \
    htop \
    net-tools \
    unzip \
    libonig-dev \
    libxml2-dev \
    libcurl4-openssl-dev \
    libzip-dev \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    gcc \
    make \
    autoconf \
    libmcrypt-dev \
    libdb-dev \
    imagemagick \
    libmagickwand-dev && \
    docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install \
    curl \
    gd \
    intl \
    mbstring \
    mysqli \
    pdo_mysql \
    soap \
    xml \
    zip \
    bcmath \
    dba \
    calendar \
    exif \
    pcntl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install MySQL client tools
RUN apt-get update && apt-get install -y default-mysql-client && apt-get clean

# Install and enable imagick
RUN pecl install imagick && \
    docker-php-ext-enable imagick

# Install and enable mcrypt using PECL
RUN pecl install mcrypt-1.0.4 && \
    docker-php-ext-enable mcrypt

# Install XDebug
RUN pecl install xdebug-3.1.6 && \
    docker-php-ext-enable xdebug

# Add XDebug configuration
COPY scripts/xdebug.ini /usr/local/etc/php/conf.d/xdebug.ini

# Enable Apache mod_rewrite
RUN a2enmod rewrite

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Copy application files first to allow Composer to access them
COPY . /var/www/html

# Copy custom PHP configuration
COPY custom-php.ini /usr/local/etc/php/conf.d/custom-php.ini

# Set the working directory to /var/www/html
WORKDIR /var/www/html

# Run Composer install
RUN composer install --no-dev --optimize-autoloader

# Set up Apache virtual host
RUN echo '<VirtualHost *:80>\n\
    ServerName dev.theloanpost.com\n\
    ServerAlias *.theloanpost.com *.us-east-2.elb.amazonaws.com\n\
    DocumentRoot /var/www/html/public\n\
    <Directory /var/www/html/public>\n\
        Options FollowSymLinks MultiViews\n\
        AllowOverride All\n\
        Require all granted\n\
    </Directory>\n\
    ErrorLog ${APACHE_LOG_DIR}/dev.theloanpost.com-error.log\n\
    CustomLog ${APACHE_LOG_DIR}/dev.theloanpost-access.log combined\n\
    RewriteEngine on\n\
</VirtualHost>' > /etc/apache2/sites-available/000-default.conf

# Set global ServerName to suppress warnings
RUN echo "ServerName localhost" >> /etc/apache2/apache2.conf

# Redirect logs to Docker
RUN ln -sfT /dev/stderr ${APACHE_LOG_DIR}/dev.theloanpost.com-error.log && \
    ln -sfT /dev/stdout ${APACHE_LOG_DIR}/dev.theloanpost-access.log

# Configure PHP error logging
RUN echo "log_errors = On\n\
error_log = /dev/stderr\n\
display_errors = On\n\
display_startup_errors = On" > /usr/local/etc/php/conf.d/docker-php-errors.ini

# Create a MySQL configuration file
RUN echo "[mysql]\n\
no-auto-rehash\n\
\n\
[mysqld]\n\
default_authentication_plugin=mysql_native_password\n\
default_time_zone='-05:00'\n\
explicit_defaults_for_timestamp = ON\n\
host_cache_size=0\n\
innodb_buffer_pool_size=512M\n\
log-bin-trust-function-creators = 1\n\
log_bin_trust_function_creators=1\n\
max_allowed_packet=256M\n\
pid-file=/tmp/mysql.pid\n\
bind-address = 0.0.0.0\n\
sql_mode = \"\"\n\
tls_version = TLSv1.2,TLSv1.3" > /etc/mysql/conf.d/custom-mysql.cnf

RUN mkdir -p /var/www/html/public/temp
RUN chown -R www-data:www-data /var/www/html && \
    chmod -R 755 /var/www/html

# Expose the container's port
EXPOSE 80
