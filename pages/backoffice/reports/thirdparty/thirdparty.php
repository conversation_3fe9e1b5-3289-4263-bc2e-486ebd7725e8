<?php

namespace pages\backoffice\reports\thirdparty;

use models\PageVariables;
use models\portals\BackofficePage;
use models\portals\Breadcrumb;
use models\constants\gl\glThirdPartyServicesCRA;
use pages\backoffice\reports\thirdparty\classes\thirdPartyServicesDTO;

class thirdparty extends BackofficePage
{
    /**
     * @var thirdPartyServicesDTO[]
     */
    public static ?array $Report = null;

    public static function Init()
    {
        parent::Init();

        self::setMasterPage(MASTERPAGE_PUBLIC);

        Breadcrumb::$title = '3rd Party Services';
        Breadcrumb::$icon = 'fas fa-home icon-md';
        Breadcrumb::$breadcrumbList = [];
        Breadcrumb::$toolbarHTML = '';
    }

    public static function Get()
    {
        self::$Report = thirdPartyServicesDTO::getReport(PageVariables::$PCID);
        $glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
        foreach (self::$Report as $row) {
            $row->service = $glThirdPartyServicesCRA[$row->cra]->Services[$row->service]['Name'] ?? $row->service;
            $row->cra = $glThirdPartyServicesCRA[$row->cra]->Name ?? $row->cra;
        }
    }
}