<?php

namespace pages\backoffice\pipeline\classes;

use models\standard\Dates;
use models\types\strongType;

class SearchFields extends strongType
{
    public ?string $a = null; // unused
    public ?string $searchPCID = null; // unused
    public ?string $page = null; // unused
    public ?string $debug = null; // unused

    public ?array $eId = null; //
    public ?array $broker = null; //
    public ?array $loanOfficerSearch = null; //
    public ?array $employeeId = null; //
    public ?string $billingDueDate = null; //
    public ?string $billingDueDateStart = null; //
    public ?string $billingDueDateEnd = null; //
    public ?int $staleDay = null; //
    public ?string $paymentBased = null; //
    public ?array $notesType = null; //
    public ?string $priorityLevel = null; //
    public ?string $activeFile = null; //
    public ?string $noOfRecordsPerPage = null;
    public ?string $leadSource = null; //
    public ?string $referringParty = null; //
    public ?array $WFID = null; //
    public ?array $WFSId = null; //
    public ?array $WFSNotCompletedId = null; //
    public ?array $propertyState = null; //
    public ?array $multipleModuleCode = null; //
    public ?array $LMRClientType = null; //
    public ?array $LMRInternalClientType = null; //
    public ?array $statusOpt = null; //
    public ?string $searchTerm = null;
    public ?array $searchField = null;
    public ?array $multiplePrimaryStatus = null; //
    public ?array $lenderName = null; //
    public ?string $stDate = null; //
    public ?string $endDate = null; //
    public ?string $modifiedStartDate = null; //
    public ?string $modifiedEndDate = null; //
    public ?string $closingStartDate = null; //
    public ?string $closingEndDate = null; //
    public ?string $maturityStartDate = null; //
    public ?string $maturityEndDate = null; //
    public ?string $appraisalStartDate = null; //
    public ?string $appraisalEndDate = null; //
    public ?string $receivedStartDate = null; //
    public ?string $receivedEndDate = null; //
    public ?string $subStDate = null; //
    public ?string $subEndDate = null; //
    public ?string $RESTPCName = null; // not used, just used to find RestProcId
    public ?int $RESTProcId = null; //
    public ?string $RESTExecutiveName = null; // not used, just used to find RESTBranchId
    public ?int $RESTBranchId = null; //
    public ?string $mySalesRep = null; //
    public ?string $paymentStatus = null; //
    public ?string $PCName = null; //
    public ?string $branchName = null; //
    public ?string $loanAuditStatus = null; //
    public ?string $CFPBAuditStatus = null; //
    public ?array $WFStepID = null; //
    public ?array $servicingStatusCode = null; //
    public ?string $branchId = null; //
    public ?array $contactSearchId = null; //

    public static function Init(?array $vals): SearchFields
    {
        foreach($vals as $k => $v) {
            if(stristr($k, '[]') !== false) {
                $vals[str_replace('[]', '', $k)] = $v;
                unset($vals[$k]);
            }
        }
        if(isset($vals['loadPipelineSearch'])) {
            unset($vals['loadPipelineSearch']);
        }

        if(isset($vals['employeeId']) && !is_array($vals['employeeId'])) {
            $vals['employeeId'] = [$vals['employeeId']];
        }

        if(isset($vals['notesType']) && !is_array($vals['notesType'])) {
            $vals['notesType'] = [$vals['notesType']];
        }

        $t = new self($vals);
        $t->initBillingDates();
        return $t;
    }

    private function initBillingDates()
    {
        $curDate = Dates::Datestamp();

        $firstDay = getdate(mktime(0, 0, 0, date('m'), date('d'), date('Y')));
        $curDay = $firstDay['wday'];
        $wkStDate = date('Y-m-d', mktime(0, 0, 0, date('m'), date('d') - $curDay, date('Y')));
        $wkStDateArray = explode('-', $wkStDate);
        $wkStDay = $wkStDateArray[2];
        $wkStMn = $wkStDateArray[1];
        $wkStYr = $wkStDateArray[0];


        switch($this->billingDueDate) {
            case 'pastDue':
                $this->billingDueDateEnd = $curDate;
                break;
            case 'thisWeek':
                $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 6, $wkStYr));
                $this->billingDueDateStart = $wkStDate;
                $this->billingDueDateEnd = $wkEndDate;
                break;
            case 'twoWeek':
                $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 13, $wkStYr));
                $this->billingDueDateStart = $wkStDate;
                $this->billingDueDateEnd = $wkEndDate;
                break;
            case 'threeWeek':
                $wkEndDate = date('Y-m-d', mktime(0, 0, 0, $wkStMn, $wkStDay + 20, $wkStYr));
                $this->billingDueDateStart = $wkStDate;
                $this->billingDueDateEnd = $wkEndDate;
                break;
        }
    }
}