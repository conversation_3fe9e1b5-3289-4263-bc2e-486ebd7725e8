<?php
namespace pages\backoffice\pipeline;

use models\constants\CFPBAuditStatusArray;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glNotesTypeArray;
use models\constants\gl\glPaymentBasedArray;
use models\constants\gl\glSalesRepresentativeArray;
use models\constants\gl\glServicingSubStatus;
use models\constants\gl\glStaleFileArray;
use models\constants\loanAuditStatusArray;
use models\constants\priorityLevelArray;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;
use pages\backoffice\pipeline\classes\SearchForm;
use pages\backoffice\pipeline\classes\PCSubStatusInfo;

?>

<div class="row mb-2">
    <?php if (SearchForm::$SelectSubmissionDate) { ?>
        <div class="col-lg-3 mb-2">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold mb-0">Select Submission Date:</label>
                <div class="input-daterange input-group">
                    <input type="text"
                           name="subStDate"
                           id="subStDate"
                           class="form-control form-controller-solid dateClass"
                           value="<?php echo SearchForm::$SearchFields->subStDate; ?>"
                           size="13"
                           maxlength="10"
                           placeholder="From Date"><label for="subStDate"></label>
                    <div class="input-group-append">
                            <span class="input-group-text">
                                <i class="la la-ellipsis-h"></i>
                            </span>
                    </div>
                    <input type="text"
                           name="subEndDate"
                           id="subEndDate"
                           class="form-control form-controller-solid dateClass"
                           value="<?php echo SearchForm::$SearchFields->subEndDate ?>"
                           size="13"
                           maxlength="10"
                           placeholder="To Date"><label for="subEndDate"></label>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-2">
            <label class="text-dark-50 font-weight-bold mb-0" for="searchText">Search Text</label>
            <div class="input-group">
                <input type="text"
                       name="searchTerm"
                       id="searchTerm"
                       autocomplete="off"
                       class="form-control form-controller-solid"
                       value="<?php echo SearchForm::$SearchFields->searchTerm; ?>"
                       placeholder="Type text to search"
                       onchange="changeFieldColor('searchField', this);"
                />
            </div>
        </div>
        <div class="col-lg-3 mb-2">
            <label class="text-dark-50 font-weight-bold mb-0">Select Search Field</label>
            <select name="searchField[]"
                    id="searchField"
                    multiple=""
                    class="chzn-select">
                <?php foreach (SearchForm::$searchFields as $s => $searchKeyField) { ?>
                    <option value="<?php echo $s; ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->searchField, $s); ?>><?php echo $searchKeyField; ?></option>
                <?php } ?>
            </select>
        </div>
        <?php if (SearchForm::$PCSearch) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6">
                <label class="text-dark-50 font-weight-bold  mb-0" for="RESTPCName">Type PC To Search</label>
                <input type="text"
                       name="RESTPCName"
                       id="RESTPCName"
                       value="<?php echo SearchForm::$SearchFields->RESTPCName ?>"
                       size="29"
                       placeholder="Type PC name to search"
                       autocomplete="off"
                       class="form-control form-controller-solid datatable-input"
                       onblur="if(this.value === '') { document.LMRReport.RESTProcId.value = 0; }"/>
                <input type="hidden"
                       id="RESTProcId"
                       name="RESTProcId"
                       value="<?php echo SearchForm::$SearchFields->RESTProcId ?>"
                       size="10"/>
            </div>
            <div class="col-lg-3 mb-lg-0 mb-6">
                <label class="text-dark-50 font-weight-bold  mb-0" for="RESTExecutiveName">
                    Type Branch Name To Search
                </label>
                <input type="text"
                       name="RESTExecutiveName"
                       id="RESTExecutiveName"
                       value="<?php echo SearchForm::$SearchFields->RESTExecutiveName ?>"
                       size="29"
                       autocomplete="off"
                       placeholder="Type Branch name to search"
                       class="form-control form-controller-solid datatable-input"
                       onblur="if(this.value === '') { document.LMRReport.RESTBranchId.value = '3178f12db7e77c19'; }"/>
                <input type="hidden"
                       id="RESTBranchId"
                       name="RESTBranchId"
                       value="<?php echo cypher::myEncryption(SearchForm::$SearchFields->RESTBranchId) ?>"/>
            </div>

            <?php if (SearchForm::$AllRep) { ?>
                <div class="col-lg-3 mb-lg-0 mb-6">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="mySalesRep">All Rep</label>
                    <select name="mySalesRep"
                            id="mySalesRep"
                            class="choice form-control form-controller-solid datatable-input">
                        <option value=""> - All Rep -</option>
                        <?php foreach (glSalesRepresentativeArray::$glSalesRepresentativeArray as $salesRepKey => $salesRep) { ?>
                            <option value="<?php echo $salesRepKey ?>" <?php echo Arrays::isSelected($salesRepKey, SearchForm::$SearchFields->mySalesRep); ?>><?php echo $salesRep ?></option>
                        <?php } ?>
                    </select>
                </div>
            <?php } ?>

            <div class="col-lg-3 mb-lg-0 mb-6">
                <label class="text-dark-50 font-weight-bold mb-0" id="paymentStatus">All/Payment Status</label>
                <select name="paymentStatus"
                        id="paymentStatus"
                        class="choice form-control form-controller-solid datatable-input">
                    <option value=""> All / Payment Status</option>
                    <option value="Payment Due" <?php echo Arrays::isSelected(SearchForm::$SearchFields->paymentStatus, 'Payment Due') ?>>
                        Payment Due
                    </option>
                    <option value="Paid" <?php echo Arrays::isSelected(SearchForm::$SearchFields->paymentStatus, 'Paid') ?>>
                        Paid
                    </option>
                    <option value="Delinquent" <?php echo Arrays::isSelected(SearchForm::$SearchFields->paymentStatus, 'Delinquent') ?>>
                        Delinquent
                    </option>
                </select>
            </div>
        <?php } ?>
        <div class="col-lg-3 mb-lg-0 mb-6">
            <label class="text-dark-50 font-weight-bold  mb-0" for="noOfRecordsPerPage">No Of Records Per Page</label>
            <select name="noOfRecordsPerPage"
                    id="noOfRecordsPerPage"
                    class="form-control"
                    onchange="resetPageNum('<?php echo pipeline::$pageNumber ?>')">
                <option value="25" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '25') ?>>
                    Show 25 per page
                </option>
                <option value="50" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '50') ?>>
                    Show 50 per page
                </option>
                <option value="75" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '75') ?>>
                    Show 75 per page
                </option>
                <option value="100" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '100') ?>>
                    Show 100 per page
                </option>
            </select>
        </div>
    <?php } ?>

    <?php if (SearchForm::$EnableForm) { ?>
    <?php if (SearchForm::$PCName && SearchForm::CheckSearchField('listAllPC_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 listAllPC_Filter">
            <label class="text-dark-50 font-weight-bold  mb-0" for="PCName">
                List all PCs
            </label>
            <div class="input-group">
                <input type="text"
                       name="PCName"
                       id="PCName"
                       class="form-control form-controller-solid datatable-input"
                       value="<?php echo Strings::stripQuote(SearchForm::$SearchFields->PCName) ?>"
                       size="23"
                       placeholder="Type PC Name To Search"
                       autocomplete="off"
                       onblur="if(this.value === '') { document.LMRReport.searchPCID.value = 0; }"/>
                <div class="input-group-append"><span class="input-group-text"><span class="cursor-pointer"
                                                                                     onclick="listAllPCs();"><i
                                    class="flaticon-refresh text-dark"></i></span></span>
                </div>
            </div> <!-- processing company Div -->
        </div>
    <?php } ?>

    <?php if (PageVariables::$userRole == 'Branch') { ?>
        <?php if (SearchForm::$branchName) { ?>
            <?php if (SearchForm::CheckSearchField('listAllBranch_Filter')) { ?>
                <div class="col-lg-3 mb-lg-0 mb-6 listAllBranch_Filter">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="branchName">
                        List All Branches
                    </label>
                    <div class="input-group">
                        <input type="text"
                               name="branchName"
                               id="branchName"
                               class="form-control form-controller-solid datatable-input"
                               value="<?php echo Strings::stripQuote(SearchForm::$SearchFields->branchName) ?>"
                               size="25" placeholder="Type Branch Name To Search"
                               autocomplete="off"
                               onblur="if(this.value === '') { document.LMRReport.branchId.value = '3178f12db7e77c19'; }"/>
                        <input type="hidden"
                               id="branchId"
                               name="branchId"
                               value="<?php echo cypher::myEncryption(SearchForm::$SearchFields->branchId) ?>">
                        <div class="input-group-append cursor-pointer"><span class="input-group-text"><span
                                        class="cursor-pointer"
                                        onclick="listAllBranches();"><i
                                            class="flaticon-refresh text-dark"></i></span></span>
                        </div>
                    </div>
                </div>
            <?php } ?>
        <?php } else { ?>
            <input type="hidden"
                   id="branchId"
                   name="branchId"
                   value="<?php echo cypher::myEncryption(SearchForm::$SearchFields->branchId) ?>"/>
        <?php } ?>
    <?php } else { ?>
        <?php if (SearchForm::CheckSearchField('listAllBranch_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 listAllBranch_Filter">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="eId">List All Branches</label>
                    <select class="form-control"
                            id="eId"
                            name="eId[]"
                            multiple="multiple">
                        <?php foreach (SearchForm::$selectedBranchesInfo as $eachBranchInfo) { ?>
                            <option value="<?php echo cypher::myEncryption($eachBranchInfo['executiveId']); ?>"
                                    selected
                            >
                                <?php echo $eachBranchInfo['LMRExecutive'] . ' - ' . $eachBranchInfo['executiveEmail']; ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php } ?>
    <?php } ?>


    <?php if (SearchForm::$broker) { ?>
        <?php if (SearchForm::CheckSearchField('listAllBroker_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 listAllBroker_Filter">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="broker">List all Brokers</label>
                    <select class="form-control " id="broker" name="broker[]" multiple="multiple">
                        <?php foreach (SearchForm::$selectedBrokersInfo as $eachBrokerInfo) { ?>
                            <option value="<?php echo($eachBrokerInfo->brokerNumber); ?>"
                                    selected><?php echo $eachBrokerInfo->bName . ' ' . $eachBrokerInfo->bLName . ' - ' . $eachBrokerInfo->bEmail; ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php } ?>

        <?php if (!PageVariables::$externalBroker && SearchForm::CheckSearchField('listAllLoanOfficer_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 listAllLoanOfficer_Filter">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="loanOfficerSearch">List all Loan
                        Officers</label>
                    <select class="form-control " id="loanOfficerSearch" name="loanOfficerSearch[]"
                            multiple="multiple">
                        <?php foreach (SearchForm::$selectedLoanOfficerInfo as $eachLoanOfficer) { ?>
                            <option value="<?php echo($eachLoanOfficer->brokerNumber); ?>"
                                    selected><?php echo $eachLoanOfficer->bName . ' ' . $eachLoanOfficer->bLName . ' - ' . $eachLoanOfficer->bEmail; ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php } ?>

    <?php } ?>

    <?php if (SearchForm::CheckSearchField('listAllEmployees_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 listAllEmployees_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="employeeId">List all Employees</label>
                <select class="form-control "
                        id="employeeId"
                        name="employeeId[]"
                        multiple="multiple">
                    <?php foreach (SearchForm::$selectedEmployeeList as $eachEmployee) { ?>
                        <option value="<?php echo($eachEmployee->AID); ?>"
                                selected><?php echo $eachEmployee->processorName . '(' . $eachEmployee->role . ')'; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('clientBilling_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 clientBilling_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="billingDueDate">Client Billing</label>
                <select name="billingDueDate" class="form-control form-controller-solid  chzn-select "
                        data-placeholder="Select Client Billing"
                        id="billingDueDate">
                    <option value=""></option>
                    <option value="pastDue" <?php echo Arrays::isSelected('pastDue', SearchForm::$SearchFields->billingDueDate) ?>>
                        Past Due
                    </option>
                    <option value="thisWeek" <?php echo Arrays::isSelected('thisWeek', SearchForm::$SearchFields->billingDueDate) ?>>
                        Due this week
                    </option>
                    <option value="twoWeek" <?php echo Arrays::isSelected('twoWeek', SearchForm::$SearchFields->billingDueDate) ?>>
                        Due in 2 weeks
                    </option>
                    <option value="threeWeek" <?php echo Arrays::isSelected('threeWeek', SearchForm::$SearchFields->billingDueDate) ?>>
                        Due in 3 weeks
                    </option>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('fileIdle_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 fileIdle_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="staleDay">File Idle</label>
                <select name="staleDay" id="staleDay" class="form-control form-controller-solid chzn-select"
                        data-placeholder="Select File Idle">
                    <option value=""></option>
                    <?php foreach (glStaleFileArray::$glStaleFileArray as $_staleDay) { ?>
                        <option value="<?php echo $_staleDay ?>" <?php echo SearchForm::$SearchFields->staleDay == $_staleDay ? 'selected' : ''; ?>>
                            > <?php echo $_staleDay ?>Days
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('notesType_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 notesType_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="notesType">Notes Type</label>
                <select name="notesType[]"
                        id="notesType"
                        class="form-control form-controller-solid datatable-input chzn-select"
                        data-placeholder="Notes Type">
                    <option value=""></option>
                    <?php foreach (glNotesTypeArray::$glNotesTypeArray as $key => $noteType) { ?>
                        <option value="<?php echo trim($key) ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->notesType, $key) ?>><?php echo trim($noteType) ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('priorityLevel_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 priorityLevel_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="priorityLevel">Priority</label>
                <select name="priorityLevel" id="priorityLevel"
                        class="form-control form-controller-solid chzn-select " data-placeholder="Select Priority">
                    <option value=""></option>
                    <?php foreach (priorityLevelArray::$priorityLevelArray as $tempPriority) { ?>
                        <option value="<?php echo $tempPriority ?>" <?php echo Arrays::isSelected($tempPriority, SearchForm::$SearchFields->priorityLevel) ?>><?php echo $tempPriority ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::$activeFile) { ?>
        <?php if (SearchForm::CheckSearchField('activeDeactiveFile_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 activeDeactiveFile_Filter">
                <label class="text-dark-50 font-weight-bold  mb-0" for="activeFile">Active/Inactive Files</label>
                <div class="form-group">
                    <select name="activeFile" class="form-control form-controller-solid  chzn-select"
                            data-placeholder="Select Active/Inactive Files"
                            id="activeFile">
                        <option value="1" <?php echo Arrays::isSelected('1', SearchForm::$SearchFields->activeFile); ?>><?php if (PageVariables::$userRole == 'Auditor' || PageVariables::$userRole == 'CFPB Auditor' || PageVariables::$userRole == 'Auditor Manager') { ?>Submitted<?php } else { ?>Active<?php } ?>
                            Files
                        </option>
                        <option value="0" <?php echo Arrays::isSelected('0', SearchForm::$SearchFields->activeFile); ?>>
                            Deactivated Files
                        </option>
                    </select>
                </div>
            </div>
        <?php } else { ?>
            <input type="hidden" name="activeFile" id="activeFile" value="1">
        <?php } ?>
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('noActiveDeactive_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 noActiveDeactive_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="noOfRecordsPerPage">Num Of Records Per
                    Page</label>
                <select name="noOfRecordsPerPage"
                        class="form-control form-controller-solid chzn-select"
                        data-placeholder="Select Num Of Records Per Page"
                        id="noOfRecordsPerPage"
                        onchange="resetPageNum('<?php echo pipeline::$pageNumber ?>')">
                    <option value="25" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '25') ?>>
                        Show 25 per page
                    </option>
                    <option value="50" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '50') ?>>
                        Show 50 per page
                    </option>
                    <option value="75" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '75') ?>>
                        Show 75 per page
                    </option>
                    <option value="100" <?php echo Arrays::isSelected(SearchForm::$SearchFields->noOfRecordsPerPage, '100') ?>>
                        Show 100 per page
                    </option>
                </select>
            </div>
        </div> <!-- file create Div-->
    <?php } ?>

    <?php if (SearchForm::$loanAuditStatus && SearchForm::CheckSearchField('loanAuditStatus_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 loanAuditStatus_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="loanAuditStatus">Loan Audit Status</label>
                <select name="loanAuditStatus"
                        id="loanAuditStatus"
                        class="form-control form-controller-solid datatable-input"
                        class="choice">
                    <option value="All"> - All File Loan Audit Status -</option>
                    <?php foreach (loanAuditStatusArray::$loanAuditStatusArray as $k => $v) { ?>
                        <option value="<?php echo $k; ?>" <?php echo Arrays::isSelected(SearchForm::$SearchFields->loanAuditStatus, $k) ?>><?php echo $v; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::$CFPBAuditStatus && SearchForm::CheckSearchField('loanAuditStatus_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 loanAuditStatus_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="loanAuditStatus">Loan Audit Status</label>
                <select name="CFPBAuditStatus"
                        id="loanAuditStatus"
                        class="form-control form-controller-solid  datatable-input"
                        class="choice">
                    <option value="All"> - All File CFPB Audit Status -</option>
                    <?php foreach (CFPBAuditStatusArray::$CFPBAuditStatusArray as $k => $v) { ?>
                        <option value="<?php echo $k; ?>" <?php echo Arrays::isSelected(SearchForm::$SearchFields->CFPBAuditStatus, $k) ?>><?php echo $v; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('leadSource_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-2 leadSource_Filter">
            <label class="text-dark-50 font-weight-bold  mb-0" for="leadSource">List
                all Lead Source</label>
            <div class="input-group">
                <input type="text"
                       name="leadSource"
                       class="form-control form-controller-solid "
                       id="leadSource"
                       value="<?php echo Strings::stripQuote(SearchForm::$SearchFields->leadSource) ?>"
                       size="23"
                       autocomplete="off"
                       placeholder="Type lead source to search"
                />
                <div class="input-group-append"><span class="input-group-text"><span
                                class="cursor-pointer"
                                onclick="listAllLeadSources(this.value);"><i
                                    class="flaticon-refresh text-dark"></i></span></span></div>
            </div>
        </div> <!-- Lead Source Div -->
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('referringParty_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 referringParty_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0 "
                       for="referringParty">Referring Party</label>
                <input type="text"
                       name="referringParty"
                       class="form-control form-controller-solid  "
                       id="referringParty"
                       value="<?php echo SearchForm::$SearchFields->referringParty; ?>"
                       size="23"
                       autocomplete="off"
                       placeholder="Type broker/referring party."/>
            </div>
        </div>
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('workflow_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 workflow_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold mb-0"
                       for="WFID">Select Workflows <img class="WFStepsLoaderDiv hide" src="<?php echo CONST_SITE_URL; ?>assets/images/ajax-loader.gif" alt=""></label>
                <select name="WFID[]"
                        id="WFID"
                        class="form-control form-controller-solid chzn-select "
                        multiple
                        data-placeholder="Select Workflows"
                        onchange="getWorkflowStepsForPipeline(this.value, '<?php echo PageVariables::$PCID ?>')">
                    <option value=""></option>
                    <?php
                    foreach (SearchForm::$PCWorkflow as $item) { ?>
                        <option value="<?php echo trim($item->WFID) ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->WFID, trim($item->WFID)) ?>><?php echo trim($item->WFName) ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </div>
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('workflowStep_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 workflowStep_Filter"
             onclick="validateWorkflowForPipeline()">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="WFSId">Select Completed Workflow Steps </label>
                <div class="WFStepsDiv">
                </div>
                <select name="WFSId[]"
                        id="WFSId"
                        class="form-control form-controller-solid chzn-select"
                        multiple
                        data-placeholder="Select Completed Workflow Steps">
                    <option value=""></option>

                    <?php foreach (SearchForm::$PCWorkflowSteps as $eachWorkflowId => $workflowStepsArray) { ?>
                        <optgroup label="<?php echo SearchForm::$workflowInfoById[$eachWorkflowId]->WFName; ?>">
                            <?php foreach ($workflowStepsArray as $eachWorkFlowStep) {
                                if ($eachWorkFlowStep->LMRID == 0) { ?>
                                    <option value="<?php echo trim($eachWorkFlowStep->WFSID) ?>"
                                        <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->WFSId, $eachWorkFlowStep->WFSID) ?>
                                    ><?php echo trim($eachWorkFlowStep->steps) ?></option>
                                <?php }
                            } ?>
                        </optgroup>
                    <?php } ?>

                </select>

            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('workflowStep_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 workflowStep_Filter"
             onclick="validateWorkflowForPipeline()">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="WFSNotCompletedId">
                    Select Not Completed Workflow Steps
                </label>
                <select name="WFSNotCompletedId[]"
                        id="WFSNotCompletedId"
                        class="form-control form-controller-solid chzn-select"
                        multiple
                        data-placeholder="Select Not Completed Workflow Steps">
                    <option value=""></option>
                    <?php foreach (SearchForm::$PCWorkflowSteps as $eachWorkflowId => $workflowStepsArray) { ?>
                        <optgroup label="<?php echo SearchForm::$workflowInfoById[$eachWorkflowId]->WFName; ?>">
                            <?php foreach ($workflowStepsArray as $eachWorkFlowStep) { ?>
                                <option value="<?php echo trim($eachWorkFlowStep->WFSID) ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->WFSNotCompletedId, trim($eachWorkFlowStep->WFSID)) ?>><?php echo trim($eachWorkFlowStep->steps) ?></option>
                            <?php } ?>
                        </optgroup>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('propertyState_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 propertyState propertyState_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="propertyState">Property State</label>
                <select data-placeholder=" - Select Property State - "
                        name="propertyState[]"
                        id="propertyState"
                        class="chzn-select form-control form-controller-solid "
                        multiple="">
                    <?php $stateArray = Arrays::fetchStates();
                    foreach ($stateArray as $item) {
                        $sOpt = Arrays::isSelectedArray(SearchForm::$SearchFields->propertyState, $item['stateCode']);
                        echo "<option value=\"" . $item['stateCode'] . "\" " . $sOpt . '>' . $item['stateName'] . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div> <!-- state to search Div -->
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('fileType_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 fileType_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="multipleModuleCode">Select File Types</label>
                <select data-placeholder="All File Types"
                        name="multipleModuleCode[]"
                        id="multipleModuleCode"
                        class="chzn-select  form-control form-controller-solid  choice_b"
                        multiple=""
                    <?php if (PageVariables::$userGroup == 'Super') { ?> onchange="getLoanPrograms();" <?php } ?> >
                    <?php foreach (SearchForm::$PCModules as $item) { ?>
                        <option value="<?php echo trim($item->moduleCode) ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->multipleModuleCode, $item->moduleCode) ?>><?php echo trim($item->moduleName); ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('LMRClientType_Filter')) { ?>

        <?php
        $serviceTypeLabel = '- Select Service Type -';
        if (in_array('HMLO', SearchForm::$PCModuleInfoKeys)) {
            $serviceTypeLabel = '- Select Loan Programs -';
        }
        ?>

        <div class="col-lg-3 mb-lg-0 mb-6  LMRClientType LMRClientType_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="LMRClientType">Select Loan Programs </label>
                <select data-placeholder="<?php echo $serviceTypeLabel ?>"
                        name="LMRClientType[]"
                        id="LMRClientType"
                        class="chzn-select form-control form-controller-solid " multiple="">
                    <?php
                    if (PageVariables::$userRole == 'Super' && PageVariables::$PCID == 0) {
                        foreach (SearchForm::$loanProgramInfo as $item) {
                            $sel = '';
                            if (in_array($item->serviceType, SearchForm::$SearchFields->LMRClientType)) {
                                $sel = ' selected ';
                            }
                            echo "<option value=\"" . $item->serviceType . "\" $sel>" . $item->serviceType . '</option>';
                        }

                    } else {
                    $tempLMRClientModuleCode = null;
                    foreach (SearchForm::$PCClientTypeInfoArray as $PCClientType) {
                    $LMRClientCode = trim($PCClientType->LMRClientType);
                    $LMRClientTypeVal = trim($PCClientType->serviceType);
                    $LMRClientModuleCode = trim($PCClientType->moduleCode);
                    if ($tempLMRClientModuleCode != $LMRClientModuleCode) {
                    if ($tempLMRClientModuleCode) {
                        echo '</optgroup>';
                    }
                    ?>
                    <optgroup label="<?php echo trim($PCClientType->moduleName) ?>">
                        <?php } ?>
                        <option value="<?php echo $LMRClientCode ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->LMRClientType, $LMRClientCode) ?>><?php echo $LMRClientTypeVal ?></option>
                        <?php
                        $tempLMRClientModuleCode = $LMRClientModuleCode;
                        }
                        }

                        ?>
                    </optgroup>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (PageVariables::$userGroup == 'Employee' || PageVariables::$userGroup == 'Super') { ?>
        <?php if (SearchForm::CheckSearchField('LMRInternalClientType_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 LMRInternalClientType LMRInternalClientType_Filter">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="LMRInternalClientType">Select Internal Loan
                        Programs </label>
                    <select data-placeholder="Select Internal Loan Programs"
                            name="LMRInternalClientType[]"
                            id="LMRInternalClientType"
                            class="chzn-select form-control form-controller-solid " multiple="">
                        <?php
                        foreach (SearchForm::$PCInternalServicesModule as $eachModule => $eachModuleInternalLoanArray) { ?>
                            <optgroup label="<?php echo trim($eachModule) ?>">
                                <?php foreach ($eachModuleInternalLoanArray as $eachInternalLoan) { ?>
                                    <option value="<?php echo $eachInternalLoan->STCode; ?>"
                                        <?php if (in_array($eachInternalLoan->STCode, SearchForm::$SearchFields->LMRInternalClientType ?? [])) {
                                            echo ' selected ';
                                        } ?>
                                    ><?php echo $eachInternalLoan->serviceType; ?></option>
                                <?php } ?>
                            </optgroup>
                        <?php }
                        ?>
                    </select>
                </div>
            </div>
        <?php } ?>
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('clientSubstatus_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 statusOpt clientSubstatus_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="statusOpt">Client File Substatus</label>
                <select data-placeholder=" - Select Client File Substatus - "
                        name="statusOpt[]"
                        id="statusOpt"
                        class="chzn-select form-control form-controller-solid "
                        multiple="">
                    <?php
                    /* @var PCSubStatusInfo[][][] $tempModulesCodeArr */
                    $tempModulesCodeArr = [];
                    foreach (SearchForm::$PCSubStatusInfo as $item) {
                        $tempModulesCodeArr[$item->moduleName][$item->category][] = $item;
                    }

                    foreach ($tempModulesCodeArr as $moduleCode => $categoryArr) {
                        ?>
                        <option value="" class="optnGrp" disabled><?php echo $moduleCode ?></option>
                        <?php foreach ($categoryArr as $category => $substatusArr) { ?>
                            <optgroup label="<?php echo $category ?>">
                                <?php foreach ($substatusArr as $item) { ?>
                                    <option value="<?php echo $item->PFSID ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->statusOpt, $item->PFSID) ?>><?php echo $item->substatus; ?></option>
                                <?php } ?>
                            </optgroup>
                            <?php
                        }
                    }
                    ?>
                </select>
            </div>
        </div>
    <?php } ?>
</div> <!-- end of row -->

<div class="row mb-2">

    <div class="col-lg-3 mb-lg-0 mb-6 searchTerm">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="searchTerm">Search Text</label>
            <input type="text" name="searchTerm" id="searchTerm"
                   value="<?php echo Strings::stripQuote(SearchForm::$SearchFields->searchTerm) ?>"
                   class="search form-control form-controller-solid " size="40"
                   placeholder="Type text to search & select field" autocomplete="off"
                   />
        </div>
    </div>
    <div class="col-lg-3 mb-lg-0 mb-6 searchField">
        <div class="form-group">
            <label class="text-dark-50 font-weight-bold  mb-0" for="searchField">Select Field To Search</label>
            <select data-placeholder=" - Select Field to search - "
                    name="searchField[]"
                    id="searchField"
                    class="chzn-select form-control form-controller-solid"
                <?php if (glCustomJobForProcessingCompany::canSearchMultipleFields(PageVariables::$PCID)) { ?>
                    multiple
                <?php } ?>
                    >
                <?php if (!glCustomJobForProcessingCompany::canSearchMultipleFields(PageVariables::$PCID)) { ?>
                    <option value="">Select One...</option>
                <?php } ?>
                <?php
                $searchKeyFields = array_keys(SearchForm::$searchFields);
                foreach ($searchKeyFields as $field) {
                    $sOpt = Arrays::isSelectedArray(SearchForm::$SearchFields->searchField, $field);
                    echo "<option value=\"" . $field . "\" " . $sOpt . '>' . SearchForm::$searchFields[$field] . '</option>';
                }
                ?>
            </select>
        </div>
    </div>

    <?php if (SearchForm::$multiplePrimaryStatus) { ?>
        <?php if (SearchForm::CheckSearchField('multiplePrimaryStatus_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 multiplePrimaryStatus multiplePrimaryStatus_Filter">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="multiplePrimaryStatus">
                        Primary Status
                    </label>
                    <select data-placeholder=" - Select File Primary Status - "
                            name="multiplePrimaryStatus[]"
                            id="multiplePrimaryStatus"
                            class="chzn-select form-control form-controller-solid "
                            multiple="">
                        <?php foreach (SearchForm::$PCStatusInfoArray as $module => $items) { ?>
                            <optgroup label="<?php echo $module ?>">
                                <?php foreach ($items as $item) { ?>
                                    <option
                                            value="<?php echo trim($item->PSID) ?>"
                                        <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->multiplePrimaryStatus, $item->PSID) ?>
                                    ><?php echo trim($item->primaryStatus) ?></option>
                                <?php } ?>
                            </optgroup>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php } ?>
    <?php } ?>

    <?php if (SearchForm::$lenderName) { ?>
        <?php if (SearchForm::CheckSearchField('lenderName_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 lenderName lenderName_Filter">
                <label class="text-dark-50 font-weight-bold  mb-0" for="lenderName">Lender name</label>
                <div class="form-group">
                    <select data-placeholder=" - Type Lender Name To Search - "
                            name="lenderName[]"
                            id="lenderName"
                            class="chzn-select form-control form-controller-solid "
                            multiple="">
                        <?php foreach (SearchForm::$lenderListArray as $item) { ?>
                            <option value="<?php echo trim($item['company']) ?>"
                                <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->lenderName, addslashes(trim($item['company']))) ?>
                            ><?php echo trim($item['company']) ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        <?php } ?>
    <?php } ?>

    <?php if (SearchForm::$WFStepID) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 WFStepID WFStepID_Filter <?php echo SearchForm::CheckSearchField('WFStepID_Filter'); ?>">
            <label class="text-dark-50 font-weight-bold mb-0" for="WFStepID">Current Secondary Status</label>
            <div class="form-group">
                <select data-placeholder=" - Current Secondary Status - "
                        name="WFStepID[]"
                        id="WFStepID"
                        class="chzn-select form-control form-controller-solid "
                        multiple="">
                    <?php foreach (SearchForm::$PCWorkflow as $workflowItem) {
                    $tempArray = [];
                    $t = 0;
                    $workflowID = trim($workflowItem->WFID);
                    $WFName = trim($workflowItem->WFName);
                    if (array_key_exists($workflowID, SearchForm::$PCWorkflowSteps)) {
                        $tempArray = SearchForm::$PCWorkflowSteps[$workflowID];
                    }
                    ?>

                    <?php foreach ($tempArray as $workflowStep) { ?>
                    <?php if ($t == 0) { ?>
                    <optgroup label="<?php echo $WFName ?>" style="text-align:center;">
                        <?php } ?>

                        <option value="<?php echo $workflowStep->WFSID ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->WFStepID, $workflowStep->WFSID) ?>><?php echo $workflowStep->steps ?></option>
                        <?php $t++;
                        } ?>
                        <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::$servicingStatusCode) {
        $glServicingSubStatus = glServicingSubStatus::$glServicingSubStatus;
        ?>
        <div class="col-lg-3 mb-lg-0 mb-6 servicingStatus_Filter <?php echo SearchForm::CheckSearchField('servicingStatus_Filter'); ?> ">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="servicingStatusCode">Select Servicing
                    Status</label>
                <select data-placeholder="Servicing Status"
                        name="servicingStatusCode[]"
                        id="servicingStatusCode"
                        class="chzn-select  form-control form-controller-solid  choice_b"
                        multiple="">
                    <?php foreach ($glServicingSubStatus as $servicingsubStatusKey => $servicingsubStatusValue) { ?>
                        <option value="<?php echo trim($servicingsubStatusKey) ?>" <?php echo Arrays::isSelectedArray(SearchForm::$SearchFields->servicingStatusCode, $servicingsubStatusKey) ?>><?php echo trim($servicingsubStatusValue); ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>
    <?php if (SearchForm::CheckSearchField('paymentBased_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 paymentBased_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="staleDay">Payment Based</label>
                <select name="paymentBased" id="paymentBased" class="form-control form-controller-solid chzn-select"
                        data-placeholder="Select Payment Based">
                    <option value=""></option>
                    <?php foreach (glPaymentBasedArray::$glPaymentBasedArray as $paymentBasedKey => $paymentBasedValue) { ?>
                        <option value="<?php echo $paymentBasedKey ?>" <?php echo SearchForm::$SearchFields->paymentBased == $paymentBasedKey ? 'selected' : ''; ?>>
                             <?php echo $paymentBasedValue ?>
                        </option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>


    <?php if (SearchForm::CheckSearchField('contacts_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 contacts_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="contactSearchId">List all Contacts</label>
                <select class="form-control " id="contactSearchId" name="contactSearchId[]" multiple="multiple">
                    <?php foreach (SearchForm::$selectedContactsInfo as $eachContactInfo) { ?>
                            <option value="<?php echo($eachContactInfo['CID']); ?>"
                                    selected><?php echo $eachContactInfo['contactName'] . ' ' . $eachContactInfo['contactLName']; ?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
    <?php } ?>
</div>
<div class="row">


    <?php if (SearchForm::CheckSearchField('fileCreatedDate_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 fileCreatedDate_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="stDate">File Created</label>
                <div class="input-daterange input-group">
                    <input type="text"
                           name="stDate"
                           id="stDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->stDate ?>"
                           size="12"
                           maxlength="10" placeholder="From"/>
                    <div class="input-group-append">
															<span class="input-group-text">
																<i class="la la-ellipsis-h"></i>
															</span>
                    </div>
                    <input type="text"
                           name="endDate"
                           id="endDate"
                           class=" form-control form-controller-solid dateClass"
                           value="<?php echo SearchForm::$SearchFields->endDate ?>"
                           size="10"
                           maxlength="10" placeholder="To"/><label for="endDate"></label>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('fileModifiedDate_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 fileModifiedDate_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="modifiedStartDate">File Modified</label>
                <div class="input-daterange input-group">
                    <input type="text"
                           name="modifiedStartDate"
                           class="form-control form-controller-solid  dateClass"
                           id="modifiedStartDate"
                           value="<?php echo SearchForm::$SearchFields->modifiedStartDate ?>"
                           size="13"
                           maxlength="10"
                           placeholder="From"/>
                    <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                    </div>
                    <input type="text"
                           name="modifiedEndDate"
                           id="modifiedEndDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->modifiedEndDate ?>"
                           size="10"
                           maxlength="10"
                           placeholder="To"/><label for="modifiedEndDate"></label>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('closingDate_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 closingDate_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="closingStartDate">Closing Date</label>
                <div class="input-daterange input-group">
                    <input type="text" name="closingStartDate" id="closingStartDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->closingStartDate ?>" size="12" maxlength="10"
                           placeholder="From"/>
                    <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                    </div>
                    <input type="text" name="closingEndDate" id="closingEndDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->closingEndDate ?>" size="10" maxlength="10"
                           placeholder="To"/><label for="closingEndDate"></label>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('maturityDate_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 maturityDate_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="maturityStartDate">Maturity Date</label>
                <div class="input-daterange input-group">
                    <input type="text" name="maturityStartDate" id="maturityStartDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->maturityStartDate ?>" size="12" maxlength="10"
                           placeholder="From"/>
                    <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                    </div>
                    <input type="text" name="maturityEndDate" id="maturityEndDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->maturityEndDate ?>" size="10" maxlength="10"
                           placeholder="To"/><label for="maturityEndDate"></label>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('appraisalOrderDate_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 appraisalOrderDate_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="appraisalStartDate">Appraisal Order Date</label>
                <div class="input-daterange input-group">
                    <input type="text"
                           name="appraisalStartDate"
                           id="appraisalStartDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->appraisalStartDate ?>"
                           size="12"
                           maxlength="10"
                           placeholder="From"/>
                    <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                    </div>
                    <input type="text" name="appraisalEndDate" id="appraisalEndDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->appraisalEndDate ?>" size="10" maxlength="10"
                           placeholder="To"/><label for="appraisalEndDate"></label>
                </div>
            </div>
        </div>
    <?php } ?>

    <?php if (SearchForm::CheckSearchField('receivedDate_Filter')) { ?>
        <div class="col-lg-3 mb-lg-0 mb-6 receivedDate_Filter">
            <div class="form-group">
                <label class="text-dark-50 font-weight-bold  mb-0" for="receivedStartDate">Received Date</label>
                <div class="input-daterange input-group">
                    <input type="text"
                           name="receivedStartDate"
                           id="receivedStartDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->receivedStartDate ?>"
                           size="12"
                           maxlength="10"
                           placeholder="From"/>
                    <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="la la-ellipsis-h"></i>
                        </span>
                    </div>
                    <input type="text"
                           name="receivedEndDate"
                           id="receivedEndDate"
                           class="form-control form-controller-solid  dateClass"
                           value="<?php echo SearchForm::$SearchFields->receivedEndDate ?>"
                           size="10"
                           maxlength="10"
                           placeholder="To"/><label for="receivedEndDate"></label>
                </div>
            </div>
        </div>
    <?php } ?>


    <?php if (SearchForm::$paymentStatus) { ?>
        <?php if (SearchForm::CheckSearchField('paymentStatus_Filter')) { ?>
            <div class="col-lg-3 mb-lg-0 mb-6 paymentStatus_Filter">
                <div class="form-group">
                    <label class="text-dark-50 font-weight-bold  mb-0" for="paymentStatus">Payment Status</label>
                    <select name="paymentStatus"
                            class="form-control form-controller-solid  choice"
                            id="paymentStatus">
                        <option value=""> All / Payment Status</option>
                        <option value="Payment Due" <?php echo Arrays::isSelected(SearchForm::$SearchFields->paymentStatus, 'Payment Due') ?>>
                            Payment Due
                        </option>
                        <option value="Paid" <?php echo Arrays::isSelected(SearchForm::$SearchFields->paymentStatus, 'Paid') ?>>
                            Paid
                        </option>
                        <option value="Delinquent" <?php echo Arrays::isSelected(SearchForm::$SearchFields->paymentStatus, 'Delinquent') ?>>
                            Delinquent
                        </option>
                    </select>
                </div>
            </div>
        <?php } ?>
    <?php } ?>


    <div class="col-lg-3 mb-lg-0 mb-6 d-none">
        <input class="btn btn-primary mr-2" type="submit" name="but_submit" value="Search"
               onclick="resetPageNum('1');">
        <input class="btn btn-secondary" type="reset" name="but_reset" value="Reset"
               onclick="clearAllFormValues();">
    </div> <!-- submit button Div-->

    <?php } ?>

</div>
