<?php

namespace pages\backoffice\loan\fm34;

use models\Controllers\backoffice\LMRequest;
use models\lendingwise\db\tblFileLOLiabilitiesInfo_db;
use models\lendingwise\tblFileLOChekingSavingInfo;
use models\lendingwise\tblFileLOLiabilitiesInfo;
use pages\backoffice\loan\fm34\classes\BorrowerEmploymentController;
use pages\backoffice\loan\fm34\classes\FM34Controller;

?>

<script src="/assets/js/importExportFM34/configFM34.Servicing.js" type="text/javascript"></script>
<script src="/assets/js/importExportFM34/funcFM34.js" type="text/javascript"></script>
<script src="/assets/js/importExportFM34/globalFM34.Servicing.js" type="text/javascript"></script>
<script src="/assets/js/importExportFM34.js" type="text/javascript"></script>
<script src="/backoffice/api_v2/js/address_lookup.js" type="text/javascript"></script>

<label for="file-download" style="margin-top: 7px;">
    <span title="fannie mae 3.4 export"
          onclick="window.location.href='/mismo/fm34Export.php?lId=<?= $_REQUEST['lId'] ?? '' ?>'"
          class="cursor-pointer tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary">
        <img src="/assets/images/fm32red.png" style="width: 20px;" alt="fannie mae 3.4 export"> Fannie Mae 3.4 Export
    </span>
</label>
<label for="file-upload" class="" style="margin-top: 7px;">
    <span title="fannie mae 3.4 import"
          onclick="$('#file-upload').trigger('click');"
          class="tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary">
        <img src="/assets/images/fm32blue.png" style="width: 20px;" alt="fannie mae 3.4 import"> Fannie Mae 3.4 Import
    </span>
</label>
<input id="file-upload" type="file" style="display: none" onchange="handleImportChange(this);">
<div id="importAlert" class="alert hidden col-2 ms-auto" role="alert"></div>

<form id="loanModForm" method="post">
    <div style="text-align: right">
        <input type="submit" class="save btn btn-primary" value="Save"/>
    </div>

    <?php foreach (fm34::$FM34Controller->FormFields as $section => $fields) { ?>
        <h3><?php echo $section; ?></h3>
        <hr/>
        <div class="row">
            <?php foreach ($fields as $field) { ?>
                <?php echo $field->renderForDatabaseType(LMRequest::File()); ?>
            <?php } ?>
        </div>
    <?php } ?>


    <?php foreach (LMRequest::myFileInfo()->borEmploymentInfo() as $i => $item) { ?>
        <?php foreach (fm34::$BorrowerEmploymentController->FormFields as $section => $fields) { ?>
            <h3><?php echo $section; ?></h3>
            <hr/>
            <div class="row">
                <?php foreach ($fields as $field) { ?>
                    <?php echo $field->renderForDatabaseTypeIndexed($item, $i); ?>
                <?php } ?>
            </div>
        <?php } ?>
    <?php } ?>

    <?php foreach (LMRequest::myFileInfo()->fileLOChekingSavingInfo() ?? [new tblFileLOChekingSavingInfo()] as $i => $item) { ?>
        <?php foreach (fm34::$FileLOChekingSavingInfoController->FormFields as $section => $fields) { ?>
            <h3><?php echo $section; ?></h3>
            <hr/>
            <div class="row">
                <?php foreach ($fields as $field) { ?>
                    <?php echo $field->renderForDatabaseTypeIndexed($item, $i); ?>
                <?php } ?>
            </div>
        <?php } ?>
    <?php } ?>

    <?php foreach (LMRequest::myFileInfo()->liabilitiesInfo() ?? [new tblFileLOLiabilitiesInfo()] as $i => $item) { ?>
        <?php foreach (fm34::$LiabilitiesAndExpensesController->FormFields as $section => $fields) { ?>
            <h3><?php echo $section; ?></h3>
            <hr/>
            <div class="row">
                <?php foreach ($fields as $field) { ?>
                    <?php echo $field->renderForDatabaseTypeIndexed($item, $i); ?>
                <?php } ?>
            </div>
        <?php } ?>
    <?php } ?>

    <div style="text-align: right">
        <input type="submit" class="save btn btn-primary" value="Save"/>
    </div>
</form>

<style>
    div.form-field {
        padding: 1.0em;
    }

    div.form-field-header {
        font-weight: bold;
    }

    i.indicator {
        font-size: 1.0em;
        color: #3f4254;
    }

    .loanProgramHeader {
        font-weight: bold;
        padding: 0.1em;
        text-align: center;
        background-color: #000;
        color: #fff;
    }

</style>

<script>
    $(function () {
        // https://stackoverflow.com/questions/50058537/enable-submit-button-only-if-a-value-changes
        const inputs = document.querySelectorAll("input, select");
        for (const el of inputs) {
            el.oldValue = el.value + el.checked;
        }

        // Declares function and call it directly
        let setEnabled;
        (setEnabled = function () {
            let e = true;
            for (const el of inputs) {
                if (el.oldValue !== (el.value + el.checked)) {
                    e = false;
                    break;
                }
            }
            $(".save").prop('disabled', e);
        })();

        document.oninput = setEnabled;
        document.onchange = setEnabled;
    });

</script>