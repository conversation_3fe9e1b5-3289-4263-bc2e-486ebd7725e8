<?php

namespace pages\backoffice\loan\web_form\HMLO\classes;

use models\constants\gl\glHMLOCreditScoreRange;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glHMLOHouseType;
use models\constants\gl\glHMLOLoanTerms;
use models\constants\gl\glRateLockPeriod;
use models\Controllers\backoffice\LMRequest;
use models\types\strongType;

class fileCustomPCLoanTermsController extends strongType
{
    public static function Init()
    {

        $glHMLOExtensionOption = glHMLOExtensionOption::$glHMLOExtensionOption;
        glHMLOCreditScoreRange::$glHMLOCreditScoreRange = glHMLOCreditScoreRange::getCreditScoreRange(LMRequest::$PCID);
        $HMLOPCTransactionType = [];
        $HMLOPCPropertyType = [];
        $HMLOPCBasicEntityType = [];
        $HMLOPCExtnOption = [];
        $HMLOPCLoanTerm = [];
        $HMLOPCOccupancy = [];
        $HMLOPCState = [];
        $HMLOPCNiches = [];
        $HMLOPCBasicLoanInfo = null;
        $PCBasicLoanTabFileIdExists = [];
        $glHMLOExtensionOptionKey = [];
        $PCSelectedTransactionType = [];
        $PCSelectedExtnOption = [];
        $PCSelectedLoanTerm = [];
        $PCSelectedOccupancy = [];
        $PCBasicLoanTabLMRIDsExists = [];
        $fileHMLONewLoanInfo = null;
        $HMLOPCAmortizationValPCLoanTermsArray = [];
        $HMLOPCAmortizationValPCLoanTerms = [];

        $minRate = '';
        $maxRate = '';
        $originationPointsRate = '';
        $originationPointsValue = '';
        $processingFee = '';
        $brokerPointsRate = '';
        $brokerPointsValue = '';
        $appraisalFee = '';
        $applicationFee = '';
        $drawsSetUpFee = '';
        $estdTitleClosingFee = '';
        $miscellaneousFee = '';
        $closingCostFinanced = '';
        $PCBorrCreditScoreRange = '';

        $HMLOPCBasicRateLockPeriodInfo = [];
        $HMLOPCAmortizationValInfo = [];

        if (LMRequest::myFileInfo()) {
            $clientTypes = LMRequest::myFileInfo()->ClientTypes();

            $HMLOPCTransactionType = LMRequest::myFileInfo()->HMLOPCTransactionType($clientTypes);
            $HMLOPCPropertyType = LMRequest::myFileInfo()->HMLOPCPropertyType($clientTypes);
            $HMLOPCBasicEntityType = LMRequest::myFileInfo()->HMLOPCBasicEntityType($clientTypes);
            $HMLOPCExtnOption = LMRequest::myFileInfo()->HMLOPCExtnOption($clientTypes);
            $HMLOPCLoanTerm = LMRequest::myFileInfo()->HMLOPCLoanTerm($clientTypes);
            $HMLOPCOccupancy = LMRequest::myFileInfo()->HMLOPCOccupancy($clientTypes);
            $HMLOPCState = LMRequest::myFileInfo()->HMLOPCState($clientTypes);
            $HMLOPCNiches = LMRequest::myFileInfo()->HMLOPCNiches($clientTypes);
            $HMLOPCBasicLoanInfo = LMRequest::myFileInfo()->HMLOPCBasicLoanInfo($clientTypes);
            $PCBasicLoanTabFileIdExists = LMRequest::myFileInfo()->PCBasicLoanTabFileIdExists();
            $fileHMLONewLoanInfo = LMRequest::myFileInfo()->getFileHMLONewLoanInfo();
            $HMLOPCAmortizationValInfo = LMRequest::myFileInfo()->HMLOPCAmortizationValInfo($clientTypes);
            $HMLOPCBasicMinSeasoningPersonalBankruptcyInfo = LMRequest::myFileInfo()->HMLOPCBasicMinSeasoningPersonalBankruptcyInfo($clientTypes);
            $HMLOPCBasicMinSeasoningBusinessBankruptcyInfo = LMRequest::myFileInfo()->HMLOPCBasicMinSeasoningBusinessBankruptcyInfo($clientTypes);
            $HMLOPCBasicMinSeasoningForeclosureInfo = LMRequest::myFileInfo()->HMLOPCBasicMinSeasoningForeclosureInfo($clientTypes);
            $HMLOPCBasicSBALoanProductInfo = LMRequest::myFileInfo()->HMLOPCBasicSBALoanProductInfo($clientTypes);
            $HMLOPCBasicEquipmentTypeInfo = LMRequest::myFileInfo()->HMLOPCBasicEquipmentTypeInfo($clientTypes);
            $HMLOPCBasicEntitityStateFormationInfo = LMRequest::myFileInfo()->HMLOPCBasicEntitityStateFormationInfo($clientTypes);
            $HMLOPCBasicPaymentFrequencyInfo = LMRequest::myFileInfo()->HMLOPCBasicPaymentFrequencyInfo($clientTypes);
            $HMLOPCBasicLoanPurposeInfo = LMRequest::myFileInfo()->HMLOPCBasicLoanPurposeInfo($clientTypes);
            $HMLOPCBasicMinTimeInBusinessInfo = LMRequest::myFileInfo()->HMLOPCBasicMinTimeInBusinessInfo($clientTypes);
            $HMLOPCBasicRateLockPeriodInfo = LMRequest::myFileInfo()->HMLOPCBasicRateLockPeriodInfo($clientTypes);
            $HMLOPCBasicLoanExitStrategyInfo = LMRequest::myFileInfo()->HMLOPCBasicLoanExitStrategyInfo($clientTypes);
        }

        if (count($PCBasicLoanTabFileIdExists) > 0) {
            foreach ($PCBasicLoanTabFileIdExists as $i => $item) {
                $PCBasicLoanTabLMRIDsExists[] = $item->BLID;
            }
        }

        if (count($glHMLOExtensionOption) > 0) {
            $glHMLOExtensionOptionKey = array_keys($glHMLOExtensionOption);
        }

        foreach ($HMLOPCTransactionType as $k => $item) {
            $PCSelectedTransactionType[] = $item->transactionType;
        }

        if (count($PCSelectedTransactionType) > 0) {
            $gltypeOfHMLOLoanRequesting = $PCSelectedTransactionType;
        }

        foreach ($HMLOPCExtnOption as $k => $item) {
            $PCSelectedExtnOption[] = $item->extnOption;
        }

        if (count($PCSelectedExtnOption) > 0) {
            $glHMLOExtensionOptionKey = $PCSelectedExtnOption;
        }


        foreach ($HMLOPCLoanTerm as $k => $item) {
            $PCSelectedLoanTerm[] = $item->loanTerm;
        }

        if (count($PCSelectedLoanTerm) > 0) {
            glHMLOLoanTerms::$glHMLOLoanTerms = $PCSelectedLoanTerm;
        }

        foreach ($HMLOPCOccupancy as $k => $item) {
            $PCSelectedOccupancy[] = $item->occupancy;
        }

        if (count($PCSelectedOccupancy) > 0) {
            glHMLOHouseType::$glHMLOHouseType = $PCSelectedOccupancy;
        }


        foreach ($HMLOPCAmortizationValInfo as $k => $item) {
            $HMLOPCAmortizationValPCLoanTermsArray[] = $item->AmortizationVal;
        }
        if (count($HMLOPCAmortizationValPCLoanTermsArray) > 0) {
            $HMLOPCAmortizationValPCLoanTerms = $HMLOPCAmortizationValPCLoanTermsArray;
        }

        $HMLOPCBasicRateLockPeriodData = [];
        foreach ($HMLOPCBasicRateLockPeriodInfo as $HMLOPCBasicRateLockPeriod) {
            $HMLOPCBasicRateLockPeriodData[] = $HMLOPCBasicRateLockPeriod['rateLockPeriod'];
        }
        glRateLockPeriod::$glRateLockPeriod = sizeof($HMLOPCBasicRateLockPeriodData) ? $HMLOPCBasicRateLockPeriodData : glRateLockPeriod::$glRateLockPeriod;

        if ($fileHMLONewLoanInfo) {
            if ($HMLOPCBasicLoanInfo) {
                $minRate = $HMLOPCBasicLoanInfo->minRate;
                $maxRate = $HMLOPCBasicLoanInfo->maxRate;
                $originationPointsRate = $HMLOPCBasicLoanInfo->originationPointsRate;
                $originationPointsValue = $HMLOPCBasicLoanInfo->originationPointsValue;
                $processingFee = $HMLOPCBasicLoanInfo->processingFee;
                $brokerPointsRate = $HMLOPCBasicLoanInfo->brokerPointsRate;
                $brokerPointsValue = $HMLOPCBasicLoanInfo->brokerPointsValue;
                $appraisalFee = $HMLOPCBasicLoanInfo->appraisalFee;
                $applicationFee = $HMLOPCBasicLoanInfo->applicationFee;
                $drawsSetUpFee = $HMLOPCBasicLoanInfo->drawsSetUpFee;
                $estdTitleClosingFee = $HMLOPCBasicLoanInfo->estdTitleClosingFee;
                $miscellaneousFee = $HMLOPCBasicLoanInfo->miscellaneousFee;
                $closingCostFinanced = $HMLOPCBasicLoanInfo->closingCostFinanced;
                $PCBorrCreditScoreRange = $HMLOPCBasicLoanInfo->PCBorrCreditScoreRange;

                $valuationBPOFee = $HMLOPCBasicLoanInfo->valuationBPOFee;
                $valuationAVMFee = $HMLOPCBasicLoanInfo->valuationAVMFee;
                $creditReportFee = $HMLOPCBasicLoanInfo->creditReportFee;
                $backgroundCheckFee = $HMLOPCBasicLoanInfo->backgroundCheckFee;
                $floodCertificateFee = $HMLOPCBasicLoanInfo->floodCertificateFee;
                $documentPreparationFee = $HMLOPCBasicLoanInfo->documentPreparationFee;
                $wireFee = $HMLOPCBasicLoanInfo->wireFee;
                $servicingSetUpFee = $HMLOPCBasicLoanInfo->servicingSetUpFee;
                $taxServiceFee = $HMLOPCBasicLoanInfo->taxServiceFee;
                $floodServiceFee = $HMLOPCBasicLoanInfo->floodServiceFee;
                $inspectionFees = $HMLOPCBasicLoanInfo->inspectionFees;
                $projectFeasibility = $HMLOPCBasicLoanInfo->projectFeasibility;
                $dueDiligence = $HMLOPCBasicLoanInfo->dueDiligence;
                $UccLienSearch = $HMLOPCBasicLoanInfo->UccLienSearch;
                $otherFee = $HMLOPCBasicLoanInfo->otherFee;
                $taxImpoundsMonth = $HMLOPCBasicLoanInfo->taxImpoundsMonth;
                $taxImpoundsMonthAmt = $HMLOPCBasicLoanInfo->taxImpoundsMonthAmt;
                $taxImpoundsFee = $HMLOPCBasicLoanInfo->taxImpoundsFee;
                $insImpoundsMonthAmt = $HMLOPCBasicLoanInfo->insImpoundsMonthAmt;
                $insImpoundsFee = $HMLOPCBasicLoanInfo->insImpoundsFee;
                $thirdPartyFees = $HMLOPCBasicLoanInfo->thirdPartyFees;
                $insImpoundsMonth = $HMLOPCBasicLoanInfo->insImpoundsMonth;

                if ($PCBorrCreditScoreRange) {
                    glHMLOCreditScoreRange::$glHMLOCreditScoreRange = explode(',', $PCBorrCreditScoreRange);
                }

            }
        } else {
            if (count($HMLOPCBasicLoanInfo) > 0) {
                foreach ($HMLOPCBasicLoanInfo as $i => $item) {
                    $minRate = $item->minRate;
                    $maxRate = $item->maxRate;
                    $PCBorrCreditScoreRange = $item->PCBorrCreditScoreRange;

                    if ($PCBorrCreditScoreRange) {
                        glHMLOCreditScoreRange::$glHMLOCreditScoreRange = explode(',', $PCBorrCreditScoreRange);
                    }
                }
            }
        }


        /**
         * Get Custom Loan Guidelines From PC..
         */
        if (count($HMLOPCPropertyType) > 0) {
            $propertyTypeKeyArray = [];
            foreach ($HMLOPCPropertyType as $pt => $item) {
                $propertyTypeKeyArray[] = $item->propertyType;
            }
            LMRequest::$propertyTypeKeys = $propertyTypeKeyArray;
        }
        if (count($HMLOPCState) > 0) {
            $stateKeyArray = [];
            foreach ($HMLOPCState as $pt => $item) {
                $stateKeyArray[] = $item->stateCode;
            }
            LMRequest::$customLoanGuidelinesStateKeys = $stateKeyArray;
        }

        foreach ($HMLOPCBasicSBALoanProductInfo as $k => $item) {
            $HMLOPCBasicSBALoanProductInfoArray[] = $item->SBALoanProductVal;
        }

        foreach ($HMLOPCBasicEquipmentTypeInfo as $k => $item) {
            $HMLOPCBasicEquipmentTypeInfoArray[] = $item->equipmentTypeVal;
        }

        foreach ($HMLOPCBasicEntitityStateFormationInfo as $k => $item) {
            $HMLOPCBasicEntitityStateFormationInfoArray[] = $item->sateCode;
        }

        foreach ($HMLOPCBasicPaymentFrequencyInfo as $k => $item) {
            $HMLOPCBasicPaymentFrequencyInfoArray[] = $item->paymentFrequencyVal;
        }

        foreach ($HMLOPCBasicLoanPurposeInfo as $k => $item) {
            $HMLOPCBasicLoanPurposeInfoArray[] = $item->purposeName;
        }

        foreach ($HMLOPCBasicEntityType as $et) {
            $HMLOPCBasicEntityTypeInfoArray[] = $et->entityType;
        }
    }
}
