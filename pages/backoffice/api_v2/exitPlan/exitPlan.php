<?php

namespace pages\backoffice\api_v2\exitPlan;

use models\constants\gl\glHMLOExitStrategy;
use models\portals\BackofficePage;
use models\portals\PublicPage;
use models\standard\HTTP;

class exitPlan extends PublicPage
{

    public static function Get()
    {
        $loanProgram = trim($_REQUEST['loanProgram'] ?? null);
        $PCID = trim($_REQUEST['PCID'] ?? null);
        if (!$loanProgram || !$PCID) {
            HTTP::ExitJSON(['error' => 'Invalid Request'], HTTP::HTTP_STATUS_BAD_REQUEST);
        }
        $applicationExitOptions = glHMLOExitStrategy::getApplicationLoanExitOptions($PCID, $loanProgram);

        HTTP::ExitJSON([
            'applicationExitOptions' => $applicationExitOptions,
        ]);

    }
}
