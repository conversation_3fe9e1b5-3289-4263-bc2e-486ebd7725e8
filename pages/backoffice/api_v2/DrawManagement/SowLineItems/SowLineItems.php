<?php

namespace pages\backoffice\api_v2\DrawManagement\SowLineItems;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\cypher;
use models\composite\oDrawManagement\SowTemplateManager;

/**
 * Class SowLineItems
 *
 * API endpoint for updating and fetching SOW line items
 *
 * @package pages\backoffice\api_v2\DrawManagement
 */
class SowLineItems extends BackofficePage
{
    /**
     * Handle GET requests to fetch SOW line items
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $pcId = $_GET['pcid'];
        if ($pcId && !is_numeric($pcId)) $pcId = cypher::decrypt($pcId);
        $pcId = (int)$pcId;

        if (!$pcId) {
            HTTP::ExitJSON(["success" => false, "message" => "PCID is required."]);
            return;
        }

        try {
            $sowTemplateManager = SowTemplateManager::forPc($pcId);
            $templateId = $sowTemplateManager->getTemplateId();
            $lineItemsData = $sowTemplateManager->getCategoriesDataArray();

            if (!$templateId) {
                HTTP::ExitJSON(["success" => false, "message" => "Template not found."]);
            }
            HTTP::ExitJSON(["success" => true, "message" => "Line items fetched successfully.", "data" => $lineItemsData]);
        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }

    /**
     * Handle POST requests to update SOW line items
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();
        $postData = file_get_contents("php://input");
        $postData = json_decode($postData, true);

        $pcId = $postData['pcid'];
        if ($pcId && !is_numeric($pcId)) $pcId = cypher::decrypt($pcId);
        $pcId = (int)$pcId;

        if (!$pcId) {
            HTTP::ExitJSON(["success" => false, "message" => "PCID is required."]);
            return;
        }

        try {
            if (!isset($postData['lineItems'])) {
                HTTP::ExitJSON(["success" => false, "message" => "Invalid request data."]);
                return;
            }

            $lineItemsData = $postData['lineItems'];
            $sowTemplateManager = SowTemplateManager::forPc($pcId);
            $success = $sowTemplateManager->saveLineItems($lineItemsData);
            if (!$success) {
                HTTP::ExitJSON(["success" => false, "message" => "Failed to save line items."]);
                return;
            }
            $lineItemsData = $sowTemplateManager->getCategoriesDataArray();
            HTTP::ExitJSON(["success" => true, "message" => "Line items saved successfully.", "data" => $lineItemsData]);

        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }
}
