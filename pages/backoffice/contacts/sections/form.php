<?php
namespace pages\backoffice\contacts;

use models\constants\gl\glUserRole;
use models\Controllers\backoffice\contactList;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;


?>

<form class="form"
      name="contactsForm"
      id="contactsForm"
      method="GET">
    <input type="hidden" name="page" id="pageNumber" value="0">
    <input type="hidden" name="sortOpt" id="sortOpt" value="<?php echo contactList::$sortOpt; ?>">
    <input type="hidden" name="orderBy" id="orderBy" value="<?php echo contactList::$orderBy; ?>">

    <div class="accordion accordion-toggle-arrow" id="contactSearchAccordion">
        <div id="contactSearchCollapse" class="collapse" data-parent="#contactSearchAccordion">
            <div class="card-body">
                <div class="row p-0 mb-4"><!-- User Roles. -->
                    <div class="col-lg-3 mb-2">
                        <label class="text-dark-50 font-weight-bold  mb-0" for="role">User Roles</label>
                        <div class="input-group">
                            <select name="role" id="role"
                                    class="form-control form-controller-solid  datatable-input">
                                <option value=""> - All Roles -</option>
                                <?php
                                $contactTypeKeyArray = array_keys(contactList::$roleContactTypeArray);
                                foreach ($contactTypeKeyArray as $tz => $contactTypeID) {
                                    $contactType = trim(contactList::$roleContactTypeArray[$contactTypeID]['type']);
                                    ?>
                                    <option value="<?php echo $contactTypeID ?>" <?php echo Arrays::isSelected($contactTypeID, contactList::$role); ?>><?php echo $contactType ?></option>
                                    <?php
                                }
                                ?>
                            </select>
                        </div>
                    </div>

                    <?php if (contactList::$userRole == 'Super') { ?>
                        <div class="col-lg-3 mb-2">
                            <label class="text-dark-50 font-weight-bold  mb-0" for="pcName">PC Name</label>
                            <div class="input-group">
                                <input type="text" name="pcName" id="pcName"
                                       value="<?php echo Strings::stripQuote(contactList::$pcName) ?>"
                                       class="form-control form-controller-solid  datatable-input"
                                       placeholder="Type text to Search PC Name">
                                <input type="hidden" name="processingCompanyId" id="processingCompanyId"
                                       value="<?php echo contactList::$procCompanyId ?>">
                            </div>
                        </div>
                    <?php } else { ?>
                        <input type="hidden" name="processingCompanyId" id="processingCompanyId"
                               value="<?php echo contactList::$procCompanyId ?>">
                        <?php
                    } ?>

                    <div class="col-lg-3 mb-2">
                        <label class="text-dark-50 font-weight-bold  mb-0" for="searchTerm">Type To
                            Search</label>
                        <div class="input-group">
                            <input type="text" name="searchTerm" id="searchTerm" autocomplete="off"
                                   class="form-control form-controller-solid  datatable-input"
                                   placeholder="Type text to Search in Company Name / Email"
                                   value="<?php echo Strings::stripQuote(contactList::$searchTerm) ?>">
                        </div>
                    </div>

                    <div class="col-lg-2 mb-2">
                        <label></label>
                        <button class="btn btn-primary btn-primary--icon form-control"
                                id="but_sumbit"
                                value="search" type="submit">
                            <span><i class="la la-search"></i><span>Search</span></span>
                        </button>
                    </div>
                    <div class="col-lg-1 mb-2">
                        <label></label>
                        <a class="btn btn-secondary btn-secondary--icon form-control"
                                type="reset"
                                href="?">
                            <span><i class="la la-close"></i><span>Reset</span></span>
                        </a>
                    </div>
                    <?php
                    if (contactList::$userRole == 'Super' || contactList::$userRole == 'Manager') {
                        $csvStr = '?csvOpt=Yes&searchTerm=' . cypher::myEncryption(contactList::$searchTerm) . '&role=' . cypher::myEncryption(contactList::$role) . '&sortOpt=' . cypher::myEncryption(contactList::$sortOpt) . '&orderBy=' . cypher::myEncryption(contactList::$orderBy) . '&opt=procCompList&activeEmp=' . cypher::myEncryption(contactList::$activeEmp) . '&PCID=' . cypher::myEncryption(contactList::$procCompanyId) . '&PCOpt=PCEmp&pcName' . cypher::myEncryption(contactList::$pcName);
                        ?>
                        <div class="col-lg-1">
                            <label class="text-dark-50 font-weight-bold  mb-0"
                                   for="exportcsv">&nbsp;</label>
                            <a class="btn btn-secondary btn-sm exportXLS form-control"
                               href="<?php echo $csvStr; ?>" title="Click to generate as csv"><i
                                        class="far fa-file-excel text-success "></i>
                            </a>
                        </div>
                    <?php } ?>

                </div>
            </div>
        </div>
    </div>

    <div class="accordion accordion-toggle-arrow" id="contactImportAccordion">
        <div id="contactImportcollapse" class="collapse" data-parent="#contactImportAccordion">
            <div class="card-body">
                <div class="row p-0 mb-4">
                    <?php if (PageVariables::$userRole == glUserRole::USER_ROLE_MANAGER) { ?>
                        <div class="col-lg-3 mb-2">
                            <label class="text-dark-50 font-weight-bold  mb-0">Download Sample
                                Template</label>
                            <div class="input-group">
                                <a class="btn btn-secondary btn-sm exportXLS far fa-file-excel text-success"
                                   target="_blank"
                                   href="<?php echo CONST_SITE_URL . 'backoffice/sampleTemplates/contactsSampleTemplate.xlsx'; ?>"
                                   title="Click to Download Sample Template"></a>
                            </div>
                        </div>
                        <div class="col-lg-3 mb-2">
                            <label class="text-dark-50 font-weight-bold  mb-0"> </label>
                            <div class="input-group">
                                <input type="FILE" class="form-control" name="contactfileSrc"
                                       id="contactfileSrc">
                            </div>
                        </div>
                        <div class="col-lg-3 mb-2">
                            <label class="text-dark-50 font-weight-bold  mb-0"> </label>
                            <div class="input-group">
                                <input class="btn btn-primary btn-primary--icon form-control" type="button"
                                       name="importcontactdata"
                                       id="importcontactdata"
                                       value="Import Contacts"
                                       onclick="contactList.importContactData()">
                                <a class="fa fa-info-circle fa-2x tooltipClass p-2"
                                   title="Please use the provided sample template spreadsheet.  Do not rename, move, or delete columns.  1 row of sample data is provided to show expected format for the data"></a>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>

    <?php if (contactList::$isHMLO == 1) { ?>
        <span class="right" style="margin-left: 10px;">
                    <a class="button addNotes"
                       onclick="contactList.addContacts('<?php echo cypher::myEncryption(contactList::$isHMLO) ?>','' ,'');"
                       style="text-decoration:none;" title="Click to add Contacts">Add Contact</a>
                </span>
    <?php } ?>
    <div class="row p-0 m-0">
        <div class="table-responsive">
            <?php echo contacts::$pagination; ?>

            <table style="width: 100%"
                   class="table table-hover LWcustomTable table-bordered table-condensed table-sm table-vertical-center">
                <thead class="thead-light">
                <tr>
                    <th class="text-center">#</th>
                    <th class="text-nowrap">
                                <span class="d-inline p-0">
                                    <span class="fa-stack">
                                        <div style="padding-bottom:5px;"><span
                                                    onclick="contactList.showSortableList('companyName', 'asc');"><i
                                                        class="cursor-pointer fas fa-sort-up fa-stack-1x <?php if (contactList::$sortOpt == 'companyName' && contactList::$orderBy == 'asc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                        <div style="padding-top:5px"><span
                                                    onclick="contactList.showSortableList('companyName', 'desc');"><i
                                                        class="cursor-pointer fas fa-sort-down fa-stack-1x <?php if (contactList::$sortOpt == 'companyName' && contactList::$orderBy == 'desc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                    </span>
                                </span>
                        <span class="d-inline p-0">Company Name</span>
                    </th>
                    <th class="text-nowrap">
                                <span class="d-inline p-0">
                                    <span class="fa-stack">
                                        <div style="padding-bottom:5px;"><span
                                                    onclick="contactList.showSortableList('contactName', 'asc');"><i
                                                        class="cursor-pointer fas fa-sort-up fa-stack-1x <?php if (contactList::$sortOpt == 'contactName' && contactList::$orderBy == 'asc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                        <div style="padding-top:5px"><span
                                                    onclick="contactList.showSortableList('contactName', 'desc');"><i
                                                        class="cursor-pointer fas fa-sort-down fa-stack-1x <?php if (contactList::$sortOpt == 'contactName' && contactList::$orderBy == 'desc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                    </span>
                                </span>
                        <span class="d-inline p-0">First Name</span>
                    </th>
                    <th class="text-nowrap">
                                <span class="d-inline p-0">
                                    <span class="fa-stack">
                                        <div style="padding-bottom:5px;"><span
                                                    onclick="contactList.showSortableList('contactLName', 'asc');"><i
                                                        class="cursor-pointer fas fa-sort-up fa-stack-1x <?php if (contactList::$sortOpt == 'contactLName' && contactList::$orderBy == 'asc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                        <div style="padding-top:5px"><span
                                                    onclick="contactList.showSortableList('contactLName', 'desc');"><i
                                                        class="cursor-pointer fas fa-sort-down fa-stack-1x <?php if (contactList::$sortOpt == 'contactLName' && contactList::$orderBy == 'desc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                    </span>
                                </span>
                        <span class="d-inline p-0">Last Name</span>
                    </th>
                    <th class="text-nowrap">
                                <span class="d-inline p-0">
                                    <span class="fa-stack">
                                        <div style="padding-bottom:5px;"><span
                                                    onclick="contactList.showSortableList('contactType', 'asc');"><i
                                                        class="cursor-pointer fas fa-sort-up fa-stack-1x <?php if (contactList::$sortOpt == 'contactType' && contactList::$orderBy == 'asc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                        <div style="padding-top:5px"><span
                                                    onclick="contactList.showSortableList('contactType', 'desc');"><i
                                                        class="cursor-pointer fas fa-sort-down fa-stack-1x <?php if (contactList::$sortOpt == 'contactType' && contactList::$orderBy == 'desc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                    </span>
                                </span>
                        <span class="d-inline p-0">Role</span>
                    </th>
                    <th class="text-nowrap">
                                <span class="d-inline p-0">
                                    <span class="fa-stack">
                                        <div style="padding-bottom:5px;"><span
                                                    onclick="contactList.showSortableList('email', 'asc');"><i
                                                        class="fas fa-sort-up fa-stack-1x <?php if (contactList::$sortOpt == 'email' && contactList::$orderBy == 'asc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                        <div style="padding-top:5px"><span
                                                    onclick="contactList.showSortableList('email', 'desc');"><i
                                                        class="cursor-pointer fas fa-sort-down fa-stack-1x <?php if (contactList::$sortOpt == 'email' && contactList::$orderBy == 'desc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                    </span>
                                </span>
                        <span class="d-inline p-0">Email</span>
                    </th>
                    <?php if (contactList::$allowToEditProfile == 1) { ?>
                        <th class="text-nowrap">Phone</th>
                        <?php
                    }
                    ?>
                    <th class="text-nowrap">
                                <span class="d-inline p-0">
                                    <span class="fa-stack">
                                        <div style="padding-bottom:5px;"><span
                                                    onclick="contactList.showSortableList('assignedFileCnt', 'asc');"><i
                                                        class="cursor-pointer fas fa-sort-up fa-stack-1x <?php if (contactList::$sortOpt == 'assignedFileCnt' && contactList::$orderBy == 'asc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                        <div style="padding-top:5px"><span
                                                    onclick="contactList.showSortableList('assignedFileCnt', 'desc');"><i
                                                        class="cursor-pointer fas fa-sort-down fa-stack-1x <?php if (contactList::$sortOpt == 'assignedFileCnt' && contactList::$orderBy == 'desc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                    </span>
                                </span>
                        <span class="d-inline p-0"># of Active Files</span>
                    </th>
                    <th class="text-nowrap">
                                <span class="d-inline p-0">
                                    <span class="fa-stack">
                                        <div style="padding-bottom:5px;"><span
                                                    onclick="contactList.showSortableList('assignedDeactivatedFileCnt', 'asc');"><i
                                                        class="cursor-pointer fas fa-sort-up fa-stack-1x <?php if (contactList::$sortOpt == 'assignedDeactivatedFileCnt' && contactList::$orderBy == 'asc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                        <div style="padding-top:5px"><span
                                                    onclick="contactList.showSortableList('assignedDeactivatedFileCnt', 'desc');"><i
                                                        class="cursor-pointer fas fa-sort-down fa-stack-1x <?php if (contactList::$sortOpt == 'assignedDeactivatedFileCnt' && contactList::$orderBy == 'desc') { ?> text-dark <?php } else { ?> icon-inactive <?php } ?>"></i></span></div>
                                    </span>
                                </span>
                        <span class="d-inline p-0"># of Deactivated Files</span>
                    </th>
                    <th class="text-nowrap">Documents</th>
                    <th class="text-nowrap">Actions</th>
                </tr>
                </thead>
                <tbody>
                <?php

                if (count(contacts::$contactsArray) > 0) {
                    $n = contactList::$pageNumber * contactList::$noOfRecordsPerPage;
                    foreach (contacts::$contactsArray as $i => $item) {
                        $appendComma = '';
                        $contactAddress = '';
                        $contactNumber = '';

                        $CID = cypher::myEncryption($item->CID);
                        $address = trim($item->address);
                        $city = trim($item->city);
                        $state = trim($item->state);
                        $zip = trim($item->zip);
                        $website = trim($item->website);
                        $phone = trim($item->phone);
                        $role = trim($item->CTypeID);
                        $barNo = trim($item->barNo);

                        $assFileCnt = (int)($item->fileCnt);
                        $assDeactivatedFileCnt = (int)($item->deactivatedFileCnt);

                        $tempPhone = Strings::formatPhoneNumber($phone);
                        $tempCell = Strings::formatPhoneNumber($item->cell);
                        $tempTollFree = Strings::formatPhoneNumber($item->tollFree);

                        if ($tempPhone && $tempPhone != 'NULL') {
                            $contactNumber = '<b>Phone</b>: ' . $tempPhone;
                            $appendComma = '<br>';
                        }
                        if ($tempTollFree && $tempTollFree != 'NULL') {
                            $contactNumber .= $appendComma . '<b>TollFree</b>: ' . $tempTollFree;
                            $appendComma = '<br>';
                        }
                        if ($tempCell && $tempCell != 'NULL') {
                            $contactNumber .= $appendComma . '<b>Cell #</b>: ' . $tempCell;
                            $appendComma = '<br>';
                        }
                        if ($barNo && $barNo != 'NULL') {
                            $contactNumber .= $appendComma . '<b>bar #</b>: ' . $barNo;
                            $appendComma = '<br>';
                        }
                        if ($website && $website != 'NULL') {
                            $contactNumber .= $appendComma . '<b>Website</b>: ' . $website;
                            $appendComma = '';
                        }

                        if ($contactNumber == '') {
                            $contactNumber = '&nbsp;';
                        }

                        if ($address && $address != 'NULL') {
                            $contactAddress = '<b>Address</b>: ' . $address;
                            $appendComma = '<br>';
                        }
                        if ($city && $city != 'NULL') {
                            $contactAddress .= $appendComma . '<b>City #</b>: ' . $city;
                            $appendComma = '<br>';
                        }
                        if ($state && $state != 'NULL') {
                            $contactAddress .= $appendComma . '<b>State #</b>: ' . $state;
                            $appendComma = '<br>';
                        }
                        if ($zip && $zip != 'NULL') {
                            $contactAddress .= $appendComma . '<b>Zip</b>: ' . $zip;
                            $appendComma = '';
                        }
                        if ($contactAddress == '') {
                            $contactAddress = '&nbsp;';
                        }
                        $serialNo = ($n + 1);
                        ?>
                        <tr>
                            <td class="text-center font-weight-bold py-4"><?php echo $serialNo ?></td>
                            <td style="white-space: pre-wrap;"><?php echo $item->companyName ?></td>
                            <td>
                                <a class="tooltipClass custifyEditContact"
                                   onclick="contactList.editContacts('<?php echo($CID) ?>', '', '', '<?php echo($role) ?>','')"
                                   id="CID=<?php echo($CID) ?>"
                                   style="cursor: pointer;"><?php echo $item->contactName ?></a>
                            </td>
                            <td>
                                <a class="tooltipClass custifyEditContact"
                                   onclick="contactList.editContacts('<?php echo($CID) ?>', '', '', '<?php echo($role) ?>','')"
                                   id="CID=<?php echo($CID) ?>"
                                   style="cursor: pointer;"><?php echo $item->contactLName ?></a>
                            </td>
                            <td>
                                <?php if (array_key_exists($role, contactList::$contactTypeArray)) {
                                    echo trim(contactList::$contactTypeArray[$role]['type']);
                                } else {
                                    echo $role;
                                } ?>
                            </td>
                            <td><?php echo $item->email ?></td>
                            <?php if (contactList::$allowToEditProfile == 1) { ?>
                                <td>
                                    <span><?php echo $contactNumber ?></span>
                                </td>
                            <?php }
                            /*   if($userRole == "Super" || $userRole == "Manager") { */
                            ?>
                            <td class="text-center">
                                <?php if ($assFileCnt > 0) { ?>
                                    <a class="tooltipClass"
                                       href="<?php echo CONST_BO_URL . 'myPipeline.php?contactId=' . $CID; ?>"
                                       target="_blank" title="Click to view pipeline">
                                        <?php echo $assFileCnt; ?></a>
                                <?php } else { ?>
                                    <?php echo $assFileCnt; ?>
                                <?php } ?>
                            </td>
                            <td class="text-center">
                                <?php if ($assDeactivatedFileCnt > 0) { ?>
                                    <a class="tooltipClass"
                                       href="<?php echo CONST_BO_URL . 'myPipeline.php?contactId=' . $CID.'&activeFile=0'; ?>"
                                       target="_blank" title="Click to view pipeline">
                                        <?php echo $assDeactivatedFileCnt; ?></a>
                                <?php } else { ?>
                                    <?php echo $assDeactivatedFileCnt; ?>
                                <?php } ?>
                            </td>
                            <td class="text-center">
                                <?php echo $item->documentCnt ?: '--'; ?>
                            </td>
                            <td>
                                <a
                                        class="change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1  my-2 custifyEditContact"
                                        onclick="contactList.editContacts('<?php echo($CID) ?>', '', '', '<?php echo($role) ?>','')"
                                        id="CID=<?php echo($CID) ?>"
                                        title='File: <?php echo htmlentities(contactList::$borrowerName); ?> > <?php echo htmlentities(contactList::$contactRole); ?> Info'><i
                                            class="tooltipClass far fa-edit"
                                            title="Click to edit <?php echo htmlentities(contactList::$contactRole); ?> Info"></i>
                                </a>
                                <?php if ($assFileCnt == 0) { ?>
                                    <a class="change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 "
                                       onclick="contactList.deleteContactInfo('<?php echo $CID ?>');">
                                        <i class="tooltipClass flaticon2-trash"
                                           title="Click to delete"></i>
                                    </a>
                                <?php } ?></td>
                            <?php
                            /* } */
                            ?>
                        </tr>
                        <?php
                        $n++;
                    }
                } else { ?>
                    <tr>
                        <td colspan="9" style="text-align:center;">No record found</td>
                    </tr>
                    <?php
                }
                ?>
                </tbody>
            </table>
        </div>
    </div>

</form>
