select t1.CID
     , t1.PCID
     , t1.companyName
     , t1.email
     , t1.address
     , t1.city
     , t1.state
     , t1.zip
     , t1.website
     , t1.phone
     , t1.cell
     , t1.fax
     , t1.description
     , t1.activeStatus
     , t1.contactName
     , t1.CTypeID
     , t1.dummyID
     , t1.licenseNo
     , t1.tollFree
     , t1.contactLName
     , t1.contactDummyId
     , t1.barNo
     , t1.stateOfFormation
     , t1.entityType
     , t1.einNo
     , t1.repTitle
     , t1.loanServicer
     , IFNULL(fileCnt, 0)               AS fileCnt
     , IFNULL(deactivatedFileCnt, 0)    AS deactivatedFileCnt
     , IFNULL(documents.documentCnt, 0) AS documentCnt
from tblContacts t1
         LEFT JOIN (SELECT tc.CID,
                           COUNT(DISTINCT CASE WHEN tl.activeStatus = 1 THEN fc.fileID END) AS fileCnt,
                           COUNT(DISTINCT CASE WHEN tl.activeStatus = 0 THEN fc.fileID END) AS deactivatedFileCnt
                    FROM tblContacts tc,
                         tblFileContacts fc,
                         tblFile tl,
                         tblFileResponse tlr,
                         tblFileUpdatedDate tfu
                    WHERE tc.CID = fc.CID
                      AND fc.fileID = tl.LMRId
                      AND tlr.LMRId = tl.LMRId
                      AND tlr.activeStatus = tl.activeStatus
                      AND tl.LMRId = tfu.fileID
                      AND tc.PCID = @PCID
                    GROUP BY tc.CID) AS fileCountData ON t1.CID = fileCountData.CID
         LEFT JOIN (SELECT contactID, COUNT(*) AS documentCnt
                    FROM tblContactsDocs t100
                    WHERE t100.activeStatus = 1
                    GROUP BY t100.contactID) AS documents ON documents.contactID = t1.CID

         JOIN tblContactsType t2 ON t1.CTypeID = t2.CTypeID
WHERE t1.activeStatus = 1
  and (
    @searchTerm = '' OR (
        t1.companyName like @searchTerm
            or t1.contactLName like @searchTerm
            or t1.contactName like @searchTerm
            or t1.email like @searchTerm
        )
    )
  AND (@role IS NULL OR t1.CTypeID = @role)
  AND t1.PCID = @PCID
  and (@CTypeID IS NULL OR t1.CTypeID = @CTypeID)
  and (@CID IS NULL OR t1.CID = @CID)
  AND t1.CTypeID <> 13 -- counselor
