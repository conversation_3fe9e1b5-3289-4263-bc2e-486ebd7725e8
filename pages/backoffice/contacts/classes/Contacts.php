<?php

namespace pages\backoffice\contacts\classes;

use models\APIHelper;
use models\Database2;
use models\types\strongType;

class Contacts extends strongType
{
    public ?int $CID = null;
    public ?int $PCID = null;
    public ?string $companyName = null;
    public ?string $email = null;
    public ?string $address = null;
    public ?string $city = null;
    public ?string $state = null;
    public ?string $zip = null;
    public ?string $website = null;
    public ?string $phone = null;
    public ?string $cell = null;
    public ?string $fax = null;
    public ?string $description = null;
    public ?int $activeStatus = null;
    public ?string $contactName = null;
    public ?int $CTypeID = null;
    public ?string $dummyID = null;
    public ?string $licenseNo = null;
    public ?string $tollFree = null;
    public ?string $contactLName = null;
    public ?string $contactDummyId = null;
    public ?string $barNo = null;
    public ?string $stateOfFormation = null;
    public ?string $entityType = null;
    public ?string $einNo = null;
    public ?string $repTitle = null;
    public ?int $fileCnt = null;
    public ?int $deactivatedFileCnt = null;
    public ?int $documentCnt = null;
    public ?string $loanServicer = null;

    public static ?int $count = null;


    /**
     * @param int $PCID
     * @param string|null $searchTerm
     * @param string|null $role
     * @param int|null $CTypeID
     * @param int $perPage
     * @param int $offset
     * @param string|null $sortBy
     * @param string|null $sortDir
     * @param int|null $CID
     * @return self[]
     */
    public static function getReport(
        int $PCID,
        ?string $searchTerm,
        ?string $role,
        ?int $CTypeID,
        int $perPage,
        int $offset,
        ?string $sortBy,
        ?string $sortDir,
        ?int $CID = null

    ): array
    {
        $params = [
            'PCID' => $PCID,
            'searchTerm' => $searchTerm ? '%' . $searchTerm . '%' : '',
            'role' => $role ?: null,
            'CTypeID' => $CTypeID ?: null,
            'CID' => $CID ?: null,
        ];

        $sql = APIHelper::getSQL(__DIR__ . '/sql/contactsCount.sql');
        $res = Database2::getInstance()->queryData($sql, $params);
        self::$count = $res[0]['cnt'] ?? 0;

        $sql = APIHelper::getSQL(__DIR__ . '/sql/contacts.sql');
        if($sortBy && $sortDir) {
            switch($sortBy) {
                case 'assignedFileCnt':
                    $sql .= ' ORDER BY fileCnt';
                    break;

                case 'email':
                    $sql .= ' ORDER BY email';
                    break;

                case 'companyName':
                    $sql .= ' ORDER BY companyName';
                    break;

                case 'contactName':
                    $sql .= ' ORDER BY contactName';
                    break;
                case 'contactLName':
                    $sql .= ' ORDER BY contactLName';
                    break;
                case 'contactType':
                    $sql .= ' ORDER BY t2.TYPE';
                    break;
                case 'assignedDeactivatedFileCnt':
                    $sql .= ' ORDER BY deactivatedFileCnt';
                    break;
                default:
                    $sql .= ' ORDER BY 1';
                    break;

            }
            $sql .= ' ' . $sortDir;
        }
        if($offset || $perPage) {
            $sql .= ' LIMIT ' . $offset . ',' . $perPage;
        }
//        Debug(Database2::getInstance()->safeQuery($sql, $params));

        return Database2::getInstance()->queryData($sql, $params, function($row) {
            return new self($row);
        });
    }
}
