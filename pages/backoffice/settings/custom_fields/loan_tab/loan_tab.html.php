<?php
namespace pages\backoffice\settings\custom_fields\loan_tab;

?>


<h3><a href="/backoffice/settings/custom_fields">Custom Fields</a> for Section - <?php use models\constants\gl\glUserRole;
    use models\PageVariables;
    use models\Request;

    echo loan_tab::$sectionHeader->sectionHeading; ?></h3>
<hr/>

<h3>
    <?php echo loan_tab::$selected->id ? 'Edit' : 'Create New' ?> Field
    <?php if(loan_tab::$selected->id) { ?>
        <a class="btn btn-small btn-primary"
           href="/backoffice/settings/custom_fields/loan_tab?sectionID=<?php echo loan_tab::$sectionHeader->sectionID; ?>">New Field</a>
    <?php } ?>
</h3>

<?php require_once 'sections/custom_field.php'; ?>

<?php require_once 'sections/preview.php'; ?>

<hr/>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Label</th>
            <th>Type</th>
            <th style="text-align: center;">Display Order</th>
            <th>Width</th>
            <th>Active</th>
            <th style="text-align: center;">File Types</th>
            <th style="text-align: center;">Loan Programs</th>
            <th>Display</th>
            <th>Mandatory</th>
            <th>Created</th>
        </tr>
    </thead>
    <tbody>
    <?php foreach(loan_tab::$customFields as $item) { ?>
        <tr class="<?php echo loan_tab::$id == $item->id ? 'active' : ''; ?>">
            <td><?php echo $item->Label; ?></td>
            <td><?php echo $item->getTblCustomFieldType_by_id()->type; ?></td>
            <td style="text-align: center;"><?php echo $item->DisplayOrder ?: ''; ?></td>
            <td><?php echo $item->Width ?: '<i class="indicator">Default</i>'; ?></td>
            <td><?php echo $item->isActive ? '<span class="label label-lg label-rounded label-success">Yes</span>' : '<span class="label label-lg label-rounded label-danger">No</span>' ; ?></td>
            <td style="text-align: center;"><?php echo sizeof($item->_fileTypes()) ?: ''; ?></td>
            <td style="text-align: center;"><?php echo sizeof($item->_loanPrograms()) ?: ''; ?></td>
            <td>
                <ul>
                <?php echo $item->displayFA ? '<li>FA</li>' : ''; ?>
                <?php echo $item->displayQA ? '<li>QA</li>' : ''; ?>
                <?php echo $item->displayBO ? '<li>BO</li>' : ''; ?>
                </ul>
            </td>
            <td>
                <ul>
                    <?php echo $item->mandatoryFA ? '<li>FA</li>' : ''; ?>
                    <?php echo $item->mandatoryQA ? '<li>QA</li>' : ''; ?>
                    <?php echo $item->mandatoryBO ? '<li>BO</li>' : ''; ?>
                </ul>
            </td>
            <td>
                <?php echo $item->createdAt; ?><br/>
                <i class="indicator"><?php echo $item->createdByEmail; ?></i>
            </td>
            <td>
                <?php if(PageVariables::$userRole == glUserRole::USER_ROLE_SUPER) { ?>
                <a class="btn btn-small btn-primary"
                   href="/backoffice/settings/custom_fields/loan_tab?pcId=<?php echo Request::Get('pcId'); ?>&sectionID=<?php echo loan_tab::$sectionHeader->sectionID; ?>&id=<?php echo $item->id; ?>">Edit</a>
                <?php } else { ?>
                    <a class="btn btn-small btn-primary"
                       href="/backoffice/settings/custom_fields/loan_tab?sectionID=<?php echo loan_tab::$sectionHeader->sectionID; ?>&id=<?php echo $item->id; ?>">Edit</a>
                <?php } ?>
            </td>
        </tr>
    <?php } ?>
    </tbody>
</table>

<script src="/backoffice/settings/js/custom_fields/loan_tab.js?<?= CONST_JS_VERSION ?>" type="application/javascript"></script>
<style>
    tr.active td {
        background-color: #00ff80;
    }
</style>
